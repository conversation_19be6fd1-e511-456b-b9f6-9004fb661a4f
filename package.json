{"name": "lavni", "version": "1.0.0", "description": "", "main": "./dist/server", "scripts": {"start": "nodemon --exec ts-node src/server.ts -e ts", "build": "NODE_OPTIONS=--max-old-space-size=4096 tsc", "start:prod": "node dist/server.js", "seed": "ts-node src/seed/seed-runner", "test": "NODE_ENV=test PORT=3011 mocha -r ts-node/register test/**/*.test.ts --exit", "test:watch": "nodemon -e ts --exec mocha -r ts-node/register test/**/*.test.ts", "migrate": "ts-node src/migrations/migration-runner.ts"}, "author": "AroshA", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.502.0", "@aws-sdk/s3-request-presigner": "^3.502.0", "@deepgram/sdk": "^3.0.1", "@google-cloud/local-auth": "^2.1.0", "@ringcentral/sdk": "^5.0.3", "@types/node-fetch": "^2.6.1", "aes256": "^1.1.0", "aws-sdk": "^2.1219.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.0", "braintree": "^2.21.0", "cool-images": "^1.0.3", "cors": "^2.8.5", "csv-writer": "^1.6.0", "csvtojson": "^2.0.10", "date-fns": "^2.30.0", "dotenv": "^8.6.0", "express": "^4.18.1", "express-validator": "^6.2.0", "faker": "^4.1.0", "ffmpeg-static": "^5.0.2", "firebase-admin": "^12.7.0", "force": "^0.0.3", "google-auth-library": "^8.0.2", "googleapis": "^105.0.0", "html-to-text": "^9.0.5", "jsrsasign": "^10.6.1", "jwt-simple": "^0.5.5", "mjml": "^4.7.1", "moment": "^2.24.0", "moment-timezone": "^0.5.26", "mongoose": "^5.8.3", "morgan": "^1.9.1", "multer": "^1.4.4", "node-cron": "^3.0.1", "node-fetch": "^2.6.7", "node-html-markdown": "^1.3.0", "nodemailer": "^6.7.2", "nodemailer-sendgrid": "^1.0.3", "opentok": "^2.17.0", "opentok-jwt": "^0.1.5", "passport": "^0.4.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pdfkit": "^0.13.0", "pdfmake": "^0.2.10", "random-profile-generator": "^2.3.0", "sharp": "^0.31.3", "socket.io": "^4.4.1", "stripe": "^9.11.0", "title-case": "^2.1.1", "twilio": "^3.84.1", "uuid": "^9.0.0", "winston": "^3.2.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/body-parser": "^1.17.0", "@types/chai": "^4.2.3", "@types/cors": "^2.8.6", "@types/express": "^4.17.0", "@types/mjml": "^4.0.4", "@types/mocha": "^5.2.7", "@types/moment-timezone": "^0.5.12", "@types/mongoose": "^5.5.11", "@types/morgan": "^1.7.36", "@types/multer": "^1.3.7", "@types/node": "^17.0.21", "@types/node-cron": "^3.0.2", "@types/nodemailer-sendgrid": "^1.0.0", "@types/passport": "^1.0.0", "@types/passport-jwt": "^3.0.1", "@types/passport-local": "^1.0.33", "@types/pdfkit": "^0.12.6", "@types/supertest": "^2.0.8", "@types/twilio": "^3.19.3", "@types/uuid": "^8.3.4", "@types/winston": "^2.4.4", "chai": "^4.2.0", "mocha": "^6.2.1", "nodemon": "^2.0.6", "supertest": "^4.0.2", "ts-node": "^10.7.0", "tslint": "^5.11.0", "typescript": "^4.8.3"}}