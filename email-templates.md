# Email Templates in Lavni System

This document lists all email templates used throughout the Lavni API system.

## General Email Templates

### 1. sendEventEmail
Standard email for event notifications.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {user's name},

{bodyText1} {otherUserName}

{bodyText2}

[Link to <PERSON>vni]
```

### 2. sendEventReminderEmail
Reminder emails for events.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
{bodyText1}

[Link to <PERSON>vni]
```

### 3. sendEventEmailNotes
Email for note-related events.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
{bodyText1}

[Link to <PERSON>vni]
```

### 4. sendEventMeetingLinkEmail
Email with meeting link information.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
{bodyText1}

{bodyText2}

[Link to <PERSON>vni]
```

### 5. sendReminderToGroupCall
Reminder for group call events.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
{bodyText1}

{bodyText2}

[Link to <PERSON>vni]
```

## Authentication Emails

### 6. sendVerifyEmail
Email with verification code.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
{bodyText1}

Your verification code is: {verificationCode}

{bodyText2}

[Link to Lavni]
```

### 7. sendForgetPasswordEmail
Email for password reset.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
We've received a request to reset your password.

[Password Reset Link]

If you didn't request this, please ignore this email.
```

## Welcome & Onboarding Emails

### 8. sendWelcomeEmailClient
Welcome email for new clients.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Welcome to Lavni!

We're excited to have you join our platform. Here's what you can do next:
- Complete your profile
- Browse available therapists
- Schedule your first session

[Link to Get Started]
```

### 9. sendSubscriptionEmail
Email related to subscription changes.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {user's name},

{bodyText1}

[Link to Lavni]
```

## Admin Notification Emails

### 10. sendAdminEmailWhenTherapistOrClientSignUp
Notifies admins about new user signup.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
{bodyText1}

New user signed up with email: {userEmail}

[Link to Lavni Admin]
```

### 11. sendAdminEmailUserSignup
Detailed admin notification about user signup.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
A new user has signed up:

Email: {userEmail}
Password: {password}

[Link to Lavni Admin]
```

## Payment & Insurance Emails

### 12. sendCoPaymentEmailForInsurance
Reminder for copayment due.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

This is a friendly reminder about your copayment of ${coPayment} for your session on {date} at {time}.

Please use the link below to make your payment:
{paymentLink}

[Link to Lavni]
```

### 13. sendCoPaymentEmailForInsurancePaid
Confirmation of copayment received.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Thank you for your copayment for the session on {createdAt}. Your payment has been processed successfully.

[Link to Lavni]
```

### 14. clientDuePaymentSettled
Notification that payment has been settled.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Your payment of ${payAmount} for {paymentMonth} has been processed successfully.

[Link to Lavni]
```

### 15. sendAdminEmailForInsurancePayment
Admin notification about insurance payment.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

We've processed a payment of ${payAmount} for {paymentMonth}. Your remaining balance is ${dueAmount}.

[Link to Lavni]
[Attachment: {attachmentName}]
```

## Appointment Reminder Emails

### 16. send15MinuteAppointmentReminder
Reminder sent 15 minutes before appointment.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Your session with {senderName} starts in 15 minutes.

[Link to Join Session]
```

### 17. send30MinuteAppointmentReminder
Reminder sent 30 minutes before appointment.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Your session with {senderName} starts in 30 minutes.

[Link to Join Session]
```

### 18. sendNextHourAppointmentReminder
Reminder sent one hour before appointment.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Your session with {senderName} starts in one hour.

[Link to Join Session]
```

### 19. sendNextDayAppointmentsReminder
Reminder sent a day before appointment.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

This is a reminder that you have an appointment with {senderName} tomorrow.

[Link to View Appointment]
```

## Account Status Emails

### 20. monthlyWithdrawalInitiated
Notification about monthly withdrawal.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname}!

Your monthly withdrawal is initiated.

Login to the dashboard to check more details.

[Link to View Appointments]
```

### 21. clientPremiumStatusChanged
Notification about premium status change.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname}!

{premiumStatus == ACTIVE ? "Great news! Your Lavni account has been upgraded to Premium, unlocking additional features and benefits. We hope you enjoy the enhanced experience." : "Your premium access has been revoked."}

[Link to Lavni]
```

### 22. userStatusHasChangedBlock
Notification about account being blocked.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Your account has been {blockStatus} for the following reason:
{reasonToBlock}

Please contact support for more information.

[Link to Contact Support]
```

### 23. userStatusHasChanged
Notification about account status change.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Your account status has been changed to: {blockStatus}

[Link to Lavni]
```

## Marketing & Notification Emails

### 24. sendMarketingEmail1
Marketing email to multiple users.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
{emailBody}

[Link to Lavni]
```

### 25. newPostInDiscover
Notification about new post in Discover section.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
New post: {title}
By: {createdBy}

[Link to View Post]
```

### 26. sendMissedMessageEmail
Notification about missed messages.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

You have {messageCount} unread message(s). Log in to view them.

[Link to Messages]
```

## Form & Document Emails

### 27. sendTherapyPlanSignatureEmail
Request for signature on therapy plan.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Please review and sign your therapy plan.

[Link to Sign Document]
```

### 28. sendClinicalAssesmentSignatureEmail
Request for signature on clinical assessment.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Please review and sign your clinical assessment.

[Link to Sign Document]
```

### 29. sendDigitalAssesmentSignatureEmail
Request for signature on digital assessment.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Please review and sign your digital assessment.

[Link to Sign Document]
```

## Referral & Friend Request Emails

### 30. sendTherapistReferralLinkEmail
Email with therapist referral link.
```
[HTML Email with header and footer]
Subject: Therapist Referral Link
Body:
{body}

[Referral Link]
```

### 31. sendClientReferralLinkEmailByClient
Email with client referral link.
```
[HTML Email with header and footer]
Subject: Client Referral Link
Body:
{messageContent}

[Referral Link]
```

### 32. sendFriendRequestFromTherapistToClient
Notification about friend request.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
You have received a friend request.

[Link to Accept/Decline]
```

## Insurance Eligibility Emails

### 33. sendAdminEmailForInsuranceEligibilitySuccess
Notification about successful insurance eligibility check.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Insurance eligibility successful for:
Client: {clientFirstName}
Insurance: {insuranceName}
Status: {eligibilityStatus}
Copayment: ${coPayment}
Therapist: {therapistFirstName}
Appointment: {appointmentStartTime}
```

### 34. sendAdminEmailForInsuranceEligibilityError
Notification about failed insurance eligibility check.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
Insurance eligibility check failed for:
Client: {clientFirstName}
Insurance: {insuranceName}
Error: {errorMsg}
```

## Verification & Reminder Emails

### 35. sendIncompleteFormSubmissionReminder
Reminder about incomplete form submission.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
This is a reminder about an incomplete {formName} for client {client.firstname} {client.lastname}.

[Link to Complete Form]
```

### 36. sendAutomatedMultipleNoShowEmailForClients
Notification about multiple no-shows.
```
[HTML Email with header and footer]
Subject: {subject}
Body:
We've noticed you've missed multiple appointments. Please contact your therapist to reschedule.

[Link to Lavni]
```

## Notes:
- All emails use HTML templates with Lavni branding
- Development/staging environments log emails to Slack instead of sending
- Production environment sends actual emails via SendGrid
- Email templates include header and footer with Lavni logo and social media links
