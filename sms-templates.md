# SMS Templates in Lavni System

This document lists all SMS templates used throughout the Lavni API system with actual message content and examples.

## General SMS Service (src/sms/config.ts)

### 1. sendEventSMS
General-purpose SMS for various event notifications. The actual content is passed in as a parameter.

**Example Usage:**
```typescript
SMSService.sendEventSMS(
  `New goal ${goal.title} has been created by ${user1.firstname} ${user1.lastname}`,
  user2.primaryPhone
);
```

### 2. sendUnpaidCOPAYSMS
Reminds clients about unpaid copayments with payment link.

**Template:**
```
Hi {firstname},
Friendly reminder: Your therapy session on {date} at {time} has a ${coPaymentValue} copayment due. Please click the link below to make the payment and ensure uninterrupted sessions.
{paymentLink}

Best,
Lavni
```

**Example:**
```
Hi John,
Friendly reminder: Your therapy session on April 10, 2025 at 2:00 PM has a $25 copayment due. Please click the link below to make the payment and ensure uninterrupted sessions.
https://pay.lavni.com/copay/12345

Best,
Lavni
```

### 3. sendPaidCOPAYSMS
Confirms payment of a copayment.

**Template:**
```
Dear {firstname},
Your copayment for your session on {createdAt} was processed successfully.

Thank you for trusting us with your care.

Best,
Lavni
```

**Example:**
```
Dear Sarah,
Your copayment for your session on April 5, 2025 was processed successfully.

Thank you for trusting us with your care.

Best,
Lavni
```

### 4. sendInactiveClientSMS
Notifies inactive clients about their account status. The specific message is passed as a parameter.

**Example Usage:**
```typescript
SMSService.sendInactiveClientSMS(
  "We've noticed you haven't logged in recently. Your account will become inactive in 30 days.",
  user.primaryPhone
);
```

### 5. sendGroupChatEventSMS
Notifications for group chat events. The specific message is passed as a parameter.

**Example Usage:**
```typescript
SMSService.sendGroupChatEventSMS(
  "New message in your therapy group 'Anxiety Support'. Log in to view the message.",
  user.primaryPhone
);
```

### 6. sendReminderSMS
General reminder messages. Used extensively for appointment reminders and insurance verification.

**Example from admin-ep.ts (Appointment Reminder):**
```
Good morning, Michael,

I'm Marcus, an automated Care Support Coordinator at Lavni, your trusted platform for connecting with therapists who resonate with you and accept your insurance. Visit us at https://mylavni.com.

I'm reaching out to confirm your appointment scheduled for 2025-04-15 02:00 PM with our therapist, Dr. Jennifer Smith. We're genuinely thrilled and eagerly anticipating your first session with us.

To finalize your appointment details, could you please confirm whether you'll be using your insurance or opting for a self-pay arrangement?

Please reply:
1 for insurance
2 for out-of-pocket
```

## Homework Notifications (src/end-point/homework-ep.ts)

### 7. New Homework Assignment
Notifies clients when a new homework is assigned by their therapist.

**Template:**
```
New Homework {homework.title} is assigned by {user1.firstname} {user1.lastname}
```

**Example:**
```
New Homework "Daily Mindfulness Journal" is assigned by Dr. Robert Johnson
```

## Goal Notifications (src/end-point/goal-ep.ts)

### 8. New Goal Creation by Therapist
Notifies client when therapist creates a new goal.

**Template:**
```
New goal {goal.title} has been created by {user1.firstname} {user1.lastname}
```

**Example:**
```
New goal "Reduce anxiety through daily meditation" has been created by Dr. Emma Wilson
```

### 9. New Goal Creation by Client
Notifies therapist when client creates a new goal.

**Template:**
```
New goal {goal.title} has been created by {user2.firstname} {user2.lastname}
```

**Example:**
```
New goal "Improve sleep habits" has been created by Jessica Brown
```

## Friend Request Notifications (src/end-point/friend-request-ep.ts)

### 10. New Friend Request
Notifies therapist about new friend/connection request from client.

**Template:**
```
You have received a request from {updatedClient.firstname} {updatedClient.lastname}
```

**Example:**
```
You have received a request from Thomas Anderson
```

## Chat Notifications (src/end-point/chat-ep.ts)

### 11. Unread Message Notification
Sent to users with unread messages after a certain time period (every 20 minutes).

**Template:**
```
You have received a new message from {senderId.firstname} {senderId.lastname}. Please see the message below.

{messageText} {optional attachment notification}
```

**Example with message:**
```
You have received a new message from Dr. Lisa Park. Please see the message below.

Just checking in to see how your progress with the exercises is going.
```

**Example with attachment:**
```
You have received a new message from Dr. Lisa Park. Please see the message below.

Here's the worksheet we discussed < Sent Attachment ( Please Login to View ) >
```

## Admin Reminder SMS (src/end-point/admin-ep.ts)

### 12. Insurance Verification Reminder
Reminds clients who have upcoming appointments but haven't verified their insurance.

**Template example from code:**
```
{greeting}, {client.firstname},

I'm Marcus, an automated Care Support Coordinator at Lavni, your trusted platform for connecting with therapists who resonate with you and accept your insurance. Visit us at https://mylavni.com.

I'm reaching out because you have an upcoming appointment with {therapist.firstname} {therapist.lastname}, but we still need to verify your insurance information. To make this process as smooth as possible, could you please provide your insurance details?

Please reply with:
1 to provide insurance information
2 if you prefer to pay out-of-pocket
```

### 13. Missed Appointment Follow-up
Sent to clients who missed their first appointment.

**Template example from code:**
```
{greeting}, {client.firstname},

This is Marcus from Lavni. I noticed you missed your recent appointment with {therapist.firstname} {therapist.lastname}. We understand that things come up, and we'd like to help you reschedule at a time that works better for you.

Please reply:
1 to reschedule your appointment
2 if you need assistance with something else
```

### 14. Custom SMS
Admin can send custom SMS to selected users or user groups.

**Example Usage:**
```typescript
SMSService.sendSMS(
  customContent, // Any message the admin wants to send
  user.primaryPhone
);
```

## Other Specialized SMS Templates

### 15. Group Therapy Reminders
Reminders about upcoming group therapy sessions.

**Example:**
```
Reminder: Your group therapy session "Anxiety Support" is tomorrow at 6:00 PM with Dr. Johnson. Login to Lavni 5 minutes before to join.
```

### 16. Insurance Eligibility Updates
Updates clients on their insurance eligibility status.

**Example:**
```
Good news! Your insurance eligibility has been verified. Your copayment amount is $25 per session. Your next appointment with Dr. Smith is on April 15, 2025.
```

### 17. Payment Processing Notifications
Notifies clients about payment processing status.

**Example:**
```
Your payment of $75 for session on April 8, 2025 has been successfully processed. Thank you for your payment.
```

## Notes:
- All SMS messages are sent through Twilio service
- In development/staging environments, SMS are logged to Slack instead of being sent
- Production environment sends actual SMS messages to recipients
- The system masks phone numbers in logs for privacy
- Many SMS templates include greeting variables that adjust based on time of day (Good morning/afternoon/evening)
