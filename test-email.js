require("dotenv").config();

const nodemailer = require("nodemailer");
const nodemailerSendgrid = require("nodemailer-sendgrid");

console.log("SENDGRID_KEY:", process.env.SENDGRID_KEY ? "<PERSON><PERSON> thiết lập" : "<PERSON>hông thiết lập");
console.log("SENGRID_SENDER:", process.env.SENGRID_SENDER);
const transport = nodemailer.createTransport(
  nodemailerSendgrid({
    apiKey: process.env.SENDGRID_KEY,
  })
);

// HTML email cơ bản
const emailHeader = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>Test Email</title>
  <style>
    table, td, div, h1, p {font-family: Arial, sans-serif;}
  </style>
</head>
<body style="margin:0;padding:0;">
  <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;background:#ffffff;">
    <tr>
      <td align="center" style="padding:0;">
        <table role="presentation" style="width:602px;border-collapse:collapse;border:1px solid #cccccc;border-spacing:0;text-align:left;">
          <tr>
            <td align="center" style="padding:40px 0 10px 0;background:#fff;">
              <img src="https://mylavni.com/static/assets/img/lavni_logo.png" alt="Lavni Logo" width="300" style="height:auto;display:block;" />
            </td>
          </tr>`;

const emailFooter = `<tr>
            <td style="padding:30px;background:#FF8000;">
              <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;font-size:9px;font-family:Arial,sans-serif;">
                <tr>
                  <td style="padding:0;width:50%;" align="left">
                    <p style="margin:0;font-size:14px;line-height:16px;font-family:Arial,sans-serif;color:#ffffff;">
                      &reg; Lavni, Inc. 2023<br/>
                    </p>
                  </td>
                  <td style="padding:0;width:50%;" align="right">
                    <table role="presentation" style="border-collapse:collapse;border:0;border-spacing:0;">
                      <tr>
                        <td style="padding:0 0 0 10px;width:38px;">
                          <a href="https://www.instagram.com/mylavni/" style="color:#ffffff;"><img src="https://png.pngtree.com/png-clipart/20180626/ourmid/pngtree-instagram-icon-instagram-logo-png-image_3584853.png" alt="Instagram" width="38" style="height:auto;display:block;border:0;" /></a>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>`;

// Hàm gửi email
async function sendTestEmail() {
  try {
    console.log("Đang gửi email tới <EMAIL>...");
    
    const result = await transport.sendMail({
      from: process.env.SENGRID_SENDER || '<EMAIL>',
    //   to: '<EMAIL>',
      to: '<EMAIL>',
      subject: 'Email Test từ Lavni API',
      html: 
        emailHeader +
        `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Kiểm tra email từ Lavni API</h1>
                  <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Đây là email kiểm tra được gửi từ script test riêng biệt.
                    <br/><br/>
                    Thời gian gửi: ${new Date().toLocaleString()}
                  </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
        emailFooter
    });
    
    console.log("Email đã được gửi thành công!");
    console.log("Chi tiết phản hồi từ SendGrid:");
    console.log(JSON.stringify(result, null, 2));
    
    return result;
  } catch (error) {
    console.error("Lỗi khi gửi email:", error);
    if (error.response && error.response.body) {
      console.error("Chi tiết lỗi SendGrid:", error.response.body);
    }
    throw error;
  }
}

// Khi đã có API key mới, bỏ comment dòng bên dưới để chạy thử
sendTestEmail()
  .then(() => console.log("Script hoàn thành"))
  .catch(() => console.log("Script thất bại"));
