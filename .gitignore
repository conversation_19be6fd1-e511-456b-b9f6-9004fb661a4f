# Created by https://www.gitignore.io

### Intellij ###
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm

*.iml

## Directory-based project format:
.idea/
# if you remove the above rule, at least ignore the following:

# User-specific stuff:
# .idea/workspace.xml
# .idea/tasks.xml
# .idea/dictionaries

# Sensitive or high-churn files:
# .idea/dataSources.ids
# .idea/dataSources.xml
# .idea/sqlDataSources.xml
# .idea/dynamic.xml
# .idea/uiDesigner.xml

# Gradle:
# .idea/gradle.xml
# .idea/libraries

# Mongo Explorer plugin:
# .idea/mongoSettings.xml

## File-based project format:
*.ipr
*.iws

## Plugin-specific files:

# IntelliJ
/out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties


### Node ###
# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release



# Dependency directory
# https://www.npmjs.org/doc/misc/npm-faq.html#should-i-check-my-node_modules-folder-into-git
node_modules

.env
package-lock.json

*.orig

# uploads/
.history
dist/

uploads/ATTACHMENTS/*
uploads/AVATARS/*
uploads/DIAGNOSISNOTE_DOCUMENTS/*
uploads/CALL_RECORDS/*
uploads/CALL_RECORDS_TEMPORY_VIDEOS/*
uploads/PROFILE_IMAGE/*
uploads/EDUCATIONAL_DOCUMENTS/*
uploads/LICENSE_DOCUMENTS/*
uploads/CLIENT_HOMEWORK_DOCUMENTS/*
uploads/THERAPIST_HOMEWORK_DOCUMENTS/*
uploads/HOMEWORK_DOCUMENTS/*
uploads/ARTICLE_IMAGE/*
uploads/ARTICLE_VIDEO_THUMBNAIL/*
uploads/PROFILE_COVER_IMAGE/*
uploads/ARTICLE_FILE/*
uploads/PROFILE_VIDEO/*
uploads/PROFILE_CONTENT/*
uploads/GOAL_DOCUMENTS/*
uploads/THEMES/*
uploads/DISCLOSURE_STATEMENT/*
uploads/INSURANCE_CARD/*
uploads/GROUP_ICON/*
uploads/UPLOAD_DOCUMENTS/*
uploads/PROFILE_IMAGE_THUMBNAIL/*
uploads/TRAINING_DOCUMENT_AND_VIDEO/*
uploads/DIAGNOSIS_NOTES/*
uploads/INSTAGRAM_FEED/*
uploads/DIAGONOSIS_NOTES/*
uploads/CLINICAL_ASSESSMENT/*
uploads/THERAPY_PLAN/*
uploads/AUTHORIZATION_FORM/*

invoices

!uploads/AVATARS/test
!uploads/DIAGNOSISNOTE_DOCUMENTS/test
!uploads/CALL_RECORDS/test
!uploads/CALL_RECORDS_TEMPORY_VIDEOS/test
!uploads/DIAGONOSIS_NOTES/test

src/python_scripts/__pycache__/*
src/python_script_new/__pycache__/*
src/python_script_assessment/__pycache__/*
src/python_script_notes/__pycache__/*

scripts/backup/*

uploads/CLAIM_CSV/*.csv

.venv/