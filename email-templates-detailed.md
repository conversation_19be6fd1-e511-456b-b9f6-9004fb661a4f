# Email Templates in Lavni System

This document lists all email templates used throughout the Lavni API system with actual message content and examples.

## General Email Templates

### 1. sendEventEmail
Standard email for event notifications.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
<h1>{bodyText1} {otherUserName}</h1>
<p>{bodyText2}</p>
[Link to Lavni]
```

**Example Usage:**
```typescript
EmailService.sendEventEmail(
  user,
  "New goal has been created!",
  "New goal " + goal.title + " has been created by",
  "Login to view more information.",
  user2?.firstname + " " + user2?.lastname
);
```

**Example Email:**
```
Subject: New goal has been created!
Body:
New goal "Improve sleep habits" has been created by <PERSON> to view more information.

[Link to Lavni]
```

### 2. sendEventReminderEmail
Reminder emails for events.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
<h2>{bodyText1}</h2>
[Link to Lavni]
```

**Example Usage:**
```typescript
EmailService.sendEventReminderEmail(
  user,
  "Reminder: Upcoming Group Session",
  "Your group therapy session is starting in 30 minutes. Please be ready to join."
);
```

### 3. sendEventEmailNotes
Email for note-related events.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
<h1>{bodyText1}</h1>
[Link to Lavni]
```

**Example Usage:**
```typescript
EmailService.sendEventEmailNotes(
  user,
  "New session notes available",
  "Your therapist has shared notes from your recent session. Login to view them."
);
```

## Authentication Emails

### 4. sendVerifyEmail
Email with verification code.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
{bodyText1}

Your verification code is: {verificationCode}

{bodyText2}
[Link to Lavni]
```

**Example Email:**
```
Subject: Verify your Lavni account
Body:
Welcome to Lavni! Please verify your account to get started.

Your verification code is: 123456

This code will expire in 10 minutes.
```

### 5. sendForgetPasswordEmail
Email for password reset.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
We've received a request to reset your password.

[Password Reset Link]

If you didn't request this, please ignore this email.
```

**Example Email:**
```
Subject: Reset your Lavni password
Body:
We've received a request to reset your password.

[Password Reset Link Button]

If you didn't request this, please ignore this email.
This link will expire in 24 hours.
```

## Welcome & Onboarding Emails

### 6. sendWelcomeEmailClient
Welcome email for new clients.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Welcome to Lavni!

We're excited to have you join our platform. Here's what you can do next:
- Complete your profile
- Browse available therapists
- Schedule your first session

[Link to Get Started]
```

**Example Email:**
```
Subject: Welcome to Lavni!
Body:
Hi Maria,

Welcome to Lavni! We're excited to have you join our mental health platform.

Here's what you can do next:
- Complete your profile by adding your insurance details
- Browse our therapists who match your needs
- Schedule your first session

[Get Started Button]

If you have any questions, our support team is here to help.
```

### 7. sendSubscriptionEmail
Email related to subscription changes.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {user's name},

{bodyText1}

[Link to Lavni]
```

**Example Email:**
```
Subject: Your Lavni Subscription Update
Body:
Hi Thomas,

Your subscription has been successfully upgraded to Premium. You now have access to additional features including priority booking and expanded therapist selection.

[View Your Subscription]
```

## Payment & Insurance Emails

### 8. sendCoPaymentEmailForInsurance
Reminder for copayment due.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

This is a friendly reminder about your copayment of ${coPayment} for your session on {date} at {time}.

Please use the link below to make your payment:
{paymentLink}

[Link to Lavni]
```

**Example Email:**
```
Subject: Copayment Due for Your Therapy Session
Body:
Hi Michael,

This is a friendly reminder about your copayment of $25 for your session on April 10, 2025 at 2:00 PM.

Please use the link below to make your payment:
https://pay.lavni.com/copay/12345

[Make Payment Button]
```

### 9. sendCoPaymentEmailForInsurancePaid
Confirmation of copayment received.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Thank you for your copayment for the session on {createdAt}. Your payment has been processed successfully.

[Link to Lavni]
```

**Example Email:**
```
Subject: Copayment Received - Thank You
Body:
Hi Rachel,

Thank you for your copayment for the session on April 5, 2025. Your payment has been processed successfully.

[View Receipt Button]
```

## Appointment Reminder Emails

### 10. send15MinuteAppointmentReminder
Reminder sent 15 minutes before appointment.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Your session with {senderName} starts in 15 minutes.

[Link to Join Session]
```

**Example Email:**
```
Subject: Your therapy session starts in 15 minutes
Body:
Hi David,

Your session with Dr. Lisa Johnson starts in 15 minutes.

[Join Session Button]
```

### 11. send30MinuteAppointmentReminder
Reminder sent 30 minutes before appointment.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

Your session with {senderName} starts in 30 minutes.

[Link to Join Session]
```

### 12. sendNextDayAppointmentsReminder
Reminder sent a day before appointment.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname},

This is a reminder that you have an appointment with {senderName} tomorrow.

[Link to View Appointment]
```

**Example Email:**
```
Subject: Reminder: Your Therapy Appointment Tomorrow
Body:
Hi Patricia,

This is a reminder that you have an appointment with Dr. James Wilson tomorrow at 3:00 PM EST.

[View Appointment Details]
```

## Account Status Emails

### 13. monthlyWithdrawalInitiated
Notification about monthly withdrawal for therapists.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname}!

Your monthly withdrawal is initiated.

Login to the dashboard to check more details.

[Link to View Appointments]
```

**Example Email:**
```
Subject: Your referral reward transfers initiated
Body:
Hi Dr. Thompson!

Your monthly withdrawal is initiated.

Login to the dashboard to check more details.

[View Earnings Button]

Thank you.

Lavni Inc.
```

### 14. clientPremiumStatusChanged
Notification about premium status change.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Hi {firstname}!

{premiumStatus == ACTIVE ? "Great news! Your Lavni account has been upgraded to Premium, unlocking additional features and benefits. We hope you enjoy the enhanced experience." : "Your premium access has been revoked."}

[Link to Lavni]
```

**Example for Premium Activation:**
```
Subject: Your Lavni Premium Membership is Active!
Body:
Hi Jessica!

Great news! Your Lavni account has been upgraded to Premium, unlocking additional features and benefits. We hope you enjoy the enhanced experience.

[View Premium Benefits Button]
```

## Form & Document Emails

### 15. sendTherapyPlanSignatureEmail
Request for signature on therapy plan.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Please review and sign your therapy plan.

[Link to Sign Document]
```

**Example Email:**
```
Subject: Your Therapy Plan Requires Signature
Body:
Hi Samuel,

Your therapist, Dr. Emma Wilson, has created a therapy plan for you. Please review and sign the document to acknowledge you've received and understand your treatment plan.

[Review and Sign Button]

This plan outlines your treatment goals and approach for the coming sessions.
```

### 16. sendClinicalAssesmentSignatureEmail
Request for signature on clinical assessment.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Please review and sign your clinical assessment.

[Link to Sign Document]
```

**Example Email:**
```
Subject: Clinical Assessment Ready for Review
Body:
Hi Taylor,

Your therapist has completed your clinical assessment. Please review and sign the document.

[Review and Sign Assessment]

This assessment helps guide your treatment plan going forward.
```

## Insurance Eligibility Emails

### 17. sendAdminEmailForInsuranceEligibilitySuccess
Notification about successful insurance eligibility check.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
Insurance eligibility successful for:
Client: {clientFirstName}
Insurance: {insuranceName}
Status: {eligibilityStatus}
Copayment: ${coPayment}
Therapist: {therapistFirstName}
Appointment: {appointmentStartTime}
```

**Example Email:**
```
Subject: Insurance Eligibility Check - Success
Body:
Insurance eligibility successful for:
Client: Jennifer Martinez
Insurance: Blue Cross Blue Shield
Status: Active
Copayment: $30
Therapist: Dr. Robert Johnson
Appointment: 2025-04-15 14:00
```

## Verification & Reminder Emails

### 18. sendIncompleteFormSubmissionReminder
Reminder about incomplete form submission.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: {subject}
Body:
This is a reminder about an incomplete {formName} for client {client.firstname} {client.lastname}.

[Link to Complete Form]
```

**Example Email:**
```
Subject: Reminder: Incomplete Intake Form
Body:
Hi Dr. Wilson,

This is a reminder about an incomplete Intake Assessment Form for client Sarah Johnson.

[Complete Form Button]

Completing this form is necessary before the first session can be scheduled.
```

## Marketing & Referral Emails

### 19. sendTherapistReferralLinkEmail
Email with therapist referral link.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: Join Lavni's Therapist Network
Body:
{body}

[Referral Link]
```

**Example Email:**
```
Subject: Join Lavni's Therapist Network
Body:
Hello,

A therapist from Lavni thinks you would be a great addition to our platform. Lavni helps connect therapists with clients seeking mental health support.

By joining Lavni, you'll gain:
- Access to more clients
- Insurance billing support
- Scheduling and practice management tools

[Join Lavni Button]

Use the referral code THREF123 when signing up.
```

### 20. sendClientReferralLinkEmailByClient
Email with client referral link.

**Template Structure:**
```html
[HTML Email with header and footer]
Subject: Client Referral Link
Body:
{messageContent}

[Referral Link]
```

**Example Email:**
```
Subject: Your friend invited you to try Lavni
Body:
Hi there,

Your friend Michael Thompson thinks Lavni could help you find the right therapist. Lavni connects you with licensed therapists who match your needs and accept your insurance.

Click below to learn more:

[Try Lavni Button]

Use referral code CLIENT789 for a special offer on your first session.
```

## Notes:
- All emails use HTML templates with Lavni branding
- Emails include a header with Lavni logo and a footer with social media links
- Development/staging environments log emails to Slack instead of sending
- Production environment sends actual emails via SendGrid
