#!/usr/bin/env node

/**
 * Simple message decryption script
 * This script decrypts messages that are encrypted with AES256
 * It uses the same encryption key as the main application
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const aes256 = require('aes256');
const readline = require('readline');

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Get the encryption key from environment variables
const MESSAGE_ENCRYPTION_KEY = process.env.MESSAGE_ENCRYPTION_KEY;

if (!MESSAGE_ENCRYPTION_KEY) {
  console.error('Error: MESSAGE_ENCRYPTION_KEY not found in .env file');
  process.exit(1);
}

/**
 * Decrypt a message using AES256 algorithm
 * @param {string} encryptedText - The encrypted text to decrypt
 * @returns {string} - The decrypted text
 */
function decryptMessage(encryptedText) {
  try {
    return aes256.decrypt(MESSAGE_ENCRYPTION_KEY, encryptedText);
  } catch (error) {
    return `Decryption failed: ${error.message}`;
  }
}

// Function to handle direct input from command line arguments
function handleDirectInput() {
  // Check if there's a command line argument
  const input = process.argv[2];
  
  if (input) {
    console.log('\nDecrypted message:');
    console.log(decryptMessage(input));
    return true;
  }
  return false;
}

// Function to create interactive prompt
function createPrompt() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('\n=== Message Decryption Tool ===');
  
  rl.question('Enter encrypted text: ', (encryptedText) => {
    if (encryptedText) {
      console.log('\nDecrypted message:');
      console.log(decryptMessage(encryptedText));
    } else {
      console.log('No input provided.');
    }
    rl.close();
  });
}

// Main execution
function main() {
  // First check if direct input is provided
  const hasDirectInput = handleDirectInput();
  
  // If no direct input, start interactive prompt
  if (!hasDirectInput) {
    createPrompt();
  }
}

main(); 