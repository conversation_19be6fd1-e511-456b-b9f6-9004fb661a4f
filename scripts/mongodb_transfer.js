#!/usr/bin/env node

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const util = require('util');
const execPromise = util.promisify(exec);

// Configuration
const config = {
    remote: {
        uri: process.env.REMOTE_MONGODB_URI || "****************************************************************",
        dbName: 'lavni_db'
    },
    local: {
        uri: process.env.LOCAL_MONGODB_URI || 'mongodb://localhost:27017',
        dbName: 'lavni_db_2023'
    },
    backupDir: path.join(__dirname, 'backup')
};

// Ensure backup directory exists
if (!fs.existsSync(config.backupDir)) {
    fs.mkdirSync(config.backupDir, { recursive: true });
}

async function executeCommand(command) {
    try {
        const { stdout, stderr } = await execPromise(command);
        if (stderr) console.error('Command stderr:', stderr);
        return stdout;
    } catch (error) {
        console.error(`Error executing command: ${command}`);
        console.error(error);
        throw error;
    }
}

async function exportDatabase() {
    console.log('Starting database export...');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const archiveFile = path.join(config.backupDir, `backup_${timestamp}.archive`);
    
    const command = `mongodump --uri="${config.remote.uri}" --archive="${archiveFile}" --gzip`;
    
    try {
        await executeCommand(command);
        console.log('Export completed successfully');
        return archiveFile;
    } catch (error) {
        console.error('Export failed:', error);
        throw error;
    }
}

async function importDatabase(archiveFile) {
    console.log('Starting database import...');
    
    const command = `mongorestore --uri="${config.local.uri}" --archive="${archiveFile}" --gzip --drop --nsFrom="lavni_db.*" --nsTo="lavni_db_2023.*" --verbose`;
    
    try {
        await executeCommand(command);
        console.log('Import completed successfully');
    } catch (error) {
        console.error('Import failed:', error);
        throw error;
    }
}

async function main() {
    try {
        // console.log('Starting database transfer process...');
        
        // // Step 1: Export from remote database
        const backupPath = await exportDatabase();
        console.log(`Backup created at: ${backupPath}`);

        // const backupPath = "/Users/<USER>/mylavni/lavni-api/scripts/backup/lavni_db_backup_2025-04-28_10-33-48.gz"
        
        // Step 2: Import to local database
        await importDatabase(backupPath);
        
        console.log('Database transfer completed successfully!');
    } catch (error) {
        console.error('Database transfer failed:', error);
        process.exit(1);
    }
}

// Execute the script
main();
