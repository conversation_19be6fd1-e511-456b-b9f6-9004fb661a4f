// Connect to database (assuming you're already connected to the correct database, e.g. use myDatabase)
// Update each insurance company in the insuranceMap

db.insurancecompanies.updateOne(
    { "insuranceCompany": "BCBS of NC" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Cigna Behavioral" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "AETNA" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Healthy Blue Medicaid" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Carolina complete health" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "United Healthcare" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Blue Cross Blue Shield Federal" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Anthem" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Wellcare Medicaid" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Village heart beat project" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "United Health Care Community Plan" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "AmeriHealth" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "My Cigna" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Blue Cross Blue Sheild" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "NC Medicare Part B" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "First Health Network" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "First Choice by Select Health" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Absolute Total Care" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Healthy Blue by Blue Choice of SC" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Molina Healthcare of South Carolina" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Medical Home Network South Carolina Solutions" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Blue Cross Blue Shield South Carolina " },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Peach State Health Plan" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Amerigroup" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "CareSource" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "NC Medicaid" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "South Carolina Healthy Connections" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Alliance Health" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Tricare" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Trillium" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Open Path" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "United UMR" },
    { $set: { "isCommercialInsurance": true, "isMedicaid": false } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "Partners" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": true } }
);

db.insurancecompanies.updateOne(
    { "insuranceCompany": "HopeShield" },
    { $set: { "isCommercialInsurance": false, "isMedicaid": false } }
);