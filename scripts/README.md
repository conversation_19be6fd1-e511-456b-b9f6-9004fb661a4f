# MongoDB Transfer Script

This script helps transfer data from a remote MongoDB to a local MongoDB.

## Requirements

- Node.js
- MongoDB Tools (mongodump and mongorestore)
- Access permissions to both databases

## Installation

1. Ensure MongoDB Tools are installed:
   ```bash
   # On macOS with Homebrew
   brew install mongodb-database-tools
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

## Configuration

The script uses the following environment variables:

- `REMOTE_MONGODB_URI`: Connection URI to the remote MongoDB (default:)
- `LOCAL_MONGODB_URI`: Connection URI to the local MongoDB (default: mongodb://localhost:27017/lavni_db_2023)

## Usage

1. Run the script:
   ```bash
   ./mongodb_transfer.js
   ```

2. Or with custom environment variables:
   ```bash
   REMOTE_MONGODB_URI="your_remote_uri" LOCAL_MONGODB_URI="your_local_uri" ./mongodb_transfer.js
   ```

## Workflow

1. The script creates a backup from the remote database using mongodump.
2. The data is compressed in gzip format.
3. The data is restored to the local database using mongorestore.
4. Backup files are stored in the ./backup directory.

## Error Handling

The script includes error handling for:
- Database connection errors
- Access permission errors
- Errors during the export/import process
- Directory creation errors

