#!/bin/bash

# Important paths
BACKUP_DIR="/home/<USER>/mongo_backups"
FILE_NAME="lavni_db_backup_$(date +'%Y-%m-%d_%H-%M-%S')_UTC.gz"
S3_BUCKET="s3://backup-mongo-mylavni"

# MongoDB connection string
MONGO_URI="************************************************************************************"

# Step 1: Create backup directory (if it doesn't exist)
mkdir -p $BACKUP_DIR

# Step 2: Export database to .gz file
echo "Starting MongoDB backup..."
mongodump --uri="$MONGO_URI" --archive="$BACKUP_DIR/$FILE_NAME" --gzip

# Check if backup file was created
if [[ -f "$BACKUP_DIR/$FILE_NAME" ]]; then
    echo "Backup completed: $FILE_NAME"

    # Step 3: Upload file to S3
    echo "Uploading backup to S3 bucket..."
    aws s3 cp "$BACKUP_DIR/$FILE_NAME" "$S3_BUCKET"

    if [[ $? -eq 0 ]]; then
        echo "Upload to S3 successful: $S3_BUCKET/$FILE_NAME"
        
        # Step 4: Delete local backup file after successful upload
        echo "Removing local backup file..."
        rm -f "$BACKUP_DIR/$FILE_NAME"
        echo "Local backup file removed."
    else
        echo "Upload to S3 failed!"
        exit 1
    fi
else
    echo "Backup failed. File not found."
    exit 1
fi

# Step 5: Completion
echo "Backup and upload task completed!"