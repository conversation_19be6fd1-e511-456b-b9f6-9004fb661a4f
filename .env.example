NODE_ENV=development
PORT=3000

#APP_URL=http://localhost:3000

API=http://localhost:3010
APP_URL=http://localhost:3000
APP_NAME="Lavni Project"

JWT_SECRET="RsBOc!h&5Dur"

# TEST_MONGOOSE_URI=mongodb://localhost:27017/lavni_db_test
MONGOOSE_URI=mongodb://localhost:27017/lavni_db
MONGO_DB_NAME=lavni_db

TIMEZONE=Asia/Colombo
FILE_ACCESS_URL=/api/public/file
AUTH_FILE_ACCESS_URL=/api/auth/file
DEFAULT_FILE=./resources/not_found.png
UPLOAD_PATH=uploads

SENDGRID_KEY=
SENDGRID_KEY_SIGN_IN=
SENGRID_SENDER=
ADMIN_EMAILS=
SUB_ADMIN_EMAILS_LEVEL1=
DEV_EMAILS=

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

DEEPGRAM_KEY=

STRIPE_SECRET_KEY=
STRIPE_PRICE_ID=

PRICE_FOR_ONE_MINUTE=1

DEFAULT_PAY_RATE_OF_THERAPIST=50

TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
TWILIO_CONVERSATION_SERVICE_ID=
TWILIO_CONVERSATION_PHONE_NUMBER=
MESSAGE_ENCRYPTION_KEY=

TWILIO_CALLING_PHONE_NUMBER=
TWILIO_API_SECRET=
TWILIO_TWIML_APP_SID=
TWILIO_API_KEY=

CHANGE_HEALTHCARE_CLIENT_ID=
CHANGE_HEALTHCARE_CLIENT_SECRET=
CHANGE_HEALTHCARE_GRANT_TYPE=client_credentials
CHANGE_HEALTHCARE_TOKEN_URL=https://sandbox.apigw.changehealthcare.com/apip/auth/v2/token
CHANGE_HEALTHCARE_ELIGIBILITY=https://sandbox.apigw.changehealthcare.com/medicalnetwork/eligibility/v3
CHANGE_HEALTHCARE_CLAIM_VALIDATION=https://sandbox.apigw.changehealthcare.com/medicalnetwork/professionalclaims/v3/validation
CHANGE_HEALTHCARE_CLAIM_SUBMISSION=https://sandbox.apigw.changehealthcare.com/medicalnetwork/professionalclaims/v3/submission
CHANGE_HEALTHCARE_CLAIM_STATUS=https://sandbox.apigw.changehealthcare.com/medicalnetwork/claimstatus/v2

CHANGE_HEALTHCARE_ELIGIBILITY_MD=https://svc.claim.md/services/eligdata/
CHANGE_HEALTHCARE_CLAIM_MD_UPLOAD=https://svc.claim.md/services/upload/
CHANGE_CLAIM_MD_ERALIST=https://svc.claim.md/services/eralist/
CHANGE_CLAIM_MD_ERADATA=https://svc.claim.md/services/eradata/
CLAIM_MD_ACCOUNT_KEY=

LAVNI_ORGANIZATION_NAME=lavni
LAVNI_NPI=
LAVNI_TAXID=

DIAGONOSIS_NOTE_DOWNLOAD_PATH=uploads\\DIAGONOSIS_NOTES\\diagonosisNote.pdf

ZOOM_API_KEY=
ZOOM_API_SECRET=
ZOOM_SDK_KEY=
ZOOM_SDK_SECRET=
ZOOM_API_BASE_URL=https://api.zoom.us/v2/videosdk
ZOOM_API_KEY_EXPIRATION_TIME=7200
ZOOM_SDK_KEY_EXPIRATION_TIME=7200

OPEN_AI_API_KEY=

INSTAGRAM_FEED_ACCESS_TOKEN=

TOKBOX_API_KEY=
TOKBOX_SECRET=

AWS_S3_REGION=us-east-1
AWS_S3_ACCESSKEY_ID=
AWS_S3_SECRET_ACCESSKEY=
AWS_S3_BUCKET=
AWS_S3_BUCKET_TRAINING_DOCUMENTS=

AWS_S3_BUCKET_REGULER_AUDIO_CALLS=

DEEPGRAM_API_KEY =

FIREBASE_TYPE=""
FIREBASE_PROJECT_ID=""
FIREBASE_PRIVATE_KEY_ID=""
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=""
FIREBASE_CLIENT_ID=""
FIREBASE_AUTH_URI=""
FIREBASE_TOKEN_URI=""
FIREBASE_AUTH_PROVIDER_CERT_URL=""
FIREBASE_CLIENT_X509_CERT_URL=""
FIREBASE_UNIVERSE_DOMAIN="googleapis.com"

JOTFORM_API_KEY = ""
NODE_ENV=""
SLACK_WEBHOOK_URL_NOTI_SERVICE_STAGING=""

# BetterStack Configuration
BETTERSTACK_API_URL=
BETTERSTACK_API_KEY=