# Lavni API

Backend API service for Lavni project, built with Node.js, TypeScript, and MongoDB.

## Prerequisites

### 1. Install NVM (Node Version Manager)

#### Windows:
1. Download NVM for Windows from: https://github.com/coreybutler/nvm-windows/releases
2. Download the `nvm-setup.exe` file and install
3. Open a new terminal and check the installation:
   ```bash
   nvm version
   ```

#### MacOS/Linux:
```bash
# Install NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Reload profile
source ~/.bashrc  # or source ~/.zshrc if using zsh

# Check the installation
nvm --version
```

### 2. Install Node.js and npm
```bash
# Install Node.js 18.17.1
nvm install 18.17.1

# Use Node.js 18.17.1
nvm use 18.17.1

# Check version
node -v  # Should output: v18.17.1
npm -v   # Should output: 9.6.7
```

### 3. Install PM2 (Process Manager)
```bash
npm install -g pm2
```

### 4. Install MongoDB
- MongoDB (v4.4 or higher)

### 5. Install TypeScript
- TypeScript

## Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lavni-api
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   - Copy the example environment file:
     ```bash
     cp .env.example .env
     ```
   - Update the `.env` file with your configurations:
     - `MONGOOSE_URI`: MongoDB connection string
     - `JWT_SECRET`: Secret key for JWT authentication
     - Configure other required environment variables like:
       - SendGrid credentials (for email)
       - Google OAuth credentials
       - AWS credentials (if using S3)
       - Other API keys as needed

4. **Database Setup**
   - Make sure MongoDB is running locally or update the `MONGOOSE_URI` to point to your MongoDB instance
   - Run database migrations:
     ```bash
     npm run migrate
     ```
   - (Optional) Run database seeding:
     ```bash
     npm run seed
     ```

## Running the Application

### Development Mode
```bash
npm start
```
This will start the server using nodemon for auto-reloading during development.

### Production Mode
1. Build the project:
   ```bash
   npm run build
   ```
2. Start the production server with PM2:
   ```bash
   pm2 start dist/server.js --name "lavni-api"

# Server sẽ chạy mặc định ở port 3000 (có thể thay đổi trong file .env)
# Kiểm tra server đã chạy:
curl http://localhost:3000

# Xem logs để kiểm tra server status
pm2 logs lavni-api

# Một số lệnh PM2 hữu ích khác
pm2 list              # Xem danh sách các process
pm2 stop lavni-api    # Dừng server
pm2 restart lavni-api # Khởi động lại server
pm2 delete lavni-api  # Xóa process
```

## Cron Jobs
The application includes scheduled tasks in the `src/cron-jobs` directory. These jobs are automatically started when the server runs. Current cron jobs include:
- `every-two-minutes.ts`: Tasks that run every 2 minutes

## Testing
Run the test suite:
```bash
npm test
```

For watch mode during development:
```bash
npm run test:watch
```

## Project Structure
- `src/`: Source code directory
  - `cron-jobs/`: Scheduled tasks
  - `migrations/`: Database migrations
  - `seed/`: Database seeding scripts
- `uploads/`: File upload directory
- `dist/`: Compiled JavaScript output
- `test/`: Test files

## Additional Notes
- The server runs on port 3000 by default (configurable via PORT in .env)
- File uploads are stored in the `uploads/` directory
- Timezone is set to Asia/Colombo by default (configurable via TIMEZONE in .env)
- Supports various integrations including:
  - AWS S3
  - SendGrid
  - Google OAuth
  - Firebase
  - Braintree payments
