
import requests
import json
import os

# Function to create a session and make a POST request
from requests.exceptions import RequestException

def submit_eligibility(data):
    url = "https://svc.claim.md/services/eligdata/"
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json"
    }
    try:
        with requests.Session() as session:
            response = session.post(url, data=data, headers=headers)
            # print(response)
            response.raise_for_status()  # Raises an HTTPError for bad responses
            return response
    except RequestException as e:
        print(f"An error occurred: {e}")
        return response

# Function to process the eligibility response
def process_eligibility_response(response):
    if response.status_code == 200:
        print("Eligibility data submitted successfully.")
        try:
            data = json.loads(response.text)
            print("Response Data:", data)  # Debugging line to inspect the structure
            benefits = data['elig']['benefit']
            return [format_benefit(b) for b in benefits if b['benefit_coverage_description'] == 'Co-Payment']
        except KeyError as e:
            print(f"Key error: {e} - check JSON structure.")
            print("Full response content:", response.text)
            return None
    else:
        print("Failed to submit eligibility data.")
        print(response.status_code, response.text)
        return None


# Function to format benefit data
def format_benefit(benefit):
    return {
        "amount": benefit['benefit_amount'],
        "description": benefit['benefit_description'],
        "Code": benefit['benefit_code']
    }


def extract_insurance_info(json_string):
    """
    Extracts insurance-related information from a JSON string.

    Parameters:
        json_string (str): A string containing JSON data.

    Returns:
        dict: A dictionary containing the required insurance details.
    """
    try:
        # Convert JSON string to dictionary
        data = json.loads(json_string)
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        return {}

    # Check if 'elig' key is in the data dictionary
    eligibility_info = data.get('elig', {})

    # Ensure that eligibility_info is a dictionary
    if not isinstance(eligibility_info, dict):
        print("Error: 'elig' key does not contain a dictionary.")
        return {}

    # Extract fields with default values if keys are missing
    extracted_info = {
        'ins_name_f': eligibility_info.get('ins_name_f', 'Unknown'),
        'ins_name_l': eligibility_info.get('ins_name_l', 'Unknown'),
        'ins_city': eligibility_info.get('ins_city', 'Unknown'),
        'ins_state': eligibility_info.get('ins_state', 'Unknown'),
        'ins_zip': eligibility_info.get('ins_zip', 'Unknown'),
        'ins_dob': eligibility_info.get('ins_dob', 'Unknown'),
        'ins_sex': eligibility_info.get('ins_sex', 'Unknown'),
        'ins_addr_1': eligibility_info.get('ins_addr_1', 'Unknown'),
    }
    print(eligibility_info)
    return extracted_info

data = {'AccountKey': '18420_X07Qzhib2EmpXA42arGNCR6e',
        'ins_name_f': 'Ashley',
        'ins_name_l': 'TEAGUE',
        'payerid': '68069',
        'ins_dob': '********',
        'pat_rel': '18',
        'fdos': '********',
        'ins_sex': 'F',
        'prov_npi': '**********',
        'prov_taxid': '*********',
        'ins_number':'900756197T'}

load = submit_eligibility(data)
print(load.text)