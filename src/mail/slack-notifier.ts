import axios from 'axios';
import { NodeHtmlMarkdown } from 'node-html-markdown';

const SLACK_WEBHOOK_URL_STAGING = process.env.SLACK_WEBHOOK_URL_NOTI_SERVICE_STAGING;
const SLACK_WEBHOOK_URL_PRODUCTION = process.env.SLACK_WEBHOOK_URL_NOTI_SERVICE_PRODUCTION;

export async function notiToSlack(content: string, isHTML: boolean = false) {
  try {
    let message = content;
    
    if (isHTML) {
      message = NodeHtmlMarkdown.translate(content);
    }

    if (process.env.NODE_ENV === 'staging' || process.env.NODE_ENV === 'development') {
      await axios.post(SLACK_WEBHOOK_URL_STAGING, { text: `NODE_ENV: ${process.env.NODE_ENV}: ${message}` });
    } else if (process.env.NODE_ENV === 'production') {
      await axios.post(SLACK_WEBHOOK_URL_PRODUCTION, { text: `NODE_ENV: ${process.env.NODE_ENV}: ${message}` });
    }
    
    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
}
