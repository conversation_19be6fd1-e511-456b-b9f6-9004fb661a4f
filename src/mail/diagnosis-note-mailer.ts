import { EmailService } from "./config";
import { AppLogger } from "../common/logging";
import { UserDao } from "../dao/user-dao";

interface FeedbackEmailData {
  therapistName: string;
  clientName: string;
  sessionDate: string;
  feedback: string;
}

export class DiagnosisNoteMailer {
  static async sendFeedbackEmail(treatmentHistory: any, lastFeedbackFromAuditor: string) {
    try {
      // Get therapist and client details
      const [therapist, client] = await Promise.all([
        UserDao.getUserById(treatmentHistory.therapistId),
        UserDao.getUserById(treatmentHistory.clientId)
      ]);

      if (!therapist || !client) {
        AppLogger.error('Could not find therapist or client for sending feedback email');
        return;
      }

      // Convert UTC time to US Eastern Time
      const sessionDate = new Date(treatmentHistory.meetingStartedTime);
      const usEasternTime = new Intl.DateTimeFormat('en-US', {
        timeZone: 'America/New_York',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }).format(sessionDate);

      await DiagnosisNoteMailer.sendFeedbackEmailTemplate({
        therapistName: therapist.firstname,
        clientName: `${client.firstname} ${client.lastname.charAt(0)}.`,
        sessionDate: usEasternTime,
        feedback: lastFeedbackFromAuditor
      }, therapist.email);

    } catch (error) {
      AppLogger.error('Error sending feedback email:', error);
    }
  }

  static async sendFeedbackEmailTemplate(data: FeedbackEmailData, receiverEmail: string) {
    const emailBody = `Dear ${data.therapistName},
Thank you for your dedication to providing high-quality care and supporting your clients in their mental health journey.

As part of our ongoing quality assurance process, we have conducted a review of your clinical documentation for the following session:
• Client: ${data.clientName}
• Session Date: ${data.sessionDate} (New York Time)

Upon review, we found that the session notes for this appointment do not fully align with Lavni's documentation standards and best practices for clinical record-keeping. Please see the auditor's detailed feedback below:

${data.feedback}

Accurate and comprehensive documentation is essential for continuity of care, treatment efficacy, and compliance with professional and ethical guidelines. We kindly ask that you review the feedback and make the necessary revisions or improvements as soon as possible.

If you have any questions or need further clarification, please do not hesitate to reach out. We appreciate your attention to this matter and your commitment to maintaining the highest standards in client care.

Best regards,
Lavni Inc.`;

    await EmailService.sendEmailToSelectedUsers(
      receiverEmail,
      emailBody,
      'Immediate Attention Required: Clinical Documentation Audit'
    );
  }
}
