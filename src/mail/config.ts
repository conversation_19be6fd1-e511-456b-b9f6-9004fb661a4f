require("dotenv").config();
import { AppLogger } from "../common/logging";
import { IClient, PremiumStatus } from "../models/client-model";
import { ITherapist } from "../models/therapist-model";
import { DUser } from "../models/user-model";
import { notiToSlack } from "./slack-notifier";
var nodemailer = require("nodemailer");
var nodemailerSendgrid = require("nodemailer-sendgrid");
var jwt = require("jsonwebtoken");
var fs = require("fs");

export namespace EmailService {
  const emailHeader = `<!DOCTYPE html> <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:o="urn:schemas-microsoft-com:office:office"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width,initial-scale=1"> <meta name="x-apple-disable-message-reformatting"> <title></title> <!--[if mso]> <noscript> <xml> <o:OfficeDocumentSettings> <o:PixelsPerInch>96</o:PixelsPerInch> </o:OfficeDocumentSettings> </xml> </noscript> <![endif]--> <style> table, td, div, h1, p {font-family: Arial, sans-serif;} </style> </head> <body style="margin:0;padding:0;"> <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;background:#ffffff;"> <tr> <td align="center" style="padding:0;"> <table role="presentation" style="width:602px;border-collapse:collapse;border:1px solid #cccccc;border-spacing:0;text-align:left;"> <tr> <td align="center" style="padding:40px 0 10px 0;background:#fff;"> <img src="https://mylavni.com/static/assets/img/lavni_logo.png" alt="" width="300" style="height:auto;display:block;" /> </td> </tr>`;
  const emailFooter = `<tr> <td style="padding:30px;background:#FF8000;"> <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;font-size:9px;font-family:Arial,sans-serif;"> <tr> <td style="padding:0;width:50%;" align="left"> <p style="margin:0;font-size:14px;line-height:16px;font-family:Arial,sans-serif;color:#ffffff;"> &copy; ${new Date().getFullYear()} Lavni<br/><a href="https://mylavni.com/" style="color:#ffffff;text-decoration:none;">All rights reserved.</a> </p> </td> <td style="padding:0;width:50%;" align="right"> <table role="presentation" style="border-collapse:collapse;border:0;border-spacing:0;"> <tr> <td style="padding:0 0 0 10px;width:38px;"> <a href="https://www.linkedin.com/company/lavni/" style="color:#ffffff;"><img src="https://mylavni.com/static/assets/img/social/linkedin.png" alt="Twitter" width="38" style="height:auto;display:block;border:0;" /></a> </td> <td style="padding:0 0 0 10px;width:38px;"> <a href="https://www.facebook.com/lavnihealth" style="color:#ffffff;"><img src="https://mylavni.com/static/assets/img/social/facebook.png" alt="Facebook" width="38" style="height:auto;display:block;border:0;" /></a> </td> <td style="padding:0 0 0 10px;width:38px;"> <a href="https://www.instagram.com/lavnimentalhealth" style="color:#ffffff;"><img src="https://mylavni.com/static/assets/img/social/instagram.png" alt="Facebook" width="38" style="height:auto;display:block;border:0;" /></a> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </body> </html>`;

  const emailHeaderWelcome = `<!DOCTYPE html> <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:o="urn:schemas-microsoft-com:office:office"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width,initial-scale=1"> <meta name="x-apple-disable-message-reformatting"> <title></title> <!--[if mso]> <noscript> <xml> <o:OfficeDocumentSettings> <o:PixelsPerInch>96</o:PixelsPerInch> </o:OfficeDocumentSettings> </xml> </noscript> <![endif]--> <style> table, td, div, h1, p {font-family: Arial, sans-serif;} </style> </head> <body style="margin:0;padding:0;"> <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;background:#ffffff;"> <tr> <td align="center" style="padding:0;"> <table role="presentation" style="width:602px;border-collapse:collapse;border:1px solid #cccccc;border-spacing:0;text-align:left;"><tr>
  <td align="center" style="padding:0px 0 10px 0;background:#fff;position:relative;">
      <img src="http://lavni.efito.xyz/static/assets/img/email/header_bar_white.png" alt="" style="height:auto; width:100%; display:block;" />
  </td>
</tr>`;

  // Helper function to get email recipients (primary and secondary if available)
  function getEmailRecipients(user: DUser): string {
    if (user.secondaryEmail) {
      return `${user.email}, ${user.secondaryEmail}`;
    }
    return `${user.email}`;
  }

const transportSingIn = nodemailer.createTransport(
  nodemailerSendgrid({
    apiKey: process.env.SENDGRID_KEY_SIGN_IN,
  })
);

const transport = nodemailer.createTransport(
  nodemailerSendgrid({
    apiKey: process.env.SENDGRID_KEY,
  })
);

if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'staging') {
  transportSingIn.sendMail = async (options: any) => {
    try {
      const content = `To: ${options.to}\nSubject: ${options.subject}\nFrom: ${options.from}`;
      notiToSlack(content);
      notiToSlack(options.html, true);
      return true;
    } catch (error) {
      console.error('Error in mock sendMail:', error);
      throw error;
    }
  };

  transport.sendMail = async (options: any) => {
    try {
      const content = `To: ${options.to}\nSubject: ${options.subject}\nFrom: ${options.from}`;
      notiToSlack(content);
      notiToSlack(options.html, true);
      return true;
    } catch (error) {
      console.error('Error in mock sendMail:', error);
      throw error;
    }
  };
}
else if (process.env.NODE_ENV === 'production') {
  const originalSendMailOfTransportSingIn = transportSingIn.sendMail.bind(transportSingIn);
  const originalSendMailOfTransport = transport.sendMail.bind(transport);

  transportSingIn.sendMail = async (options: any) => {
    try {
      const content = `To: ${options.to}\nSubject: ${options.subject}\nFrom: ${options.from}`;
      await notiToSlack(content);
      await notiToSlack(options.html, true);

      return await originalSendMailOfTransportSingIn(options);
    } catch (error) {
      console.error('Error in production sendMail:', error);
      throw error;
    }
  };

  transport.sendMail = async (options: any) => {
    try {
      const content = `To: ${options.to}\nSubject: ${options.subject}\nFrom: ${options.from}`;
      await notiToSlack(content);
      await notiToSlack(options.html, true);

      return await originalSendMailOfTransport(options);
    } catch (error) {
      console.error('Error in production sendMail:', error);
      throw error;
    }
  };
}


  export async function sendEventEmail(
    user: DUser,
    subject: string,
    bodyText1?: string,
    bodyText2?: string,
    otherUserName?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: getEmailRecipients(user),
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1} ${otherUserName != null && otherUserName
          }</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${bodyText2}</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL
          }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;"> Visit Lavni</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendEventReminderEmail(
    user: DUser,
    subject: string,
    bodyText1?: string,
    bodyText2?: string,
    otherUserName?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: getEmailRecipients(user),
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h2 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1}</h2>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;"> Visit Lavni</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendEventEmailNotes(
    user: DUser,
    subject: string,
    bodyText1?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: getEmailRecipients(user),
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1}</h1>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;"> Visit Lavni</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendEventMeetingLinkEmail(
    user: DUser,
    subject: string,
    bodyText1?: string,
    bodyText2?: string,
    // bodyText3?: string,
    otherUserName?: string
  ) {
    try {
      const recipients = getEmailRecipients(user);
      AppLogger.info(`Send Event Email: ${bodyText1} ${otherUserName}, User Email: ${recipients}`)
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: recipients,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1} ${otherUserName != null && otherUserName
          }</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${bodyText2} </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      AppLogger.error(`Send Event Email: Error Occured For User Email: ${user?.email}, ${error}`)
      return false;
    }
  }

  export async function sendReminderToGroupCall(
    userEmail: string,
    subject: string,
    bodyText1?: string,
    bodyText2?: string,
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${userEmail}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1}</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${bodyText2} </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  //   <tr>
  //   <td style="padding:0;">
  //     <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
  //       <a href="${bodyText3}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
  //         <span style="color: #FF8000; font-size:20px;">Join Meeting </span>
  //       </a>
  //     </div>
  //   </td>
  // </tr>

  // export async function sendVacationInfoEmail(
  //   user: DUser,
  //   subject: string,
  //   bodyText1?: string,
  //   bodyText2?: string,
  //   otherUserName?: string
  // ) {
  //   try {
  //     transport.sendMail({
  //       from: process.env.SENGRID_SENDER,
  //       to: `${user.email}`,
  //       subject: subject,
  //       html:
  //         emailHeader +
  //         `<tr>
  //         <td style="padding:36px 30px 42px 30px;">
  //           <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
  //             <tr>
  //               <td style="padding:0 0 36px 0;color:#153643;">
  //                 <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1} ${otherUserName != null && otherUserName
  //         }</h1>
  //                 <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${bodyText2}</p>
  //               </td>
  //             </tr>
  //             <tr>
  //               <td style="padding:0;">
  //                 <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
  //                   <a href="${process.env.APP_URL
  //         }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
  //                     <span style="color: #FF8000; font-size:20px;"> Visit Lavni</span>
  //                   </a>
  //                 </div>
  //               </td>
  //             </tr>
  //           </table>
  //         </td>
  //       </tr>` +
  //         emailFooter,
  //     });
  //     return true;
  //   } catch (error) {
  //     return false;
  //   }
  // }

  export async function sendVerifyEmail(
    user: DUser,
    subject: string,
    verificationCode: string,
    bodyText1: string,
    bodyText2: string
  ) {
    try {
      transportSingIn.sendMail({
        from: process.env.SENGRID_SENDER,
        to: getEmailRecipients(user),
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1}</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${bodyText2}</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <span style="color: #153643; font-size:20px;">${verificationCode}</span>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendForgetPasswordEmail(user: DUser, subject: string) {
    const token = await jwt.sign(
      {
        _id: user._id,
        email: user.email,
      },
      process.env.JWT_SECRET
    );

    const url = `${process.env.APP_URL}/recover-password?token=${token}`;

    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: getEmailRecipients(user),
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Lavni - Reset Password!</h1>
                  <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Did you request to change your password? If so click here.
                  </p>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${url}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;"> Click Here</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  // export async function sendWelcomeEmail(user: DUser, subject: string) {
  //   try {
  //     transport.sendMail({
  //       from: process.env.SENGRID_SENDER,
  //       to: `${user.email}`,
  //       subject: subject,
  //       html:
  //         emailHeader +
  //         `<tr>
  //         <td style="padding:36px 30px 42px 30px;">
  //           <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
  //             <tr>
  //               <td style="padding:0 0 36px 0;color:#153643;">
  //                 <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Welcome To Lavni</h1>
  //                 <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
  //                   Find a therapist that aligns best with you. We strive to create a future where it is easy for anyone to access a mental health clinician. A space where you can be your authentic self while protecting your privacy.

  //                   <br/><br/>

  //                   Transparency and compliance is at the core of our electronic health records. We are a community filled with support to guide you every step of the way. Your experience matters.
  //                 </p>
  //                 <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
  //                   <a href="https://mylavni.com" style="color:#FF8000;text-decoration:underline; font-weight: bold;">Visit My Lavni</a></p>
  //               </td>
  //             </tr>
  //             <tr>
  //               <td style="padding:0;">
  //                 <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
  //                   <tr>
  //                     <td style="width:260px;padding:0;vertical-align:top;color:#153643;">
  //                       <p style="margin:0 0 25px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;"><img src="https://mylavni.com/static/assets/img/service1.png" alt="" width="260" style="height:auto;display:block;" /></p>
  //                       <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Providing content and resources to teach you about mental health. Peer on peer support which includes meeting mentors to coach you through embracing your challenges.</p>
  //                     </td>
  //                     <td style="width:20px;padding:0;font-size:0;line-height:0;">&nbsp;</td>
  //                     <td style="width:260px;padding:0;vertical-align:top;color:#153643;">
  //                       <p style="margin:0 0 25px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;"><img src="https://mylavni.com/static/assets/img/service2.png" alt="" width="260" style="height:auto;display:block;" /></p>
  //                       <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Meet with a therapist virtually (communicate with your mental health professional via text, voice call, and video call.)</p>
  //                     </td>
  //                   </tr>
  //                 </table>

  //                 <table role="presentation" style="margin-top: 30px;width:100%;border-collapse:collapse;border:0;border-spacing:0;">
  //                   <tr>
  //                     <td style="width:260px;padding:0;vertical-align:top;color:#153643;">
  //                       <p style="margin:0 0 25px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;"><img src="https://mylavni.com/static/assets/img/service3.png" alt="" width="260" style="height:auto;display:block;" /></p>
  //                       <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">We provide easy and affordable billing options directly linked to your insurance.</p>
  //                     </td>
  //                     <td style="width:20px;padding:0;font-size:0;line-height:0;">&nbsp;</td>
  //                     <td style="width:260px;padding:0;vertical-align:top;color:#153643;">
  //                       <p style="margin:0 0 25px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;"><img src="https://mylavni.com/static/assets/img/service4.png" alt="" width="260" style="height:auto;display:block;" /></p>
  //                       <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Our content is designed with you in mind to cultivate and stimulate your mental health. Our licensed clinicians are here to inspire and teach coping mechanisms to help you heal and thrive.</p>
  //                     </td>
  //                   </tr>
  //                 </table>
  //               </td>
  //             </tr>
  //           </table>
  //         </td>
  //       </tr>` +
  //         emailFooter,
  //     });

  //     return true;
  //   } catch (error) {
  //     return false;
  //   }
  // }

  export async function sendSubscriptionEmail(
    user: DUser,
    subject: string,
    bodyText1?: string,
    otherUserName?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${user.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1}
                  </h1>
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Lavni</h1>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">Visit Lavni</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAdminEmailWhenTherapistOrClientSignUp(
    subject: string,
    bodyText1?: string,
    userEmail?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: process.env.ADMIN_EMAILS,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1}
                  ${userEmail != null && userEmail
          }</h1>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL
          }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">Visit Lavni</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAdminEmailWhenTherapistToSubAdminLevel1(
    subject: string,
    bodyText1?: string,
    userEmail?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: process.env.SUB_ADMIN_EMAILS_LEVEL1,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1}
                  ${userEmail != null && userEmail
          }</h1>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL
          }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">Visit Lavni</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAdminEmailUserSignup(
    subject: string,
    userEmail?: string,
    password?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${userEmail}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">
                Welcome to Lavni!
                </h1>
              </td>
              </tr>

              <tr>
              <td style="padding:0 0 36px 0;color:#153643;">

                <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                Below are your login credentials:
                </p>


                <p style="margin:0 0 12px 0;font-size:16px;font-weight: bold;line-height:24px;font-family:Arial,sans-serif;">
                     Email: ${userEmail}
                </p>
                <p style="margin:0 0 12px 0;font-size:16px;font-weight: bold;line-height:24px;font-family:Arial,sans-serif;">
                     Password: ${password}
                </p>

                <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    For security reasons, we recommend changing your password after your first login. You can do this by navigating to your account settings.
                </p>


                <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                   Thank you for choosing Lavni! We appreciate your trust in our platform.
                </p>


                <p style="margin:0 0 12px 0;font-size:16px;line-height:5px;font-family:Arial,sans-serif;">
                        Best regards,

                </p>
                <p style="margin:0 0 12px 0;font-size:16px;line-height:5px;font-family:Arial,sans-serif;">
                        Lavni
                </p>
                <br/>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">Get Started</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAdminEmailForInsurancePayment(
    subject: string,
    email: string,
    firstname: string,
    payAmount: string,
    dueAmount: number,
    paymentMonth: string,
    attachmentName: string,
    attachmentPath: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        attachments: [
          {
            filename: attachmentName,
            path: attachmentPath,
            contentType: "application/pdf",
          },
        ],
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"}!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    We have covered $${payAmount} from you insurance for the month of ${paymentMonth}.

                    You have a pending balance of $${dueAmount}. Please kindly settle the rest of the payment to keep your Lavni account active.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    You can use the following link to navigate to your payments page.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">

                  <a href=${process.env.APP_URL}>${process.env.APP_URL
          }/payment-history</a></p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you.</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni Inc.</p><br><br>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendCoPaymentEmailForInsurance(
    subject: string,
    email: string,
    firstname: string,
    date: any,
    time: any,
    coPayment: string,
    paymentLink: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  Friendly reminder: Your therapy session on ${date} at ${time} has a $${coPayment} copayment due. Please click the link below to make the payment and ensure uninterrupted sessions.
                  </p>

                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  ${paymentLink}
                  </p>
                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you!</p>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni</p><br><br>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendCoPaymentEmailForInsurancePaid(
    subject: string,
    email: string,
    firstname: string,
    createdAt: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Dear ${firstname ? firstname : "cleint"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Your copayment for your session on ${createdAt} was processed successfully.Thank you for trusting us with your care.
                  </p>
                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best,</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni</p><br><br>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }
  export async function clientDuePaymentSettled(
    subject: string,
    email: string,
    firstname: string,
    payAmount: number,
    paymentMonth: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Successfully made payment of $${payAmount} for the month of ${paymentMonth}.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Login to your Lavni account to check more details.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">

                  <a href=${process.env.APP_URL}>${process.env.APP_URL
          }/payment-history</a></p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you.</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni Inc.</p><br><br>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAdminEmailForInsuranceAndClientPayment(
    subject: string,
    email: string,
    firstname: string,
    payAmount: string,
    dueAmount: number,
    paymentMonth: string,
    attachment: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        attachment: attachment,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    We have covered $${payAmount} from your insurance for the month of ${paymentMonth}.

                    And due amount of $${dueAmount} has been recovered from your default payment method.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Login to the dashboard to check more details.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">

                  <a href=${process.env.APP_URL}>${process.env.APP_URL
          }/payment-history</a></p> <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you.</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni Inc.</p><br><br>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function send15MinuteAppointmentReminder(
    subject: string,
    email: string,
    firstname: string,
    senderName: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  This is a reminder that your therapy session is in 15 minutes. Please sign into
                  <a href="https://mylavni.com/signin/" style="color: #ff8000; text-decoration: underline;">mylavni.com</a>
                  to join the session.
                  If you encounter any issues, feel free to reach out. Looking forward to our session.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  Best <br>
                  Lavni
                  </p>

                  <br>

                  <br>

                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}/appointments
                    }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">View Appointment</span>
                    </a>
                  </div>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you.</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:12px;font-family:Arial,sans-serif;">Lavni Inc.</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function send30MinuteAppointmentReminder(
    subject: string,
    email: string,
    firstname: string,
    senderName: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    You have a scheduled appointment with ${senderName} in next 30 minutes.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Login to the dashboard to connect with appointment.
                  </p>

                  <br>

                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}/appointments
                    }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">View Appointment</span>
                    </a>
                  </div>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you.</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:12px;font-family:Arial,sans-serif;">Lavni Inc.</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendNextHourAppointmentReminder(
    subject: string,
    email: string,
    firstname: string,
    senderName: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    You have a scheduled appointment with ${senderName} in next Hour.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Login to the dashboard to connect with appointment.
                  </p>

                  <br>

                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}/appointments
                    }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">View Appointment</span>
                    </a>
                  </div>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you.</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:12px;font-family:Arial,sans-serif;">Lavni Inc.</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendNextDayAppointmentsReminder(
    subject: string,
    email: string,
    firstname: string,
    senderName: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          },</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  I hope all is well! This is a gentle reminder of your upcoming session tomorrow with  ${senderName ? senderName : "There"
          }. You’ll get a notification 15 minutes before the event.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  As always, if you have any questions or concerns, you can send us an email or shoot me a text message.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  Enjoy your session ${firstname ? firstname : "There"},
                  </p>

                  <br>

                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}/appointments
                    }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">View Appointments</span>
                    </a>
                  </div>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Cheers,</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:12px;font-family:Arial,sans-serif;">Team Lavni</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function monthlyWithdrawalInitiated(
    subject: string,
    email: string,
    firstname: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Your monthly withdrawal is initiated.
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Login to the dashboard to check more details.
                  </p>

                  <br>

                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}/earnings
                    }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">View Appointments</span>
                    </a>
                  </div>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you.</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:12px;font-family:Arial,sans-serif;">Lavni Inc.</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function clientPremiumStatusChanged(
    subject: string,
    email: string,
    firstname: string,
    premiumStatus: PremiumStatus
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    ${premiumStatus == PremiumStatus.ACTIVE
            ? "Great news! Your Lavni account has been upgraded to Premium, unlocking additional features and benefits. We hope you enjoy the enhanced experience."
            : "Your premium access has been revoked."
          }
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Login to the dashboard to check more details.
                  </p>

                  <br>

                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}/payment-history
                    }" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">View Status</span>
                    </a>
                  </div>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you.</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:12px;font-family:Arial,sans-serif;">Lavni Inc.</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  export async function userStatusHasChangedBlock(
    subject: string,
    email: string,
    firstname: string,
    reasonToBlock: string,
    blockStatus: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Dear ${firstname}.</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  We regret to inform you that your account has been temporarily suspended due to ${reasonToBlock}.</p>

                  <br>
                  <p  style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">To resolve this matter and gain further assistance, we kindly ask you to reach out to our administrative <NAME_EMAIL>. They will provide you with the necessary support to address this issue promptly.We apologize for any inconvenience caused and appreciate your cooperation in this matter.</P>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:12px;font-family:Arial,sans-serif;">Lavni.</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  export async function userStatusHasChanged(
    subject: string,
    email: string,
    firstname: string,
    blockStatus: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    ${blockStatus == "blocked"
            ? "Sorry to inform you that your Lavni account is blocked."
            : "Your Lavni account is unblocked."
          }
                  </p>

                  <br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you.</p><br>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:12px;font-family:Arial,sans-serif;">Lavni Inc.</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendMarketingEmail1(
    subject: string,
    customEmailArray: any,
    emailBody: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: customEmailArray.email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 15px 0;font-family:Arial,sans-serif;">Hello ${customEmailArray.firstname} ${customEmailArray.lastname},</h1>

                  ${emailBody}

                </td>
              </tr>
            </table>
          </td>
          </tr>` +
          emailFooter,
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  export async function newPostInDiscover(
    subject: string,
    title: string,
    createdBy: string,
    articleId: ObjectId,
    customEmailArray: { to: { email: any }[] }[]
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        personalizations: customEmailArray,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 15px 0;font-family:Arial,sans-serif;">New Blog Post</h1>

                  <p style="margin:0 0 0px 0;font-size:18px;font-weight:bold;line-height:24px;font-family:Arial,sans-serif;">
                    ${title}
                  </p>

                  <p style="margin:0 0 0px 0;font-size:15px;line-height:24px;font-family:Arial,sans-serif;">
                    By ${createdBy}
                  </p>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}/article-details/${articleId}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">View Article</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendMissedMessageEmail(
    subject: string,
    email: string,
    firstname: string,
    messageCount: number
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `
        <tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 15px 0;font-family:Arial,sans-serif;">New Blog Post</h1>

                  <p style="margin:0 0 0px 0;font-size:18px;font-weight:bold;line-height:24px;font-family:Arial,sans-serif;">
                   Hi ${firstname}!
                  </p>

                  <p style="margin:0 0 0px 0;font-size:15px;line-height:24px;font-family:Arial,sans-serif;">
                    You have received ${messageCount} messages while you were away.
                  </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        ` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendTestEmail(email: string, subject: string) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Welcome To Lavni</h1>
                  <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    Test

                    <br/><br/>

                    Test 2
                  </p>
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    <a href="https://google.com" style="color:#FF8000;text-decoration:underline; font-weight: bold;">Testni</a></p>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendVeryfiyLinkToClient(
    clientEmail: string,
    firstname: string,
    subject: string,
    userId: ObjectId
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${clientEmail}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p>This is lavni's automated assistant.</p>
                  <p>I saw you signed up but have not verified your email.</p>
                  <p>Please click the link below to verify your account</p>
                </td>
              </tr>
              <tr>
              <td style="padding:0;">
                <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                  <a href="${process.env.APP_URL
          }/client-verify/${userId}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                    <span style="color: #FF8000; font-size:20px;">Verification Link</span>
                  </a>
                </div>
              </td>
            </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendInsuranceAndOutofpocket(
    clientEmail: string,
    firstname: string,
    subject: string,
    userId: ObjectId
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${clientEmail}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hi ${firstname ? firstname : "There"
          }!</h1>
                  <p>This is lavni's automated assistant.</p>
                  <p>I noticed that you matched with a therapist.</p>
                  <p>You can cover your session with options such as insurance or out of pocket.</p>
                  <p>Please sign in.</p>
                </td>
              </tr>
              <tr>
              <td style="padding:0;">
                <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                  <a href="${process.env.APP_URL
          }/signin}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                    <span style="color: #FF8000; font-size:20px;">Sign In</span>
                  </a>
                </div>
              </td>
            </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendFriendRequestFromTherapistToClient(
    clientEmail: string,
    therapistEmail: string,
    subject: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${clientEmail}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p>Dear Client,</p>
                  <p>Welcome to Lavni, where we are committed to helping you achieve your personal and emotional goals. Here is some information about how we work:</p>
                  <ul>
                    <li>Each session is <b>60 minutes</b> long, which gives you enough time to share your thoughts and feelings and get valuable feedback from your therapist.</li>
                    <li>We start with an <b>Assessment</b> session, where we establish a baseline of your current situation and set realistic and measurable goals for your therapy.</li>
                    <li>We have <b>weekly sessions</b> to help you make consistent and gradual progress towards your goals. You can schedule and reschedule your recurring sessions online at your convenience.</li>
                    <li>If you need to cancel a session, please do so at least <b>12 hours</b> before the scheduled time to avoid a <b>$35 cancellation fee</b>.</li>
                    <li>If you use your health insurance to pay for the sessions, your <b>copay</b> will be deducted from your card before each session. We accept most major insurance plans.</li>
                    <li>We ask you to make a pledge to stick with the therapy for at least 12 sessions, as this is the average time it takes to see noticeable improvement. We believe in you and your potential to overcome your challenges.</li>
                    <li>You can monitor your progress over time on your <b>dashboard</b>, where you can see your goal attainment, session feedback, and mood ratings.</li>
                    <li>You can also choose to use a <b>customizable avatar</b> during the sessions, which you can enable or disable as you wish. This feature is designed to make you feel more comfortable and relaxed while talking to your therapist.</li>
                  </ul>
                  <p>We hope this information helps you understand what we do and how we can help you. If you have any questions or concerns, please feel free to contact us. We look forward to working with you and supporting you on your journey.</p>
                  <p>Sincerely,</p>
                  <p>Lavni</p>
                </td>
              </tr>
              <tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendEmailToSelectedUsers(
    userEmail: string,
    body: string,
    subject: string
  ) {
    try {
      const result = await transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${userEmail}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p>${body}</p>
                </td>
              </tr>
              <tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      console.log('Email sent result:', result);
      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  export async function sendTherapistReferralLinkEmail(
    userEmail: string,
    body: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${userEmail}`,
        subject: "Therapist Referral Link",
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                <div style="text-align: center;">
                    <a href="${body}" style="display: inline-block; padding: 10px; background-color: #ff8000; color: #fff; text-decoration: none; border-radius: 4px;">View Therapist Profile</a>
                </div>
                </td>
              </tr>
              <tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendTherapistReferralLinkEmailByTherapist(
    userEmail: string,
    messageContent: string
    // subject: "Therapist Referal Link",
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${userEmail}`,
        subject: "Therapist Referral Link",
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="color: #000000;">${messageContent}</p>
                </td>
              </tr>
              <tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendClientReferralLinkEmailByClient(
    userEmail: string,
    messageContent: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${userEmail}`,
        subject: "Client Referral Link",
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="color: #000000;">${messageContent}</p>
                </td>
              </tr>
              <tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAdminEmailWhenCallingExeptionEmail(
    subject: string,
    bodyText1?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: process.env.DEV_EMAILS,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">${bodyText1}</h1>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">Visit Lavni</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendPercentageTransactionDevPurpose(
    subject: string,
    therapistId: string,
    clientId: string,
    copaymentVal: any,
    cptAmount: any,
    selectedPaymentAmount: string,
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: process.env.DEV_EMAILS,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">TherapistID : ${therapistId}</p>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">ClientID : ${clientId}</p>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">copayment Value : ${copaymentVal}</p>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">CPT Amount : ${cptAmount}</p>

                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">transactionAmount : ${selectedPaymentAmount}</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  // export async function sendInactiveClientEmail(
  //   user: DUser,
  //   firstName: string
  // ) {
  //   try {
  //     transport.sendMail({
  //       from: process.env.SENGRID_SENDER,
  //       to: `${user.email}`,
  //       subject:
  //         "Supporting Your Continued Well-Being: Reactivation of Your Account",
  //       html:
  //         emailHeader +
  //         `<tr>
  //         <td style="padding:36px 30px 42px 30px;">
  //           <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
  //             <tr>
  //               <td style="padding:0 0 36px 0;color:#153643;">
  //                 <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Dear ${firstName ? firstName : "client"
  //         },</h1>
  //                 <br>
  //                 <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">I hope this message finds you well. I am writing to you with a sense of care and concern regarding your recent appointments.</p>
  //                 <br>
  //                 <p  style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Our system has flagged your account as inactive due to multiple no-show appointments, leading to the unfortunate cancellation of your upcoming sessions. I want you to know that our primary concern is your well-being, and we understand that unforeseen circumstances may have contributed to this situation.</P>
  //                 <br>
  //                 <p  style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">To resolve this issue and reactivate your account, I encourage you to schedule a new appointment at your earliest convenience. Your mental health journey is important to us, and we want to ensure you have the support you need.</P>
  //                 <br>
  //                 <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">If there are any challenges or specific reasons for the missed appointments, please feel free to share them with me. We are here to work together to find a solution that best fits your needs.</p>
  //                 <br>
  //                 <p  style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Your well-being is our priority, and we look forward to supporting you on your therapeutic journey.</P>
  //                 <br>
  //                 <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Kind regards,</p>
  //                 <br>
  //                 <p style="margin:0 0 0px 0;font-size:16px;line-height:12px;font-family:Arial,sans-serif;">Lavni.</p>
  //               </td>
  //             </tr>
  //           </table>
  //         </td>
  //       </tr>` +
  //         emailFooter,
  //     });
  //     return true;
  //   } catch (error) {
  //     return false;
  //   }
  // }

  export async function sendAdminEmailForInsuranceEligibilitySuccess(
    subject: string,
    email: string,
    clientFirstName: string,
    insuranceName: string,
    eligibilityStatus: string,
    coPayment: number,
    therapistFirstName: string,
    appointmentStartTime: string,
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hello ${clientFirstName ? clientFirstName : "There"}!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                 I'm Lavni's automated insurance verification assistant. I've confirmed that your ${insuranceName} is currently ${eligibilityStatus},
                  and your sessions are covered with a $${coPayment} copayment.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  Your scheduled appointment with ${therapistFirstName} on  ${appointmentStartTime} has been confirmed. You'll receive a reminder 24 hours and 15 minutes before your session. To join, simply sign in and click the button on your dashboard – your therapist will be ready and waiting.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  For any questions, feel free to reach out to <NAME_EMAIL>.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  We're genuinely excited about your upcoming appointment and are committed to ensuring it's a positive experience for you.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p><br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni Insurance Verification Team</p><br><br>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAdminEmailForInsuranceEligibilitySuccessWithoutAppoinmentData(
    subject: string,
    email: string,
    clientFirstName: string,
    insuranceName: string,
    eligibilityStatus: string,
    coPayment: number,
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hello ${clientFirstName ? clientFirstName : "There"}!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                 I'm Lavni's automated insurance verification assistant. I've confirmed that your ${insuranceName} is currently ${eligibilityStatus},
                  and your sessions are covered with a $${coPayment} copayment.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  For any questions, feel free to reach out to <NAME_EMAIL>.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  We're genuinely excited about your upcoming appointment and are committed to ensuring it's a positive experience for you.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p><br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni Insurance Verification Team</p><br><br>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAdminEmailForInsuranceEligibilityError(
    subject: string,
    email: string,
    clientFirstName: string,
    insuranceName: string,
    errorMsg: string,
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: email,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hello ${clientFirstName ? clientFirstName : "There"}!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  I'm Lavni's automated insurance verification assistant. Regrettably, we encountered an issue while verifying your ${insuranceName} insurance due to  ${errorMsg}. Please contact our <NAME_EMAIL> to resolve this matter promptly.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  We're genuinely excited about your upcoming appointment and are dedicated to ensuring it's a positive experience for you.
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p><br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni Insurance Verification Team</p><br><br>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }


  export async function sendGroupChatEventEmail(
    user: DUser,
    firstName: string,
    subject: string,
    bodyText: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${user.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Dear ${firstName},</h1>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${bodyText}</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;"> Visit Lavni</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendPendingCopaymentEmail(
    user: DUser,
    subject: string,
    bodyText1?: string,
    otherUserName?: DUser
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${user.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;">Hello ${user?.firstname ? user?.firstname : "Client"}!</h1>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                       ${bodyText1}
                  </p>
                  <br>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${otherUserName?.firstname ? otherUserName?.firstname : "Therapist"}</p><br><br>
                </td>
              </tr>
                            <tr>
                <td style="padding:0;">
                  <div style="width: 100%; background: #efefef; font-family: Lucida Grande,Lucida Sans Unicode,Lucida Sans,Geneva,Verdana,sans-serif; font-weight: bold; text-align: center; padding: 50px 0px;">
                    <a href="${process.env.APP_URL}/profile/9" style="width: 50px; padding:10px; background: #fff; border-radius: 5px; box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; text-decoration: none;">
                      <span style="color: #FF8000; font-size:20px;">Pay Now</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>`+
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendWelcomeEmailClient(user: DUser, subject: string) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${user.email}`,
        subject: subject,
        html:
          emailHeaderWelcome +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
             <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
                   <tr>
                       <td style="padding:0 0 36px 0;color:#153643;">
                          <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
                                <tr>
                                    <td style="width:340px;padding:0;vertical-align:top;color:#153643;position:relative;">
                                       <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);">
                                           <h1 style="font-size:24px;margin:0 0 20px 0;font-family:Arial,sans-serif;  color: #153643">Dear ${user?.firstname ? user?.firstname : "Client"},</h1>
                                           <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif; color: #153643">Welcome to Lavni! We are delighted to have you join our community as you embark on your journey toward mental health wellness.</p>
                                           <p style="margin:0 0 12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;  color: #153643">Here at Lavni, we understand that taking the first step toward seeking support can be daunting, and we commend you for prioritizing your mental well-being. Our platform is designed with your comfort and convenience in mind, providing a safe space for you to explore and address your mental health needs.</p>
                                       </div>
                                    </td>

                                    <td style="padding:0;font-size:0;line-height:0;">&nbsp;</td>

                                    <td style="width:180px;padding:0;vertical-align:middle;text-align:center;color:#153643;">
                                       <div style="display:inline-block;">
                                           <img src="https://lavni.efito.xyz/static/assets/img/email/img1.png" alt="" width="260" style="height:auto;display:block;" />
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                     <tr>
                          <td style="padding:0 0 36px 0;color:#153643;">
                             <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Whether you're seeking guidance through challenging times, looking to enhance your well-being, or simply in need of someone to listen, our dedicated team of therapists is here to support you every step of the way.</p>
                           </td>
                     </tr>

                     <tr>
                         <td style="padding:0 0 36px 0;color:#153643;">
                            <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">As you begin your therapeutic journey with Lavni, we want to assure you that your privacy and confidentiality are of utmost importance to us. You have the option to use your insurance coverage or pay for sessions out of pocket, ensuring accessibility to quality care that fits your unique needs.</p>
                        </td>
                     </tr>

                      <tr>
                          <td style="padding:0 0 36px 0;color:#153643;">
                             <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Please don't hesitate to reach out if you have any questions or if there's anything we can assist you with. Your well-being is our top priority, and we are committed to providing you with the support and resources you need to thrive.</p>
                           </td>
                       </tr>

                       <tr>
                           <td style="padding:0 0 36px 0;color:#153643;">
                              <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you for entrusting us with your mental health journey. We look forward to accompanying you on the path to healing and growth.</p>
                           </td>
                       </tr>

                        <tr>
                            <td style="padding:0 0 36px 0;color:#153643;">
                               <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Warm regards,</p>
                               <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Marcus King</p>
                               <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">CEO, Lavni</p><br>
                             </td>
                        </tr>
             </table>
           </td>
      </tr>` +
          emailFooter,
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendClinicalAssesmentSignatureEmail(
    client: DUser,
    therapist: DUser,
    subject: string,
    formId?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${client.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Hi ${client?.firstname ? client?.firstname : "Client"}!</p>
                  </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">I hope this message finds you well. I wanted to remind you to add your signature to your assessment and treatment plan. Your signature is essential to confirm that we’re on the same page regarding your care and goals.</p>
                  </td>
              </tr>

              <tr>
                  <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">You can sign it through the client portal at your earliest convenience using the link below. If you have any questions or need assistance, please don’t hesitate to reach out.</p>
                  </td>
              </tr>

              <tr>
                  <td style="padding:0 0 36px 0;color:#153643;">
                    <a href="${process.env.APP_URL}/assesment-client-signature/${formId}" style="display: inline-block; padding: 10px; background-color: #ff8000; color: #fff; text-decoration: none; border-radius: 4px;">Click here to sign</a>
                  </td>
              </tr>

              <tr>
                  <td style="padding:0 0 36px 0;color:#153643;">
                      <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p>
                      <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${therapist?.firstname ? therapist?.firstname : "Your Therapist"}</p>
                    </td>
              </tr>

            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendTherapyPlanSignatureEmail(
    client: DUser,
    therapist: DUser,
    subject: string,
    formId?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${client.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Hi ${client?.firstname ? client?.firstname : "Client"}!</p>
                  </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">I hope this message finds you well. I wanted to remind you to add your signature to your therapy plan. Your signature is essential to confirm that we’re on the same page regarding your care and goals.</p>
                  </td>
              </tr>

              <tr>
                  <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">You can sign it through the client portal at your earliest convenience using the link below. If you have any questions or need assistance, please don’t hesitate to reach out.</p>
                  </td>
              </tr>

              <tr>
                  <td style="padding:0 0 36px 0;color:#153643;">
                    <a href="${process.env.APP_URL}/therapy-plan-client-signature/${formId}" style="display: inline-block; padding: 10px; background-color: #ff8000; color: #fff; text-decoration: none; border-radius: 4px;">Click here to sign</a>
                  </td>
              </tr>

              <tr>
                  <td style="padding:0 0 36px 0;color:#153643;">
                      <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p>
                      <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${therapist?.firstname ? therapist?.firstname : "Your Therapist"}</p>
                    </td>
              </tr>

            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendStripeBlanceIncreeceDevPurpose(
    subject: string,
    bodyText1?: string,
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: process.env.DEV_EMAILS,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${bodyText1}</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendRegistrationApprovelEmailToTherapist(
    therapist: ITherapist,
    subject: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${therapist.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Dear ${therapist?.firstname ? therapist?.firstname : "Therapist"}!</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Congratulations on passing the initial review process! We are thrilled to welcome you to Lavni, where we are dedicated to transforming mental health care through innovation and compassion.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">To complete your onboarding and begin helping clients on our platform, please follow the link below to sign in to your Lavni account and complete the necessary steps:</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <a href="${process.env.APP_URL}/signin" style="display: inline-block; padding: 10px; background-color: #ff8000; color: #fff; text-decoration: none; border-radius: 4px;">Sign In to Lavni</a>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Next Steps:</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <div style="padding-left:20px;">
                    <p style="margin:0 0 8px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">1. Sign In: Use the link above to access your Lavni account.</p>
                    <p style="margin:0 0 8px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">2. Complete Your Profile: Update your personal and professional information, including your credentials and availability.</p>
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">3. Review Policies: Familiarize yourself with Lavni’s policies and procedures to ensure compliance.</p>
                  </div>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">If you have any questions or need assistance during the onboarding process, please don’t hesitate to reach out to our support <NAME_EMAIL>. We’re here to help you every step of the way.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">We look forward to working together to make a positive impact in the mental health community.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Warm regards,</p>
                    <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Laura Valentine</p>
                    <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">CMO</p>
                    <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni, Inc.</p>
                </td>
              </tr>

            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAutomatedMultipleNoShowEmailForClients(
    user: DUser,
    subject: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${user.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Dear ${user?.firstname ?? "Client" } ${user?.lastname ?? ""},</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">I hope this message finds you well. I am writing to you with a sense of care and concern regarding your recent appointments.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Our system has flagged your account as inactive due to multiple no-show appointments, leading to the unfortunate cancellation of your upcoming sessions. I want you to know that our primary concern is your well-being, and we understand that unforeseen circumstances may have contributed to this situation.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">To resolve this issue and reactivate your account, please schedule a new appointment by logging into your account at <a href="https://mylavni.com/">mylavni.com</a>. If you encounter any issues or have questions, you can also reach us via email at <a href="mailto:<EMAIL>"><EMAIL></a> or by calling us at <a href="tel:+***********">************</a>. Your mental health journey is important to us, and we want to ensure you have the support you need.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">If there are any challenges or specific reasons for the missed appointments, please feel free to share them with us. We are here to work together to find a solution that best fits your needs.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Your well-being is our priority, and we look forward to supporting you on your therapeutic journey.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Kind regards,</p>
                    <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni, Inc.</p>
                </td>
              </tr>

            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendAccountInactivationByTherapistEmailForClients(
    user: DUser,
    subject: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${user.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Dear ${user?.firstname ?? "Client" } ${user?.lastname ?? ""},</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">I hope this message finds you well. I am writing to inform you about a change in the status of your account with Lavni.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Your therapist has manually set your account to inactive status, which has led to the cancellation of your upcoming sessions. This decision may have been made based on specific therapeutic considerations, no-shows, or other factors discussed during your sessions.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">If you wish to discuss this change or if you would like to reactivate your account and continue your therapeutic journey, please log into your account at <a href="https://mylavni.com/">mylavni.com</a>. For any questions or assistance, you can also reach us via email at <a href="mailto:<EMAIL>"><EMAIL></a> or by calling us at <a href="tel:+***********">************</a>.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Your mental health and well-being are very important to us. We are here to support you and address any concerns you may have. Please feel free to share any specific reasons or circumstances that may have contributed to this status change, so we can work together to find the best path forward.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">We look forward to the opportunity to continue supporting you on your therapeutic journey.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Kind regards,</p>
                    <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni, Inc.</p>
                </td>
              </tr>

            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendIncompleteFormSubmissionReminder(
    therapist: ITherapist,
    client: IClient,
    formName : string,
    subject: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${therapist.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Dear ${therapist?.firstname || 'Therapist'} ${therapist?.lastname || ''},</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">This is a reminder that <b>${formName}</b> is still incomplete for your client. Client name is <b>${client?.firstname || '-'} ${client?.lastname || ''}</b>. To ensure timely processing, please complete and submit the necessary documents as soon as possible.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">If you require assistance or have any questions regarding the form, feel free to contact us.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you for your prompt attention to this matter.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Crystal | Bush</p>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Clinical Director</p>
                </td>
              </tr>

            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendEmailToAdminWhenTherapistApprovedInsuranceDocuments(
    therapist: ITherapist,
    subject: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: process.env.DOCUMENTS_APPROVE_ADMIN_EMAIL,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Dear Admin</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;"><b>${therapist?.firstname || 'Therapist'}</b> has submitted the all documents for review. Please review the submission at your earliest convenience to ensure it is processed in a timely manner.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">If you need any additional information or have questions, please feel free to reach out.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you for your attention.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni, Inc.</p>
                </td>
              </tr>

            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendEmailToAdminForPendingApprovalReach24Hours(
    therapistFirstname: string,
    therapistLastname: string,
    clientFirstname: string,
    clientLastname: string,
    insuranceCompany: string,
    subject: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: process.env.DOCUMENTS_APPROVE_ADMIN_EMAIL,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Dear Admin</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">This is a reminder that <b>${therapistFirstname || 'Therapist'} ${therapistLastname || ''}</b> has submitted the all documents for his/him client <b>${clientFirstname} ${clientLastname || ''}</b>, insurance company <b>${insuranceCompany || '-'}</b>, and it is pending your approval. Please note that the 24-hour deadline for this approval is approaching.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Kindly review the document before the deadline to ensure timely processing.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you for your attention to this.</p>
                </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Lavni, Inc.</p>
                </td>
              </tr>

            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  //digital assessment client signature

  export async function sendDigitalAssesmentSignatureEmail(
    client: DUser,
    therapist: DUser,
    subject: string,
    formId?: string
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: `${client.email}`,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Hi ${
                      client?.firstname ? client?.firstname : "Client"
                    }!</p>
                  </td>
              </tr>

              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">I hope this message finds you well. I wanted to remind you to add your signature to your assessment and treatment plan. Your signature is essential to confirm that we’re on the same page regarding your care and goals.</p>
                  </td>
              </tr>

              <tr>
                  <td style="padding:0 0 36px 0;color:#153643;">
                    <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">You can sign it through the client portal at your earliest convenience using the link below. If you have any questions or need assistance, please don’t hesitate to reach out.</p>
                  </td>
              </tr>

              <tr>
                  <td style="padding:0 0 36px 0;color:#153643;">
                    <a href="${
                      process.env.APP_URL
                    }/digital-assesment-client-signature/${formId}" style="display: inline-block; padding: 10px; background-color: #ff8000; color: #fff; text-decoration: none; border-radius: 4px;">Click here to sign</a>
                  </td>
              </tr>

              <tr>
                  <td style="padding:0 0 36px 0;color:#153643;">
                      <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Best regards,</p>
                      <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${
                        therapist?.firstname
                          ? therapist?.firstname
                          : "Your Therapist"
                      }</p>
                    </td>
              </tr>

            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  export async function sendStripeBlanceIncreaseErrorsDevPurpose(
    subject: string,
    bodyText1?: string,
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: process.env.DEV_EMAILS,
        subject: subject,
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">${bodyText1}</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Send notification to therapist when a client's insurance becomes inactive
   * @param therapistEmail Therapist's email address
   * @param data Object containing therapistName, clientName, and errorMessage
   * @returns Boolean indicating success or failure
   */
  export async function sendInsuranceInactiveNotificationToTherapist(
    therapistEmail: string,
    data: { therapistName: string; clientName: string; errorMessage: string }
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: therapistEmail,
        subject: "Client Insurance Status Alert: Coverage Issue Detected",
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Hello ${data.therapistName},</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">This is an important notice regarding your client, ${data.clientName}. Our system has detected that their insurance coverage is currently inactive.</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Error details: ${data.errorMessage}</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">The client has been notified and asked to update their insurance information. Until this is resolved, the client's account has been downgraded from premium status, which may affect their access to certain features.</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Please consider reaching out to your client directly to discuss alternative payment options if needed for upcoming sessions.</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you,</p>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">The Lavni Team</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      AppLogger.error("Error sending insurance inactive notification to therapist:", error);
      return false;
    }
  }

  /**
   * Send notification to client when their insurance becomes inactive
   * @param clientEmail Client's email address
   * @param data Object containing clientName, errorMessage, and loginUrl
   * @returns Boolean indicating success or failure
   */
  export async function sendInsuranceInactiveNotificationToClient(
    clientEmail: string,
    data: { clientName: string; errorMessage: string; loginUrl: string }
  ) {
    try {
      transport.sendMail({
        from: process.env.SENGRID_SENDER,
        to: clientEmail,
        subject: "Action Required: Your Insurance Coverage Status",
        html:
          emailHeader +
          `<tr>
          <td style="padding:36px 30px 42px 30px;">
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Hello ${data.clientName},</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">We're reaching out because our system has detected an issue with your insurance coverage status. Your insurance has been identified as inactive for the following reason:</p>
                  <p style="margin:12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;font-style:italic;">${data.errorMessage}</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">As a result, your account has been temporarily downgraded from premium status. To restore full access to all premium features, please take one of the following actions as soon as possible:</p>
                  <ul style="margin:12px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">
                    <li>Log in to your account and update your insurance information</li>
                    <li>Reply to this email with your updated insurance details</li>
                    <li>Contact your insurance provider to resolve any issues with your coverage</li>
                  </ul>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;text-align:center;">
                  <a href="${data.loginUrl}" style="background:#FF8000;color:#ffffff;display:inline-block;font-family:Arial,sans-serif;font-size:16px;line-height:44px;text-align:center;text-decoration:none;width:200px;-webkit-text-size-adjust:none;border-radius:5px;">Log in to update</a>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                </td>
              </tr>
              <tr>
                <td style="padding:0 0 36px 0;color:#153643;">
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">Thank you,</p>
                  <p style="margin:0 0 0px 0;font-size:16px;line-height:24px;font-family:Arial,sans-serif;">The Lavni Team</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>` +
          emailFooter,
      });
      return true;
    } catch (error) {
      AppLogger.error("Error sending insurance inactive notification to client:", error);
      return false;
    }
  }

}
