from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
import asyncio
import sys

meetingId = sys.argv[2]
mongoConnectString = sys.argv[3]
dbName = sys.argv[4]

client = AsyncIOMotorClient(mongoConnectString)
db = client[dbName]

# Create indexes on the collections (run this once)
async def create_indexes():
    await db['meetings'].create_index([('meetingId', 1)])
    await db['transcribes'].create_index([('meetingId', 1)])

async def get_conversation_from_transcript(meetingId):
    try:
        # Query the `meetings` table using _id
        meeting_record = await db['meetings'].find_one({'_id': ObjectId(meetingId)})

        if not meeting_record:
            return {'error': 'No meeting found with this _id'}

        # Extract the meetingId from the record, call it transcriptId
        transcript_id = meeting_record.get('meetingId')
        if not transcript_id:
            return {'error': 'No transcriptId (meetingId) found in the meeting record'}

        # Query the `transcribes` table using transcriptId (which is meetingId)
        transcribe_record = await db['transcribes'].find_one({'meetingId': transcript_id})

        if not transcribe_record:
            return {'error': 'No transcribe found for this transcriptId'}

        # Extract transcriptText from the transcribe record, call it convo
        convo = transcribe_record.get('transcriptText')

        if not convo:
            return {'error': 'No transcriptText found in the transcribe record'}

        return {'convo': convo}
    except Exception as e:
        print(f"Error retrieving transcript: {e}")
        return {'error': str(e)}

# Example of running the async function
async def main():
    await create_indexes()  # Create indexes if not already created
    result = await get_conversation_from_transcript(meetingId)
    print(result)

# Run the main function
if __name__ == "__main__":
    asyncio.run(main())
