from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
import asyncio
import sys

meetingId = sys.argv[2]
mongoConnectString = sys.argv[3]
dbName = sys.argv[4]

client = AsyncIOMotorClient(mongoConnectString)
db = client[dbName]

# Create indexes on the collections (run this once)
async def create_indexes():
    await db['diagnosisnotes'].create_index([('meetingId', 1)])
    await db['diagnosisnotes'].create_index([('clientId', 1)])

async def get_diagnosis_codes(meetingId):
    try:
        # Find the document with the given meetingId
        meeting = await db['diagnosisnotes'].find_one({'meetingId': ObjectId(meetingId)})

        if not meeting:
            return {'error': 'No meeting found with this meetingId'}

        # Extract the clientId from the found meeting
        client_id = meeting.get('clientId')

        # Query for all documents with the same clientId
        client_documents = await db['diagnosisnotes'].find({'clientId': client_id}).sort('encounter_date', 1).to_list(length=None)

        primary_code = None
        secondary_code = None

        for doc in client_documents:
            # Check if primary diagnosis code exists
            if not primary_code and 'diagnosisICDcodes' in doc and doc['diagnosisICDcodes']:
                primary_code = doc['diagnosisICDcodes']

            # Check if secondary diagnosis code exists
            if not secondary_code and 'secondaryDiagnosisICDcodes' in doc and doc['secondaryDiagnosisICDcodes']:
                secondary_code = doc['secondaryDiagnosisICDcodes']

            # Stop once we have both primary and secondary codes
            if primary_code and secondary_code:
                break

        return {
            'primaryDiagnosisICDcodes': primary_code,
            'secondaryDiagnosisICDcodes': secondary_code
        }
    except Exception as e:
        print(f"Error retrieving diagnosis codes: {e}")
        return {'error': str(e)}

# Example of running the async function
async def main():
    await create_indexes()  # Create indexes if not already created
    result = await get_diagnosis_codes(meetingId)
    print(result)

# Run the main function
if __name__ == "__main__":
    asyncio.run(main())
