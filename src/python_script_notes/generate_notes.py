import openai
import sys

openAIAPIKey = sys.argv[1]
openai.api_key = openAIAPIKey

def generate_notes(conversation_text, note_type, diagnosis=None, assessment=None, previous_notes=None):
    """
    Generates clinical notes based on the provided conversation text and other details.

    Parameters:
    - conversation_text (str): The text of the conversation.
    - note_type (str): The type of note to generate (e.g., 'pie', 'soap').
    - diagnosis (str): The diagnosis information.
    - assessment (str): The assessment information.
    - previous_notes (str): Previous notes from earlier sessions.

    Returns:
    - str: The generated clinical notes.
    """
    # Prepare context details based on the available information
    context = "This is a follow-up session with the client."

    if diagnosis:
        context += f" The client has been diagnosed with {diagnosis}."

    if assessment:
        context += f" The client's assessment indicated {assessment}."

    if previous_notes:
        context += f" In the previous session, the following was discussed: {previous_notes}."

    # Define the prompt structure based on note_type
    if note_type.lower() == 'pie':
        messages = [
            {"role": "system", "content": "You are a skilled therapist who uses DSM-5 criteria."},
            {"role": "user", "content": f"""
            {context}

            Please generate a PIE note for the following therapist-client session. Follow the PIE format:

            P (Problem): Describe the client's ongoing issues or any new developments in their symptoms or situation. Mention whether these issues align with the initial diagnosis or suggest a need for reassessment.

            I (Intervention): Summarize the therapist's intervention and suggestions during this session. Note how the interventions relate to the client's diagnosis, assessment, and therapy plan (if applicable).

            E (Evaluation): Evaluate the effectiveness of the interventions in this session. Provide recommendations for the next steps and note if further reassessment or adjustments to the therapy plan are necessary.

            Conversation: {conversation_text}
            """}
        ]
    
    elif note_type.lower() == 'soap':
        messages = [
            {"role": "system", "content": "You are a skilled therapist who uses DSM-5 criteria and follows SOAP note format."},
            {"role": "user", "content": f"""
            {context}

            Please generate a SOAP note for the following therapist-client session. Follow the SOAP format:

            S (Subjective): Summarize the client's subjective experience during the session, including any symptoms, feelings, or concerns they express.

            O (Objective): Record observable facts during the session. This could include the therapist's observations of the client's behavior, mood, and any relevant test results or measurements.

            A (Assessment): Provide an interpretation of the subjective and objective information. Relate it to the client's diagnosis (if applicable) and assess the client's progress. Mention if there is a need for further assessment or modification of the diagnosis.

            P (Plan): Outline the next steps for therapy. This includes future goals, planned interventions, and any adjustments to the current therapy plan.

            Conversation: {conversation_text}
            """}
        ]

    else:
        raise ValueError("Invalid note type. Please use 'pie' or 'soap'.")

    # Call OpenAI API to generate the note
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4o-mini",  # or "gpt-3.5-turbo"
            messages=messages,
            max_tokens=500,  # Adjust as needed
            temperature=0.5
        )
        note = response['choices'][0]['message']['content']
        return note
    except Exception as e:
        print(f"Error generating notes: {e}")
        return "Note generation failed."

