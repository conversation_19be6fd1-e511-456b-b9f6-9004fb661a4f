import asyncio
from Get_transcript import get_conversation_from_transcript  # Adjust the import based on your actual function
from get_diagnosis import get_diagnosis_codes
from sentize_anonymise import get_sanitized_conversation  # Assuming this is the function for sanitization
from summary import summarize_text_dynamic
from generate_notes import generate_notes
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
import asyncio
import sys

meetingId = sys.argv[2]
mongoConnectString = sys.argv[3]
dbName = sys.argv[4]
notes_type = sys.argv[5] 

client = AsyncIOMotorClient(mongoConnectString)
db = client[dbName]

async def create_indexes():
    await db['diagnosisnotes'].create_index([('meetingId', 1)])
    await db['diagnosisnotes'].create_index([('clientId', 1)])
async def process_notes(meeting_id):
    # Step 1: Get the sanitized conversation
    sanitized_convo = await get_conversation_from_transcript(meeting_id)
    conversation_text = sanitized_convo.get('convo', '')

    # Step 2: Retrieve diagnosis codes
    diagnosis = await get_diagnosis_codes(meeting_id)
    diagnosis_codes = ', '.join(f"{key}: {value}" for key, value in diagnosis.items())

    # Step 3: Sanitize the conversation

    sanitized_text = get_sanitized_conversation(conversation_text)

    # Step 4: Summarize the sanitized conversation
    summarized_convo = summarize_text_dynamic(sanitized_text)

    # Step 5: Generate clinical notes
    # notes_type = 'pie'  # or 'soap', based on your requirement
    session_notes = generate_notes(
        summarized_convo,
        note_type=notes_type,
        diagnosis=diagnosis_codes
    )

    # Step 6: Print the generated notes
    print("Clinical Note:\n", session_notes)


if __name__ == "__main__":
    meeting_id = meetingId  # Replace with the actual meeting ID
    asyncio.run(process_notes(meeting_id))