import re
import gensim
from gensim.summarization import summarize  # Adjusted import statement
import time  # Import time module for measuring execution time

def simple_tokenize(text):
    """Tokenizes text into words using simple string methods."""
    return re.findall(r'\b\w+\b', text)

def simple_sent_tokenize(text):
    """Tokenizes text into sentences using simple string methods."""
    return re.split(r'(?<=[.!?]) +', text)

def summarize_text_dynamic(text, max_summary_tokens=5734):
    """
    Summarizes the input text dynamically based on the maximum summary token limit.

    Parameters:
    - text (str): The text to summarize.
    - max_summary_tokens (int): The maximum number of tokens for the summary.

    Returns:
    - str: The summarized text.
    """
    # Preprocess the text
    text = re.sub(r'\s+', ' ', text)  # Replace multiple whitespaces with a single space

    # Tokenize the text to count the number of words (tokens)
    original_tokens = simple_tokenize(text)
    num_original_tokens = len(original_tokens)

    # If the text is shorter than the maximum allowed summary length, return the original text
    if num_original_tokens <= max_summary_tokens:
        return text

    # Start timing the summarization process
    start_time = time.time()

    # Split the text into manageable chunks if it's too long
    max_chunk_size = 10000  # Adjust based on Gensim's limitations (approximate max words Gensim can handle)
    sentences = simple_sent_tokenize(text)
    chunks = []
    current_chunk = ''
    current_length = 0

    for sentence in sentences:
        sentence_length = len(simple_tokenize(sentence))
        if current_length + sentence_length <= max_chunk_size:
            current_chunk += ' ' + sentence
            current_length += sentence_length
        else:
            chunks.append(current_chunk.strip())
            current_chunk = sentence
            current_length = sentence_length

    if current_chunk:
        chunks.append(current_chunk.strip())

    # Calculate the proportion of tokens in each chunk relative to the total number of tokens
    chunk_token_counts = [len(simple_tokenize(chunk)) for chunk in chunks]
    total_chunk_tokens = sum(chunk_token_counts)

    # Summarize each chunk
    summaries = []
    for chunk, chunk_tokens in zip(chunks, chunk_token_counts):
        # Calculate the chunk's proportion of the total tokens
        chunk_proportion = chunk_tokens / total_chunk_tokens
        # Allocate summary length to this chunk
        chunk_summary_tokens = int(chunk_proportion * max_summary_tokens)
        # Ensure a minimum summary length for each chunk
        if chunk_summary_tokens < 50:
            chunk_summary_tokens = 50
        # Summarize the chunk using Gensim
        try:
            summary = summarize(chunk, word_count=chunk_summary_tokens)
            summaries.append(summary)
        except ValueError:
            # If summarization fails (e.g., text too short), include the original chunk
            summaries.append(chunk)

    # Combine the summaries of all chunks
    combined_summary = ' '.join(summaries)

    # Verify if the combined summary exceeds the maximum summary tokens
    final_tokens = simple_tokenize(combined_summary)
    final_token_count = len(final_tokens)

    if final_token_count > max_summary_tokens:
        # If it exceeds, adjust by summarizing the combined summary
        try:
            combined_summary = summarize(combined_summary, word_count=max_summary_tokens)
        except ValueError:
            # If summarization fails, truncate the summary
            combined_summary = ' '.join(final_tokens[:max_summary_tokens])

    # End timing the summarization process
    end_time = time.time()
    execution_time = end_time - start_time

    # Print original and final token counts and execution time
    # print(f"Original token count: {num_original_tokens}")
    # print(f"Final token count after summarization: {final_token_count}")
    # print(f"Execution time: {execution_time:.2f} seconds")

    return combined_summary