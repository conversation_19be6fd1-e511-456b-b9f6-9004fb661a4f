const aes256 = require("aes256");

const key = "obvwoqcbv21801f19d0zibcoavwpnq";

export const DoEncrypt = (text: string) => {
  const encrypted = aes256.encrypt(key, text);
  return encrypted;
};

export const DoDecrypt = (cipher: string) => {
  try {
    if (cipher && cipher?.length > 0) {
      const decrypted = aes256.decrypt(key, cipher);
      const first3Words = decrypted.split(' ').slice(0, 3).join(' ');
      return first3Words;
    }

    return cipher;
  } catch (error) {
    console.log(error);

    return "";
  }
};
