/**
 * Utility file for state name and abbreviation mappings
 */

/**
 * Mapping from state names to abbreviations
 */
export const STATE_NAME_TO_ABBREVIATION: Record<string, string> = {
  "Alabama": "AL",
  "Alaska": "AK",
  "Arizona": "AZ",
  "Arkansas": "AR",
  "California": "CA",
  "Colorado": "CO",
  "Connecticut": "CT",
  "Delaware": "DE",
  "Florida": "FL",
  "Georgia": "GA",
  "Hawaii": "HI",
  "Idaho": "ID",
  "Illinois": "IL",
  "Indiana": "IN",
  "Iowa": "IA",
  "Kansas": "KS",
  "Kentucky": "KY",
  "Louisiana": "LA",
  "Maine": "ME",
  "Maryland": "MD",
  "Massachusetts": "MA",
  "Michigan": "MI",
  "Minnesota": "MN",
  "Mississippi": "MS",
  "Missouri": "MO",
  "Montana": "MT",
  "Nebraska": "NE",
  "Nevada": "NV",
  "New Hampshire": "NH",
  "New Jersey": "NJ",
  "New Mexico": "NM",
  "New York": "NY",
  "North Carolina": "NC",
  "North Dakota": "ND",
  "Ohio": "OH",
  "Oklahoma": "OK",
  "Oregon": "OR",
  "Pennsylvania": "PA",
  "Rhode Island": "RI",
  "South Carolina": "SC",
  "South Dakota": "SD",
  "Tennessee": "TN",
  "Texas": "TX",
  "Utah": "UT",
  "Vermont": "VT",
  "Virginia": "VA",
  "Washington": "WA",
  "West Virginia": "WV",
  "Wisconsin": "WI",
  "Wyoming": "WY"
};

/**
 * Reverse mapping from state abbreviations to full state names
 */
export const STATE_ABBREVIATION_TO_NAME: Record<string, string> = Object.entries(STATE_NAME_TO_ABBREVIATION).reduce(
  (acc, [name, abbr]) => {
    acc[abbr] = name;
    return acc;
  },
  {} as Record<string, string>
);

/**
 * Gets the full state name from a state abbreviation
 * @param stateAbbreviation The two-letter state abbreviation
 * @returns The full state name or the original input if not found
 */
export function getStateNameFromAbbreviation(stateAbbreviation: string): string {
  if (!stateAbbreviation) return '';
  
  // Convert to uppercase to ensure matching
  const upperAbbr = stateAbbreviation.toUpperCase();
  return STATE_ABBREVIATION_TO_NAME[upperAbbr] || stateAbbreviation;
}

/**
 * Gets the state abbreviation from a full state name
 * @param stateName The full state name
 * @returns The two-letter state abbreviation or the original input if not found
 */
export function getStateAbbreviationFromName(stateName: string): string {
  if (!stateName) return '';
  
  return STATE_NAME_TO_ABBREVIATION[stateName] || stateName;
}
