const moment = require('moment-timezone');
// Tải dữ liệu timezone đúng cách
moment.tz.setDefault('Asia/Ho_Chi_Minh');

const winston = require('winston');
const SESSION = Symbol.for('session');
// <PERSON><PERSON><PERSON> bảo SERVICE_NAME luôn là 'ANDIE' nếu không có trong biến môi trường
const SERVICE_NAME = 'ANDIE';

// Lưu trữ userId trong một biến thông thường thay vì Symbol
let currentUserId = '-';

// TODO use a package like express-http-context to get session info.
const sessionData = winston.format(function (info: any) {
    info[SESSION] = {user_id: currentUserId, progress_id: 0};
    return info;
});

const appendTimestamp = winston.format((info: any, opts: any) => {
    // Sử dụng múi giờ Việt Nam
    info.timestamp = moment().format('YYYY-MM-DDTHH:mm:ssZ');
    return info;
});

const standardFormat = winston.format.printf((info: any) => {
    return `${info.timestamp} ${info.level.toUpperCase()} ${SERVICE_NAME} ${info.message}`;
});

const requestLoggerTransporter: any[] = [
    new winston.transports.Console({
        level: 'info',
    })];
requestLoggerTransporter.push(
    new winston.transports.File({
        level: 'info',
        filename: 'log/request.log',
    }));
export const RequestLogger = winston.createLogger({
    format: winston.format.combine(
        appendTimestamp(),
        sessionData(),
        standardFormat
    ),
    label: 'request',
    transports: requestLoggerTransporter
});

const appLoggerTransporter: any[] = [
    new winston.transports.Console({
        level: 'info',
        label: 'app'
    })];
appLoggerTransporter.push(
    new winston.transports.File({
        filename: 'log/app.log',
        level: 'info',
        label: 'app'
    }));
export const AppLogger = winston.createLogger({
    format: winston.format.combine(
        appendTimestamp(),
        sessionData(),
        standardFormat
    ),
    transports: appLoggerTransporter
});

const errorLoggerTransporter: any[] = [
    new winston.transports.Console({
        level: 'error',
        label: 'error'
    })];
errorLoggerTransporter.push(
    new winston.transports.File({
        filename: 'log/error.log',
        level: 'error',
        label: 'error'
    }));
export const ErrorLogger = winston.createLogger({
    format: winston.format.combine(
        appendTimestamp(),
        sessionData(),
        standardFormat
    ),
    transports: errorLoggerTransporter
});

const uaLoggerTransporter: any[] = [
    new winston.transports.Console({
        level: 'info',
        label: 'ua'
    })];
uaLoggerTransporter.push(
    new winston.transports.File({
        filename: 'log/ua.log',
        level: 'info',
        label: 'ua'
    })
);
export const UaLogger = winston.createLogger({
    format: winston.format.combine(
        appendTimestamp(),
        sessionData(),
        standardFormat
    ),
    transports: uaLoggerTransporter
});

export const setUserId = (userId: string | number) => {
    // Cập nhật biến currentUserId thay vì thêm vào Symbol
    currentUserId = userId?.toString() || '-';
    
    // Cập nhật format cho tất cả các logger
    [AppLogger, ErrorLogger, RequestLogger, UaLogger].forEach(logger => {
        logger.format = winston.format.combine(
            appendTimestamp(),
            sessionData(),
            standardFormat
        );
    });
};
