import { Request, Response, NextFunction } from 'express';
import { setUserId, RequestLogger } from '../logging';

/**
 * Middleware để log request và kèm thông tin user vào log
 */
export const loggerMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Lấy userId từ user đã xác thực (nếu có)
  const userId = req.user?._id || req.user?.id || '-';
  
  // Set userId để sử dụng trong tất cả các log tiếp theo
  setUserId(userId);
  
  // Log request
  RequestLogger.info(`${req.method} ${req.originalUrl}`);
  
  // Hack nhỏ để log status code sau khi request hoàn thành
  const originalEnd = res.end;
  res.end = function(...args: any[]) {
    // Log khi response gửi trả về
    RequestLogger.info(`${req.method} ${req.originalUrl} - Status: ${res.statusCode}`);
    
    // G<PERSON><PERSON> hàm end gốc
    return originalEnd.apply(res, args);
  };
  
  next();
}; 