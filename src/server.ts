/// <reference path="global.d.ts" />
require("dotenv").config();

import * as express from "express";
import * as morgan from "morgan";
import * as routes from "./routes";
import { RequestLoggerHandler } from "./middleware/request-logger";
import { handleError } from "./middleware/error-handler";
import { Authentication } from "./middleware/authentication";
import { AppLogger } from "./common/logging";
import databaseSetup from "./startup/database";
import passportStartup from "./startup/passport";
import * as cors from "cors";
import * as http from "http";
import { ResponseHandler } from "./middleware/response-handler";
import { Server } from "socket.io";
import { DefaultEventsMap } from "socket.io/dist/typed-events";
import * as cron from "node-cron";
import { TransactionsEp } from "./end-point/transactions-ep";
import { AppointmentEp } from "./end-point/appointment-ep";
import { ArticleEp } from "./end-point/article-ep";
import { ZoomVideoCallEP } from "./end-point/zoom-video-call-ep";
import { ChatEp } from "./end-point/chat-ep";
import { AdminEp } from "./end-point/admin-ep";
import { VonageCallEp } from "./end-point/vonage-call-ep";
import { InstagramFeedEp } from "./end-point/instagram-feed-ep";
import { TherapistEp } from "./end-point/therapist-ep";
import AdminStatistics from "./schemas/admin-statistics";
import { AdminStatisticsEp } from "./end-point/admin-statistics-ep";
import { ReferralEarningEp } from "./end-point/referral-earning-ep";
import { ClientRewardEp } from "./end-point/client-reward-ep";
import { InsuranceEp } from "./end-point/insurance-ep";
import { everyTwoMinCronJob } from "./cron-jobs/every-two-minutes"
import { processJotformSubmissions } from "./cron-jobs/jotform-submissions";
import { detectDuplicateTreatmentHistories } from "./cron-jobs/duplicate-treatment-detector";
import { trackTwilioMessageStatus } from "./cron-jobs/twilio-message-status-tracker";
import { EligibilityEp } from "./end-point/eligibility-ep";

const PORT: any = process.env.PORT || 4000;

// Schedule the job for the 26 day
// cron.schedule('0 0 26 * *', () => {
//   AdminEp.checkStripeBlanceAnd1thPayments();
// });


// cron.schedule('45 11 13 3 *', () => {
//   console.log('Executing task on March 13 at 11:45 AM UTC');
//   AdminEp.checkStripeBlanceAnd15thPayments();
// });

// Cron job to top up money into Stripe 2 days before payday (on the 1st and 15th)
cron.schedule('0 0 * * *', () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Set time to 00:00:00 for accurate date comparison
  
  // Determine the next payday (1st and 15th of the month)
  let nextPayday;
  const currentDay = today.getDate();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();
  
  if (currentDay < 15) {
    // The next payday is the 15th of this month
    nextPayday = new Date(currentYear, currentMonth, 15);
  } else {
    // The next payday is the 1st of next month
    nextPayday = new Date(currentYear, currentMonth + 1, 1);
  }
  
  // Determine the top-up date based on the payday
  const paydayDayOfWeek = nextPayday.getDay(); // 0 = Sunday, 1 = Monday
  const daysBeforeToTopup = (paydayDayOfWeek === 0 || paydayDayOfWeek === 1) ? 3 : 2;
  
  // Calculate the top-up date - JavaScript automatically adjusts the month if necessary
  const topupDate = new Date(nextPayday);
  topupDate.setDate(nextPayday.getDate() - daysBeforeToTopup);
  
  // Compare the current date with the top-up date
  const isTopupDay = 
    today.getFullYear() === topupDate.getFullYear() &&
    today.getMonth() === topupDate.getMonth() &&
    today.getDate() === topupDate.getDate();
  
  if (isTopupDay) {
    // Determine whether to execute the function for the 1st or the 15th payday
    if (nextPayday.getDate() === 1) {
      console.log(`Executing Stripe top-up ${daysBeforeToTopup} days before the 1st of the month payday`);
      AdminEp.checkStripeBlanceAnd1thPayments();
    } else {
      console.log(`Executing Stripe top-up ${daysBeforeToTopup} days before the 15th of the month payday`);
      AdminEp.checkStripeBlanceAnd15thPayments();
    }
  }
});


// Schedule the job for the 30th day of January, March, May, July, August, October, December months
cron.schedule(
  "0 0 30 1,3,5,7,8,10,12 *",
  TransactionsEp.setMonthlyAllAccumulatedBalanceCrownjobIn1stOfMonth
);

// Schedule the job for the 29th day of  April, June, September, November months
cron.schedule(
  "0 0 29 4,6,9,11 *",
  TransactionsEp.setMonthlyAllAccumulatedBalanceCrownjobIn1stOfMonth
);

// Schedule the job for the 27th day of February non-leap year
cron.schedule("0 0 27 2 *", () => {
  const year = new Date().getFullYear();
  const isLeapYear = year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);

  if (!isLeapYear) {
    TransactionsEp.setMonthlyAllAccumulatedBalanceCrownjobIn1stOfMonth();
  }
});

// Schedule the job for the 28th day of February leap year
cron.schedule("0 0 28 2 *", () => {
  const year = new Date().getFullYear();
  const isLeapYear = year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);

  if (isLeapYear) {
    TransactionsEp.setMonthlyAllAccumulatedBalanceCrownjobIn1stOfMonth();
  }
});

cron.schedule(
  "0 0 13 * *",
  TransactionsEp.setMonthlyAllAccumulatedBalanceCrownjobIn15thOfMonth
);

// Run every 15 mins
cron.schedule("0 */15 * * * *", AppointmentEp.sendReminderEmailsEvery15Minutes);

// Run every 15 mins - Process Jotform submissions
cron.schedule("0 */15 * * * *", processJotformSubmissions);



//Run every 20 mins
cron.schedule("0 */20 * * * *", ChatEp.sendSMSForUnreadUser);

//Run every 30 mins
cron.schedule("0 */30 * * * *", AppointmentEp.sendReminderEmailsEvery30Minutes);

// Run every 30 hour in purpose of send notifications in one hour
cron.schedule("0 */30 * * * *", AppointmentEp.sendReminderEmailsEveryHour);

// Run every 30min
cron.schedule("0 */30 * * * *", AppointmentEp.sendReminderEmailsEveryDay);

// Run every 30min
cron.schedule("0 */30 * * * *", AppointmentEp.sendReminderSMSsEveryNext18Hours);

// Run every 15min Notes Reminder
cron.schedule("0 */15 * * * *", AppointmentEp.sendReminderSMSsEveryNext48Hours);

// Run every day
cron.schedule("0 0 * * *", ArticleEp.shuffleSortList);

// Run every day
cron.schedule("0 0 * * *", ArticleEp.shuffleSortListOrder);

// Run every day
cron.schedule("0 0 * * *", AdminEp.updateFlagTreatmentHistories);

// Run every fouteen days
cron.schedule("0 0 */14 * *", AdminEp.setDailySuccessClaimsStatusUpdate);

// Run every 30 Minutes
// cron.schedule("0 */30 * * * *", AdminEp.setFirstMessageFromTherapistToClient);

// Run every two days
cron.schedule("0 0 */2 * *", AdminEp.set2DayClaimCopaymentStatusCheck);

// Run every 10 Minutes
cron.schedule("*/10 * * * *", AdminEp.setReminderMessageToClientVerify);

// Run every 10 Minutes
cron.schedule(
  "*/10 * * * *",
  AdminEp.setReminderMessageToClientNoInsuranceOrNoSubcription
);

// Run every 48h Notes Reminder
cron.schedule(
  "0 */15 * * * *",
  AdminEp.setReminderMessageToClientNoInsuranceOrNoSubcription48h
);

// Run every day - Admin Statistics Update
cron.schedule("0 0 * * *", AdminStatisticsEp.updateAdminStatisctics);

cron.schedule("*/2 * * * *", AdminEp.sendReminderSmsToClients);

cron.schedule("*/2 * * * *", everyTwoMinCronJob);

cron.schedule("0 * * * *", () => {
  InstagramFeedEp.fetchAndSaveInstagramFeed(); // Run the Instagram feed update job
});

// Run every 30Min hours
cron.schedule('0 */30 * * * *', () => {
  AdminEp.sendReminderSmsToMissed1stAppointmentClients()
});

cron.schedule('0 */2 * * *', () => {
  AdminEp.getUnsubmittedClaimsMd();
});

// Run every day
// Run duplicate treatment history detection every 15 minutes
cron.schedule("*/15 * * * *", detectDuplicateTreatmentHistories);

cron.schedule("0 0 * * *", () => { AdminEp.getAllERAList(); });

// Run every day - Reward allocation to therapists
cron.schedule("0 0 * * *", ReferralEarningEp.addRewardAmountToTherapist);

// Run every day - Reward allocation to clients
cron.schedule("0 0 * * *", ClientRewardEp.addRewardAmountToClients);

// Run every day 8 pm on America/New_York time
cron.schedule("0 20 * * *", AppointmentEp.sendTomorrowAppointmentScheduleReminderForTherapist, {
  timezone: "America/New_York"
});

// Run every day - Transmit faxes to insurance companies
cron.schedule("0 0 * * *", AdminEp.sendFaxToInsurance);

// Run every 15 minutes - Check the status of sent faxes and update the database accordingly
cron.schedule("*/15 * * * *", AdminEp.checkFaxStatus);

// Run every 8 hours - Resend faxes that have a message status of 'SendingFailed'
cron.schedule("0 */8 * * *", AdminEp.resendFaxesToInsurance);

// Run every hour - Check pending approvals and send reminders to admin
cron.schedule("0 * * * *", AdminEp.checkPendingApprovalsAndSendRemindersToAdmin);

// Run first of month - check EligibilityMD for each client
// cron.schedule("0 0 1 * *", InsuranceEp.checkEligibilityMDOnFirstOfMonth);
// cron.schedule("0 0 1 * *", EligibilityEp.checkEligibilityMDOnFirstOfMonth);
// cron.schedule("15 17 7 5 *", EligibilityEp.checkEligibilityMDOnFirstOfMonth);
// cron.schedule("22 20 13 5 *", EligibilityEp.checkEligibilityMDOnFirstOfMonth);
cron.schedule("59 15 13 5 *", EligibilityEp.checkEligibilityMDOnFirstOfMonth);

// Run every 1 hour - Admin statistic update
cron.schedule("0 * * * *", AdminStatisticsEp.updateAdminStatisticsData);

// Run every 12 hours - Track Twilio message status and update counters
cron.schedule("0 */24 * * *", trackTwilioMessageStatus);

// No inspection JSIgnoredPromiseFromCall
databaseSetup();

const app = express();
app.use(RequestLoggerHandler);
app.use(ResponseHandler);
app.use(express.json({ limit: "20mb" }));
app.use(express.urlencoded({ limit: "20mb", extended: true }));

// Noinspection JSIgnoredPromiseFromCall
passportStartup(app);
// app.use(morgan("combined")); // Comment để loại bỏ morgan logs

app.use(
  cors({
    optionsSuccessStatus: 200,
    origin: "*",
    allowedHeaders: [
      "Content-Type, Access-Control-Allow-Headers, Access-Control-Allow-Origin, Authorization, X-Requested-With",
      "Cache-Control",
    ],
  })
);

app.use("/api/auth", Authentication.verifyToken);

let server = new http.Server(app);

const connections = [];
export let chatUserList: any = [];

export let io: Server<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any>;

io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["PUT", "GET", "POST", "DELETE", "OPTIONS"],
    credentials: true,
  },
  transports: ["polling"],
});

io.on("connection", (socket: any) => {
  connections.push(socket);

  console.log("Connected: %s sockets connected.", connections.length);

  if (socket) {
    io.to(socket.id).emit("new-connect", { socket_id: socket.id });
  }

  socket.on("disconnect", (data: any) => {
    console.log("Disconnected: %s sockets.", connections.length);

    //remove user from chatUserList
    //chatUserList = chatUserList.filter((user: any) => user.socketId !== socket.id);
    let asignTempArrayToMainArr = true;

    const tempChatUserList = chatUserList.map((user: any) => {
      if (
        Array.isArray(user?.socketId) &&
        user?.socketId.some((socketId: any) => socketId == socket.id)
      ) {
        if (user.socketId.length <= 1) {
          asignTempArrayToMainArr = false;
          return;
        } else {
          const tempSocketIds = user.socketId.filter(
            (socketIdinner: any) => socketIdinner !== socket.id
          );
          return { ...user, socketId: tempSocketIds };
        }
      }
      return user;
    });

    if (asignTempArrayToMainArr) {
      chatUserList = tempChatUserList;
    } else {
      chatUserList = chatUserList.filter((user: any) =>
        user?.socketId.some((socketId: any) => socketId != socket.id)
      );
    }

    io.emit("get-online-users", chatUserList);
  });

  // new socket implementation for video call starts here

  // Call User
  socket.on("call-user", async (data: any) => {
    const { receiverId } = data;
    const { senderId } = data;

    const sendingUser = chatUserList.find(
      (user: any) => user.userId === senderId
    );

    const recievingUser = chatUserList.find(
      (user: any) => user.userId === receiverId
    );

    io.to(recievingUser?.socketId).emit("incoming-call", {
      callId: data.callId,
      senderSocketId: data.senderSocketId,
      useDefaultAvatar: data.useDefaultAvatar,
      avatarId: data.avatarId,
      avatarBackgroundId: data.avatarBackgroundId,
      incognito: data.incognito,
      therapistId: data.therapistId,
      callerName: data.callerName,
      isVideoCall: data.isVideoCall,
      meetingDuration: data.meetingDuration,
      recieversUserId: data.myUserId,
      recieverRcordingAllowed: data.recieverRcordingAllowed,
      videoSdkToken: data.videoSdkToken,
    });

    io.to(sendingUser?.socketId).emit("outgoing-call", {
      callId: data.callId,
      socketId: data.socketId,
      isVideoCall: data.isVideoCall,
    });
  });

  socket.on("vonage-call-user", async (data: any) => {
    const { receiverId } = data;
    const { senderId } = data;

    const sendingUser = chatUserList.find(
      (user: any) => user.userId === senderId
    );

    const recievingUser = chatUserList.find(
      (user: any) => user.userId === receiverId
    );

    io.to(recievingUser?.socketId).emit("vonage-incoming-call", {
      callId: data.callId,
      senderSocketId: data.senderSocketId,
      useDefaultAvatar: data.useDefaultAvatar,
      avatarId: data.avatarId,
      avatarBackgroundId: data.avatarBackgroundId,
      incognito: data.incognito,
      therapistId: data.therapistId,
      callerName: data.callerName,
      isVideoCall: data.isVideoCall,
      meetingDuration: data.meetingDuration,
      recieversUserId: data.myUserId,
      recieverRcordingAllowed: data.recieverRcordingAllowed,
      vonageSessionId: data.vonageSessionId,
    });

    io.to(sendingUser?.socketId).emit("outgoing-call", {
      callId: data.callId,
      socketId: data.socketId,
      isVideoCall: data.isVideoCall,
    });
  });

  // Respond Call
  socket.on("respond-call", async (data: any, callback: any) => {
    callback(true);
    const { receiverId } = data;
    const recievingUser = chatUserList.find(
      (user: any) => user.userId === receiverId
    );

    io.to(recievingUser?.socketId).emit("status-received", {
      status: data.status,
      callId: data.callId,
      senderName: data.senderName,
    });
  });

  // Extendcall time
  socket.on("extend-call-time", async (data: any, callback: any) => {
    callback(true);
    const { receiverId } = data;
    const recievingUser = chatUserList.find(
      (user: any) => user.userId === receiverId
    );

    io.to(recievingUser?.socketId).emit("call-time-extended", {
      status: data.status,
      callId: data.callId,
      extendedTime: data.extendedTime,
    });
  });

  // Cancel Call
  socket.on("cancel-call", async (data: any, callback: any) => {
    callback(true);
    const { receiverId } = data;
    const recievingUser = chatUserList.find(
      (user: any) => user.userId === receiverId
    );

    io.to(recievingUser?.socketId).emit("call-canceled", {
      callId: data.callId,
    });
  });

  // End meeting for both users
  socket.on("end-call-for-both", async (data: any) => {
    const { receiverId } = data;
    const recievingUser = chatUserList.find(
      (user: any) => user.userId === receiverId
    );

    io.to(recievingUser?.socketId).emit("end-call-for-both-recieved", {
      callId: data.callId,
      socketId: data.socketId,
      senderSocketId: data.senderSocketId,
    });
  });

  // Notifications
  socket.on("send-notification", async (data: any) => {
    const { receiverId } = data;
    const recievingUser = chatUserList.find(
      (user: any) => user.userId === receiverId
    );

    io.to(recievingUser?.socketId).emit("notification-received", {
      data: data,
    });
  });

  // new socket implementation for video call ends here
  // new socket implementation for video call starts here

  //chat feature socket configurations starts from here

  // Add new user to list
  socket.on("new-user-add", (newUserId: any) => {
    if (!chatUserList?.some((user: any) => user.userId === newUserId)) {
      chatUserList.push({
        userId: newUserId,
        socketId: [socket.id],
        active: true,
      });
    } else if (chatUserList?.some((user: any) => user.userId === newUserId)) {
      if (
        !chatUserList.some(
          (user: any) =>
            Array.isArray(user?.socketId) &&
            user?.socketId.some((socketId: any) => socketId == socket.id)
        )
      ) {
        const tempChatUserList = chatUserList.map((userInner: any) => {
          if (userInner?.userId == newUserId) {
            return {
              ...userInner,
              socketId: [...userInner.socketId, socket.id],
            };
          }
          return userInner;
        });
        chatUserList = tempChatUserList;
      }
    }

    io.emit("get-online-users", chatUserList);
  });

  // Send message
  socket.on("send-message", (data: any) => {
    const { receiverId } = data;

    const user = chatUserList.find((user: any) => user.userId === receiverId);

    io.to(user?.socketId).emit(
      "receive-message",
      data,
      function (err: any, responseData: any) {
        if (err) {
          console.log(err);
        } else {
          // Event was emitted successfully
        }
      }
    );
    io.to(user?.socketId).emit("refresh-unread-notification");
  });

  // Send online user list
  socket.on("fetch-online-users", () => {
    io.emit("get-online-users", chatUserList);
  });

  // Recipient is typing
  socket.on(
    "recipient-typing-receive",
    (receiverId: { recipientId: string; chatId: string }) => {
      const user = chatUserList.find(
        (user: any) => user.userId === receiverId.recipientId
      );
      io.to(user?.socketId).emit("recipient-is-typing", {
        isTyping: true,
        chatId: receiverId.chatId,
      });
    }
  );

  socket.on(
    "recipient-typing-end-receive",
    (receiverId: { recipientId: string; chatId: string }) => {
      const user = chatUserList.find(
        (user: any) => user.userId === receiverId.recipientId
      );
      io.to(user?.socketId).emit("recipient-is-typing", {
        isTyping: false,
        chatId: receiverId.chatId,
      });
    }
  );

  socket.on("end-call-for-all-vonage", async (data: any) => {
    const therapistName = data?.therapistName;
    data?.groupMembers.forEach((item: any) => {
      const recieverSocketId = item?.userDetails?.socketId;
      const recieverId = item?.userDetails?._id;
      const recieverMessage = "Call canceled!";
      io.to(recieverSocketId).emit("end-call-for-all-vonage-recieved", {
        recieverId,
        recieverMessage,
        therapistName: therapistName,
      });
    });
  });

  socket.on("join-group-chat-room", (roomId: any) => {
    socket.join(roomId);
  });

  socket.on("send-group-chat-message", (roomId: any, message: any) => {
    io.to(roomId).emit(`new-group-chat-message-${roomId}`, message);
  });

  socket.on("send-group-chat-message-delete", (roomId: any, messageId: any) => {
    io.to(roomId).emit(`group-chat-message-delete-${messageId}`, messageId);
  });

  socket.on(
    "send-group-chat-message-delete-in",
    (roomId: any, messageId: any) => {
      io.to(roomId).emit(
        `group-chat-message-delete-in-${messageId}`,
        messageId
      );
    }
  );
});

server.listen(PORT, () => {
  AppLogger.info("--> HTTPS Server successfully started at port " + PORT);
});

routes.initRoutes(app);
app.use(handleError);

export default app;
