var mongoose = require("mongoose");
import { NextFunction, Request, Response } from "express";
import { check, validationResult } from "express-validator";
import { Types } from "mongoose";
import { ClientDao } from "../dao/client-dao";
import { TherapistDao } from "../dao/therapist-dao";
import { UserDao } from "../dao/user-dao";
import { DClient, PremiumStatus, SubscriptionStatus } from "../models/client-model";
import { UserRole } from "../models/user-model";
import { Preference } from "../models/sub-models/preference-model";
import { EmailService } from "../mail/config";
import { InvoiceDao } from "../dao/invoice-dao";
import { PaymentStatus } from "../models/invoice-model";
import { SMSService } from "../sms/config";
import { Review } from "../models/sub-models/review-model";
import { AppointmentEp } from "./appointment-ep";
import { FriendRequestEp } from "./friend-request-ep";
import { TherapistEp } from "./therapist-ep";
import { ArticleEp } from "./article-ep";
import { VideoCallDao } from "../dao/videocall-dao";
import { VonageCallGroupEp } from "./vonage-call-group-ep";
import { AppointmentDao } from "../dao/appointment-dao";
import { FriendRequestDao } from "../dao/friend-request-dao";
import { SmsChatEp } from "./sms-chat-ep";

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

export function addReviewValidationRules() {
  return [
    check("noOfStars")
      .not()
      .isEmpty()
      .withMessage("No of stars cannot be empty.")
      .isNumeric()
      .withMessage("No of stars can only be a number"),
    check("review").not().isEmpty().withMessage("Review cannot be empty.").isString().withMessage("Review can only be a string."),
  ];
}
export namespace ClientEp {
  export function countValidationRules() {
    return [check("count").not().isEmpty().isNumeric().withMessage("Number of words is required!")];
  }

  export async function dislikeTherapist(req: Request, res: Response, next: NextFunction) {
    const role = req.user.role;
    const clientId = req.user._id;
    const therapistId = req.params.therapistId;

    if (role != UserRole.CLIENT) {
      return res.sendError("Invalid user role.");
    }
    if (!therapistId) {
      return res.sendError("Removing user id not found.");
    }

    try {
      if (!mongoose.Types.ObjectId.isValid(therapistId)) {
        return res.sendError("Invalid therapist Id.");
      }
      let therapist = await TherapistDao.getUserById(therapistId);

      if (!therapist) {
        return res.sendError("Invalid therapist Id.");
      }

      await UserDao.dislikeClient(Types.ObjectId(therapistId), clientId);

      let addedToDislikes = await UserDao.dislikeTherapist(clientId, Types.ObjectId(therapistId));

      if (addedToDislikes) {
        return res.sendSuccess("Therapist is added to disliked list.", "Success");
      }

      return res.sendError("Unable to dislike Therapist.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function addPreferences(req: Request, res: Response, next: NextFunction) {
    const clientId = req.user._id;
    const gender = req.body.gender;
    const ethnicityId = req.body.ethnicityId;
    const profession = req.body.professionId;
    const experiencedIn = req.body.experiencedIn;
    const role = req.user.role;

    if (role == UserRole.CLIENT) {
      try {
        const preferenceDetails: Preference = {};

        if (gender) {
          preferenceDetails.gender = gender;
        }

        if (ethnicityId) {
          preferenceDetails.ethnicityId = ethnicityId;
        }

        if (profession) {
          preferenceDetails.professionId = profession;
        }

        if (experiencedIn) {
          preferenceDetails.experiencedIn = experiencedIn;
        }

        const clientDetails: DClient = {
          preference: preferenceDetails,
        };

        let addedPreference = await ClientDao.updateClient(clientId, clientDetails);

        return res.sendSuccess(addedPreference, "Successfully Updated.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function clientSubscribe(req: Request, res: Response, next: NextFunction) {
    const role = req.user.role;

    if (role == UserRole.CLIENT) {
      try {
        const { paymentMethod } = req.body;

        const client = await ClientDao.getUserById(req.user._id);

        let stripeCusId;

        if (client.stripeCustomerId) {
          const stripeCustomer = await stripe.customers.retrieve(client.stripeCustomerId);

          if (!stripeCustomer.deleted) {
            stripeCusId = stripeCustomer.id;
            await stripe.paymentMethods.attach(paymentMethod, { customer: stripeCusId });
          } else {
            const customer = await stripe.customers.create({
              payment_method: paymentMethod,
              email: client.email,
              name: client.firstname + " " + client.lastname,
              invoice_settings: {
                default_payment_method: paymentMethod,
              },
            });

            stripeCusId = customer.id;
          }
        } else {
          const customer = await stripe.customers.create({
            payment_method: paymentMethod,
            email: client.email,
            name: client.firstname + " " + client.lastname,
            invoice_settings: {
              default_payment_method: paymentMethod,
            },
          });

          stripeCusId = customer.id;
        }

        const subscription = await stripe.subscriptions.create({
          customer: stripeCusId,
          items: [{ price: process.env.STRIPE_PRICE_ID }],
          expand: ["latest_invoice.payment_intent"],
          default_payment_method: paymentMethod,
        });

        const status = subscription["latest_invoice"]["payment_intent"]["status"];
        const clientSecret = subscription["latest_invoice"]["payment_intent"]["client_secret"];
        const subscriptionId = subscription.id;
        const subscriptionStatus = subscription.status;

        client.stripeCustomerId = stripeCusId;
        client.subscriptionId = subscriptionId;
        client.subscriptionStatus = subscriptionStatus;

        client.save();

        await EmailService.sendSubscriptionEmail(
          client,
          "New subscription is created!",
          "New subscription is created by",
          client.firstname + " " + client.lastname
        );

        await SMSService.sendEventSMS(`New subscription is created by ${client.firstname} ${client.lastname}`, client.primaryPhone);

        return res.sendSuccess(
          {
            clientSecret: clientSecret,
            status: status,
            subscriptionId: subscriptionId,
            subscriptionStatus: subscriptionStatus,
          },
          "New subscription created successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }


  export async function clientCreate(req: Request, res: Response, next: NextFunction) {
    const role = req.user.role;
    const email_text = "Congratulations! Your card information has been successfully added to your account. We are thrilled to welcome you to our community. To ensure that your payments are effortless, secure, and hassle-free, we have partnered with Stripe, the leading online payment platform. We appreciate your choice of our service and hope you have a wonderful experience with us. If you need any further assistance, please don't hesitate to let us know. We are always here to help."
    if (role == UserRole.CLIENT) {
      try {
        const { paymentMethod } = req.body;

        const client = await ClientDao.getUserById(req.user._id);
        let stripeCusId;

        if (client.stripeCustomerId) {
          const stripeCustomer = await stripe.customers.retrieve(client.stripeCustomerId);

          if (!stripeCustomer.deleted) {
            stripeCusId = stripeCustomer.id;
            await stripe.paymentMethods.attach(paymentMethod, { customer: stripeCusId });
          } else {
            const customer = await stripe.customers.create({
              payment_method: paymentMethod,
              email: client.email,
              name: client.firstname + " " + client.lastname,
              invoice_settings: {
                default_payment_method: paymentMethod,
              },
            });

            stripeCusId = customer.id;
          }
        } else {
          const customer = await stripe.customers.create({
            payment_method: paymentMethod,
            email: client.email,
            name: client.firstname + " " + client.lastname,
            invoice_settings: {
              default_payment_method: paymentMethod,
            },
          });

          stripeCusId = customer.id;
        }


        client.stripeCustomerId = stripeCusId;

        client.save();

        await EmailService.sendSubscriptionEmail(
          client,
          "Congratulations! Your Stripe account has been successfully created.",
          email_text,
          client.firstname + " " + client.lastname
        );

        await SMSService.sendEventSMS(`Congratulations! Your Stripe account has been successfully created.`, client.primaryPhone);

        return res.sendSuccess(
          client.premiumStatus,
          "New stripe account created successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }


  export async function attachPaymentMethod(req: Request, res: Response, next: NextFunction) {
    const role = req.user.role;
    if (role == UserRole.CLIENT) {
      try {
        const { paymentMethod } = req.body;

        const client = await ClientDao.getUserById(req.user._id);

        if (client.stripeCustomerId) {
          const newPaymentMethod = await stripe.paymentMethods.attach(paymentMethod, { customer: client.stripeCustomerId });

          const existingPaymentMethods = await stripe.paymentMethods.list({
            customer: client.stripeCustomerId,
            limit: 1,
          });

          if (existingPaymentMethods.data.length <= 1) {
            await stripe.customers.update(client.stripeCustomerId, {
              invoice_settings: {
                default_payment_method: paymentMethod,
              },
            });
          }

          await EmailService.sendSubscriptionEmail(
            client,
            "New payment method is attached!",
            "New payment method is attached by",
            client.firstname + " " + client.lastname
          );

          await SMSService.sendEventSMS(`New payment method is attached by ${client.firstname} ${client.lastname}`, client.primaryPhone);
          if (newPaymentMethod) {
            return res.sendSuccess(newPaymentMethod, "New payment method attached.");
          } else {
            return res.sendError("Unable to add new payment method.");
          }
        } else {
          const customer = await stripe.customers.create({
            payment_method: paymentMethod,
            email: client.email,
            name: client.firstname + " " + client.lastname,
            invoice_settings: {
              default_payment_method: paymentMethod,
            },
          });

          client.stripeCustomerId = customer.id;
          client.save();

          const newPaymentMethod = await stripe.paymentMethods.attach(paymentMethod, { customer: customer.id });

          await EmailService.sendSubscriptionEmail(
            client,
            "New payment method is attached!",
            "New payment method is attached by",
            client.firstname + " " + client.lastname
          );

          await SMSService.sendEventSMS(`New payment method is attached by ${client.firstname} ${client.lastname}`, client.primaryPhone);

          if (newPaymentMethod) {
            return res.sendSuccess(newPaymentMethod, "New payment method attached.");
          } else {
            return res.sendError("Unable to add new payment method.");
          }
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getActiveSubscription(req: Request, res: Response) {
    const role = req.user.role;

    if (role == UserRole.CLIENT) {
      try {
        const client = await ClientDao.getUserById(req.user._id);

        if (!client.subscriptionId) {
          return res.sendError("No active subscription found.");
        }

        const subscription = await stripe.subscriptions.retrieve(client.subscriptionId);

        if (client.subscriptionStatus != subscription.status) {
          client.subscriptionStatus = subscription.status;
          client.save();
        }

        if (subscription) {
          return res.sendSuccess(subscription, "Received active subscription.");
        } else {
          return res.sendError("No active subscription found.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getPaymentMethods(req: Request, res: Response) {
    const role = req.user.role;

    if (role == UserRole.CLIENT) {
      try {
        const client = await ClientDao.getUserById(req.user._id);

        if (!client.stripeCustomerId) {
          return res.sendError("Not an active stripe customer.");
        }

        const stripeCustomer = await stripe.customers.retrieve(client.stripeCustomerId);

        if (!stripeCustomer) {
          return res.sendError("Not an active stripe customer.");
        }

        const paymentMethods = await stripe.paymentMethods.list({
          customer: client.stripeCustomerId,
          type: "card",
        });

        if (paymentMethods) {
          const data = {
            paymentMethods: paymentMethods,
            stripeCustomer: stripeCustomer,
          };

          return res.sendSuccess(data, "Received payment methods.");
        } else {
          return res.sendError("No payment methods found.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getBillingHistory(req: Request, res: Response) {
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    if (role == UserRole.CLIENT) {
      try {
        const client = await ClientDao.getUserById(req.user._id);

        if (!client.stripeCustomerId) {
          return res.sendError("Not an active stripe customer.");
        }

        const paymentIntents = await stripe.paymentIntents.list({
          customer: client.stripeCustomerId,
        });

        if (paymentIntents) {
          return res.sendSuccess(paymentIntents, "Received payment intents.");
        } else {
          return res.sendError("No active subscription found.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function cancelSubscription(req: Request, res: Response) {
    const role = req.user.role;

    if (role == UserRole.CLIENT) {
      try {
        const client = await ClientDao.getUserById(req.user._id);

        if (
          !client.subscriptionId ||
          (client.subscriptionStatus != SubscriptionStatus.ACTIVE && client.subscriptionStatus != SubscriptionStatus.PAST_DUE)
        ) {
          return res.sendError("No active subscription to cancel.");
        }

        const subscriptionUpdated = stripe.subscriptions.update(client.subscriptionId, { cancel_at_period_end: true });

        await EmailService.sendSubscriptionEmail(
          client,
          "Subscription cancelled!",
          "Subscription is cancelled by",
          client.firstname + " " + client.lastname
        );

        await SMSService.sendEventSMS(`Subscription is cancelled by ${client.firstname} ${client.lastname}`, client.primaryPhone);
        if (subscriptionUpdated) {
          return res.sendSuccess(subscriptionUpdated, "Successfully cancelled.");
        } else {
          return res.sendError("Unable to cancel subscription.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function resumeSubscription(req: Request, res: Response) {
    const role = req.user.role;

    if (role == UserRole.CLIENT) {
      try {
        const client = await ClientDao.getUserById(req.user._id);

        if (
          !client.subscriptionId ||
          (client.subscriptionStatus != SubscriptionStatus.ACTIVE && client.subscriptionStatus != SubscriptionStatus.PAST_DUE)
        ) {
          return res.sendError("No active subscription to cancel.");
        }

        const subscriptionUpdated = stripe.subscriptions.update(client.subscriptionId, { cancel_at_period_end: false });

        if (subscriptionUpdated) {
          return res.sendSuccess(subscriptionUpdated, "Successfully Resumed.");
        } else {
          return res.sendError("Unable to resume subscription.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function changeDefaultPaymentMethod(req: Request, res: Response) {
    const role = req.user.role;

    if (role == UserRole.CLIENT) {
      try {
        const client = await ClientDao.getUserById(req.user._id);

        if (!client.stripeCustomerId) {
          return res.sendError("Not a valid stripe customer.");
        }

        const paymentMethodId = req.body.paymentMethodId;

        const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);

        if (!paymentMethod) {
          return res.sendError("No such payment method found.");
        }

        if (paymentMethod.customer != client.stripeCustomerId) {
          return res.sendError("You don't have access to this payment method.");
        }

        if (client.subscriptionId && client.subscriptionId != "") {
          if (client.subscriptionStatus != SubscriptionStatus.ACTIVE && client.subscriptionStatus != SubscriptionStatus.PAST_DUE) {
            return res.sendError("No active subscription to update.");
          }

          const subscriptionUpdated = stripe.subscriptions.update(client.subscriptionId, { default_payment_method: paymentMethodId });

          if (subscriptionUpdated) {
            return res.sendSuccess(subscriptionUpdated, "Default payment method is updated successfully.");
          } else {
            return res.sendError("No active subscription found.");
          }
        } else {
          if (client.premiumStatus == PremiumStatus.ACTIVE) {
            const customer = await stripe.customers.update(client.stripeCustomerId, {
              invoice_settings: {
                default_payment_method: paymentMethodId,
              },
            });

            if (customer) {
              return res.sendSuccess("Default payment method is updated successfully.");
            }
          } else {
            return res.sendError("Sorry! You don't have access to premium features.");
          }
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function makeMonthlyInvoicePayment(req: Request, res: Response) {
    const clientId = req.user._id;
    const invoiceId = req.params.invoiceId;

    try {
      if (!invoiceId) {
        return res.sendError("Invalid invoice id.");
      }

      const client = await ClientDao.getUserById(clientId);

      if (!client.stripeCustomerId) {
        return res.sendError("Not a valid stripe customer. Please create payment method first.");
      }

      const invoice = await InvoiceDao.getInvoiceById(Types.ObjectId(invoiceId), clientId);

      if (!invoice) {
        return res.sendError("Invalid invoice id.");
      }

      if (invoice.paymentStatus == PaymentStatus.PAID) {
        return res.sendError("Invoice is already paid.");
      }

      if (invoice.dueAmount <= 0) {
        return res.sendError("Your due amount to be paid is $0.");
      }

      const stripeCustomer = await stripe.customers.retrieve(client.stripeCustomerId);

      if (!stripeCustomer || !stripeCustomer.invoice_settings.default_payment_method) {
        return res.sendError("Not a valid stripe customer. Please create payment method first.");
      }

      const chargeForMonth = await stripe.paymentIntents.create({
        amount: Math.round(invoice.dueAmount * 100),
        currency: "usd",
        customer: client.stripeCustomerId,
        payment_method: stripeCustomer.invoice_settings?.default_payment_method,
        description: "Client: " + client.email + ", Payment Month: " + invoice.paymentMonth,
        confirm: true,
      });

      if (chargeForMonth) {
        invoice.paymentStatus = PaymentStatus.PAID;
        invoice.save();

        await EmailService.clientDuePaymentSettled(
          "Payment is successfully made for month " + invoice.paymentMonth + ".",
          client.email,
          client.firstname,
          invoice.dueAmount,
          invoice.paymentMonth
        );

        await SMSService.sendEventSMS(
          `Payment is successfully made for month ${invoice.paymentMonth}.Hi ${client.firstname} Successfully made payment of $${invoice.dueAmount} for the month of ${invoice.paymentMonth} `,
          client.primaryPhone
        );

        return res.sendSuccess("Payment is successfully made for month " + invoice.paymentMonth + ".");
      } else {
        return res.sendError("Unable to charge your card at this time. Please try again.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateWordCount(req: Request, res: Response) {
    const role = req.user.role;
    const clientId = req.user._id;
    const wordCount = Number(req.body.count);

    if (role == UserRole.CLIENT) {
      let resp = await ClientDao.updateClient(clientId, {
        chatWordCount: wordCount,
      });

      return res.sendSuccess("", "Word count updated!");
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function getClientByClientId(req: Request, res: Response) {
    const role = req.user.role;
    const clientId = req.params.clientId;

    if (role == UserRole.THERAPIST || role == UserRole.SUPER_ADMIN || role == UserRole.CLIENT || role == UserRole.SUB_ADMIN) {
      let client = await ClientDao.getUserById(clientId);

      return res.sendSuccess(client, "Success");
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function getSingleClientByClientId(req: Request, res: Response) {
    const role = req.user.role;
    const clientId = req.params.clientId;

    if (role == UserRole.THERAPIST) {
      let client = await ClientDao.getUserById(clientId);
      let clientData = {
        coverPhotoId: client?.coverPhotoId,
        photoId: client?.photoId,
        description: client?.description,
        firstname: client?.firstname,
        lastname: client?.lastname,
        gender: client?.gender,
        dateOfBirth: client?.dateOfBirth,
        ethnicityId: client?.ethnicityId,
        username: client?.username,
        primaryPhone: client?.primaryPhone,
      }
      return res.sendSuccess(clientData, "Success");
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function sendReferralMail(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    const emailAddress = req.body.emailAddress

    const content = req.body.url;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (!emailAddress) {
      return res.sendError("email is required.");
    }
    if (content === undefined || content === null) {
      return res.sendError("url is not set.");
    } else {

      try {
        await EmailService.sendTherapistReferralLinkEmail(
          emailAddress,
          content,
        );
        return res.sendSuccess("Success");
      } catch (error) {
        return res.sendError(error);
      }
    }
  }

  export async function sendReferralSMS(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const content = req.body.url;
    const primaryPhone = req.body.phoneNumber;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (!primaryPhone) {
      return res.sendError("Phone number is required")
    }
    if (content === undefined || null) {
      return res.sendError("No Message body");
    }

    try {

      await SMSService.sendEventSMS(
        content,
        primaryPhone
      );

      return res.sendSuccess("Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sessiongraphCount(_id: any, userRole: any,) {
    const userId = _id;
    const role = userRole;
    if (
      role == UserRole.THERAPIST ||
      role == UserRole.CLIENT
    ) {
      try {
        const user = await UserDao.getUserById(userId);

        if (user) {
          const stat =
            await VideoCallDao.getAllMeetingsForSpecificTherapistForCurrentWeekDao(
              userId
            );

          if (stat.length === 0) {
            const error3 = "Something went wrong! Could not load appointment statistics"
            return error3;
          }

          return stat;
        } else {
          const error3 = "User not found"
          return error3;
        }
      } catch (error) {
        return error;
      }
    } else {
      const error3 = "Invalid user role."
      return error3;
    }
  }

  export async function getClientDashboardStats(req: Request, res: Response, next: NextFunction) {
    const role = req.user.role;
    const userId = req.user._id;

    if (role == UserRole.CLIENT) {
      try {
        let upcommingAppointments = await AppointmentEp.viewAllUpcomingAppointmentStats(role, userId, 3);
        let pendingAppointments = await AppointmentEp.viewAllPendingAppointmentStats(role, userId, 3);
        let matchedTherapists = await FriendRequestEp.viewAllRequestsByClientStats(userId, 3, 1);
        let matchedTherapistMobile = await FriendRequestEp.viewAllRequestsByClientStats(userId, 1, 1);
        let allTherapists = await TherapistEp.searchTherapistsStats(role, userId, 3, 1);
        let articles = await ArticleEp.getArticlesStats(userId, 3, 1);
        let clientStats = await ClientEp.sessiongraphCount(userId, role);
        let getAllUpCommingGroupCalls = await VonageCallGroupEp.viewAllUpCommingGroupCalls(role, userId, 3);

        let data = {
          upcommingAppointmentStats: upcommingAppointments,
          pendingAppointmentStats: pendingAppointments,
          matchedTherapistStats: matchedTherapists,
          matchedTherapistMobileStatus: matchedTherapistMobile,
          allTherapistStats: allTherapists,
          articleStats: articles,
          clientStats: clientStats,
          allUpCommingGroupCalls: getAllUpCommingGroupCalls
        };

        return res.sendSuccess(data, "statistics data.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("you have not permission");
    }
  }



  export async function getClientAppointment(req: Request, res: Response, next: NextFunction) {
    const role = req.user.role;
    const userId = req.user._id;
    
    if (role == UserRole.CLIENT) {
      try {
        let appointmentList = await AppointmentDao.getAllAppointmentsByClientIdNew(
          userId,
          "2",
          100,
          1
        );

        return res.sendSuccess(appointmentList, "Appointmnet data.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("you have not permission");
    }
  }

  export async function getAllMatchedTherapists(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      let matchedTherapists = await FriendRequestEp.viewAllRequestsByClientStats(req.user._id);

      return res.sendSuccess(matchedTherapists, "All matched therapists.");
    } catch (error) {
      return res.sendError(error);
    }
  }
  
  export async function changePrimaryTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapistId = req.body.therapistId;

    try {
      let isRequestValid = await FriendRequestDao.checkIfUserIsFriend(
        req.user._id,
        therapistId
      );

      if(!isRequestValid) {
        return res.sendError("Sorry you're not connected with this Therapist.");
      }

      let changePrimaryTherapist = await ClientDao.updateClient(
        req.user._id,
        {primaryTherapist: therapistId}
      );

      if(changePrimaryTherapist) {

        SmsChatEp.changeTherapistFromConversation(changePrimaryTherapist.primaryTherapist,req.user.role.toString(),req.user._id)

        return res.sendSuccess("Primary therapist is updated successfully!", therapistId);
      } else {
        return res.sendError("Error occured while updating primary therapist");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendReferralLinkSMS(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const messageContent = req.body.messageContent;
    const primaryPhone = req.body.phoneNumber;
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (!primaryPhone) {
      return res.sendError("Phone number is required");
    }
    if (messageContent === undefined || null) {
      return res.sendError("No Message body");
    }

    try {
      await SMSService.sendEventSMS(messageContent, primaryPhone);

      return res.sendSuccess("Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendReferralLinkMail(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const emailAddress = req.body.emailAddress;
    const message = req.body.messageContent;
    const updatedMessage = message.replace(/\n\n/g, "<br/><br/>");
    const messageContent = updatedMessage;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (!emailAddress) {
      return res.sendError("email is required.");
    }
    if (messageContent === undefined || messageContent === null) {
      return res.sendError("No Message body");
    } else {
      try {
        await EmailService.sendClientReferralLinkEmailByClient(
          emailAddress,
          messageContent
        );
        return res.sendSuccess("Success");
      } catch (error) {
        return res.sendError(error);
      }
    }
  }

  export async function getClientCopaymentAmount(req: Request, res: Response, next: NextFunction) {
    try {
      const clientId = req.user._id;
      
      if (!mongoose.Types.ObjectId.isValid(clientId)) {
        return res.sendError("Invalid client ID");
      }

      const copaymentAmount = await ClientDao.getClientCopaymentAmount(clientId);
      
      return res.sendSuccess({ copaymentAmount }, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getClientLastCopaymentInfo(req: Request, res: Response, next: NextFunction) {
    try {
      const clientId = req.user._id;
      
      if (!mongoose.Types.ObjectId.isValid(clientId)) {
        return res.sendError("Invalid client ID");
      }

      const client = await ClientDao.getUserById(clientId);
      
      if (!client) {
        return res.sendError("Client not found");
      }
      
      const lastCopaymentInfo = {
        last_pay_copayment_time: client.last_pay_copayment_time || null,
        last_pay_copayment_amount: client.last_pay_copayment_amount || null
      };
      
      return res.sendSuccess(lastCopaymentInfo, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

}
