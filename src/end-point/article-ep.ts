import { NextFunction, Request, Response } from "express";
import { check, validationResult } from "express-validator";
import { Types } from "mongoose";
import multer = require("multer");
import path = require("path");
import { AdminDao } from "../dao/admin-dao";
import { ArticleDao } from "../dao/article-dao";
import { TherapistDao } from "../dao/therapist-dao";
import { UploadDao } from "../dao/upload-dao";
import { UserDao } from "../dao/user-dao";
import { EmailService } from "../mail/config";
import { DArticle } from "../models/article-model";
import { DHashtag } from "../models/hashtag-model";
import { DUpload } from "../models/upload-model";
import { UserRole } from "../models/user-model";
import { SMSService } from "../sms/config";
import { UploadCategory } from "./user-ep";
let mongoose = require("mongoose");
let fs = require("fs");

export namespace ArticleEp {
  export function searchArticlesByTagsValidationRules() {
    return [
      check("searchTags").isArray().withMessage("Search tags are required."),
      check("type").isString().not().isEmpty().withMessage("Type is required."),
    ];
  }

  export function searchArticlesByTagsAndHashTagsValidationRules() {
    return [
      check("experienceTags")
        .isArray()
        .withMessage("Search tags are required."),
      check("hashTags").isArray().withMessage("Hash tags are required."),
    ];
  }

  export function addReplyValidationRules() {
    return [
      check("articleId").not().isEmpty().withMessage("Article Id is required."),
      check("commentId").not().isEmpty().withMessage("Comment Id is required."),
      check("reply")
        .not()
        .isEmpty()
        .withMessage("Reply is required.")
        .isString()
        .withMessage("Reply is required."),
    ];
  }

  export async function addArticle(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    let destination1 = `${process.env.UPLOAD_PATH}/${UploadCategory.ARTICLE_IMAGE}`;

    fs.access(destination1, (error: any) => {
      if (error) {
        return fs.mkdir(destination1, (error: any) => {
          return true;
        });
      }
    });

    const storage = multer.diskStorage({
      destination: async (req, file, cb) => {
        await articleValidationRules(req, file, cb);
      },
    });

    async function articleValidationRules(req: any, file: any, cb: any) {
      try {
        let articleDetails = JSON.parse(req.body.articleDetails);

        if (
          !articleDetails.articleTitle ||
          typeof articleDetails.articleTitle !== "string"
        ) {
          return cb(Error("Article title is required."));
        }

        if (
          !articleDetails.articleTags ||
          !(articleDetails.articleTags instanceof Array) ||
          articleDetails.articleTags.length === 0
        ) {
          return cb(Error("Article tags are required."));
        }

        if (
          !articleDetails.hashTags ||
          !(articleDetails.hashTags instanceof Array) ||
          articleDetails.hashTags.length === 0
        ) {
          return cb(Error("Hashtags are required."));
        }

        if (
          !articleDetails.articleBody ||
          typeof articleDetails.articleBody !== "string"
        ) {
          return cb(Error("Article body is required."));
        }

        if (file.fieldname === "articleFile") {
          cb(null, destination1);
        }
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    const upload = multer({
      storage: storage,
    }).fields([{ name: "articleFile", maxCount: 1 }]);

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        const fileType = req.body.fileType;

        let uploadedArticleFile;

        if (fileType === "IMAGE") {
          if ((req.files as any).articleFile.length <= 0) {
            return res.sendError("Article image or video is required.");
          }

          const upload: any = (req.files as any).articleFile[0];

          const articleFile: DUpload = {
            userId: userId as unknown as Types.ObjectId,
            originalName: upload.originalname.replace(/ /g, ""),
            name: upload.filename,
            type: upload.mimetype,
            path: upload.path,
            fileSize: upload.size,
            extension: path.extname(upload.originalname),
            category: UploadCategory.ARTICLE_IMAGE,
          };

          uploadedArticleFile = await UploadDao.createUpload(articleFile);
        }

        let requestBody: any;

        try {
          requestBody = JSON.parse(req.body.articleDetails);
        } catch (error) {
          return res.sendError("Invalid article details.");
        }

        let newExpTags: Types.ObjectId[] = [];

        try {
          await Promise.all(
            requestBody.hashTags.map(async (tag: any) => {
              const isFound = await AdminDao.getHashTagByName(tag);
              if (!isFound || isFound == null) {
                const hashTagData: DHashtag = {
                  name: tag,
                };
                await AdminDao.addHashtag(hashTagData);
              }
            })
          );
        } catch (error) {
          return res.sendError(error);
        }

        try {
          await Promise.all(
            requestBody.articleTags.map(async (tag: any) => {
              const isFound = await AdminDao.getExperienceTagsByName(tag);
              if (!isFound || isFound == null) {
                const addedTag = await AdminDao.addExperienceTag(tag);
                newExpTags.push(addedTag._id);
              } else {
                newExpTags.push(isFound._id);
              }
            })
          );
        } catch (error) {
          return res.sendError(error);
        }

        let article: DArticle;

        if (fileType === "VIDEO") {
          article = {
            createdBy: userId,
            articleTitle: requestBody.articleTitle,
            articleTags: newExpTags,
            articleBody: requestBody.articleBody,
            hashTags: requestBody.hashTags,
            vimeoId: requestBody.vimeoId,
          };
        } else {
          article = {
            createdBy: userId,
            articleTitle: requestBody.articleTitle,
            articleTags: newExpTags,
            articleBody: requestBody.articleBody,
            uploadId: uploadedArticleFile._id,
            hashTags: requestBody.hashTags,
          };
        }

        try {
          let savedArticle = await ArticleDao.addArticle(article);

          if (!savedArticle) {
            return res.sendError("Error while saving article details.");
          }

          const therapist = await UserDao.getUserById(userId);

          const clientsList = await AdminDao.getAllClients(-1, 0);

          const customEmailArray: { to: { email: any }[] }[] = [];

          await Promise.all(
            clientsList.map(async (client: any) => {
              customEmailArray.push({ to: [{ email: client.email }] });
            })
          );

          await EmailService.newPostInDiscover(
            "Lavni - New blog post created by " +
            therapist.firstname +
            " " +
            therapist.lastname,
            requestBody.articleTitle,
            therapist.firstname + " " + therapist.lastname,
            savedArticle._id,
            customEmailArray
          );
          await SMSService.sendEventSMS(
            "Lavni - New blog post created!",
            therapist.primaryPhone
          );

          return res.sendSuccess(savedArticle, "Article saved.");
        } catch (error) {
          return res.sendError(error);
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateArticle(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    const articleId = Types.ObjectId(req.params.id);

    try {
      let article = await ArticleDao.getArticleById(articleId);

      if (!article) {
        return res.sendError("Article not found for the provided article id");
      }
    } catch (error) {
      return res.sendError(error);
    }

    let destination1 = `${process.env.UPLOAD_PATH}/${UploadCategory.ARTICLE_IMAGE}`;

    const storage = multer.diskStorage({
      destination: async (req, file, cb) => {
        await articleValidationRules(req, file, cb);
      },
    });

    async function articleValidationRules(req: any, file: any, cb: any) {
      try {
        let articleDetails = null;

        try {
          articleDetails = JSON.parse(req.body.articleDetails);
        } catch (error) {
          return cb(Error(error + ":("), null);
        }

        if (
          !articleDetails.articleTitle ||
          typeof articleDetails.articleTitle !== "string"
        ) {
          return cb(Error("Article title is required."), null);
        }

        if (
          !articleDetails.articleTags ||
          !(articleDetails.articleTags instanceof Array) ||
          articleDetails.articleTags.length === 0
        ) {
          return cb(Error("Article tags are required."), null);
        }

        if (
          !articleDetails.hashTags ||
          !(articleDetails.hashTags instanceof Array) ||
          articleDetails.hashTags.length === 0
        ) {
          return cb(Error("Hashtags are required."), null);
        }

        if (
          !articleDetails.articleBody ||
          typeof articleDetails.articleBody !== "string"
        ) {
          return cb(Error("Article body is required."), null);
        }

        if (file.fieldname === "articleFile") {
          cb(null, destination1);
        }
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    const upload = multer({
      storage: storage,
    }).fields([{ name: "articleFile", maxCount: 1 }]);

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        let requestBody;

        try {
          requestBody = JSON.parse(req.body.articleDetails);
        } catch (error) {
          return res.sendError("Provided details are not valid");
        }

        let article: DArticle;

        let newExpTags: Types.ObjectId[] = [];

        try {
          await Promise.all(
            requestBody.hashTags.map(async (tag: any) => {
              const isFound = await AdminDao.getHashTagByName(tag);
              if (!isFound || isFound == null) {
                const hashTagData: DHashtag = {
                  name: tag,
                };
                await AdminDao.addHashtag(hashTagData);
              }
            })
          );
        } catch (error) {
          return res.sendError(error);
        }

        try {
          await Promise.all(
            requestBody.articleTags.map(async (tag: any) => {
              const isFound = await AdminDao.getExperienceTagsByName(tag);
              if (!isFound || isFound == null) {
                const addedTag = await AdminDao.addExperienceTag(tag);
                newExpTags.push(addedTag._id);
              } else {
                newExpTags.push(isFound._id);
              }
            })
          );
        } catch (error) {
          return res.sendError(error);
        }

        const fileType = req.body.fileType;

        let uploadedArticleFile;

        if (fileType === "IMAGE") {
          if ((req.files as any).articleFile.length <= 0) {
            return res.sendError("Article image or video is required.");
          }

          const upload: any = (req.files as any).articleFile[0];

          const articleFile: DUpload = {
            userId: userId as unknown as Types.ObjectId,
            originalName: upload.originalname.replace(/ /g, ""),
            name: upload.filename,
            type: upload.mimetype,
            path: upload.path,
            fileSize: upload.size,
            extension: path.extname(upload.originalname),
            category: UploadCategory.ARTICLE_IMAGE,
          };

          uploadedArticleFile = await UploadDao.createUpload(articleFile);
        }

        if (fileType === "VIDEO") {
          article = {
            createdBy: userId,
            articleTitle: requestBody.articleTitle,
            articleTags: newExpTags,
            articleBody: requestBody.articleBody,
            hashTags: requestBody.hashTags,
            vimeoId: requestBody.vimeoId,
            uploadId: null,
          };
        } else {
          article = {
            createdBy: userId,
            articleTitle: requestBody.articleTitle,
            articleTags: newExpTags,
            articleBody: requestBody.articleBody,
            uploadId: uploadedArticleFile._id,
            hashTags: requestBody.hashTags,
            vimeoId: null,
          };
        }

        if (requestBody.deletingUploadId) {
          await deletePreviousArticleFile(requestBody.deletingUploadId);
        }

        async function deletePreviousArticleFile(uploadId: string) {
          let resultHandler = async function (error: any) {
            if (error) {
              throw error;
            }
          };

          try {
            let upload = await UploadDao.getUpload(uploadId);
            await fs.unlink(upload.path, resultHandler);
            await UploadDao.deleteUploadById(uploadId);
          } catch (error) {
            return res.sendError(
              "Previous article image could not be deleted."
            );
          }
        }

        try {
          let savedArticle = await ArticleDao.updateArticle(articleId, article);

          if (!savedArticle) {
            return res.sendError("Error while saving article details.");
          }

          return res.sendSuccess(
            savedArticle,
            "Article is updated successfully!"
          );
        } catch (error) {
          return res.sendError(error);
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateArticleWithoutFile(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    const articleId = Types.ObjectId(req.params.id);

    try {
      let article = await ArticleDao.getArticleById(articleId);

      if (!article) {
        return res.sendError("Article not found for the provided article id");
      }

      let newExpTags: Types.ObjectId[] = [];

      try {
        await Promise.all(
          req.body.articleTags.map(async (tag: any) => {
            const isFound = await AdminDao.getHashTagByName(tag);
            if (!isFound || isFound == null) {
              const hashTagData: DHashtag = {
                name: tag,
              };
              await AdminDao.addHashtag(hashTagData);
            }
          })
        );
      } catch (error) {
        return res.sendError(error);
      }

      try {
        await Promise.all(
          req.body.articleTags.map(async (tag: any) => {
            const isFound = await AdminDao.getExperienceTagsByName(tag);
            if (!isFound || isFound == null) {
              const addedTag = await AdminDao.addExperienceTag(tag);
              newExpTags.push(addedTag._id);
            } else {
              newExpTags.push(isFound._id);
            }
          })
        );
      } catch (error) {
        return res.sendError(error);
      }

      let updatedArticle: DArticle;

      updatedArticle = {
        createdBy: userId,
        articleTitle: req.body.articleTitle,
        articleTags: newExpTags,
        articleBody: req.body.articleBody,
        hashTags: req.body.hashTags,
      };

      try {
        let savedArticle = await ArticleDao.updateArticle(
          articleId,
          updatedArticle
        );

        if (!savedArticle) {
          return res.sendError("Error while saving article details.");
        }

        return res.sendSuccess(
          savedArticle,
          "Article is updated successfully!"
        );
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteArticle(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    const articleId = Types.ObjectId(req.params.articleId);

    try {
      let article: any = await ArticleDao.getArticleById(articleId);

      if (article == null) {
        return res.sendError("No article found for the provided Id");
      }

      if (article.createdBy._id.toString() !== userId.toString()) {
        return res.sendError("You cannot delete articles published by others.");
      }

      try {
        let deletedArticle = await ArticleDao.deleteArticleById(articleId);

        return res.sendSuccess(deletedArticle, "Article deleted.");
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }
  const sortListArray: any = [];
  const sortOrderArray: any = [1];
  export async function shuffleSortList() {
    const array = ["articleTitle", "_id", "createdAt", "updatedAt", "hashTags"];
    var currentIndex = array.length,
      temporaryValue,
      randomIndex;

    while (0 !== currentIndex) {
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex -= 1;

      temporaryValue = array[currentIndex];
      array[currentIndex] = array[randomIndex];
      array[randomIndex] = temporaryValue;
    }
    if (sortListArray[0] == undefined) {
      sortListArray.splice(0, sortListArray.length);
      sortListArray.push("articleTitle");
    } else {
      sortListArray.splice(0, sortListArray.length);
      sortListArray.push(array[0]);
    }
  }
  export async function shuffleSortListOrder() {
    if (sortOrderArray[0] == 1) {
      sortOrderArray.splice(0, sortOrderArray.length);
      sortOrderArray.push([-1]);
    } else {
      sortOrderArray.splice(0, sortOrderArray.length);
      sortOrderArray.push([1]);
    }
  }

  export async function getAllArticles(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const sortItem =
      sortListArray[0] == undefined ? "articleTitle" : sortListArray[0];
    const sortOrder = Number(sortOrderArray[0]);

    try {
      let articles = await ArticleDao.getAllArticles(
        userId,
        limit,
        offset,
        sortItem,
        sortOrder
      );
      let articleCount = await ArticleDao.getAllArticlesCount(userId);
      let newCount = articleCount - limit * offset;
      if (articles.length === 0) {
        return res.sendError("No articles found.");
      }

      const data = {
        articleSet: articles,
        count: newCount,
      };

      return res.sendSuccess(data, "Articles found.");
    } catch (error) {
      return res.sendError(error);
    }
  }



  export async function getArticlesStats(_id: any, userLimit: any, userOffset: any) {
    const userId = _id;
    const limit = Number(userLimit);
    const offset = Number(userOffset);
    const sortItem =
      sortListArray[0] == undefined ? "articleTitle" : sortListArray[0];
    const sortOrder = Number(sortOrderArray[0]);

    try {
      let articles = await ArticleDao.getAllArticlesStats(
        userId,
        limit,
        offset,
        sortItem,
        sortOrder
      );
      if (articles.length === 0) {
        const error3 = "No articles found."
        return error3;
      }

      return articles;
    } catch (error) {
      return error;
    }
  }
  export async function getAllPublicArticles(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const sortItem =
      sortListArray[0] == undefined ? "articleTitle" : sortListArray[0];
    const sortOrder = Number(sortOrderArray[0]);

    try {
      let articles = await ArticleDao.getAllPublicArticles(
        limit,
        offset,
        sortItem,
        sortOrder
      );
      let articleCount = await ArticleDao.getAllPublicArticlesCount();
      let newCount = articleCount - limit * offset;
      if (articles.length === 0) {
        return res.sendError("No articles found.");
      }

      const data = {
        articleSet: articles,
        count: newCount,
      };

      return res.sendSuccess(data, "Articles found.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllArticlePublic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const shuffle = (array: any) => {
      var currentIndex = array.length,
        temporaryValue,
        randomIndex;

      while (0 !== currentIndex) {
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex -= 1;

        temporaryValue = array[currentIndex];
        array[currentIndex] = array[randomIndex];
        array[randomIndex] = temporaryValue;
      }

      return array;
    };
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const code = Number(Math.floor(Math.random() * 10));
    const articlecount = await ArticleDao.getAllArticle();

    try {
      const articleIds = ["62d582057c4a0e2a49eb7adf", "62d5806f7c4a0e2a49eb7a3e", "62d58a8c7c4a0e2a49eb7c28"];
      const articles = await ArticleDao.getAllArticlesPublic(limit, articleIds, offset);
      // const row = shuffle(articles);
      if (articles.length === 0) {
        return res.sendError("Something went wrong! Could not load articles.");
      }
      return res.sendSuccess(articles, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getArticleById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const articleId = Types.ObjectId(req.params.id);
    const user = req.user.role;
    const userId = req.user._id;

    if (
      user == UserRole.THERAPIST ||
      user == UserRole.CLIENT ||
      user == UserRole.SUPER_ADMIN ||
      user == UserRole.SUB_ADMIN
    ) {
      try {
        let article = await ArticleDao.getArticleByArticleId(articleId, userId);

        if (!article) {
          return res.sendError("No article found for the provided article Id.");
        }

        return res.sendSuccess(article, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("User type cannot access this route.");
    }
  }

  export async function getArticleByIdPublic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const articleId = Types.ObjectId(req.params.id);

    try {
      let article = await ArticleDao.getArticleByArticleIdPublic(articleId);

      if (!article) {
        return res.sendError("No article found for the provided article Id.");
      }

      return res.sendSuccess(article, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function toggleLike(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const articleId = Types.ObjectId(req.params.id);
    const userRole = req.user.role;
    const userId = req.user._id;
    let updatedLikedBy: string[] = [];

    if (userRole == UserRole.THERAPIST || userRole == UserRole.CLIENT) {
      try {
        let article = await ArticleDao.getArticleById(articleId);

        if (!article) {
          return res.sendError("No article found for the provided article Id");
        }

        async function removeElement(array: string[], value: string) {
          return array.filter(function (element: string) {
            return element !== value;
          });
        }

        if (article.likedBy.length === 0) {
          updatedLikedBy = article.likedBy.concat([userId.toString()]);
        } else {
          for (let user of article.likedBy) {
            if (user.toString() === userId.toString()) {
              updatedLikedBy = await removeElement(
                article.likedBy,
                userId.toString()
              );
            } else {
              updatedLikedBy = article.likedBy.concat([userId.toString()]);
            }
          }
        }

        try {
          let likedUser = await ArticleDao.updateArticleField(
            articleId,
            updatedLikedBy
          );
          return res.sendSuccess(likedUser, "Success");
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("User type cannot access this route.");
    }
  }

  export async function addComment(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const articleId = Types.ObjectId(req.params.id);
    const userRole = req.user.role;
    const userId = req.user._id;
    const comment = req.body.comment;

    if (userRole == UserRole.THERAPIST || userRole == UserRole.CLIENT) {
      try {
        let article = await ArticleDao.getArticleById(articleId);

        if (!article) {
          return res.sendError("No article found for the provided article Id");
        }

        try {
          const commentDetails: any = {
            userId: userId,
            comment: comment,
            date: new Date(),
          };

          let addedComment = await ArticleDao.addComment(
            articleId,
            commentDetails
          );

          if (!addedComment) {
            return res.sendError("Comment could not be added");
          }

          return res.sendSuccess(addedComment, "Success");
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("User type cannot access this route.");
    }
  }

  export async function addReply(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userRole = req.user.role;
    const userId = req.user._id;
    const articleId = req.body.articleId;
    const commentId = req.body.commentId;
    const reply = req.body.reply;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!mongoose.Types.ObjectId.isValid(articleId)) {
      return res.sendError("Invalid article id.");
    }

    if (!mongoose.Types.ObjectId.isValid(commentId)) {
      return res.sendError("Invalid comment id.");
    }

    if (userRole == UserRole.THERAPIST || userRole == UserRole.CLIENT) {
      try {
        let article = await ArticleDao.getArticleById(
          Types.ObjectId(articleId)
        );

        if (!article) {
          return res.sendError("No article found for the provided article Id");
        }
        try {
          let updatedComment = await ArticleDao.addReply(
            articleId,
            commentId,
            reply,
            userId
          );

          if (!updatedComment) {
            return res.sendError("Comment could not be added");
          }

          return res.sendSuccess(updatedComment, "Success");
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("User type cannot access this route.");
    }
  }

  export async function searchArticlesByTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const searchTags = req.body.searchTags;
    const type = req.body.type;
    const userId = req.user._id;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    for (let id of searchTags) {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        return res.sendError("Invalid items in search tag array.");
      }
    }

    if (req.user.role == UserRole.THERAPIST) {
      try {
        const searchResult = await ArticleDao.searchArticlesByTags(
          searchTags,
          type,
          userId,
          limit,
          offset
        );
        const resultCount = await ArticleDao.searchArticlesByTagsCount(
          searchTags,
          type,
          userId
        );
        let count = resultCount - limit * offset;
        const resultList = {
          articleSet: searchResult,
          count: count,
        };
        return res.sendSuccess(resultList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("User type cannot access this route.");
    }
  }

  export async function searchArticlesByTagsAndHashTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const experienceTags = req.body.experienceTags;
    const hashTags = req.body.hashTags;
    const userId = req.user._id;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    for (let id of experienceTags) {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        return res.sendError("Invalid item in search tags.");
      }
    }

    if (req.user.role == UserRole.CLIENT) {
      try {
        const searchResult = await ArticleDao.searchArticlesByTagsAndHashTags(
          userId,
          experienceTags,
          hashTags,
          limit,
          offset
        );
        const resultCount =
          await ArticleDao.searchArticlesByTagsAndHashTagsCount(
            userId,
            experienceTags,
            hashTags
          );
        let count = resultCount - limit * offset;
        const resultList = {
          articleSet: searchResult,
          count: count,
        };
        return res.sendSuccess(resultList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("User type cannot access this route.");
    }
  }
  export async function searchPublicArticlesByTagsAndHashTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const experienceTags = req.body.experienceTags;
    const hashTags = req.body.hashTags;

    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    for (let id of experienceTags) {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        return res.sendError("Invalid item in search tags.");
      }
    }


    try {
      const searchResult = await ArticleDao.searchPublicArticlesByTagsAndHashTags(

        experienceTags,
        hashTags,
        limit,
        offset
      );
      const resultCount =
        await ArticleDao.searchPublicArticlesByTagsAndHashTagsCount(

          experienceTags,
          hashTags
        );
      let count = resultCount - limit * offset;
      const resultList = {
        articleSet: searchResult,
        count: count,
      };
      return res.sendSuccess(resultList, "Success");
    } catch (error) {
      return res.sendError(error);
    }

  }


  export async function getPopularPosts(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;

    if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
      try {
        let popularPostList = await ArticleDao.getPopularPosts();

        return res.sendSuccess(popularPostList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }
  export async function getPopularPublicPosts(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      let popularPostList = await ArticleDao.getPopularPosts();

      return res.sendSuccess(popularPostList, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateCommentReply(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const articleId = req.params.id;
    const comments = req.body.comment;

    if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
      try {
        let updatedList = await ArticleDao.updateComment(articleId, comments);

        return res.sendSuccess(updatedList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }
}
