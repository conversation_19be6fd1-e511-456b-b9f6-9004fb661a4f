import { NextFunction, Request, Response } from "express";
import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { ChatDao } from "../dao/chat-dao";
import { UploadDao } from "../dao/upload-dao";
import { ChatData } from "../models/chat-model";
import { AppLogger } from "../common/logging";
import { MessageData } from "../models/chat-message-model";
import { DUpload } from "../models/upload-model";
import { UserRole } from "../models/user-model";
import Upload from "../schemas/upload-schema";
import * as path from "path";
import User from "../schemas/user-schema";
import {
  check,
  param,
  ValidationChain,
  validationResult,
} from "express-validator";
import Message from "../schemas/message-schema";
import { SMSService } from "../sms/config";
import Chat from "../schemas/chat-schema";

const { htmlToText } = require("html-to-text");
const aes256 = require("aes256");
const encryptionKey = process.env.MESSAGE_ENCRYPTION_KEY;

export namespace ChatEp {
  // Get user chats - todo modify to get all previous chats
  export async function getAllUsers(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const getAllCreators = await ChatDao.getAllUsers(userId);
      let populatedData = await Upload.populate(getAllCreators, [
        {
          path: "profileImageId",
        },
        {
          path: "coverPhotoId",
        },
      ]);
      res.sendSuccess(populatedData, "Success");
    } catch (err) {
      return res.sendError("Something went wrong");
    }
  }

  // Create chat object as inner function
  export async function createChatInnerFunction(
    therpistId: string,
    clientId: string
  ) {
    try {
      const members = [
        mongoose.Types.ObjectId(therpistId),
        mongoose.Types.ObjectId(clientId),
      ];
      if (!members || members?.length < 2) {
        return "Please add atleast two members";
      }

      if (therpistId == clientId) {
        return "Sender and reciever must be different users";
      }

      const checkTherapistIdExist = await ChatDao.getUserDetails(
        mongoose.Types.ObjectId(therpistId)
      );
      const checkClientIdExist = await ChatDao.getUserDetails(
        mongoose.Types.ObjectId(clientId)
      );

      if (!checkTherapistIdExist) {
        return "Therapist not found";
      }

      if (checkTherapistIdExist?.role != UserRole.THERAPIST) {
        return "TherapistId and role is different";
      }

      if (!checkClientIdExist) {
        return "Client not found";
      }
      if (checkClientIdExist?.role != UserRole.CLIENT) {
        return "ClientId and role is different";
      }

      const checkChatExist = await ChatDao.findChatByMemberIds(members);
      if (checkChatExist) {
        await changeChatIsActiveStatusInnerFunction(checkChatExist._id, true);
        return "Chat already exist";
      }

      const chatData: ChatData = {
        isActive: true,
        unreadSMS: false,
        lastActiveTime: new Date(),
        members: members.map((id) => {
          return id;
        }),
        unreadMessage: null,
      };

      const createChat = await ChatDao.createChat(chatData);

      if (!createChat) {
        return "Creating chat failed";
      }
    } catch (err) {
      return "Something went wrong with chat creation";
    }
  }

  export function getAllUserChatsValidationRules() {
    return [
      check("searchableString")
        .notEmpty({ ignore_whitespace: true })
        .withMessage("Searchable String is required.")
        .isString()
        .withMessage("Invalid Searchable String."),
      check("limit")
        .notEmpty()
        .withMessage("Limit is required.")
        .isInt({ min: 1 })
        .withMessage("Limit must be a positive integer."),
      check("offset")
        .notEmpty()
        .withMessage("Offset is required.")
        .isInt({ min: 0 })
        .withMessage("Offset must be a non-negative integer."),
    ];
  }

  export async function getAllUserChats(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const limit = Number(req.body.limit);
      const offset = Number(req.body.offset);

      let searchedName: any = null;

      if (
        req.body.searchableString != null &&
        req.body.searchableString != ""
      ) {
        let searchableString = req.body.searchableString;
        let seacrhItem = searchableString.replace(/\s/g, "");
        searchedName = searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
      }

      const foundCreators = await Chat.find({
        members: userId,
        isActive: true,
      })
        .populate([
          {
            path: "members",
            match: { firstname: { $exists: true } },
            populate: {
              path: "photoId",
              select: {
                path: 1,
                url: 1
              },
            },
            select: {
              firstname: 1,
              lastname: 1,
              primaryPhone: 1,
              photoId: 1,
              email: 1
            },
          },
          {
            path: "unreadMessage.lastMessage",
          },
        ])
        .sort({ lastActiveTime: -1 }).lean();

      const chatWithFilterOne = foundCreators.filter((chat: any) => {
        return chat.members.some((member: any) => {
          return member && member.role && member.role === "CLIENT";
        });
      });

      const chatWithFilterTwo = chatWithFilterOne.filter((chat: any) => {
        return chat.members.some((member: any) => {
          return (
            userId &&
            member &&
            member._id &&
            member._id.toString() != userId.toString() &&
            (!searchedName ||
              (searchedName &&
                ((member.firstname && searchedName.test(member.firstname)) ||
                  (member.lastname && searchedName.test(member.lastname)))))
          );
        });
      });

      const finalChats = chatWithFilterTwo.slice(offset, offset + limit);

      res.sendSuccess(chatWithFilterTwo, "Success");
    } catch (err) {
      return res.sendError(err);
    }
  }

  // Admin get all user chats
  export async function adminGetAllUserChats(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const chats = await ChatDao.adminGetAllUserChats();
      res.sendSuccess(chats, "Success");
    } catch (err) {
      return res.sendError("Something went wrong");
    }
  }

  // Create new message
  export async function createMessage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const { chatId, senderId, text, unreadMessageObj, mentionedMessageId } =
      req.body;

    let tempUnreadMessageObj = null;

    try {
      if (!senderId || !text || !chatId) {
        return res.sendError("Please provide senderId, chatId and text.");
      }

      const checkCreatorIdExist = await ChatDao.getUserDetails(senderId);
      const checkMemberIdExist = await ChatDao.getUserDetails(chatId);

      if (!checkMemberIdExist) {
        return res.sendError("User not found");
      }

      if (!checkCreatorIdExist) {
        return res.sendError("Creator not found");
      }

      const members = [senderId, chatId];

      let checkChatExist = await ChatDao.findChatByMemberIds(members);

      if (!checkChatExist) {
        if (!members || members?.length < 2) {
          return res.sendError("Please add atleast two members");
        }

        if (senderId == chatId) {
          return res.sendError("Sender and reciever must be different users");
        }

        const chatData: ChatData = {
          isActive: true,
          unreadSMS: false,
          lastActiveTime: new Date(),
          members: members.map((id: string) => {
            return mongoose.Types.ObjectId(id);
          }),
          unreadMessage: null,
        };

        checkChatExist = await ChatDao.createChat(chatData);
      }

      let messageData: MessageData = {
        chatId: checkChatExist._id,
        senderId: mongoose.Types.ObjectId(senderId),
        text: text,
        AttachmentUploadId: null,
        mentionedMessage: mentionedMessageId || null,
        isActive: true,
        isMsgSeen: false,
      };

      const chats = await ChatDao.createMessage(messageData);

      const populateMentionedMessage = await Message.populate(chats, [
        { path: "mentionedMessage", model: "Message" },
      ]);

      const populateSenderId = await User.populate(populateMentionedMessage, [
        { path: "senderId", model: "User" },
        { path: "mentionedMessage.senderId", model: "User" },
      ]);

      const populateChats = await Upload.populate(populateSenderId, {
        path: "mentionedMessage.AttachmentUploadId",
      });

      if (unreadMessageObj) {
        tempUnreadMessageObj = {
          ...unreadMessageObj,
          unreadUserId: mongoose.Types.ObjectId(unreadMessageObj.unreadUserId),
          lastMessage: Types.ObjectId(chats._id),
          msgCount: unreadMessageObj?.msgCount + 1,
        };
      }
      const updateLastActiveTime = await ChatDao.updateLastActiveInChat(
        checkChatExist?._id,
        new Date(),
        tempUnreadMessageObj
      );
      return res.sendSuccess(populateChats, "Message is successfully sent!");
    } catch (err) {
      return res.sendError("Something went wrong");
    }
  }

  // Get all messages relavant to chat
  export async function getAllMeeagesByChatId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const { chatId } = req.params;
    const senderId = req.user._id;

    try {
      const checkMemberIdExist = await ChatDao.getUserDetails(
        mongoose.Types.ObjectId(chatId)
      );

      if (!checkMemberIdExist) {
        return res.sendError("User not found.");
      }

      const members = [senderId, checkMemberIdExist._id];

      const checkChatExist = await ChatDao.findChatByMemberIds(members);

      let chats: any[];

      if (checkChatExist) {
        chats = await ChatDao.getAllMessagesByChatId(
          checkChatExist._id,
          limit,
          offset
        );
      } else {
        chats = [];
      }

      res.sendSuccess(chats, "Success");
    } catch (err) {
      return res.sendError("Something went wrong.");
    }
  }

  export async function changeChatIsActiveStatusInnerFunction(
    chatId: string,
    status: boolean
  ) {
    try {
      await ChatDao.updateChatIsActiveStatus(chatId, status);
    } catch (err) {
      console.log(err);
    }
  }

  export async function changeChatIsActiveStatusInnerFunctionByUserIds(
    therpistId: mongoose.Types.ObjectId,
    clientId: mongoose.Types.ObjectId,
    status: boolean
  ) {
    try {
      await ChatDao.updateChatIsActiveStatusByUserIds(
        [therpistId, clientId],
        status
      );
    } catch (err) {
      console.log(err);
    }
  }

  export async function MarkAsReadMessagesByChatId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const { chatId } = req.params;

    try {
      const chats = await ChatDao.MarkAsReadMessagesByChatId(
        mongoose.Types.ObjectId(chatId)
      );

      res.sendSuccess(chats, "Success");
    } catch (err) {
      return res.sendError("Something went wrong");
    }
  }

  export async function deleteMessageByMessageId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const { messageId } = req.params;

    try {
      const chats = await ChatDao.deleteMessageByMessageId(
        mongoose.Types.ObjectId(messageId)
      );
      res.sendSuccess(chats, "Success");
    } catch (err) {
      return res.sendError("Something went wrong");
    }
  }

  export async function uploadAttachmentFileAndCreateMessage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.file) {
        const attachmentFileData: any = {
          attachFile: req.file,
        };

        if (attachmentFileData.attachFile) {
          const profileImageDetails: DUpload = {
            originalName: attachmentFileData.attachFile.originalname,
            name: attachmentFileData.attachFile.filename,
            type: attachmentFileData.attachFile.mimetype,
            path: attachmentFileData.attachFile.path,
            fileSize: attachmentFileData.attachFile.size,
            extension: path.extname(attachmentFileData.attachFile.originalname),
            category: "MESSAGE_ATTACHMENT",
          };

          let uploadAttachFile = await UploadDao.createUpload(
            profileImageDetails
          );

          if (!uploadAttachFile) {
            return res.sendError("Attachement Upload Failed");
          }

          if (!req.body.messageData) {
            return res.sendError("No message details provided");
          }

          const {
            chatId,
            senderId,
            text,
            unreadMessageObj,
            mentionedMessageId,
          } = JSON.parse(req.body.messageData);

          let tempUnreadMessageObj = null;

          const checkCreatorIdExist = await ChatDao.getUserDetails(senderId);
          const checkMemberIdExist = await ChatDao.getUserDetails(chatId);

          if (!checkMemberIdExist) {
            return res.sendError("User not found");
          }

          if (!checkCreatorIdExist) {
            return res.sendError("Creator not found");
          }

          const members = [senderId, chatId];

          let checkChatExist = await ChatDao.findChatByMemberIds(members);

          if (!checkChatExist) {
            if (!members || members?.length < 2) {
              return res.sendError("Please add atleast two members");
            }

            if (senderId == chatId) {
              return res.sendError(
                "Sender and reciever must be different users"
              );
            }

            const chatData: ChatData = {
              isActive: true,
              unreadSMS: false,
              lastActiveTime: new Date(),
              members: members.map((id: string) => {
                return mongoose.Types.ObjectId(id);
              }),
              unreadMessage: null,
            };

            checkChatExist = await ChatDao.createChat(chatData);
          }

          const messageData: MessageData = {
            chatId: checkChatExist._id,
            senderId: mongoose.Types.ObjectId(senderId),
            text: text,
            AttachmentUploadId: uploadAttachFile._id,
            mentionedMessage: mentionedMessageId || null,
            isActive: true,
            isMsgSeen: false,
          };

          if (!chatId || !senderId) {
            return res.sendError("Please provide senderId,chatId");
          }

          const chats = await ChatDao.createMessage(messageData);

          const populateMentionedMessage = await Message.populate(chats, [
            { path: "mentionedMessage", model: "Message" },
          ]);

          const populateChats = await Upload.populate(
            populateMentionedMessage,
            [
              {
                path: "AttachmentUploadId",
              },
              {
                path: "mentionedMessage.AttachmentUploadId",
              },
            ]
          );

          const populateSenderId = await User.populate(populateChats, [
            { path: "senderId", model: "User" },
            { path: "mentionedMessage.senderId", model: "User" },
          ]);

          if (unreadMessageObj) {
            tempUnreadMessageObj = {
              ...unreadMessageObj,
              lastMessage: chats._id,
              msgCount: unreadMessageObj?.msgCount + 1,
            };
          }

          const updateLastActiveTime = await ChatDao.updateLastActiveInChat(
            checkChatExist?._id,
            new Date(),
            tempUnreadMessageObj
          );

          return res.sendSuccess(populateSenderId, "Success");
        }
      } else {
        return res.sendError("No file found");
      }
    } catch (err) {
      return res.sendError("Something went wrong - " + err);
    }
  }

  export function searchAndBrowseChatsValidationRules(): ValidationChain[] {
    return [
      check("searchTag")
        .exists()
        .withMessage("searchTag is required")
        .isString()
        .withMessage("searchTag is not a String"),
    ];
  }

  // Search and browse chats
  export async function searchAndBrowseChats(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }

      const userId = req.user._id;
      const searchTag = req.body.searchTag;

      const getAllChatsUserParticipated = await ChatDao.searchChats(
        userId,
        searchTag
      );

      if (!getAllChatsUserParticipated) {
        return res.sendError("Fetching Chat List Failed");
      }

      let populatedData = await Upload.populate(getAllChatsUserParticipated, [
        {
          path: "members.profileImageId",
        },
      ]);
      return res.sendSuccess(populatedData, "Success");
    } catch (err) {
      return res.sendError(err);
    }
  }

  // Search and browse chats
  export async function searchAndBrowseChatsForAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }

      const searchTag = req.body.searchTag;

      const getAllChatsUserParticipated = await ChatDao.searchChatsForAdmin(
        searchTag
      );

      if (!getAllChatsUserParticipated) {
        return res.sendError("Fetching Chat List Failed");
      }

      let populatedData = await Upload.populate(getAllChatsUserParticipated, [
        {
          path: "members.profileImageId",
        },
      ]);
      return res.sendSuccess(populatedData, "Success");
    } catch (err) {
      return res.sendError(err);
    }
  }

  export async function getUserUnreadChatCount(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const { userId } = req.params;

    try {
      const chatsCount = await ChatDao.getUserUnreadChatCount(
        mongoose.Types.ObjectId(userId)
      );

      res.sendSuccess(chatsCount, "Success");
    } catch (err) {
      return res.sendError("Something went wrong");
    }
  }

  export async function getUserByNameInnerFunc(
    firstName: string,
    lastName: string
  ) {
    try {
      const chatsCount = await ChatDao.getUserByNameInnerFunc(
        firstName,
        lastName
      );
      return chatsCount;
    } catch (err) {
      return err;
    }
  }

  // Get all user chats CronJob SMS
  export async function sendSMSForUnreadUser() {
    try {
      AppLogger.info(`.:: Send Unread Message Reminder SMS Every 20 Minutes ::.`);
      const chats = await ChatDao.getAllUserChatsSendSMS();
      if(chats && chats.length > 0) {
        await Promise.all(chats.map(async function (a: any) {
          try {
            if (a?.unreadMessage?.unreadUserId?.primaryPhone) {
              if(
                a?.unreadMessage?.unreadUserId?.role == UserRole.CLIENT &&
                a?.unreadMessage?.lastMessage?.senderId?.role == UserRole.THERAPIST &&
                a?.unreadMessage?.lastMessage?.senderId?._id &&
                a?.unreadMessage?.unreadUserId?.primaryTherapist &&
                a?.unreadMessage?.unreadUserId?.primaryTherapist.toString() == a?.unreadMessage?.lastMessage?.senderId?._id.toString()
              ){
                AppLogger.info(
                  `.:: Did Not Send Unread Message Reminder SMS for -- chatId: ${a?._id ?? "Invalid chatId"} messageId: ${a?.unreadMessage?.lastMessage?._id ?? "Invalid messageId"} phoneNumber: ${a?.unreadMessage?.unreadUserId?.primaryPhone ?? "Invalid Phone Number"} - Primary therapist sent this message ::.`
                );
              } else {

                let finalMessageText = "";
                let isMediaMessage = false;
                if(a?.unreadMessage?.lastMessage?.text){
                  const messageTextDecrypted = await doDecrypt(
                    a?.unreadMessage?.lastMessage?.text
                  );
                  finalMessageText = htmlToTextFunction(
                    messageTextDecrypted
                  );
                  if (finalMessageText && finalMessageText.length > 21) {
                    finalMessageText = finalMessageText.substring(0, 20) + "...";
                  }
                }

                if(a?.unreadMessage?.lastMessage?.AttachmentUploadId){
                  isMediaMessage = true;
                }

                const finalMessageToSend = `You have received a new message from ${a?.unreadMessage?.lastMessage?.senderId?.firstname ?? "user"} ${a?.unreadMessage?.lastMessage?.senderId?.lastname ?? ""}. Please see the message below.

                ${(finalMessageText ?? "") + (isMediaMessage ? " < Sent Attachment ( Please Login to View ) >": "")} `;
                
                let smsSent = await SMSService.sendEventSMS(
                  finalMessageToSend,
                  a?.unreadMessage?.unreadUserId?.primaryPhone
                );
                if (smsSent) {
                  const chats = await ChatDao.MarkAsSemdSMSByChatId(
                    mongoose.Types.ObjectId(a?._id)
                  );
                  AppLogger.info(
                    `.:: Send Unread Message Reminder SMS for -- chatId: ${a?._id ?? "Invalid chatId"} messageId: ${a?.unreadMessage?.lastMessage?._id ?? "Invalid messageId"} phoneNumber: ${a?.unreadMessage?.unreadUserId?.primaryPhone ?? "Invalid Phone Number"} ::.`
                  );
                } else {
                  AppLogger.error(
                    `.:: Send Unread Message Reminder SMS for -- chatId: ${a?._id ?? "Invalid chatId"} messageId: ${a?.unreadMessage?.lastMessage?._id ?? "Invalid messageId"} phoneNumber: ${a?.unreadMessage?.unreadUserId?.primaryPhone ?? "Invalid Phone Number"} failed ::.`
                  );
                }
              }
            }
          } catch (error) {
            AppLogger.error(
              `.:: Send Unread Message Reminder SMS for -- chatId: ${a?._id ?? "Invalid id"} messageId: ${a?.unreadMessage?.lastMessage?._id ?? "Invalid messageId"} failed with error ${error ?? "No error recieved"} ::.`
            );
          }
        }));
      }
      
    } catch (error) {
      AppLogger.error(`.:: Send Unread Message Reminder SMS Every 20 Minutes Failed ::.`);
    }
  }

  export async function getAllUnreadMessagesFromUserId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.params.userId;
      const unreadMessages = await ChatDao.getAllUnreadMessagesFromUserId(
        mongoose.Types.ObjectId(userId)
      );
      return res.sendSuccess(unreadMessages, "Success");
    } catch (error) {
      return res.sendError("Something went wrong");
    }
  }

  export async function getAllUnreadChatsFromUserId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.params.userId;
      const chatList = await ChatDao.getAllUnreadChatsFromUserId(
        mongoose.Types.ObjectId(userId)
      );
      return res.sendSuccess(chatList, "Success");
    } catch (error) {
      console.log(error);

      return res.sendError("Something went wrong");
    }
  }

  export async function getChatByChatIdForDashboard(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.params.userId;
      const chatId = req.params.chatId;
      const chat = await ChatDao.getChatByChatIdForDashboard(
        mongoose.Types.ObjectId(userId),
        mongoose.Types.ObjectId(chatId)
      );
      return res.sendSuccess(chat, "Success");
    } catch (error) {
      return res.sendError("Something went wrong");
    }
  }

  export async function getUnreadMessageCountByChatId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.params.userId;
      const chatId = req.params.chatId;
      const messageCount = await ChatDao.getUnreadMessageCountByChatId(
        mongoose.Types.ObjectId(userId),
        mongoose.Types.ObjectId(chatId)
      );
      return res.sendSuccess(messageCount, "Success");
    } catch (error) {
      return res.sendError("Something went wrong");
    }
  }

  export async function updateMeetingTherapistTranscribeAllowed(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistTranscribeAllowed = req.body.therapistTranscribeAllowed;
    } catch (error) {
      return res.sendError("Something went wrong");
    }
  }

  async function doDecrypt(cipher: string) {
    try {
      if (cipher && cipher?.length > 0) {
        const decrypted = aes256.decrypt(encryptionKey, cipher);
        const regex = /<p>(.*?)<\/p>/;
        const withoutTags = decrypted.replace(regex, "$1");
        return withoutTags;
      }
      return "";
    } catch (error) {
      return "";
    }
  }

  function htmlToTextFunction(htmlTextMessage: string) {
    try {
      if (htmlTextMessage) {
        const finalText = htmlToText(htmlTextMessage, {
          wordwrap: 160,
        });
        return finalText;
      }
      return "";
    } catch (error) {
      return "";
    }
  }
}
