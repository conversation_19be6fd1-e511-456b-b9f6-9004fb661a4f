import { NextFunction, Request, Response } from "express";
import { UserRole } from "../models/user-model";
import { UserDao } from "../dao/user-dao";
import { AppLogger } from "../common/logging";
import User from "../schemas/user-schema";
import { getFormattedLastInteraction } from "../services/ringcentral-service";
import { TwilioService } from "../services/twilio-service";
import Meeting from "../schemas/meeting-schema";
import Appointment from "../schemas/appointment-schema";
import FriendRequest from "../schemas/friend-request-schema";
import { CallingStatus } from "../models/meeting-model";
import { AppointmentStatus } from "../models/appointment-model";
import { Types } from "mongoose";

export namespace MetricsEp {
    // Get aggregated metrics for a date range
    export async function getAggregatedMetrics(req: Request, res: Response, next: NextFunction) {
        try {
            const startDate = req.query.start_date as string;
            const endDate = req.query.end_date as string;

            // Validate input dates
            if (!startDate || !endDate) {
                return res.status(400).json({
                    success: false,
                    message: "Both start_date and end_date are required"
                });
            }

            // Parse dates
            const start = new Date(startDate + "T00:00:00.000Z");
            const end = new Date(endDate + "T23:59:59.999Z");

            if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid date format. Use YYYY-MM-DD."
                });
            }

            // Count new signups
            const signupsCount = await User.countDocuments({
                createdAt: { $gte: start, $lte: end }
            });

            // Count completed appointments
            const completedAppointmentsCount = await Appointment.countDocuments({
                start: { $gte: start, $lte: end },
                status: AppointmentStatus.COMPLETED
            });

            // Simulated additional metrics (replace with real logic as needed)
            const scheduledAppointments = completedAppointmentsCount + 200;
            const missedAppointments = scheduledAppointments - completedAppointmentsCount;
            const churnedClients = 15;
            const totalSessions = completedAppointmentsCount;
            const sessionsForNewClients = Math.floor(0.6 * completedAppointmentsCount);

            const aggregatedMetrics = {
                newSignups: signupsCount,
                scheduledAppointments: scheduledAppointments,
                completedAppointments: completedAppointmentsCount,
                missedAppointments: missedAppointments,
                churnedClients: churnedClients,
                totalSessions: totalSessions,
                sessionsForNewClients: sessionsForNewClients
            };

            return res.status(200).json({
                success: true,
                data: aggregatedMetrics
            });
        } catch (error) {
            AppLogger.error(`Error in getAggregatedMetrics: ${error}`);
            return res.status(500).json({
                success: false,
                message: "Internal server error"
            });
        }
    }

    // Generate week labels for each month in the date range
    function generateMonthlyWeekLabels(start: Date, end: Date): string[] {
        const labels: string[] = [];
        const monthAbbr = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const multipleYears = start.getFullYear() !== end.getFullYear();
        
        let current = new Date(start);
        while (current <= end) {
            const year = current.getFullYear();
            const month = current.getMonth();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            const monthLabels = new Set<string>();
            
            for (let day = 1; day <= daysInMonth; day++) {
                const week = Math.floor((day - 1) / 7) + 1;
                const label = multipleYears 
                    ? `${year} ${monthAbbr[month]} W${week}` 
                    : `${monthAbbr[month]} W${week}`;
                monthLabels.add(label);
            }
            
            labels.push(...Array.from(monthLabels));
            
            // Move to next month
            current.setMonth(current.getMonth() + 1);
            current.setDate(1);
        }
        
        return labels;
    }

    // Get clients data for a date range
    export async function getClientsData(req: Request, res: Response, next: NextFunction) {
        try {
            const startDate = req.query.start_date as string;
            const endDate = req.query.end_date as string;
            const stateFilter = req.query.stateFilter as string;
            const therapistFilter = req.query.therapistFilter as string;
            const statusFilter = req.query.statusFilter as string;
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 25;

            // Validate input dates
            if (!startDate || !endDate) {
                return res.status(400).json({
                    success: false,
                    message: "Both start_date and end_date are required"
                });
            }

            // Parse dates
            const start = new Date(startDate + "T00:00:00.000Z");
            const end = new Date(endDate + "T23:59:59.999Z");

            if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid date format. Use YYYY-MM-DD."
                });
            }

            // Build query filter
            const queryFilter: any = {
                role: UserRole.CLIENT,
                createdAt: { $gte: start, $lte: end }
            };
            
            if (stateFilter) {
                queryFilter.state = stateFilter;
            }

            // Get clients
            const skip = (page - 1) * limit;
            const clients = await User.find(queryFilter)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .lean();

            // Generate week labels
            const weekLabels = generateMonthlyWeekLabels(start, end);
            const currentDate = new Date();
            const clientsList = [];

            // Process each client
            for (const client of clients) {
                // Calculate weeks on platform
                const onboardDate = client.createdAt as Date;
                const weeksOnPlatform = onboardDate && onboardDate < currentDate
                    ? Math.floor((currentDate.getTime() - onboardDate.getTime()) / (7 * 24 * 60 * 60 * 1000))
                    : 0;

                // Get therapist info
                const friendRequest = await FriendRequest.findOne({
                    clientId: client._id,
                    status: "APPROVED"
                }).populate("therapistId", "firstname lastname").lean();

                let therapistName = "";
                if (friendRequest && friendRequest.therapistId) {
                    const therapist = friendRequest.therapistId as any;
                    therapistName = `${therapist.firstname || ""} ${therapist.lastname || ""}`.trim();
                }

                // If there's a therapist filter and it doesn't match, skip this client
                if (therapistFilter && therapistName.toLowerCase() !== therapistFilter.toLowerCase()) {
                    continue;
                }

                // Initialize weekly status
                const weeklyStatus: Record<string, string> = {};
                weekLabels.forEach(label => {
                    weeklyStatus[label] = "No Appointment";
                });

                // Get meetings for the client in this time range
                const meetings = await Meeting.find({
                    clientId: client._id,
                    createdAt: { $gte: start, $lte: end }
                }).lean();

                let completedCount = 0;
                let missedCount = 0;

                // Process each meeting
                for (const meeting of meetings) {
                    const meetingDate = meeting.createdAt as Date;
                    if (!meetingDate) continue;

                    const month = meetingDate.getMonth();
                    const day = meetingDate.getDate();
                    const week = Math.floor((day - 1) / 7) + 1;
                    const monthAbbr = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                    
                    const multipleYears = start.getFullYear() !== end.getFullYear();
                    const label = multipleYears 
                        ? `${meetingDate.getFullYear()} ${monthAbbr[month]} W${week}` 
                        : `${monthAbbr[month]} W${week}`;

                    if (meeting.callingStatus === CallingStatus.COMPLETED) {
                        weeklyStatus[label] = "Successful";
                        completedCount++;
                    } else if (meeting.callingStatus === CallingStatus.CANCELLED) {
                        weeklyStatus[label] = "Missed";
                        missedCount++;
                    } else if (meeting.callingStatus === "RESCHEDULED") { // Not in enum but possible value
                        weeklyStatus[label] = "Rescheduled";
                    }
                }

                // For weeks before the client's onboard date, mark as "Not Onboarded"
                if (onboardDate && onboardDate > start) {
                    const deltaWeeks = Math.floor((onboardDate.getTime() - start.getTime()) / (7 * 24 * 60 * 60 * 1000));
                    for (let i = 0; i < Math.min(deltaWeeks, weekLabels.length); i++) {
                        weeklyStatus[weekLabels[i]] = "Not Onboarded";
                    }
                }

                // If there's a status filter and no matching status exists, skip this client
                if (statusFilter && !Object.values(weeklyStatus).some(status => 
                    status.toLowerCase() === statusFilter.toLowerCase())) {
                    continue;
                }

                // Create client data object
                const clientData = {
                    id: client._id.toString(),
                    name: `${client.firstname || ""} ${client.lastname || ""}`.trim(),
                    phoneNumber: client.primaryPhone || "",
                    upcomingAppointment: null, // Would need to get from appointments collection
                    therapist: therapistName,
                    state: client.state || "",
                    dateOnboarded: onboardDate ? onboardDate.toISOString().split('T')[0] : "",
                    weeksOnPlatform: weeksOnPlatform,
                    sessionsCompleted: completedCount,
                    missedSessions: missedCount,
                    // Get last interaction dates dynamically from RingCentral API and Twilio
                    lastTimeInteractRingCentral: await getFormattedLastInteraction(client.primaryPhone) || null,
                    lastTimeInteractTwilo: await TwilioService.getLastInteractionTime(client.primaryPhone) || null,
                    weeklyStatus: weeklyStatus
                };

                clientsList.push(clientData);
            }

            return res.status(200).json({
                success: true,
                data: clientsList
            });
        } catch (error) {
            AppLogger.error(`Error in getClientsData: ${error}`);
            return res.status(500).json({
                success: false, 
                message: "Internal server error"
            });
        }
    }

    // Get all therapists
    export async function getTherapists(req: Request, res: Response, next: NextFunction) {
        try {
            const therapists = await User.find({ role: UserRole.THERAPIST })
                .select("_id firstname lastname")
                .lean();

            const formattedTherapists = therapists.map(t => ({
                id: t._id.toString(),
                name: `${t.firstname || ""} ${t.lastname || ""}`.trim()
            }));

            return res.status(200).json({
                success: true,
                data: formattedTherapists
            });
        } catch (error) {
            AppLogger.error(`Error in getTherapists: ${error}`);
            return res.status(500).json({
                success: false,
                message: "Internal server error"
            });
        }
    }

    // Get database info for debugging
    export async function getDbInfo(req: Request, res: Response, next: NextFunction) {
        try {
            const collections = await User.db.collection("system.namespaces").find().toArray();
            const dbCollections = collections.map(c => c.name.split('.')[1]).filter(Boolean);

            return res.status(200).json({
                success: true,
                data: {
                    databases: [User.db.name],
                    collections_in_db: dbCollections
                }
            });
        } catch (error) {
            AppLogger.error(`Error in getDbInfo: ${error}`);
            return res.status(500).json({
                success: false,
                message: "Internal server error"
            });
        }
    }
} 