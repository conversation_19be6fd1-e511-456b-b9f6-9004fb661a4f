import {NextFunction, Request, Response} from "express";
import {SaasTherapistDao} from "../dao/saas-therapist-dao";
import {DSaasTherapistTask} from "../models/saas-therapist-task-model";
import {AppLogger} from "../common/logging";
import {UserRole} from "../models/user-model";
import {AdminDao} from "../dao/admin-dao";
import {validationResult, check} from "express-validator";
import {UserDao} from "../dao/user-dao";
import {Types} from "mongoose";
import { TherapistDao } from "../dao/therapist-dao";

let mongoose = require("mongoose");
const moment = require("moment-timezone");

export namespace SaasTherapistEp {
    export function updatePersonalDetailsValidationRules() {
        return [
            check("firstname")
                .optional()
                .isString()
                .isLength({ min: 1, max: 50 })
                .withMessage("First name must be between 1 and 50 characters"),
            check("lastname")
                .optional()
                .isString()
                .isLength({ min: 1, max: 50 })
                .withMessage("Last name must be between 1 and 50 characters"),
            check("dateOfBirth")
                .optional()
                .isISO8601()
                .withMessage("Date of birth must be a valid date"),
            check("gender")
                .optional()
                .isString()
                .isIn(['Male', 'Female', 'Other'])
                .withMessage("Gender must be Male, Female, or Other"),
            check("primaryPhone")
                .optional()
                .isString()
                .matches(/^\+?[1-9]\d{1,14}$/)
                .withMessage("Primary phone must be a valid phone number"),
            check("streetAddress")
                .optional()
                .isString()
                .isLength({ max: 200 })
                .withMessage("Street address must not exceed 200 characters"),
            check("city")
                .optional()
                .isString()
                .isLength({ max: 100 })
                .withMessage("City must not exceed 100 characters"),
            check("state")
                .optional()
                .isString()
                .isLength({ max: 50 })
                .withMessage("State must not exceed 50 characters"),
            check("zipCode")
                .optional()
                .isString()
                .matches(/^\d{5}(-\d{4})?$/)
                .withMessage("Zip code must be a valid US zip code"),
            check("homePhone")
                .optional()
                .isString()
                .matches(/^\+?[1-9]\d{1,14}$/)
                .withMessage("Home phone must be a valid phone number")
        ];
    }
    export async function getAllClients(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = req.user._id;
        const limit = req.query.limit ? parseInt(req.query.limit as string) : undefined;
        const offset = req.query.offset ? parseInt(req.query.offset as string) : undefined;

        try {
            let clientList;
            if (limit && offset) {
                clientList = await SaasTherapistDao.getAllClients(
                    therapistId,
                    limit,
                    offset
                );
            } else {
                clientList = await SaasTherapistDao.getAllClients(therapistId);
            }

            return res.sendSuccess(clientList, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getPersonalDetails(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const clientId = new mongoose.Types.ObjectId(req.params.clientId);
        try {
            let personalInfo = await SaasTherapistDao.getPersonalDetails(
                clientId
            );

            return res.sendSuccess(personalInfo, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updatePersonalDetails(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const clientId = new mongoose.Types.ObjectId(req.params.clientId);
        try {
            // Check validation errors
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }
            // Extract only the fields that can be updated
            const allowedFields = [
                'firstname',
                'lastname',
                'dateOfBirth',
                'gender',
                'primaryPhone',
                'streetAddress',
                'city',
                'state',
                'zipCode',
                'homePhone'
            ];

            // Filter request body to only include allowed fields
            const updateData: any = {};
            allowedFields.forEach(field => {
                if (req.body[field] !== undefined) {
                    updateData[field] = req.body[field];
                }
            });

            // Convert dateOfBirth string to Date if provided
            if (updateData.dateOfBirth) {
                updateData.dateOfBirth = new Date(updateData.dateOfBirth);
            }

            const updatedPersonalInfo = await SaasTherapistDao.updatePersonalDetails(
                clientId,
                updateData
            );

            if (!updatedPersonalInfo) {
                return res.sendError("Client not found or update failed");
            }

            AppLogger.info(`Updated personal details for client Id: ${clientId}`);
            return res.sendSuccess(updatedPersonalInfo, "Personal details updated successfully");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getTodoDetails(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = req.user._id;
        try {
            let todoList = await SaasTherapistDao.getTodoDetails(
                therapistId
            );

            return res.sendSuccess(todoList, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getTaskDetails(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = req.user._id;
        const limit = parseInt(req.query.limit as string) || 10;
        const offset = parseInt(req.query.offset as string) || 0;

        try {
            const taskDetails = await SaasTherapistDao.getTaskDetails(therapistId, limit, offset);
            return res.sendSuccess(taskDetails, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function createTask(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const therapistId = new mongoose.Types.ObjectId(req.user._id);
            const {user, ...restOfBody} = req.body;

            const saasTherapistData: DSaasTherapistTask = {
                therapistId: therapistId,
                ...restOfBody,
            };

            const newTask = await SaasTherapistDao.createTask(saasTherapistData);

            if (!newTask) {
                return res.sendError("Error while creating Task");
            }

            AppLogger.info(`Created Task for therapist Id: ${newTask.therapistId}`);
            return res.sendSuccess(newTask, "Task is successfully created.");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateTaskById(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const _id = new mongoose.Types.ObjectId(req.body._id);
            const {user, ...restOfBody} = req.body;

            const taskData = {
                ...restOfBody,
            };
            console.log(taskData);
            console.log(_id);
            const updatedTask = await SaasTherapistDao.updateTaskById(_id, taskData);

            if (!updatedTask) {
                return res.sendError(`Error while updating task!`);
            }

            AppLogger.info(`Updated task for therapist Id: ${updatedTask.therapistId}`);
            return res.sendSuccess(updatedTask, `Task is successfully updated.`);
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function deleteTaskById(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const taskId = new mongoose.Types.ObjectId(req.params.taskId);

            const deleteTask = await SaasTherapistDao.deleteTaskById(taskId);

            if (!deleteTask) {
                return res.sendError("Error while deleting Task");
            }

            AppLogger.info(`Deleted Task for therapist Id: ${deleteTask.therapistId}`);
            return res.sendSuccess(deleteTask, "Task is successfully deleted.");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getInsuranceDetailsViaClientId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const clientId = new mongoose.Types.ObjectId(req.params.clientId);
        console.log('getInsuranceDetailsViaClientId', clientId);
        try {
            let insuranceDetails = await SaasTherapistDao.getInsuranceListByClientId(clientId);
            return res.sendSuccess(insuranceDetails, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function viewProfileByUserId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const role = req.user.role;
        const userId = req.params.userId;

        if (role === UserRole.THERAPIST) {
            try {
                let user = await AdminDao.viewProfileByUserId(userId);
                return res.sendSuccess(user, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function searchClientsByTherapist(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {

            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }

            const limit = Number(req.params.limit);
            const offset = Number(req.params.offset);
            const searchableString = req.body.searchableString;
            const gender = req.body.gender;
            const status = req.body.status;
            const isSubscription = req.body.isSubscription;
            const zipCode = req.body.zipCode;
            const appointmentStatus = req.body.appointmentStatus;
            const therapistId = req.user._id;
            const clientActiveStatus = req.body.clientActiveStatus;
            const insuranceActiveStatus = req.body.insuranceActiveStatus;
            const copaymentStatus = req.body.copaymentStatus;
            const userId = req.user._id;

            if (!userId) {
                return res.sendError(
                    "Invalid user"
                );
            }

            if (req.user.role === UserRole.THERAPIST) {
                const ownUser = await UserDao.getUserByUserId(req.user._id);
                if (!ownUser) {
                    return res.sendError("Invalid user");
                }
            }

            const result = await SaasTherapistDao.searchClientsByTherapist(
                searchableString,
                limit,
                offset,
                gender,
                status,
                isSubscription,
                zipCode,
                therapistId,
                clientActiveStatus,
                appointmentStatus,
                insuranceActiveStatus,
                copaymentStatus
            );
            return res.sendSuccess(result, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function switchDashboardVersion(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const user = await UserDao.getUserByUserId(req.user._id);

            if (!user) {
                return res.sendError("User not found for the provided ID.");
            }

            const isNewDashboard = user.isNewDashboard === undefined ? false : !user.isNewDashboard;

            const updatedTherapist = await TherapistDao.updateTherapist(req.user._id, {isNewDashboard});

            if (!updatedTherapist) {
                return res.sendError("Failed to switch requested dashboard. Please try again later.");
            }

            return res.sendSuccess({ isNewDashboard }, "Dashboard version updated successfully.");
        } catch (error) {
            return res.sendError("An error occurred while switching the dashboard.");
        }
    }

}