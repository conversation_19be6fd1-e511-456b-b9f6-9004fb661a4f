import { NextFunction, Request, Response } from "express";
import { ReferralEarningDao } from "../dao/referral-earning-dao";
import { UserRole } from "../models/user-model";
import { UserDao } from "../dao/user-dao";
import { RewardType } from "../models/referral-earning-model";
import { TransactionType } from "../models/transaction-model";
import { WithdrawalDao } from "../dao/withdrawal-dao";
import { DNotification, NotificationEvent } from "../models/notification-model";
import { NotificationDao } from "../dao/notification-dao";
import { EmailService } from "../mail/config";
import { SMSService } from "../sms/config";
import { AppLogger } from "../common/logging";
import { validationResult } from "express-validator";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import ReferralEarning from "../schemas/referral-earning-schema";
import Therapist from "../schemas/therapist-schema";
import TherapistReferral from "../schemas/therapist-referral-schema";
import { StringOrObjectId } from "../common/util";
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

export namespace ReferralEarningEp {

  export async function getAllReferralEarnings(req: Request, res: Response, next: NextFunction) {
    try {
      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);
      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
        if(req.user.role == UserRole.SUB_ADMIN){
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if(ownUser.adminPermission.referralEarnings != true){
            return res.sendError(
              "You don't have permission for view referral earnings!"
            );
          }
        }
        const earnings = await ReferralEarningDao.getReferralEarningsWithoutFilter(limit, offset);
        let approvedTherapistRewardsCount = await ReferralEarningDao.getAllApprovedTherapistRewardsCount();

        const referralEarnings: any = {
          therapistRewardsList: earnings,
          count: approvedTherapistRewardsCount
        };

        return res.sendSuccess(referralEarnings, "Referral earning details.");
      } else {
        return res.sendError("Invalid user role!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function approveOrDeclineReferralEarningRecord(req: Request, res: Response, next: NextFunction) {
    try {
      const referralEarningId = req.body.referralEarningId;
      const verifiedStatus = req.body.verifiedStatus;
      if (req.user.role == UserRole.SUB_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.referralEarnings != true) {
            return res.sendError(
              "You don't have permission for modify referral earning data!"
            );
          }
        }

        let isReferralEarning: any = await ReferralEarningDao.getReferralEarningById(referralEarningId);

        if (isReferralEarning == null) {
          return res.sendError("No referral earning record found.");
        }

        try {
          const data = {
            verifiedStatus: verifiedStatus
          }
          const referralEarning = await ReferralEarningDao.updateReferralEarningById(referralEarningId, data);
          return res.sendSuccess(referralEarning, `Referral earning record ${verifiedStatus}!`);
        } catch (error) {
          return res.sendError(error);
        }
      } else {
        return res.sendError("Invalid user role!");
      }

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteReferralEarningRecord(req: Request, res: Response, next: NextFunction) {
    try {
      const referralEarningId = req.params.referralEarningId;
      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.referralEarnings != true) {
            return res.sendError(
              "You don't have permission for delete referral earning data!"
            );
          }
        }
        let referralEarning: any = await ReferralEarningDao.getReferralEarningById(referralEarningId);

        if (referralEarning == null) {
          return res.sendError("No referral earning record found!");
        }

        try {
          let deletedReferralEarning = await ReferralEarningDao.deleteReferralEarningById(referralEarningId);

          return res.sendSuccess(deletedReferralEarning, "Referral earning record DELETED!");
        } catch (error) {
          return res.sendError(error);
        }
      } else {
        return res.sendError("Invalid user role!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  // Single Payment - Referral Earning - Admin
  export async function transferReferralEarningPaymentToTherapist(req: Request, res: Response, next: NextFunction) {
    AppLogger.info(`.:: Single payment for referral reward process started. ` + new Date() + " ::.");
    try {
      const therapistId = req.body.therapistId;
      const referralEarningId = req.body.referralEarningId

      if (!therapistId) {
        return res.sendError("Invalid therapist id.");
      }
      if (!referralEarningId) {
        return res.sendError("Invalid referral Earning id.");
      }

      if (referralEarningId && referralEarningId?.length > 0) {
        let totalAmount: any = 0;
        
        const earningData = await ReferralEarningDao.getReferralEarningForPaymentById(referralEarningId, therapistId);

        if (earningData?.transactionAmount) {
          totalAmount = earningData.transactionAmount;
        }

        const therapist = await UserDao.getTherapistById(therapistId);
        if (therapist.stripeConnectedAccountId) {
          if (totalAmount > 0) {
            try {
              const paidData = await stripe.transfers.create({
                amount: Math.round(totalAmount * 100),
                currency: "usd",
                destination: therapist.stripeConnectedAccountId,
                description: "Lavni transfers Initiated To " + therapist.firstname + " " + therapist.lastname,
                transfer_group: "ORDER_BALANCE_" + totalAmount,
              });
              if (paidData) {
                let withdrawalReferralEarningCreated = await ReferralEarningDao.withdrawReferralEarning(
                  therapist._id,
                  totalAmount.toString(),
                  TransactionType.REFERRAL
                );

                if (withdrawalReferralEarningCreated) {
                  const data = await WithdrawalDao.createWithdrawal({
                    therapistId: therapist._id,
                    withdrawnAmount: totalAmount,
                  });

                  const notificationDetails: DNotification = {
                    senderId: req.body._id,
                    receiverId: therapist._id,
                    event: NotificationEvent.TRANSFER,
                    link: process.env.APP_URL + "/earnings",
                    content: "Your referral reward transfers initiated. Date: " + new Date(),
                    variant: "info",
                    readStatus: false,
                  };

                  await NotificationDao.createNotification(notificationDetails);

                  await EmailService.monthlyWithdrawalInitiated("Your referral reward transfers initiated.", therapist.email, therapist.firstname);

                  await SMSService.sendEventSMS(
                    `Hi ${therapist.firstname}! Your referral reward transfers are initiated. Login to the dashboard to check more details.`,
                    therapist.primaryPhone
                  );

                  // start bank transfer
                  const externalAccounts = await stripe.accounts.listExternalAccounts(
                    therapist.stripeConnectedAccountId,
                    { object: 'bank_account' }
                  );

                  if (externalAccounts && externalAccounts.data[0] && externalAccounts.data[0].id) {
                    const bankAccountID = externalAccounts.data[0].id;

                    const payout = await stripe.payouts.create({
                      amount: Math.round(totalAmount * 100),
                      currency: 'usd',
                      destination: bankAccountID,
                    }, {
                      stripeAccount: therapist.stripeConnectedAccountId,
                    });
                  }

                  // end bank transfer
                }
                const updatedReferralEarning = await ReferralEarningDao.updateReferralEarningList(referralEarningId);

                AppLogger.info(`.:: Single payment for referral reward process completed. ` + new Date() + " ::.");
                return res.sendSuccess(updatedReferralEarning);
              } else {
                return res.sendError("Referral Earning payment failed.");
              }
            } catch (error) {
              return res.sendError(error);
            }
          } else {
            return res.sendError("Total amount is 0.");
          }
        } else {
          return res.sendError("No stripe account id.");
        }

      } else {
        return res.sendError("Referral Earning id is required.");
      }
    } catch (error) {
      AppLogger.error(`.:: An error occurred while single payment for referral reward process. Date: ` + new Date(), error + " ::.");
      return res.sendError(error);
    }
  }

  interface BulkPaymentResult {
    success: boolean;
    error: string | null;
  }

  // Bulk Payment - Referral Earning - Admin
  export async function transferReferralBonusOfCurrentMonthAsBulk(req: Request, res: Response, next: NextFunction) {
    const bulkPaymentType = req.body.bulkPaymentType;
    AppLogger.info(`.:: Bulk payment for referral reward process started. Bulk Payment Type: ${bulkPaymentType}. ` + new Date() + " ::.");

    try {

      if (!bulkPaymentType) {
        return res.sendError("No bulk payment type provided.");
      }

      let referralEarnings;

      switch (bulkPaymentType) {
        case "APPROVED_ONLY":
          referralEarnings = await ReferralEarningDao.getApprovedReferralEarningsForCurrentMonth();
          break;
        case "APPROVED_OR_PENDING":
          referralEarnings = await ReferralEarningDao.getAllReferralEarningsForCurrentMonth();
          break;
        default:
          return res.sendError("Invalid bulk payment type.");
      }

      if (!referralEarnings || referralEarnings.length === 0) {
        return res.sendError("No referral earnings found for the current month.");
      }

      const bulkPayments = await Promise.all(referralEarnings.map(async (referralEarning) => {
        let result: BulkPaymentResult = { success: false, error: null };

        try {
          const totalAmount = referralEarning?.transactionAmount;
          const therapist = await UserDao.getTherapistById(referralEarning?.therapistId);

          if (!therapist.stripeConnectedAccountId) {
            result.error = "No stripe account id.";
            return result;
          }

          if (totalAmount <= 0) {
            result.error = "Total amount is 0 or negative.";
            return result;
          }

          const paidData = await stripe.transfers.create({
            amount: Math.round(totalAmount * 100),
            currency: "usd",
            destination: therapist.stripeConnectedAccountId,
            description: "Lavni transfers Initiated To " + therapist.firstname + " " + therapist.lastname,
            transfer_group: "ORDER_BALANCE_" + totalAmount,
          });

          if (!paidData) {
            result.error = "Referral Earning payment failed.";
            return result;
          }

          // Withdrawal processing
          const withdrawalReferralEarningCreated = await ReferralEarningDao.withdrawReferralEarning(
            therapist._id,
            totalAmount.toString(),
            TransactionType.REFERRAL
          );

          if (!withdrawalReferralEarningCreated) {
            result.error = "Failed to create withdrawal for referral earning.";
            return result;
          }

          // Create withdrawal record
          await WithdrawalDao.createWithdrawal({
            therapistId: therapist._id,
            withdrawnAmount: totalAmount,
          });

          // Send notification
          const notificationDetails: DNotification = {
            senderId: req.body._id,
            receiverId: therapist._id,
            event: NotificationEvent.TRANSFER,
            link: process.env.APP_URL + "/earnings",
            content: "Your referral bonus transfers initiated. Date: " + new Date(),
            variant: "info",
            readStatus: false,
          };

          await NotificationDao.createNotification(notificationDetails);
          await EmailService.monthlyWithdrawalInitiated("Your referral reward transfers initiated.", therapist.email, therapist.firstname);
          await SMSService.sendEventSMS(
            `Hi ${therapist.firstname}! Your referral reward transfers are initiated. Login to the dashboard to check more details.`,
            therapist.primaryPhone
          );

          // Bank transfer
          const externalAccounts = await stripe.accounts.listExternalAccounts(
            therapist.stripeConnectedAccountId,
            { object: 'bank_account' }
          );

          if (externalAccounts && externalAccounts.data[0] && externalAccounts.data[0].id) {
            const bankAccountID = externalAccounts.data[0].id;

            await stripe.payouts.create({
              amount: Math.round(totalAmount * 100),
              currency: 'usd',
              destination: bankAccountID,
            }, {
              stripeAccount: therapist.stripeConnectedAccountId,
            });
          }

          // Update referral earning status
          await ReferralEarningDao.updateReferralEarningList(referralEarning?._id);

          result.success = true;
        } catch (error) {
          result.error = error.message || error;
        }

        return result;
      }));

      const successfulPayments = bulkPayments.filter(p => p.success);
      const failedPayments = bulkPayments.filter(p => !p.success);
      const successed = successfulPayments.length;
      const failed = failedPayments.length;
      AppLogger.info(`.:: Bulk payment for referral reward process completed. Bulk Payment Type: ${bulkPaymentType}. Succeed Payments: ${successed}. Failed Payments: ${failed}. ` + new Date() + " ::.");
      return res.sendSuccess({},"Bulk payment for referral reward process completed.");

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getSingleTherapistRewardDetails(req: Request, res: Response, next: NextFunction) {
    const therapistId = req.user._id;
    const role = req.user.role;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (role == UserRole.THERAPIST) {
      try {
        let recentRewards = await ReferralEarningDao.getReferralEarningByTherapistId(therapistId);

        if (!recentRewards) {
          return res.sendError("Unable to get recent rewards.");
        }

        return res.sendSuccess(recentRewards, "Recent rewards of Therapist.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function addRewardAmountToTherapist() {
    try {
      const bonusAmount = "50";
      
      const therapistsForFirst = await TreatmentHistory.aggregate([
        {
          $lookup: {
            from: 'therapistreferrals',
            localField: 'therapistId',
            foreignField: 'referredUserId',
            as: 'referralDetails'
          }
        },
        {
          $unwind: {
            path: '$referralDetails',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $match: {
            referralDetails: { $ne: null },
            'referralDetails.referrerUserId': { $ne: null }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'clientId',
            foreignField: '_id',
            as: 'clientDetails'
          }
        },
        {
          $unwind: {
            path: '$clientDetails',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $match: {
            $or: [
              {
                $and: [
                  { claimStatus: { $exists: true } },
                  { claimStatus: 'PAID' }
                ]
              },
              {
                $and: [
                  { clientDetails: { $ne: null } },
                  { 'clientDetails.subscriptionStatus': { $ne: null } },
                  { 'clientDetails.subscriptionStatus': { $eq: 'active' } }
                ]
              }
            ]
          }
        },
        {
          $group: {
            _id: '$therapistId',
            meetingCount: { $sum: 1 }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'therapist'
          }
        },
        {
          $unwind: {
            path: '$therapist',
            preserveNullAndEmptyArrays: false
          }
        },
        {
          $project: {
            _id: 0,
            userId: '$therapist._id',
            firstSessionBonus: '$therapist.firstSessionBonus',
            meetingCount: 1
          }
        },
        { $match: {
            $or: [
              { firstSessionBonus: { $exists: false } },
              { firstSessionBonus: null }
            ],
            meetingCount: { $gte: 4 }
          }
        },
      ]);

      if (therapistsForFirst) {
        await Promise.all(therapistsForFirst.map(async therapist => {
          if (therapist && therapist.userId && therapist.meetingCount && therapist.meetingCount >= 4) {
            
            if (!therapist.firstSessionBonus) {
              const existingReferralEarning = await ReferralEarning.findOne({
                therapistId: therapist.userId,
                type: TransactionType.REFERRAL,
                rewardType: RewardType.OWNFIRST
              });

              if (!existingReferralEarning) {
                let referralEarning = await ReferralEarningDao.createReferralEarningForRewards(
                  therapist.userId,
                  bonusAmount,
                  TransactionType.REFERRAL,
                  RewardType.OWNFIRST,
                  null
                );

                if (referralEarning && referralEarning._id) {
                  await Therapist.findByIdAndUpdate(
                    therapist.userId,
                    { $set: { firstSessionBonus: referralEarning._id } },
                    { useFindAndModify: false }
                  );
                }
              }
            }
            
            const referralRecord = await TherapistReferral.findOne({
              referredUserId: therapist.userId,
              $or: [
                { firstSessionCompletionBonusForReferrer: { $exists: false } },
                { firstSessionCompletionBonusForReferrer: null }
              ]
            });

            if (referralRecord && referralRecord._id && referralRecord.referrerUserId && !(referralRecord.firstSessionCompletionBonusForReferrer)) {
              
              const therapistId: StringOrObjectId = referralRecord.referrerUserId.toString();

              const existingReferralEarning = await ReferralEarning.findOne({
                therapistId: therapistId,
                type: TransactionType.REFERRAL,
                rewardType: RewardType.REFERFIRST,
                rewardReferralId: referralRecord._id
              });

              if (!existingReferralEarning) {
                let referralEarning = await ReferralEarningDao.createReferralEarningForRewards(
                  referralRecord.referrerUserId,
                  bonusAmount,
                  TransactionType.REFERRAL,
                  RewardType.REFERFIRST,
                  referralRecord._id
                );

                if (referralEarning && referralEarning._id) {
                  await TherapistReferral.findByIdAndUpdate(
                    referralRecord._id,
                    { $set: { firstSessionCompletionBonusForReferrer: referralEarning._id } },
                    { useFindAndModify: false }
                  );
                }
              }
            }

          }
        }));
      }

      const therapistsForSecond = await TreatmentHistory.aggregate([
        {
          $lookup: {
            from: 'therapistreferrals',
            localField: 'therapistId',
            foreignField: 'referredUserId',
            as: 'referralDetails'
          }
        },
        {
          $unwind: {
            path: '$referralDetails',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $match: {
            referralDetails: { $ne: null },
            'referralDetails.referrerUserId': { $ne: null }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'clientId',
            foreignField: '_id',
            as: 'clientDetails'
          }
        },
        {
          $unwind: {
            path: '$clientDetails',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $match: {
            $or: [
              {
                $and: [
                  { claimStatus: { $exists: true } },
                  { claimStatus: 'PAID' }
                ]
              },
              {
                $and: [
                  { clientDetails: { $ne: null } },
                  { 'clientDetails.subscriptionStatus': { $ne: null } },
                  { 'clientDetails.subscriptionStatus': { $eq: 'active' } }
                ]
              }
            ]
          }
        },
        {
          $group: {
            _id: '$therapistId',
            meetingCount: { $sum: 1 }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'therapist'
          }
        },
        {
          $unwind: {
            path: '$therapist',
            preserveNullAndEmptyArrays: false
          }
        },
        {
          $project: {
            _id: 0,
            userId: '$therapist._id',
            twentySessionsBonus: '$therapist.twentySessionsBonus',
            meetingCount: 1
          }
        },
        { $match: {
            $or: [
              { twentySessionsBonus: { $exists: false } },
              { twentySessionsBonus: null }
            ],
            meetingCount: { $gte: 20 }
          }
        },
      ]);

      if (therapistsForSecond) {
        await Promise.all(therapistsForSecond.map(async therapist => {
          if (therapist && therapist.userId && therapist.meetingCount && therapist.meetingCount >= 20) {
            
            if (!therapist.twentySessionsBonus) {
              const existingReferralEarning = await ReferralEarning.findOne({
                therapistId: therapist.userId,
                type: TransactionType.REFERRAL,
                rewardType: RewardType.OWNSECOND
              });

              if (!existingReferralEarning) {
                let referralEarning = await ReferralEarningDao.createReferralEarningForRewards(
                  therapist.userId,
                  bonusAmount,
                  TransactionType.REFERRAL,
                  RewardType.OWNSECOND,
                  null
                );

                if (referralEarning && referralEarning._id) {
                  await Therapist.findByIdAndUpdate(
                    therapist.userId,
                    { $set: { twentySessionsBonus: referralEarning._id } },
                    { useFindAndModify: false }
                  );
                }
              }
            }
            
            const referralRecord = await TherapistReferral.findOne({
              referredUserId: therapist.userId,
              $or: [
                { twentySessionsCompletionBonusForReferrer: { $exists: false } },
                { twentySessionsCompletionBonusForReferrer: null }
              ]
            });

            if (referralRecord && referralRecord._id && referralRecord.referrerUserId && !(referralRecord.twentySessionsCompletionBonusForReferrer)) {
              
              const therapistId: StringOrObjectId = referralRecord.referrerUserId.toString();

              const existingReferralEarning = await ReferralEarning.findOne({
                therapistId: therapistId,
                type: TransactionType.REFERRAL,
                rewardType: RewardType.REFERSECOND,
                rewardReferralId: referralRecord._id
              });

              if (!existingReferralEarning) {
                let referralEarning = await ReferralEarningDao.createReferralEarningForRewards(
                  referralRecord.referrerUserId,
                  bonusAmount,
                  TransactionType.REFERRAL,
                  RewardType.REFERSECOND,
                  referralRecord._id
                );

                if (referralEarning && referralEarning._id) {
                  await TherapistReferral.findByIdAndUpdate(
                    referralRecord._id,
                    { $set: { twentySessionsCompletionBonusForReferrer: referralEarning._id } },
                    { useFindAndModify: false }
                  );
                }
              }
            }

          }
        }));
      }
      
      AppLogger.info("Therapists rewards allocation process has been successfully completed.");

    } catch (error) {
        AppLogger.error(`An error occurred while allocating rewards to therapists. Error: ${error}.`);
    }
  }

  // export async function rewardAPI(req: Request, res: Response, next: NextFunction) {
  //   try {
  //     const bonusAmount = "50";

  //     const therapistsForFirst = await TreatmentHistory.aggregate([
  //       {
  //         $lookup: {
  //           from: 'therapistreferrals',
  //           localField: 'therapistId',
  //           foreignField: 'referredUserId',
  //           as: 'referralDetails'
  //         }
  //       },
  //       {
  //         $unwind: {
  //           path: '$referralDetails',
  //           preserveNullAndEmptyArrays: true
  //         }
  //       },
  //       {
  //         $match: {
  //           referralDetails: { $ne: null },
  //           'referralDetails.referrerUserId': { $ne: null }
  //         }
  //       },
  //       {
  //         $lookup: {
  //           from: 'users',
  //           localField: 'clientId',
  //           foreignField: '_id',
  //           as: 'clientDetails'
  //         }
  //       },
  //       {
  //         $unwind: {
  //           path: '$clientDetails',
  //           preserveNullAndEmptyArrays: true
  //         }
  //       },
  //       {
  //         $match: {
  //           $or: [
  //             {
  //               $and: [
  //                 { claimStatus: { $exists: true } },
  //                 { claimStatus: 'PAID' }
  //               ]
  //             },
  //             {
  //               $and: [
  //                 { clientDetails: { $ne: null } },
  //                 { 'clientDetails.subscriptionStatus': { $ne: null } },
  //                 { 'clientDetails.subscriptionStatus': { $eq: 'active' } }
  //               ]
  //             }
  //           ]
  //         }
  //       },
  //       {
  //         $group: {
  //           _id: '$therapistId',
  //           meetingCount: { $sum: 1 }
  //         }
  //       },
  //       {
  //         $lookup: {
  //           from: 'users',
  //           localField: '_id',
  //           foreignField: '_id',
  //           as: 'therapist'
  //         }
  //       },
  //       {
  //         $unwind: {
  //           path: '$therapist',
  //           preserveNullAndEmptyArrays: false
  //         }
  //       },
  //       {
  //         $project: {
  //           _id: 0,
  //           userId: '$therapist._id',
  //           firstSessionBonus: '$therapist.firstSessionBonus',
  //           meetingCount: 1
  //         }
  //       },
  //       { $match: {
  //           $or: [
  //             { firstSessionBonus: { $exists: false } },
  //             { firstSessionBonus: null }
  //           ],
  //           meetingCount: { $gte: 4 }
  //         }
  //       },
  //     ]);

  //     if (therapistsForFirst) {
  //       await Promise.all(therapistsForFirst.map(async therapist => {
  //         if (therapist && therapist.userId && therapist.meetingCount && therapist.meetingCount >= 4) {
            
  //           if (!therapist.firstSessionBonus) {
  //             const existingReferralEarning = await ReferralEarning.findOne({
  //               therapistId: therapist.userId,
  //               type: TransactionType.REFERRAL,
  //               rewardType: RewardType.OWNFIRST
  //             });

  //             if (!existingReferralEarning) {
  //               let referralEarning = await ReferralEarningDao.createReferralEarningForRewards(
  //                 therapist.userId,
  //                 bonusAmount,
  //                 TransactionType.REFERRAL,
  //                 RewardType.OWNFIRST,
  //                 null
  //               );

  //               if (referralEarning && referralEarning._id) {
  //                 await Therapist.findByIdAndUpdate(
  //                   therapist.userId,
  //                   { $set: { firstSessionBonus: referralEarning._id } },
  //                   { useFindAndModify: false }
  //                 );
  //               }
  //             }
  //           }
            
  //           const referralRecord = await TherapistReferral.findOne({
  //             referredUserId: therapist.userId,
  //             $or: [
  //               { firstSessionCompletionBonusForReferrer: { $exists: false } },
  //               { firstSessionCompletionBonusForReferrer: null }
  //             ]
  //           });

  //           if (referralRecord && referralRecord._id && referralRecord.referrerUserId && !(referralRecord.firstSessionCompletionBonusForReferrer)) {
              
  //             const therapistId: StringOrObjectId = referralRecord.referrerUserId.toString();

  //             const existingTransaction = await ReferralEarning.findOne({
  //               therapistId: therapistId,
  //               type: TransactionType.REFERRAL,
  //               rewardType: RewardType.REFERFIRST,
  //               rewardReferralId: referralRecord._id
  //             });

  //             if (!existingTransaction) {
  //               let referralEarning = await ReferralEarningDao.createReferralEarningForRewards(
  //                 referralRecord.referrerUserId,
  //                 bonusAmount,
  //                 TransactionType.REFERRAL,
  //                 RewardType.REFERFIRST,
  //                 referralRecord._id
  //               );

  //               if (referralEarning && referralEarning._id) {
  //                 await TherapistReferral.findByIdAndUpdate(
  //                   referralRecord._id,
  //                   { $set: { firstSessionCompletionBonusForReferrer: referralEarning._id } },
  //                   { useFindAndModify: false }
  //                 );
  //               }
  //             }
  //           }

  //         }
  //       }));
  //     }

  //     const therapistsForSecond = await TreatmentHistory.aggregate([
  //       {
  //         $lookup: {
  //           from: 'therapistreferrals',
  //           localField: 'therapistId',
  //           foreignField: 'referredUserId',
  //           as: 'referralDetails'
  //         }
  //       },
  //       {
  //         $unwind: {
  //           path: '$referralDetails',
  //           preserveNullAndEmptyArrays: true
  //         }
  //       },
  //       {
  //         $match: {
  //           referralDetails: { $ne: null },
  //           'referralDetails.referrerUserId': { $ne: null }
  //         }
  //       },
  //       {
  //         $lookup: {
  //           from: 'users',
  //           localField: 'clientId',
  //           foreignField: '_id',
  //           as: 'clientDetails'
  //         }
  //       },
  //       {
  //         $unwind: {
  //           path: '$clientDetails',
  //           preserveNullAndEmptyArrays: true
  //         }
  //       },
  //       {
  //         $match: {
  //           $or: [
  //             {
  //               $and: [
  //                 { claimStatus: { $exists: true } },
  //                 { claimStatus: 'PAID' }
  //               ]
  //             },
  //             {
  //               $and: [
  //                 { clientDetails: { $ne: null } },
  //                 { 'clientDetails.subscriptionStatus': { $ne: null } },
  //                 { 'clientDetails.subscriptionStatus': { $eq: 'active' } }
  //               ]
  //             }
  //           ]
  //         }
  //       },
  //       {
  //         $group: {
  //           _id: '$therapistId',
  //           meetingCount: { $sum: 1 }
  //         }
  //       },
  //       {
  //         $lookup: {
  //           from: 'users',
  //           localField: '_id',
  //           foreignField: '_id',
  //           as: 'therapist'
  //         }
  //       },
  //       {
  //         $unwind: {
  //           path: '$therapist',
  //           preserveNullAndEmptyArrays: false
  //         }
  //       },
  //       {
  //         $project: {
  //           _id: 0,
  //           userId: '$therapist._id',
  //           twentySessionsBonus: '$therapist.twentySessionsBonus',
  //           meetingCount: 1
  //         }
  //       },
  //       { $match: {
  //           $or: [
  //             { twentySessionsBonus: { $exists: false } },
  //             { twentySessionsBonus: null }
  //           ],
  //           meetingCount: { $gte: 20 }
  //         }
  //       },
  //     ]);

  //     if (therapistsForSecond) {
  //       await Promise.all(therapistsForSecond.map(async therapist => {
  //         if (therapist && therapist.userId && therapist.meetingCount && therapist.meetingCount >= 20) {
            
  //           if (!therapist.twentySessionsBonus) {
  //             const existingReferralEarning = await ReferralEarning.findOne({
  //               therapistId: therapist.userId,
  //               type: TransactionType.REFERRAL,
  //               rewardType: RewardType.OWNSECOND
  //             });

  //             if (!existingReferralEarning) {
  //               let referralEarning = await ReferralEarningDao.createReferralEarningForRewards(
  //                 therapist.userId,
  //                 bonusAmount,
  //                 TransactionType.REFERRAL,
  //                 RewardType.OWNSECOND,
  //                 null
  //               );

  //               if (referralEarning && referralEarning._id) {
  //                 await Therapist.findByIdAndUpdate(
  //                   therapist.userId,
  //                   { $set: { twentySessionsBonus: referralEarning._id } },
  //                   { useFindAndModify: false }
  //                 );
  //               }
  //             }
  //           }
            
  //           const referralRecord = await TherapistReferral.findOne({
  //             referredUserId: therapist.userId,
  //             $or: [
  //               { twentySessionsCompletionBonusForReferrer: { $exists: false } },
  //               { twentySessionsCompletionBonusForReferrer: null }
  //             ]
  //           });

  //           if (referralRecord && referralRecord._id && referralRecord.referrerUserId && !(referralRecord.twentySessionsCompletionBonusForReferrer)) {
              
  //             const therapistId: StringOrObjectId = referralRecord.referrerUserId.toString();

  //             const existingReferralEarning = await ReferralEarning.findOne({
  //               therapistId: therapistId,
  //               type: TransactionType.REFERRAL,
  //               rewardType: RewardType.REFERSECOND,
  //               rewardReferralId: referralRecord._id
  //             });

  //             if (!existingReferralEarning) {
  //               let referralEarning = await ReferralEarningDao.createReferralEarningForRewards(
  //                 referralRecord.referrerUserId,
  //                 bonusAmount,
  //                 TransactionType.REFERRAL,
  //                 RewardType.REFERSECOND,
  //                 referralRecord._id
  //               );

  //               if (referralEarning && referralEarning._id) {
  //                 await TherapistReferral.findByIdAndUpdate(
  //                   referralRecord._id,
  //                   { $set: { twentySessionsCompletionBonusForReferrer: referralEarning._id } },
  //                   { useFindAndModify: false }
  //                 );
  //               }
  //             }
  //           }

  //         }
  //       }));
  //     }

  //     AppLogger.info("Therapists rewards allocation process has been successfully completed - (Test API Route).");
  //     return res.sendSuccess({}, "Therapists rewards allocation process has been successfully completed.");

  //   } catch (error) {
  //     AppLogger.error(`An error occurred while allocating rewards to therapists - (Test API Route). Error: ${error}.);
  //     return res.sendError(error);
  //   }
  // }

}
