import { NextFunction, Request, Response } from "express";
import { GroupTherapyDao } from "../dao/group-therapy-dao";
import { UserRole } from "../models/user-model";

export namespace GroupTherapyEp {
  export async function getAllGroupTherapySessions(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (
      req.user.role == UserRole.CLIENT ||
      req.user.role == UserRole.THERAPIST
    ) {
      try {
        let groupTherapySessions =
          await GroupTherapyDao.getAllGroupTherapySessions();

        return res.sendSuccess(groupTherapySessions, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("User type cannot access this route.");
    }
  }
}
