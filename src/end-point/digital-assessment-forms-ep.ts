import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
let mongoose = require("mongoose");
import { DigitalAssessmentDao } from "../dao/digital-assessment-dao";
import DigitalAssessmentForms from "./../schemas/digital-assessment-form-schema";
import { AdminDao } from "../dao/admin-dao";
import {
  DDigitalAssessment,
  IDigitalAssessment,
} from "../models/digital-assessment-model";
import { validationResult } from "express-validator";
import MeetingSchema from "./../schemas/meeting-schema";
import Meeting from "./../schemas/meeting-schema";
import { VideoCallDao } from "../dao/videocall-dao";
import { AppLogger } from "../common/logging";
import DigitalAssessmentVersion from "../schemas/digital-assessment-form-version-schema";
import moment = require("moment");
import { MOCK_ASSESSMENT_DATA } from "../constants/mock-assessment-data";

export namespace DigitalAssessmentDetailsEp {
  export async function addDigitalAssessmentDetails(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }

      if (!mongoose.isValidObjectId(req.body.clientId)) {
        return res.sendError(
          "Digital Assessment client Id is not valid ObjectId"
        );
      }

      if (!mongoose.isValidObjectId(req.body.therapistId)) {
        return res.sendError(
          "Digital Assessment therapist Id is not valid ObjectId"
        );
      }
      console.log("call add data");
      const isAdmin = req.body.isAdmin;

      delete req.body.data._id;

      if (!isAdmin) {
        const response = await DigitalAssessmentDao.addDigitalAssessmentDetails(
          req.body.data as DDigitalAssessment
        );

        return res.sendSuccess(response);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateDigitalAssessmentForms(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    const data = req.body.data;
    const id = req.body._id;
    const isSendClientEmail = req.body.isSendClientEmail;

    if (!mongoose.isValidObjectId(id)) {
      return res.sendError("Digital Assessment form Id is not valid ObjectId");
    }
    try {
      const result = await DigitalAssessmentDao.UpdateDigitalAssessmentDetails(
        data,
        id,
        isSendClientEmail
      );
      return res.sendSuccess(result);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getDigitalAssessmentDetailsByClientAndTherapistID(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const clientId = req.params.clientId;
      const therapistId = req.params.therapistId;

      const result =
        await DigitalAssessmentDao.getDigitalAssessmentDetailsByClientIdAndTherapistId(
          clientId,
          therapistId
        );
      return res.sendSuccess(result);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getDigitalAssessmentDetailsByTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const clientId = req?.body?.clientId;
      const therapistId = req?.user?._id;
      const result =
        await DigitalAssessmentDao.getSingleDigitalAssessmentDetailsByTherapist(
          clientId,
          therapistId
        );
      return res.sendSuccess(result);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getDigitalAssessmentDetailsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const assesmentId = req?.body?.assesmentId;
      const result =
        await DigitalAssessmentDao.getSingleDigitalAssessmentDetailsByAdmin(
          assesmentId
        );
      return res.sendSuccess(result);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getClientUsingDigitalAssessmentIdByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const assesmentId = req.body.assesmentId;

      if (!assesmentId || !mongoose.Types.ObjectId.isValid(assesmentId)) {
        return res.sendError("Invalid assesment Id");
      }

      let clinicalDetails = await DigitalAssessmentForms.findById(
        assesmentId
      ).select("clientId");

      if (!clinicalDetails) {
        return res.sendError(
          "There is no record matching the specified assessment id!"
        );
      }
      const client = await AdminDao.getClientById(clinicalDetails?.clientId);
      if (!client) {
        return res.sendError(
          "There is no record matching the specified client id!"
        );
      }
      return res.sendSuccess(client, "Client data.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getDigitalAssestmentSessionDataByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const assesmentId = req.body.assesmentId;

      if (!assesmentId || !mongoose.Types.ObjectId.isValid(assesmentId)) {
        return res.sendError("Invalid assesment Id");
      }

      let prevClinicalDetails = await DigitalAssessmentForms.findById(
        assesmentId
      );

      if (!prevClinicalDetails) {
        return res.sendError(
          "There is no record matching the specified assesment id!"
        );
      }

      const clientId = prevClinicalDetails?.clientId;
      const therapistId = prevClinicalDetails?.therapistId;

      let dateOfSession = "";

      if (!therapistId) {
        return res.sendError("Invalid therapist id ");
      }

      if (!clientId) {
        return res.sendError("Invalid client id ");
      }

      const completedFirstSession = await MeetingSchema.findOne({
        clientId: clientId,
        therapistId: therapistId,
        callingStatus: "COMPLETED",
      }).sort({ createdAt: 1 });

      if (completedFirstSession) {
        if (completedFirstSession?.regularMeetingDate) {
          dateOfSession = completedFirstSession?.regularMeetingDate.toString();
        } else {
          dateOfSession = completedFirstSession?.createdAt.toString();
        }
      }

      const digitalAssesmentVersion = await DigitalAssessmentVersion.find({ clientId:clientId, therapistId: therapistId }).sort({ createdAt: -1 });

      const today = moment().startOf("day");

      const isVersionCreatedToday = digitalAssesmentVersion.some((version) =>
        moment(version?.versionCreatedAt).isSame(today, "day")
      );

      const finalDataObject = {
        dateOfSession: dateOfSession,
        isVersionCreatedToday: isVersionCreatedToday,
      };

      return res.sendSuccess(finalDataObject, "Clinical assessment data.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getDigitalAssestmentSessionData(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const clientId = req?.body?.clientId;
      const therapistId = req?.user?._id;

      let dateOfSession = "";

      if (!therapistId) {
        return res.sendError("Invalid therapist id ");
      }

      if (!clientId || !mongoose.Types.ObjectId.isValid(clientId)) {
        return res.sendError("Invalid client id ");
      }

      const therapistIdString = req?.user?._id.toString();

      const completedFirstSession = await MeetingSchema.findOne({
        clientId: clientId,
        therapistId: therapistId,
        callingStatus: "COMPLETED",
      }).sort({ createdAt: 1 });

      if (completedFirstSession) {
        if (completedFirstSession?.regularMeetingDate) {
          dateOfSession = completedFirstSession?.regularMeetingDate.toString();
        } else {
          dateOfSession = completedFirstSession?.createdAt.toString();
        }
      }

      const digitalAssesmentVersion = await DigitalAssessmentVersion.find({ clientId:clientId, therapistId: therapistId }).sort({ createdAt: -1 });

      const today = moment().startOf("day");

      const isVersionCreatedToday = digitalAssesmentVersion.some((version) =>
        moment(version?.versionCreatedAt).isSame(today, "day")
      );

      // const ClinicalDetails = (assesmentId || !isCreatingNewForm) ? await ClinicalAssesment.findById(assesmentId) : null;
      // let ClinicalDetails = await ClinicalAssesmentDao.getClinicalAssesmentByClientId(clientId, therapistIdString);

      let finalDataObject = {};

      finalDataObject = {
        dateOfSession: dateOfSession,
        isVersionCreatedToday: isVersionCreatedToday,
      };

      return res.sendSuccess(finalDataObject, "Date Of Session.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateDigitalAssessmentFormsClientSignature(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const id = req.body.id;

      if (!id || !mongoose.Types.ObjectId.isValid(id)) {
        return res.sendError("Invalid client id ");
      }

      const signature = req.body.signature;
      const result =
        await DigitalAssessmentDao.updateDigitalAssessmentClientSignature(
          id,
          signature
        );

      return res.sendSuccess(result, "Date Of Session.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getPreviousAIGeneratedAssessment(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      let dataExtracted: any;


      console.log("meetingId", req.body.meetingId);
      console.log("clientId", req.body.clientId);

      if (!req.body.meetingId) {
        return res.sendError("Invalid meeting Id");
      }

      if (!req.body.clientId) {
        return res.sendError("Invalid client Id");
      }

      const meetingId = Types.ObjectId(req.body.meetingId);
      const clientId = Types.ObjectId(req.body.clientId);

      const therapistId = req.user._id;

      if (!mongoose.Types.ObjectId.isValid(meetingId)) {
        return res.sendError("Invalid meeting Id");
      }

      if (!mongoose.Types.ObjectId.isValid(clientId)) {
        return res.sendError("Invalid client Id");
      }

      const aiGeneratedData =
        await DigitalAssessmentDao.getPreviousAIGeneratedAssessmentByIds(
          meetingId,
          clientId,
          therapistId
        );

      if (aiGeneratedData) {
        dataExtracted = await DigitalAssessmentDao.processGeneratedData(
          aiGeneratedData?.aiResponse
        );
      }

      return res.sendSuccess(dataExtracted, "AI generated assessment data");
    } catch (error) {
      return res.sendError("Error:", error);
    }
  }

  export async function generateOpenAIAssessment(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const openAIAPIKey = process.env.OPEN_AI_API_KEY;
      const mongoConnectString = process.env.MONGOOSE_URI;
      const mongoDBName = process.env.MONGO_DB_NAME;

      const meetingId = req?.body?.meetingId;
      const therapistId = req?.user?._id;

      // Special case for specific therapist - return success immediately
      if (therapistId.toString() === "62f17eb1b81f31a68505d9ad") {
        try {
          const meetingOriginData = await Meeting.findOne({ meetingId: meetingId });

          if (meetingOriginData) {
            let updateResult = await DigitalAssessmentDao.updateDigitalAssessmentWithAIData(
              meetingOriginData.clientId,
              therapistId,
              MOCK_ASSESSMENT_DATA
            );
            console.log({updateResult})
          }
        } catch (updateError) {
          AppLogger.error(`Error updating digital assessment: ${updateError}`);
          // We don't return an error here because we still want to return the generated data
        }
        return res.sendSuccess(MOCK_ASSESSMENT_DATA, "Successfully assessment generated.");
      }
      
      if (!meetingId) {
        return res.sendError("Invalid meeting id");
      }

      if (!therapistId) {
        return res.sendError("Invalid user id");
      }

      

      const meetingOriginData = await Meeting.findOne({ meetingId: meetingId });

      if (!meetingOriginData) {
        return res.sendError("Invalid meeting id.");
      }

      const transcribeData = await VideoCallDao.getTranscribeByMeetingId(
        meetingOriginData?.meetingId
      );

      if (!transcribeData || transcribeData?.transcriptText.length == 0) {
        return res.sendError(
          "Unable to find transcribe data for this meeting.",
          404
        );
      }

      let dataExtracted: any;
      let updateResult: any;

      var spawn = require("child_process").spawn;

      let platform = process.platform === "win32" ? "python" : "python3";

      let process1 = spawn(
        platform,
        [
          "./src/python_script_assessment/Generate_assements.py",
          openAIAPIKey,
          meetingOriginData._id,
          mongoConnectString,
          mongoDBName,
        ],
        { shell: true }
      );

      process1.stdout.on("data", async function (data: any) {
        let processedText = data
          .toString()
          .replace(/(\r\n|\n|\r)/gm, "")
          .replace(/ :/g, ":")
          .replace(/\*/g, " ")
          .replace(/�/g, "'")
          .trim();

        await DigitalAssessmentDao.saveGeneratedAIResponseForAssessment(
          meetingOriginData._id,
          meetingOriginData.clientId,
          therapistId,
          processedText
        );

        dataExtracted = await DigitalAssessmentDao.processGeneratedData(
          processedText
        );


        try {
          updateResult = await DigitalAssessmentDao.updateDigitalAssessmentWithAIData(
            meetingOriginData.clientId,
            therapistId,
            dataExtracted
          );
          console.log({updateResult})
        } catch (updateError) {
          AppLogger.error(`Error updating digital assessment: ${updateError}`);
          // We don't return an error here because we still want to return the generated data
        }
      });

      process1.on("close", (code: any) => {
        if (code === 0) {
          if (dataExtracted) {
            return res.sendSuccess(
              dataExtracted,
              "Successfully assessment generated."
            );
          } else {
            return res.sendError(
              "Assessment generation failed due to the unavailability of the AI generated raw data."
            );
          }
        } else {
          return res.sendError(
            `Failed to generate assessment. Error code: ${code}`
          );
        }
      });

      process1.stderr.on("data", (data: any) => {
        AppLogger.error(`AI Generated Assessment Error: ${data}`);
        return res.sendError(`AI Generated Assessment Error: ${data}`);
      });
    } catch (error) {
      return res.sendError(`Failed to generate assessment. Details: ${error}`);
    }
  }
}
