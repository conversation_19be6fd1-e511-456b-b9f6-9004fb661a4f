import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
let mongoose = require("mongoose");
import multer = require("multer");
import path = require("path");
import { AdminDao } from "../dao/admin-dao";
import { UploadDao } from "../dao/upload-dao";
import { UserDao } from "../dao/user-dao";
import { DHashtag } from "../models/hashtag-model";
import { DProfession } from "../models/profession-model";
import {
  DProfessionLicense,
  IProfessionLicense,
} from "../models/profession-license-model";
import { DUpload, IUpload } from "../models/upload-model";
import { DUser, PayRateTypes, Permission, UserRole } from "../models/user-model";
import { UploadCategory } from "./user-ep";
import { ReportDao } from "../dao/report-user-dao";
import { ReviewStatus } from "../models/report-user-model";
import { check, validationResult } from "express-validator";
import { EducationDao } from "../dao/education-dao";
import { IEducation } from "../models/education-model";
import { LicenseDao } from "../dao/license-dao";
import { ILicense } from "../models/license-model";
import { StringOrObjectId } from "../common/util";
import fetch from "node-fetch";
import { AppointmentDao } from "../dao/appointment-dao";
import { FriendRequestDao } from "../dao/friend-request-dao";
import { GoalDao } from "../dao/goal-dao";
import { HomeworkDao } from "../dao/homework-dao";
import { VideoCallDao } from "../dao/videocall-dao";
import { NotificationDao } from "../dao/notification-dao";
import { TherapistDao } from "../dao/therapist-dao";
import { ArticleDao } from "../dao/article-dao";
import { ClientDao } from "../dao/client-dao";
import { DInvoice, PaymentStatus } from "../models/invoice-model";
import { InvoiceDao } from "../dao/invoice-dao";
import { EmailService } from "../mail/config";
import { DClient, IClient, PremiumStatus } from "../models/client-model";
import { FeedbackDao } from "../dao/feedback-dao";
import { TransactionsDao } from "../dao/transactions-dao";
import { WithdrawalDao } from "../dao/withdrawal-dao";
import { DNotification, NotificationEvent, NotificationVarient } from "../models/notification-model";
import { PdfService } from "../pdf/pdf-service";
import { SMSService } from "../sms/config";
import { AdminStatisticsDao } from "../dao/admin-statistics-dao";
import { AppLogger } from "../common/logging";
import { DTherapist, ITherapist, RegistrationApprovalStatus } from "../models/therapist-model";
import Insurance from "../schemas/insurance-schema";
import InsuranceCompany from "../schemas/Insurance-company-schema";
import Client from "../schemas/client-schema";
import { InsuranceDao } from "../dao/insurance-dao";
import { InsuranceEp } from "./insurance-ep";
import { ChatDao } from "../dao/chat-dao";
import { ITreatmentHistory } from "../models/treatment-history-model";
import { FriendRequestStatus, IFriendRequest } from "../models/friend-request-model";
import { REMINDER_SMS_TYPE } from "../models/reminder-sms-model";
import { AppointmentStatus, ApprovalStatus } from "../models/appointment-model";
import { CallingStatus } from "../models/meeting-model";
import { LavniReviewStatus } from "../models/lavni-review-model";
import { SmsChatEp } from "./sms-chat-ep";
import User from "../schemas/user-schema";
import Therapist from "../schemas/therapist-schema";
import FriendRequest from "../schemas/friend-request-schema";
import { FaxService } from "../fax/config";
import InsuranceDocApproval from "../schemas/insurance-doc-approval-schema";
import { SubmissionApprovalStatus } from "../models/insurance-doc-approval-model";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import ERAClaimMdList from "../schemas/era-claims-list-schema";
import Transaction from "../schemas/transaction-schema";
import { TransactionType } from "../models/transaction-model";
import { addDays, endOfMonth, isAfter, startOfMonth } from "date-fns";
const { ObjectId } = require('mongodb');

const moment = require('moment-timezone');

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
var fs = require("fs");
require("dotenv").config();
const sharp = require("sharp");
const uuid = require('uuid'); // Importing the UUID library
let jwt = require("jsonwebtoken");

// Generating a unique ID
const uniqueID = uuid.v4();

// State mapping for US states
const STATE_MAP: Record<string, string> = {
  "Alabama": "AL", "Alaska": "AK", "Arizona": "AZ", "Arkansas": "AR",
  "California": "CA", "Colorado": "CO", "Connecticut": "CT", "Delaware": "DE",
  "Florida": "FL", "Georgia": "GA", "Hawaii": "HI", "Idaho": "ID",
  "Illinois": "IL", "Indiana": "IN", "Iowa": "IA", "Kansas": "KS",
  "Kentucky": "KY", "Louisiana": "LA", "Maine": "ME", "Maryland": "MD",
  "Massachusetts": "MA", "Michigan": "MI", "Minnesota": "MN", "Mississippi": "MS",
  "Missouri": "MO", "Montana": "MT", "Nebraska": "NE", "Nevada": "NV",
  "New Hampshire": "NH", "New Jersey": "NJ", "New Mexico": "NM", "New York": "NY",
  "North Carolina": "NC", "North Dakota": "ND", "Ohio": "OH", "Oklahoma": "OK",
  "Oregon": "OR", "Pennsylvania": "PA", "Rhode Island": "RI", "South Carolina": "SC",
  "South Dakota": "SD", "Tennessee": "TN", "Texas": "TX", "Utah": "UT",
  "Vermont": "VT", "Virginia": "VA", "Washington": "WA", "West Virginia": "WV",
  "Wisconsin": "WI", "Wyoming": "WY"
};

// Helper function to convert full state name to 2-letter abbreviation
function convertState(fullState: string): string {
  if (!fullState) return fullState;
  return STATE_MAP[fullState.trim()] || fullState.trim();
}

export namespace AdminEp {
  export async function getAllClientInsuranceCompanies(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      // Get all clients with insurance
      const users = await UserDao.getAllUsers();
      console.log('Total users:', users.length);
      
      // Get clients with insurance
      const clients = await Client.find({
        role: UserRole.CLIENT,
        insuranceId: { $exists: true, $ne: null }
      }).select('insuranceId').lean();
      console.log('Clients with insurance:', clients.length);

      if (clients.length === 0) {
        return res.sendSuccess([], "No clients with insurance found");
      }

      // Get all insurances with populated insurance companies
      const insurances = await Insurance.find({
        _id: { $in: clients.map(client => client.insuranceId) }
      }).populate('insuranceCompanyId').lean();
      console.log('Found insurances:', insurances.length);

      // Extract unique insurance companies and sort by name
      const insuranceCompanies = [...new Set(insurances
        .filter(insurance => insurance.insuranceCompanyId)
        .map(insurance => JSON.stringify(insurance.insuranceCompanyId)))]
        .map(company => JSON.parse(company))
        .sort((a, b) => {
          const nameA = (a.insuranceCompany || '').toLowerCase().trim();
          const nameB = (b.insuranceCompany || '').toLowerCase().trim();
          return nameA.localeCompare(nameB);
        });

      console.log('Final unique companies:', insuranceCompanies.length);
      return res.sendSuccess(insuranceCompanies, "Client insurance companies retrieved successfully");
    } catch (error) {
      console.error('Error:', error);
      return res.sendError('Internal Server Error');
    }
  }

  export function addEthnicityValidationRules() {
    return [
      check("ethnicity")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Ethnicity is required!")
        .isAlpha("en-US", { ignore: " " })
        .withMessage("Ethnicity can only contain letters."),
    ];
  }

  export function updateEthnicityValidationRules() {
    return [
      check("updatedEthnicityName")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Ethnicity is required!")
        .isAlpha("en-US", { ignore: " " })
        .withMessage("Ethnicity can only contain letters."),
      check("ethnicityId")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Ethnicity ID is required!"),
    ];
  }

  export function addExpTagValidationRules() {
    return [
      check("ethnicity")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Ethnicity is required!")
        .isAlpha("en-US", { ignore: " " })
        .withMessage("Ethnicity can only contain letters."),
    ];
  }

  export function updateExpTagValidationRules() {
    return [
      check("updatedExpTag")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Experience tag is required!")
        .isAlpha("en-US", { ignore: " " })
        .withMessage("Experience tag can only contain letters."),
      check("expId")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Experience tag ID is required!"),
    ];
  }

  export function updateInsuranceCompanyValidationRules() {
    return [
      check("updatedInsuranceCompany")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Insurance Company is required!")
        .isAlpha("en-US", { ignore: " " })
        .withMessage("Insurance Company can only contain letters."),
      check("insuranceCompanyId")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Insurance Company ID is required!"),
    ];
  }

  export function updateHashTagValidationRules() {
    return [
      check("updatedHashTag")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Hash tag is required!"),
      // .isAlpha("en-US", { ignore: " " })
      // .withMessage("Hash tag can only contain letters."),
      check("hashTagId")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Hash tag ID is required!"),
    ];
  }

  export function chargePaymentAndSendEmailValidationRules() {
    return [
      check("clientId")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Organization name is required"),
      check("payAmount")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Paid amount is required"),
      check("paymentMonth")
        .not()
        .isEmpty()
        .isString()
        .withMessage("monthOfPayment is required"),
      check("duePayment")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Due Payment is required"),
    ];
  }

  export function addInsuranceCompanyValidationRules() {
    return [
      check("insuranceCompany")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Insurance Company is required!")
        .isAlpha("en-US", { ignore: " " })
        .withMessage("Insurance Company can only contain letters."),
    ];
  }

  export async function addEthnicity(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const ethnicity = req.body.ethnicity;
      try {
        const isFound = await AdminDao.getEthnicityByType(ethnicity);

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewEthnicity != true) {
            return res.sendError(
              "You don't have permission for Ethnicity!"
            );
          }
        }

        if (isFound) {
          return res.sendError(
            "Ethnicity type is already found in the system."
          );
        }
        const ethnicityType = await AdminDao.addEthnicity(ethnicity);
        if (!ethnicityType) {
          return res.sendError(
            "Something went wrong! Ethnicity type could not be created."
          );
        }
        return res.sendSuccess(ethnicityType, "Ethnicity type created.");
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteEthnicity(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const ethnicityId = req.params.deleteEthnicityId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewEthnicity != true) {
            return res.sendError(
              "You don't have permission for Ethnicity!"
            );
          }
        }

        let ethnicity: any = await AdminDao.getEthnicityById(ethnicityId);

        if (ethnicity == null) {
          return res.sendError("No ethnicity found for the Id.");
        }

        try {
          let deletedEthnicity = await AdminDao.deleteEthnicity(ethnicityId);

          return res.sendSuccess(deletedEthnicity, "Ethnicity deleted.");
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllEthnicityTypes(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    try {
      // if(req.user.role == UserRole.SUB_ADMIN){
      //   const ownUser = await UserDao.getUserByUserId(req.user._id);
      //   if(ownUser.adminPermission.viewEthnicity == true){
      //     return res.sendError(
      //       "You don't have permission for Ethnicity!"
      //     );
      //   }
      // }

      const ethnicityTypes = await AdminDao.getAllEthnicityTypes(limit, offset);

      const ethnicityCount = await AdminDao.getAllEthnicitysCount();

      const count = ethnicityCount - limit * offset;

      const data = {
        set: ethnicityTypes,
        count: count,
      };

      if (ethnicityTypes.length === 0) {
        return res.sendError(
          "Something went wrong! Could not load ethnicity types."
        );
      }

      return res.sendSuccess(data, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllEthnicityTypes_(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      // if(req.user.role == UserRole.SUB_ADMIN){
      //   const ownUser = await UserDao.getUserByUserId(req.user._id);
      //   if(ownUser.adminPermission.viewEthnicity != true){
      //     return res.sendError(
      //       "You don't have permission for Ethnicity!"
      //     );
      //   }
      // }

      const ethnicityTypes = await AdminDao.getAllEthnicityTypes_();

      if (ethnicityTypes.length === 0) {
        return res.sendError(
          "Something went wrong! Could not load ethnicity types."
        );
      }

      return res.sendSuccess(ethnicityTypes, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateEthnicity(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const ethnicityId = req.body.ethnicityId;
    const updatedEthnicityName = req.body.updatedEthnicityName;

    const errors = validationResult(req);

    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewEthnicity != true) {
        return res.sendError(
          "You don't have permission for Ethnicity!"
        );
      }
    }

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const isIdFound = await AdminDao.getEthnicityById(ethnicityId);

      if (isIdFound) {
        const isNameFound = await AdminDao.getEthnicityByType(
          updatedEthnicityName
        );

        if (isNameFound) {
          return res.sendError("Provided ethnicity already exists.");
        } else {
          const updatedEthnicity = await AdminDao.updateEthnicity(ethnicityId, {
            ethnicity: updatedEthnicityName,
          });

          return res.sendSuccess(updatedEthnicity, "Successfully updated.");
        }
      } else {
        return res.sendError("Ethnicity id not found.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function addExperienceTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const expTag = req.body.expTag;
      try {
        const isFound = await AdminDao.getExperienceTag(
          expTag.replace(/\s\s+/g, " ").trim()
        );

        if (isFound) {
          return res.sendError("Tag is already found in the system.");
        }

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewExperienceTags != true) {
            return res.sendError(
              "You don't have permission for Experience Tag!"
            );
          }
        }

        const expereinceTag = await AdminDao.addExperienceTag(expTag);

        if (!expereinceTag) {
          return res.sendError(
            "Something went wrong! Tag could not be created."
          );
        }
        return res.sendSuccess(expereinceTag, "Tag type created.");
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateExperienceTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const expId = req.body.expId;
    const updatedExpTag = req.body.updatedExpTag;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewExperienceTags != true) {
        return res.sendError(
          "You don't have permission for Experience Tag!"
        );
      }
    }

    try {
      const isIdFound = await AdminDao.getExperienceTagById(expId);

      if (isIdFound) {
        const isNameFound = await AdminDao.getExperienceTagsByName(
          updatedExpTag.replace(/\s\s+/g, " ").trim()
        );

        if (isNameFound) {
          return res.sendError("Provided experience tag already exists.");
        } else {
          const updatedTag = await AdminDao.updateExperienceTag(expId, {
            experienceTag: updatedExpTag,
          });

          return res.sendSuccess(updatedTag, "Successfully updated.");
        }
      } else {
        return res.sendError("Experience tag id not found.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteExperienceTag(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const experienceTagId = req.params.deleteExperienceTagId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        let experienceTag: any = await AdminDao.getExperienceTagById(
          experienceTagId
        );

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewExperienceTags != true) {
            return res.sendError(
              "You don't have permission for Experience Tag !"
            );
          }
        }

        if (experienceTag == null) {
          return res.sendError("No experience tag found for the Id.");
        }

        try {
          let deletedExperienceTag = await AdminDao.deleteExperienceTag(
            experienceTagId
          );

          return res.sendSuccess(
            deletedExperienceTag,
            "Experience Tag deleted."
          );
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllExperienceTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = req.params.limit;
    const offset = req.params.offset;

    if (
      req.user.role == UserRole.THERAPIST ||
      req.user.role == UserRole.CLIENT ||
      req.user.role == UserRole.SUPER_ADMIN ||
      req.user.role == UserRole.SUB_ADMIN
    ) {
      try {
        const expTags = await AdminDao.getAllExperienceTags(
          Number(limit),
          Number(offset)
        );

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewExperienceTags != true) {
            return res.sendError(
              "You don't have permission for Experience Tag!"
            );
          }
        }

        if (expTags.length === 0) {
          return res.sendError(
            "Something went wrong! Could not load experience tags."
          );
        }

        return res.sendSuccess(expTags, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }
  export async function getAllPublicExperienceTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = req.params.limit;
    const offset = req.params.offset;

    try {
      const expTags = await AdminDao.getAllExperienceTags(
        Number(limit),
        Number(offset)
      );

      if (expTags.length === 0) {
        return res.sendError(
          "Something went wrong! Could not load experience tags."
        );
      }

      return res.sendSuccess(expTags, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function addProfession(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const name = req.body.profession;
      const disabled = req.body.isDisabled;

      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewProfessions != true) {
            return res.sendError(
              "You don't have permission for View Profession!"
            );
          }
        }

        const isFound = await AdminDao.getProfessionByName(name);

        if (isFound) {
          return res.sendError("Profession is already found in the system.");
        }

        const newProfession: DProfession = {
          name: name,
          disabled: disabled,
        };

        const createdProfession = await AdminDao.addProfession(newProfession);

        if (!createdProfession) {
          return res.sendError(
            "Something went wrong! Profession could not be created."
          );
        }
        return res.sendSuccess(createdProfession, "Profession created.");
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateProfession(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const professionId = req.params.professionId;
      const name = req.body.profession;
      const disabled = req.body.disabled;

      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewProfessions != true) {
            return res.sendError(
              "You don't have permission for View Profession!"
            );
          }
        }

        const isFound = await AdminDao.getProfessionByName(name);

        if (isFound) {
          return res.sendError("Profession is already found in the system.");
        }

        const updatedProfession: DProfession = {
          name: name,
          disabled: disabled,
        };

        let profession = await AdminDao.updateProfession(
          professionId,
          updatedProfession
        );
        return res.sendSuccess(profession, "Profession updated!.");
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteProfession(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const professionId = req.params.deleteProfessionId;

    async function deleteProfessionLicenses(idArray: Types.ObjectId[]) {
      await Promise.all(
        idArray.map(async (id: Types.ObjectId) => {
          let isDeleted = await AdminDao.deleteProfessionLicense(id);
          if (!isDeleted) {
            return false;
          }
        })
      );
      return true;
    }

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewProfessions != true) {
            return res.sendError(
              "You don't have permission for View Profession!"
            );
          }
        }

        let profession: any = await AdminDao.getProfessionId(professionId);

        if (profession == null) {
          return res.sendError("No profession  found for the Id.");
        }
        let deletedProfession = await AdminDao.deleteProfession(professionId);
        if (deletedProfession) {
          let professionLicense: any =
            await AdminDao.getProfessionLicenseByProfessionId(professionId);

          if (professionLicense.length < 0) {
            return res.sendError("No profession license found for the Id.");
          }

          let deletingIds = professionLicense.map(
            (item: IProfessionLicense) => {
              return item._id;
            }
          );

          let hasDeleted = await deleteProfessionLicenses(deletingIds);

          if (!hasDeleted)
            return res.sendError(
              "Somethig went wrong while deleting profession!"
            );

          return res.sendSuccess(deletedProfession, "Profession deleted.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function addProfessionLicense(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.SUB_ADMIN) {
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if (ownUser.adminPermission.viewProfessions != true) {
          return res.sendError(
            "You don't have permission for View Profession!"
          );
        }
      }

      const name: string = req.body.subProfession;
      const professionId = req.body.professionId;

      try {
        const newSubProfession: DProfessionLicense = {
          name: name,
          professionId: professionId,
        };

        const createdProfession = await AdminDao.addProfessionLicense(
          newSubProfession
        );

        if (!createdProfession) {
          return res.sendError(
            "Something went wrong! Profession license could not be created."
          );
        }
        return res.sendSuccess(
          createdProfession,
          "Profession license created."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteProfessionLicense(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const professionLicenseId = req.params.deleteProfessionLicenseId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewProfessions != true) {
            return res.sendError(
              "You don't have permission for View Profession!"
            );
          }
        }

        let professionLicense: any = await AdminDao.getProfessionLicenseId(
          professionLicenseId
        );

        if (professionLicense == null) {
          return res.sendError("No profession license found for the Id.");
        }

        try {
          let deletedProfessionLicense = await AdminDao.deleteProfessionLicense(
            professionLicenseId
          );

          return res.sendSuccess(
            deletedProfessionLicense,
            "Profession license deleted."
          );
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllProfessions(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = req.params.limit;
    const offset = req.params.offset;

    if (
      req.user.role == UserRole.THERAPIST ||
      req.user.role == UserRole.CLIENT ||
      req.user.role == UserRole.SUPER_ADMIN ||
      req.user.role == UserRole.SUB_ADMIN
    ) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewProfessions != true) {
            return res.sendError(
              "You don't have permission for View Profession!"
            );
          }
        }

        const professionList = await AdminDao.getAllProfessions(
          Number(limit),
          Number(offset)
        );

        if (professionList.length === 0) {
          return res.sendError(
            "Something went wrong! Could not load professions."
          );
        }

        return res.sendSuccess(professionList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllProfessionsPublic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      // if(req.user.role == UserRole.SUB_ADMIN){
      //   const ownUser = await UserDao.getUserByUserId(req.user._id);
      //   if(ownUser.adminPermission.viewProfessions != true){
      //     return res.sendError(
      //       "You don't have permission for View Profession!"
      //     );
      //   }
      // }

      const professionList = await AdminDao.getAllProfessionsPublic();

      if (professionList.length === 0) {
        return res.sendError(
          "Something went wrong! Could not load professions."
        );
      }

      return res.sendSuccess(professionList, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getProfessionById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.SUB_ADMIN) {
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if (ownUser.adminPermission.viewProfessions != true) {
          return res.sendError(
            "You don't have permission for View Profession!"
          );
        }
      }

      const professionId = req.params.professionId;
      const profession = await AdminDao.getProfessionById(professionId);
      if (!profession) {
        return res.sendError(
          "Something went wrong! Could not load profession!."
        );
      }
      return res.sendSuccess(profession, "Success!");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllProfessionLicenses(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (
      req.user.role == UserRole.THERAPIST ||
      req.user.role == UserRole.CLIENT ||
      req.user.role == UserRole.SUPER_ADMIN ||
      req.user.role == UserRole.SUB_ADMIN
    ) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewProfessions != true) {
            return res.sendError(
              "You don't have permission for View Profession!"
            );
          }
        }

        const subProfessionList = await AdminDao.getAllProfessionLicens();

        if (subProfessionList.length === 0) {
          return res.sendError(
            "Something went wrong! Could not load professions license."
          );
        }

        return res.sendSuccess(subProfessionList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getProfessionLicenseById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.SUB_ADMIN) {
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if (ownUser.adminPermission.viewProfessions != true) {
          return res.sendError(
            "You don't have permission for View Profession!"
          );
        }
      }

      const professionLicenseId = req.params.professionLicenseId;
      const professionLicense = await AdminDao.getProfessionLicenseId(
        professionLicenseId
      );
      if (!professionLicense) {
        return res.sendError(
          "Something went wrong! Could not load profession license!."
        );
      }
      return res.sendSuccess(professionLicense, "Success!");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function addHashTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const name = req.body.hashTag;
      if (req.user.role == UserRole.SUB_ADMIN) {
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if (ownUser.adminPermission.viewHashTags != true) {
          return res.sendError(
            "You don't have permission for View Hash Tag!"
          );
        }
      }

      try {
        const isFound = await AdminDao.getHashTagByName(
          name.replace(/\s\s+/g, " ").trim()
        );

        if (isFound) {
          return res.sendError("Hashtag is already found in the system.");
        }

        const hashTag: DHashtag = {
          name: name,
        };

        const createdHashTag = await AdminDao.addHashtag(hashTag);

        if (!createdHashTag) {
          return res.sendError(
            "Something went wrong! Hashtag could not be created."
          );
        }
        return res.sendSuccess(createdHashTag, "Hashtag created.");
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateHashTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const hashTagId = req.body.hashTagId;
    const updatedHashTag = req.body.updatedHashTag;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      if (req.user.role == UserRole.SUB_ADMIN) {
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if (ownUser.adminPermission.viewHashTags != true) {
          return res.sendError(
            "You don't have permission for View Hash Tag!"
          );
        }
      }

      const isIdFound = await AdminDao.getHashTagById(hashTagId);

      if (isIdFound) {
        const isNameFound = await AdminDao.getHashTagByName(
          updatedHashTag.replace(/\s\s+/g, " ").trim()
        );

        if (isNameFound) {
          return res.sendError("Provided hash tag already exists.");
        } else {
          const updatedTag = await AdminDao.updateHashTag(hashTagId, {
            name: updatedHashTag,
          });

          return res.sendSuccess(updatedTag, "Successfully updated!");
        }
      } else {
        return res.sendError("Hash tag Id not found!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteHashTag(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const hashTagId = req.params.deleteHashTagId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewHashTags != true) {
            return res.sendError(
              "You don't have permission for View Hash Tag!"
            );
          }
        }

        let hashTag: any = await AdminDao.getHashTagById(hashTagId);

        if (hashTag == null) {
          return res.sendError("No hash tag found for the Id.");
        }

        try {
          let deletedHashTag = await AdminDao.deleteHashTag(hashTagId);

          return res.sendSuccess(deletedHashTag, "Hash Tag deleted.");
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllHashTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (
      req.user.role == UserRole.THERAPIST ||
      req.user.role == UserRole.CLIENT ||
      req.user.role == UserRole.SUPER_ADMIN ||
      req.user.role == UserRole.SUB_ADMIN
    ) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewHashTags != true) {
            return res.sendError(
              "You don't have permission for View Hash Tag!"
            );
          }
        }
        const hashTagList = await AdminDao.getAllHashTags(limit, offset);

        if (hashTagList.length === 0) {
          return res.sendError(
            "Something went wrong! Could not load hashtags."
          );
        }

        return res.sendSuccess(hashTagList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }
  export async function getAllPublicHashTags(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    try {
      // if(req.user.role == UserRole.SUB_ADMIN){
      //   const ownUser = await UserDao.getUserByUserId(req.user._id);
      //   if(ownUser.adminPermission.viewHashTags != true){
      //     return res.sendError(
      //       "You don't have permission for View Hash Tag!"
      //     );
      //   }
      // }

      const hashTagList = await AdminDao.getAllHashTags(limit, offset);

      if (hashTagList.length === 0) {
        return res.sendError("Something went wrong! Could not load hashtags.");
      }

      return res.sendSuccess(hashTagList, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllPendingClients(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    try {
      if (req.user.role == UserRole.SUB_ADMIN) {
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if (ownUser.adminPermission.manageClients != true) {
          return res.sendError(
            "You don't have permission for Manage Clients!"
          );
        }
      }
      const clientList = await AdminDao.getAllPendingClients(limit, offset);

      return res.sendSuccess(clientList, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllApprovedClients(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.manageClients != true) {
            return res.sendError(
              "You don't have permission for Manage Clients!"
            );
          }
        }
        const clientList = await AdminDao.getAllApprovedClients(limit, offset);

        return res.sendSuccess(clientList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function approveRejectClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        const clientId = req.body.userId;
        const status = req.body.status;

        if (!clientId) {
          return res.sendError("No userId given.");
        }

        const client = await UserDao.getUserById(clientId);

        if (!client || client.role !== UserRole.CLIENT) {
          return res.sendError("No client found with given userId.");
        }

        const updatedClient = await AdminDao.approveRejectClient(
          client._id,
          status
        );

        return res.sendSuccess(updatedClient, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllPendingTherapists(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.manageTherapists != true) {
            return res.sendError(
              "You don't have permission for Manage Therapists!"
            );
          }
        }
        const therapistList = await AdminDao.getAllPendingTherapists(
          limit,
          offset
        );

        return res.sendSuccess(therapistList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllApprovedTherapists(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.manageTherapists != true) {
            return res.sendError(
              "You don't have permission for Manage Therapists!"
            );
          }
        }
        const therapistList = await AdminDao.getAllApprovedTherapists(
          limit,
          offset
        );

        return res.sendSuccess(therapistList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function approveRejectTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        const therapistId = req.body.userId;
        const status = req.body.status;

        if (!therapistId) {
          return res.sendError("No userId given.");
        }

        const therapist = await UserDao.getUserById(therapistId);

        if (!therapist || therapist.role !== UserRole.THERAPIST) {
          return res.sendError("No therapist found with given userId.");
        }

        const updatedTherapist = await AdminDao.approveRejectTherapist(
          therapist._id,
          status
        );

        return res.sendSuccess(updatedTherapist, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function updateThemeImage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewThemeImage != true) {
        return res.sendError(
          "You don't have permission for View Theme Images!"
        );
      }
    }

    const uploadCategory = UploadCategory.THEMES;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateThemeImageValidationRules(req, cb);
      },
    });

    async function updateThemeImageValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) =>
              cb(error, "destination")
            );
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    const upload = multer({ storage: storage }).single("themeImage");

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + " ");
        } else {
          if (req.file == null || req.file === undefined) {
            return res.sendError("Image not found.");
          } else {
            const image = req.file;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            const data: DUpload = {
              originalName: image.originalname.replace(/ /g, ""),
              name: image.filename,
              type: image.mimetype,
              path: image.path,
              fileSize: image.size,
              extension: path.extname(image.originalname) || req.body.extension,
              category: uploadCategory,
              signRequired: signRequired,
            };

            let uploadedThemeImage: IUpload = await UploadDao.createUpload(
              data
            );

            if (uploadedThemeImage == null) {
              return res.sendError("Error while uploading the cover image.");
            }

            return res.sendSuccess(uploadedThemeImage, "Success");
          }
        }
      });
    } catch (error) {
      return res.sendError("Failed to upload image. " + error);
    }
  }

  export async function getAllClients(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    let limit = 0;
    let offset = 0;

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      limit = parseInt(req.params.limit);
      offset = parseInt(req.params.offset);
      try {
        const clients = await AdminDao.getAllClients(limit, offset);
        const clientCount = await AdminDao.getAllClientsCount();
        let count = clientCount - limit * offset;

        const data = {
          clientSet: clients,
          count: count,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllAdminUsers(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    let limit = 0;
    let offset = 0;

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      if (req.user.role == UserRole.SUB_ADMIN) {
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if (ownUser.adminPermission.statistics != true) {
          return res.sendError(
            "You don't have permission for Statistics!"
          );
        }
      }
      limit = parseInt(req.params.limit);
      offset = parseInt(req.params.offset);
      try {
        const adminUsers = await AdminDao.getAllAdminUsers(limit, offset);

        return res.sendSuccess(adminUsers, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllTherapists(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    let limit = 0;
    let offset = 0;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      limit = parseInt(req.params.limit);
      offset = parseInt(req.params.offset);
      try {
        const therapist = await AdminDao.getAllTherapists(limit, offset);
        const therapistCount = await AdminDao.getAllTherapistsCount();

        let count = therapistCount - limit * offset;

        const data = {
          therapistSet: therapist,
          count: count,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllTherapistsSimple(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.manageTherapists != true) {
            return res.sendError(
              "You don't have permission for Manage Therapists!"
            );
          }
        }

        const therapists = await AdminDao.getAllTherapistsSimple();
        return res.sendSuccess(therapists, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllTherapistsPublic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    let limit = 0;
    let offset = 0;
    limit = parseInt(req.params.limit);
    offset = parseInt(req.params.offset);
    try {
      const therapist = await AdminDao.getAllTherapists(limit, offset);
      const therapistCount = await AdminDao.getAllTherapistsCount();

      let count = therapistCount - limit * offset;

      const data = {
        therapistSet: therapist,
        count: count,
      };

      return res.sendSuccess(data, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function searchClientsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {

      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }

      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);
      const searchableString = req.body.searchableString;
      const gender = req.body.gender;
      const status = req.body.status;
      const isSubscription = req.body.isSubscription;
      const zipCode = req.body.zipCode;
      const appointmentStatus = req.body.appointmentStatus;
      const therapistId = req.body.therapistId;
      const clientActiveStatus = req.body.clientActiveStatus;

      const insuranceActiveStatus = req.body.insuranceActiveStatus;
      const copaymentStatus = req.body.copaymentStatus;
      const state = req.body.state;
      const insuranceCompanyId = req.body.insuranceCompanyId;
      const userId = req.user._id;

      if (!userId) {
        return res.sendError(
          "Invalid user"
        );
      }

      if (req.user.role == UserRole.SUB_ADMIN) {
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if (ownUser.adminPermission.newsLetterEmails != true && ownUser.adminPermission.viewAllClients != true && ownUser.adminPermission.createAppointmentAdmin != true) {
          return res.sendError(
            "You don't have permission!"
          );
        }
      }

      let freindIdListArray: Types.ObjectId[] = [];

      if (therapistId) {
        const therapist = await AdminDao.getUserById(therapistId);

        if (therapist?.friendRequests?.length > 0) {
          await Promise.all(therapist.friendRequests.map(id =>
            AdminDao.getFriendRequestById(mongoose.Types.ObjectId(id))
          )).then(results => {
            const clientIds = results.filter(item => item !== null && item?.status == "APPROVED").map(item => item.clientId);

            freindIdListArray = clientIds;
          })
        }
      }

      const result = await AdminDao.searchClientsByAdmin(
        searchableString,
        limit,
        offset,
        gender,
        status,
        isSubscription,
        zipCode,
        freindIdListArray,
        therapistId ? true : false,
        clientActiveStatus,
        appointmentStatus,
        insuranceActiveStatus,
        copaymentStatus,
        insuranceCompanyId,
        state,
        
      );

      const clientData = await AdminDao.getDataForClients(userId, result);

      const data = {
        userSet: clientData
      };

      return res.sendSuccess(data, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function searchMeetingsAndRecordingsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableText = req.body.searchableText;
    let userRole = req.body.role;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewMeetingsAndRecordings != true) {
        return res.sendError(
          "You don't have permission for Meetings & Recordings!"
        );
      }
    }

    if (userRole == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        const result = await AdminDao.searchMeetingsAndRecordingsByAdmin(
          limit,
          offset,
          searchableText
        );
        return res.sendSuccess(result, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function getAllClinicalNotesByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    let userRole = req.body.role;
    const searchableClient = req.body.searchableClient;
    const searchableTherapist = req.body.searchableTherapist;
    const claimDate = req.body.claimDate;
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (userRole == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.statistics != true) {
            return res.sendError(
              "You don't have permission for Statistics!"
            );
          }
        }
        const result = await AdminStatisticsDao.getTeatmentHistoryNotesByAdmin(
          limit,
          offset,
          searchableClient,
          searchableTherapist,
          claimDate
        );

        return res.sendSuccess(result, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function getAllPassDueNotesByTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    const Therapist = req.body.therapistId;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const data = await AdminStatisticsDao.getTreatmentHistoryByTherapistId(
        Therapist,
        limit,
        offset
      );

      return res.sendSuccess(data, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllClaimsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    let userRole = req.body.role;
    const searchableClient = req.body.searchableClient;
    const searchableTherapist = req.body.searchableTherapist;
    const claimStatus = req.body.claimStatus;
    const insuranceCompany = req.body.insuranceCompany;
    const claimDate = req.body.claimDate;
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (userRole == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.statistics != true) {
            return res.sendError(
              "You don't have permission for Statistics!"
            );
          }
        }
        const result = await AdminStatisticsDao.getTeatmentHistoryByAdmin(
          limit,
          offset,
          searchableClient,
          searchableTherapist,
          claimStatus,
          claimDate,
          insuranceCompany
        );

        return res.sendSuccess(result, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function searchTherapistsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const gender = req.body.gender;
    const status = req.body.status;
    const isSubscription = req.body.isSubscription;
    const zipCode = req.body.zipCode;
    const state = req.body.state;
    const therapistCategorizationByType = req.body.therapistCategorizationByType;
    const adminApproved = req.body.adminApproved;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.newsLetterEmails != true && ownUser.adminPermission.createAppointmentAdmin != true) {
            return res.sendError(
              "You don't have permission!"
            );
          }
        }

        const result = await AdminDao.searchTherapists(
          searchableString,
          limit,
          offset,
          gender,
          status,
          isSubscription,
          zipCode,
          state,
          therapistCategorizationByType,
          adminApproved
        );
        const countUser = await AdminDao.getAllTherapistsCount();

        const count = countUser - limit * offset;

        const data = {
          userSet: result,
          count: count,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }


  export async function searchClientsByAdminStatistic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const monthAndYear = req.body.monthAndYear;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.statistics != true) {
            return res.sendError(
              "You don't have permission for Statistics!"
            );
          }
        }
        const result = await AdminDao.searchClientsByAdminStatistic(
          searchableString,
          limit,
          offset,
          monthAndYear
        );

        const countUser = await AdminDao.getAllClientsCount();

        const count = countUser - limit * offset;

        const data = {
          userSet: result,
          count: count,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function searchTherapistsByAdminStatistic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const monthAndYear = req.body.monthAndYear;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.statistics != true) {
            return res.sendError(
              "You don't have permission for Statistics!"
            );
          }
        }
        const result = await AdminDao.searchTherapistsStatistic(
          searchableString,
          limit,
          offset,
          monthAndYear
        );

        const appointmentCounts =
          await AdminStatisticsDao.getPastAppointmentCountsForTherapists(
            result
          );

        const countUser = await AdminDao.getAllTherapistsCount();

        const count = countUser - limit * offset;

        const data = {
          userSet: appointmentCounts,
          count: count,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function getAllReportReviews(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.reportReviews != true) {
            return res.sendError(
              "You don't have permission for Report Reviews!"
            );
          }
        }

        const reportList = await ReportDao.getAllReportReviews(limit, offset);

        return res.sendSuccess(reportList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function viewUserProfileById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.params.userId;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        let user = await AdminDao.viewProfileByUserId(userId);
        return res.sendSuccess(user, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function updateFirstTimeLogin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const updateUser: any = {
        isLoginFirstTime: false
      };

      let updatedUser = await AdminDao.updateUser(req.user._id, updateUser);

      return res.sendSuccess(updatedUser, "User is updated successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateUserByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const data = req.body;
    let permissions;

    if (role == UserRole.SUPER_ADMIN) {
      try {
        if (data.role == UserRole.SUB_ADMIN) {
          permissions = {
            statistics: data?.adminPermissions?.includes(Permission.statistics) ?? false,
            adminDashboard: data?.adminPermissions?.includes(Permission.adminDashboard) ?? false,
            viewEthnicity: data?.adminPermissions?.includes(Permission.viewEthnicity) ?? false,
            viewExperienceTags: data?.adminPermissions?.includes(Permission.viewExperienceTags) ?? false,
            viewInsuranceCompanies: data?.adminPermissions?.includes(Permission.viewInsuranceCompanies) ?? false,
            createAppointmentAdmin: data?.adminPermissions?.includes(Permission.createAppointmentAdmin) ?? false,
            viewProfessions: data?.adminPermissions?.includes(Permission.viewProfessions) ?? false,
            documents: data?.adminPermissions?.includes(Permission.documents) ?? false,
            accessManagement: data?.adminPermissions?.includes(Permission.accessManagement) ?? false,
            viewHashTags: data?.adminPermissions?.includes(Permission.viewHashTags) ?? false,
            viewThemeImage: data?.adminPermissions?.includes(Permission.viewThemeImage) ?? false,
            reportReviews: data?.adminPermissions?.includes(Permission.reportReviews) ?? false,
            reviews: data?.adminPermissions?.includes(Permission.reviews) ?? false,
            contactUs: data?.adminPermissions?.includes(Permission.contactUs) ?? false,
            articles: data?.adminPermissions?.includes(Permission.articles) ?? false,
            feedback: data?.adminPermissions?.includes(Permission.feedback) ?? false,
            newsLetterEmails: data?.adminPermissions?.includes(Permission.newsLetterEmails) ?? false,
            marketingEmails: data?.adminPermissions?.includes(Permission.marketingEmails) ?? false,
            viewMeetingsAndRecordings: data?.adminPermissions?.includes(Permission.viewMeetingsAndRecordings) ?? false,
            viewAllClients: data?.adminPermissions?.includes(Permission.viewAllClients) ?? false,
            manageClients: data?.adminPermissions?.includes(Permission.manageClients) ?? false,
            premiumClients: data?.adminPermissions?.includes(Permission.premiumClients) ?? false,
            reminderSms: data?.adminPermissions?.includes(Permission.reminderSms) ?? false,
            viewAllTherapists: data?.adminPermissions?.includes(Permission.viewAllTherapists) ?? false,
            manageTherapists: data?.adminPermissions?.includes(Permission.manageTherapists) ?? false,
            viewTherapistReviews: data?.adminPermissions?.includes(Permission.viewTherapistReviews) ?? false,
            viewTherapistsSoapReviews: data?.adminPermissions?.includes(Permission.viewTherapistsSoapReviews) ?? false,
            educationalDetails: data?.adminPermissions?.includes(Permission.educationalDetails) ?? false,
            licenseDetails: data?.adminPermissions?.includes(Permission.licenseDetails) ?? false,
            therapistRequests: data?.adminPermissions?.includes(Permission.therapistRequests) ?? false,
            availableBalances: data?.adminPermissions?.includes(Permission.availableBalances) ?? false,
            adminApprovePayment: data?.adminPermissions?.includes(Permission.adminApprovePayment) ?? false,
            referralEarnings: data?.adminPermissions?.includes(Permission.referralEarnings) ?? false,
            clientRewards: data?.adminPermissions?.includes(Permission.clientRewards) ?? false,
            notifications: data?.adminPermissions?.includes(Permission.notifications) ?? false,
            sessionFeedback: data?.adminPermissions?.includes(Permission.sessionFeedback) ?? false,
            techTickets: data?.adminPermissions?.includes(Permission.techTickets) ?? false,
          };
        }

        const updateUser: any = {
          email: data.email,
          primaryPhone: data.primaryPhone,
          role: data.role,
          message: data.message,
          adminPermission: permissions
        };

        let updatedUser = await AdminDao.updateUser(data.userId, updateUser);

        return res.sendSuccess(updatedUser, "User is updated successfully.");
      } catch (error) {
        return res.sendError(error);
      }
    } else if (role == UserRole.SUB_ADMIN) {
      try {
        if (data.role == UserRole.SUB_ADMIN) {
          permissions = {
            statistics: data?.adminPermissions?.includes(Permission.statistics) ?? false,
            adminDashboard: data?.adminPermissions?.includes(Permission.adminDashboard) ?? false,
            viewEthnicity: data?.adminPermissions?.includes(Permission.viewEthnicity) ?? false,
            viewExperienceTags: data?.adminPermissions?.includes(Permission.viewExperienceTags) ?? false,
            viewInsuranceCompanies: data?.adminPermissions?.includes(Permission.viewInsuranceCompanies) ?? false,
            createAppointmentAdmin: data?.adminPermissions?.includes(Permission.createAppointmentAdmin) ?? false,
            viewProfessions: data?.adminPermissions?.includes(Permission.viewProfessions) ?? false,
            documents: data?.adminPermissions?.includes(Permission.documents) ?? false,
            accessManagement: data?.adminPermissions?.includes(Permission.accessManagement) ?? false,
            viewHashTags: data?.adminPermissions?.includes(Permission.viewHashTags) ?? false,
            viewThemeImage: data?.adminPermissions?.includes(Permission.viewThemeImage) ?? false,
            reportReviews: data?.adminPermissions?.includes(Permission.reportReviews) ?? false,
            reviews: data?.adminPermissions?.includes(Permission.reviews) ?? false,
            contactUs: data?.adminPermissions?.includes(Permission.contactUs) ?? false,
            articles: data?.adminPermissions?.includes(Permission.articles) ?? false,
            feedback: data?.adminPermissions?.includes(Permission.feedback) ?? false,
            newsLetterEmails: data?.adminPermissions?.includes(Permission.newsLetterEmails) ?? false,
            marketingEmails: data?.adminPermissions?.includes(Permission.marketingEmails) ?? false,
            viewMeetingsAndRecordings: data?.adminPermissions?.includes(Permission.viewMeetingsAndRecordings) ?? false,
            viewAllClients: data?.adminPermissions?.includes(Permission.viewAllClients) ?? false,
            manageClients: data?.adminPermissions?.includes(Permission.manageClients) ?? false,
            premiumClients: data?.adminPermissions?.includes(Permission.premiumClients) ?? false,
            reminderSms: data?.adminPermissions?.includes(Permission.reminderSms) ?? false,
            viewAllTherapists: data?.adminPermissions?.includes(Permission.viewAllTherapists) ?? false,
            manageTherapists: data?.adminPermissions?.includes(Permission.manageTherapists) ?? false,
            viewTherapistReviews: data?.adminPermissions?.includes(Permission.viewTherapistReviews) ?? false,
            viewTherapistsSoapReviews: data?.adminPermissions?.includes(Permission.viewTherapistsSoapReviews) ?? false,
            educationalDetails: data?.adminPermissions?.includes(Permission.educationalDetails) ?? false,
            licenseDetails: data?.adminPermissions?.includes(Permission.licenseDetails) ?? false,
            therapistRequests: data?.adminPermissions?.includes(Permission.therapistRequests) ?? false,
            availableBalances: data?.adminPermissions?.includes(Permission.availableBalances) ?? false,
            adminApprovePayment: data?.adminPermissions?.includes(Permission.adminApprovePayment) ?? false,
            referralEarnings: data?.adminPermissions?.includes(Permission.referralEarnings) ?? false,
            clientRewards: data?.adminPermissions?.includes(Permission.clientRewards) ?? false,
            notifications: data?.adminPermissions?.includes(Permission.notifications) ?? false,
            sessionFeedback: data?.adminPermissions?.includes(Permission.sessionFeedback) ?? false,
            techTickets: data?.adminPermissions?.includes(Permission.techTickets) ?? false,
          };
        }

        const updateUser: any = {
          email: data.email,
          primaryPhone: data.primaryPhone,
          role: data.role,
          message: data.message,
          adminPermission: permissions
        };

        let updatedUser = await AdminDao.updateUser(data.userId, updateUser);

        return res.sendSuccess(updatedUser, "User is updated successfully.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function blockUserByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.body.userId;
    const reportId = req.body.reportId;
    const reasonToBlock = req.body.reasonToBlock;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        let updatedUser = await AdminDao.updateUser(userId, {
          blockedByAdmin: true,
          reasonToBlock: reasonToBlock,
          priorityNumber: 0
        });

        if (reportId) {
          if (updatedUser) {
            await ReportDao.updateReport(reportId, {
              status: ReviewStatus.BLOCKED,
            });

            return res.sendSuccess("User blocked!", "Success");
          } else {
            return res.sendError("Error while blocking the user.");
          }
        }

        await EmailService.userStatusHasChangedBlock(
          "Temporary Suspension of Your Account",
          updatedUser.email,
          updatedUser.firstname,
          reasonToBlock,
          "blocked"
        );

        await SMSService.sendEventSMS(
          `Hi ${updatedUser.firstname}! Sorry to inform you that your Lavni account is blocked.`,
          updatedUser.primaryPhone
        );

        return res.sendSuccess("User blocked!", "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function unblockUserByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.body.userId;
    const reportId = req.body.reportId;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        let updatedUser = await AdminDao.updateUser(userId, {
          blockedByAdmin: false,
          reasonToBlock: null,
        });

        if (reportId) {
          if (updatedUser) {
            let updatedReview = await ReportDao.updateReport(reportId, {
              status: ReviewStatus.UNBLOCKED,
            });

            return res.sendSuccess(updatedReview, "Success");
          } else {
            return res.sendError("Error while blocking the user.");
          }
        }

        await EmailService.userStatusHasChanged(
          "Your Lavni account is unblocked.",
          updatedUser.email,
          updatedUser.firstname,
          "unblocked"
        );

        await SMSService.sendEventSMS(
          `Hi ${updatedUser.firstname}! Your Lavni account is unblocked.`,
          updatedUser.primaryPhone
        );

        return res.sendSuccess("User unblocked!", "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllContactUsRequests(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.contactUs != true) {
            return res.sendError(
              "You don't have permission for Contact Request!"
            );
          }
        }

        const contactUsRequestList = await AdminDao.getAllContactUsRequests(
          limit,
          offset
        );

        let contactsCount = await AdminDao.getAllContactCount();

        const contactList: any = {
          contactList: contactUsRequestList,
          count: contactsCount,
        };
        return res.sendSuccess(contactList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllPendingEducationalDetails(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.educationalDetails != true) {
            return res.sendError(
              "You don't have permission for Educational Details!"
            );
          }
        }
        const pendingEducationalDetails =
          await AdminDao.getAllPendingEducationalDetails(limit, offset);

        return res.sendSuccess(pendingEducationalDetails, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function getAllApprovedEducationalDetails(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.educationalDetails != true) {
            return res.sendError(
              "You don't have permission for Educational Details!"
            );
          }
        }
        const approvedEducationalDetails =
          await AdminDao.getAllApprovedEducationalDetails(limit, offset);

        return res.sendSuccess(approvedEducationalDetails, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function getAllPendingLicenseDetails(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const name = req.query.name as string || null;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.licenseDetails != true) {
            return res.sendError(
              "You don't have permission for Licenses / Permits!"
            );
          }
        }
        const pendingLicenseDetails =
          await AdminDao.getAllPendingLicenseDetails(limit, offset, name);

        return res.sendSuccess(pendingLicenseDetails, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function getAllApprovedLicenseDetails(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const name = req.query.name as string || null;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.licenseDetails != true) {
            return res.sendError(
              "You don't have permission for Licenses / Permits!"
            );
          }
        }
        const approvedLicenseDetails =
          await AdminDao.getAllApprovedLicenseDetails(limit, offset, name);

        return res.sendSuccess(approvedLicenseDetails, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function toggleEducationalDetailsReviewStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const educationDetailsId = req.params.educationDetailsId;
    let updatedDetails: IEducation = null;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        const data = await EducationDao.getEducationalDetailsById(
          Types.ObjectId(educationDetailsId)
        );

        if (data.reviewStatus === "PENDING") {
          updatedDetails = await AdminDao.updateEducationalDetails(
            educationDetailsId,
            { reviewStatus: "APPROVED" }
          );
        } else {
          updatedDetails = await AdminDao.updateEducationalDetails(
            educationDetailsId,
            { reviewStatus: "PENDING" }
          );
        }
        return res.sendSuccess(updatedDetails, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function toggleLicenseDetailsReviewStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const licenseId = req.params.licenseId;
    let updatedDetails: ILicense = null;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        const data = await LicenseDao.getLicenseDetailsById(
          Types.ObjectId(licenseId)
        );

        if (data.reviewStatus === "PENDING") {
          updatedDetails = await AdminDao.updateLicenseDetails(licenseId, {
            reviewStatus: "APPROVED",
          });
        } else {
          updatedDetails = await AdminDao.updateLicenseDetails(licenseId, {
            reviewStatus: "PENDING",
          });
        }
        return res.sendSuccess(updatedDetails, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllArticles(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.articles != true) {
            return res.sendError(
              "You don't have permission for Articles!"
            );
          }
        }
        const articeList = await AdminDao.getAllArticles(limit, offset);

        return res.sendSuccess(articeList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function deleteArticleById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const articleId = req.params.articleId;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.articles != true) {
            return res.sendError(
              "You don't have permission for Articles!"
            );
          }
        }

        await AdminDao.deleteArticleById(articleId);

        return res.sendSuccess("", "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function deleteThemeImage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const imageId = req.params.imageId;

    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewThemeImage != true) {
        return res.sendError(
          "You don't have permission for View Theme Images!"
        );
      }
    }

    async function deleteThemeImage(uploadId: StringOrObjectId) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldPhoto = await UploadDao.getUpload(uploadId.toString());
        await fs.unlink(oldPhoto.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }

      return isDeleted;
    }

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        let isFound = await UploadDao.getUpload(imageId);

        if (isFound) {
          let isDeleted = await deleteThemeImage(imageId);

          if (!isDeleted) {
            return res.sendError("Error while deleting the theme image.");
          }

          return res.sendSuccess("Image deleted!", "Success");
        } else {
          return res.sendError("No image found with the provided Id.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function deleteUser(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.params.userId;
    async function deleteUploads(uploadId: StringOrObjectId) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldPhoto = await UploadDao.getUpload(uploadId.toString());
        await fs.unlink(oldPhoto.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }

      return isDeleted;
    }

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        const user = await UserDao.getUserByUserId(userId);

        if (user) {
          if (user.role === "CLIENT") {
            await AppointmentDao.deleteAppointmentByClientId(
              Types.ObjectId(userId)
            );

            await FriendRequestDao.deleteAllRequestsByClientId(userId);
            await ChatDao.deleteAllChatsByClientId(userId);
            await GoalDao.deleteGoalsByClientId(userId);
            await HomeworkDao.deleteHomeworkByClientId(userId, deleteUploads);
            await VideoCallDao.deleteMeetingsByClientId(userId);
            await NotificationDao.deleteSentNotificationsByUserId(userId);
            await NotificationDao.deleteReceivedNotificationsByUserId(userId);
            await ReportDao.deleteReportedReviewsByUserId(userId);
            await ReportDao.deleteReportedByReviewsByUserId(userId);
            await TherapistDao.deleteTreatmentHistoryByClientId(userId);
            await ArticleDao.deleteRepliesByUserId(userId);
            await ArticleDao.deleteLikesByUserId(userId);
            await ArticleDao.deleteCommentsByUserId(userId);
            await VideoCallDao.deleteAllDiagnosisNotesByClientId(userId);
            await FeedbackDao.deleteAllFeedbackByUserId(userId);
            await VideoCallDao.deleteAllTranscribeNotesByClientId(userId);
            await InsuranceDao.deleteInsuranceDetailsByClientId(userId);
            await InvoiceDao.deleteInvoicesByClientId(userId);
            await VideoCallDao.deleteAllDiagnosisNotesByClientId(userId);
            await UserDao.deleteUserById(userId, deleteUploads);
          } else {
            await AppointmentDao.deleteAppointmentByTherapistId(userId);
            await FriendRequestDao.deleteAllRequestsByTherapistId(userId);
            await ChatDao.deleteAllChatsByTherapistId(userId);
            await GoalDao.deleteGoalsByTherapistId(userId);
            await HomeworkDao.deleteHomeworkByTherapistId(
              userId,
              deleteUploads
            );
            await VideoCallDao.deleteMeetingsByTherapistId(userId);
            await NotificationDao.deleteSentNotificationsByUserId(userId);
            await NotificationDao.deleteReceivedNotificationsByUserId(userId);
            await ReportDao.deleteReportedReviewsByUserId(userId);
            await ReportDao.deleteReportedByReviewsByUserId(userId);
            await TherapistDao.deleteTreatmentHistoryByTherapistId(userId);
            await ArticleDao.deleteRepliesByUserId(userId);
            await ArticleDao.deleteLikesByUserId(userId);
            await ArticleDao.deleteCommentsByUserId(userId);
            await ArticleDao.deleteArticlesByTherapistId(userId, deleteUploads);
            await EducationDao.deleteEducationDetailsByTherapistId(
              userId,
              deleteUploads
            );
            await LicenseDao.deleteLicenseDetailsByTherapistId(
              userId,
              deleteUploads
            );
            await VideoCallDao.deleteAllDiagnosisNotesByTherapistId(userId);
            await FeedbackDao.deleteAllFeedbackByUserId(userId);
            await VideoCallDao.deleteAllDiagnosisNotesByTherapistId(userId);
            await VideoCallDao.deleteAllTranscribeNotesByTherapistId(userId);
            await TransactionsDao.deleteAllTransactionsByTherapistId(userId);
            await WithdrawalDao.deleteAllWithdrawalDetailsByTherapistId(userId);
            await UserDao.deleteUserById(userId, deleteUploads);
          }
        } else {
          return res.sendError("User does not exist!");
        }

        return res.sendSuccess("User deleted!", "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function togglePremiumMembership(req: Request, res: Response) {
    const role = req.user.role;
    const clientId = req.params.clientId;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        const client = await ClientDao.getUserById(clientId);
        let updatedClient: IClient = null;

        if (client) {
          if (client.premiumStatus == PremiumStatus.ACTIVE) {
            updatedClient = await ClientDao.updateClient(clientId, {
              premiumStatus: PremiumStatus.REVOKED,
              premiumMembershipRevokedDate: new Date(),
            });

            await EmailService.clientPremiumStatusChanged(
              "Your premium access has been revoked.",
              client.email,
              client.firstname,
              PremiumStatus.REVOKED
            );

            await SMSService.sendEventSMS(
              `Hi ${client.firstname}! Your premium access has been revoked. Login to the dashboard to check more details.`,
              client.primaryPhone
            );
          } else if (client.premiumStatus == PremiumStatus.REVOKED) {
            updatedClient = await ClientDao.updateClient(clientId, {
              premiumStatus: PremiumStatus.ACTIVE,
              premiumMembershipStartedDate: new Date(),
            });

            await EmailService.clientPremiumStatusChanged(
              "Congratulations! You have been granted premium user membership.",
              client.email,
              client.firstname,
              PremiumStatus.ACTIVE
            );

            await SMSService.sendEventSMS(
              `Dear ${client.firstname}!, \nGreat news! Your Lavni account has been upgraded to Premium, unlocking additional
               features and benefits. We hope you enjoy the enhanced experience. Login to the dashboard to check more details.`,
              client.primaryPhone
            );
          } else {
            updatedClient = await ClientDao.updateClient(clientId, {
              premiumStatus: PremiumStatus.ACTIVE,
              premiumMembershipStartedDate: new Date(),
            });

            await EmailService.clientPremiumStatusChanged(
              "Congratulations! You have been granted premium user membership.",
              client.email,
              client.firstname,
              PremiumStatus.ACTIVE
            );

            await SMSService.sendEventSMS(
              `Dear ${client.firstname}!, \nGreat news! Your Lavni account has been upgraded to Premium, unlocking additional
               features and benefits. We hope you enjoy the enhanced experience. Login to the dashboard to check more details.`,
              client.primaryPhone
            );
          }

          return res.sendSuccess(updatedClient, "Success");
        } else {
          return res.sendError("Invalid clientId");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function getAllPremiumClients(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.premiumClients != true) {
            return res.sendError(
              "You don't have permission for Premium Clients!"
            );
          }
        }
        const clientList = await AdminDao.getAllPremiumClients(limit, offset);

        return res.sendSuccess(clientList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function getAllPremiumMembershipRevokedClients(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.premiumClients != true) {
            return res.sendError(
              "You don't have permission for Premium Clients!"
            );
          }
        }
        const clientList = await AdminDao.getAllPremiumMembershipRevokedClients(
          limit,
          offset
        );

        return res.sendSuccess(clientList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function getAllPremiumAndRevokedClients(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.premiumClients != true) {
            return res.sendError(
              "You don't have permission for Premium Clients!"
            );
          }
        }
        const premClientCount = await AdminDao.getAllPremiumClientsCount();
        const revokedClientCount = await AdminDao.getAllRevokedClientsCount();

        const data = {
          premClientCount: premClientCount,
          revokedClientCount: revokedClientCount,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function chargePaymentAndSendEmail(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    const clientId = Types.ObjectId(req.body.clientId);
    const payAmount = req.body.payAmount;
    const duePayment = req.body.duePayment;
    const paymentMonth = req.body.paymentMonth;

    try {
      const client = await ClientDao.getUserById(clientId);

      const recentInvoice = await InvoiceDao.getInvoiceByClientIdAndMonth(
        clientId,
        paymentMonth
      );

      if (recentInvoice) {
        return res.sendError(
          "You have already created an invoice for the selected month."
        );
      } else {
        const systemAdmin = (await AdminDao.getAllAdmins())[0];

        const insurancePayment: DInvoice = {
          clientId: clientId,
          insuranceId: client.insuranceId,
          paidAmountByInsurance: payAmount,
          dueAmount: duePayment,
          paymentStatus: PaymentStatus.PENDING,
          paymentMonth: paymentMonth,
        };

        let invoice = await InvoiceDao.addInvoice(insurancePayment);

        if (invoice) {
          const invoicePdf = await PdfService.generateInvoice(
            payAmount,
            duePayment,
            paymentMonth,
            clientId.toString()
          );

          const i = invoicePdf.split("/");

          const invoiceName = i[4];

          if (client.stripeCustomerId) {
            const stripeCustomer = await stripe.customers.retrieve(
              client.stripeCustomerId
            );

            const chargeForMonth = await stripe.paymentIntents.create({
              amount: Math.round(duePayment * 100),
              currency: "usd",
              customer: client.stripeCustomerId,
              payment_method:
                stripeCustomer.invoice_settings?.default_payment_method,
              description:
                "Client: " +
                client.email +
                ", Payment Month: " +
                invoice.paymentMonth,
              confirm: true,
            });

            if (chargeForMonth) {
              invoice.paymentStatus = PaymentStatus.PAID;
              invoice.save();

              let isSent =
                await EmailService.sendAdminEmailForInsuranceAndClientPayment(
                  "Your monthly payment has been recovered from insurance & your card.",
                  client.email,
                  client.firstname,
                  payAmount,
                  duePayment,
                  paymentMonth,
                  invoicePdf
                );
              await SMSService.sendEventSMS(
                `Your monthly payment has been recovered from insurance & your card. Hi ${client.firstname} We have covered $${payAmount} from you insurance for the month of ${paymentMonth}  And due amount of $${duePayment} has been recovered from your default payment method.`,
                client.primaryPhone
              );
              if (isSent) {
                const notificationDetails: DNotification = {
                  senderId: systemAdmin._id,
                  receiverId: client._id,
                  event: NotificationEvent.PREMIUM_MONTHLY_PAYMENT,
                  link: process.env.APP_URL + "/payment-history",
                  content:
                    "Your monthly payment has been recovered from insurance & your card.",
                  variant: "info",
                  readStatus: false,
                };

                await NotificationDao.createNotification(notificationDetails);

                return res.sendSuccess(
                  null,
                  "Payment is successfully charged from insurance & client."
                );
              } else {
                return res.sendError("Error while sending the email!");
              }
            } else {
              let isSent = await EmailService.sendAdminEmailForInsurancePayment(
                "Payment Reminder",
                client.email,
                client.firstname,
                payAmount,
                duePayment,
                paymentMonth,
                invoiceName,
                invoicePdf
              );
              await SMSService.sendEventSMS(
                `Hi ${client.firstname} We have covered $${payAmount} from you insurance for the month of ${paymentMonth}.

                You have a pending balance of $${duePayment}. Please kindly settle rest of the payment to keep your Lavni account active.`,
                client.primaryPhone
              );

              if (isSent) {
                const notificationDetails: DNotification = {
                  senderId: systemAdmin._id,
                  receiverId: client._id,
                  event: NotificationEvent.PREMIUM_MONTHLY_PAYMENT,
                  link: process.env.APP_URL + "/payment-history",
                  content: "Payment Reminder. You have due payment to settle.",
                  variant: "info",
                  readStatus: false,
                };

                await NotificationDao.createNotification(notificationDetails);

                return res.sendSuccess(
                  null,
                  "Payment is successfully charged & sent invoice to client."
                );
              } else {
                return res.sendError("Error while sending the email!");
              }
            }
          } else {
            let isSent = await EmailService.sendAdminEmailForInsurancePayment(
              "Payment Reminder",
              client.email,
              client.firstname,
              payAmount,
              duePayment,
              paymentMonth,
              invoiceName,
              invoicePdf
            );
            await SMSService.sendEventSMS(
              `Hi ${client.firstname} We have covered $${payAmount} from you insurance for the month of ${paymentMonth}.

              You have a pending balance of $${duePayment}. Please kindly settle rest of the payment to keep your Lavni account active.`,
              client.primaryPhone
            );
            if (isSent) {
              return res.sendSuccess(
                null,
                "Payment is successfully charged & sent invoice to client."
              );
            } else {
              return res.sendError("Error while sending the email!");
            }
          }
        } else {
          return res.sendError("Error while creating invoice!");
        }
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllMeetingsForSpecificTherapistForTranscribeByTherapistId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const id = req.params.therapistId;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.sendError("Invalid object Id!");
    }

    const therapistId = Types.ObjectId(id);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        const response =
          await VideoCallDao.getAllMeetingsForSpecificTherapistForTranscribeDao(
            therapistId,
            limit,
            offset
          );
        return res.sendSuccess(response, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function getAllMeetingsByTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const date = Number(req.body.date);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.adminDashboard != true) {
            return res.sendError(
              "You don't have permission for Dashboard!"
            );
          }
        }
        const response = await VideoCallDao.getAllMeetingsForSpecificTherapist(
          date
        );
        const array: any[] = [];
        for (let i = 0; i < response.length; i++) {
          const matchingId = array.findIndex(
            (j) =>
              j.therapistId._id.toString() ==
              response[i].therapistId._id.toString()
          );
          if (array[matchingId] && response[i].spentDuration !== null) {
            (array[matchingId].totalSpentDuration += response[i].spentDuration),
              (array[matchingId].totalMeetingDuration +=
                response[i].meetingDuration);
          } else {
            array.push({
              therapistId: response[i].therapistId,
              totalSpentDuration: response[i].spentDuration,
              totalMeetingDuration: response[i].meetingDuration,
            });
          }
        }

        return res.sendSuccess(array, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }
  export async function createInvoice(req: Request, res: Response) {
    await PdfService.generateInvoice("a", "b", "C", "d");

    return res.sendSuccess("", "Success");
  }

  export async function sendMarketingEmailList(req: Request, res: Response) {
    const role = req.user.role;
    const emaiList = req.body;

    let emailsSent;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.marketingEmails != true) {
            return res.sendError(
              "You don't have permission for Marketing Email!"
            );
          }
        }

        emailsSent = await AdminDao.setMarketingEmails(emaiList);

        if (emailsSent) {
          return res.sendSuccess(emailsSent, "All emails save successfully.");
        } else {
          return res.sendError("Error saving emails.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function deleteMarketingEmailLists(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const marketingEmailId = req.params.marketingEmailId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.marketingEmails != true) {
            return res.sendError(
              "You don't have permission for Marketing Email!"
            );
          }
        }
        let marketingEmails: any = await AdminDao.getMarketingEmails(
          marketingEmailId
        );

        if (marketingEmails == null) {
          return res.sendError("No marketing emails for the Id.");
        }

        try {
          let deletedMarketingEmails = await AdminDao.deleteMarketingEmails(
            marketingEmailId
          );

          return res.sendSuccess(
            deletedMarketingEmails,
            "Marketing emails deleted."
          );
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllMarketingEmailLists(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.marketingEmails != true) {
            return res.sendError(
              "You don't have permission for Marketing Email!"
            );
          }
        }

        const response = await AdminDao.getAllMarketingEmails(limit, offset);
        return res.sendSuccess(response, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function sendMarketingEmail1(req: Request, res: Response) {
    const role = req.user.role;
    const emaiList = req.body.emailList;
    const emailBody = req.body.emailBody;
    const emailSubject = req.body.emailSubject;

    let emailsSent;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.marketingEmails != true) {
            return res.sendError(
              "You don't have permission for Marketing Email!"
            );
          }
        }
        for (const obj of emaiList) {
          emailsSent = await EmailService.sendMarketingEmail1(
            emailSubject,
            obj,
            emailBody
          );
        }

        if (emailsSent) {
          return res.sendSuccess(null, "All emails sent successfully.");
        } else {
          return res.sendError("Error sending emails.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function addInsuranceCompany(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const errors = validationResult(req);

    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewInsuranceCompanies != true) {
        return res.sendError(
          "You don't have permission for Insurance Companies!"
        );
      }
    }

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {

      const insuranceCompany = req.body.insuranceCompany;
      const coPayment = req.body?.coPayment;
      const contractPrice = req.body?.contractPrice;
      const states = req.body?.states;
      const fax = req.body?.fax;
      const tradingPartnerServiceId = req.body?.tradingPartnerServiceId;
      const organizationName = req.body?.organizationName;
      const payerName = req.body?.payerName;
      const link = req.body?.link;

      try {
        const isFound = await AdminDao.getInsuranceCompanyByType(
          insuranceCompany
        );

        if (isFound) {
          return res.sendError(
            "Insurance Company is already found in the system."
          );
        }
        const insurance = await AdminDao.addInsuranceCompany(
          insuranceCompany,
          coPayment,
          contractPrice,
          states,
          fax,
          tradingPartnerServiceId,
          organizationName,
          payerName,
          link
        );
        if (!insurance) {
          return res.sendError(
            "Something went wrong! Insurance Company type could not be created."
          );
        }
        return res.sendSuccess(insurance, "Insurance Company created.");
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllInsuranceCompanies(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = req.params.limit;
    const offset = req.params.offset;

    try {

      if (req.user.role == UserRole.SUB_ADMIN) {
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if (ownUser.adminPermission.viewInsuranceCompanies != true && ownUser.adminPermission.adminApprovePayment != true && ownUser.adminPermission.statistics != true) {
          return res.sendError(
            "You don't have permission!"
          );
        }
      }

      const insuranceCompany = await AdminDao.getAllInsuranceCompaniesTypes(
        Number(limit),
        Number(offset)
      );

      if (insuranceCompany.length === 0) {
        return res.sendError(
          "Something went wrong! Could not load Insurance Companies."
        );
      }

      return res.sendSuccess(insuranceCompany, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllInsuranceCompaniesPublic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      // if(req.user.role == UserRole.SUB_ADMIN){
      //   const ownUser = await UserDao.getUserByUserId(req.user._id);
      //   if(ownUser.adminPermission.statistics != true){
      //     return res.sendError(
      //       "You don't have permission for Statistics!"
      //     );
      //   }
      // }
      const insuranceCompany =
        await AdminDao.getAllInsuranceCompaniesTypesPublic();

      if (insuranceCompany.length === 0) {
        return res.sendError(
          "Something went wrong! Could not load Insurance Companies."
        );
      }

      return res.sendSuccess(insuranceCompany, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getInsuranceById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewInsuranceCompanies != true) {
        return res.sendError(
          "You don't have permission for Insurance Companies!"
        );
      }
    }

    const insuranceCompanyId = req.params.insuranceCompanyId;
    try {
      let insuranceCompany: any = await AdminDao.getInsuranceCompanyById(
        insuranceCompanyId
      );

      if (insuranceCompany.length === 0) {
        return res.sendError(
          "Something went wrong! Could not load Insurance Companies."
        );
      }

      return res.sendSuccess(insuranceCompany, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteInsuranceCompany(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const insuranceCompanyId = req.params.deleteInsuranceCompanyId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewInsuranceCompanies != true) {
            return res.sendError(
              "You don't have permission for Insurance Companies!"
            );
          }
        }

        let insuranceCompany: any = await AdminDao.getInsuranceCompanyById(
          insuranceCompanyId
        );

        if (insuranceCompany == null) {
          return res.sendError("No experience tag found for the Id.");
        }

        try {
          let deletedInsuranceCompany = await AdminDao.deleteInsuranceCompany(
            insuranceCompanyId
          );

          return res.sendSuccess(
            deletedInsuranceCompany,
            "Insurance Company deleted."
          );
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function updateInsuranceCompany(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const insuranceCompanyId = req.body.insuranceCompanyId;
    const updatedInsuranceCompany = req.body?.updatedInsuranceCompany;
    const updatedContractPrice = req.body?.updatedContractPrice;
    const updatedCoPayment = req.body?.updatedCoPayment;
    const states = req.body?.states;
    const fax = req.body?.fax;
    const updatedTradingPartnerServiceId = req.body?.updatedTradingPartnerServiceId;
    const updatedOrganizationName = req.body?.updatedOrganizationName;
    const updatedPayerName = req.body?.updatedPayerName;
    const updatedLink = req.body?.updatedLink;
    
    const errors = validationResult(req);

    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewInsuranceCompanies != true) {
        return res.sendError(
          "You don't have permission for Insurance Companies!"
        );
      }
    }

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const isIdFound = await AdminDao.getInsuranceCompanyById(
        insuranceCompanyId
      );

      if (isIdFound) {
        const isNameFound = await AdminDao.checkInsuaranceCompanyExistNyName(
          updatedInsuranceCompany.replace(/\s\s+/g, " ").trim(),
          insuranceCompanyId
        );

        // const isNameFound = false;
        if (isNameFound) {
          return res.sendError("Provided insurance company already exists.");
        } else {
          const updatedInsurance = await AdminDao.updateInsuranceCompany(
            insuranceCompanyId,
            {
              insuranceCompany: updatedInsuranceCompany,
              contractPrice: updatedContractPrice,
              coPayment: updatedCoPayment,
              states: states,
              fax: fax,
              tradingPartnerServiceId: updatedTradingPartnerServiceId || null,
              organizationName: updatedOrganizationName || null,
              payerName: updatedPayerName || null,
              link: updatedLink || null,
            }
          );

          return res.sendSuccess(updatedInsurance, "Successfully updated.");
        }
      } else {
        return res.sendError("Insurance company id not found.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getDiagnosisNotesByClientId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = req.params.clientId;
    const limit = req.params.limit;
    const offset = req.params.offset;

    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid client Id");
    }

    try {
      let client = await ClientDao.getUserById(clientId);

      if (!client) {
        return res.sendError(
          "Could not find a client with the provided client Id"
        );
      }

      let noteList = await AdminDao.getDiagnosisNotesByClientId(
        Types.ObjectId(clientId),
        Number(limit),
        Number(offset)
      );

      res.sendSuccess(noteList, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllMeetingsAndRecords(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const limit = req.params.limit;
    const offset = req.params.offset;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewMeetingsAndRecordings != true) {
            return res.sendError(
              "You don't have permission for Meeting & Recordings!"
            );
          }
        }
        let meetings = await AdminDao.getAllMeetingsAndRecordings(
          Number(limit),
          Number(offset)
        );

        return res.sendSuccess(meetings, "meetings and recordings data.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("you have not permission");
    }
  }

  export async function deleteMeetingsAndRecordings(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const meetingId = req.params.meetingId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewMeetingsAndRecordings != true) {
            return res.sendError(
              "You don't have permission for Meetings & Recordings!"
            );
          }
        }
        let meetingAndRecording: any = await AdminDao.getMeetingsAndRecording(
          meetingId
        );

        if (meetingAndRecording == null) {
          return res.sendError("No meetings and recording for the Id.");
        }

        try {
          let deletedMeetingAndRecording =
            await AdminDao.deleteMeetingsAndRecording(meetingId);

          return res.sendSuccess(
            deletedMeetingAndRecording,
            "Meetings and recordings deleted."
          );
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllTherapistPendingReviews(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewTherapistReviews != true) {
            return res.sendError(
              "You don't have permission for Manage Reviews!"
            );
          }
        }
        const reportList = await AdminDao.getAllPendingTherapistReviews(
          limit,
          offset
        );

        return res.sendSuccess(reportList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllTherapistsSOAPReviews(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewTherapistsSoapReviews != true) {
            return res.sendError(
              "You don't have permission for AI Soap Reviews!"
            );
          }
        }
        const reportList = await AdminDao.getAllSOAPTherapistReviews(
          limit,
          offset
        );

        return res.sendSuccess(reportList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllTherapistApprovedReviews(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewTherapistReviews != true) {
            return res.sendError(
              "You don't have permission for Manage Reviews!"
            );
          }
        }
        const reportList = await AdminDao.getAllApprovedTherapistReviews(
          limit,
          offset
        );

        return res.sendSuccess(reportList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function updateCustomerReview(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const reviewId = req.body.reviewId;
    const status = req.body.status;

    const errors = validationResult(req);

    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.reviews != true) {
        return res.sendError(
          "You don't have permission for Customer Reviews!"
        );
      }
    }

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const isIdFound = await AdminDao.getReviewById(reviewId);

      if (isIdFound) {
        const updatedReview = await AdminDao.updateReview(reviewId, {
          status: status,
        });

        return res.sendSuccess(updatedReview, "Successfully updated.");
      } else {
        return res.sendError("Review id not found.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllLavniPendingReviews(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    try {
      const pendingReviews = await AdminDao.getAllLavniReviews(
        limit,
        offset,
        LavniReviewStatus.PENDING
      );

      return res.sendSuccess(pendingReviews, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllLavniApprovedReviews(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    try {
      const approvedReviews = await AdminDao.getAllLavniReviews(
        limit,
        offset,
        LavniReviewStatus.APPROVED
      );

      return res.sendSuccess(approvedReviews, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function searchClientsByName(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      // Get pagination parameters from the URL
      const limit = Number(req.params.limit) || 10;
      const offset = Number(req.params.offset) || 0;
      
      // Get search string from the request body
      const searchString = req.body.searchString || '';
      
      // Check if user is admin
      if (
        req.user.role !== UserRole.SUPER_ADMIN && 
        req.user.role !== UserRole.SUB_ADMIN && 
        req.user.role !== UserRole.ADMIN
      ) {
        return res.status(403).json({
          success: false,
          message: "Access denied. Admin privileges required."
        });
      }

      // Create the search query based on the number of words
      const words = searchString.trim().split(/\s+/);
      let query = {};
      
      if (words.length === 0 || searchString === '') {
        // Empty search - return all clients
        query = { role: UserRole.CLIENT };
      } else if (words.length === 1) {
        // Single word search - match either first or last name
        const term = new RegExp(words[0], 'i');
        query = {
          role: UserRole.CLIENT,
          $or: [
            { firstname: term },
            { lastname: term },
            { email: term }
          ]
        };
      } else {
        // Multi-word search - try to match full name patterns
        const firstWord = new RegExp(words[0], 'i');
        const otherWords = new RegExp(words.slice(1).join(' '), 'i');
        
        query = {
          role: UserRole.CLIENT,
          $or: [
            // First word as first name, rest as last name
            { $and: [
                { firstname: firstWord },
                { lastname: otherWords }
              ]
            },
            // First word as last name, rest as first name
            { $and: [
                { lastname: firstWord },
                { firstname: otherWords }
              ]
            },
            // Full text could be in either field
            { firstname: new RegExp(searchString, 'i') },
            { lastname: new RegExp(searchString, 'i') },
            { email: new RegExp(searchString, 'i') }
          ]
        };
      }

      // Find clients matching the query with pagination
      const clients = await User.find(query)
        .select('_id firstname lastname email gender dateOfBirth phone profileImage state zipCode')
        .sort({ firstname: 1, lastname: 1 })
        .skip(offset)
        .limit(limit);

      // Get total count for pagination
      const totalCount = await User.countDocuments(query);

      return res.status(200).json({
        success: true,
        message: "Clients found successfully",
        data: {
          clients: clients,
          totalCount: totalCount,
          currentPage: Math.floor(offset / limit) + 1,
          totalPages: Math.ceil(totalCount / limit)
        }
      });
    } catch (error) {
      console.error("Error searching clients by name:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while searching clients",
        error: error.message
      });
    }
  }

export async function updateLavniReview(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const reviewId = req.body.reviewId;
    const status = req.body.status;

    try {
      const isIdFound = await AdminDao.getLavniReviewById(reviewId);

      if (isIdFound) {
        const updatedReview = await AdminDao.updateLavniReview(reviewId, {
          status: status
        });

        return res.sendSuccess(updatedReview, "Successfully updated.");
      } else {
        return res.sendError("Review id not found.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendReminderClinicalNotes(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      AppLogger.info(
        `.:: Send Reminder Clinical Notes SMS and Email By Therapist ::.`
      );
      const noteId = req.body.noteId;
      let note = await AdminStatisticsDao.getDiagnosisNoteNotesByAdmin(noteId);
      const therapistNote = note as any;

      if (therapistNote.therapistId?.reminderType && therapistNote.therapistId?.reminderType.text == true) {
        const smsSend = await SMSService.sendEventSMS(
          `Lavni Reminder: Your clinical note for ${therapistNote?.clientId?.firstname.charAt(0)}. ${therapistNote?.clientId?.lastname} is not complete.`,
          therapistNote.therapistId?.primaryPhone
        );
      }

      if (therapistNote.therapistId.reminderType && therapistNote.therapistId.reminderType.email == true) {
        const sendEmail = await EmailService.sendEventEmailNotes(
          therapistNote.therapistId,
          "Lavni Reminder!",
          `Your clinical note for ${therapistNote.clientId.firstname.charAt(0)}. ${therapistNote.clientId.lastname} is not complete.`
        );
        if (sendEmail) {
          return res.sendSuccess("Clinical Notes reminder sent successfully");
        } else {
          return res.sendError("Failed to send reminder Clinical Notes");
        }
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendReminderClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      AppLogger.info(`.:: Send Reminder SMS By Client ::.`);
      const clientId = req.body.clientId;
      const message = req.body.message;
      let client = await AdminStatisticsDao.getClientsByAdmin(clientId);
      if (message) {
        if (client.reminderType && client.reminderType.text == true) {
          const smsSend = await SMSService.sendEventSMS(
            message,
            client?.primaryPhone
          );
        }
        if (client.reminderType && client.reminderType.email == true) {
          const sendEmail = await EmailService.sendEventEmailNotes(
            client,
            "Lavni Reminder!",
            message
          );
          if (sendEmail) {
            return res.sendSuccess("Clinical Notes reminder sent successfully");
          } else {
            return res.sendError("Failed to send reminder Clinical Notes");
          }
        }
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateClientProfileByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {

      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }

      const userId = req.body.userId;
      const description = req.body.description;
      const firstname = req.body.firstname;
      const lastname = req.body.lastname;
      const email = req.body.email;
      const gender = req.body.gender;
      const ethnicityId = req.body.ethnicityId;
      const dateOfBirth = req.body.dateOfBirth;
      const username = req.body.username;
      const incognito = req.body.incognito;
      const middleInitials = req.body.middleInitials;
      const maritalStatus = req.body.maritalStatus;
      const streetAddress = req.body.streetAddress;
      const unit = req.body.unit;
      const city = req.body.city;
      const freeUser = req.body.freeUser;
      const state = req.body.state;
    const therapistCategorizationByType = req.body.therapistCategorizationByType;
      const zipCode = req.body.zipCode;
      const primaryPhone = req.body.primaryPhone;
      const homePhone = req.body.homePhone;
      const workPhone = req.body.workPhone;
      const voiceMail = req.body.voiceMail;
      const reminderType = req.body.reminderType;
      const reminderTime = req.body.reminderTime;

      if (email) {
        if (primaryPhone) {
          let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
            primaryPhone
          );

          if (
            isprimaryPhoneUsed &&
            userId.toString() !== isprimaryPhoneUsed._id.toString()
          ) {
            return res.sendError("Provided primary phone is already taken.");
          }
        }

        const client: DClient = {
          description: description,
          email: email,
          gender: gender,
          ethnicityId: Types.ObjectId(ethnicityId),
          dateOfBirth: dateOfBirth,
          incognito: incognito,
          middleInitials: middleInitials,
          maritalStatus: maritalStatus,
          streetAddress: streetAddress,
          unit: unit,
          city: city,
          state: state,
          zipCode: zipCode,
          primaryPhone: primaryPhone,
          homePhone: homePhone,
          workPhone: workPhone,
          voiceMail: voiceMail,
          reminderType: reminderType,
          reminderTime: reminderTime,
          freeUser: freeUser,
        };

        if (firstname) {
          client.firstname = firstname.trim();
        }

        if (lastname) {
          client.lastname = lastname.trim();
        }

        if (username) {
          client.username = username.trim();
        }

        if (primaryPhone) {
          const isValidPhoneNumber = await SmsChatEp.validatePhoneNumberFromTwilio(primaryPhone.toString());

          if (!isValidPhoneNumber) {
            return res.sendError("Invalid phone number");
          }
        }

        let updatedClient = await UserDao.updateUser(userId, client);

        if (!updatedClient) {
          return res.sendError("Something went wrong. Please try again later.");
        }

        if (primaryPhone) {
          SmsChatEp.changePhoneNumberFromTwilio(primaryPhone.toString(), req.user.role.toString(), userId.toString());
        }

        return res.sendSuccess(updatedClient, "Your profile has been updated successfully.");

      } else {
        return res.sendError("Please provide email.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateTherapistProfileByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {


    let newExpTags: Types.ObjectId[] = [];
    let newInsuranceCompanyTags: Types.ObjectId[] = [];
    let uploadCategory = UploadCategory.DISCLOSURE_STATEMENT;


    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateTherapisProfileValidationRules(req, cb);
      },
    });

    async function updateTherapisProfileValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let profileDetails = JSON.parse(req.body.profileDetails);


        let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
          profileDetails.primaryPhone
        );

        if (
          isprimaryPhoneUsed &&
          profileDetails?.userId.toString() !== isprimaryPhoneUsed._id.toString()
        ) {
          return res.sendError("Provided primary phone is already taken.");
        }

        if (
          !profileDetails.firstname ||
          typeof profileDetails.firstname !== "string"
        ) {
          return cb(Error("Firstname is required."), null);
        }

        if (
          !profileDetails.lastname ||
          typeof profileDetails.lastname !== "string"
        ) {
          return cb(Error("Lastname is required."), null);
        }

        if (
          !profileDetails.username ||
          typeof profileDetails.username !== "string"
        ) {
          return cb(Error("Username is required."), null);
        }

        if (!profileDetails.email || typeof profileDetails.email !== "string") {
          return cb(Error("Email is required."), null);
        }

        if (
          !profileDetails.gender ||
          typeof profileDetails.gender !== "string"
        ) {
          return cb(Error("Gender is required."), null);
        }

        if (
          !profileDetails.timeZone ||
          typeof profileDetails.timeZone !== "string"
        ) {
          return cb(Error("Timezone is required."), null);
        }

        if (
          !profileDetails.deletingStatementId ||
          typeof profileDetails.deletingStatementId !== "string"
        ) {
          return cb(Error("Deleting statement id is required."), null);
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) =>
              cb(error, "destination")
            );
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    async function deleteOldFiles(uploadId: string) {
      let isDeleted = false;

      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldFile = await UploadDao.getUpload(uploadId);
        await fs.unlink(oldFile.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }
      return isDeleted;
    }

    const upload = multer({ storage: storage }).single("disclosureStatement");

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        let isValid = true;
        let isFileDeleted = false;

        if (!req.file) {
          let profileDetails = JSON.parse(req.body.profileDetails);


          let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
            profileDetails.primaryPhone
          );

          if (
            isprimaryPhoneUsed &&
            profileDetails?.userId.toString() !== isprimaryPhoneUsed._id.toString()
          ) {
            return res.sendError("Provided primary phone is already taken.");
          }

          if (
            !profileDetails.firstname ||
            typeof profileDetails.firstname !== "string"
          ) {
            isValid = false;
            return res.sendError("Firstname is required.");
          }

          if (
            !profileDetails.lastname ||
            typeof profileDetails.lastname !== "string"
          ) {
            isValid = false;
            return res.sendError("lastname is required.");
          }

          if (
            !profileDetails.username ||
            typeof profileDetails.username !== "string"
          ) {
            isValid = false;
            return res.sendError("Username is required.");
          }

          if (
            !profileDetails.email ||
            typeof profileDetails.email !== "string"
          ) {
            isValid = false;
            return res.sendError("email is required.");
          }

          if (
            !profileDetails.gender ||
            typeof profileDetails.gender !== "string"
          ) {
            isValid = false;
            return res.sendError("gender is required.");
          }

          if (
            !profileDetails.primaryPhone ||
            typeof profileDetails.primaryPhone !== "string"
          ) {
            isValid = false;
            return res.sendError("primary phone number is required.");
          }

          if (
            !profileDetails.dateOfBirth ||
            typeof profileDetails.dateOfBirth !== "string"
          ) {
            isValid = false;
            return res.sendError("dateOfBirth is required.");
          }

          if (
            !profileDetails.deletingStatementId ||
            typeof profileDetails.deletingStatementId !== "string"
          ) {
            isValid = false;
            return res.sendError("deletingStatementId is required.");
          }

          if (
            !profileDetails.timeZone ||
            typeof profileDetails.timeZone !== "string"
          ) {
            isValid = false;
            return res.sendError("Timezone is required.");
          }

          const user = await TherapistDao.getUserById(profileDetails?.userId);

          if (isValid) {
            if (!user.username) {
              if (!profileDetails.username) {
                return res.sendError("Username field is required.");
              }

              const foundUser = await UserDao.getUserByUsername(
                profileDetails.username
              );

              if (foundUser) {
                return res.sendError("Username is already taken.");
              }
            }

            if (user._id.toString() === profileDetails?.userId.toString()) {
              if (
                profileDetails.experiencedIn &&
                profileDetails.experiencedIn.length > 0
              ) {
                await Promise.all(
                  profileDetails.experiencedIn.map(async (tag: any) => {
                    const isFound = await AdminDao.getExperienceTagsByName(tag);
                    if (!isFound || isFound == null) {
                      const addedTag = await AdminDao.addExperienceTag(tag);
                      newExpTags.push(addedTag._id);
                    } else {
                      newExpTags.push(isFound._id);
                    }
                  })
                );
              }
            }

            if (user._id.toString() === profileDetails?.userId.toString()) {
              if (
                profileDetails.insuranceCompanies &&
                profileDetails.insuranceCompanies.length > 0
              ) {
                await Promise.all(
                  profileDetails.insuranceCompanies.map(
                    async (company: any) => {
                      const isFound = await AdminDao.getInsuranceCompanyByType(
                        company
                      );
                      if (!isFound || isFound == null) {
                        const addedCompany = await AdminDao.addInsuranceCompany(
                          company
                        );
                        newInsuranceCompanyTags.push(addedCompany._id);
                      } else {
                        newInsuranceCompanyTags.push(isFound._id);
                      }
                    }
                  )
                );
              }
            }
            if (profileDetails.deletingStatementId !== "none") {
              isFileDeleted = await deleteOldFiles(
                profileDetails.deletingStatementId
              );

              if (!isFileDeleted)
                return res.sendError("Error while deleting the previous file");
            }
          }

          const therapistDetails: ITherapist = await UserDao.getUserById(
            profileDetails?.userId
          );

          if (therapistDetails) {
            const therapist: DTherapist = {
              description: profileDetails.description,
              firstname: profileDetails.firstname.trim(),
              lastname: profileDetails.lastname.trim(),
              middleInitials: profileDetails.middleInitials,
              email: profileDetails.email,
              gender: profileDetails.gender,
              ethnicityId:
                profileDetails.ethnicityId !== null ||
                  profileDetails.ethnicityId !== undefined ||
                  profileDetails.ethnicityId !== ""
                  ? Types.ObjectId(profileDetails.ethnicityId)
                  : null,
              dateOfBirth: profileDetails.dateOfBirth,
              experiencedIn: newExpTags,
              profession:
                profileDetails.profession !== null ||
                  profileDetails.profession !== undefined ||
                  profileDetails.profession !== ""
                  ? Types.ObjectId(profileDetails.profession)
                  : null,
              professionLicense:
                profileDetails.professionLicense !== null ||
                  profileDetails.professionLicense !== undefined ||
                  profileDetails.professionLicense !== ""
                  ? Types.ObjectId(profileDetails.professionLicense)
                  : null,
              yearsOfExperience: profileDetails.yearsOfExperience,
              workingHours: profileDetails.workingHours,
              insuranceCompanies: newInsuranceCompanyTags,
              username: profileDetails.username,
              socialSecurity: profileDetails.socialSecurity,
              cAQH: profileDetails.cAQH,
              nPI1: profileDetails.nPI1,
              taxIdentification: profileDetails.taxIdentification,
              license: profileDetails.license,
              issueDate: profileDetails.issueDate,
              expirationDate: profileDetails.expirationDate,
              schoolName: profileDetails.schoolName,
              dateOfGraduation: profileDetails.dateOfGraduation,
              schoolStreetAddress: profileDetails.schoolStreetAddress,
              schoolCity: profileDetails.schoolCity,
              schoolState: profileDetails.schoolState,
              schoolZipCode: profileDetails.schoolZipCode,
              taxonomyCode: profileDetails.taxonomyCode,
              malpracticePolicy: profileDetails.malpracticePolicy,
              malpracticeExpirationDate: profileDetails.malpracticeExpirationDate,
              disclosureStatementId: user.disclosureStatementId
                ? user.disclosureStatementId
                : null,
              primaryPhone: profileDetails.primaryPhone,
              streetAddress: profileDetails.streetAddress,
              city: profileDetails.city,
              state: profileDetails.state,
              zipCode: profileDetails.zipCode,
              timeZone: profileDetails.timeZone,
              googleCalendarAccess: profileDetails.googleCalendarAccess ?? therapistDetails?.googleCalendarAccess,
              caqhUserName: profileDetails.caqhUserName,
              caqhPassword: profileDetails.caqhPassword,
              medicaidUsername: profileDetails?.medicaidUsername,
              MedicaidPassword: profileDetails?.MedicaidPassword,
              medicaidId: profileDetails?.medicaidId,
              psychologyTodayUsername: profileDetails?.psychologyTodayUsername,
              psychologyTodayPassword: profileDetails?.psychologyTodayPassword,
              therapyState: [...new Set([...(profileDetails.therapyState || []), profileDetails.state])]
            };

            let updatedTherapist = await UserDao.updateUser(profileDetails?.userId, therapist);

            if (!updatedTherapist) {
              return res.sendError("Failed to update the therapist.");
            }

            return res.sendSuccess(
              updatedTherapist,
              "Your profile has been updated successfully."
            );
          } else {
            return res.sendError("The therapist could not be found.");
          }
        } else {
          let profileDetails = JSON.parse(req.body.profileDetails);

          let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
            profileDetails.primaryPhone
          );

          if (
            isprimaryPhoneUsed &&
            profileDetails?.userId.toString() !== isprimaryPhoneUsed._id.toString()
          ) {
            return res.sendError("Provided primary phone is already taken.");
          }

          let isFileDeleted = false;
          let uploadedDcoument = null;

          const user = await UserDao.getUser(profileDetails?.userId);

          if (!user.username) {
            if (!profileDetails.username) {
              return res.sendError("Username field is required.");
            }

            const foundUser = await UserDao.getUserByUsername(
              profileDetails.username
            );

            if (foundUser) {
              return res.sendError("Username is already taken.");
            }
          }

          if (user._id.toString() === profileDetails?.userId.toString()) {
            if (
              profileDetails.experiencedIn &&
              profileDetails.experiencedIn.length > 0
            ) {
              await Promise.all(
                profileDetails.experiencedIn.map(async (tag: any) => {
                  const isFound = await AdminDao.getExperienceTagsByName(tag);
                  if (!isFound || isFound == null) {
                    const addedTag = await AdminDao.addExperienceTag(tag);
                    newExpTags.push(addedTag._id);
                  } else {
                    newExpTags.push(isFound._id);
                  }
                })
              );
            }
          }

          if (user._id.toString() === profileDetails?.userId.toString()) {
            if (
              profileDetails.insuranceCompanies &&
              profileDetails.insuranceCompanies.length > 0
            ) {
              await Promise.all(
                profileDetails.insuranceCompanies.map(async (company: any) => {
                  const isFound = await AdminDao.getInsuranceCompanyByType(
                    company
                  );

                  if (!isFound || isFound == null) {
                    const addedCompany = await AdminDao.addInsuranceCompany(
                      company
                    );
                    newInsuranceCompanyTags.push(addedCompany._id);
                  } else {
                    newInsuranceCompanyTags.push(isFound._id);
                  }
                })
              );
            }
          }

          if (profileDetails.deletingStatementId !== "none") {
            isFileDeleted = await deleteOldFiles(
              profileDetails.deletingStatementId
            );

            if (!isFileDeleted)
              return res.sendError("Error while deleting the previous file");
          }

          const upload: any = req.file;

          let signRequired: boolean = false;
          if (req.body.signRequired !== undefined) {
            signRequired = req.body.signRequired;
          }

          if (req.file) {
            const disclosureStatement = {
              userId: profileDetails?.userId as unknown as Types.ObjectId,
              originalName: upload.originalname.replace(/ /g, ""),
              name: upload.filename,
              type: upload.mimetype,
              path: upload.path,
              fileSize: upload.size,
              extension:
                path.extname(upload.originalname) || req.body.extension,
              category: uploadCategory,
              signRequired: signRequired,
            };

            uploadedDcoument = await UploadDao.createUpload(
              disclosureStatement
            );

            if (!uploadedDcoument) {
              return res.sendError("Error while uploading profile image.");
            }
          }

          const therapist: DTherapist = {
            description: profileDetails.description,
            firstname: profileDetails.firstname,
            lastname: profileDetails.lastname,
            middleInitials: profileDetails.middleInitials,
            email: profileDetails.email,
            gender: profileDetails.gender,
            ethnicityId:
              profileDetails.ethnicityId !== null ||
                profileDetails.ethnicityId !== undefined ||
                profileDetails.ethnicityId !== ""
                ? Types.ObjectId(profileDetails.ethnicityId)
                : null,
            dateOfBirth: profileDetails.dateOfBirth,
            experiencedIn: newExpTags,
            insuranceCompanies: newInsuranceCompanyTags,
            profession:
              profileDetails.profession !== null ||
                profileDetails.profession !== undefined ||
                profileDetails.profession !== ""
                ? Types.ObjectId(profileDetails.profession)
                : null,
            professionLicense:
              profileDetails.professionLicense !== null ||
                profileDetails.professionLicense !== undefined ||
                profileDetails.professionLicense !== ""
                ? Types.ObjectId(profileDetails.professionLicense)
                : null,
            yearsOfExperience: profileDetails.yearsOfExperience,
            workingHours: profileDetails.workingHours,
            username: profileDetails.username,
            socialSecurity: profileDetails.socialSecurity,
            cAQH: profileDetails.cAQH,
            nPI1: profileDetails.nPI1,
            taxIdentification: profileDetails.taxIdentification,
            license: profileDetails.license,
            issueDate: profileDetails.issueDate,
            expirationDate: profileDetails.expirationDate,
            schoolName: profileDetails.schoolName,
            dateOfGraduation: profileDetails.dateOfGraduation,
            schoolStreetAddress: profileDetails.schoolStreetAddress,
            schoolCity: profileDetails.schoolCity,
            schoolState: profileDetails.schoolState,
            schoolZipCode: profileDetails.schoolZipCode,
            taxonomyCode: profileDetails.taxonomyCode,
            malpracticePolicy: profileDetails.malpracticePolicy,
            malpracticeExpirationDate: profileDetails.malpracticeExpirationDate,
            disclosureStatementId: uploadedDcoument._id,
            primaryPhone: profileDetails.primaryPhone,
            streetAddress: profileDetails.streetAddress,
            city: profileDetails.city,
            state: profileDetails.state,
            zipCode: profileDetails.zipCode,
            timeZone: profileDetails.timeZone,
            reminderType: profileDetails.reminderType,
            reminderTime: profileDetails.reminderTime,
            caqhUserName: profileDetails.caqhUserName,
            caqhPassword: profileDetails.caqhPassword,
            medicaidUsername: profileDetails?.medicaidUsername,
            MedicaidPassword: profileDetails?.MedicaidPassword,
            psychologyTodayUsername: profileDetails?.psychologyTodayUsername,
            psychologyTodayPassword: profileDetails?.psychologyTodayPassword,
            therapyState: [...new Set([...(profileDetails.therapyState || []), profileDetails.state])]
          };

          let updatedTherapist = await UserDao.updateUser(profileDetails?.userId, therapist);

          if (!updatedTherapist) {
            return res.sendError("Failed to update the therapist.");
          }

          return res.sendSuccess(
            updatedTherapist,
            "Your profile has been updated successfully."
          );
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateProfileCoverImageByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.params.userId;
    const themeId = req.body.themeId;
    const uploadCategory = UploadCategory.PROFILE_COVER_IMAGE;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateCoverImageValidationRules(req, cb);
      },
    });

    async function updateCoverImageValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) =>
              cb(error, "destination")
            );
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    async function deleteOldPhoto(uploadId: StringOrObjectId) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldPhoto = await UploadDao.getUpload(uploadId.toString());
        if (oldPhoto.category === UploadCategory.THEMES) {
          isDeleted = true;
        } else {
          await fs.unlink(oldPhoto.path, resultHandler);
          await UploadDao.deleteUploadById(uploadId);
          isDeleted = true;
        }
      } catch (error) {
        isDeleted = false;
      }

      return isDeleted;
    }

    const upload = multer({ storage: storage }).single("profileCoverImage");

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + " ");
        } else {
          if (req.file == null || req.file === undefined) {
            if (themeId) {
              let user: any = await UserDao.getUserById(userId);

              if (
                user.coverPhotoId &&
                user.coverPhotoId.category !== UploadCategory.THEMES
              ) {
                await deleteOldPhoto(user.coverPhotoId);
              }

              let updatedUser = await UserDao.updateUser(userId, {
                coverPhotoId: themeId,
              });

              return res.sendSuccess(updatedUser, "Success");
            } else {
              return res.sendError(
                "You have to either upload an image or select an already provided image."
              );
            }
          } else {
            const image = req.file;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            const data: DUpload = {
              userId: userId as unknown as Types.ObjectId,
              originalName: image.originalname.replace(/ /g, ""),
              name: image.filename,
              type: image.mimetype,
              path: image.path,
              fileSize: image.size,
              extension: path.extname(image.originalname) || req.body.extension,
              category: uploadCategory,
              signRequired: signRequired,
            };

            try {
              let user = await UserDao.getUserById(userId);

              if (
                user.coverPhotoId !== null &&
                user.coverPhotoId !== undefined
              ) {
                let isFileDeleted = await deleteOldPhoto(user.coverPhotoId);

                if (isFileDeleted) {
                  let uploadedCoverImage: IUpload =
                    await UploadDao.createUpload(data);

                  if (uploadedCoverImage == null) {
                    return res.sendError(
                      "Error while uploading the cover image."
                    );
                  }

                  const userDetails: DUser = {
                    coverPhotoId: uploadedCoverImage._id,
                  };

                  let updatedUser = await UserDao.updateUser(
                    userId,
                    userDetails
                  );

                  if (updatedUser == null) {
                    return res.sendError("User could not be updated.");
                  }

                  return res.sendSuccess(updatedUser, "Success");
                } else {
                  return res.sendError("Error while deleting the cover image.");
                }
              } else {
                let uploadedCoverImage: IUpload = await UploadDao.createUpload(
                  data
                );

                if (uploadedCoverImage == null) {
                  return res.sendError(
                    "Error while uploading the cover image."
                  );
                }

                const userDetails: DUser = {
                  coverPhotoId: uploadedCoverImage._id,
                };

                let updatedUser = await UserDao.updateUser(userId, userDetails);

                if (updatedUser == null) {
                  return res.sendError("User could not be updated.");
                }

                return res.sendSuccess(updatedUser, "Success");
              }
            } catch (error) {
              return res.sendError(error);
            }
          }
        }
      });
    } catch (error) {
      return res.sendError("Failed to upload image. " + error);
    }
  }

  export async function updateProfileImageByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = Types.ObjectId(req.params.userId);
    const uploadCategory = UploadCategory.PROFILE_IMAGE;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateProfileImageValidationRules(req, cb);
      },
    });

    async function updateProfileImageValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) =>
              cb(error, "destination")
            );
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    async function deleteOldPhoto(uploadId: StringOrObjectId) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldPhoto = await UploadDao.getUpload(uploadId.toString());
        await fs.unlink(oldPhoto.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }

      return isDeleted;
    }

    const upload = multer({ storage: storage }).single(
      "profileImage"
    );

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + " ");
        } else {
          if (req.file == null || req.file === undefined) {
            return res.sendError("Image not found.");
          } else {
            const image = req.file;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }
            const data: DUpload = {
              userId: userId as unknown as Types.ObjectId,
              originalName: image.originalname.replace(/ /g, ""),
              name: image.filename,
              type: image.mimetype,
              path: image.path,
              fileSize: image.size,
              extension: path.extname(image.originalname) || req.body.extension,
              category: uploadCategory,
              signRequired: signRequired,
            };

            try {
              let user = await AdminDao.getUserById(userId);
              let uploadedImage: IUpload = null;
              let uploadedImageThambnail: IUpload = null;

              if (user.photoId) {
                let isFileDeleted = await deleteOldPhoto(user.photoId);

                if (isFileDeleted) {
                  uploadedImage = await UploadDao.createUpload(data);

                  if (uploadedImage == null) {
                    return res.sendError("Error while uploading the image.");
                  }

                  const userDetails: DUser = {
                    photoId: uploadedImage._id,
                  };

                  let updatedUser = await UserDao.updateUser(
                    userId,
                    userDetails
                  );

                  if (updatedUser == null) {
                    return res.sendError("User could not be updated.");
                  }

                  return res.sendSuccess(updatedUser, "Success");
                } else {
                  return res.sendError("Error while deleting the file.");
                }
              } else if (user.photoId) {
                let isFileDeleted = await deleteOldPhoto(user.photoId);

                if (isFileDeleted) {
                  uploadedImage = await UploadDao.createUpload(data);


                  if (uploadedImage == null) {
                    return res.sendError("Error while uploading the image.");
                  }
                  const userDetails: DUser = {
                    photoId: uploadedImage._id
                  };

                  let updatedUser = await UserDao.updateUser(
                    userId,
                    userDetails
                  );

                  if (updatedUser == null) {
                    return res.sendError("User could not be updated.");
                  }

                  return res.sendSuccess(updatedUser, "Success");
                }
              } else {
                uploadedImage = await UploadDao.createUpload(data);

                if (uploadedImage == null) {
                  return res.sendError("Error while uploading the image.");
                }
                const userDetails: DUser = {
                  photoId: uploadedImage._id
                };

                let updatedUser = await UserDao.updateUser(userId, userDetails);

                if (updatedUser == null) {
                  return res.sendError("User could not be updated.");
                }

                return res.sendSuccess(updatedUser, "Success");
              }
            } catch (error) {
              return res.sendError(error);
            }
          }
        }
      });
    } catch (error) {
      return res.sendError("Failed to upload image. " + error);
    }
  }

  export async function searchMatchTherapistsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const gender = req.body.gender;
    const status = req.body.status;
    const isSubscription = req.body.isSubscription;
    const zipCode = req.body.zipCode;
    const state = req.body.state;
    const therapistCategorizationByType = req.body.therapistCategorizationByType;
    const clientId = req.body.clientId;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewAllClients != true && ownUser.adminPermission.statistics != true && ownUser.adminPermission.createAppointmentAdmin != true) {
        return res.sendError(
          "You don't have permission!"
        );
      }
    }

    if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        const client = await UserDao.getUserById(clientId)
        if (!client) {
          return res.sendError("Incorrect client Id!");
        }
        const friendRequestList = await AdminDao.getFriendRequestByClientId(clientId)
        const stringifiedFriendRequests: string[] = [];

        friendRequestList.forEach((request) => {
          stringifiedFriendRequests.push(request.therapistId.toString());
        });

        const result = await AdminDao.searchTherapistsByClientIdFriends(
          searchableString,
          limit,
          offset,
          gender,
          status,
          isSubscription,
          zipCode,
          stringifiedFriendRequests
        );
        const countUser = await AdminDao.getTherapistsByClientIdCount(
          searchableString,
          gender,
          status,
          isSubscription,
          zipCode,
          stringifiedFriendRequests
        );

        const data = {
          userSet: result,
          count: countUser,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }
  export async function searchMatchedTherapistsByClientId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const gender = req.body.gender;
    const status = req.body.status;
    const isSubscription = req.body.isSubscription;
    const zipCode = req.body.zipCode;
    const state = req.body.state;
    const therapistCategorizationByType = req.body.therapistCategorizationByType;
    const clientId = req.body.clientId;
    const therapistId = req.body.therapistId;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const client = await UserDao.getUserById(clientId)
      if (!client) {
        return res.sendError("Incorrect client Id!");
      }
      const friendRequestList = await AdminDao.getFriendRequestByClientId(clientId)
      const stringifiedFriendRequests: string[] = [];

      friendRequestList.forEach((request) => {
        stringifiedFriendRequests.push(request.therapistId.toString());
      });

      const result = await AdminDao.searchTherapistsByClientIdMatchedFriends(
        searchableString,
        limit,
        offset,
        gender,
        status,
        isSubscription,
        zipCode,
        stringifiedFriendRequests
      );

      const countUser = await AdminDao.getMatchedTherapistsByClientIdCount(
        searchableString,
        gender,
        status,
        isSubscription,
        zipCode,
        stringifiedFriendRequests
      );

      const data = {
        userSet: result,
        count: countUser,
      };

      return res.sendSuccess(data, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function searchMatchedTherapistsWithoutOwnTherapistByClientId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const clientId = req.body.clientId;
    const therapistId = req.body.therapistId;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const client = await UserDao.getUserById(clientId)
      if (!client) {
        return res.sendError("Incorrect client Id!");
      }
      const friendRequestList = await AdminDao.getFriendRequestByClientIdAndTherapistId(clientId, therapistId)
      const stringifiedFriendRequests: string[] = [];

      friendRequestList.forEach((request) => {
        stringifiedFriendRequests.push(request.therapistId.toString());
      });

      const result = await AdminDao.searchTherapistsByClientIdAndTherapistIdMatchedFriends(
        limit,
        offset,
        stringifiedFriendRequests
      );



      return res.sendSuccess(result, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function setDailySuccessClaimsStatusUpdate() {
    const claimList = await AdminStatisticsDao.getSuccessTeatmentHistoryByAdmin(
    );

    if (claimList) {
      await Promise.all(claimList.map(async (claim) => {
        const treatmentId = claim._id;
        const crwonJobRunCount = !claim.cronjobCount ? 0 : claim.cronjobCount;
        const insuranceId = claim.clientDetails.insuranceId;
        const serviceId = claim.clientInsuranceDetails.insuranceCompanyId;
        const noteId = claim.diagnosisNoteDetails._id;

        const updatedTreatment = await TherapistDao.updateToCrownJobRunCountTreatmentHistoryById(treatmentId, crwonJobRunCount);
        if (crwonJobRunCount <= 1) {
          const result = await InsuranceEp.viewClaimStautsDaily(insuranceId, serviceId, noteId)
        } else {
          let updatedTherapist = await TherapistDao.updateToUnPaidTreatmentHistoryStatusById(treatmentId);
        }
        AppLogger.info('.::update Cliams Status Every Day: ::.' + claim?._id)
      }));
    }
  }


  export async function set2DayClaimCopaymentStatusCheck() {
    const claimList = await AdminStatisticsDao.getAllUnpaidClaims();

      if (claimList) {
        await Promise.all(claimList.map(async (claim) => {
          let isSent = await EmailService.sendCoPaymentEmailForInsurance(
            "Payment Reminder",
            claim?.clientDetails?.email,
            claim?.clientDetails?.firstname,
            moment(claim?.createdAt).tz("America/New_York").format("YYYY/MM/DD"),
            moment(claim?.createdAt).tz("America/New_York").format("HH:mm a"),
            claim?.copayment?.amount as any,
            `https://mylavni.com/stripe-copayment/${claim?._id}`
          );

          await SMSService.sendUnpaidCOPAYSMS(
            claim?.clientDetails?.firstname,
            moment(claim?.createdAt).tz("America/New_York").format("YYYY/MM/DD"),
            moment(claim?.createdAt).tz("America/New_York").format("HH:mm a"),
            claim?.copayment?.amount as any,
            claim?.clientDetails?.primaryPhone,
            `https://mylavni.com/stripe-copayment/${claim?._id}`,
            'admin-ep set2DayClaimCopaymentStatusCheck'
          );

        AppLogger.info('.::check co payment Status Every 2Day: ::.' + claim?._id)
      }));
    }
  }

  export async function setFirstMessageFromTherapistToClient() {
    let frientRequestsListIn30Minutes = await FriendRequestDao.getAllFriendRequestsBefore30Minutes();

    if (frientRequestsListIn30Minutes) {
      await Promise.all(frientRequestsListIn30Minutes.map(async (friendRequest: any) => {
        const clientDetails: any = friendRequest?.clientId
        const therapistDetails: any = friendRequest?.clientId

        await EmailService.sendFriendRequestFromTherapistToClient(
          clientDetails.email,
          therapistDetails.email,
          "Welcome " + clientDetails?.firstname + ", Next steps"
        );
        AppLogger.info('.:: First Message From Therapist To Client Every 30 Minutes ::.' + friendRequest?.clientId)
      }));
    }
  }


  export async function setReminderMessageToClientVerify() {
    let verifyEvry10min = await FriendRequestDao.getAllPendingVerifyAccountsBefore10Minutes();

    if (verifyEvry10min) {
      await Promise.all(verifyEvry10min.map(async (verify: any) => {
        await EmailService.sendVeryfiyLinkToClient(
          verify.email,
          verify.firstname,
          "Lavni Reminder",
          verify._id,
        );

        await SMSService.sendEventSMS(
          `Hi ${verify.firstname ? verify.firstname : "There"}!
          \nThis is lavni's automated assistant.
          \nI saw you signed up but have not verified your email.
          \nPlease click the link below to verify your account.
          \nVerification Link: ${process.env.APP_URL}/client-verify/${verify._id}`,
          verify.primaryPhone
        );
        AppLogger.info('.:: Reminder Message From admin verify To Client Every 30 Minutes ::.' + verify?._id)
      }));
    }
  }

  export async function setReminderMessageToClientNoInsuranceOrNoSubcription() {
    let verifyEvry10min = await FriendRequestDao.getInsuranceOrNoSubcriptionAccountsBefore10Minutes();

    if (verifyEvry10min) {
      await Promise.all(verifyEvry10min.map(async (verify: any) => {
        await EmailService.sendInsuranceAndOutofpocket(
          verify.email,
          verify.firstname,
          "Lavni Reminder",
          verify._id,
        );

        await SMSService.sendEventSMS(
          `Hi ${verify.firstname ? verify.firstname : "There"}!
          \nThis is lavni's automated assistant.
          \nI noticed that you matched therapist.
          \nYou can cover your session with options such as insurance or out of pocket.
          \nPlease sign in.
          \nSignin: ${process.env.APP_URL}/signin`,
          verify.primaryPhone
        );
        AppLogger.info('.:: Reminder Message From admin verify To Client Every 30 Minutes ::.' + verify?._id)
      }));
    }

  }


  export async function setReminderMessageToClientNoInsuranceOrNoSubcription48h() {
    let verifyEvry10min = await FriendRequestDao.getInsuranceOrNoSubcriptionAccountsBefore48Minutes();

    if (verifyEvry10min) {
      await Promise.all(verifyEvry10min.map(async (verify: any) => {
        await SMSService.sendEventSMS(
          `Hi ${verify.firstname ? verify.firstname : "There"}!
          \n\nI hope this message finds you well.
          \nI wanted to follow up to see if you had a chance to review the information.
          \nI sent you about covering your session .
          \nPlease sign in.
          \n\nSignin: ${process.env.APP_URL}/signin`,
          verify.primaryPhone
        );
        AppLogger.info('.:: Reminder Message From admin verify To Client Every 48 hours ::.' + verify?._id)
      }));
    }
  }

  export async function sendMailToUsers(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    const usersList = req.body.usersList;
    const subject = req.body.subject;
    const content = req.body.content;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (!usersList) {
      return res.sendError("No Users");
    }
    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.newsLetterEmails != true) {
        return res.sendError(
          "You don't have permission for News-Letter-Emails!"
        );
      }
    }

    try {
      if (usersList) {
        await Promise.all(usersList.map(async (user: any) => {
          const userId = new mongoose.Types.ObjectId(user);
          const data = await AdminDao.getUserById(userId);
          await EmailService.sendEmailToSelectedUsers(data.email, content, subject);
          AppLogger.info('.:: Send Mail to User ::.' + userId)
        }));
      }

      return res.sendSuccess("Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendSMSToUsers(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    const usersList = req.body.usersList;
    const content = req.body.content;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (!usersList) {
      return res.sendError("No Users");
    }
    if (!content) {
      return res.sendError("No Message body");
    }
    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.newsLetterEmails != true) {
        return res.sendError(
          "You don't have permission for News-Letter-Emails!"
        );
      }
    }

    try {
      if (usersList) {
        await Promise.all(usersList.map(async (user: any) => {
          const userId = new mongoose.Types.ObjectId(user);
          const data = await AdminDao.getUserById(
            userId
          );
          await SMSService.sendEventSMS(
            content,
            data.primaryPhone
          );
          AppLogger.info('.:: Send SMS to User ::.' + userId)
        }));
      }

      return res.sendSuccess("Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getMergeMeetings(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    const noteId = req.body.noteId;
    if (!noteId) {
      return res.sendError("No Notes");
    }
    let spentDuration = 0
    try {
      const meetingIdList = await AdminDao.getDiagnosisNotesById(
        noteId
      );
      if (meetingIdList?.length > 0) {
        for (const meetingID of meetingIdList) {
          const meetingId = new mongoose.Types.ObjectId(meetingID);
          const meetingData = await AdminDao.getMeetingsAndRecording(
            meetingId
          );
          spentDuration += meetingData?.spentDuration || 0;
        }
        return res.sendSuccess(spentDuration, "Success");
      }
      else {
        const singleMeetingId = await AdminDao.getDiagnosisNotesMeetingById(
          noteId
        );
        const meetingID = new mongoose.Types.ObjectId(singleMeetingId);
        const meetingData2 = await AdminDao.getMeetingsAndRecording(
          meetingID
        );
        const spentDurationSingleMeet = meetingData2?.spentDuration
        return res.sendSuccess(spentDurationSingleMeet, "Success");
      }

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateClientDobByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    const userId = req.body.userId;
    const dateOfBirth = req.body.dateOfBirth;
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!userId) {
      return res.sendError("userId is Required");
    }

    try {
      if (userId) {
        // if (user._id.toString() === userId.toString()) {
        const client: DClient = {
          dateOfBirth: dateOfBirth
        };

        let updatedClient = await UserDao.updateUser(userId, client);

        if (updatedClient == null) {
          return res.sendError(
            "Something went wrong. Please try again later."
          );
        }

        return res.sendSuccess(
          updatedClient,
          "Your profile has been updated successfully."
        );

      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function runClaimByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    const insuranceId = req.body.insuranceId;
    const serviceId = req.body.serviceId;
    const noteId = req.body.noteId;

    if (!insuranceId) {
      return res.sendError("insuranceId is Required");
    }

    if (!serviceId) {
      return res.sendError("serviceId is Required");
    }
    if (!noteId) {
      return res.sendError("noteId is Required");
    }

    try {
      const result = await InsuranceEp.viewClaimStautsDaily(insuranceId, serviceId, noteId)
      return res.sendSuccess(result)
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateTherapistPayRateByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const payRateType = req.body.payRateType;
    const percentage = req.body.percentage;
    const therapistId = req.body.therapistId;
    const flatValue = req.body.flatValue;

    if (!payRateType) {
      return res.sendError("pay rate type is required");
    }
    if (payRateType == PayRateTypes.PERCENTAGE_USER) {
      if (!percentage) {
        return res.sendError("percentage is required");
      }
    }
    if (payRateType == PayRateTypes.FLAT_USER) {
      if (!flatValue) {
        return res.sendError("flat value is required");
      }
    }
    const user = await AdminDao.getUserById(therapistId);
    if (user && user.blockedByAdmin) {
      return res.sendError("This user blocked by admin");
    }
    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        const typePayRate: any = {
          type: payRateType,
          flatValue: flatValue,
        }
        const typePercentage: any = {
          type: payRateType,
          percentage: percentage,
        }
        const updateUser: any = {
          payRateType: payRateType == PayRateTypes.PERCENTAGE_USER ? typePercentage : typePayRate,
        };

        let updatedUser = await AdminDao.updateUser(therapistId, updateUser);

        return res.sendSuccess(updatedUser, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function updateClientCopaymentAmountByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const clientId = req.body.clientId;
    const copaymentAmount = req.body.copaymentAmount;

    if (copaymentAmount < 0) {
      return res.sendError("Co payment amount is required");
    }
    const user = await AdminDao.getUserById(clientId);
    const previousCopaymentAmount = user.copaymentAmount;
    if (user && user.blockedByAdmin) {
      return res.sendError("This user blocked by admin");
    }
    try {
      const updateUser: any = {
        copaymentAmount: copaymentAmount,
      };

      let updatedUser = await AdminDao.updateUser(clientId, updateUser);
      let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(clientId, copaymentAmount, previousCopaymentAmount);


      return res.sendSuccess(updatedUser, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function reviewSubmissionByAdminOnBehalfOfClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const adminId = req.user._id;
    const clientId = req.body.clientId;
    const reviewMessage = req.body.reviewMessage;
    const ratingValue = req.body.ratingValue;

    if (!reviewMessage || reviewMessage === null) {
      return res.sendError("Review message is required");
    }
    if (!ratingValue || ratingValue === null) {
      return res.sendError("Rating is required");
    }
    const user = await AdminDao.getUserById(clientId);
    if (user && user.blockedByAdmin) {
      return res.sendError("This user blocked by admin");
    }
    try {
      const fullName = `${user?.firstname || 'Client'} ${user?.lastname || 'User'}`.trim();
      const reviewContent: any = {
        clientId: clientId,
        adminId: adminId,
        name: fullName,
        email: user?.email,
        reviewMessage: reviewMessage,
        ratingValue: ratingValue,
        status: LavniReviewStatus.APPROVED
      };

      let updatedReview = await AdminDao.createOrUpdateClientReviewByAdmin(clientId, reviewContent);

      return res.sendSuccess(updatedReview, "Action Successfully Completed!");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function reviewSubmissionByAdminOnBehalfOfTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const adminId = req.user._id;
    const therapistId = req.body.therapistId;
    const reviewMessage = req.body.reviewMessage;
    const ratingValue = req.body.ratingValue;

    if (!reviewMessage || reviewMessage === null) {
      return res.sendError("Review message is required");
    }
    if (!ratingValue || ratingValue === null) {
      return res.sendError("Rating is required");
    }
    const user = await AdminDao.getUserById(therapistId);
    if (user && user.blockedByAdmin) {
      return res.sendError("This user blocked by admin");
    }
    try {
      const fullName = `${user?.firstname || 'Therapist'} ${user?.lastname || 'User'}`.trim();
      const reviewContent: any = {
        therapistId: therapistId,
        adminId: adminId,
        name: fullName,
        email: user?.email,
        reviewMessage: reviewMessage,
        ratingValue: ratingValue,
        status: LavniReviewStatus.APPROVED
      };

      let updatedReview = await AdminDao.createOrUpdateTherapistReviewByAdmin(therapistId, reviewContent);

      return res.sendSuccess(updatedReview, "Action Successfully Completed!");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getExistingReviewByClientId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = req.params.clientId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        let review = await AdminDao.getClientReview(clientId);
        return res.sendSuccess(review);
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getExistingReviewByTherapistId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapistId = req.params.therapistId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        let review = await AdminDao.getTherapistReview(therapistId);
        return res.sendSuccess(review);
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }


  export async function updateOldTherapistPayRateType(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistList = await AdminDao.getAllTherapistList();
      const updatedTherapists: DTherapist[] = [];

      if (therapistList) {
        for (const therapist of therapistList) {
          let therapistId = therapist._id;

          if (therapistId) {
            let payRateTypeData: any = {
              type: PayRateTypes.FLAT_USER,
              flatValue: 50,
            };

            let therapistData: any = {
              payRateType: payRateTypeData,
            };

            let updatedTherapist = await UserDao.updateUser(therapistId, therapistData);
            updatedTherapists.push(updatedTherapist);
          }
        }

        return res.sendSuccess(updatedTherapists);
      }
      if (therapistList?.length == 0) {
        return res.sendSuccess("No therapist to update pay rate type");
      }
    } catch (error) {
      return res.sendError("Error updating therapist pay rate type");
    }
  }

  export async function updateTherapistPnumberByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.body.userId;
    const priorityNumber = req.body.priorityNumber;
    const pnumber = parseInt(priorityNumber, 10);
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!userId) {
      return res.sendError("userId is Required");
    }

    try {
      const therapistPnum: any = {
        priorityNumber: pnumber
      };

      let updatedTherapist;

      try {
        updatedTherapist = await UserDao.updateUserPriorityNumber(userId, therapistPnum);
      } catch (updateError) {
        if (updateError.message === "Priority number is required and must be a valid number.") {
          return res.sendError("Priority number is required and must be a valid number.");
        }
        else if (updateError.message === "This user is blocked by admin.") {
          return res.sendError("This user is blocked by admin.", 403);
        }
        else if (updateError.message === "Priority number already assigned to another user.") {
          return res.sendError(updateError.message);
        } else {
          return res.sendError("Something went wrong while updating. Please try again later.");
        }
      }

      if (!updatedTherapist) {
        return res.sendError("Something went wrong. Please try again later.");
      }

      return res.sendSuccess(
        updatedTherapist,
        "Priority number has been updated successfully."
      );
    } catch (error) {
      return res.sendError("Unexpected error occurred. Please try again later.");
    }
  }

  export async function updateTherapistBlackTherapyPnumberByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.body.userId;
    const blackTherapyPriorityNumber = req.body.blackTherapyPriorityNumber;
    const pnumber = parseInt(blackTherapyPriorityNumber, 10);
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!userId) {
      return res.sendError("userId is Required");
    }

    try {
      const therapistPnum: any = {
        blackTherapyPriorityNumber: pnumber
      };

      let updatedTherapist;

      try {
        updatedTherapist = await UserDao.updateUserBlackTherapyPriorityNumber(userId, therapistPnum);
      } catch (updateError) {
        if (updateError.message === "Priority number is required and must be a valid number.") {
          return res.sendError("Priority number is required and must be a valid number.");
        }
        else if (updateError.message === "This user is blocked by admin.") {
          return res.sendError("This user is blocked by admin.", 403);
        }
        else if (updateError.message === "Priority number already assigned to another user.") {
          return res.sendError(updateError.message);
        } else {
          return res.sendError("Something went wrong while updating. Please try again later.");
        }
      }

      if (!updatedTherapist) {
        return res.sendError("Something went wrong. Please try again later.");
      }

      return res.sendSuccess(
        updatedTherapist,
        "Priority number has been updated successfully."
      );
    } catch (error) {
      return res.sendError("Unexpected error occurred. Please try again later.");
    }
  }

  export async function updateTherapistRelationshipTherapyPnumberByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.body.userId;
    const relationshipTherapyPriorityNumber = req.body.relationshipTherapyPriorityNumber;
    const pnumber = parseInt(relationshipTherapyPriorityNumber, 10);
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!userId) {
      return res.sendError("userId is Required");
    }

    try {
      const therapistPnum: any = {
        relationshipTherapyPriorityNumber: pnumber
      };

      let updatedTherapist;

      try {
        updatedTherapist = await UserDao.updateUserRelationShipTherapyPriorityNumber(userId, therapistPnum);
      } catch (updateError) {
        if (updateError.message === "Priority number is required and must be a valid number.") {
          return res.sendError("Priority number is required and must be a valid number.");
        }
        else if (updateError.message === "This user is blocked by admin.") {
          return res.sendError("This user is blocked by admin.", 403);
        }
        else if (updateError.message === "Priority number already assigned to another user.") {
          return res.sendError(updateError.message);
        } else {
          return res.sendError("Something went wrong while updating. Please try again later.");
        }
      }

      if (!updatedTherapist) {
        return res.sendError("Something went wrong. Please try again later.");
      }

      return res.sendSuccess(
        updatedTherapist,
        "Priority number has been updated successfully."
      );
    } catch (error) {
      return res.sendError("Unexpected error occurred. Please try again later.");
    }
  }

  export async function updateTherapistMensMentalTherapyPnumberByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.body.userId;
    const mensMentalTherapyPriorityNumber = req.body.mensMentalTherapyPriorityNumber;
    const pnumber = parseInt(mensMentalTherapyPriorityNumber, 10);
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!userId) {
      return res.sendError("userId is Required");
    }

    try {
      const therapistPnum: any = {
        mensMentalTherapyPriorityNumber: pnumber
      };

      let updatedTherapist;

      try {
        updatedTherapist = await UserDao.updateUserMensMentalTherapyPriorityNumber(userId, therapistPnum);
      } catch (updateError) {
        if (updateError.message === "Priority number is required and must be a valid number.") {
          return res.sendError("Priority number is required and must be a valid number.");
        }
        else if (updateError.message === "This user is blocked by admin.") {
          return res.sendError("This user is blocked by admin.", 403);
        }
        else if (updateError.message === "Priority number already assigned to another user.") {
          return res.sendError(updateError.message);
        } else {
          return res.sendError("Something went wrong while updating. Please try again later.");
        }
      }

      if (!updatedTherapist) {
        return res.sendError("Something went wrong. Please try again later.");
      }

      return res.sendSuccess(
        updatedTherapist,
        "Priority number has been updated successfully."
      );
    } catch (error) {
      return res.sendError("Unexpected error occurred. Please try again later.");
    }
  }

  export async function getAllTherapistCount(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

      try {

        const therapistCount = await AdminDao.getAllTherapistsCount();
        const getAllPriorityNumbers = await AdminDao.getAllPriorityNumbers();
        let count = therapistCount

        const data = {
          count: count,
          getAllPriorityNumbers: getAllPriorityNumbers
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function searchAllTherapistsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const gender = req.body.gender;
    const status = req.body.status;
    const isSubscription = req.body.isSubscription;
    const zipCode = req.body.zipCode;
    const state = req.body.state;
    const therapistCategorizationByType = req.body.therapistCategorizationByType;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewAllTherapists != true) {
            return res.sendError(
              "You don't have permission for View Therapists!"
            );
          }
        }
        const result = await AdminDao.searchAllTherapists(
          searchableString,
          limit,
          offset,
          gender,
          status,
          isSubscription,
          zipCode
        );
        const countUser = await AdminDao.getAllTherapistsCount();

        const count = countUser - limit * offset;

        const data = {
          userSet: result,
          count: count,
          totalCount: countUser
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function searchAllReferralEarningsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const rewardType = req.body.rewardType;
    const payStatus = req.body.payStatus;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.referralEarnings != true) {
            return res.sendError(
              "You don't have permission for view referral earnings!"
            );
          }
        }
        const result = await AdminDao.searchAllReferralEarnings(
          searchableString,
          limit,
          offset,
          rewardType,
          payStatus
        );
        const countUser = await AdminDao.getAllReferralEarningsCount();

        const count = countUser - limit * offset;

        const data = {
          userSet: result,
          count: count,
          totalCount: countUser
        };

        return res.sendSuccess(data, "Referral earnings.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function searchAllClientRewardsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const rewardType = req.body.rewardType;
    const payStatus = req.body.payStatus;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.clientRewards != true) {
            return res.sendError(
              "You don't have permission for view client rewards!"
            );
          }
        }
        const result = await AdminDao.searchAllClientRewards(
          searchableString,
          limit,
          offset,
          rewardType,
          payStatus
        );
        const countUser = await AdminDao.getAllClientRewardsCount();

        const count = countUser - limit * offset;

        const data = {
          userSet: result,
          count: count,
          totalCount: countUser
        };

        return res.sendSuccess(data, "Client rewards.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function updateMonthlyPaymentsAndTransaction(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const monthlyPaymentId = req.body.monthlyPaymentId;
    const transactionId = req.body.transactionId;
    const transactionAmount = req.body.transactionAmount;
    const insuaranceCompanyName = req.body.insuaranceCompanyName;

    const errors = validationResult(req);
    if (!monthlyPaymentId) {
      return res.sendError('Monthly payment id is required');
    }
    if (!transactionId) {
      return res.sendError('Transaction id is required');
    }
    if (!transactionAmount) {
      return res.sendError('Transaction amountis required');
    }
    if (!insuaranceCompanyName) {
      return res.sendError('Inusarance company name is required');
    }
    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {

      const monthlyPayment = await AdminDao.getMonthlyPaymentById(monthlyPaymentId);
      if (!monthlyPayment) {
        return res.sendError(`Invalid Monthly payment Id`);
      }
      const transaction = await AdminDao.getTransactionById(transactionId)
      if (!transaction) {
        return res.sendError(`Invalid transaction Id`);
      }

      if (monthlyPayment && monthlyPayment.therapistTransaction) {
        monthlyPayment.therapistTransaction.forEach(therapistTransaction => {
          if (therapistTransaction.recentTransaction !== null) {
            let def = 0;
            therapistTransaction.recentTransaction.forEach((transactionItem: { _id: any; accumulatedBalance: number; transactionAmount: any; insuranceCompany: any; accumulatedTotalEarnings: number; }) => {

              if (transactionItem._id == transactionId) {
                const defValue = (transactionAmount - transactionItem.transactionAmount)
                let newAccumulatedBalance = transactionItem.accumulatedBalance + defValue;
                let newAccumulatedTotalEarnings = transactionItem.accumulatedTotalEarnings + defValue;
                def = newAccumulatedTotalEarnings
                if (transactionItem) {
                  transactionItem.insuranceCompany = insuaranceCompanyName;
                  transactionItem.transactionAmount = transactionAmount;
                  transactionItem.accumulatedBalance = newAccumulatedBalance
                  transactionItem.accumulatedTotalEarnings = newAccumulatedTotalEarnings
                }
              }
            });
            therapistTransaction.recentTransaction.forEach((transactionItem: { _id: any; accumulatedBalance: number; transactionAmount: any; insuranceCompany: any; accumulatedTotalEarnings: number; }) => {
              if (transactionItem._id != transactionId) {
                transactionItem.accumulatedTotalEarnings = def;
              }
            });
          }
        });
      }

      if (transaction) {
        let previousTransactionAmount = transaction?.transactionAmount
        transaction.insuranceCompany = insuaranceCompanyName;
        transaction.transactionAmount = transactionAmount;
        transaction.accumulatedBalance = (transaction?.accumulatedBalance - previousTransactionAmount) + transactionAmount
        transaction.accumulatedTotalEarnings = (transaction?.accumulatedTotalEarnings - previousTransactionAmount) + transactionAmount
      }

      const updateMonthlyPaynemt = await TherapistDao.updateMonthlyPaynemt(monthlyPaymentId, monthlyPayment)
      const updateTransaction = await AdminDao.updateTransaction(transactionId, transaction);

      // let data = {
      //   updateMonthlyPaynemt: updateMonthlyPaynemt ? updateMonthlyPaynemt : null,
      //   updateTransaction: updateTransaction ? updateTransaction : null
      // }
      const updatedMonthlyPayment = await AdminDao.getMonthlyPaymentById(monthlyPaymentId);
      return res.sendSuccess(updatedMonthlyPayment, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }



  export async function deleteTreatmentHistoryByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const noteId = req.body.noteId;
    try {
      let updatedUser = await AdminStatisticsDao.deleteTreatmentHistoryByAdmin(noteId, {
        deleteTreatmentHistory: true,
      });
      if (updatedUser) {
        return res.sendSuccess("Treatment history successfully deleted.", "Treatment history successfully deleted.");
      } else {
        return res.sendError("Error while deleting the treatment history.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateTreatmentHistoryStatusByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const noteId = req.body.noteId;
    const claimStatus = req.body.claimStatus;
    try {
      let updatedUser = await AdminStatisticsDao.updateTreatmentHistoryClaimStatusByAdmin(noteId, {
        claimStatus: claimStatus,
        errorMsg: ""
      });

      if (updatedUser) {
        return res.sendSuccess(updatedUser, "Claim status successfully updated.");
      } else {
        return res.sendError("Error while updating theclaim status.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }



  export async function updateFlagTreatmentHistories() {
    try {
      let treatmentHistories = await AdminStatisticsDao.updateFlagAlltreatmentHistories();
      AppLogger.info(`.:: Update Treatment Histories ::.`);
    } catch (error) {
      console.log("error", error)
    }
  }

  export async function removeTreatmentHistoryFlag(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.body.noteId) {
        return res.sendError("Note ID is required in the request body.")
      }

      const noteId = req.body.noteId;

      let updatedUser = await AdminStatisticsDao.removeTreatmentHistoryFlag(noteId);
      if (updatedUser) {
        return res.sendSuccess("Treatment history flag successfully removed.", "Treatment history flag successfully removed.");
      } else {
        return res.sendError("Error while removing the treatment history flag.");
      }
    } catch (error) {
      return res.sendError("An error occurred while removing the treatment history flag: " + error.message);
    }
  }

  export async function deleteTreatmentHistoryFlag(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const response = await AdminDao.deleteFlagTretmentHistory()
      return res.sendSuccess(response)
    } catch (error) {
      return res.sendError("An error occurred while removing the treatment history flag: " + error.message);
    }
  }


  export async function updateOpenClaim(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapistId = req.body.therapistId;
    const claimOpen = req.body.claimOpen;

    try {
      const updateUser: any = {
        claimOpen: claimOpen,
      };

      let updatedUser = await AdminDao.updateUser(therapistId, updateUser);
      if (claimOpen && claimOpen == true) {
        let treatmentHistoryList = await AdminDao.getPendingSubmitionTreatmentHistoryByTherapistId(therapistId)
        AppLogger.info(`Pending submission for TherapistId: ${therapistId}. TreatmentHistory List, ${treatmentHistoryList}`);
        if (treatmentHistoryList?.length > 0) {
          Promise.all(treatmentHistoryList.map(treatment => {
            InsuranceEp.reSubmitPendingClaims(treatment, res)
          }))
            .then(results => {
              console.log('All claims completed successfully');
            })
            .catch(error => {
              AppLogger.error(`A Claim submission failed. TherapistId: ${therapistId}. Error details: ${error}.`);
              console.error('A Claim submission failed', error);
            });
        }
      }
      return res.sendSuccess(updatedUser)
    } catch (error) {
      return res.sendError("An error occurred while removing the treatment history flag: " + error.message);
    }
  }


  export async function getDetailClaimsStatusOfTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapistId = req.params.therapistId;
    let treatmentHistoryList = await AdminDao.getAllTreatmentHistoryByTherapistId(therapistId)
    return res.sendSuccess(treatmentHistoryList)
  }


  export async function notUpdatedTreatmentHistories(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    try {
      const countUser = await AdminDao.getAllDiagnosisNotes();

      return res.sendSuccess(countUser, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllTherapistsList(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewAllClients != true) {
            return res.sendError(
              "You don't have permission for View Clients!"
            );
          }
        }

        const therapist = await AdminDao.getAllTherapistsList();

        return res.sendSuccess(therapist, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllReminderSms(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    let userRole = req.body.role;
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (userRole == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.reminderSms != true) {
            return res.sendError(
              "You don't have permission for Reminder Sms!"
            );
          }
        }
        const result = await AdminDao.getReminderSms(
          limit,
          offset,
        );

        return res.sendSuccess(result, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function sendReminderSmsToClients() {
    try {
      const clients = await AdminDao.getReminderSmsClientList();
      const matchedClients = clients?.filter(client => client.friendRequests && client.friendRequests.length > 0);
      const noMatchedClients = clients?.filter(client => !client.friendRequests || client.friendRequests?.length == 0);
      const currentDate = new Date();
      const hour = currentDate.getHours();

      let greeting: string;

      if (hour < 12) {
        greeting = "Good morning";
      } else if (hour < 18) {
        greeting = "Good afternoon";
      } else {
        greeting = "Good evening";
      }

      if (matchedClients && matchedClients?.length > 0) {
        await Promise.all(matchedClients.map(async (client) => {
          const appointments = await AdminDao.getPendingUpcommingAppointmentsByClientId(client?._id)
          if (appointments?.length > 0) {
            // no insuarance
            if (!client?.insuranceId || client?.insuranceId == undefined) {
              console.log("matched + no insuarance + has appointment functions=====================A>", client?._id)
              const utcTime = moment.utc(appointments[0]?.start);
              const estTime = utcTime.tz('America/New_York');
              const smsBody = `${greeting}, ${client.firstname},

              \nI'm Marcus, an automated Care Support Coordinator at Lavni, your trusted platform for connecting with therapists who resonate with you and accept your insurance. Visit us at https://mylavni.com.
              \nI'm reaching out to confirm your appointment scheduled for ${estTime.format('YYYY-MM-DD hh:mm A')} with our therapist, ${appointments[0]?.therapistId?.firstname} ${appointments[0]?.therapistId?.lastname}. We're genuinely thrilled and eagerly anticipating your first session with us.
              \nTo finalize your appointment details, could you please confirm whether you'll be using your insurance or opting for a self-pay arrangement?
              \nPlease reply:
              1 for insurance
              2 for out-of-pocket`;

              const clientData: any = {
                reminderSMS: REMINDER_SMS_TYPE?.MATCH_APPOINTMENT_NO_INSUARANCE
              }
              const updateClient = await AdminDao.updateUser(client?._id, clientData)

              const sendSMS = await SMSService.sendReminderSMS(
                smsBody,
                client.primaryPhone
              );
              if (sendSMS) {
                AppLogger.info('.:: MATCH_APPOINTMENT_NO_INSUARANCE Sent ::.' + client?._id)
              } else {
                AppLogger.error('.:: MATCH_APPOINTMENT_NO_INSUARANCE not sent ::.' + client?._id)
              }

              const cleanedSmsBody = smsBody.replace(/\n/g, '');
              const data = {
                clientId: client._id,
                sentSMS: cleanedSmsBody,
                smsType: REMINDER_SMS_TYPE?.MATCH_APPOINTMENT_NO_INSUARANCE
              }
              const saveReminderSMS = await AdminDao.addReminderSMS(data)
            } else {
              // matched + has insuarance + has appointment functions
              console.log("matched + has insuarance + has appointment functions=====================B>", client?._id)
              const utcTime = moment.utc(appointments[0]?.start);
              const estTime = utcTime.tz('America/New_York');
              const smsBody = `${greeting}, ${client.firstname},

              \nI am Marcus, an automated care support coordinator for Lavni, your go-to platform for a therapist you can relate to that accepts your insurance. Visit us at https://mylavni.com.
              \nI was reaching out to confirm your appointment for ${estTime.format('YYYY-MM-DD hh:mm A')} with our therapist ${appointments[0]?.therapistId?.firstname} ${appointments[0]?.therapistId?.lastname}.
              \nYou will get a reminder 15 minutes before your appointment. All you have to do is sign in on our website and click the "Join" button on the pop-up screen to join your session. Your therapist will be there waiting.
              \nPlease don't hesitate to reach out if you have any questions.
              \nWe are genuinely excited about your upcoming appointment and are here to ensure it's a positive experience for you.`;

              const clientData: any = {
                reminderSMS: REMINDER_SMS_TYPE?.MATCH_APPOINTMENT_INSUARANCE
              }
              const updateClient = await AdminDao.updateUser(client?._id, clientData)

              const sendSMS = await SMSService.sendReminderSMS(
                smsBody,
                client.primaryPhone
              );
              if (sendSMS) {
                AppLogger.info('.:: MATCH_APPOINTMENT_INSUARANCE Sent ::.' + client?._id)
              } else {
                AppLogger.error('.:: MATCH_APPOINTMENT_INSUARANCE not sent ::.' + client?._id)
              }

              const cleanedSmsBody = smsBody.replace(/\n/g, '');
              const data = {
                clientId: client._id,
                sentSMS: cleanedSmsBody,
                smsType: REMINDER_SMS_TYPE?.MATCH_APPOINTMENT_INSUARANCE
              }
              const saveReminderSMS = await AdminDao.addReminderSMS(data)
            }
          }
        }));
      }
      if (noMatchedClients) {
        await Promise.all(noMatchedClients.map(async (client) => {
          if (!client?.insuranceId || client?.insuranceId == undefined) {
            // no matched + no insuarance function
            console.log("no matched + no insuarance functions=====================C>", client?._id)
            // const smsBody = `${greeting}, ${client.firstname},
            // \nI am Marcus, an automated care support coordinator for Lavni, your go-to platform for a therapist you can relate to that accepts your insurance. https://mylavni.com
            // \nI was reaching out because you recently signed up on our website, and I just wanted to confirm. Are you still interested in connecting with one of our therapists?
            // \nPlease reply:
            // 1 for yes
            // 2 for no`;

            const latestSmsText =
              `Hi ${client?.firstname ?? ""}, I'm Marcus, your automated Care Support Coordinator at Lavni; your go-to platfom for a therapist you can relate to that accepts your insurance. Visit our website: https://mylavni.com .
            
             I noticed that you recently signed up on our website, and I just wanted to confirm.
             
             Are you still interested in connecting with one of our therapists?
             Please reply:
             1 for Yes
             2 for No`;

            const clientData: any = {
              reminderSMS: REMINDER_SMS_TYPE?.NO_MATCH_NO_INSUARANCE
            }

            const updateClient = await AdminDao.updateUser(client?._id, clientData)

            const sendSMS = await SMSService.sendReminderSMS(
              latestSmsText,
              client.primaryPhone
            );
            if (sendSMS) {
              AppLogger.info('.:: NO_MATCH_NO_INSUARANCE Sent ::.' + client?._id)
            } else {
              AppLogger.error('.:: NO_MATCH_NO_INSUARANCE not sent ::.' + client?._id)
            }

            const cleanedSmsBody = latestSmsText.replace(/\n/g, '');
            const data = {
              clientId: client._id,
              sentSMS: cleanedSmsBody,
              smsType: REMINDER_SMS_TYPE?.NO_MATCH_NO_INSUARANCE
            }
            const saveReminderSMS = await AdminDao.addReminderSMS(data)
          } else {
            //no matched + has insuarance function
            console.log("no matched + has insuarance functions=====================D>", client?._id)
            // const smsBody = `${greeting}, ${client.firstname},
            // \nI am Marcus, an automated care support coordinator for Lavni, your go-to platform for a therapist you can relate to that accepts your insurance. https://mylavni.com
            // \nI was reaching out because you recently signed up on our website, and I just wanted to confirm. Great news! Your insurance covers your session.
            // \nAre you interested in connecting with one of our therapists?
            // \nPlease reply:
            // 1 for yes
            // 2 for no`;

            const latestSmsText =
              `Hi ${client?.firstname ?? ""}, I'm Marcus, your automated Care Support Coordinator at Lavni; your go-to platfom for a therapist you can relate to that accepts your insurance. Visit our website: https://mylavni.com .
            
             I noticed that you recently signed up on our website, and I wanted to let you know some great news—your insurance covers your sessions with us!
             
             Are you interested in connecting with one of our therapists?
             Please reply:
             1 for Yes
             2 for No`;

            const clientData: any = {
              reminderSMS: REMINDER_SMS_TYPE?.NO_MATCH_INSUARANCE
            }
            const updateClient = await AdminDao.updateUser(client?._id, clientData)

            const sendSMS = await SMSService.sendReminderSMS(
              latestSmsText,
              client.primaryPhone
            );
            if (sendSMS) {
              AppLogger.info('.:: NO_MATCH_INSUARANCE Sent ::.' + client?._id)
            } else {
              AppLogger.error('.:: NO_MATCH_INSUARANCE not sent ::.' + client?._id)
            }

            const cleanedSmsBody = latestSmsText.replace(/\n/g, '');
            const data = {
              clientId: client._id,
              sentSMS: cleanedSmsBody,
              smsType: REMINDER_SMS_TYPE?.NO_MATCH_INSUARANCE
            }
            const saveReminderSMS = await AdminDao.addReminderSMS(data)
          }
        }));
      }
    } catch (error) {
      AppLogger.error('Error running reminder sms:', error.message)
    }
  }

  export async function sendReminderSmsToMissed1stAppointmentClients() {
    try {
      const clients = await AdminDao.getNewestClientList();
      if (clients) {
        await Promise.all(clients.map(async (client) => {
          if (client) {
            const now = new Date();
            const appointments = await AdminDao.getAllAppointmentByClientId(client?._id);
            if (appointments && appointments?.length > 0) {
              const meetings = await VideoCallDao.getMeetingByAppoinmentId(appointments[0]?.clientId, appointments[0]?.therapistId?._id);
              const startTime = new Date(appointments[0]?.start);
              const timeDifferenceInMilliseconds = now.getTime() - startTime.getTime();
              const timeDifferenceInMinutes = timeDifferenceInMilliseconds / (1000 * 60);
              const utcTime = moment.utc(appointments[0]?.start);
              const estTime = utcTime.tz('America/New_York');

              if (timeDifferenceInMinutes > 29 && timeDifferenceInMinutes < 80 && appointments[0]?.status != AppointmentStatus.COMPLETED && appointments[0]?.status != AppointmentStatus.REJECTED && meetings <= 0) {
                const smsBody = `I am Marcus, an automated care support coordinator for Lavni, your go-to platform for a therapist you can relate to that accepts your insurance. https://mylavni.com
                \nI was reaching out because you did not show up for your appointment today at ${estTime.format('YYYY-MM-DD hh:mm A')} with our therapist ${appointments[0]?.therapistId?.firstname} ${appointments[0]?.therapistId?.lastname}. Please see our no-show policy here https://mylavni.com/noshow-policy/
                \nAre you still interested in having therapy sessions with us?
                \nPlease reply:
                1 for yes
                2 for no`;

                const clientData: any = {
                  missed1stAppointment: true
                }

                const updateClient = await AdminDao.updateUser(client?._id, clientData);

                const sendSMS = await SMSService.sendReminderSMS(
                  smsBody,
                  client?.primaryPhone
                );
                if (sendSMS) {
                  AppLogger.info('.:: NO_SHOW_FIRST_APPOINTMENT Sent SMS ::.' + client?._id)
                } else {
                  AppLogger.error('.:: NO_SHOW_FIRST_APPOINTMENT not sent SMS ::.' + client?._id)
                }


                const cleanedSmsBody = smsBody.replace(/\n/g, '');
                const data = {
                  clientId: client._id,
                  sentSMS: cleanedSmsBody,
                  smsType: REMINDER_SMS_TYPE?.NO_SHOW_FIRST_APPOINTMENT
                }
                const saveReminderSMS = await AdminDao.addReminderSMS(data)
              }
            }
          }
        }));
      }
    } catch (error) {
      AppLogger.error('Error running reminder sms:', error.message)
    }
  }

  export async function getUnsubmittedClaimsMd() {
    try {
      const claims = await AdminDao.getAllPendingClaimMd();
      if (claims) {
        await Promise.all(claims.map(async (claim) => {
          const claimRes = await InsuranceEp.submitPendingClaimMd(claim);
          AppLogger.info('.::Claim MD update automated for claim ID: ::.' + claim?._id)
        }));
      }
    } catch (error) {
      AppLogger.error('Error running reminder sms:', error.message)
    }
  }

  export async function sendCustomSms(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const reminderId = req.body.reminderId;
    const message = req.body.message;
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!message) {
      return res.sendError('Message body is required');
    }
    if (!reminderId) {
      return res.sendError('Id is invalid');
    }
    try {

      const reminderSMS = await AdminDao.getReminderSmsById(reminderId)

      if (reminderSMS) {
        if (reminderSMS?.clientId) {
          const client = await AdminDao.getClientById(reminderSMS?.clientId)
          if (client && client?.primaryPhone) {
            const smsBody = `${message}`
            await SMSService.sendReminderSMS(
              smsBody,
              client.primaryPhone
            );
            const smsReplyList = reminderSMS?.replySMS || [];
            const smsNewArray = [message];
            const combinedArray = [...smsReplyList, ...smsNewArray];

            const data = {
              replySMS: combinedArray
            };

            const updatedReminder = await AdminDao.UpdateReminderSmsDataByID(reminderId, data);
            return res.sendSuccess(updatedReminder);
          } else {
            return res.sendError('No primary phone number');
          }
        } else {
          return res.sendError('No client ID');
        }
      } else {
        return res.sendError('No Reminder SMS found in this ID');
      }
    } catch (error) {
      return res.sendError("An error occurred while removing the treatment history flag: " + error.message);
    }
  }

  export async function getInsuaranceCompanyByState(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const state = req?.body?.state;

      if (!state) {
        return res.sendError('State is required');
      }

      const isuranceCompaniesByState = await AdminDao.getAllInsuranceCompaniesByState(state);

      if (!isuranceCompaniesByState) {
        return res.sendError("Error while retrieving insurance companies for state");
      }

      return res.sendSuccess(isuranceCompaniesByState, "Insurance companies for state");

    } catch (error) {
      return res.sendError("Error while retrieving insurance companies for state");
    }
  }


  export async function getReminderSmsByClientId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const clientId = String(req.params.clientId);

    let userRole = req.body.role;
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (!clientId) {
      return res.sendError("Invalid Clinet ID.");
    }

    if (userRole == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.reminderSms != true) {
            return res.sendError(
              "You don't have permission for Reminder Sms!"
            );
          }
        }

        const result = await AdminDao.getReminderSmsByClientIdWithPagination(
          limit,
          offset,
          clientId,
        );

        return res.sendSuccess(result, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function checkStripeBlanceAnd15thPayments() {
    let stripeBalance = 0;
    let result = 0;
    let topupAmount = 0;

    try {
      const balance = await stripe.balance.retrieve();
      stripeBalance = balance.available[0].amount / 100;
      result = await TherapistDao.getAllMonthlyPaynemtsLastMonth1stAnd15thByAdmin();
      if (result > stripeBalance) {
        const topupAmount = result - stripeBalance
        const topup = await stripe.topups.create({
          amount: Math.round(topupAmount * 100),
          currency: 'usd',
          description: 'Top-up',
          statement_descriptor: 'Top-up',
        });

        AppLogger.info('Stripe balance: ' + topupAmount);
        await EmailService.sendStripeBlanceIncreeceDevPurpose(
          "Stripe Balance Changes",
          `Your 15th Of Month payment amount is ${result}. Stripe balance is ${stripeBalance}. Top-up amount is ${topupAmount}.`
        );
      }
    } catch (error) {
      AppLogger.error(`check-Stripe-Blance-And-15th-Payments - Error Details: ${error}.`);

      await EmailService.sendStripeBlanceIncreaseErrorsDevPurpose(
        "Stripe Balance Changes Error(s) - 15th",
        `An error occurred while processing Stripe balance and payments.\n\nError: ${error.message}.\n\n15th Of Month payment amount: ${result}\nStripe balance: ${stripeBalance}\nTop-up amount: ${topupAmount}.\n\nFull Error Details: ${error}.`
      );
    }
  }

  export async function checkStripeBlanceAnd1thPayments() {
    let stripeBalance = 0;
    let result = 0;
    let topupAmount = 0;

    try {
      const balance = await stripe.balance.retrieve();
      stripeBalance = balance.available[0].amount / 100;
      result = await TherapistDao.saveAllVerifiedTherapists1stOfMonthByAdmin();
      
      if (result > stripeBalance) {
        const topupAmount = result - stripeBalance
        const topup = await stripe.topups.create({
          amount: Math.round(topupAmount * 100),
          currency: 'usd',
          description: 'Top-up',
          statement_descriptor: 'Top-up',
        });

        AppLogger.info('Stripe balance: ' + topupAmount);
        await EmailService.sendStripeBlanceIncreeceDevPurpose(
          "Stripe Balance Changes",
          `Your 1st Of Month payment amount is ${result}. Stripe balance is ${stripeBalance}. Top-up amount is ${topupAmount}.`
        );
      }
    } catch (error) {
      AppLogger.error(`check-Stripe-Blance-And-1th-Payments - Error Details: ${error}.`);

      await EmailService.sendStripeBlanceIncreaseErrorsDevPurpose(
        "Stripe Balance Changes Error(s) - 1th",
        `An error occurred while processing Stripe balance and payments.\n\nError: ${error.message}.\n\n1th Of Month payment amount: ${result}\nStripe balance: ${stripeBalance}\nTop-up amount: ${topupAmount}.\n\nFull Error Details: ${error}.`
      );
    }
  }



  export async function getAllERAList() {
    try {
      const lastERA = await AdminDao.getLastERAClaim();

      const formData = new URLSearchParams({
        AccountKey: process.env.CLAIM_MD_ACCOUNT_KEY,
        ERAID: lastERA?.eraid ? String(lastERA.eraid) : ''
      });

      const claimValidation = await fetch(process.env.CHANGE_CLAIM_MD_ERALIST, {
        method: "POST",
        body: new URLSearchParams(formData).toString(),
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Accept": "application/json"
        },
      });

      const responseData = await claimValidation.json();
      if (Array.isArray(responseData?.era)) {
        const results = await Promise.all(responseData.era.map(async (eraData: any) => {
          return AdminDao.addERAClaimsMd(eraData);
        }));
      } else {
        console.error('responseData.era is not an array');
      }

      const allERAClaims = await AdminDao.getAllERAPendingClaims();

      if (allERAClaims?.length > 0) {
        try {
          const results = await Promise.all(allERAClaims.map(eraClaim => getERADetails(eraClaim)));
          const successResults = results.filter(result => result.success).map(result => result.data);
          const errorResults = results.filter(result => !result.success);

          if (errorResults.length > 0) {
            console.error('Some claims failed', errorResults);
            AppLogger.error(`Some claims failed Date: ` + new Date());
          }
        } catch (error) {
          console.error('A claim submission failed', error);
          AppLogger.error(`Some claims failed Date: ` + new Date());
        }
      } else {
        AppLogger.error(`Some claims failed Date: ` + new Date());
      }
    } catch (error) {
      AppLogger.error(`An error occurred: ` + new Date());
      console.error("An error occurred:", error);
    }
  }

  export async function getERADetails(eraClaimData: any) {
    try {
      const formData = {
        AccountKey: process.env.CLAIM_MD_ACCOUNT_KEY,
        eraid: eraClaimData?.eraid
      };

      const claimValidation = await fetch(process.env.CHANGE_CLAIM_MD_ERADATA, {
        method: "POST",
        body: new URLSearchParams(formData).toString(),
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Accept": "application/json"
        },
      });

      const responseData = await claimValidation.json();

      if (Array.isArray(responseData?.claim)) {
        const results = await Promise.all(responseData.claim.map(async (eraClaim: any) => {
          return AdminDao.addERAClaimDataMd(eraClaim);
        }));
      } else {
        console.error('responseData.claim is not an array');
      }
      const allERAClaimLists = await AdminDao.getAllERAPendingClaimLists();

      if (allERAClaimLists?.length > 0) {
        try {
          const results = await Promise.all(allERAClaimLists.map(async (eraClaim: any) =>
            await AdminDao.findAndUpdateTreatmentHistory(
              eraClaim?._id,
              eraClaim?.pcn,
              eraClaim?.pat_name_f,
              eraClaim?.pat_name_l,
              eraClaim?.from_dos,
              eraClaim?.claim_received_date,
              eraClaim?.total_paid,
              eraClaim?.charge[0]?.adjustment[0]?.code,
              eraClaimData?._id
            )
          ));
        } catch (error) {
          console.error('A claim submission failed', error);
        }
      }
      return { success: true, data: responseData };
    } catch (error) {
      console.error("An error occurred:", error);
      return { success: false, error: error.message };
    }
  }



  export async function filterTherapistByTimeRange(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const errors = validationResult(req);

    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const gender = req.body.gender;
    const status = req.body.status;
    const isSubscription = req.body.isSubscription;
    const zipCode = req.body.zipCode;
    const state = req.body.state;
    const therapistCategorizationByType = req.body.therapistCategorizationByType;
    const startDate = req.body?.startDate;
    const endDate = req.body?.endDate;
    const selectDays = req.body?.selectDays?.length > 0 ? req.body?.selectDays : [];
    const startTime = req.body?.startTime;
    const endTime = req.body?.endTime;
    const isFilterByDateRange = req.body.isSelectAdvanceFiltering;


    const sDate = moment(startDate, 'YYYY-MM-DD');
    const eDate = moment(endDate, 'YYYY-MM-DD');
    const differenceInDays = eDate.diff(sDate, 'days');

    if (differenceInDays > 30) {
      return res.sendError("Selected date difference need to be less than 30 days")
    }

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const result = await AdminDao.filterTherapistByTimeRange(
        searchableString,
        limit,
        offset,
        isFilterByDateRange,
        gender,
        status,
        isSubscription,
        zipCode,
        startDate,
        endDate,
        selectDays,
        startTime,
        endTime,
      );
      const countUser = await AdminDao.getAllTherapistsCount();

      const count = countUser - limit * offset;

      const data = {
        userSet: result,
        count: count,
      };

      return res.sendSuccess(data, "Success");
    } catch (error) {
      return res.sendError(error);
    }

  }


  export async function getERALists(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const claimStatus = req.body.claimStatus;

    try {
      const allERAClaimLists = await AdminDao.getAllERALists(claimStatus, limit, offset);
      return res.sendSuccess(allERAClaimLists, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllPendingTherapistRequests(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const role = req.user.role;
      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);

      if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.therapistRequests != true) {
            return res.sendError(
              "You don't have permission for view pending therapist requests!"
            );
          }
        }
        const therapistList = await Therapist.find({ registrationApprovalStatus: RegistrationApprovalStatus.PENDING })
          .sort({ createdAt: -1 })
          .skip(offset)
          .limit(limit);

        return res.sendSuccess(therapistList, "Success");
      } else {
        return res.sendError("You don't have permission!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllApprovedTherapistRequests(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const role = req.user.role;
      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);

      if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.therapistRequests != true) {
            return res.sendError(
              "You don't have permission for view approved therapist requests!"
            );
          }
        }
        const therapistList = await Therapist.find({ registrationApprovalStatus: RegistrationApprovalStatus.APPROVED })
          .sort({ createdAt: -1 })
          .skip(offset)
          .limit(limit);

        return res.sendSuccess(therapistList, "Success");
      } else {
        return res.sendError("You don't have permission!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllRejectedTherapistRequests(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);

      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.therapistRequests != true) {
            return res.sendError(
              "You don't have permission for view rejected therapist requests!"
            );
          }
        }
        const therapistList = await Therapist.find({ registrationApprovalStatus: RegistrationApprovalStatus.REJECTED })
          .sort({ createdAt: -1 })
          .skip(offset)
          .limit(limit);

        return res.sendSuccess(therapistList, "Success");
      } else {
        return res.sendError("You don't have permission!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function approveTherapistRequest(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    try {
      const therapistId = req.body.userId;

      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.therapistRequests != true) {
            return res.sendError(
              "You don't have permission for approve therapist request!"
            );
          }
        }
        if (!therapistId) {
          return res.sendError("No userId given.");
        }

        const updatedTherapist = await Therapist.findOneAndUpdate(
          { _id: therapistId },
          { $set: { registrationApprovalStatus: RegistrationApprovalStatus.APPROVED } },
          { new: true }
        );

        if (!updatedTherapist) {
          return res.sendError("No therapist found with given userId.");
        }

        if (updatedTherapist && updatedTherapist.email) {
          await EmailService.sendRegistrationApprovelEmailToTherapist(updatedTherapist, `${updatedTherapist.firstname ?? "Therapist"}, Welcome to Lavni!`);
        }

        if (updatedTherapist && updatedTherapist.primaryPhone) {

          const finalMessageText =
            `Dear ${updatedTherapist.firstname ?? "Therapist"},

         Congrats on passing the initial review! We're thrilled to welcome you to Lavni. Please sign in to your Lavni account to complete your onboarding: ${process.env.APP_URL}/signin .

         Next Steps:

            1. Sign In.
            2. Complete Your Profile.
            3. Review Policies.

         For assistance, email <NAME_EMAIL> . Looking forward to making a positive impact together!

         Warm regards,
         Laura Valentine
         CMO, Lavni, Inc.`;

          await SMSService.sendEventSMS(
            finalMessageText,
            updatedTherapist.primaryPhone
          );
        }

        return res.sendSuccess(updatedTherapist, "Success");
      } else {
        return res.sendError("Invalid user role!");
      }
    } catch (error) {
      return res.sendError(error);
    }

  }

  export async function rejectTherapistRequest(
    req: Request,
    res: Response,
    next: NextFunction
  ) {

    try {
      const therapistId = req.body.userId;

      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.therapistRequests != true) {
            return res.sendError(
              "You don't have permission for reject therapist request!"
            );
          }
        }
        if (!therapistId) {
          return res.sendError("No userId given.");
        }

        const updatedTherapist = await Therapist.findOneAndUpdate(
          { _id: therapistId },
          { $set: { registrationApprovalStatus: RegistrationApprovalStatus.REJECTED } },
          { new: true }
        );

        if (!updatedTherapist) {
          return res.sendError("No therapist found with given userId.");
        }

        return res.sendSuccess(updatedTherapist, "Success");
      } else {
        return res.sendError("Invalid user role!");
      }
    } catch (error) {
      return res.sendError(error);
    }

  }

  export async function getRecentSessionsForClientByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const clientId = new ObjectId(req.params.clientId);
      const therapistId = new ObjectId(req.params.therapistId);
      const limit = 5;
      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.viewMeetingsAndRecordings != true) {
            return res.sendError(
              "You dont't have permission for Meetings & Recordings!"
            );
          }
        }

        const client = await AdminDao.getClientById(clientId);
        if (!client) {
          return res.sendError("No client found with given userId.");
        }
        const therapist = await AdminDao.getUserById(therapistId);
        if (!therapist) {
          return res.sendError("No therapist found with given userId.");
        }

        let sessions = await AdminDao.getRecentSessionsForSelectedClient(client._id, therapist._id, limit);
        return res.sendSuccess(sessions, "Recent meeting details.");
      } else {
        return res.sendError("Invalid user role!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function searchInsuranceDocumentsByCondition(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);
      const searchableStringClient = req.body.clientSearchableString;
      const searchableStringTherapist = req.body.therapistSearchableString;
      const searchableInsuranceCompany = req.body.searchableInsuranceCompany;

      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }

      if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
        try {
          if (req.user.role == UserRole.SUB_ADMIN) {
            const ownUser = await UserDao.getUserByUserId(req.user._id);
            if (ownUser.adminPermission.approvalQueue != true) {
              return res.sendError(
                "You don't have permission for insurance documents approval section!"
              );
            }
          }
          const result = await AdminDao.searchInsuranceDocAndFilter(
            searchableStringClient,
            searchableStringTherapist,
            searchableInsuranceCompany,
            limit,
            offset
          );

          return res.sendSuccess(result, "Filtered client insurance documents.");
        } catch (error) {
          return res.sendError(error);
        }
      } else {
        return res.sendError("No permission to access!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function searchDocumentDownloads(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);
      let searchableStringClient = req.body.clientSearchableString;
      let searchableStringTherapist = req.body.therapistSearchableString;
      let searchableInsuranceCompany = req.body.searchableInsuranceCompany;

      let searchedNameClient = null;

      if (searchableStringClient && searchableStringClient.trim() != "") {
        searchableStringClient = searchableStringClient.trim();
        let seacrhItem = searchableStringClient.replace(/\s/g, "");
        searchedNameClient = searchableStringClient != null ? new RegExp(`^${seacrhItem}`, "i") : null;
      }

      const clientNameQuery =
        searchedNameClient
          ? {
            $and: [
              {
                $or: [
                  { 'client.firstname': searchedNameClient },
                  { 'client.lastname': searchedNameClient },
                  { 'client.email': searchedNameClient },
                  { 'client.fullName': searchedNameClient },
                ],
              },
            ],
          }
          : {};

      let searchedNameTherapist = null;

      if (searchableStringTherapist && searchableStringTherapist.trim() != "") {
        searchableStringTherapist = searchableStringTherapist.trim();
        let seacrhItem = searchableStringTherapist.replace(/\s/g, "");
        searchedNameTherapist = searchableStringTherapist != null ? new RegExp(`^${seacrhItem}`, "i") : null;
      }

      const therapistNameQuery =
        searchedNameTherapist
          ? {
            $and: [
              {
                $or: [
                  { 'therapist.firstname': searchedNameTherapist },
                  { 'therapist.lastname': searchedNameTherapist },
                  { 'therapist.email': searchedNameTherapist },
                  { 'therapist.fullName': searchedNameTherapist },
                ],
              },
            ],
          }
          : {};

      let insuranceCompanyQuery: any = {};

      if (searchableInsuranceCompany) {
        insuranceCompanyQuery['insuranceCompanies._id'] = new Types.ObjectId(searchableInsuranceCompany);
      }

      const searchResult = await Client.aggregate([
        {
          $sort: { createdAt: -1 }
        },
        {
          $match: {
            $or: [
              { blockedByAdmin: false },
              { blockedByAdmin: { $exists: false } }
            ]
          }
        },
        {
          $lookup: {
            from: "meetings",
            let: { clientId: "$_id" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$clientId", "$$clientId"] },
                      { $eq: ["$callingStatus", "COMPLETED"] }
                    ]
                  }
                }
              },
              {
                $project: { _id: 1, therapistId: 1 }
              },
              {
                $lookup: {
                  from: "transactions",
                  let: { meetingId: "$_id" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $and: [
                            { $eq: ["$meetingId", "$$meetingId"] },
                            { $eq: ["$type", "EARNING"] }
                          ]
                        }
                      }
                    },
                    {
                      $project: { _id: 1, eligibleForPayment: 1, paidStatus: 1, therapistId: 1 }
                    }
                  ],
                  as: "transactions"
                }
              }
            ],
            as: "meetings"
          }
        },
        {
          $addFields: {
            allTransactions: {
              $reduce: {
                input: "$meetings",
                initialValue: [],
                in: { $concatArrays: ["$$value", "$$this.transactions"] }
              }
            }
          }
        },
        {
          $addFields: {
            filteredTransactions: {
              $filter: {
                input: "$allTransactions",
                as: "transaction",
                cond: {
                  $and: [
                    {
                      $or: [
                        { $eq: ["$$transaction.eligibleForPayment", true] },
                        { $not: { $ifNull: ["$$transaction.eligibleForPayment", false] } }
                      ]
                    },
                    { $ne: ["$$transaction.eligibleForPayment", false] },
                    { $ne: ["$$transaction.paidStatus", "PAID"] }
                  ]
                }
              }
            },
            filteredHoldTransactions: {
              $filter: {
                input: "$allTransactions",
                as: "transaction",
                cond: {
                  $and: [
                    { $eq: ["$$transaction.eligibleForPayment", false] },
                    { $ne: ["$$transaction.paidStatus", "PAID"] }
                  ]
                }
              }
            }
          }
        },
        {
          $lookup: {
            from: "clinicalassesments",
            let: { clientId: "$_id" },
            pipeline: [
              { $match: { $expr: { $eq: ["$clientId", "$$clientId"] } } },
              { $sort: { createdAt: 1 } },
              {
                $lookup: {
                  from: "clinicalassesmentversions",
                  let: { clinicalAssesmentId: "$_id" },
                  pipeline: [
                    { $match: { $expr: { $eq: ["$clinicalAssesmentId", "$$clinicalAssesmentId"] } } },
                    { $sort: { createdAt: -1 } },
                    { $project: { _id: 1, reasonForEdit: 1, versionCreatedAt: 1 } }
                  ],
                  as: "clinicalAssesmentVersions"
                }
              },
              { $project: { _id: 1, therapistId: 1, createdAt: 1, clinicalAssesmentVersions: 1 } }
            ],
            as: "clinicalAssesmentDetails"
          }
        },
        {
          $lookup: {
            from: "digitalassessments",
            let: { clientId: "$_id" },
            pipeline: [
              { $match: { $expr: { $eq: ["$clientId", "$$clientId"] } } },
              { $sort: { createdAt: 1 } },
              {
                $lookup: {
                  from: "digitalassessmentversions",
                  let: { digitalAssessmentId: "$_id" },
                  pipeline: [
                    { $match: { $expr: { $eq: ["$digitalAssessmentId", "$$digitalAssessmentId"] } } },
                    { $sort: { createdAt: -1 } },
                    { $project: { _id: 1, reasonForEdit: 1, versionCreatedAt: 1 } }
                  ],
                  as: "digitalAssessmentVersions"
                }
              },
              { $project: { _id: 1, therapistId: 1, createdAt: 1, digitalAssessmentVersions: 1 } }
            ],
            as: "digitalAssessmentDetails"
          }
        },
        {
          $lookup: {
            from: "therapyplans",
            let: { clientId: "$_id" },
            pipeline: [
              { $match: { $expr: { $eq: ["$clientId", "$$clientId"] } } },
              { $sort: { createdAt: 1 } },
              {
                $lookup: {
                  from: "therapyplanversions",
                  let: { therapyPlanId: "$_id" },
                  pipeline: [
                    { $match: { $expr: { $eq: ["$therapyPlanId", "$$therapyPlanId"] } } },
                    { $sort: { createdAt: -1 } },
                    { $project: { _id: 1, reasonForEdit: 1, versionCreatedAt: 1 } }
                  ],
                  as: "therapyPlanVersions"
                }
              },
              { $project: { _id: 1, therapistId: 1, createdAt: 1, therapyPlanVersions: 1 } }
            ],
            as: "therapyPlanDetails"
          }
        },
        {
          $lookup: {
            from: "uploaddocuments",
            let: { clientId: "$_id" },
            pipeline: [
              { $match: { $expr: { $eq: ["$clientId", "$$clientId"] } } },
              { $sort: { createdAt: -1 } },
              {
                $lookup: {
                  from: "uploads",
                  localField: "uploadDocumentId",
                  foreignField: "_id",
                  as: "uploadDetails"
                }
              },
              { $unwind: "$uploadDetails" },
              {
                $project: {
                  _id: 0,
                  therapistId: 1,
                  uploadDocumentId: {
                    _id: "$uploadDetails._id",
                    originalName: "$uploadDetails.originalName",
                    createdAt: "$uploadDetails.createdAt"
                  }
                }
              }
            ],
            as: "uploadDocumentsDetails"
          }
        },
        {
          $lookup: {
            from: "insurances",
            let: { clientId: "$_id", primaryInsuranceId: "$insuranceId" },
            pipeline: [
              { $match: { $expr: { $eq: ["$clientId", "$$clientId"] } } },
              {
                $lookup: {
                  from: "insurancecompanies",
                  localField: "insuranceCompanyId",
                  foreignField: "_id",
                  as: "insuranceCompanyDetails"
                }
              },
              { $unwind: "$insuranceCompanyDetails" },
              {
                $lookup: {
                  from: "authorizationforms",
                  let: { insuranceCompanyId: "$insuranceCompanyDetails._id" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $and: [
                            { $eq: ["$clientId", "$$clientId"] },
                            { $eq: ["$insuranceCompanyId", "$$insuranceCompanyId"] }
                          ]
                        }
                      }
                    },
                    { $project: { _id: 1, authFormType: 1, therapistId: 1, createdAt: 1 } },
                    { $sort: { createdAt: -1 } }
                  ],
                  as: "authorizationForms"
                }
              },
              {
                $project: {
                  _id: "$insuranceCompanyDetails._id",
                  isPrimary: { $eq: ["$_id", "$$primaryInsuranceId"] },
                  authorizationForms: 1,
                  memberId: "$subscriber.memberId"
                }
              }
            ],
            as: "insuranceCompanies"
          }
        },
        {
          $addFields: {
            allTherapistIds: {
              $setUnion: [
                "$clinicalAssesmentDetails.therapistId",
                "$digitalAssessmentDetails.therapistId",
                "$therapyPlanDetails.therapistId",
                "$uploadDocumentsDetails.therapistId",
                "$filteredTransactions.therapistId",
                "$filteredHoldTransactions.therapistId",
                {
                  $reduce: {
                    input: "$insuranceCompanies.authorizationForms",
                    initialValue: [],
                    in: { $concatArrays: ["$$value", "$$this.therapistId"] }
                  }
                }
              ]
            }
          }
        },

        {
          $lookup: {
            from: "users",
            let: { therapistIds: "$allTherapistIds" },
            pipeline: [
              { $match: { $expr: { $in: ["$_id", "$$therapistIds"] } } },
              { $project: { _id: 1, firstname: 1, lastname: 1, email: 1, gender: 1, dateOfBirth: 1, insuranceId: 1 } }
            ],
            as: "therapistDetails"
          }
        },
        {
          $unwind: "$allTherapistIds"
        },
        {
          $lookup: {
            from: "treatmenthistories",
            let: { 
              clientId: "$_id",
              therapistId: "$allTherapistIds"
            },
            pipeline: [
              { 
                $match: { 
                  $expr: { 
                    $and: [
                      { $eq: ["$clientId", "$$clientId"] },
                      { $eq: ["$therapistId", "$$therapistId"] }
                    ]
                  }
                }
              },
              { $project: { _id: 1, therapistId: 1 } }
            ],
            as: "treatmentHistories"
          }
        },
        {
          $group: {
            _id: { clientId: "$_id", therapistId: "$allTherapistIds" },
            clientInfo: { $first: "$$ROOT" },
            meetingCount: {
              $sum: {
                $size: {
                  $filter: {
                    input: "$meetings",
                    as: "meeting",
                    cond: { $eq: ["$$meeting.therapistId", "$allTherapistIds"] }
                  }
                }
              }
            },
            sessionsCount: {
              $sum: {
                $size: {
                  $filter: {
                    input: "$treatmentHistories",
                    as: "history",
                    cond: { $eq: ["$$history.therapistId", "$allTherapistIds"] }
                  }
                }
              }
            },
            therapistInfo: {
              $first: {
                $filter: {
                  input: "$therapistDetails",
                  as: "therapist",
                  cond: { $eq: ["$$therapist._id", "$allTherapistIds"] }
                }
              }
            },
            clinicalAssesments: {
              $push: {
                $filter: {
                  input: "$clinicalAssesmentDetails",
                  as: "assessment",
                  cond: { $eq: ["$$assessment.therapistId", "$allTherapistIds"] }
                }
              }
            },
            digitalAssesments: {
              $push: {
                $filter: {
                  input: "$digitalAssessmentDetails",
                  as: "digital",
                  cond: { $eq: ["$$digital.therapistId", "$allTherapistIds"] }
                }
              }
            },
            therapyPlans: {
              $push: {
                $filter: {
                  input: "$therapyPlanDetails",
                  as: "plan",
                  cond: { $eq: ["$$plan.therapistId", "$allTherapistIds"] }
                }
              }
            },
            uploadDocuments: {
              $push: {
                $filter: {
                  input: "$uploadDocumentsDetails",
                  as: "uploadDoc",
                  cond: { $eq: ["$$uploadDoc.therapistId", "$allTherapistIds"] }
                }
              }
            },
            insuranceCompanies: { $first: "$insuranceCompanies" },
            hasTransaction: {
              $push: {
                $filter: {
                  input: "$filteredTransactions",
                  as: "releaseFilter",
                  cond: { $eq: ["$$releaseFilter.therapistId", "$allTherapistIds"] }
                }
              }
            },
            hasHoldTransaction: {
              $push: {
                $filter: {
                  input: "$filteredHoldTransactions",
                  as: "holdFilter",
                  cond: { $eq: ["$$holdFilter.therapistId", "$allTherapistIds"] }
                }
              }
            }
          }
        },
        {
          $project: {
            _id: 0,
            createdAtDate: '$clientInfo.createdAt',
            finalTherapistId: '$therapistInfo._id',
            meetingCount: 1,
            sessionsCount: 1,
            client: {
              _id: '$clientInfo._id',
              firstname: '$clientInfo.firstname',
              lastname: '$clientInfo.lastname',
              fullName: { $concat: ["$clientInfo.firstname", " ", "$clientInfo.lastname"] },
              email: '$clientInfo.email',
              gender: '$clientInfo.gender',
              dateOfBirth: '$clientInfo.dateOfBirth',
              insuranceId: '$clientInfo.insuranceId',
              memberId: {
                $let: {
                  vars: {
                    primaryInsurance: {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: "$insuranceCompanies",
                            as: "ins",
                            cond: { $eq: ["$$ins.isPrimary", true] }
                          }
                        },
                        0
                      ]
                    }
                  },
                  in: { $ifNull: ["$$primaryInsurance.memberId", ""] }
                }
              }
            },
            therapist: {
              $let: {
                vars: {
                  therapist: { $arrayElemAt: ["$therapistInfo", 0] }
                },
                in: {
                  _id: "$$therapist._id",
                  firstname: "$$therapist.firstname",
                  lastname: "$$therapist.lastname",
                  fullName: {
                    $concat: [
                      { $ifNull: ["$$therapist.firstname", ""] },
                      { $cond: [{ $and: ["$$therapist.firstname", "$$therapist.lastname"] }, " ", ""] },
                      { $ifNull: ["$$therapist.lastname", ""] }
                    ]
                  },
                  email: "$$therapist.email",
                  gender: "$$therapist.gender",
                  dateOfBirth: "$$therapist.dateOfBirth"
                }
              }
            },
            docs: {
              clinicalassessment: {
                $map: {
                  input: {
                    $reduce: {
                      input: "$clinicalAssesments",
                      initialValue: [],
                      in: { $concatArrays: ["$$value", "$$this"] }
                    }
                  },
                  as: "clinicalAssesment",
                  in: {
                    _id: "$$clinicalAssesment._id",
                    createdAt: "$$clinicalAssesment.createdAt",
                    versions: "$$clinicalAssesment.clinicalAssesmentVersions"
                  }
                }
              },
              digitalassessment: {
                $map: {
                  input: {
                    $reduce: {
                      input: "$digitalAssesments",
                      initialValue: [],
                      in: { $concatArrays: ["$$value", "$$this"] }
                    }
                  },
                  as: "digitalAssesment",
                  in: {
                    _id: "$$digitalAssesment._id",
                    createdAt: "$$digitalAssesment.createdAt",
                    versions: "$$digitalAssesment.digitalAssessmentVersions"
                  }
                }
              },
              therapyplans: {
                $map: {
                  input: {
                    $reduce: {
                      input: "$therapyPlans",
                      initialValue: [],
                      in: { $concatArrays: ["$$value", "$$this"] }
                    }
                  },
                  as: "therapyPlan",
                  in: {
                    _id: "$$therapyPlan._id",
                    createdAt: "$$therapyPlan.createdAt",
                    versions: "$$therapyPlan.therapyPlanVersions"
                  }
                }
              }
            },
            uploadDocuments: {
              $map: {
                input: {
                  $reduce: {
                    input: "$uploadDocuments",
                    initialValue: [],
                    in: { $concatArrays: ["$$value", "$$this"] }
                  }
                },
                as: "uploadDoc",
                in: {
                  uploadDocumentId: {
                    _id: "$$uploadDoc.uploadDocumentId._id",
                    originalName: "$$uploadDoc.uploadDocumentId.originalName",
                    createdAt: "$$uploadDoc.uploadDocumentId.createdAt"
                  }
                }
              }
            },
            insuranceCompanies: {
              $map: {
                input: "$insuranceCompanies",
                as: "company",
                in: {
                  _id: "$$company._id",
                  isPrimary: "$$company.isPrimary",
                  authorizationForms: {
                    $let: {
                      vars: {
                        authForm: {
                          $arrayElemAt: [
                            {
                              $filter: {
                                input: "$$company.authorizationForms",
                                as: "form",
                                cond: { $eq: ["$$form.therapistId", "$_id.therapistId"] }
                              }
                            },
                            0
                          ]
                        }
                      },
                      in: {
                        _id: "$$authForm._id",
                        authFormType: "$$authForm.authFormType"
                      }
                    }
                  }
                }
              }
            },
            hasTransactions: {
              $gt: [
                {
                  $size: {
                    $filter: {
                      input: "$hasTransaction",
                      as: "transactionArray",
                      cond: { $ne: [{ $size: "$$transactionArray" }, 0] }
                    }
                  }
                },
                0
              ]
            },
            hasHoldTransactions: {
              $gt: [
                {
                  $size: {
                    $filter: {
                      input: "$hasHoldTransaction",
                      as: "holdTransactionArray",
                      cond: { $ne: [{ $size: "$$holdTransactionArray" }, 0] }
                    }
                  }
                },
                0
              ]
            }
          }
        },
        {
          $sort: {
            createdAtDate: -1,
            finalTherapistId: -1
          }
        },
        {
          $match: {
            $and: [
              clientNameQuery,
              therapistNameQuery
            ]
          }
        },
        { $skip: offset },
        { $limit: limit }
      ]);

      if (!searchResult) {
        return res.sendError("Something went wrong !");
      }

      return res.sendSuccess(searchResult, "Filtered documents.");

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function uploadInsurancePdfByAdmin(req: Request, res: Response, next: NextFunction) {
    const insuranceDocApprovalId = req.params.insuranceDocApprovalId;
    const uploadCategory = req.params.uploadCategory;

    let specificFormType: string;

    if (uploadCategory === UploadCategory.CLINICAL_ASSESSMENT) {
      specificFormType = 'clinicalAssessmentUploadId';
    } else if (uploadCategory === UploadCategory.THERAPY_PLAN) {
      specificFormType = 'therapyPlanUploadId';
    } else if (uploadCategory === UploadCategory.AUTHORIZATION_FORM) {
      specificFormType = 'authorizationFormUploadId';
    } else {
      return res.sendError("Wrong upload destination provided!");
    }

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateValidationRules(req, cb);
      },
      filename: function (req, file, cb) {
        const extension = path.extname(file.originalname);
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + extension);
      }
    });

    async function updateValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) => cb(error, "destination"));
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    const upload = multer({ storage: storage }).single("file");

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + " ");
        }

        const uploadPdf: any = req.file;

        if (uploadPdf) {
          let uploadedInsurancePdf = null;
          let signRequired: boolean = false;

          if (req.body.signRequired !== undefined) {
            signRequired = req.body.signRequired;
          }

          const data: DUpload = {
            originalName: uploadPdf.originalname.replace(/ /g, ""),
            name: uploadPdf.filename,
            type: uploadPdf.mimetype,
            path: uploadPdf.path,
            fileSize: uploadPdf.size,
            extension: path.extname(uploadPdf.originalname),
            category: uploadCategory,
            signRequired: signRequired,
          };

          try {
            uploadedInsurancePdf = await UploadDao.createUpload(data);

            if (!uploadedInsurancePdf) {
              return res.sendError("Error while uploading the PDF.");
            }
          } catch (error) {
            return res.sendError("Error while saving uploaded PDF.");
          }

          try {
            const updateFormId = {
              [specificFormType]: uploadedInsurancePdf?._id,
            };

            const updatedInsuranceDocApproval = await AdminDao.updateInsuranceDocApprovalsById(
              insuranceDocApprovalId,
              updateFormId
            );

            if (!updatedInsuranceDocApproval) {
              return res.sendError("An error occurred while updating Insurance Doc Approvals table!");
            }

            return res.sendSuccess(updatedInsuranceDocApproval, "Successfully uploaded.");
          } catch (error) {
            return res.sendError("Error while updating Insurance Doc Approvals.");
          }
        } else {
          return res.sendError("No PDF file found in the request.");
        }
      });
    } catch (error) {
      return res.sendError("An unexpected error occurred.");
    }
  }

  export async function getUploadedInsuranceDocStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const insuranceDocApprovalId = Types.ObjectId(req.params.insuranceDocApprovalId);

      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You don't have permission for view or take an action for insurance documents approval section!"
            );
          }
        }
        const documentData = await InsuranceDocApproval.findById(insuranceDocApprovalId).select('clinicalAssessmentUploadId therapyPlanUploadId authorizationFormUploadId');
        if (documentData == null) {
          return res.sendError("No record found for the given Id.");
        }
        return res.sendSuccess(documentData, "Uploaded Insurance Document Status");
      } else {
        return res.sendError("You don't have permission!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function adminApprovalForInsuranceDocumentsFaxing(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const insuranceDocApprovalId = Types.ObjectId(req.body.insuranceDocApprovalId);
      const adminApprovalStatus = req.body.adminApprovalStatus;

      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You don't have permission for view or take an action for insurance documents approval section!"
            );
          }
        }
        const insuranceDocApproval = await InsuranceDocApproval.findOneAndUpdate(
          { _id: insuranceDocApprovalId },
          { $set: { adminApprovalStatus: adminApprovalStatus } },
          { new: true }
        );

        if (insuranceDocApproval == null) {
          return res.sendError("Admin approval has been failed!");
        }

        AdminEp.sendFaxToInsurance().catch((e) => {
          AppLogger.error(`Fax Service | Error sending fax. InsuranceDocApproval record Id: ${insuranceDocApprovalId}. E-details: ${e.message}`);
        });

        return res.sendSuccess(insuranceDocApproval, "Admin approval has been successful. A fax will be sent to the insurance company.");
      } else {
        return res.sendError("You don't have permission!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendReminderEmailToTherapistRegardingIncompletedForm(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistId = Types.ObjectId(req.body.therapistId);
      const clientId = Types.ObjectId(req.body.clientId);
      const formType = req.body.formType;
      const formName = req.body.formName;

      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }

      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You don't have permission for view or take an action for insurance documents approval section!"
            );
          }
        }

        const client = await AdminDao.getClientById(clientId);
        if (!client) {
          return res.sendError("No client found with given userId.");
        }
        const therapist = await TherapistDao.getUserById(therapistId);
        if (!therapist) {
          return res.sendError("No therapist found with given userId.");
        }

        if (
          formType === UploadCategory.CLINICAL_ASSESSMENT ||
          formType === UploadCategory.THERAPY_PLAN ||
          formType === UploadCategory.AUTHORIZATION_FORM
        ) {
          let data = await EmailService.sendIncompleteFormSubmissionReminder(therapist, client, formName, `Reminder: Please Complete the ${formName} for Submission`);
          AppLogger.info(`Send reminder mail to therapist regarding incompleted ${formName}. Therapist: ${therapistId}. Client: ${clientId}`);
          return res.sendSuccess(data, "Email sent successfully!");
        } else {
          return res.sendError("Invalid form type provided.");
        }
      } else {
        return res.sendError("You don't have permission!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function checkPendingApprovalsAndSendRemindersToAdmin() {
    try {
      // let pendingReached24Hours = await AdminDao.getAllPendingAdminApprovalsReached24Hours();
      // if (pendingReached24Hours) {
      //   await Promise.all(pendingReached24Hours.map(async (record: any) => {
      //     await EmailService.sendEmailToAdminForPendingApprovalReach24Hours(
      //       record?.therapistId?.firstname,
      //       record?.therapistId?.lastname,
      //       record?.clientId?.firstname,
      //       record?.clientId?.lastname,
      //       record?.insuranceCompanyId?.insuranceCompany,
      //       `Reminder: Pending Approval for ${record?.therapistId?.firstname} All Documents`
      //     );
      //     AppLogger.info('A reminder email send admin to check therapist approval, that reached more than 24 hours. InsuranceDocApproval record id: ' + record?._id);
      //   }));
      // }
    } catch (error) {
      AppLogger.info("An error occured sending the reminder email to admin, which 24-hour deadline for approval is approaching.");
    }
  }

  export async function sendFaxToInsurance() {
    try {
      const records = await InsuranceDocApproval.find({
        adminApprovalStatus: SubmissionApprovalStatus.APPROVED,
        clinicalAssessmentUploadId: { $exists: true, $ne: null },
        therapyPlanUploadId: { $exists: true, $ne: null },
        authorizationFormUploadId: { $exists: true, $ne: null },
        messageId: { $exists: false },
        messageStatus: { $exists: false }
      });

      if (records.length > 0) {
        const resentFaxes = await Promise.all(
          records.map(async (record) => {
            const { _id } = record;
            try {
              const documentStats = await AdminDao.getUploadedInsuranceDocStatistics(_id);

              const clinicalAssessmentFileName = documentStats.clinicalAssessmentUploadId
                ? documentStats.clinicalAssessmentUploadId.name
                : null;
              const therapyPlanFileName = documentStats.therapyPlanUploadId
                ? documentStats.therapyPlanUploadId.name
                : null;
              const authorizationFormFileName = documentStats.authorizationFormUploadId
                ? documentStats.authorizationFormUploadId.name
                : null;
              const fax = documentStats.insuranceCompanyId ? documentStats.insuranceCompanyId.fax : null;
              const faxNumber = fax ? fax.replace(/[()\-\s]/g, '') : null;

              if (clinicalAssessmentFileName && therapyPlanFileName && authorizationFormFileName && faxNumber) {
                const faxInfo = await FaxService.sendFax(
                  clinicalAssessmentFileName,
                  therapyPlanFileName,
                  authorizationFormFileName,
                  faxNumber
                );

                if (faxInfo && faxInfo.id) {

                  const {
                    id: messageId,
                    to: [{ recipientId, phoneNumber: toPhoneNumber, messageStatus: toMessageStatus }],
                    from: { phoneNumber: fromPhoneNumber },
                    messageStatus,
                    faxPageCount,
                    readStatus,
                    creationTime,
                    lastModifiedTime
                  } = faxInfo;

                  const updatedInsuranceDocApproval = await InsuranceDocApproval.findOneAndUpdate(
                    { _id },
                    {
                      $set: {
                        messageId,
                        messageStatus,
                        recipientId,
                        toPhoneNumber,
                        toMessageStatus,
                        fromPhoneNumber,
                        readStatus,
                        faxPageCount,
                        creationTime,
                        lastModifiedTime
                      }
                    },
                    { new: true, useFindAndModify: false }
                  );

                  if (!updatedInsuranceDocApproval) {
                    AppLogger.error(`Fax Service | FAX sent. Message Id: ${messageId}. But Error updating database record for InsuranceDocApproval record Id: ${_id}.`);
                  } else {
                    AppLogger.info(`Fax Service | Fax sent successfully. Updated InsuranceDocApproval record Id: ${_id} with Message Id: ${messageId}.`);
                  }
                } else {
                  AppLogger.error(`Fax Service | Failed to send fax for record Id: ${_id}.`);
                }
              } else {
                AppLogger.warn(`Fax Service | Missing insurance document names or fax number for record Id: ${_id}.`);
              }
            } catch (error) {
              AppLogger.error(`Fax Service | Error processing fax for record Id: ${_id}. Error details: ${error.message}`);
            }
          })
        );
        AppLogger.info(`Fax Service | Fax operation completed.`);
      }
    } catch (error) {
      AppLogger.error(`Fax Service | Error during fax sending process. Error details: ${error.message}`);
    }
  }

  export async function checkFaxStatus() {
    try {
      const records = await InsuranceDocApproval.find({
        messageId: { $exists: true },
        $or: [
          {
            $and: [
              { messageStatus: 'Sent' },
              { readStatus: 'Unread' }
            ]
          },
          { messageStatus: 'Queued' }
        ]
      });

      if (records.length > 0) {
        const updatePromises = records.map(async (record) => {
          const { _id, messageId, messageStatus, readStatus } = record;

          try {
            const updatedStatus = await FaxService.getUpdatedFaxStatus(messageId);

            if (updatedStatus && (updatedStatus?.messageStatus !== messageStatus || updatedStatus?.readStatus !== readStatus)) {
              AppLogger.info(`Fax Service | Updating InsuranceDocApproval record Id: ${_id}.`);

              return await InsuranceDocApproval.findOneAndUpdate(
                { _id },
                {
                  $set: {
                    messageStatus: updatedStatus?.messageStatus,
                    readStatus: updatedStatus?.readStatus
                  }
                },
                { new: true, useFindAndModify: false }
              );
            } else {
              AppLogger.info(`Fax Service | No status change for InsuranceDocApproval record Id: ${_id}.`);
              return record;
            }
          } catch (error) {
            AppLogger.error(`Fax Service | Error updating status for record Id: ${_id}. Error details: ${error.message}`);
            return record;
          }
        });
        const updatedRecords = await Promise.all(updatePromises);
        AppLogger.info(`Fax Service | Fax statuses checking process completed!.`);
      }
    } catch (error) {
      AppLogger.error(`Fax Service | Error checking fax statuses. Error details: ${error.message}`);
    }
  }

  export async function resendFaxesToInsurance() {
    try {
      const records = await InsuranceDocApproval.find({
        messageId: { $exists: true },
        messageStatus: 'SendingFailed'
      });

      if (records.length > 0) {
        const resentFaxes = await Promise.all(records.map(async (record) => {
          const { _id, messageId } = record;

          try {
            const resentFax = await FaxService.resendFax(messageId);

            if (!resentFax) {
              AppLogger.warn(`Fax Service | Failed to resend fax for record ID: ${_id}`);
              return null;
            }

            const {
              id,
              to: [{ recipientId, phoneNumber: toPhoneNumber, messageStatus: toMessageStatus }],
              from: { phoneNumber: fromPhoneNumber },
              messageStatus,
              faxPageCount,
              readStatus,
              creationTime,
              lastModifiedTime
            } = resentFax;

            const updatedRecord = await InsuranceDocApproval.findOneAndUpdate(
              { _id },
              {
                $set: {
                  messageId: id,
                  messageStatus,
                  recipientId,
                  toPhoneNumber,
                  toMessageStatus,
                  fromPhoneNumber,
                  readStatus,
                  faxPageCount,
                  creationTime,
                  lastModifiedTime
                },
              },
              { new: true, useFindAndModify: false }
            );

            AppLogger.info(`Fax Service | Successfully resent fax and updated record ID: ${_id} with old messageId: ${messageId} to new messageId: ${id} and status: ${messageStatus}`);
            return updatedRecord;
          } catch (error) {
            AppLogger.error(`Fax Service | Error resending fax for record ID: ${_id}. Message id: ${messageId}. Error details: ${error.message}`);
            return null;
          }
        }));
        AppLogger.info(`Fax Service | Fax resend operation completed.`);
      }
    } catch (error) {
      AppLogger.error(`Fax Service | Error during fax resend operation. Error details: ${error.message}`);
    }
  }

  export async function getEraListForClaim(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const treatmentId = req.body.treatmentId;

      if (!treatmentId || !mongoose.Types.ObjectId.isValid(treatmentId)) {
        return res.sendError("Invalid treatment Id");
      }

      let prevTreatmentIdDetails = await TreatmentHistory.findById(treatmentId);

      if (!prevTreatmentIdDetails) {
        return res.sendError("Invalid treatment Id");
      }

      if (!prevTreatmentIdDetails?.pcn) {
        return res.sendError("No pcn found for this Id");
      }

      const pcnFrom = prevTreatmentIdDetails?.pcn;

      const pcnForSearch = new RegExp(`^${pcnFrom}$`, 'i');

      const finalData = await ERAClaimMdList.aggregate([
        {
          $match: {
            pcn: pcnForSearch
          }
        },
        {
          $project: {
            pcn: 1,
            from_dos: 1,
            claim_received_date: 1,
            pat_name_f: 1,
            pat_name_l: 1,
            total_paid: 1,
            total_charge: 1,
            status: 1,
            charge: 1,
          }
        },
        {
          $sort: { createdAt: -1 },
        },
      ]);

      if (!finalData) {
        return res.sendError("Error happen when retrieving data");
      }

      return res.sendSuccess(finalData, "Era data list.");

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function releaseOrHoldPaymentForSpecificTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = req.body.clientId;
    const therapistId = req.body.therapistId;
    const actionType = req.body.actionType;

    if (!clientId || !mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid client Id");
    }

    if (!therapistId || !mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid therapist Id");
    }

    if (!actionType) {
      return res.sendError("Action type is required");
    }

    try {
      const matchedMeetings = await AdminDao.getMeetingByIds(clientId, therapistId);

      if (!matchedMeetings || matchedMeetings.length === 0) {
        return res.sendError("No meetings found.");
      }

      if (actionType === "hold") {

        const eligibleTransactions = matchedMeetings.map(async (meeting) => {
          const meetingId: StringOrObjectId = meeting._id;

          const transactions = await Transaction.find({
            meetingId: meetingId,
            therapistId: meeting.therapistId,
            type: TransactionType.EARNING,
            paidStatus: { $in: [null, "PENDING"] },
            $or: [
              { eligibleForPayment: { $eq: true } },
              { eligibleForPayment: { $exists: false } },
            ],
          } as any);

          const updateEligibility = transactions.map((transaction) =>
            AdminDao.updateEligibilityForPayment(transaction._id, {
              eligibleForPayment: false,
            })
          );

          return Promise.all(updateEligibility);
        });
        const data = await Promise.all(eligibleTransactions);
        return res.sendSuccess(data, "Payment hold.");

      } else if (actionType === "release") {

        const currentDate = new Date();
        const thisMonthStart = startOfMonth(currentDate);
        const thisMonth15th = addDays(thisMonthStart, 15);
        const thisMonthEnd = endOfMonth(currentDate);

        let paymentReleaseDate: Date;

        if (isAfter(currentDate, thisMonth15th)) {
          paymentReleaseDate = addDays(thisMonthEnd, 1);
        } else {
          paymentReleaseDate = thisMonth15th;
        }

        const eligibleTransactions = matchedMeetings.map(async (meeting) => {
          const meetingId: StringOrObjectId = meeting._id;

          const transactions = await Transaction.find({
            meetingId: meetingId,
            therapistId: meeting.therapistId,
            type: TransactionType.EARNING,
            paidStatus: { $in: [null, "PENDING"] },
            eligibleForPayment: { $exists: true, $eq: false },
          } as any);

          const updateEligibility = transactions.map((transaction) =>
            AdminDao.updateEligibilityForPayment(transaction._id, {
              eligibleForPayment: true,
              paymentReleaseDate: paymentReleaseDate,
            })
          );

          return Promise.all(updateEligibility);
        });
        const data = await Promise.all(eligibleTransactions);
        return res.sendSuccess(data, "Payment released.");

      } else {
        return res.sendError("Something went wrong. You can only choose to either release or hold the payment. Please ensure that the payment status is set appropriately before proceeding.");
      }
    } catch (error) {
      AppLogger.error(`Release or Hold Payment for Specific Therapist - Action type: ${actionType}. Error Details: ${error}`);
      return res.sendError(error.message || `An error occurred while ${actionType} payment.`);
    }
  }

  export async function searchMatchClientsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const gender = req.body.gender;
    const status = req.body.status;
    const isSubscription = req.body.isSubscription;
    const zipCode = req.body.zipCode;
    const state = req.body.state;
    const therapistCategorizationByType = req.body.therapistCategorizationByType;
    const therapistId = req.body.therapistId;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewAllClients != true && ownUser.adminPermission.statistics != true && ownUser.adminPermission.createAppointmentAdmin != true) {
        return res.sendError(
          "You don't have permission!"
        );
      }
    }

    if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN) {
      try {
        const therapist = await UserDao.getUserById(therapistId)
        if (!therapist) {
          return res.sendError("Incorrect therapist Id!");
        }
        const friendRequestList = await AdminDao.getFriendRequestByTherapistId(therapistId);
        const stringifiedFriendRequests: string[] = [];

        friendRequestList.forEach((request) => {
          stringifiedFriendRequests.push(request.clientId.toString());
        });

        const result = await AdminDao.searchClientsByTherapistIdFriends(
          searchableString,
          limit,
          offset,
          gender,
          status,
          isSubscription,
          zipCode,
          stringifiedFriendRequests
        );

        const data = {
          userSet: result,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function searchMatchedTherapistsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const gender = req.body.gender;
    const status = req.body.status;
    const isSubscription = req.body.isSubscription;
    const zipCode = req.body.zipCode;
    const state = req.body.state;
    const therapistCategorizationByType = req.body.therapistCategorizationByType;
    const clientId = req.body.clientId;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }
    if (req.user.role == UserRole.SUB_ADMIN) {
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if (ownUser.adminPermission.viewAllClients != true && ownUser.adminPermission.statistics != true && ownUser.adminPermission.createAppointmentAdmin != true) {
        return res.sendError(
          "You don't have permission!"
        );
      }
    }

    if (req.user.role == UserRole.SUPER_ADMIN || UserRole.ADMIN) {
      try {
        const client = await UserDao.getUserById(clientId)
        if (!client) {
          return res.sendError("Incorrect client Id!");
        }
        const friendRequestList = await AdminDao.getFriendRequestByClientId(clientId);
        const stringifiedFriendRequests: string[] = [];

        friendRequestList.forEach((request) => {
          stringifiedFriendRequests.push(request.therapistId.toString());
        });

        const result = await AdminDao.searchMatchedTherapistsByClientIdFriends(
          searchableString,
          limit,
          offset,
          gender,
          status,
          isSubscription,
          zipCode,
          stringifiedFriendRequests
        );

        const data = {
          userSet: result,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function getAppointmentAuthTokenByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const allowedRoles: UserRole[] = [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.SUB_ADMIN];

      if (!allowedRoles.includes(req.user.role as UserRole)) {
        return res.sendError("No permission to access!");
      }

      const user = await UserDao.getUserByUserId(req.user._id);
      if (!user) {
        return res.sendError("User not authenticated.");
      }

      let data;
      if (req.user.role === UserRole.SUPER_ADMIN) {
        data = {
          appointmentAuthToken: user.appointmentAuthToken,
          appointmentAuthTokenExpiredAt: user.appointmentAuthTokenExpiredAt,
        };
      } else {
        data = {
          appointmentAuthToken: "Not available",
          appointmentAuthTokenExpiredAt: "Not available",
        };
      }

      return res.sendSuccess(data, "Success");
    } catch (error) {
      return res.sendError(`An unexpected error occurred. Error: ${error.message}`);
    }
  }

  export async function generateAppointmentAuthTokenByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.SUPER_ADMIN) {

        if (!req.user || !req.user._id) {
          return res.sendError("Unauthorized access. User not authenticated.");
        }

        const userId = req.user._id;
        const { validityPeriod } = req.body;
        const expiresIn = validityPeriod ? `${validityPeriod}d` : "30d";

        const user = await UserDao.getUserByUserId(userId);
        if (!user) {
          return res.sendError("User not found.");
        }

        const authToken = generateAuthToken(user._id, expiresIn);
        if (!authToken) {
          return res.sendError("Failed to generate the auth token. Try again.");
        }

        const now = new Date();
        const expirationDate = new Date(now.getTime() + parseInt(validityPeriod) * 24 * 60 * 60 * 1000);

        await UserDao.updateUser(userId, {
          appointmentAuthToken: authToken,
          appointmentAuthTokenExpiredAt: expirationDate,
        });

        const data = {
          appointmentAuthToken: authToken,
          appointmentAuthTokenExpiredAt: expirationDate
        };
        return res.sendSuccess(data, "Auth token generated successfully.");
      } else {
        return res.sendError("No permission to access!");
      }
    } catch (error) {
      return res.sendError(`An unexpected error occurred. Error: ${error.message}`);
    }
  }

  export function generateAuthToken(userId: string, expiresIn: string): string {
    const payload = { userId };
    const secretKey = process.env.JWT_SECRET;

    try {
      return jwt.sign(payload, secretKey, { expiresIn });
    } catch (error) {
      throw new Error("Failed to generate JWT token.");
    }
  }

  export async function addStateByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.SUPER_ADMIN) {

        if (!req.user || !req.user._id) {
          return res.sendError("Unauthorized access. User not authenticated.");
        }
        const userId = req.user._id;

        const state = req.body.state;
    const therapistCategorizationByType = req.body.therapistCategorizationByType;

        if(!state || state == "") {
          return res.sendError("State cannot empty or null");
        }

        const addStateRes = await AdminDao.addStateByAdmin(state);
        if (addStateRes.success){
          return res.sendSuccess(addStateRes?.state, "State successfully added by admin")
        } else {
          return res.sendError(`Error: ${addStateRes?.error}`)
        }
        
      } else {
        return res.sendError("Only Admin has access to do add states");
      }
    } catch (error) {
      return res.sendError(`An unexpected error occurred while adding states. Error: ${error.message}`);
    }
  }

  export async function deleteStateByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.SUPER_ADMIN) {

        if (!req.user || !req.user._id) {
          return res.sendError("Unauthorized access. User not authenticated.");
        }
        const stateId = req.body.stateId;

        if(!stateId || stateId == "") {
          return res.sendError("State Id cannot empty or null to delete state");
        }

        const addStateRes = await AdminDao.deleteStateByAdmin(stateId);
        if (addStateRes.success){
          return res.sendSuccess(addStateRes?.state, "State successfully deleted by admin")
        } else {
          return res.sendError(`Error occured while deleting state. error: ${addStateRes?.error}`)
        }
        
      } else {
        return res.sendError("Only Admin has access to do delete states");
      }
    } catch (error) {
      return res.sendError(`An unexpected error occurred while deleting states. Error: ${error.message}`);
    }
  }

  export async function submitClaimMd(req: Request, res: Response) {
    try {
      const { therapistId, clientIds } = req.body;
      
      if (!therapistId || !clientIds || !Array.isArray(clientIds)) {
        return res.status(400).json({
          success: false,
          message: "Invalid request parameters. therapistId and clientIds array are required."
        });
      }
  
      const treatmentHistoryList = await AdminDao.getPendingSubmissionTreatmentHistoryByTherapistAndClientIds(therapistId, clientIds);
      AppLogger.info(`Pending submission for TherapistId: ${therapistId} and clientIds: ${clientIds}. TreatmentHistory List: ${treatmentHistoryList.length} items`);
  
      if (treatmentHistoryList?.length > 0) {
        await Promise.all(treatmentHistoryList.map(treatment => 
          InsuranceEp.reSubmitPendingClaims(treatment, res)
        ))
        .then(() => {
          AppLogger.info(`All claims completed successfully for therapist ${therapistId}`);
          return res.status(200).json({
            success: true,
            message: "All claims processed successfully"
          });
        })
        .catch(error => {
          AppLogger.error(`A Claim submission failed. TherapistId: ${therapistId}. Error details: ${error}.`);
          return res.status(500).json({
            success: false,
            message: "One or more claim submissions failed",
            error: error.message
          });
        });
      } else {
        return res.status(200).json({
          success: true,
          message: "No pending claims found for processing"
        });
      }
    } catch (error) {
      AppLogger.error(`Error in submitClaimMd: ${error}`);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message
      });
    }
  }

  export async function getAllStatesByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.SUPER_ADMIN) {

        if (!req.user || !req.user._id) {
          return res.sendError("Unauthorized access. User not authenticated.");
        }

        const addStateRes = await AdminDao.getAllStatesByAdmin();
        if (addStateRes.success){
          return res.sendSuccess(addStateRes?.states, "States successfully retrieved by admin")
        } else {
          return res.sendError(`Error occured while retrieving state. error: ${addStateRes?.error}`)
        }
        
      } else {
        return res.sendError("Only Admin has access to retrieve all states");
      }
    } catch (error) {
      return res.sendError(`An unexpected error occurred while retrieving states. Error: ${error.message}`);
    }
  }

  /**
   * Hàm này xử lý việc submit claim trực tiếp từ giao diện admin
   * @param req Request chứa thông tin claim
   * @param res Response
   */
  export async function submitAdminClaim(req: Request, res: Response) {
    try {
      const claimData = req.body;
      
      if (!claimData) {
        return res.status(400).json({
          success: false,
          message: "Claim data is required"
        });
      }

      // Format data for claim.md
      const payload = {
        claim_form: 1500,
        payer_name: claimData.organizationName || "",
        payerid: claimData.tradingPartnerServiceId || "",
        accept_assign: "Y",
        employment_related: "N",
        
        // Insured (client) information
        ins_name_f: claimData.firstname || "",
        ins_name_l: claimData.lastname || "",
        ins_addr_1: claimData.address1 || "",
        ins_city: claimData.city || "",
        ins_dob: claimData.dateOfBirth || "",
        ins_sex: claimData.gender ? (claimData.gender.toLowerCase() === "male" ? "M" : "F") : "",
        ins_state: convertState(claimData.state) || "",
        ins_zip: claimData.postalCode || "",
        ins_number: claimData.memberId || "",
        
        // Provider info
        bill_taxonomy: "251S00000X",
        place_of_service_1: claimData.placeOfService || "10",
        prov_name_f: claimData.providerFirstname || "",
        prov_name_l: claimData.providerLastname || "",
        prov_npi: claimData.providerNPI || "",
        prov_taxonomy: claimData.taxonomyCode || "",
        
        // Generate random PCN key of length 20
        pcn: Array(20).fill(0).map(() => 
          "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"[Math.floor(Math.random() * 36)]
        ).join(''),
        
        // Claim details
        charge_1: parseFloat(claimData.chargeAmount),
        pat_rel: "18",
        total_charge: parseFloat(claimData.chargeAmount),
        claimNumber: claimData.claimNumber || new Date().getTime().toString(),
        proc_code_1: claimData.proc_code_1 || claimData.procCode || "",
        mod1_1: claimData.modifier || "",
        diag_1: claimData.diagnosisCode ? claimData.diagnosisCode.replace(".", "") : "",
        diag_2: claimData.secondaryDiagnosisCode ? claimData.secondaryDiagnosisCode.replace(".", "") : "",
        from_date_1: claimData.serviceDate || new Date().toLocaleDateString('en-US', {month: '2-digit', day: '2-digit', year: '2-digit'}),
        
        // Billing entity details
        bill_name: "Lavni Inc",
        bill_addr_1: "804 South Garnett St.",
        bill_city: "Henderson",
        bill_state: "NC",
        bill_zip: "27536",
        bill_npi: "**********",
        bill_phone: "**********",
        bill_taxid: "*********",
        bill_taxid_type: "E",
        
        // Additional details
        diag_ref_1: "A",
        units_1: 1,
        
        // Patient info (same as insured for self)
        pat_name_f: claimData.firstname || "",
        pat_name_l: claimData.lastname || "",
        pat_addr_1: claimData.address1 || "",
        pat_city: claimData.city || "",
        pat_state: convertState(claimData.state) || "",
        pat_zip: claimData.postalCode || "",
        pat_dob: claimData.dateOfBirth || "",
        pat_sex: claimData.gender ? (claimData.gender.toLowerCase() === "male" ? "M" : "F") : ""
      };

      // Create CSV file
      const os = require('os');
      const path = require('path');
      const fs = require('fs');
      const csv = require('csv-writer').createObjectCsvWriter;
      
      // Create temporary directory for CSV file
      const tempDir = path.join(os.tmpdir(), 'lavni_claims');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      const outputFilePath = path.join(tempDir, `claim_${Date.now()}.csv`);
      
      // Write CSV
      const csvWriter = csv({
        path: outputFilePath,
        header: Object.keys(payload).map(key => ({ id: key, title: key }))
      });
      
      await csvWriter.writeRecords([payload]);
      console.log(`CSV file created at ${outputFilePath}`);
      
      // Submit to claim.md
      const FormData = require('form-data');
      const axios = require('axios');
      
      const formData = new FormData();
      formData.append('AccountKey', process.env.CLAIM_MD_ACCOUNT_KEY || "");
      formData.append('File', fs.createReadStream(outputFilePath), {
        filename: path.basename(outputFilePath),
        contentType: 'text/csv'
      });
      
      const response = await axios.post(
        process.env.CHANGE_HEALTHCARE_CLAIM_MD_UPLOAD || "https://svc.claim.md/services/upload/",
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Accept': 'application/xml'
          }
        }
      );
      
      // Check response and determine status
      const responseText = response.data || "";
      const expectedMessage = "Received 1 claims in file:";
      const submissionStatus = responseText.includes(expectedMessage) ? "Success" : "Failure";
      
      // Log to database using direct import
      const AdminSubmitClaimByJotFormModel = require('../models/admin-submit-claim-jotform-model').AdminSubmitClaimByJotFormModel;
      
      try {
        console.log("Logging to adminsubmitclaimbyjotforms collection...");
        const saved = await AdminSubmitClaimByJotFormModel.create({
          clientId: claimData.clientId || null,
          therapistId: claimData.therapistId || null,
          memberId: claimData.memberId || "",
          submission_status: submissionStatus,
          response: responseText,
          payload: payload,
          claimData: claimData,
          submittedBy: req.user?._id || null,
          submittedAt: new Date()
        });
        console.log("Saved claim log to DB with ID:", saved._id);
      } catch (dbError) {
        console.error("Error saving to database:", dbError);
        // Still continue to return success even if DB logging fails
      }
      
      // Cleanup
      try {
        fs.unlinkSync(outputFilePath);
      } catch (err) {
        console.error("Error deleting temp file:", err);
      }
      
      return res.status(200).json({
        success: true,
        message: `Claim submission ${submissionStatus.toLowerCase()}`,
        data: {
          submissionStatus,
          response: responseText
        }
      });
      
    } catch (error) {
      console.error("Error submitting admin claim:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while submitting claim",
        error: error.message
      });
    }
  }

}
