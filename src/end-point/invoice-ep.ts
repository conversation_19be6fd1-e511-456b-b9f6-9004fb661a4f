import { NextFunction, Request, Response } from "express";
import { validationResult } from "express-validator";
import { Types } from "mongoose";
import { ClientDao } from "../dao/client-dao";
import { InvoiceDao } from "../dao/invoice-dao";
import { UserRole } from "../models/user-model";

export namespace InvoiceEp {
  export async function getPaymentHistoryByClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const clientId = req.params.clientId;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        let paymentHistoryList = await InvoiceDao.getInvoiceListByClientId(
          Types.ObjectId(clientId),
          limit,
          offset
        );

        return res.sendSuccess(paymentHistoryList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function getCurrentUserPaymentHistory(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.CLIENT) {
      try {
        let paymentHistoryList = await InvoiceDao.getInvoiceListByClientId(
          req.user._id,
          limit,
          offset
        );

        return res.sendSuccess(paymentHistoryList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }
}
