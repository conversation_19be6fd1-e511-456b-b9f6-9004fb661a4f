import { NextFunction, Request, Response } from "express";
import * as fs from "fs";
const https = require("https");
import * as http from 'http';
import { InstagramFeedDao } from "../dao/instagram-feed-dao";
const path = require('path');
const sharp = require('sharp');
var cron = require('node-cron');
import { AppLogger } from "../common/logging";

export namespace InstagramFeedEp {
  export async function fetchAndSaveInstagramFeed() {
    try {
        const accessToken = process.env.INSTAGRAM_FEED_ACCESS_TOKEN;;
        const apiUrl = `https://graph.instagram.com/me/media?fields=id,caption,media_url,media_type,permalink&access_token=${accessToken}&limit=12`;
  
        https.get(apiUrl, async (instagramRes:any) => {
          let data = '';
  
        instagramRes.on('data', (chunk: string | Buffer) => {
          data += chunk;
        });
        
        // When the response is complete
        instagramRes.on('end', async () => {
          if (instagramRes?.statusCode === 200) {
            const feedData = JSON.parse(data).data;
            const existingImages = new Set()
            const deletedInstagramFeeds = await InstagramFeedDao.deleteAllInstagramFeed();

            const uploadsFolderPath = 'uploads';
            const folderName = 'INSTAGRAM_FEED';
            const instagramFeedFolderPath = path.join(uploadsFolderPath, folderName);

            if (!fs.existsSync(instagramFeedFolderPath)) {
              fs.mkdirSync(instagramFeedFolderPath);
            }

            const promises = feedData.map( async (post: any) => {
              if (post.media_type === 'IMAGE' && post.media_url) {
                
                const instagramData = {
                    instagramId: post.id,
                    path: `/instagramFeed/${post.id}.jpg`,
                    mediaUrl: post.permalink,
                    caption: post.caption
                }
                
                const uploadResponse = await InstagramFeedDao.createInstagramFeed(instagramData)
                if(uploadResponse._id){
                    
                    const imageFileName = `uploads/INSTAGRAM_FEED/${post.id}.jpg`;
                    const imageRes = await fetchImage(post.media_url);
                    existingImages.add(imageFileName);

                    if (!fs.existsSync(imageFileName)) {
                      const imageBuffer = await sharp(imageRes).resize({ width: 216, height: 270 }).toBuffer();
                      fs.writeFileSync(imageFileName, imageBuffer);

                      AppLogger.info(`Saved image to ${imageFileName}`);
                    } else {
                      AppLogger.info(`Image ${imageFileName} already exists, skipping.`)
                    }
                }
              }
            })

            await Promise.all(promises);

            const filesInFolder = fs.readdirSync('uploads/INSTAGRAM_FEED');
            for (const file of filesInFolder) {
              if (!existingImages.has(`uploads/INSTAGRAM_FEED/${file}`)) {
                fs.unlinkSync(`uploads/INSTAGRAM_FEED/${file}`);
              }
            }
          } else {
            console.error('Error fetching Instagram feed:', instagramRes?.statusMessage);
            // return res.status(instagramRes.statusCode).json({ error: 'Error fetching Instagram feed' });
          }
        });
          
        })
    } catch (error) {
      AppLogger.error('Error fetching Instagram feed:', error.message)
      // return res.status(500).json({ error: 'Internal server error' });
    }
  }

  export async function fetchImage(imageUrl: any) {
    return new Promise<Buffer>((resolve, reject) => {
      const request = https.get(imageUrl, (imageRes: any) => {
        if (imageRes.statusCode !== 200) {
          reject(new Error(`Failed to fetch image. Status code: ${imageRes?.statusCode}`));
          return;
        }

        const imageData: Uint8Array[] = [];
        imageRes.on('data', (chunk: Uint8Array) => {
          imageData.push(chunk);
        });

        imageRes.on('end', () => {
          const imageBuffer = Buffer.concat(imageData);
          resolve(imageBuffer);
        });
      });

      request.on('error', (error: Error) => {
        reject(error);
      });
    });
  }

  export async function getInstagramFeed(req: Request, res: Response, next: NextFunction) {
    try {
      const instagramFeeds = await InstagramFeedDao.getAllInstagramFeed();
      return res.sendSuccess(instagramFeeds, "Success")
      
    } catch (error) {
      console.log(error);
      
    }
  }

  export async function runInstagramFeedCronJob(req: Request, res: Response, next: NextFunction) {
    try {
    await fetchAndSaveInstagramFeed();
    return res.sendSuccess("success")  
    } catch (error) {
      console.log(error);
      
    }
  }

}
