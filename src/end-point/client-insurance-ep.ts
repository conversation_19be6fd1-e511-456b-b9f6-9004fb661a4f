import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import User from "../schemas/user-schema";
import Insurance from "../schemas/insurance-schema";
import InsuranceCompany from "../schemas/Insurance-company-schema";

export namespace ClientInsuranceEp {
  /**
   * API to get insurance company name from clientId
   * Process:
   * 1. Get insuranceId from users table
   * 2. Get insuranceCompanyId from insurances table
   * 3. Get insurance company information from insurancecompanies table
   */
  export async function getInsuranceCompanyByClientId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const { clientId } = req.body;

    if (!clientId) {
      return res.sendError("Client ID is required");
    }

    try {
      // Step 1: Get user information to obtain insuranceId
      const user = await User.findById(clientId).select("insuranceId").lean();
      
      if (!user) {
        return res.sendError("Client not found");
      }

      if (!user.insuranceId) {
        return res.sendError("Client has no insurance");
      }

      // Step 2: Find insurance with _id = insuranceId to get insuranceCompanyId
      const insurance = await Insurance.findById(user.insuranceId).select("insuranceCompanyId").lean();
      
      if (!insurance) {
        return res.sendError("Insurance information not found");
      }

      if (!insurance.insuranceCompanyId) {
        return res.sendError("Insurance company not found");
      }

      // Step 3: Find insuranceCompany with _id = insuranceCompanyId
      const insuranceCompany = await InsuranceCompany.findById(insurance.insuranceCompanyId).lean();
      
      if (!insuranceCompany) {
        return res.sendError("Insurance company details not found");
      }

      return res.sendSuccess({
        insuranceCompany: insuranceCompany.insuranceCompany,
        coPayment: insuranceCompany.coPayment
      }, "Insurance company information retrieved successfully");
      
    } catch (error) {
      console.error('Error in getInsuranceCompanyByClientId:', error);
      return res.sendError('Server Error');
    }
  }
} 