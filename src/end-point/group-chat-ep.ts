var mongoose = require("mongoose");
import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import { UploadCategory } from "./user-ep";
import multer = require("multer");
import { DUpload } from "../models/upload-model";
import * as path from "path";
import { UploadDao } from "../dao/upload-dao";
import { DGroupChat } from "../models/group-chat-model";
import { GroupChatDao } from "../dao/group-chat-dao";

var fs = require("fs");

export namespace GroupChatEp {
  export async function createGroupChat(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapistId = req.user._id;
    const uploadCategory = UploadCategory.GROUP_ICON;

    const storage = multer.diskStorage({
        destination: async (req, FileRes, cb) => {
          await groupChatValidationRules(req, cb);
        },
      });

    async function groupChatValidationRules(req: any, cb: any) {
        const destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

        try {
            let groupDetails = JSON.parse(req.body.groupDetails);
            
            if(!groupDetails.groupTitle || typeof groupDetails.groupTitle !== "string"){
                return cb(Error("Group title is required."), null);
            }

            if(!groupDetails.groupType || typeof groupDetails.groupType !== "string"){
                return cb(Error("Group type is required."), null);
            }

            if(groupDetails.groupType && groupDetails.groupType == "password"){
                if(!groupDetails.password || typeof groupDetails.password !== "string"){
                    return cb(Error("Password is required."), null);
                }
            }

            fs.access(destination, (error: any) => {
                if (error) {
                  return fs.mkdir(destination, (error: any) =>
                    cb(error, "destination")
                  );
                } else {
                  return cb(null, destination);
                }
              });
        } catch (error) {
            return cb(Error(error), null);
        }

    } 
    
    const upload = multer({ storage: storage }).single("groupIcon");

    try {
        upload(req, res, async function (error: any){
            if(error){
                return res.sendError(error + " ");
            } else {
                let groupDetails = JSON.parse(req.body.groupDetails);
                let isValid = true;

                if (!req.file){
                    
                    if(!groupDetails.groupTitle || typeof groupDetails.groupTitle !== "string"){
                        isValid = false;
                        return res.sendError("Group title is required.");
                    }
        
                    if(!groupDetails.groupType || typeof groupDetails.groupType !== "string"){
                        isValid = false;
                        return res.sendError("Group type is required.");
                    }
        
                    if(groupDetails.groupType && groupDetails.groupType == "password"){
                        if(!groupDetails.password || typeof groupDetails.password !== "string"){
                            isValid = false;
                            return res.sendError("Group password is required.");
                        }
                    }
                }

                if(isValid){
                    const groupIcon: any = req.file;
                    
                    if(groupIcon){
                        let signRequired: boolean = false;

                        if (req.body.signRequired !== undefined) {
                            signRequired = req.body.signRequired;
                        }

                        const data: DUpload = {
                            userId: therapistId as unknown as Types.ObjectId,
                            originalName: groupIcon.originalname.replace(/ /g, ""),
                            name: groupIcon.filename,
                            type: groupIcon.mimetype,
                            path: groupIcon.path,
                            fileSize: groupIcon.size,
                            extension:
                            path.extname(groupIcon.originalname) || req.body.extension,
                            category: uploadCategory,
                            signRequired: signRequired,
                        };

                        let uploadedgroupIcon = await UploadDao.createUpload(data);

                        const groupChat:DGroupChat = {
                            groupTitle: groupDetails.groupTitle,
                            groupIconId: uploadedgroupIcon._id
                        }

                        let createdGroupChat = await GroupChatDao.createGroupChat(groupChat);

                        return res.sendSuccess(createdGroupChat, "Group chat created successfully.");

                    } else {
                        const groupChat:DGroupChat = {
                            groupTitle: groupDetails.groupTitle,
                            groupIconId: null
                        }

                        let createdGroupChat = await GroupChatDao.createGroupChat(groupChat);

                        return res.sendSuccess(createdGroupChat, "Group chat created successfully.");
                    }
      
                }
            }
        })
    } catch (error) {
        return res.sendError(error);
    }

  }
}
