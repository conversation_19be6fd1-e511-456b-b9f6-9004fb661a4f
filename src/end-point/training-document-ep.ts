import { NextFunction, Request, Response } from "express";
import multer = require("multer");
import * as path from "path";
import * as fs from "fs";
import { UserDao } from "../dao/user-dao";
import { UserRole } from "../models/user-model";
import { DUpload } from "../models/upload-model";
import { Types } from "mongoose";
import { UploadDao } from "../dao/upload-dao";
import { DDocument, AudienceType, IDocument } from "../models/document-model";
import { TrainigDocumentDao } from "../dao/training-document-dao";
import { DClient } from "../models/client-model";
import { DTrainingDocumentFolder } from "../models/training-document-folder-model";
let mongoose = require("mongoose");
import Therapist from "../schemas/therapist-schema";
import {
  check,
  param,
  ValidationChain,
  validationResult,
} from "express-validator";
import { DTrainingDocumentFolderFile } from "../models/training-document-folder-file-model";
import AWS = require("aws-sdk");
import { AdminDao } from "../dao/admin-dao";
import { TherapistDao } from "../dao/therapist-dao";

export enum UploadCategory {
  TRAINING_DOCUMENT_AND_VIDEO = "TRAINING_DOCUMENT_AND_VIDEO",
}

export namespace TrainingDocumentEp {
  export async function createTrainingDocumentsAndVideo(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    let uploadCategory = UploadCategory.TRAINING_DOCUMENT_AND_VIDEO;
    let isValid: boolean = true;
    let uploadedFiles: any[] = [];

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await homeworkValidationRules(req, cb);
      },
    });

    async function homeworkValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let trainingDetails = JSON.parse(req.body.trainingDetails);

        const user = await UserDao.getUserById(userId);

        if (!user) {
          return cb(Error("User not found the provided user Id."));
        }

        if (user.role !== UserRole.THERAPIST) {
          return cb(Error("Invalid user role."));
        }

        if (!mongoose.Types.ObjectId.isValid(trainingDetails.createdBy)) {
          return cb(Error("Invalid Therapist Id."), null);
        }

        if (
          !trainingDetails.documentTitle ||
          typeof trainingDetails.documentTitle !== "string"
        ) {
          return cb(Error("Title is required."), null);
        }

        if (
          !trainingDetails.documentDescription ||
          typeof trainingDetails.documentDescription !== "string"
        ) {
          return cb(Error("Description is required."), null);
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) =>
              cb(error, "destination")
            );
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    const upload = multer({ storage: storage }).array("uploads", 10);

    try {
      upload(req, res, async (error: any) => {
        if (error) {
          return res.sendError(error + "");
        }

        try {
          try {
            let trainingDetails = JSON.parse(req.body.trainingDetails);

            if (!trainingDetails.createdBy) {
              isValid = false;
              return res.sendError("Therapist Id is required.");
            }

            if (!mongoose.Types.ObjectId.isValid(trainingDetails.createdBy)) {
              isValid = false;
              return res.sendError("You can not create.");
            }

            if (
              !trainingDetails.documentTitle ||
              typeof trainingDetails.documentTitle !== "string"
            ) {
              isValid = false;
              return res.sendError("Title is required.");
            }

            if (
              !trainingDetails.documentDescription ||
              typeof trainingDetails.documentDescription !== "string"
            ) {
              isValid = false;
              return res.sendError("Description is required.");
            }
          } catch (error) {
            isValid = false;
            return res.sendError("Invalid Training Document Details json.");
          }

          if (isValid) {
            let request;

            try {
              request = JSON.parse(req.body.trainingDetails);
            } catch (error) {
              res.sendError(error);
            }

            const uploads: any = req.files;

            for (const upload of uploads) {
              const data: DUpload = {
                userId: userId as unknown as Types.ObjectId,
                originalName: upload.originalname.replace(/ /g, ""),
                name: upload.filename,
                type: upload.mimetype,
                path: upload.path,
                fileSize: upload.size,
                extension:
                  path.extname(upload.originalname) || req.body.extension,
                category: uploadCategory,
              };

              let uploadedFile = await UploadDao.createUpload(data);

              uploadedFiles.push(uploadedFile);
            }

            let uploadedIds: any = uploadedFiles.map((item: any) => {
              return item._id;
            });

            const trainingDocuments: DDocument = {
              createdBy: userId,
              documentTitle: request.documentTitle,
              documentDescription: request.documentDescription,
              uploads: uploadedIds,
              audience: request.audience,
              vimoIds: request.vimoIds,
            };

            try {
              let response = await TrainigDocumentDao.createTrainigDocument(
                trainingDocuments
              );
              return res.sendSuccess(
                response,
                "Training Documents and Videos added."
              );
            } catch (error) {
              return res.sendError(error);
            }
          }
        } catch (error) {
          return res.sendError(error);
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateTrainingDocumentsAndVideo(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    let uploadCategory = UploadCategory.TRAINING_DOCUMENT_AND_VIDEO;
    let deletingFiles = [];
    const userId = req.user._id;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateTrainingDocumentValidationRules(req, cb);
      },
    });

    async function updateTrainingDocumentValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let trainingDetails = JSON.parse(req.body.trainingDetails);

        try {
          const trainingDoc = await TrainigDocumentDao.getTrainingDocumentById(
            trainingDetails._id
          );

          deletingFiles = trainingDoc.uploads;

          if (!trainingDoc) {
            return cb(
              Error(
                "No training document details found for the provided doc Id."
              )
            );
          }
        } catch (error) {
          return cb(Error(error), null);
        }

        if (!mongoose.Types.ObjectId.isValid(trainingDetails.createdBy)) {
          return cb(Error("Invalid Therapist Id."), null);
        }

        if (
          !trainingDetails.documentTitle ||
          typeof trainingDetails.documentTitle !== "string"
        ) {
          return cb(Error("Title is required."), null);
        }

        if (
          !trainingDetails.documentDescription ||
          typeof trainingDetails.documentDescription !== "string"
        ) {
          return cb(Error("Description is required."), null);
        }

        if (
          !trainingDetails.deletingUploadIds ||
          !(trainingDetails.deletingUploadIds instanceof Array)
        ) {
          return cb(Error("Deleting upload id should be an array.")), null;
        }

        if (trainingDetails.deletingUploadIds.length !== 0) {
          for (let upload of trainingDetails.deletingUploadIds) {
            if (!deletingFiles.includes(upload)) {
              return cb(Error("Invalid deleting upload Ids"), null);
            }
          }
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) =>
              cb(error, "destination")
            );
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    const upload = multer({ storage });

    try {
      upload.array("uploads", 10)(req, res, async function (error: any) {
        if (error) {
          return res.send(error + " ");
        }

        let trainingDetails: any;

        try {
          if (req.files.length === 0) {
            try {
              trainingDetails = JSON.parse(req.body.trainingDetails);
            } catch (error) {
              return res.sendError(error);
            }

            if (!mongoose.Types.ObjectId.isValid(trainingDetails._id)) {
              return res.sendError("Invalid training document Id.");
            }

            try {
              const trainingDoc =
                await TrainigDocumentDao.getTrainingDocumentById(
                  trainingDetails._id
                );

              deletingFiles = trainingDoc.uploads;

              if (!trainingDoc) {
                return res.send(
                  Error(
                    "No training documrnt found for the provided training doc Id."
                  )
                );
              }
            } catch (error) {
              return res.sendError(error);
            }

            if (!trainingDetails.createdBy) {
              return res.sendError("Created user id is required!");
            }

            if (!mongoose.Types.ObjectId.isValid(trainingDetails.createdBy)) {
              return res.sendError("Created id is invalied!");
            }

            if (!trainingDetails.documentTitle) {
              return res.sendError("Title is required!");
            }

            if (!trainingDetails.documentDescription) {
              return res.sendError("Description is required!");
            }

            if (
              !trainingDetails.deletingUploadIds ||
              !(trainingDetails.deletingUploadIds instanceof Array)
            ) {
              return res.sendError("Deleting upload id should be an array.");
            }

            if (trainingDetails.deletingUploadIds.length !== 0) {
              for (let upload of trainingDetails.deletingUploadIds) {
                if (!deletingFiles.includes(upload)) {
                  return res.sendError("Invalid deleting upload Ids");
                }
              }
            }

            let previousTrainigDocDetails: IDocument = null;
            let previousUploadIds: any[] = null;

            try {
              previousTrainigDocDetails =
                await TrainigDocumentDao.getTrainingDocumentById(
                  trainingDetails._id
                );
            } catch (error) {
              return res.sendError("Invalid homework id");
            }

            previousUploadIds = previousTrainigDocDetails.uploads;

            async function trimData(id: any) {
              for (
                let i = 0;
                i < previousTrainigDocDetails.uploads.length;
                i++
              ) {
                if (previousUploadIds[i].toString() === id.toString()) {
                  previousUploadIds.splice(i, 1);
                }
              }
            }

            async function deleteFiles() {
              for (let id of trainingDetails.deletingUploadIds) {
                let resultHandler = async function (err: any) {
                  if (err) {
                    throw err;
                  }
                };
                try {
                  let upload = await UploadDao.getUpload(id);
                  await fs.unlink(upload.path, resultHandler);
                  await UploadDao.deleteUploadById(id);
                  await trimData(id);
                } catch (error) {
                  return res.sendError(error);
                }
              }
            }

            try {
              await deleteFiles();
            } catch (error) {
              return res.send("Error while deleting previous files" + error);
            }

            const newTrainingDocument: DDocument = {
              documentTitle: trainingDetails.documentTitle,
              documentDescription: trainingDetails.documentDescription,
              createdBy: trainingDetails.createdBy,
              editedBy: trainingDetails.editedBy,
              audience: trainingDetails.audience,
              vimoIds: trainingDetails.vimoIds,
              uploads: previousUploadIds,
            };

            try {
              let uodateTrainingDocument =
                await TrainigDocumentDao.updateTrainingDocument(
                  trainingDetails._id,
                  newTrainingDocument
                );
              return res.sendSuccess(
                uodateTrainingDocument,
                "training document updated."
              );
            } catch (error) {
              return res.send(error);
            }
          } else {
            const uploads: any = req.files;
            let newUploads = [];

            try {
              trainingDetails = JSON.parse(req.body.trainingDetails);
            } catch (error) {
              return res.sendError(error);
            }

            for (const upload of uploads) {
              const data: DUpload = {
                userId: req.user as unknown as Types.ObjectId,
                originalName: upload.originalname.replace(/ /g, ""),
                name: upload.filename,
                type: upload.mimetype,
                path: upload.path,
                fileSize: upload.size,
                extension:
                  path.extname(upload.originalname) || req.body.extension,
                category: uploadCategory,
              };

              let uploadedFile = await UploadDao.createUpload(data);
              newUploads.push(uploadedFile);
            }

            if (newUploads.length === 0) {
              return res.sendError("Error while saving uploads");
            } else {
              let uploadResult: any = newUploads.map((item: any) => {
                return item._id;
              });

              let previousTrainingDocumentDetails: IDocument = null;
              let previousUploadIds: any[] = null;

              try {
                previousTrainingDocumentDetails =
                  await TrainigDocumentDao.getTrainingDocumentById(
                    trainingDetails._id
                  );
              } catch (error) {
                return res.sendError("Invalid training document id");
              }

              previousUploadIds = previousTrainingDocumentDetails.uploads;

              async function trimData(id: any) {
                for (
                  let i = 0;
                  i < previousTrainingDocumentDetails.uploads.length;
                  i++
                ) {
                  if (previousUploadIds[i].toString() === id.toString()) {
                    previousUploadIds.splice(i, 1);
                  }
                }
              }

              async function deleteFiles() {
                for (let id of trainingDetails.deletingUploadIds) {
                  let resultHandler = async function (err: any) {
                    if (err) {
                      throw err;
                    }
                  };
                  try {
                    let upload = await UploadDao.getUpload(id);
                    await fs.unlink(upload.path, resultHandler);
                    await UploadDao.deleteUploadById(id);
                    await trimData(id);
                  } catch (error) {
                    return res.sendError(error);
                  }
                }
              }

              async function updateDetails() {
                await deleteFiles();
                let finalUploads = previousUploadIds.concat(uploadResult);

                let requestBody = null;

                try {
                  requestBody = JSON.parse(req.body.trainingDetails);
                } catch (error) {
                  return res.sendError("Invalid training document details");
                }

                const newTrainingDocument: DDocument = {
                  documentTitle: requestBody.documentTitle,
                  documentDescription: requestBody.documentDescription,
                  createdBy: requestBody.createdBy,
                  editedBy: requestBody.editedBy,
                  audience: requestBody.audience,
                  vimoIds: requestBody.vimoIds,
                  uploads: finalUploads,
                };

                try {
                  let uodateTrainingDocument =
                    await TrainigDocumentDao.updateTrainingDocument(
                      trainingDetails._id,
                      newTrainingDocument
                    );
                  return res.sendSuccess(
                    uodateTrainingDocument,
                    "training document updated."
                  );
                } catch (error) {
                  return res.send(error);
                }
              }

              try {
                await updateDetails();
              } catch (error) {
                return res.send(error);
              }
            }
          }
        } catch (error) {
          return res.send(error);
        }
      });
    } catch (error) {
      return res.send(error);
    }
  }

  export async function getAllTrainingDocuments(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    let subType: AudienceType;
    switch (req.user.role) {
      case UserRole.CLIENT:
        subType = AudienceType.CLIENTS;
        break;
      case UserRole.THERAPIST:
        subType = AudienceType.THERAPISTS;
        break;
    }

    const doc = await TrainigDocumentDao.getAllDocuments(subType, userId);
    return res.sendSuccess(doc, "all training documents");
  }

  export async function deleteTrainingDocument(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const tId = req.params.tId;
    const trainingDoc = await TrainigDocumentDao.deleteTrainingDocumentById(
      tId
    );
    return res.sendSuccess(trainingDoc, "delete training document");
  }
  export async function getTrainingDocument(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const tId = req.params.tId;
    const trainingDoc = await TrainigDocumentDao.getTrainingDocumentById(tId);
    return res.sendSuccess(trainingDoc, "delete training document");
  }

  export async function searchDocuments(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    const searchableText = req.body.searchableText;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    let subType: AudienceType;

    switch (req.user.role) {
      case UserRole.CLIENT:
        subType = AudienceType.CLIENTS;
        break;
      case UserRole.THERAPIST:
        subType = AudienceType.THERAPISTS;
        break;
    }

    if (
      req.user.role == UserRole.CLIENT ||
      req.user.role == UserRole.THERAPIST ||
      req.user.role == UserRole.SUPER_ADMIN ||
      req.user.role == UserRole.SUB_ADMIN
    ) {
      try {
        let searchResults = await TrainigDocumentDao.searchDocuments(
          userId,
          searchableText,
          subType,
          limit,
          offset
        );

        return res.sendSuccess(searchResults, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function searchDocumentsAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    const searchableText = req.body.searchableText;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    let subType: AudienceType;

    if (
      req.user.role == UserRole.SUPER_ADMIN ||
      req.user.role == UserRole.SUB_ADMIN
    ) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.documents != true) {
            return res.sendError(
              "You don't have permission for Training Documents!"
            );
          }
        }
        let searchResults = await TrainigDocumentDao.searchDocuments(
          userId,
          searchableText,
          subType,
          limit,
          offset
        );

        return res.sendSuccess(searchResults, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export function createTrainingDocumentFolderValidation(): ValidationChain[] {
    return [
      check("parentFolderId")
        .optional({ nullable: true })
        .isMongoId()
        .withMessage("parentFolderId is not a Type of mongo id"),
      check("folderName")
        .notEmpty()
        .withMessage("folderName is required")
        .isString()
        .withMessage("folderName is not a Type of string"),
      check("createdBy")
        .notEmpty()
        .withMessage("createdBy is required")
        .isMongoId()
        .withMessage("createdBy is not a Type of mongo id"),
    ];
  }

  export async function createTrainingDocumentFolder(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }
      //   if(req.user.role == UserRole.THERAPIST){
      // const userId = req.user?._id;

      const parentFolderId = req.body?.parentFolderId;
      const folderName = req.body?.folderName;
      let isTrainingDocumentFolderAlreadyExist;
      if (parentFolderId === null) {
        isTrainingDocumentFolderAlreadyExist =
          await TrainigDocumentDao.checkTrainingDocumentFolderAlreadyExist(
            null,
            folderName
          );
      } else {
        isTrainingDocumentFolderAlreadyExist =
          await TrainigDocumentDao.checkTrainingDocumentFolderAlreadyExist(
            Types.ObjectId(parentFolderId),
            folderName
          );
      }

      if (isTrainingDocumentFolderAlreadyExist.length > 0) {
        return res.sendError("This folder name already exist");
      }

      const userId = req.body?.createdBy;
      const data: DTrainingDocumentFolder = {
        parentFolderId,
        folderName,
        createdBy: userId,
      };
      const folderNameRes =
        await TrainigDocumentDao.createTrainingDocumentFolder(data);
      return res.sendSuccess(folderNameRes);

      // } else {
      //   return res.sendError("Invalid user role.");
      // }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function createTrainingDocumentFolderFileValidation(): ValidationChain[] {
    return [
      check("parentFolderId")
        .optional({ nullable: true })
        .isMongoId()
        .withMessage("parentFolderId is not a Type of mongo id"),
      check("originalFileName")
        .notEmpty()
        .withMessage("originalFileName is required")
        .isString()
        .withMessage("originalFileName is not a Type of string"),
      check("fileNameInAwsBucket")
        .notEmpty()
        .withMessage("fileNameInAwsBucket is required")
        .isString()
        .withMessage("fileNameInAwsBucket is not a Type of string"),
      check("type")
        .notEmpty()
        .withMessage("type is required")
        .isString()
        .withMessage("type is not a Type of string"),
      check("createdBy")
        .notEmpty()
        .withMessage("createdBy is required")
        .isMongoId()
        .withMessage("createdBy is not a Type of mongo id"),
    ];
  }

  export async function createTrainingDocumentFolderFile(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }
      //   if(req.user.role == UserRole.THERAPIST){
      // const userId = req.user?._id;

      const parentFolderId = req.body?.parentFolderId;
      const fileNameInAwsBucket = req.body?.fileNameInAwsBucket;
      const originalFileName = req.body?.originalFileName;
      const type = req.body?.type;
      const userId = req.body?.createdBy;
      const title = req.body.title;
      const description = req.body.description;
      let isTrainingDocumentFolderFileAlreadyExist;
      if (parentFolderId === null) {
        // const fileNameForCheck = `root-${fileName}`;
        isTrainingDocumentFolderFileAlreadyExist =
          await TrainigDocumentDao.checkTrainingDocumentFolderFileAlreadyExist(
            null,
            originalFileName
          );
      } else {
        // const fileNameForCheck = `${parentFolderId}-${fileName}`;
        isTrainingDocumentFolderFileAlreadyExist =
          await TrainigDocumentDao.checkTrainingDocumentFolderFileAlreadyExist(
            Types.ObjectId(parentFolderId),
            originalFileName
          );
      }

      if (isTrainingDocumentFolderFileAlreadyExist.length > 0) {
        return res.sendError("This file name already exist");
      }

      const data: DTrainingDocumentFolderFile = {
        parentFolderId,
        originalFileName,
        fileNameInAwsBucket,
        type,
        createdBy: userId,
        title,
        description,
      };
      const folderNameRes =
        await TrainigDocumentDao.createTrainingDocumentFolderFile(data);
      return res.sendSuccess(folderNameRes);

      // } else {
      //   return res.sendError("Invalid user role.");
      // }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getTrainingDocumentFoldersAndFiles(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      //   if(req.user.role == UserRole.THERAPIST){
      // const userId = req.user?._id;

      const parentFolderId = req.params?.parentFolderId || null;
      // Get the sort order from query params (asc or desc)
      const nameOrder = req.query?.name_order as string || null;
      console.log("parentFolderId ", parentFolderId);
      console.log("nameOrder ", nameOrder);

      if (parentFolderId && !mongoose.isValidObjectId(parentFolderId)) {
        return res.sendError("ParentFolderId is not valid objectId");
      }
      let trainingDocumentFolders;
      let trainingDocumentFolderFiles;
      if (parentFolderId === null) {
        trainingDocumentFolders =
          await TrainigDocumentDao.getTrainingDocumentFoldersByParentFolderId(
            null,
            nameOrder
          );
        trainingDocumentFolderFiles =
          await TrainigDocumentDao.getTrainingDocumentFolderFilesByParentFolderId(
            null,
            nameOrder
          );
      } else {
        trainingDocumentFolders =
          await TrainigDocumentDao.getTrainingDocumentFoldersByParentFolderId(
            Types.ObjectId(parentFolderId),
            nameOrder
          );
        trainingDocumentFolderFiles =
          await TrainigDocumentDao.getTrainingDocumentFolderFilesByParentFolderId(
            Types.ObjectId(parentFolderId),
            nameOrder
          );
      }

      let path;
      if (parentFolderId != null) {
        path = await getFolderPath(Types.ObjectId(parentFolderId));
      }

      return res.sendSuccess({
        folders: trainingDocumentFolders,
        files: trainingDocumentFolderFiles,
        pathToFolder: path,
      });
      // } else {
      //   return res.sendError("Invalid user role.");
      // }
    } catch (error) {
      return res.sendError(error);
    }
  }

  async function getFolderPath(folderId: Types.ObjectId) {
    const folderPath: any = [];

    async function findPath(currentFolderId: Types.ObjectId) {
      if (!currentFolderId) {
        return;
      }
      console.log("currentFolderId ", currentFolderId);

      const folder = await TrainigDocumentDao.getTrainingDocumentFolderBylderId(
        currentFolderId
      );
      if (!folder) {
        return;
      }

      folderPath.unshift({
        folderName: folder.folderName,
        _id: folder._id,
      });

      await findPath(folder.parentFolderId);
    }

    await findPath(folderId);
    return folderPath;
  }

  export async function generatePresignedURLForStoreFilesInAWSBucket(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const parentFolderId = req.body.parentFolderId;
      const files = req.body.files;
      if (!files || !Array.isArray(files)) {
        return res.sendError("Invalid files array");
      }
      AWS.config.update({
        accessKeyId: process.env.AWS_S3_ACCESSKEY_ID,
        secretAccessKey: process.env.AWS_S3_SECRET_ACCESSKEY,
        region: process.env.AWS_S3_REGION,
      });

      const s3 = new AWS.S3();

      const urls = await Promise.all(
        files.map(async (file: { name: string; type: string }) => {
          console.log("file ################### ", file);
          let checkFileNameAlreadyExist;
          if (parentFolderId === null) {
            checkFileNameAlreadyExist =
              await TrainigDocumentDao.checkTrainingDocumentFolderFileAlreadyExist(
                null,
                file.name
              );
          } else {
            checkFileNameAlreadyExist =
              await TrainigDocumentDao.checkTrainingDocumentFolderFileAlreadyExist(
                Types.ObjectId(parentFolderId),
                file.name
              );
          }

          if (checkFileNameAlreadyExist.length > 0) {
            return {
              success: false,
              message: `This file name already exist`,
              fileName: file.name,
            };
          }
          const params = {
            Bucket: process.env.AWS_S3_BUCKET_TRAINING_DOCUMENTS,
            Key:
              parentFolderId == null
                ? `root-${file.name}`
                : `${parentFolderId}-${file.name}`,
            Expires: 300, // URL expiration time in seconds
            ContentType: file.type,
          };
          const url = s3.getSignedUrl("putObject", params);
          return { success: true, url, key: params.Key, fileName: file.name };
        })
      );

      return res.sendSuccess(urls);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function generatePresignedURLForGetFiles(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const parentFolderId = req.body.parentFolderId;
      const fileName = req.body.originalFileName;
      const fileNameInAwsBucket = req.body.fileNameInAwsBucket;
      if (!fileName) {
        return res.sendError("No file found");
      }
      AWS.config.update({
        accessKeyId: process.env.AWS_S3_ACCESSKEY_ID,
        secretAccessKey: process.env.AWS_S3_SECRET_ACCESSKEY,
        region: process.env.AWS_S3_REGION,
      });

      const s3 = new AWS.S3();
      console.log("file ################### ", fileName);
      let checkFileExistInDB;
      if (parentFolderId == null) {
        checkFileExistInDB =
          await TrainigDocumentDao.checkTrainingDocumentFolderFileAlreadyExist(
            null,
            fileName
          );
      } else {
        checkFileExistInDB =
          await TrainigDocumentDao.checkTrainingDocumentFolderFileAlreadyExist(
            Types.ObjectId(parentFolderId),
            fileName
          );
      }

      if (checkFileExistInDB.length == 0) {
        return res.sendSuccess(
          "There is no file for this name in the directory"
        );
      }
      const params = {
        Bucket: process.env.AWS_S3_BUCKET_TRAINING_DOCUMENTS,
        Key: fileNameInAwsBucket,
        // parentFolderId == null
        //   ? `root-${fileName}`
        //   : `${parentFolderId}-${fileName}`,
        Expires: 300, // URL expiration time in seconds
      };
      const url = await s3.getSignedUrlPromise("getObject", params);

      return res.sendSuccess(url);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteFileFolderTrainingDocument(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const fileId = req.body.fileId;

    console.log(fileId);

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const user = await UserDao.getUser(req.user._id);
      const file = await TrainigDocumentDao.getTrainingDocumentByDocId(fileId);

      if (!file) {
        return res.sendError("File is not found");
      }

      if (!user) {
        return res.sendError("Authority User not found");
      }

      if (
        user.role === UserRole.SUPER_ADMIN ||
        user.role === UserRole.SUB_ADMIN ||
        user.grantedAccessFileFolderPermission
      ) {
        console.log("call in");
        AWS.config.update({
          accessKeyId: process.env.AWS_S3_ACCESSKEY_ID,
          secretAccessKey: process.env.AWS_S3_SECRET_ACCESSKEY,
          region: process.env.AWS_S3_REGION,
        });
        const s3 = new AWS.S3();
        const params = {
          Bucket: process.env.AWS_S3_BUCKET_TRAINING_DOCUMENTS,
          Key: file.fileNameInAwsBucket,
        };
        const result = await TrainigDocumentDao.deleteTrainingDocumentByDocId(
          fileId
        );

        const deleteS3Obj = s3.deleteObject(params).promise();

        await Promise.all([result, deleteS3Obj]);

        return res.sendSuccess("File is deleted successfully");
      }
    } catch (error) {
      return res.sendError("Internal Server Error");
    }
  }

  async function deleteFoldersAndFiles(data: {
    files: { fileNameInAwsBucket: string; id: Types.ObjectId }[];
    folderIds: Types.ObjectId[];
  }) {
    AWS.config.update({
      accessKeyId: process.env.AWS_S3_ACCESSKEY_ID,
      secretAccessKey: process.env.AWS_S3_SECRET_ACCESSKEY,
      region: process.env.AWS_S3_REGION,
    });
    const s3 = new AWS.S3();
    let promises;
    if (data.files.length > 0) {
      promises = data.files.map((file) => {
        const params = {
          Bucket: process.env.AWS_S3_BUCKET_TRAINING_DOCUMENTS,
          Key: file.fileNameInAwsBucket,
        };

        const deleteFileFromS3 = s3.deleteObject(params).promise();

        const deleteFileFromDb =
          TrainigDocumentDao.deleteTrainingDocumentByDocId(file.id);

        return Promise.all([
          deleteFileFromDb,
          deleteFileFromS3,
          // deleteFolderPromise,
        ]);
      });

      const deleteFolderPromise =
        await TrainigDocumentDao.deleteTrainingDocumentFolderByFolderId(
          data.folderIds
        );

      await Promise.all([promises, deleteFolderPromise]);
    } else {
      promises =
        await TrainigDocumentDao.deleteTrainingDocumentFolderByFolderId(
          data.folderIds
        );
      await Promise.all(promises);
    }
  }

  export async function deleteFoldersTrainingDocument(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const folderId = req.body.folderId;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const user = await UserDao.getUser(req.user._id);
      const data = await TrainigDocumentDao.getFolderFilesIdsByParentFoldersId(
        folderId
      );

      if (
        user.role === UserRole.SUPER_ADMIN ||
        user.role === UserRole.SUB_ADMIN ||
        user.grantedAccessFileFolderPermission
      ) {
        if (data) {
          await deleteFoldersAndFiles(data);
          return res.sendSuccess("All files and folders deleted successfully");
        }
      } else {
        return res.sendError(
          "User have not permission to telete folders and files"
        );
      }

      // return res.sendSuccess("No files or folders to delete");
    } catch (error) {
      console.log(error);
      return res.sendError("Error deleting files and folders");
    }
  }

  //file rename
  export async function renameFile(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const originalFileName = req.body.originalFileName;
    const fileId = req.body.fileId;

    const data: Partial<DTrainingDocumentFolderFile> = {
      originalFileName: originalFileName,
    };
    try {
      const user = await UserDao.getUser(req.user._id);

      const file = await TrainigDocumentDao.getTrainingDocumentByDocId(fileId);

      if (!file) {
        return res.sendError("File is not found");
      }
      if (
        user.role === UserRole.SUPER_ADMIN ||
        user.role === UserRole.SUB_ADMIN ||
        user.grantedAccessFileFolderPermission
      ) {
        const result = await TrainigDocumentDao.renameFile(fileId, data);

        return res.sendSuccess(result);
      } else {
        return res.sendError("You have not permission files");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  //rename folder
  export async function renameFolder(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const folderId = req.body.folderId;
    const folderName = req.body.folderName;

    const data: Partial<DTrainingDocumentFolder> = {
      folderName,
    };

    try {
      const user = await UserDao.getUser(req.user._id);

      const folder = await TrainigDocumentDao.getTrainingDocumentFolderBylderId(
        folderId
      );

      if (!folder) {
        return res.sendError("Folder is not found");
      }

      if (
        user.role === UserRole.SUPER_ADMIN ||
        user.role === UserRole.SUB_ADMIN ||
        user.grantedAccessFileFolderPermission
      ) {
        const result = await TrainigDocumentDao.renameFolder(folderId, data);

        return res.sendSuccess(result);
      } else {
        return res.sendError("You have not permission to rename folders");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  //seach folder files
  export async function searchFileFolders(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const searchString = req.body.searchString;
    const parentFolderId = req.body.parentFolderId;
    try {
      const result = await TrainigDocumentDao.searchFoldersFiles(
        searchString,
        parentFolderId === null
          ? parentFolderId
          : Types.ObjectId(parentFolderId)
      );

      return res.sendSuccess(result);
      console.log(result);
    } catch (error) {
      console.log(error);
    }
  }

  export async function filterTherapistsDocAccessByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const searchableString = req.body.searchableString;
    const grantedStatus = req.body.grantedStatus;

    const status = req.body.status;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (
      req.user.role == UserRole.SUPER_ADMIN ||
      UserRole.ADMIN ||
      UserRole.SUB_ADMIN
    ) {
      try {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.accessManagement != true) {
            return res.sendError(
              "You don't have permission for View Access Management!"
            );
          }
        }
        const result = await TrainigDocumentDao.filterTherapistByDocumentAccess(
          searchableString,
          limit,
          offset,
          status,
          grantedStatus
        );
        const countUser = await AdminDao.getAllTherapistsCount();

        const count = countUser - limit * offset;

        const data = {
          userSet: result,
          count: count,
          totalCount: countUser,
        };

        return res.sendSuccess(data, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("No permission to access!");
    }
  }

  export async function allowAccessToTherapistToCreateFolders(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    const therapistId = req.body.therapistId;
    const grantedAccessFileFolderPermissionFront =
      req.body.grantedAccessFileFolderPermission;

    try {
      const user = await UserDao.getUser(req.user._id);

      if (!therapistId) {
        return res.sendError("Therapist Id not found");
      }

      const therapist = await UserDao.getUser(therapistId);

      if (!therapist || therapist.role != UserRole.THERAPIST) {
        return res.sendError("User Not found or invalid user role");
      }
      if (!user) {
        return res.sendError("Authority User not found");
      }

      if (
        user.role === UserRole.SUPER_ADMIN ||
        user.role === UserRole.SUB_ADMIN ||
        user.role === UserRole.ADMIN
      ) {
        // const data = {
        //   grantedAccessFileFolderPermission: grantedAccessFileFolderPermission  === "TRUE" ? true : false,
        // };
        // let user;
        // let user = await UserDao.updateUser(therapistId, data);


        let therapist = await Therapist.findOneAndUpdate(
          {_id: therapistId} ,
          {
            $set: {
              grantedAccessFileFolderPermission:
                grantedAccessFileFolderPermissionFront == "TRUE",
            },
          },
          { new: true}
        );

        return res.sendSuccess(therapist);
      } else {
        return res.sendError("Invalid User role to update User");
      }
    } catch (error) {
      console.log(error);
      res.status(500).send("Internal Server Error");
    }
  }
}
