import {NextFunction, Request, Response} from "express";
import {check, validationResult} from "express-validator";
import {Types} from "mongoose";
import {StringOrObjectId, Util} from "../common/util";
import {AdminDao} from "../dao/admin-dao";
import {ClientDao} from "../dao/client-dao";
import {EthnicityDao} from "../dao/ethnicity-dao";
import {TherapistDao} from "../dao/therapist-dao";
import {UserDao} from "../dao/user-dao";
import {DClient} from "../models/client-model";
import {Review, ReviewStatus} from "../models/sub-models/review-model";
import {AINotesType, DTherapist} from "../models/therapist-model";
import {DTreatmentHistory} from "../models/treatment-history-model";
import {IUser, UserRole} from "../models/user-model";
import {SMSService} from "../sms/config";
import {AppLogger} from "../common/logging";
import {AppointmentDao} from "../dao/appointment-dao";
import {VideoCallDao} from "../dao/videocall-dao";
import {DCustomerReview} from "../models/customer-reviews-model";
import {AppointmentEp} from "./appointment-ep";
import {FriendRequestEp} from "./friend-request-ep";
import {EmailService} from "../mail/config";
import {TransactionsEp} from "./transactions-ep";
import {FriendRequestDao} from "../dao/friend-request-dao";
import User from "../schemas/user-schema";
import Appointment from "../schemas/appointment-schema";
import {AppointmentStatus} from "../models/appointment-model";
import {VonageCallGroupEp} from "./vonage-call-group-ep";
import {CallingStatus} from "../models/meeting-model";
import {DTherapistScoreConstants, ITherapistScoreConstants} from "../models/therapist-score-constants-model";
import {DDiagnosisNote} from "./../models/diagnosis-note-model";
import {DMeeting} from "./../models/meeting-model";
import Therapist from "../schemas/therapist-schema";
import {DInsuranceDocApproval} from "../models/insurance-doc-approval-model";
import {FriendRequestStatus} from "../models/friend-request-model";
import FriendRequest from "../schemas/friend-request-schema";
import * as fs from 'fs';
import * as path from 'path';
import Meeting from '../schemas/meeting-schema';
import Upload from '../schemas/upload-schema';

let mongoose = require("mongoose");
const moment = require("moment-timezone");
import AWS = require("aws-sdk");

export namespace TherapistEp {
    export function updateTherapistValidationRules() {
        return [
            check("workingHours")
                .isArray()
                .withMessage("Working hours should be an array."),
            check("experiencedIn")
                .isArray()
                .withMessage("experiencedIn must be an array."),
        ];
    }

    export function reviewValidationRules() {
        return [
            check("review")
                .not()
                .isEmpty()
                .withMessage("Review is required.")
                .isString()
                .isLength({max: 1000})
                .withMessage("Review field should not be more than 1000 chars long."),
        ];
    }

    export function addReviewValidationRules() {
        return [
            check("therapistId")
                .not()
                .isEmpty()
                .withMessage("Therapist id cannot be empty.")
                .isString()
                .withMessage("Therapist id shoould be a string."),
            check("noOfStars")
                .not()
                .isEmpty()
                .withMessage("No of stars cannot be empty.")
                .isNumeric()
                .withMessage("No of stars can only be a number"),
            check("review")
                .not()
                .isEmpty()
                .withMessage("Review cannot be empty.")
                .isString()
                .withMessage("Review can only be a string."),
        ];
    }

    export function searchClientByParamsValidationRules() {
        return [
            check("gender").isString().withMessage("Gender must be a string value"),
            check("ethnicityId").isString().withMessage("Id must be a string value."),
            check("experiencedIn")
                .isArray()
                .withMessage("Invalid experiencedIn value."),
        ];
    }

    export async function getAllTherapists(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const limit = Number(req.params.limit);
        const offset = Number(req.params.offset);

        if (req.user.role == UserRole.CLIENT) {
            try {
                let therapistList = await TherapistDao.getAllTherapists(
                    userId,
                    limit,
                    offset
                );

                return res.sendSuccess(therapistList, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function createRegularMeeting(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = req.user._id;
        const role = req.user.role;

        if (role == UserRole.THERAPIST) {
            try {
                const therapist = await TherapistDao.getUserById(therapistId);

                if (!therapist) {
                    return res.sendError("Invalid therapist id!");
                }

                // Get these details from body
                const clientId = req.body.recieverId;
                const firstSpeaker = req.body.firstSpeaker;
                const uploadedFilePath = req.body.uploadedFilePath;
                const callDuration = parseInt(req.body.callDuration); // Or 60
                const spendDuration = parseInt(req.body.spendDuration);
                let recordingAllowedStatus;
                const audioAvailable = true;
                const meetingType = req.body.meetingType;

                const regularMeetingStartTime = req.body.regularMeetingStartTime;
                const hour = moment(regularMeetingStartTime, ["h:mm A"]).hour();
                const minute = moment(regularMeetingStartTime, ["h:mm A"]).minute();

                const regularMeetingDate = new Date(req.body.regularMeetingDate);

                // const regularMeetingDateLocal = moment.tz(regularMeetingDateString, moment.tz.guess()).set({
                //   hour: moment(regularMeetingStartTime, ["h:mm A"]).hour(),
                //   minute: moment(regularMeetingStartTime, ["h:mm A"]).minute(),
                //   second: 0
                // }).toDate();

                // const regularMeetingDate = moment(regularMeetingDateLocal).utc().toDate();

                let audioFile;

                // if (meetingType == "Regular" && req.body.audioAvailable) {

                //   const uploadData = {
                //     path: req.body?.uploadedFilePath,
                //     originalName: req.body?.originalName,
                //     type: ".mp3",
                //     isUrl: false,
                //   };

                //   try {
                //     const uploadResponse = await UploadDao.createUpload(uploadData);

                //     if (uploadResponse._id) {
                //       audioFile = uploadResponse._id;
                //     }
                //   } catch (dbError) {
                //     return res
                //       .status(500)
                //       .json({ error: "Database operation failed." });
                //   }
                // }

                let regulatMeetingId;
                let newClientIdentifier;
                let newTherapistIdentifier;
                let newPassword;

                regulatMeetingId = await Util.getRandomInt(10000, 100000);
                newClientIdentifier = await Util.getRandomInt(10000, 100000);
                newTherapistIdentifier = await Util.getRandomInt(10000, 100000);

                while (newClientIdentifier == newTherapistIdentifier) {
                    newTherapistIdentifier = await Util.getRandomInt(10000, 100000);
                }

                newPassword = await Util.getRandomInt(10000, 100000);

                if (audioAvailable) {
                    recordingAllowedStatus = true;
                } else {
                    recordingAllowedStatus = false;
                }

                // Create meeting object ---------------------------------------------------
                let meetingDetails: DMeeting;

                meetingDetails = {
                    clientId: clientId,
                    therapistId: therapistId,
                    transcribeAllowed: true,
                    transcribeCreated: false,
                    transcribingInProcess: false,
                    recordingAllowed: recordingAllowedStatus,
                    accepted: true,
                    meetingDuration: callDuration,
                    spentDuration: spendDuration,
                    isAppointmentBased: false,
                    firstSpeaker: firstSpeaker,
                    s3AudioPath: uploadedFilePath,
                    callingStatus: CallingStatus.COMPLETED,
                    createdBy: req.user._id,
                    recordingSharedWithClient: false,
                    noOfVideose: 0,
                    clientIdentifier: newClientIdentifier.toString(),
                    therapistIdentifier: newTherapistIdentifier.toString(),
                    password: newPassword.toString(),
                    meetingId: meetingType === 'Regular' ? "Regular Call - " + regulatMeetingId.toString() : "Twilio Call - " + regulatMeetingId.toString(),
                    regularMeetingStartTime,
                    regularMeetingDate,
                    therapistAllowedTranscribe: true,
                    clientAllowedTranscribe: true
                };

                if (req.body.audioAvailable && meetingType == "Regular") {
                    meetingDetails.audioFiles = [audioFile];
                }

                const createdMeeting = await VideoCallDao.createMeeting(meetingDetails);

                if (!createdMeeting) {
                    return res.sendError("Meeting Creation Failed.");
                }

                // Create diagnosis note ---------------------------------------------------
                const diagnosisNoteData: DDiagnosisNote = {
                    clientId: clientId,
                    meetingId: createdMeeting._id,
                    therapistId: therapistId,
                    updated: false,
                    updatedByTherapist: false,
                    isVonageTranscribe: true,
                };

                const diagnosisNote = await VideoCallDao.createDiagnosisNote(
                    diagnosisNoteData
                );

                // Create treatment history object ---------------------------------------------------
                const treatmentHistoryDetails: DTreatmentHistory = {
                    clientId: clientId,
                    therapistId: therapistId,
                    note: "Test Note...",
                    isMeetingTranscribe: true,
                    meetingId: createdMeeting._id,
                    diagnosisNoteId: diagnosisNote._id,
                    meetingStartedTime: regularMeetingDate,
                };

                const treatmentHistoryCreated = await TherapistDao.addTreatmentHistory(
                    treatmentHistoryDetails
                );

                if (treatmentHistoryCreated) {
                    return res.sendSuccess("New resgular meeting is created.");
                } else {
                    return res.sendError(
                        "Error occured while creating resgular meeting."
                    );
                }
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    //start
    export async function regularCallDataMigrations(
        req: Request,
        res: Response,
        next: NextFunction
    ) {

        const successfullyUploadedMeetings: string[] = [];

        try {


            AWS.config.update({
                accessKeyId: process.env.AWS_S3_ACCESSKEY_ID, // Store securely
                secretAccessKey: process.env.AWS_S3_SECRET_ACCESSKEY, // Store securely
                region: process.env.AWS_S3_REGION, // e.g., 'us-west-2'
            });

            const s3 = new AWS.S3();

            const allMeetings = await Meeting.find({
                vonageArchiveId: {$exists: false},
                s3AudioPath: {$exists: false},
                meetingId: /Regular Call/,
                callingStatus: {$in: [CallingStatus.COMPLETED]}
            });

            if (!allMeetings.length) {
                return res.status(404).json({
                    message: 'No completed regular call meetings found with given conditions.'
                });
            }

            for (const meet of allMeetings) {
                try {
                    if (meet.audioFiles.length === 0) {
                        continue;
                    }
                    const data = await Upload.findById(meet.audioFiles[0]);

                    if (!data || !data.path) {
                        throw new Error(`Audio file for meeting ID ${meet._id} not found in Uploads collection.`);
                    }

                    await fs.promises.access(data.path, fs.constants.F_OK);

                    const fileContent = await fs.promises.readFile(data.path);
                    const fileName = path.basename(data.path);

                    const s3FilePath = `CALL_RECORDS/${meet.therapistId}/${Date.now()}_${fileName}`;

                    const params = {
                        Bucket: process.env.AWS_S3_BUCKET_REGULER_AUDIO_CALLS,
                        Key: s3FilePath,
                        Body: fileContent,
                        ContentType: 'audio/mp3', // Adjust based on file type
                    };

                    const uploadResult = await s3.upload(params).promise();

                    meet.s3AudioPath = `${s3FilePath}`;
                    meet.transcribeCreated = false;
                    meet.therapistAllowedTranscribe = true;
                    meet.clientAllowedTranscribe = true;
                    if (!meet.firstSpeaker) {
                        meet.firstSpeaker = meet.therapistId;
                    }
                    await meet.save();

                    successfullyUploadedMeetings.push(`${meet._id}`);

                    console.log(`Successfully migrated: ${fileName}`);
                } catch (error) {

                    console.error(error);
                    return res.status(500).json({
                        message: `Error uploading file for meeting ID ${meet._id}`,
                        error: error.message
                    });
                }
            }


            console.log("updated meeting ids");
            console.log(successfullyUploadedMeetings);

            // Respond with success
            return res.status(200).json({
                message: 'All files uploaded and records saved successfully.',
                uploadedMeetings: successfullyUploadedMeetings,
            });

        } catch (error) {
            console.error("Error during file upload or record saving:", error);

            return res.status(500).json({
                message: "Error during file upload or record saving.",
                error: error.message
            });
        }
    }

    //end

//start
    export async function regularCallDataUpdate(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const successfullyUpdatedMeetings: string[] = [];
        try {
            const allMeetings = await Meeting.find({
                vonageArchiveId: {$exists: false},
                regularMeetingDate: {$exists: true},
                s3AudioPath: {$ne: null},
                meetingId: /Regular Call/,
                transcribeCreated: true,
                clientAllowedTranscribe: false,
                therapistAllowedTranscribe: false,
                callingStatus: {$in: [CallingStatus.COMPLETED]}
            });

            if (!allMeetings.length) {
                return res.status(404).json({
                    message: "No regular call meetings found with given conditions."
                });
            }

            for (const meet of allMeetings) {
                meet.clientAllowedTranscribe = true;
                meet.therapistAllowedTranscribe = true;
                await meet.save();

                successfullyUpdatedMeetings.push(`${meet._id}`);
                console.log(`Successfully updated meetingId:${meet._id}`);
            }
            return res.status(200).json({
                message: "All regular meeting call records updated successfully",
                updatedMeetings: successfullyUpdatedMeetings
            });
        } catch (error) {
            console.log(error);
            return res.status(500).json({
                message: `Error updating meeting records`,
                error: error.message
            });
        }
    }

//end

    export async function searchTherapistsStats(
        userRole: any,
        _id: any,
        userLimit: any,
        userOffset: any
    ) {
        const userId = _id;
        const role = userRole;
        const gender = "";
        const ethnicity = "";
        const profession = "";
        const experiencedIn = [""];
        const searchTherapistName = "";
        const limit = Number(userLimit);
        const offset = Number(userOffset);

        if (role == UserRole.CLIENT) {
            try {
                let searchResults = await TherapistDao.searchTherapists(
                    userId,
                    gender,
                    ethnicity,
                    profession,
                    experiencedIn,
                    searchTherapistName,
                    [Types.ObjectId(userId.toString())],
                    limit,
                    offset
                );

                return searchResults;
            } catch (error) {
                return error;
            }
        } else {
            const error3 = "Invalid user role.";
            return error3;
        }
    }

    export async function searchTherapists(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const gender = req.body.gender;
        const ethnicity = req.body.ethnicity;
        const profession = req.body.profession;
        const experiencedIn = req.body.experiencedIn;
        const searchTherapistName = req.body.searchTherapistName;
        const limit = Number(req.params.limit);
        const offset = Number(req.params.offset);

        if (req.user.role == UserRole.CLIENT) {
            const client: DClient = req.user as DClient;

            try {
                let searchResults = await TherapistDao.searchTherapists(
                    userId,
                    gender,
                    ethnicity,
                    profession,
                    experiencedIn,
                    searchTherapistName,
                    [Types.ObjectId(client._id.toString())],
                    limit,
                    offset
                );

                return res.sendSuccess(searchResults, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function searchTherapistsPublic(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const gender = req.body.gender;
        const insuranceCompanies = req.body.insuranceCompanies;
        const ethnicity = req.body.ethnicity;
        const profession = req.body.profession;
        const experiencedIn = req.body.experiencedIn;
        const searchTherapistName = req.body.searchTherapistName;
        // const service = req.body.service;
        // const therapyType = req.body.therapyType;
        const state = req.body.state;
        const limit = Number(req.params.limit);
        const offset = Number(req.params.offset);
        let experienceTagSymptoms = [];
        console.log(req.body);

        if (req.body?.experienceTagSymptoms != null) {
            if (Array.isArray(req.body?.experienceTagSymptoms)) {
                experienceTagSymptoms = req.body?.experienceTagSymptoms.map((item: {
                    selectedSymptomId: string
                }) => item.selectedSymptomId);
            } else {
                return res.sendError("No valid experience tag symptoms found.");
            }

        }

        try {
            const searchResults = await TherapistDao.searchTherapistsPublic(
                gender,
                ethnicity,
                insuranceCompanies,
                profession,
                experiencedIn,
                searchTherapistName,
                state,
                limit,
                offset,
                experienceTagSymptoms,
            );
            // if (service == "couple") {
            //   searchResults = await TherapistDao.searchTherapistsPublicCouple(
            //     gender,
            //     ethnicity,
            //     insuranceCompanies,
            //     profession,
            //     experiencedIn,
            //     searchTherapistName,
            //     limit,
            //     offset,
            //     experienceTagSymptoms
            //   );
            // } else if (service == "teen") {
            //   searchResults = await TherapistDao.searchTherapistsPublicTeen(
            //     gender,
            //     ethnicity,
            //     insuranceCompanies,
            //     profession,
            //     experiencedIn,
            //     searchTherapistName,
            //     limit,
            //     offset
            //   );
            // } else {
            //   if (therapyType == "blackTherapy") {
            //     searchResults = await TherapistDao.searchBlackTherapistsPublic(
            //       gender,
            //       ethnicity,
            //       insuranceCompanies,
            //       profession,
            //       experiencedIn,
            //       searchTherapistName,
            //       state,
            //       limit,
            //       offset
            //     );
            //   } else if (therapyType == "mensTherapy") {
            //     searchResults = await TherapistDao.searchMensTherapistsPublic(
            //       gender,
            //       ethnicity,
            //       insuranceCompanies,
            //       profession,
            //       experiencedIn,
            //       searchTherapistName,
            //       state,
            //       limit,
            //       offset
            //     );
            //   } else if (therapyType == "relationshipTherapy") {
            //     searchResults = await TherapistDao.searchRelationshipTherapistsPublic(
            //       gender,
            //       ethnicity,
            //       insuranceCompanies,
            //       profession,
            //       experiencedIn,
            //       searchTherapistName,
            //       state,
            //       limit,
            //       offset
            //     );
            //   } else {
            //     searchResults = await TherapistDao.searchTherapistsPublic(
            //       gender,
            //       ethnicity,
            //       insuranceCompanies,
            //       profession,
            //       experiencedIn,
            //       searchTherapistName,
            //       state,
            //       limit,
            //       offset,
            //       experienceTagSymptoms,
            //     );
            //   }
            // }

            return res.sendSuccess(searchResults, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function allTherapistsPublic(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            let searchResults = await TherapistDao.allTherapistsPublic();

            return res.sendSuccess(searchResults, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function addReview(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const role = req.user.role;
        const therapistId = req.body.therapistId;
        const noOfStars = req.body.noOfStars;
        const review = req.body.review;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        if (!mongoose.Types.ObjectId.isValid(therapistId)) {
            return res.sendError("Invalid object Id");
        } else {
            try {
                let isFound = await TherapistDao.getUserById(therapistId);

                if (!isFound) {
                    return res.sendError(
                        "No existing therapist for the provided therapistId."
                    );
                }

                if (role == UserRole.CLIENT) {
                    try {
                        const reviewDetails: Review = {
                            client: userId,
                            stars: noOfStars,
                            text: review,
                            status: ReviewStatus.PENDING,
                            createdAt: new Date(),
                        };

                        let addedReview = await TherapistDao.addReview(
                            therapistId,
                            reviewDetails
                        );

                        return res.sendSuccess(
                            addedReview,
                            "Thank you for your feedback. Your rating has been submitted."
                        );
                    } catch (error) {
                        return res.sendError(error);
                    }
                } else {
                    return res.sendError("Invalid user role");
                }
            } catch (error) {
                return res.sendError(error);
            }
        }
    }

    export async function viewReviewsByTherapistId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const role = req.user.role;
        const therapistId = req.params.id;

        if (!mongoose.Types.ObjectId.isValid(therapistId)) {
            return res.sendError("Invalid object Id");
        }

        if (role == UserRole.THERAPIST || role == UserRole.CLIENT) {
            try {
                let review = await TherapistDao.viewReviewsByTherapistId(
                    Types.ObjectId(therapistId)
                );

                return res.sendSuccess(review, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function searchClientByParams(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const role = req.user.role;
        const therapistId = req.user._id;
        const gender = req.body.gender;
        const ethnicityId = req.body.ethnicityId;
        const experiencedIn = req.body.experiencedIn;
        const limit = Number(req.params.limit);
        const offset = Number(req.params.offset);

        let messageList: string[] = [];
        let isValid = false;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        async function validateEntries(
            ethnicityId: StringOrObjectId,
            experiencedIn: StringOrObjectId[]
        ) {
            if (ethnicityId !== "-1") {
                if (!mongoose.Types.ObjectId.isValid(ethnicityId)) {
                    messageList.push("Invalid ethnicity Id.");
                    return false;
                }
                try {
                    let ethnicity = await EthnicityDao.getEthnicityById(ethnicityId);
                    if (!ethnicity) {
                        messageList.push("Ethnicity does not exist for the provided id.");
                        return false;
                    }
                } catch (error) {
                    messageList.push(error);
                    return false;
                }
            }

            if (experiencedIn.length > 0) {
                for (let item of experiencedIn) {
                    if (!mongoose.Types.ObjectId.isValid(item)) {
                        messageList.push("Invalid exp tag id.");
                        return false;
                    }
                }
                try {
                    for (let item of experiencedIn) {
                        let expTag = await AdminDao.getExperienceTagById(item);
                        if (!expTag) {
                            messageList.push(
                                "Experience tag does not exist for a provided Id."
                            );
                            return false;
                        }
                    }
                } catch (error) {
                    messageList.push(error);
                    return false;
                }
            }
            return true;
        }

        if (ethnicityId === "-1" && experiencedIn.length === 0) {
            isValid = true;
        } else {
            isValid = await validateEntries(ethnicityId, experiencedIn);
        }

        if (isValid) {
            if (role == UserRole.THERAPIST) {
                try {
                    let searchResult = await TherapistDao.searchClientByParams(
                        gender,
                        therapistId,
                        ethnicityId,
                        experiencedIn,
                        limit,
                        offset
                    );
                    return res.sendSuccess(searchResult, "Success");
                } catch (error) {
                    return res.sendError(error);
                }
            } else {
                return res.sendError("Invalid user role.");
            }
        } else {
            return res.sendError(messageList.toString());
        }
    }

    export async function treatmentHistory(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = req.user._id;
        const clientId = req.body.clientId;
        const note = req.body.note;
        const role = req.user.role;
        if (role == UserRole.THERAPIST) {
            try {
                let user = await ClientDao.getUserById(clientId);

                if (!user) {
                    return res.sendError("No user found for the provided user Id");
                }

                try {
                    const treatmentHistoryDetails: DTreatmentHistory = {
                        clientId: clientId,
                        therapistId: therapistId,
                        note: note,
                        isMeetingTranscribe: false,
                    };

                    let addedTreatmentHistory = await TherapistDao.addTreatmentHistory(
                        treatmentHistoryDetails
                    );

                    if (!addedTreatmentHistory) {
                        return res.sendError("Note could not be added");
                    }

                    return res.sendSuccess(addedTreatmentHistory, "Success");
                } catch (error) {
                    return res.sendError(error);
                }
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("User type cannot access this route.");
        }
    }

    export async function mergeTreatmentHistory(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const therapistId = req.user._id;
            const clientId = req.body.clientId;
            const treatmentHistories = req.body.treatmentHistories;

            let filteredTreatmentHistories: any =
                await TherapistDao.filterTreamentHistoriesByIdList(
                    treatmentHistories,
                    clientId,
                    therapistId
                );

            if (filteredTreatmentHistories.length <= 1) {
                return res.sendError(
                    "Unable to merge these meetings at this moment!"
                );
            }

            let hasSOAP = false;
            let hasPIE = false;

            filteredTreatmentHistories.forEach((history: any) => {
                if (history?.diagnosisNoteId?.updatedByTherapist == true) {
                    const noteType = history?.diagnosisNoteId?.noteType ?? AINotesType.SOAP;

                    if (noteType == AINotesType.SOAP) {
                        hasSOAP = true;
                    } else if (noteType == AINotesType.PIE) {
                        hasPIE = true;
                    }
                }
            });

            if (hasSOAP && hasPIE) {
                return res.sendError("Sorry!. Unable to merge sessions with different note types (SOAP and PIE).", 406);
            }

            filteredTreatmentHistories.sort(
                (a: any, b: any) =>
                    a.meetingId.callingStatus - b.meetingId.callingStatus
            );

            filteredTreatmentHistories.sort((a: any, b: any) => {
                if (
                    a.meetingId.callingStatus == CallingStatus.COMPLETED &&
                    b.meetingId.callingStatus !== CallingStatus.COMPLETED
                ) {
                    return -1;
                }

                if (
                    b.meetingId.callingStatus == CallingStatus.COMPLETED &&
                    a.meetingId.callingStatus !== CallingStatus.COMPLETED
                ) {
                    return 1;
                }

                return 0;
            });

            filteredTreatmentHistories.sort((a: any, b: any) => {
                if (
                    a.diagnosisNoteId.updatedByTherapist == true &&
                    b.diagnosisNoteId.updatedByTherapist !== true
                ) {
                    return -1;
                }

                if (
                    b.diagnosisNoteId.updatedByTherapist == true &&
                    a.diagnosisNoteId.updatedByTherapist !== true
                ) {
                    return 1;
                }

                return 0;
            });

            let diagnosisNoteIdList = [];
            let meetingIdList = [];
            let zoomMeetingIdList = [];
            let oldMergeLists: Types.ObjectId[] = [];
            let removeTreamentIdList: any[] = [];
            let meetingsInDiffDates = false;
            let meetingDatesArray: any[] = [];
            let totalSpentDuration = 0;

            let i = 0;

            for (const element of filteredTreatmentHistories) {
                var dateObj = element.meetingStartedTime;
                const estDate = moment
                    .utc(dateObj)
                    .tz("America/New_York")
                    .format("YYYY-MM-DD");

                if (i != 0) {
                    diagnosisNoteIdList.push(element.diagnosisNoteId);
                    removeTreamentIdList.push(element._id);

                    if (!meetingDatesArray.includes(estDate)) {
                        meetingsInDiffDates = true;
                    }
                }

                meetingDatesArray.push(estDate.valueOf());

                oldMergeLists.push(...element.mergedMeetings);
                meetingIdList.push(element.meetingId._id);
                zoomMeetingIdList.push(element.meetingId.meetingId);
                totalSpentDuration += parseFloat(element.meetingId.spentDuration);

                i++;
            }

            if (meetingsInDiffDates) {
                return res.sendError(
                    "Sorry!. Unable to merge sessions in different dates.",
                    406
                );
            }

            let arr = oldMergeLists.concat(meetingIdList);

            let arrW: string[] = [];

            for (const element of arr) {
                arrW.push(element.toString());
            }

            let mergedArr = arrW.filter(function (item, pos) {
                return arrW.indexOf(item) == pos;
            });

            let allTranscribesList: any =
                await TherapistDao.filterTranscribesByIdList(
                    zoomMeetingIdList,
                    clientId,
                    therapistId
                );

            let finalTranscriptText: any[] = [];

            for (const element of allTranscribesList) {
                finalTranscriptText.push(...element.transcriptText);
            }

            const response = await TherapistDao.updateMergedRecords(
                filteredTreatmentHistories[0]._id,
                filteredTreatmentHistories[0].meetingId._id,
                filteredTreatmentHistories[0].meetingId.meetingId,
                mergedArr,
                diagnosisNoteIdList,
                removeTreamentIdList,
                finalTranscriptText,
                clientId,
                therapistId,
                totalSpentDuration
            );

            return res.sendSuccess(response, "Meetings are merged successfully.");
        } catch (error) {
            return res.sendError(error);
        }

    }

    export async function viewTreatmentHistoryByClientId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const role = req.user.role;
            const therapistid = req.user._id;
            const clientId = req.params.clientId;
            const limit = Number(req.params.limit);
            const offset = Number(req.params.offset);

            if (!mongoose.Types.ObjectId.isValid(clientId)) {
                return res.sendError("Invalid object Id");
            }

            if (role == UserRole.THERAPIST) {
                try {
                    let review = await TherapistDao.viewTreatmentHistorysByClientId(
                        Types.ObjectId(clientId),
                        therapistid,
                        limit,
                        offset
                    );

                    return res.sendSuccess(review, "Success");
                } catch (error) {
                    return res.sendError(error);
                }
            } else {
                return res.sendError("Invalid user role.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function viewTreatmentHistoryByTherapistIdStats(
        userRole: any,
        _id: any,
        userLimit: any,
        userOffset: any
    ) {
        const therapistId = _id;
        const role = userRole;
        const limit = Number(userLimit);
        const offset = Number(userOffset);

        if (role == UserRole.THERAPIST) {
            try {
                let review = await TherapistDao.viewAllTreatmentHistorysByTherapistId(
                    Types.ObjectId(therapistId),
                    limit,
                    offset
                );
                return review;
            } catch (error) {
                return error;
            }
        } else {
            const error3 = "Invalid user role.";
            return error3;
        }
    }

    export async function viewTreatmentHistoryByTherapistIdViewMore(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const role = req.user.role;
            // const clientId = req.user._id;
            const therapistId = req.params.therapistId;
            const searchClientName = req.body.searchClientName;
            const limit = Number(req.params.limit);
            const offset = Number(req.params.offset);

            if (!mongoose.Types.ObjectId.isValid(therapistId)) {
                return res.sendError("Invalid object Id");
            }

            if (role == UserRole.THERAPIST) {
                try {
                    let review =
                        await TherapistDao.viewTreatmentHistorysByTherapistIdViewMore(
                            Types.ObjectId(therapistId),
                            searchClientName,
                            // clientId,
                            limit,
                            offset
                        );

                    return res.sendSuccess(review, "Success");
                } catch (error) {
                    return res.sendError(error);
                }
            } else {
                return res.sendError("Invalid user role.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function viewTreatmentHistoryByTherapistId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const role = req.user.role;
            const clientId = req.user._id;
            const therapistId = req.params.therapistId;
            const limit = Number(req.params.limit);
            const offset = Number(req.params.offset);

            if (!mongoose.Types.ObjectId.isValid(therapistId)) {
                return res.sendError("Invalid object Id");
            }

            if (role == UserRole.CLIENT) {
                try {
                    let review = await TherapistDao.viewTreatmentHistorysByTherapistId(
                        Types.ObjectId(therapistId),
                        clientId,
                        limit,
                        offset
                    );

                    return res.sendSuccess(review, "Success");
                } catch (error) {
                    return res.sendError(error);
                }
            } else {
                return res.sendError("Invalid user role.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export function deleteTreatmentHistory(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        let noteId = req.params._id;

        if (!mongoose.Types.ObjectId.isValid(noteId)) {
            return res.sendError("Invalid object Id");
        }

        TherapistDao.deleteTreatmentHistoryById(Types.ObjectId(noteId))
            .then((treat) => {
                res.sendSuccess(treat, "Treatment Note Deleted Successfully.");
            })
            .catch(next);
    }

    export async function updateTreatmentHistory(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = req.user._id;
        const noteId = req.body.noteId;
        const role = req.user.role;
        const clientId = req.body.clientId;
        const note = req.body.note;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        if (role == UserRole.THERAPIST) {
            const details: DTreatmentHistory = {
                note: note,
                isMeetingTranscribe: false,
            };

            try {
                let updatedTherapist = await TherapistDao.updateTreatmentHistoryById(
                    noteId,
                    details
                );

                if (!updatedTherapist) {
                    return res.sendError("Error while updating the therapist.");
                }

                return res.sendSuccess(
                    updatedTherapist,
                    "Therapist details are successfully updated."
                );
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function updateProfileVideo(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const vimeoId = req.body.vimeoId;

        const therapist: DTherapist = {
            vimeoId: vimeoId,
        };

        if (!vimeoId) {
            return res.sendError("Please upload valid file.");
        }

        let updatedTherapist = await UserDao.updateUser(userId, therapist);

        if (!updatedTherapist) {
            return res.sendError("Failed to update the therapist.");
        }

        return res.sendSuccess(updatedTherapist, "Successfully updated.");
    }

    export async function getTherapistByProfessionId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const role = req.user.role;
        const pId = req.params.pId;
        const limit = Number(req.params.limit);
        const offset = Number(req.params.offset);

        if (!pId) {
            return res.sendError("Need Profession Id");
        }

        if (role == UserRole.SUPER_ADMIN || UserRole.ADMIN || UserRole.SUB_ADMIN) {
            try {
                if (req.user.role == UserRole.SUB_ADMIN) {
                    const ownUser = await UserDao.getUserByUserId(req.user._id);
                    if (ownUser.adminPermission.viewProfessions != true) {
                        return res.sendError(
                            "You don't have permission for View Profession!"
                        );
                    }
                }

                let therapists = await UserDao.getTherapistByProfessionId(
                    Types.ObjectId(pId),
                    limit,
                    offset
                );

                const countUser = await AdminDao.getAllTherapistsCount();

                const count = countUser - limit * offset;

                const data = {
                    therapistSet: therapists,
                    count: count,
                };

                return res.sendSuccess(data, "Therapists by Profession Id");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("No permission to access!");
        }
    }

    export async function sendReminderSms(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            AppLogger.info(`.:: Send Reminder SMS By Therapist ::.`);
            const appointmentId = req.body.appointmentId;
            let appointment = await AppointmentDao.getAppointmentSendSMSById(
                appointmentId
            );
            const therapistAppintment = appointment as any;
            const utcTime = moment.utc(appointment.start);
            const estTime = utcTime.tz("America/New_York");
            const smsSend = await SMSService.sendEventSMS(
                `Lavni Reminder: You have a session with your ${
                    therapistAppintment?.therapistId?.firstname
                } ${therapistAppintment?.therapistId?.lastname} at  ${estTime.format(
                    "YYYY-MM-DD hh:mm A"
                )}.`,

                therapistAppintment.clientId?.primaryPhone
            );
            if (smsSend) {
                return res.sendSuccess("Reminder SMS sent successfully");
            } else {
                return res.sendError("Failed to send reminder SMS");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function createPin(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = req.user._id;
        const pinNumber = req.body.pinNumber;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        try {
            const updatedTherapist = await TherapistDao.updateTherapistPinNumber(
                therapistId,
                pinNumber
            );

            if (!updatedTherapist) {
                return res.sendError("Error while updating the therapist.");
            }

            return res.sendSuccess(
                updatedTherapist,
                "Therapist pin is successfully updated."
            );
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updatePin(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = req.user._id;
        const newPinNumber = req.body.newPinNumber;
        // const oldPinNumber = req.body.oldPinNumber;
        // const confirmPinNumber = req.body.confirmPinNumber;
        const password = req.body.password;
        const role = req.user.role;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        if (role == UserRole.THERAPIST) {
            try {
                const user: IUser = await UserDao.getUserById(therapistId);
                let isMatch = await user.comparePassword(password);
                if (!isMatch) {
                    return res.sendError("Invalid Password.");
                }
                // if (user.pinNumber != oldPinNumber){
                //     return res.sendError("Incorrect old pin.");
                // }
                // if (newPinNumber != confirmPinNumber){
                //     return res.sendError("Mismatched new pin and confirmation pin.");
                // }
                const updatedTherapist = await TherapistDao.updateTherapistPinNumber(
                    therapistId,
                    newPinNumber
                );

                if (!updatedTherapist) {
                    return res.sendError("Error while updating the therapist.");
                }

                return res.sendSuccess(
                    updatedTherapist,
                    "Therapist pin is successfully updated."
                );
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid User");
        }
    }

    export async function generateSoapWithOpenAI(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const openAIAPIKey = process.env.OPEN_AI_API_KEY;
            const mongoConnectString = process.env.MONGOOSE_URI;
            const mongoDBName = process.env.MONGO_DB_NAME;

            const meetingId = req.body.meetingId;

            const meetingData = await UserDao.getClientAndTherapistEmailsByMeetingId(
                meetingId
            );

            if (!meetingData) {
                return res.sendError("Invalid meeting id.");
            }

            const transcribeData = await VideoCallDao.getTranscribeByMeetingId(
                meetingData.meetingId
            );

            if (!transcribeData || transcribeData.transcriptText.length == 0) {
                return res.sendError(
                    "Unable to find transcribe data for this meeting.",
                    404
                );
            }

            var spawn = require("child_process").spawn;

            let platform = process.platform === "win32" ? "python" : "python3";

            var process1 = spawn(
                platform,
                [
                    "./src/python_scripts/open_ai.py",
                    openAIAPIKey,
                    `"${meetingId}"`,
                    mongoConnectString,
                    mongoDBName,
                ],
                {shell: true}
            );

            process1.stdout.on("data", async function (data: any) {
                let processedText = data
                    .toString()
                    .replace(/(\r\n|\n|\r)/gm, "")
                    .replace(/ :/g, ":");

                await VideoCallDao.getTranscribeByMeetingIdAndUpdateAIResponse(
                    meetingId,
                    processedText
                );

                let subjective;
                var subArray1 = processedText.split("Subjective:");
                subArray1.shift();
                var sub1 = subArray1.join("Subjective:");
                subjective = sub1.split("Objective:").shift();

                if (subjective == "" || subjective == null) {
                    var subArray1 = processedText.split("S:");
                    subArray1.shift();
                    var sub1 = subArray1.join("S:");
                    subjective = sub1.split("O:").shift();
                }

                let objective;
                var subArray2 = processedText.split("Objective:");
                subArray2.shift();
                var sub2 = subArray2.join("Objective:");
                objective = sub2.split("Assessment:").shift();

                if (objective == "" || objective == null) {
                    var subArray2 = processedText.split("O:");
                    subArray2.shift();
                    var sub2 = subArray2.join("O:");
                    objective = sub2.split("A:").shift();
                }

                let assessment;
                var subArray3 = processedText.split("Assessment:");
                subArray3.shift();
                var sub3 = subArray3.join("Assessment:");
                assessment = sub3.split("Plan:").shift();

                if (assessment == "" || assessment == null) {
                    var subArray3 = processedText.split("A:");
                    subArray3.shift();
                    var sub3 = subArray3.join("A:");
                    assessment = sub3.split("P:").shift();
                }

                let plan;
                var subArray4 = processedText.split("Plan:");
                subArray4.shift();
                plan = subArray4.join("Plan:");

                if (plan == "" || plan == null) {
                    var subArray4 = processedText.split("P:");
                    subArray4.shift();
                    plan = subArray4.join("P:");
                }

                const dataExtracted = {
                    subjective: subjective.trim(),
                    objective: objective.trim(),
                    assessment: assessment.trim(),
                    plan: plan.trim(),
                };

                return res.sendSuccess(dataExtracted, "Successfully Generated SOAP.");
            });
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateAiGeneratedCount(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = req.user._id;
        const aiGenerateCount = req.body.aiGenerateCount;

        const role = req.user.role;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        if (role == UserRole.THERAPIST) {
            try {
                const updatedTherapist = await TherapistDao.updateAiGeneratedCount(
                    therapistId,
                    aiGenerateCount
                );

                if (!updatedTherapist) {
                    return res.sendError("Error while updating the therapist.");
                }

                return res.sendSuccess(
                    updatedTherapist,
                    "Therapist Ai Generated Count is successfully updated."
                );
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid User");
        }
    }

    // export async function getAllTherapists(
    //   req: Request,
    //   res: Response,
    //   next: NextFunction
    // ) {
    //   const userId = req.user._id;
    //   const limit = Number(req.params.limit);
    //   const offset = Number(req.params.offset);

    //   if (req.user.role == UserRole.CLIENT) {
    //     try {
    //       let therapistList = await TherapistDao.getAllTherapists(
    //         userId,
    //         limit,
    //         offset
    //       );

    //       return res.sendSuccess(therapistList, "Success");
    //     } catch (error) {
    //       return res.sendError(error);
    //     }
    //   } else {
    //     return res.sendError("Invalid user role.");
    //   }
    // }

    export async function getAiGeneratedCountFromRequest(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;

        if (req.user.role == UserRole.THERAPIST) {
            try {
                let countList = await TherapistDao.getAiGeneratedCount(userId);

                return res.sendSuccess(countList, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function TherapistReview(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;

        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }

            const review = req.body.review;
            const stars = req.body.noOfStars;

            const therapistReview: DCustomerReview = {
                therapistId: userId,
                review: review,
                stars: stars,
                ganarateSOAP: true,
                status: ReviewStatus.PENDING,
            };

            let response = await UserDao.therapistReviewDao(therapistReview);

            if (response) {
                const updatedTherapist = await TherapistDao.updateAiGenerateReview(
                    userId
                );
            }

            if (response) {
                return res.sendSuccess(response, "Review added Successfully.");
            } else {
                return res.sendError("Review could not be added. Please try again.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getTherapistDashboardStats(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const role = req.user.role;
        const userId = req.user._id;
        const filterValue = req.body.filterValue;
        if (role == UserRole.THERAPIST) {
            try {
                let upcommingAppointments =
                    await AppointmentEp.viewAllUpcomingAppointmentStats(role, userId, 3);
                let pendingAppointments =
                    await AppointmentEp.viewAllPendingAppointmentStats(role, userId, 3);
                let matchedClients =
                    await FriendRequestEp.viewAllRequestsByTherapistStats(
                        role,
                        userId,
                        3,
                        1
                    );
                let allTreatmentSessions =
                    await TherapistEp.viewTreatmentHistoryByTherapistIdStats(
                        role,
                        userId,
                        10,
                        1
                    );
                let therapistAppointmentStats =
                    await AppointmentEp.getAllStatisticsOfAppointmentStat(
                        userId,
                        role,
                        filterValue
                    );
                let therapistEarningsStats = await TransactionsEp.getAllEarnings(
                    userId,
                    role,
                    filterValue
                );
                let therapistTotalEarningsStats =
                    await TransactionsEp.getAllTotalEarnings(userId, role);
                let getAllUpCommingGroupCalls =
                    await VonageCallGroupEp.viewAllUpCommingGroupCalls(role, userId, 3);

                let getTherapistMeetingStats = await FriendRequestEp.getTherapistMeetingStats(role, userId);

                const therapistScore = await TherapistDao.getTherapistScoreByTherapistID(userId);

                let data = {
                    upcommingAppointmentStats: upcommingAppointments,
                    pendingAppointmentStats: pendingAppointments,
                    matchedClientStats: matchedClients,
                    allTreatmentSessionStats: allTreatmentSessions,
                    therapistAppointmentStats: therapistAppointmentStats,
                    therapistEarningsStats: therapistEarningsStats,
                    therapistTotalEarningsStats: therapistTotalEarningsStats,
                    allUpCommingGroupCalls: getAllUpCommingGroupCalls,
                    therapistMeetingStats: getTherapistMeetingStats,
                    therapistScore
                };

                return res.sendSuccess(data, "statistics data.");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("you have not permission");
        }
    }

    export async function sendReferralMail(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const emailAddress = req.body.emailAddress;
        const message = req.body.messageContent;
        const updatedMessage = message.replace(/\n\n/g, "<br/><br/>");
        const messageContent = updatedMessage;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }
        if (!emailAddress) {
            return res.sendError("email is required.");
        }
        if (messageContent === undefined || messageContent === null) {
            return res.sendError("No Message body");
        } else {
            try {
                await EmailService.sendTherapistReferralLinkEmailByTherapist(
                    emailAddress,
                    messageContent
                );
                return res.sendSuccess("Success");
            } catch (error) {
                return res.sendError(error);
            }
        }
    }

    export async function sendReferralSMS(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const messageContent = req.body.messageContent;
        const primaryPhone = req.body.phoneNumber;
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }
        if (!primaryPhone) {
            return res.sendError("Phone number is required");
        }
        if (messageContent === undefined || null) {
            return res.sendError("No Message body");
        }

        try {
            await SMSService.sendEventSMS(messageContent, primaryPhone);

            return res.sendSuccess("Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function makeClientInactive(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const clientId = req.body.clientId;
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }

            if (!clientId) {
                return res.sendError("Client Id is required.");
            }

            let client = await ClientDao.getUserById(clientId);

            if (!client || client == null) {
                return res.sendError("No existing client for the provided client Id.");
            }

            let isFriend = await FriendRequestDao.checkIfUserIsFriend(
                client._id,
                req.user._id
            );

            if (!isFriend) {
                return res.sendError(
                    "Sorry! Selected client has not connected with this therapist yet."
                );
            }

            let clientUser = await User.findOneAndUpdate(
                {_id: clientId},
                {$set: {clientActiveStatus: false}},
                {new: true}
            );

            if (clientUser != null) {
                await Appointment.updateMany(
                    {
                        clientId: clientId,
                        status: {
                            $in: [
                                AppointmentStatus.WAITING_FOR_APPROVAL,
                                AppointmentStatus.PENDING,
                            ],
                        },
                    },
                    {
                        $set: {status: AppointmentStatus.REJECTED},
                    }
                );

                await EmailService.sendAccountInactivationByTherapistEmailForClients(
                    clientUser,
                    "Supporting Your Continued Well-Being: Reactivation of Your Account"
                );

                return res.sendSuccess(
                    {},
                    "Client " +
                    client.firstname +
                    " " +
                    client.lastname +
                    " is disabled successfully."
                );
            } else {
                return res.sendError(
                    "Sorry, Unable to disable this client at this time."
                );
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function validateAppoimentsAvailableInBlockDaterange(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const blockDatesRange = req.body.blockDatesRange;

        try {
            if (blockDatesRange) {
                const appointmentsAvailable =
                    await AppointmentDao.getAppointmentsOfTherapistByDateRange(
                        blockDatesRange.start,
                        blockDatesRange.end,
                        req.user._id
                    );

                return res.sendSuccess(appointmentsAvailable, "Appointments Found.");
            } else {
                return res.sendError("No block dates found.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateBlockedDates(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const blockedDates = req.body.blockedDates;
        const newBlockDates = req.body.newBlockDates;

        try {
            if (newBlockDates) {
                let areAppointmentsAvailable = false;

                await Promise.all(
                    newBlockDates.map(async (blockDate: any) => {
                        const appointmentsAvailable =
                            await AppointmentDao.getAppointmentsOfTherapistByDateRange(
                                blockDate.start,
                                blockDate.end,
                                req.user._id
                            );

                        if (appointmentsAvailable.length > 0) {
                            areAppointmentsAvailable = true;
                        }
                    })
                );

                if (areAppointmentsAvailable) {
                    return res.sendError(
                        "Sorry! Already scheduled appointments during the time range you picked."
                    );
                }
            }

            let updatedTherapist = await User.findOneAndUpdate(
                {_id: req.user._id},
                {$set: {blockedDates: blockedDates}},
                {new: true}
            );

            if (updatedTherapist) {
                return res.sendSuccess(
                    blockedDates,
                    "Blocked dates are updated successfully."
                );
            } else {
                return res.sendError(
                    "Sorry, Unable to update blocked dates at this time."
                );
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function addBlockedDates(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const newBlockDates = req.body.newBlockDates;

        try {
            if (!newBlockDates || !Array.isArray(newBlockDates) || newBlockDates.length === 0) {
                return res.sendError("Please provide valid dates to block.");
            }

            // Kiểm tra xem có lịch hẹn trong khoảng thời gian đã chọn không
            let areAppointmentsAvailable = false;

            await Promise.all(
                newBlockDates.map(async (blockDate: any) => {
                    const appointmentsAvailable =
                        await AppointmentDao.getAppointmentsOfTherapistByDateRange(
                            blockDate.start,
                            blockDate.end,
                            req.user._id
                        );

                    if (appointmentsAvailable.length > 0) {
                        areAppointmentsAvailable = true;
                    }
                })
            );

            if (areAppointmentsAvailable) {
                return res.sendError(
                    "Sorry! Already scheduled appointments during the time range you picked."
                );
            }

            // Lấy danh sách blocked dates hiện tại của therapist
            const therapist = await User.findById(req.user._id);
            if (!therapist) {
                return res.sendError("User not found.");
            }

            // Tạo một danh sách mới kết hợp các blocked dates hiện tại và mới
            const currentBlockedDates = therapist.blockedDates || [];
            
            // Kiểm tra trùng lặp giữa newBlockDates và currentBlockedDates
            const duplicateDates = [];
            const uniqueNewDates = newBlockDates.filter((newDate: any) => {
                const isDuplicate = currentBlockedDates.some((existingDate: any) => {
                    // Chuyển đổi chuỗi thời gian thành đối tượng Date để so sánh
                    const newStartDate = new Date(newDate.start).getTime();
                    const newEndDate = new Date(newDate.end).getTime();
                    const existingStartDate = new Date(existingDate.start).getTime();
                    const existingEndDate = new Date(existingDate.end).getTime();
                    
                    // So sánh timestamp thay vì chuỗi
                    return (
                        newStartDate === existingStartDate && 
                        newEndDate === existingEndDate
                    );
                });
                
                if (isDuplicate) {
                    duplicateDates.push(newDate);
                    return false;
                }
                return true;
            });
            
            // Nếu tất cả khoảng thời gian đều trùng lặp
            if (uniqueNewDates.length === 0) {
                return res.sendSuccess(
                    currentBlockedDates,
                    "No new dates added. All dates were already blocked."
                );
            }

            const combinedBlockedDates = [...currentBlockedDates, ...uniqueNewDates];

            // Cập nhật blocked dates của therapist
            let updatedTherapist = await User.findOneAndUpdate(
                {_id: req.user._id},
                {$set: {blockedDates: combinedBlockedDates}},
                {new: true}
            );

            if (updatedTherapist) {
                return res.sendSuccess(
                    combinedBlockedDates,
                    `Blocked dates added successfully. ${uniqueNewDates.length} new dates added, ${duplicateDates.length} duplicates skipped.`
                );
            } else {
                return res.sendError(
                    "Sorry, Unable to add blocked dates at this time."
                );
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateTherapistAvailableTimes(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const updatedWorkingHours = req.body.workingHours;
        const therapist = await TherapistDao.getUserById(userId);

        therapist.workingHours = updatedWorkingHours;

        therapist.save();

        return res.sendSuccess(therapist, "Successfully updated working hours.");
    }

    export async function updateTherapistSignature(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const signature = req.body.signature;
        try {
            const data = {
                signature: signature,
            };
            const updatedTherapist = await TherapistDao.updateTherapist(userId, data);

            return res.sendSuccess(
                updatedTherapist,
                "Successfully updated signature."
            );
        } catch (error) {
            return res.sendError("error:", error);
        }
    }

    export async function getTherapistsList(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        // const userId = req.user._id;
        const gender = req.body.gender;
        const ethnicity = req.body.ethnicity;
        const profession = req.body.profession;
        const experiencedIn = req.body.experiencedIn;
        const searchTherapistName = req.body.searchTherapistName;
        const limit = Number(req.params.limit);
        const offset = Number(req.params.offset);

        const clientId = req?.body?.clientId;

        if (!clientId) {
            return res.sendError("client Id is required");
        }
        if (req.user.role == UserRole.THERAPIST) {
            const client: DClient = req.user as DClient;

            try {
                const client = await UserDao.getUserById(clientId);

                console.log("client===============>", client);
                let searchResults = await TherapistDao.getSearchTherapistsList(
                    // userId,
                    gender,
                    ethnicity,
                    profession,
                    experiencedIn,
                    searchTherapistName,
                    // [Types.ObjectId(client._id.toString())],
                    limit,
                    offset
                );
                console.log("searchResults==========>", searchResults);

                return res.sendSuccess(searchResults, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function addSubTherapistToTreatmentHistory(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const treatmentHistoryId = req.body.treatmentHistoryId;
        const subTherapistId = req.body.subTherapistId;
        const role = req.user.role;

        if (!treatmentHistoryId) {
            return res.sendError("Treatment History Id is required");
        }

        if (!subTherapistId) {
            return res.sendError("Sub Therapist Id is required");
        }

        try {
            let subTherapist = await TherapistDao.getUserById(subTherapistId);
            let treatmentHistory = await TherapistDao.getTreatmentById(
                treatmentHistoryId
            );

            if (!subTherapist) {
                return res.sendError("No user found for the provided sub therapist Id");
            }

            if (!treatmentHistory) {
                return res.sendError("No treatment history found for the provided Id");
            }

            try {
                let updatedTreatmentHistory =
                    await TherapistDao.updateTreatmentHistorySubTherapistById(
                        treatmentHistoryId,
                        subTherapistId
                    );

                if (!updatedTreatmentHistory) {
                    return res.sendError("Note could not be updated");
                }

                return res.sendSuccess(updatedTreatmentHistory, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getTherapistScoreDetails(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const {limit, offset} = req.params;

            const therapistScoreList = await TherapistDao.getTherapistScoreDetails(Number(limit), Number(offset));

            return res.sendSuccess(therapistScoreList);
        } catch (error) {
            AppLogger.error(`Calculating therapist score error occured`);
        }
    }

    export async function getScoreOfTheTherapist() {
        AppLogger.info(`start calculating therapist score cron job`);
        try {
            const therapistList = await TherapistDao.getAlltherapistsForScoring();
            if (therapistList.length == 0) {
                AppLogger.info(`No therapists found!`);
                return;
            }

            const therapistScoreConstants = await TherapistDao.getTherapistScoreConstants();
            if (therapistScoreConstants.length == 0) {
                AppLogger.info(`No therapist score constants found!`);
                return;
            }
            const therapistScoreConstantValues: ITherapistScoreConstants = therapistScoreConstants[0];

            const promises: any = therapistList.map(async therapi => {
                try {
                    const therapistData: any = await TherapistDao.getScoreOfTheTherapist(therapi._id);

                    const response_time: number = therapistData?.totalResponseTimeInHours ? therapistData?.totalResponseTimeInHours : 0;
                    const availability: number = therapistData?.availability ? therapistData?.availability : 0;
                    const follow_up_appointments: number = therapistData?.noOfFollowUpAppointments ? therapistData?.noOfFollowUpAppointments : 0;
                    const number_of_sessions: number = therapistData?.totalSessions ? therapistData?.totalSessions : 0;
                    const missed_appointments: number = therapistData?.missedAppointments ? therapistData?.missedAppointments : 0;
                    const loyalty_years: number = moment().diff(therapi?.createdAt, 'months');
                    const number_of_scheduled_appointments: number = therapistData?.noOfScheduledAppointments ? therapistData?.noOfScheduledAppointments : 0;
                    const number_of_matches: number = therapistData?.noOfFriendRequests ? therapistData?.noOfFriendRequests : 0;

                    const response_time_in_days: number = response_time / 24;
                    // if(therapi._id == "65bec7f93fc37fcf6959ba10"){
                    //   console.log("0 response_time ", response_time_in_days);
                    //   console.log("1 availability ", availability);
                    //   console.log("2 follow_up_appointments ", follow_up_appointments);
                    //   console.log("3 number_of_sessions ", number_of_sessions);
                    //   console.log("4 missed_appointments ", missed_appointments);
                    //   console.log("5 loyalty_years ", loyalty_years);
                    //   console.log("6 number_of_scheduled_appointments ", number_of_scheduled_appointments);
                    //   console.log("7 number_of_matches ", number_of_matches);

                    // }
                    const dataForTherapistScoreTable = {
                        therapistId: therapi._id,
                        responseTime: response_time_in_days,
                        availability,
                        followUpAppointments: follow_up_appointments,
                        totalSessions: number_of_sessions,
                        missedAppointments: missed_appointments,
                        loyaltyYears: loyalty_years,
                        scheduledAppointments: number_of_scheduled_appointments,
                        noOfMatches: number_of_matches,
                    };

                    return dataForTherapistScoreTable;
                } catch (error) {
                    console.log(error);

                    AppLogger.error(`Error happend while getScoreOfTheTherapist for theraphist id : ${therapi?._id}`);
                    return null;
                }

            });

            let results: any = await Promise.all(promises);
            results = results.filter((result: any) => result !== null);

            let maxResponseTime: number = 0;
            let minResponseTime: number = Infinity;
            let maxAvailability: number = 0;
            let minAvailability: number = Infinity;
            let maxFollowUpAppointments: number = 0;
            let minFollowUpAppointments: number = Infinity;
            let maxTotalSessions: number = 0;
            let minTotalSessions: number = Infinity;
            let maxMissedAppointments: number = 0;
            let minMissedAppointments: number = Infinity;
            let maxLoyaltyYears: number = 0;
            let minLoyaltyYears: number = Infinity;
            let maxScheduledAppointments: number = 0;
            let minScheduledAppointments: number = Infinity;
            let maxNoOfMatches: number = 0;
            let minNoOfMatches: number = Infinity;

            results.forEach((obj: any) => {
                const responseTime = obj.responseTime >= 0 ? obj.responseTime : 0;
                const availability = obj.availability >= 0 ? obj.availability : 0;
                const followUpAppointments = obj.followUpAppointments >= 0 ? obj.followUpAppointments : 0;
                const totalSessions = obj.totalSessions >= 0 ? obj.totalSessions : 0;
                const missedAppointments = obj.missedAppointments >= 0 ? obj.missedAppointments : 0;
                const loyaltyYears = obj.loyaltyYears >= 0 ? obj.loyaltyYears : 0;
                const scheduledAppointments = obj.scheduledAppointments >= 0 ? obj.scheduledAppointments : 0;
                const noOfMatches = obj.noOfMatches >= 0 ? obj.noOfMatches : 0;

                if (responseTime > maxResponseTime) {
                    maxResponseTime = responseTime;
                }
                if (responseTime < minResponseTime) {
                    minResponseTime = responseTime;
                }
                if (availability > maxAvailability) {
                    maxAvailability = availability;
                }
                if (availability < minAvailability) {
                    minAvailability = availability;
                }
                if (followUpAppointments > maxFollowUpAppointments) {
                    maxFollowUpAppointments = followUpAppointments;
                }
                if (followUpAppointments < minFollowUpAppointments) {
                    minFollowUpAppointments = followUpAppointments;
                }
                if (totalSessions > maxTotalSessions) {
                    maxTotalSessions = totalSessions;
                }
                if (totalSessions < minTotalSessions) {
                    minTotalSessions = totalSessions;
                }
                if (missedAppointments > maxMissedAppointments) {
                    maxMissedAppointments = missedAppointments;
                }
                if (missedAppointments < minMissedAppointments) {
                    minMissedAppointments = missedAppointments;
                }
                if (loyaltyYears > maxLoyaltyYears) {
                    maxLoyaltyYears = loyaltyYears;
                }
                if (loyaltyYears < minLoyaltyYears) {
                    minLoyaltyYears = loyaltyYears;
                }
                if (scheduledAppointments > maxScheduledAppointments) {
                    maxScheduledAppointments = scheduledAppointments;
                }
                if (scheduledAppointments < minScheduledAppointments) {
                    minScheduledAppointments = scheduledAppointments;
                }
                if (noOfMatches > maxNoOfMatches) {
                    maxNoOfMatches = noOfMatches;
                }
                if (noOfMatches < minNoOfMatches) {
                    minNoOfMatches = noOfMatches;
                }
            });

            // Print or use the max and min response times
            // console.log("Maximum response time:", maxResponseTime);
            // console.log("Minimum response time:", minResponseTime);
            // console.log("maxAvailability: ", maxAvailability);
            // console.log("minAvailability:", minAvailability);
            // console.log("maxFollowUpAppointments:", maxFollowUpAppointments);
            // console.log("minFollowUpAppointments:", minFollowUpAppointments);
            // console.log("maxTotalSessions:", maxTotalSessions);
            // console.log("minTotalSessions:", minTotalSessions);
            // console.log("maxScheduledAppointments:", maxScheduledAppointments);
            // console.log("minScheduledAppointments:", minScheduledAppointments);
            // console.log("maxNoOfMatches:", maxNoOfMatches);
            // console.log("minNoOfMatches:", minNoOfMatches);
            // console.log("maxLoyality:", maxLoyaltyYears);
            // console.log("minLoyaloty:", minLoyaltyYears);
            const maxScore: number = (
                (therapistScoreConstantValues?.responseTimeWeight * 1) +
                therapistScoreConstantValues?.availabilityWeight * 1 +
                therapistScoreConstantValues?.followUpAppointmentsWeight * 1 +
                therapistScoreConstantValues?.totalSessionsWeight * 1 -
                therapistScoreConstantValues?.missedAppointmentsWeight * 0 +
                therapistScoreConstantValues?.loyalityYearsWeight * 1 +
                therapistScoreConstantValues?.scheduledAppointmentsWeight * 1 +
                therapistScoreConstantValues?.noOfMatchesWeight * 1);

            // console.log("Max Scoreeeeeeeeeeeeeee ", maxScore);


            const resultsAfterMinMaxScaler: any = results.map(async (therapi: any) => {

                const therapistId = therapi.therapistId;
                const responseTime: number = therapi.responseTime >= 0 ? therapi.responseTime : 0;
                const availability: number = therapi.availability >= 0 ? therapi.availability : 0;
                const followUpAppointments: number = therapi.followUpAppointments >= 0 ? therapi.followUpAppointments : 0;
                const totalSessions: number = therapi.totalSessions >= 0 ? therapi.totalSessions : 0;
                const missedAppointments: number = therapi.missedAppointments >= 0 ? therapi.missedAppointments : 0;
                const loyaltyYears: number = therapi.loyaltyYears >= 0 ? therapi.loyaltyYears : 0;
                const scheduledAppointments: number = therapi.scheduledAppointments >= 0 ? therapi.scheduledAppointments : 0;
                const noOfMatches: number = therapi.noOfMatches >= 0 ? therapi.noOfMatches : 0;

                let calculatedResponseTime: number;
                if (responseTime == 0) {
                    // If therapist has no any data relevant for the calculation of response time.
                    calculatedResponseTime = 1;
                } else {
                    calculatedResponseTime = (maxResponseTime - minResponseTime) <= 0 ? 0 : (responseTime - minResponseTime) / (maxResponseTime - minResponseTime);
                }
                // const calculatedResponseTime: number = (maxResponseTime - minResponseTime) <= 0 ? 0 : (responseTime - minResponseTime) / (maxResponseTime - minResponseTime);
                const calculatedAvailability: number = (maxAvailability - minAvailability) <= 0 ? 0 : (availability - minAvailability) / (maxAvailability - minAvailability);
                const calculatedFollowUpAppointments: number = (maxFollowUpAppointments - minFollowUpAppointments) <= 0 ? 0 : (followUpAppointments - minFollowUpAppointments) / (maxFollowUpAppointments - minFollowUpAppointments);
                const calculatedTotalSessions: number = (maxTotalSessions - minTotalSessions) <= 0 ? 0 : (totalSessions - minTotalSessions) / (maxTotalSessions - minTotalSessions);
                const calculatedMissedAppointments: number = (maxMissedAppointments - minMissedAppointments) <= 0 ? 0 : (missedAppointments - minMissedAppointments) / (maxMissedAppointments - minMissedAppointments);
                const calculatedLoyaltyYears: number = (maxLoyaltyYears - minLoyaltyYears) <= 0 ? 0 : (loyaltyYears - minLoyaltyYears) / (maxLoyaltyYears - minLoyaltyYears);
                const calculatedScheduledAppointments: number = (maxScheduledAppointments - minScheduledAppointments) <= 0 ? 0 : (scheduledAppointments - minScheduledAppointments) / (maxScheduledAppointments - minScheduledAppointments);
                const calculatedNoOfMatches: number = (maxNoOfMatches - minNoOfMatches) <= 0 ? 0 : (noOfMatches - minNoOfMatches) / (maxNoOfMatches - minNoOfMatches);

                const score: number = (
                    (therapistScoreConstantValues.responseTimeWeight * (1 - calculatedResponseTime)) +
                    therapistScoreConstantValues.availabilityWeight * calculatedAvailability +
                    therapistScoreConstantValues.followUpAppointmentsWeight * calculatedFollowUpAppointments +
                    therapistScoreConstantValues.totalSessionsWeight * calculatedTotalSessions -
                    therapistScoreConstantValues.missedAppointmentsWeight * calculatedMissedAppointments +
                    therapistScoreConstantValues.loyalityYearsWeight * calculatedLoyaltyYears +
                    therapistScoreConstantValues.scheduledAppointmentsWeight * calculatedScheduledAppointments +
                    therapistScoreConstantValues.noOfMatchesWeight * calculatedNoOfMatches);

                // if(therapi.therapistId == "62ed5a2da406a17f80591278"){
                //   console.log(calculatedScheduledAppointments);
                //   console.log(scheduledAppointments);

                //   console.log("0", therapistScoreConstantValues.responseTimeMultiplier * (1 - calculatedResponseTime));
                //   console.log("1", therapistScoreConstantValues.availabilityMultiplier * calculatedAvailability);
                //   console.log("2", therapistScoreConstantValues.followUpAppointmentsMultiplier * calculatedFollowUpAppointments);
                //   console.log("3", therapistScoreConstantValues.totalSessionsMultiplier * calculatedTotalSessions);
                //   console.log("4", therapistScoreConstantValues.missedAppointmentsMultiplier * calculatedMissedAppointments);
                //   console.log("5", therapistScoreConstantValues.loyalityYearsWeight * calculatedLoyaltyYears);
                //   console.log("6", therapistScoreConstantValues.scheduledAppointmentsMultiplier * calculatedScheduledAppointments);
                //   console.log("7", therapistScoreConstantValues.noOfMatchesMultiplier * calculatedNoOfMatches);

                // }

                const scorePercentage: number = (score / maxScore) * 100;
                const dataForTherapistScoreTable = {
                    therapistId,
                    responseTime: calculatedResponseTime.toFixed(3),
                    availability: calculatedAvailability.toFixed(3),
                    followUpAppointments: calculatedFollowUpAppointments.toFixed(3),
                    totalSessions: calculatedTotalSessions.toFixed(3),
                    missedAppointments: calculatedMissedAppointments.toFixed(3),
                    loyaltyYears: calculatedLoyaltyYears.toFixed(3),
                    scheduledAppointments: calculatedScheduledAppointments.toFixed(3),
                    noOfMatches: calculatedNoOfMatches.toFixed(3),
                    score: scorePercentage.toFixed(3)
                };

                const createTherapistScore = await TherapistDao.createTherapistScore(dataForTherapistScoreTable);
                const returnDaata = {
                    updateOne: {
                        filter: {_id: createTherapistScore.therapistId},
                        update: {$set: {score: createTherapistScore.score.toFixed(3)}}
                    }
                };
                return returnDaata;
            });
            let results2: any = await Promise.all(resultsAfterMinMaxScaler);
            results2.sort((a: any, b: any) => b.updateOne.update.$set.score - a.updateOne.update.$set.score);
            results2.forEach((update: any, index: any) => {
                update.updateOne.update.$set.priorityNumber = index + 1;
            });
            const ss = await TherapistDao.updateTherapistPriorityNumberUsingBulkWrite(results2);
            AppLogger.info(`End calculating therapist score cron job`);
            return;
        } catch (error) {
            AppLogger.error(`Calculating therapist score error occured`);
            return;
        }
    }

    export async function calculateResponseTime2(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const tesr = await TherapistDao.getScoreOfTheTherapist(Types.ObjectId("62ed5a2da406a17f80591278"));

            return res.sendSuccess(tesr);
        } catch (error) {
            return res.sendError("error:", error);
        }
    }

    export async function createTherapistScoreConstants(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            // const data: DTherapistScoreConstants = {
            //   responseTimeWeight: 0.15,
            //   availabilityWeight: 0.30,
            //   followUpAppointmentsWeight: 0.20,
            //   totalSessionsWeight: 0.15,
            //   missedAppointmentsWeight: 0.10,
            //   loyaltyYearsWeight: 0.1,
            //   scheduledAppointmentsWeight: 0.20,
            //   noOfMatchesWeight: 0.1
            // }

            const data: DTherapistScoreConstants = req.body;
            const therapistScoreConstants = await TherapistDao.createTherapistScoreConstants(data);

            return res.sendSuccess(therapistScoreConstants);
        } catch (error) {
            return res.sendError("error:", error);
        }
    }

    export async function getTherapistScoreConstants(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const therapistScoreConstants = await TherapistDao.getTherapistScoreConstants();
            return res.sendSuccess(therapistScoreConstants);
        } catch (error) {
            return res.sendError("error:", error);
        }
    }

    export async function forTesting(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const therapistScoreConstants = await TherapistDao.forTesting(Types.ObjectId("65bec7f93fc37fcf6959ba10"));
            if (therapistScoreConstants[0].totalResponseTimeInHours == 0) {
                console.log("####################################");

            } else {
                console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");
            }
            return res.sendSuccess(therapistScoreConstants);
        } catch (error) {
            return res.sendError("error:", error);
        }
    }

    export async function updateAINoteType(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {

            const noteType = req?.body?.noteType;
            const therapistId = req?.user?._id;

            if (!noteType || (noteType !== "SOAP" && noteType !== "PIE") || !therapistId) {
                return res.sendError("Invalid note typpe");
            }

            const updatedTherapist = await Therapist.findOneAndUpdate(
                {_id: therapistId},
                {$set: {aiNotesType: noteType}},
                {new: true}
            );

            if (!updatedTherapist) {
                return res.sendError("Invalid therapist");
            }

            return res.sendSuccess({}, "Updated successfully.");

        } catch (error) {
            return res.sendError("error:", error);
        }
    }

    export async function getPresignUrlForTreatementHistoryAudioByMeetingId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const meetingId = req.body.meetingId;
            const path = req.body.path;

            if (!meetingId) {
                return res.sendError("Meeting Id is not found.");
            }

            if (!path) {
                return res.sendError("Path is not found .");
            }

            const url = await TherapistDao.getPresignUrlRegulerSessionAudioByPathAndId(meetingId, path);

            if (!url) {
                return res.sendError("Presign Url is not generated in given path or meeting.");
            }

            return res.sendSuccess(url);

        } catch (error) {
            return res.sendError("error:", error);
        }
    }

    export async function getTherapistScoreAllDetailsByTherapistId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const userId = req.user._id;

            const therapistScoreDetails = await TherapistDao.getTherapistScoreAllDetailsByTherapistId(userId);

            if (!therapistScoreDetails) {
                return res.sendError("Something went wrong!");
            }
            return res.sendSuccess(therapistScoreDetails);
        } catch (error) {
            return res.sendError("error:", error);
        }
    }


    export async function searchClientInsuranceDocumentsByTherapist(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const limit = Number(req.params.limit);
            const offset = Number(req.params.offset);
            const therapistId = req.user._id;
            let searchableStringClient = req.body.clientSearchableString;
            let searchableInsuranceCompany = req.body.searchableInsuranceCompany;

            let searchedNameClient = null;

            if (searchableStringClient && searchableStringClient.trim() != "") {
                searchableStringClient = searchableStringClient.trim();
                let seacrhItem = searchableStringClient.replace(/\s/g, "");
                searchedNameClient = searchableStringClient != null ? new RegExp(`^${seacrhItem}`, "i") : null;
            }

            const clientNameQuery =
                searchedNameClient
                    ? {
                        $and: [
                            {
                                $or: [
                                    {'client.firstname': searchedNameClient},
                                    {'client.lastname': searchedNameClient},
                                    {'client.email': searchedNameClient},
                                    {'client.fullName': searchedNameClient},
                                ],
                            },
                        ],
                    }
                    : {};

            let insuranceCompanyQuery: any = {};

            if (searchableInsuranceCompany) {
                insuranceCompanyQuery['insuranceCompanyDetails._id'] = new Types.ObjectId(searchableInsuranceCompany);
            }
            const searchResult = await FriendRequest.aggregate([
                {
                    $sort: {
                        createdAt: -1
                    }
                },
                {
                    $match: {
                        $and: [
                            {therapistId: therapistId},
                            {status: FriendRequestStatus.APPROVED}
                        ]
                    }
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "clientId",
                        foreignField: "_id",
                        as: "clientInfo"
                    }
                },
                {
                    $unwind: {
                        path: "$clientInfo"
                    }
                },
                {
                    $match: {
                        $or: [
                            {"clientInfo.blockedByAdmin": false},
                            {"clientInfo.blockedByAdmin": {$exists: false}}
                        ]
                    }
                },
                {
                    $group: {
                        _id: {clientId: "$clientId", therapistId: "$therapistId"}
                    }
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "_id.clientId",
                        foreignField: "_id",
                        as: "userDetails"
                    }
                },
                {
                    $unwind: "$userDetails"
                },
                {
                    $lookup: {
                        from: "insurances",
                        let: {userInsuranceId: "$userDetails.insuranceId"},
                        pipeline: [
                            {$match: {$expr: {$eq: ["$_id", "$$userInsuranceId"]}}},
                            {$project: {insuranceCompanyId: 1}}
                        ],
                        as: "primaryInsurance"
                    }
                },
                {
                    $unwind: {
                        path: "$primaryInsurance",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $addFields: {
                        primaryInsuranceCompanyId: "$primaryInsurance.insuranceCompanyId"
                    }
                },
                {
                    $lookup: {
                        from: "insurances",
                        localField: "_id.clientId",
                        foreignField: "clientId",
                        as: "insuranceDetails"
                    }
                },
                {
                    $unwind: {
                        path: "$insuranceDetails",
                        preserveNullAndEmptyArrays: false
                    }
                },
                {
                    $lookup: {
                        from: "insurancecompanies",
                        localField: "insuranceDetails.insuranceCompanyId",
                        foreignField: "_id",
                        as: "insuranceCompanyDetails"
                    }
                },
                {
                    $unwind: {
                        path: "$insuranceCompanyDetails",
                        preserveNullAndEmptyArrays: false
                    }
                },
                // {
                //   $match: {
                //     $and: [
                //       { "insuranceCompanyDetails.fax": { $exists: true, $ne: null }},
                //       { "insuranceCompanyDetails.authorizationFormAvailability": { $exists: true, $eq: true }}
                //     ],
                //   }
                // },
                {
                    $lookup: {
                        from: "users",
                        localField: "_id.clientId",
                        foreignField: "_id",
                        pipeline: [
                            {
                                $project: {_id: 1, firstname: 1, lastname: 1, email: 1, createdAt: 1}
                            }
                        ],
                        as: "clientDetails"
                    }
                },
                {
                    $unwind: {
                        path: "$clientDetails",
                        preserveNullAndEmptyArrays: false
                    }
                },
                {
                    $match: insuranceCompanyQuery
                },
                {
                    $lookup: {
                        from: "clinicalassesments",
                        let: {
                            clientId: "$_id.clientId",
                            therapistId: "$_id.therapistId"
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            {$eq: ["$clientId", "$$clientId"]},
                                            {$eq: ["$therapistId", "$$therapistId"]}
                                        ]
                                    }
                                }
                            }
                        ],
                        as: "clinicalAssessments"
                    }
                },
                {
                    $unwind: {
                        path: "$clinicalAssessments",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $lookup: {
                        from: "digitalassessments",
                        let: {
                            clientId: "$_id.clientId",
                            therapistId: "$_id.therapistId"
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            {$eq: ["$clientId", "$$clientId"]},
                                            {$eq: ["$therapistId", "$$therapistId"]}
                                        ]
                                    }
                                }
                            }
                        ],
                        as: "digitalAssessments"
                    }
                },
                {
                    $unwind: {
                        path: "$digitalAssessments",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $lookup: {
                        from: "therapyplans",
                        let: {
                            clientId: "$_id.clientId",
                            therapistId: "$_id.therapistId"
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            {$eq: ["$clientId", "$$clientId"]},
                                            {$eq: ["$therapistId", "$$therapistId"]}
                                        ]
                                    }
                                }
                            }
                        ],
                        as: "therapyPlans"
                    }
                },
                {
                    $unwind: {
                        path: "$therapyPlans",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $lookup: {
                        from: "authorizationforms",
                        let: {
                            clientId: "$_id.clientId",
                            therapistId: "$_id.therapistId",
                            insuranceCompanyId: "$insuranceCompanyDetails._id"
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            {$eq: ["$clientId", "$$clientId"]},
                                            {$eq: ["$therapistId", "$$therapistId"]},
                                            {$eq: ["$insuranceCompanyId", "$$insuranceCompanyId"]}
                                        ]
                                    }
                                }
                            }
                        ],
                        as: "authorizationForms"
                    }
                },
                {
                    $unwind: {
                        path: "$authorizationForms",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $lookup: {
                        from: "meetings",
                        let: {clientId: "$_id.clientId", therapistId: "$_id.therapistId"},
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            {$eq: ["$clientId", "$$clientId"]},
                                            {$eq: ["$therapistId", "$$therapistId"]},
                                            {$eq: ["$callingStatus", "COMPLETED"]}
                                        ]
                                    }
                                }
                            }
                        ],
                        as: "meetingDetails"
                    }
                },
                {
                    $addFields: {
                        meetingCount: {$size: "$meetingDetails"}
                    }
                },
                {
                    $lookup: {
                        from: "insurancedocapprovals",
                        let: {
                            clientId: "$_id.clientId",
                            therapistId: "$_id.therapistId",
                            insuranceCompanyId: "$insuranceCompanyDetails._id"
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            {$eq: ["$clientId", "$$clientId"]},
                                            {$eq: ["$therapistId", "$$therapistId"]},
                                            {$eq: ["$insuranceCompanyId", "$$insuranceCompanyId"]}
                                        ]
                                    }
                                }
                            }
                        ],
                        as: "insuranceDocApprovals"
                    }
                },
                {
                    $unwind: {
                        path: "$insuranceDocApprovals",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $group: {
                        _id: {clientId: "$_id.clientId", therapistId: "$_id.therapistId"},
                        clientDetails: {$first: "$clientDetails"},
                        primaryInsuranceCompanyId: {$first: "$primaryInsuranceCompanyId"},
                        insuranceCompanies: {
                            $addToSet: {
                                _id: "$insuranceCompanyDetails._id",
                                isPrimary: {$eq: ["$insuranceCompanyDetails._id", "$primaryInsuranceCompanyId"]},
                                name: "$insuranceCompanyDetails.insuranceCompany",
                                fax: "$insuranceCompanyDetails.fax",
                                authorizationFormAvailability: "$insuranceCompanyDetails.authorizationFormAvailability",
                                authorizationForms: {$ifNull: ["$authorizationForms._id", null]},
                                authFormType: {$ifNull: ["$authorizationForms.authFormType", null]},
                                insuranceDocApprovalId: {$ifNull: ["$insuranceDocApprovals._id", null]},
                                therapistApprovalStatus: {$ifNull: ["$insuranceDocApprovals.therapistApprovalStatus", null]},
                                adminApprovalStatus: {$ifNull: ["$insuranceDocApprovals.adminApprovalStatus", null]},
                                messageId: {$ifNull: ["$insuranceDocApprovals.messageId", null]},
                                messageStatus: {$ifNull: ["$insuranceDocApprovals.messageStatus", null]},
                                link: {$ifNull: ["$insuranceCompanyDetails.link", null]}
                            }
                        },
                        clinicalAssessment: {$first: "$clinicalAssessments._id"},
                        digitalAssessment: {$first: "$digitalAssessments._id"},
                        therapyPlan: {$first: "$therapyPlans._id"},
                        meetingCount: {$first: "$meetingCount"}
                    }
                },
                {
                    $project: {
                        _id: 0,
                        clientCreatedAtDate: '$clientDetails.createdAt',
                        client: {
                            _id: "$clientDetails._id",
                            firstname: "$clientDetails.firstname",
                            lastname: "$clientDetails.lastname",
                            fullName: {$concat: ["$clientDetails.firstname", " ", "$clientDetails.lastname"]},
                            email: "$clientDetails.email"
                        },
                        insuranceCompanies: 1,
                        clinicalAssessment: 1,
                        digitalAssessment: 1,
                        therapyPlan: 1,
                        meetingCount: 1
                    }
                },
                {
                    $sort: {
                        clientCreatedAtDate: -1
                    }
                },
                {
                    $match: clientNameQuery
                },
                {
                    $skip: offset
                },
                {
                    $limit: limit
                }
            ]);

            if (!searchResult) {
                return res.sendError("Something went wrong !");
            }

            return res.sendSuccess(searchResult, "Filtered insurance documents.");

        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function submitDocsForAdminApproval(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const therapistId = String(req.user._id);
        const clientId = req.body.clientId;
        const insuranceCompanyId = req.body.insuranceCompanyId;
        const therapistApprovalStatus = req.body.therapistApprovalStatus;
        if (req.user.role == UserRole.THERAPIST) {
            try {
                let client = await ClientDao.getUserById(clientId);
                let therapist = await TherapistDao.getUserById(therapistId);

                if (!client) {
                    return res.sendError("No client found for the provided Id!");
                }

                try {
                    const submissionDetails: DInsuranceDocApproval = {
                        clientId: clientId,
                        therapistId: therapistId,
                        insuranceCompanyId: insuranceCompanyId,
                        therapistApprovalStatus: therapistApprovalStatus
                    };

                    let submittedDetails = await TherapistDao.submitDocsForAdminApprovalByTherapist(submissionDetails);

                    if (!submittedDetails) {
                        return res.sendError("The submission for admin approval was not successful!");
                    }

                    await EmailService.sendEmailToAdminWhenTherapistApprovedInsuranceDocuments(therapist, `Action Required: Review ${therapist?.firstname}'s All Documents Submission`);

                    return res.sendSuccess(submittedDetails, "Successfully submitted for admin approval!");
                } catch (error) {
                    AppLogger.error(`submit-Docs-For-Admin-Approval 1 - Error Details: ${error}.`);
                    return res.sendError(error);
                }
            } catch (error) {
                AppLogger.error(`submit-Docs-For-Admin-Approval 2 - Error Details: ${error}.`);
                return res.sendError(error);
            }
        } else {
            return res.sendError("You don't have permission!");
        }
    }
}
