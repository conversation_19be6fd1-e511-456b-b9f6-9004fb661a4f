import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import { UserRole } from "../models/user-model";
import JotFormInvitation from "../schemas/jotform-invitation-schema";
import User from "../schemas/user-schema";
import { EmailService } from "../mail/config";
import { AppLogger } from "../common/logging";
import * as dotenv from 'dotenv';

// Đảm bảo dotenv được cấu hình
dotenv.config();

// Constants for invitation types
const INVITATION_TYPE = {
    THERAPIST: "THERAPIST",
    ADMIN: "ADMIN"
};

// Form IDs constants - sử dụng process.env với fallback values
const FORM_IDS = {
    PHQ9_AND_GAD7: process.env.ACCEPTED_FORM_IDS_PHQ9_AND_GAD7 || "243196224373053",
    DAST: process.env.ACCEPTED_FORM_IDS_DAST || "243185022607047",
    AUDIT: process.env.ACCEPTED_FORM_IDS_AUDIT || "243184181929059"
};

// Form templates for emails
const EMAIL_TEMPLATES = {
    PHQ9_AND_GAD7: {
        subject: "Lavni Clinical Director: Complete Form For A Quick Mental Health Check-In",
        getBody: (clientName: string) => `
            <h2>PHQ-9 & GAD-7 Combined Form</h2>
            <p>Hi ${clientName},</p>
            <p>Our Clinical Director at Lavni has requested that you complete a quick self-assessment form that combines the PHQ-9 and GAD-7.</p>
            <p>This form helps us better understand how you're feeling by screening for symptoms of depression and anxiety. It only takes a few minutes and allows our team to support you in the most meaningful way possible.</p>
            <p>👉 <a href="{{FORM_LINK}}">Click here to complete your PHQ-9 & GAD-7 form</a></p>
            <p>If you have any questions or need help, feel free to reply to this email or call us at (*************.</p>
            <p>Warmly,</p>
            <p>Lavni Inc.</p>
        `
    },
    DAST: {
        subject: "Please Complete This Quick Substance Use Screening",
        getBody: (clientName: string) => `
            <p>Hi ${clientName},</p>
            <p>Our Clinical Director at Lavni has asked you to complete a short screening form called the DAST-10 (Drug Abuse Screening Test).</p>
            <p>This form helps us understand if there are any substance use concerns so we can ensure you're receiving the most effective and personalized care. It's fully confidential and takes only a few minutes to complete.</p>
            <p>👉 <a href="{{FORM_LINK}}">Click here to complete your DAST-10 form</a></p>
            <p>If you have any questions, we're here to help. Just reply to this email or give us a call at (*************.</p>
            <p>All the best,</p>
            <p>Lavni Inc.</p>
        `
    },
    AUDIT: {
        subject: "Your Wellness Matters – Quick Alcohol Use Survey",
        getBody: (clientName: string) => `
            <p>Hi ${clientName},</p>
            <p>To continue providing the best care possible, our Clinical Director at Lavni is asking you to complete the AUDIT (Alcohol Use Disorders Identification Test) form.</p>
            <p>This short form helps us better understand any patterns of alcohol use, so your treatment can be as supportive and tailored as possible. It's private, simple, and only takes a few minutes.</p>
            <p>👉 <a href="{{FORM_LINK}}">Click here to complete your AUDIT form</a></p>
            <p>Thank you for taking this step. If you have any questions, feel free to reach out at (*************.</p>
            <p>Warm regards,</p>
            <p>Lavni Inc.</p>
        `
    },
    DEFAULT: {
        subject: "Form from Lavni Admin",
        getBody: (clientName: string, adminName: string) => `
            <p>Hello ${clientName},</p>
            <p>Lavni Admin ${adminName} has sent you a form to complete.</p>
            <p>Please click the link below to access the form:</p>
            <p><a href="{{FORM_LINK}}">{{FORM_LINK}}</a></p>
            <p>Thank you,</p>
            <p>Lavni Administration Team</p>
        `
    },
    // Templates cho therapist
    THERAPIST_PHQ9_AND_GAD7: {
        subject: "Quick Mental Health Check-In Requested by [Therapist Name]",
        getBody: (clientName: string, therapistName: string) => `
            <h2>PHQ-9 & GAD-7 Combined Form</h2>
            <p>Hi ${clientName},</p>
            <p>Your therapist, ${therapistName}, has asked you to complete a quick self-assessment form that combines the PHQ-9 and GAD-7. This form helps track your emotional wellbeing by screening for symptoms of depression and anxiety.</p>
            <p>It only takes a few minutes to complete and gives your therapist helpful insight to better support you during your sessions.</p>
            <p>👉 <a href="{{FORM_LINK}}">Click here to complete your PHQ-9 & GAD-7 form</a></p>
            <p>If you have any questions or need support, just reply to this message, or call us at (*************. We're here for you.</p>
            <p>Warmly,</p>
            <p>Lavni Inc.</p>
        `
    },
    THERAPIST_DAST: {
        subject: "[Therapist Name] Requested a Quick Substance Use Screening",
        getBody: (clientName: string, therapistName: string) => `
            <h2>DAST-10 Form</h2>
            <p>Hi ${clientName},</p>
            <p>Your therapist (${therapistName}) has asked you to complete a short form called the DAST-10 (Drug Abuse Screening Test). This quick assessment helps identify and understand any challenges related to substance use, so your care can be as comprehensive as possible.</p>
            <p>It's completely confidential and takes just a few minutes.</p>
            <p>👉 <a href="{{FORM_LINK}}">Click here to complete your DAST-10 form</a></p>
            <p>Let us know if you have any questions or need assistance — we're always here to help. Feel free to call us at (*************.</p>
            <p>All the best,</p>
            <p>Lavni Inc.</p>
        `
    },
    THERAPIST_AUDIT: {
        subject: "Help [Therapist Name] Personalize Your Care – Quick Alcohol Use Survey",
        getBody: (clientName: string, therapistName: string) => `
            <h2>AUDIT Form</h2>
            <p>Hi ${clientName},</p>
            <p>Your therapist (${therapistName}) has asked you to fill out the AUDIT (Alcohol Use Disorders Identification Test). This short survey helps understand patterns of alcohol use and ensures your therapy is fully aligned with your wellness needs.</p>
            <p>It's simple, private, and only takes a few minutes.</p>
            <p>👉 <a href="{{FORM_LINK}}">Click here to complete your AUDIT form</a></p>
            <p>Thanks for taking this step — and as always, we're here if you need anything. Feel free to call us at (*************.</p>
            <p>Warm regards,</p>
            <p>Lavni Inc.</p>
        `
    },
    THERAPIST_DEFAULT: {
        subject: "Form from your therapist",
        getBody: (clientName: string, therapistName: string) => `
            <p>Hello ${clientName},</p>
            <p>Your therapist ${therapistName} has sent you a form to complete.</p>
            <p>Please click the link below to access the form:</p>
            <p><a href="{{FORM_LINK}}">{{FORM_LINK}}</a></p>
            <p>Thank you,</p>
            <p>Lavni Team</p>
        `
    }
};

// Helper function to extract form ID from form link URL
function extractFormIdFromURL(url: string): string | null {
    try {
        const urlObj = new URL(url);
        const pathParts = urlObj.pathname.split('/');
        // Check if fill-form path exists
        if (urlObj.pathname.includes('/fill-form/')) {
            const formIdIndex = pathParts.indexOf('fill-form') + 1;
            if (formIdIndex < pathParts.length) {
                return pathParts[formIdIndex];
            }
        }
        // Try to find a number-only segment that matches our form IDs
        for (const part of pathParts) {
            if (/^\d+$/.test(part) && 
                (part === FORM_IDS.PHQ9_AND_GAD7 || 
                 part === FORM_IDS.DAST || 
                 part === FORM_IDS.AUDIT)) {
                return part;
            }
        }
        // Try to extract form ID from hash fragment
        if (urlObj.hash) {
            const matches = urlObj.hash.match(/[\d]+/);
            if (matches && matches.length > 0) {
                return matches[0];
            }
        }
        return null;
    } catch (error) {
        AppLogger.error("Error extracting form ID from URL", error);
        return null;
    }
}

// Helper function to identify form type based on form ID
function identifyFormType(formId: string | null): string {
    if (!formId) return 'DEFAULT';
    
    if (formId === FORM_IDS.PHQ9_AND_GAD7) return 'PHQ9_AND_GAD7';
    if (formId === FORM_IDS.DAST) return 'DAST';
    if (formId === FORM_IDS.AUDIT) return 'AUDIT';
    
    return 'DEFAULT';
}

export class JotFormInvitationEp {
    /**
     * Send form invitation to client
     * This endpoint allows therapists to send JotForm links to clients via email
     * @param req Request object containing clientId and formLink in body
     * @param res Response object
     * @param next NextFunction
     */
    static async sendFormInvitation(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            // Check if user is a therapist
            if (req.user.role !== UserRole.THERAPIST) {
                return res.status(403).json({
                    success: false,
                    message: "Access denied. Therapist privileges required."
                });
            }

            const { clientId, formLink } = req.body;

            // Validate request body
            if (!clientId || !formLink) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required fields: clientId and formLink are required"
                });
            }

            // Validate ObjectId
            if (!Types.ObjectId.isValid(clientId)) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid clientId format"
                });
            }

            // Validate formLink URL format
            try {
                new URL(formLink);
            } catch (error) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid formLink URL format"
                });
            }

            // Get client information
            const client = await User.findById(clientId);
            if (!client) {
                return res.status(404).json({
                    success: false,
                    message: "Client not found"
                });
            }

            // Get therapist information
            const therapist = await User.findById(req.user._id);
            if (!therapist) {
                return res.status(404).json({
                    success: false,
                    message: "Therapist information not found"
                });
            }

            // Create new JotFormInvitation record
            const jotFormInvitation = new JotFormInvitation({
                clientId: new Types.ObjectId(clientId),
                therapistId: req.user._id,
                formLink: formLink,
                emailSent: false,
                sentAt: new Date(),
                status: "ACTIVE",
                invitationType: INVITATION_TYPE.THERAPIST
            });

            // Save record to database
            await jotFormInvitation.save();

            // Extract form ID from the form link URL
            const formId = extractFormIdFromURL(formLink);
            
            // Identify form type based on form ID
            const formType = identifyFormType(formId);
            
            AppLogger.info(`Form ID: ${formId}, Form Type: ${formType}`);
            
            // Determine email template based on form type
            let emailSubject, emailBody;
            const therapistName = `${therapist.firstname} ${therapist.lastname}`;
            const clientName = client.firstname;
            
            switch (formType) {
                case 'PHQ9_AND_GAD7':
                    emailSubject = EMAIL_TEMPLATES.THERAPIST_PHQ9_AND_GAD7.subject.replace('[Therapist Name]', therapistName);
                    emailBody = EMAIL_TEMPLATES.THERAPIST_PHQ9_AND_GAD7.getBody(clientName, therapistName);
                    break;
                case 'DAST':
                    emailSubject = EMAIL_TEMPLATES.THERAPIST_DAST.subject.replace('[Therapist Name]', therapistName);
                    emailBody = EMAIL_TEMPLATES.THERAPIST_DAST.getBody(clientName, therapistName);
                    break;
                case 'AUDIT':
                    emailSubject = EMAIL_TEMPLATES.THERAPIST_AUDIT.subject.replace('[Therapist Name]', therapistName);
                    emailBody = EMAIL_TEMPLATES.THERAPIST_AUDIT.getBody(clientName, therapistName);
                    break;
                default:
                    emailSubject = EMAIL_TEMPLATES.THERAPIST_DEFAULT.subject;
                    emailBody = EMAIL_TEMPLATES.THERAPIST_DEFAULT.getBody(clientName, therapistName);
            }

            // Replace form link placeholder in template
            emailBody = emailBody.replace(/{{FORM_LINK}}/g, formLink);

            // Try to send email
            try {
                await EmailService.sendEmailToSelectedUsers(
                    client.email,
                    emailBody,
                    emailSubject
                );

                // Update JotFormInvitation record to reflect email was sent
                jotFormInvitation.emailSent = true;
                await jotFormInvitation.save();
            } catch (emailError) {
                AppLogger.error("Error sending form invitation email", emailError);
                // We'll continue even if email fails, as the record is saved
            }

            return res.status(200).json({
                success: true,
                message: "Form invitation sent successfully",
                data: jotFormInvitation
            });
        } catch (error) {
            AppLogger.error("Error in sendFormInvitation", error);
            return res.status(500).json({
                success: false,
                message: "Error sending form invitation",
                error: error.message
            });
        }
    }

    /**
     * Get form invitations sent to a client
     * @param req Request object containing clientId in params
     * @param res Response object
     * @param next NextFunction
     */
    static async getFormInvitationsByClientId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { clientId } = req.params;

            // Validate ObjectId
            if (!Types.ObjectId.isValid(clientId)) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid clientId format"
                });
            }

            // Find invitations for this client
            const invitations = await JotFormInvitation.find({
                clientId: new Types.ObjectId(clientId),
                status: "ACTIVE"
            }).sort({ sentAt: -1 }); // Sort by sent date, newest first

            return res.status(200).json({
                success: true,
                message: "Form invitations retrieved successfully",
                data: invitations
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: "Error retrieving form invitations",
                error: error.message
            });
        }
    }

    /**
     * Get form invitations sent by a therapist
     * @param req Request object containing therapistId in params
     * @param res Response object
     * @param next NextFunction
     */
    static async getFormInvitationsByTherapistId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { therapistId } = req.params;

            // Validate ObjectId
            if (!Types.ObjectId.isValid(therapistId)) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid therapistId format"
                });
            }

            // Find invitations sent by this therapist
            const invitations = await JotFormInvitation.find({
                therapistId: new Types.ObjectId(therapistId),
                status: "ACTIVE"
            }).sort({ sentAt: -1 }); // Sort by sent date, newest first

            return res.status(200).json({
                success: true,
                message: "Form invitations retrieved successfully",
                data: invitations
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: "Error retrieving form invitations",
                error: error.message
            });
        }
    }

    /**
     * Send form invitation to client from admin
     * This endpoint allows admins to send JotForm links to clients via email
     * @param req Request object containing clientId and formLink in body
     * @param res Response object
     * @param next NextFunction
     */
    static async sendFormInvitationFromAdmin(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            // Check if user is an admin
            if (
                req.user.role !== UserRole.SUPER_ADMIN &&
                req.user.role !== UserRole.SUB_ADMIN &&
                req.user.role !== UserRole.ADMIN
            ) {
                return res.status(403).json({
                    success: false,
                    message: "Access denied. Admin privileges required."
                });
            }

            const { clientId, formLink } = req.body;

            // Validate request body
            if (!clientId || !formLink) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required fields: clientId and formLink are required"
                });
            }

            // Validate ObjectId
            if (!Types.ObjectId.isValid(clientId)) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid clientId format"
                });
            }

            // Validate formLink URL format
            try {
                new URL(formLink);
            } catch (error) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid formLink URL format"
                });
            }

            // Get client information
            const client = await User.findById(clientId);
            if (!client) {
                return res.status(404).json({
                    success: false,
                    message: "Client not found"
                });
            }

            // Get admin information
            const admin = await User.findById(req.user._id);
            if (!admin) {
                return res.status(404).json({
                    success: false,
                    message: "Admin information not found"
                });
            }

            // Create new JotFormInvitation record
            const jotFormInvitation = new JotFormInvitation({
                clientId: new Types.ObjectId(clientId),
                therapistId: req.user._id, // Store admin ID in therapistId field
                formLink: formLink,
                emailSent: false,
                sentAt: new Date(),
                status: "ACTIVE",
                invitationType: INVITATION_TYPE.ADMIN // Mark as admin invitation
            });

            // Save record to database
            await jotFormInvitation.save();

            // Extract form ID from the form link URL
            const formId = extractFormIdFromURL(formLink);
            
            // Identify form type based on form ID
            const formType = identifyFormType(formId);
            
            AppLogger.info(`Form ID: ${formId}, Form Type: ${formType}`);
            
            // Determine email template based on form type
            let emailSubject, emailBody;
            const adminName = `${admin.firstname} ${admin.lastname}`;
            const clientName = client.firstname;
            
            switch (formType) {
                case 'PHQ9_AND_GAD7':
                    emailSubject = EMAIL_TEMPLATES.PHQ9_AND_GAD7.subject;
                    emailBody = EMAIL_TEMPLATES.PHQ9_AND_GAD7.getBody(clientName);
                    break;
                case 'DAST':
                    emailSubject = EMAIL_TEMPLATES.DAST.subject;
                    emailBody = EMAIL_TEMPLATES.DAST.getBody(clientName);
                    break;
                case 'AUDIT':
                    emailSubject = EMAIL_TEMPLATES.AUDIT.subject;
                    emailBody = EMAIL_TEMPLATES.AUDIT.getBody(clientName);
                    break;
                default:
                    emailSubject = EMAIL_TEMPLATES.DEFAULT.subject;
                    emailBody = EMAIL_TEMPLATES.DEFAULT.getBody(clientName, adminName);
            }

            // Replace form link placeholder in template
            emailBody = emailBody.replace(/{{FORM_LINK}}/g, formLink);

            // Try to send email
            try {
                await EmailService.sendEmailToSelectedUsers(
                    client.email,
                    emailBody,
                    emailSubject
                );

                // Update JotFormInvitation record to reflect email was sent
                jotFormInvitation.emailSent = true;
                await jotFormInvitation.save();
            } catch (emailError) {
                AppLogger.error("Error sending form invitation email from admin", emailError);
                // We'll continue even if email fails, as the record is saved
            }

            return res.status(200).json({
                success: true,
                message: "Form invitation sent successfully by admin",
                data: jotFormInvitation
            });
        } catch (error) {
            AppLogger.error("Error in sendFormInvitationFromAdmin", error);
            return res.status(500).json({
                success: false,
                message: "Error sending form invitation",
                error: error.message
            });
        }
    }
}
