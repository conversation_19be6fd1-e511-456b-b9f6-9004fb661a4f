import { NextFunction, Request, Response } from "express";
import Meeting from "../schemas/meeting-schema";
const _ = require('lodash');
import { CallingStatus, DMeeting } from "../models/meeting-model";
import { check, Validation<PERSON>hain, validationR<PERSON>ult } from "express-validator";
import moment = require("moment");
import { VideoCallDao } from "../dao/videocall-dao";
import User from "../schemas/user-schema";
import { EmailService } from "../mail/config";
import { Util } from "../common/util";
import { AppLogger } from "../common/logging";
import { ZoomVideoCallEP } from "./zoom-video-call-ep";
import ChatGroupCall from "../schemas/chat-group-call-schema";
import { ChatGroupCallCallingStatus } from "../models/chat-group-call-model";
import { VonageCallGroupDao } from "../dao/vonage-call-group-dao";
import ChatGroupMember from "../schemas/chat-group-member-schema";
import { Types } from "mongoose";
import { SMSService } from "../sms/config";
import { DDiagnosisNote } from "../models/diagnosis-note-model";
import { DTreatmentHistory } from "../models/treatment-history-model";
import { TherapistDao } from "../dao/therapist-dao";
import { ChatGroupMemberType } from "../models/chat-group-member-model";

const { createClient } = require('@deepgram/sdk');
const apiKey = process.env.TOKBOX_API_KEY;
const secret = process.env.TOKBOX_SECRET;
const opentokUrl = 'https://api.opentok.com/v2/project';
const OpenTok = require('opentok');
const opentok = new OpenTok(apiKey, secret);

export namespace VonageCallGroupEp {

  export async function cancelVonageCallGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const preMeetingData: any = await Meeting.findOne({
        vonageSessionName: req.body.sessionName,
        callingStatus: { $nin: ['completed', 'cancelled'] }
      })
      if (!preMeetingData) {
        return res.sendError("Meeting already completed or cancelled!")
      }

      // Get first member of the group
      const chatGroupCall = await ChatGroupCall.findById(preMeetingData.chatGroupCallId);
      if (!chatGroupCall) {
        return res.sendError("Chat group call not found!");
      }

      // Get group members
      const groupMembers = await ChatGroupMember.find({ 
        groupId: chatGroupCall.groupId,
        role: ChatGroupMemberType.MEMBER,
        is_main: true 
      }).sort({ createdAt: 1 }).limit(1); // Get first member

      if (!groupMembers || groupMembers.length === 0) {
        return res.sendError("No main members found in group!");
      }

      const firstMember = groupMembers[0];

      if (preMeetingData) {
          if (
            preMeetingData.callingStatus === CallingStatus.ONGOING ||
            preMeetingData.callingStatus === CallingStatus.STARTED
          ) {

            await Meeting.findByIdAndUpdate(preMeetingData._id, {
              callingStatus:
                preMeetingData.callingStatus === CallingStatus.ONGOING
                  ? CallingStatus.COMPLETED
                  : CallingStatus.CANCELLED,
            });
          }
          const result = await ZoomVideoCallEP.createDiagnosisNoteForGroup(preMeetingData._id, firstMember.userId);
          const updateChatGroupCall = await ChatGroupCall.findByIdAndUpdate(
          preMeetingData?.chatGroupCallId, 
          { callingStatus: ChatGroupCallCallingStatus.ENDED }
        )
        // const chatGroupCallData = await ChatGroupCall.findById(preMeetingData.chatGroupCallId);
        // if(chatGroupCallData) {
        //   const groupId = chatGroupCallData.groupId;
        //   const groupMembersWithUserDetails = await ChatGroupCallMembers.aggregate([
        //       {
        //         $match: { groupId }  // Match the documents with the specified groupId
        //       },
        //       {
        //         $lookup: {
        //           from: 'users',      // The name of the 'users' collection
        //           localField: 'userId',
        //           foreignField: '_id', // The field in the 'users' collection to match
        //           as: 'userDetails'    // The alias for the joined data
        //         }
        //       },
        //       {
        //         $unwind: '$userDetails' // Unwind the array created by $lookup (assuming one-to-one relationship)
        //       }
        //     ]);

        //   return res.sendSuccess(groupMembersWithUserDetails);
        // }
        const groupMembersWithUserDetails = await VonageCallGroupDao.getGroupMembersSocketIds(preMeetingData.chatGroupCallId)
        return res.sendSuccess(groupMembersWithUserDetails);

      } else {
        return res.sendError("Invalid Meeting Id.");
      }

    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Call Cancelled Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function joinVonageMeetingValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isString()
        .withMessage("Invalid meetingId."),
      // check("recieverId")
      //   .notEmpty()
      //   .withMessage("recieverId is required")
      //   .isMongoId()
      //   .withMessage("Invalid recieverId."),
      check("isAppointmentBased")
        .notEmpty()
        .withMessage("isAppointmentBased is required")
        .isBoolean()
        .withMessage("isAppointmentBased is not a boolean type."),
      // check("appointmentId")
      //   .optional()
      //   .isMongoId()
      //   .withMessage("Invalid appointmentId."),
      // check("callDuration"),
      //   .notEmpty()
      //   .withMessage("callDuration is required")
      //   .isInt({ min: 0, max: 60 })
      //   .withMessage("Your meeting duration is not within 0 and 60 minutes."),
    ];
  }

  export async function joinVonageMeetingGroup(req: Request, res: Response, next: NextFunction) {
    try {
      // const errors = validationResult(req);
      // if (!errors.isEmpty()) {
      //   return res.sendError(errors.array()[0]);
      // }
      let remainingCallDurationJoinedCall;
      let therapistId: any;

      let meetingId;
      console.log("########################################");
      
      // global
      let hasValidMeetingId = false;
      meetingId = req.body.meetingId;
      let preMeetingData = await Meeting.findByIdAndUpdate(meetingId);

      if (preMeetingData) {
        remainingCallDurationJoinedCall = preMeetingData.meetingDuration;
        therapistId = preMeetingData.therapistId;

        if (
          preMeetingData.callingStatus != CallingStatus.ONGOING &&
          preMeetingData.callingStatus != CallingStatus.STARTED
        ) {
          return res.sendError("invalid meeting calling status");
        }
        if (
          !preMeetingData.clientIdentifier ||
          !preMeetingData.therapistIdentifier ||
          !preMeetingData.password
        ) {
          return res.sendError("Meeting id found in appointment but not in DB");
        }
        remainingCallDurationJoinedCall = preMeetingData.meetingDuration;
        if (
          preMeetingData.callingStatus === CallingStatus.ONGOING &&
          preMeetingData.bothJoinedAt &&
          preMeetingData.meetingDuration
        ) {
          let remainingTime = preMeetingData.meetingDuration;
          const totalMeetingDuration = preMeetingData.meetingDuration;
          const bothJoinedTime = preMeetingData.bothJoinedAt;
          const currentTime = new Date();
          const timeDifference = moment.duration(
            moment(currentTime).diff(moment(bothJoinedTime))
          );
          const timeDifferenceAsMinutes = timeDifference.asMinutes();
          const finalTimeDifference = Math.round(timeDifferenceAsMinutes);
          if (finalTimeDifference >= 0) {
            remainingTime = totalMeetingDuration - finalTimeDifference;
            if (remainingTime >= 0) {
              remainingCallDurationJoinedCall = remainingTime;
              const updatedMeeting = await Meeting.findOneAndUpdate(
                { meetingId: meetingId },
                { spentDuration: finalTimeDifference },
                { new: true }
              );
            }
            if (remainingTime <= 0) {
                await Meeting.findByIdAndUpdate(preMeetingData._id, {
                  callingStatus:
                    preMeetingData.callingStatus === CallingStatus.ONGOING
                      ? CallingStatus.COMPLETED
                      : CallingStatus.CANCELLED,
                });
              

              return res.sendError("Meeting time exceeded for this call");
            }
          } else {
            res.sendError("invalid meeting time");
          }

        }
      } else {
        return res.sendError("Invalid Meeting Id");
      }
        if (preMeetingData) {
          hasValidMeetingId = true;
          if (
            preMeetingData.callingStatus === CallingStatus.STARTED ||
            preMeetingData.callingStatus === CallingStatus.ONGOING
          ) {
          } else {
            return res.sendError("Meeting Cancelled", 333);
          }
        } else {
          return res.sendError("Invalid Meeting Id.");
        }

      const previousMeetingId = preMeetingData._id.toString();


      const finalData = {
        meetingData: preMeetingData,
      };

      return res.sendSuccess(finalData, "Call Details");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Join Zoom Meeting Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function initializeVonageVideoCallGroupValidation(): ValidationChain[] {
    return [
      check("callDuration")
        .notEmpty()
        .withMessage("callDuration is required")
        .isInt({ min: 0, max: 60 })
        .withMessage("Your meeting duration is not within 0 and 60 minutes."),
      check("isTranscribeAllowed")
        .notEmpty()
        .withMessage("isTranscribeAllowed is required/..")
        .isBoolean()
        .withMessage("isTranscribeAllowed is not a boolean type."),
      check("isGroupCall")
        .notEmpty()
        .withMessage("isGroupCall is required/..")
        .isBoolean()
        .withMessage("isGroupCall is not a boolean type."),
      check("chatGroupCallId")
        .notEmpty()
        .withMessage("chatGroupCallId is required/..")
        .isMongoId()
        .withMessage("isTranscribeAllowed is not a Mongo Id."),
    ];
  }

  export async function initializeVonageVideoCallGroup(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }
      console.log(req.body);
      

      let therapistId;
      let callDuration = parseInt(req.body.callDuration);
      const transcribeAllowedForLoggedUser = req.body.transcribeAllowedForLoggedUser;
      const isAudioCall = req.body.isAudioCall;
      const isGroupCall = req.body.isGroupCall;
      const chatGroupCallId = req.body.chatGroupCallId;
    
      therapistId = req.user._id;

      let newClientIdentifier;
      let newTherapistIdentifier;
      let newPassword;

      newClientIdentifier = await Util.getRandomInt(10000, 100000);
      newTherapistIdentifier = await Util.getRandomInt(10000, 100000);

      while (newClientIdentifier == newTherapistIdentifier) {
        newTherapistIdentifier = await Util.getRandomInt(10000, 100000);
      }

      newPassword = await Util.getRandomInt(10000, 100000);

      let meetingDetails: DMeeting;
 
       meetingDetails = {
          therapistId: therapistId,
          transcribeAllowed: req.body.isTranscribeAllowed,
          // transcribeAllowed: false,
          transcribingInProcess: false,
          accepted: false,
          noOfVideose: 0,
          transcribeCreated: false,
          meetingDuration: callDuration,
          isAppointmentBased: false,
          audioFiles: [],
          recordingAllowed: true,
          // recordingAllowed: false,
          callingStatus: CallingStatus.INITIALIZED,
          createdBy: req.user._id,
          recordingSharedWithClient: false,
          clientIdentifier: newClientIdentifier.toString(),
          therapistIdentifier: newTherapistIdentifier.toString(),
          password: newPassword.toString(),
          isAudioCall,
          therapistAllowedTranscribe: transcribeAllowedForLoggedUser,
          isGroupCall: isGroupCall,
          chatGroupCallId: chatGroupCallId
       };

      const createdMeeting = await VideoCallDao.createMeeting(meetingDetails);

      if (!createdMeeting) {
        return res.sendError("Meeting Creation Failed.");
      }

      const dataForSend = {
        alreadyStarted: false,
        password: createdMeeting.password,
        meetingId: createdMeeting._id,
        clientIdentifier: createdMeeting.clientIdentifier,
        therapistIdentifier: createdMeeting.therapistIdentifier,
      };

      return res.sendSuccess(dataForSend, "call initialization details");
    } catch (error) {
      return res.sendError("call initialization failed.");
    }
  }

  export function startCallGroupValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isMongoId()
        .withMessage("Invalid meetingId."),
      check("sessionName")
        .notEmpty()
        .withMessage("sessionName is required")
        .isString()
        .withMessage("Invalid sessionName."),
      check("groupCallId")
        .notEmpty()
        .withMessage("groupCallId is required")
        .isMongoId()
        .withMessage("Invalid groupCallId."),
    ];
  }

  export async function startVonageVideoCallGroup(req: Request, res: Response, next: NextFunction) {
    try {

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      let therapistId;

      therapistId = req.user._id;

      const alreadyCreatedMeetingId = req.body.meetingId;
      const vonageSessionName = req.body.sessionName;
      const groupCallId = req.body.groupCallId;

      const alreadyCreatedMeetingData = await Meeting.findById(
        alreadyCreatedMeetingId
      );

      if (!alreadyCreatedMeetingData) {
        return res.sendError("invalid meeting id");
      }
      if (alreadyCreatedMeetingData.meetingId) {
        return res.sendError("invalid meeting id");
      }

      if (
        alreadyCreatedMeetingData.callingStatus != CallingStatus.INITIALIZED
      ) {
        return res.sendError("invalid meeting status");
      }

      const updatedMeetingData = await Meeting.findByIdAndUpdate(
        alreadyCreatedMeetingId,
        { vonageSessionName: vonageSessionName, callingStatus: CallingStatus.STARTED }
      );
      if (!updatedMeetingData) {
        return res.sendError("meeting details update failed.");
      }

      const updateChatGroupCall = await ChatGroupCall.findByIdAndUpdate(
        groupCallId,
        { callingStatus: ChatGroupCallCallingStatus.ONGOING }
      )
      if (!updateChatGroupCall) {
        return res.sendError("chatGroupCall update failed.");
      }

      const finalData = {
        meetingData: updatedMeetingData
      };

    //   const userForSendEmails = await User.findById(req.body.recieverId);
      const ownDetails = await User.findById(req.user._id);
      const usersInGroup: any[] = await VonageCallGroupDao.getAllUsersInGroupByGroupCallId(Types.ObjectId(groupCallId));
      
      console.log(usersInGroup);
      if(usersInGroup.length > 0){
        const smsPromises = usersInGroup.map(async (userForSendNotification) => {
          if (userForSendNotification.userPhoneNumber) {
            await SMSService.sendEventSMS(
              `New Group Meeting Started by ${ownDetails.firstname} ${ownDetails.lastname}\n\n}`,
              userForSendNotification.userPhoneNumber
            );
          }

          if (userForSendNotification && ownDetails) {
            await EmailService.sendReminderToGroupCall(
              userForSendNotification.userEmail,
              "New Group Meeting Started",
              `New Group Meeting Started by ${ownDetails?.firstname} ${ownDetails?.lastname}`,
              `Please login to ${process.env.APP_URL}/signin to join meeting.`,
            );

          }
        });
        await Promise.all(smsPromises);
      };

      return res.sendSuccess(finalData, "Call Details");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Start Zoom Video Call Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export async function getMeetingDetailsByGroupCallId(req: Request, res: Response, next: NextFunction) {
    try {
      const { groupCallId } = req.params;
      const meetingData = await Meeting.findOne({
        isGroupCall: true, chatGroupCallId: groupCallId
      })
      if(!meetingData){
        return res.sendError('Metting not found!')
      } 
      return res.sendSuccess(meetingData)
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Start Zoom Video Call Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function acceptVonageGroupCallValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isMongoId()
        .withMessage("meetingId is not a Type of string"),
    ];
  }

  export async function acceptVonageGroupCall(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      let meetingId = req.body.meetingId;
      const date = new Date();
      const previousData = await Meeting.findOne({ _id: meetingId });
      if (previousData) {
        if (previousData.bothJoinedAt) {
          const updatedMeeting = await Meeting.findOneAndUpdate(
            { _id: meetingId },
            {
              accepted: true,
              callingStatus: CallingStatus.ONGOING,
            },
            { new: true }
          );
        } else {
          const updatedMeeting = await Meeting.findOneAndUpdate(
            { _id: meetingId },
            {
              accepted: true,
              callingStatus: CallingStatus.ONGOING,
              bothJoinedAt: date,
            },
            { new: true }
          );
        }
      }

      return res.sendSuccess("Meeting and appointment data updated");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Accept Zoom Call Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }
  
  export async function viewAllUpCommingGroupCalls (userRole: any, userId: Types.ObjectId, userLimit: any) {
    try {
      const allUpcomingGroupCalls = await VonageCallGroupDao.getAllUpCommingGroupCalls(userRole, userId, userLimit)
      return allUpcomingGroupCalls;
    } catch (error) {
      return "Error getAllUpCommingGroupCalls"
    }
  }

  export async function checkOngoingGroupCallForClient (req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user._id;
      const allUpcomingGroupCalls = await VonageCallGroupDao.checkOngoingGroupCalls(userId)
      return res.sendSuccess(allUpcomingGroupCalls);
    } catch (error) {
      return res.sendError("Error getAllUpCommingGroupCalls")
    }
  }
}