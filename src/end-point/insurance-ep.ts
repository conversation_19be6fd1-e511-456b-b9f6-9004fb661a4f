require("dotenv").config();
import { NextFunction, Request, Response } from "express";
import { UserDao } from "../dao/user-dao";
import { UserRole } from "../models/user-model";
import multer = require("multer");
import { UploadDao } from "../dao/upload-dao";
import { Types } from "mongoose";
import * as path from "path";
import { AccessTokenResponse, DInsurance, Eligibility, InsuranceState, InsuranceToken, Insurance_company, partner, IInsurance, Tradingpartner_ServiceId } from "../models/insurance-model";
import { InsuranceDao } from "../dao/insurance-dao";
import fetch from "node-fetch";
import { AdminDao } from "../dao/admin-dao";
import moment = require("moment");
import { IClient, PremiumStatus } from "../models/client-model";
import { ClientDao } from "../dao/client-dao";
import { DiagnosisNoteSchema } from "../schemas/diagnosis-note-schema";
import { VideoCallDao } from "../dao/videocall-dao";
import { TherapistDao } from "../dao/therapist-dao";
import { SMSService } from "../sms/config";
import { EmailService } from "../mail/config";
import { AppLogger } from "../common/logging";
import { AdminStatisticsDao } from "../dao/admin-statistics-dao";
import { VideoChatEP } from "./video-chat-ep";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import Client from "../schemas/client-schema";
import Insurance from "../schemas/insurance-schema";
import { getStateNameFromAbbreviation } from "../common/state-utils";
import { StringOrObjectId } from "../common/util";
const { OAuth2Client } = require("google-auth-library");
var mongoose = require("mongoose");
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
var fs = require("fs");
const fss = require('fs').promises;
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const axios = require('axios');
const FormData = require('form-data');
const crypto = require('crypto');
import User from "../schemas/user-schema";

// VideoChatEP is already imported at the top of this file

export enum UploadCategory {
  INSURANCE_CARD = "INSURANCE_CARD",
  INSURANCE_CARD_BACK = "INSURANCE_CARD_BACK",
  CLAIM_CSV = "CLAIM_CSV",
}

export namespace InsuranceEp {

  function generateRandomKey(length: any) {
    return crypto.randomBytes(length).toString('hex').slice(0, length);
  }


  export async function checkEligibilityBeforInsuranceDetails(insurance: any, userId: any, res: Response) {
    try {

      const currentDate = new Date();

      const submitterOrganizationName = insurance.insuranceCompanyId;

      const memberId = insurance.subscriber.memberId;

      const isFound = await AdminDao.getInsuranceCompanyById(submitterOrganizationName);

      const clientData = await ClientDao.getUserById(userId);

      const insurancePartner = isFound.insuranceCompany as keyof typeof partner;

      const eligibility_payload_md = {
        "AccountKey": process.env.CLAIM_MD_ACCOUNT_KEY,
        "ins_name_f": clientData?.firstname,
        "ins_name_l": clientData?.lastname,
        "payerid": Tradingpartner_ServiceId[insurancePartner],
        "pat_rel": "18",
        "fdos": moment.utc(currentDate).format('YYYYMMDD'),
        "ins_dob": moment.utc(clientData?.dateOfBirth).format('YYYYMMDD'),
        "ins_sex": (clientData?.gender == "Male" ? "M" : "F"),
        "ins_number": memberId,
        "prov_npi": process.env.LAVNI_NPI,
      }

      const claimEligibilityAndActiveData = await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, clientData?._id);

      if (claimEligibilityAndActiveData?.finalStatus == "ACTIVE") {

        const eligibilityInfo = claimEligibilityAndActiveData?.eligibilityInfoData;

        let coPaymentClientAmount: any = clientData?.copaymentAmount

        let claimEligibilityData;
        if (coPaymentClientAmount) {
          claimEligibilityData = {
            claimEligibilityDetails: eligibilityInfo,
            coPaymentValue: coPaymentClientAmount
          }
        } else {
          claimEligibilityData = {
            claimEligibilityDetails: eligibilityInfo,
            coPaymentValue: 0
          }
        }

        return claimEligibilityData;
      } else {
        const resError = {
          error: "error",
          msg: claimEligibilityAndActiveData?.finalError
        }
        return resError;
      }

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function addInsuranceInfo(req: Request, res: Response, next: NextFunction) {
    const userId = req.user._id;
    let uploadCategory = UploadCategory.INSURANCE_CARD;
    let uploadInsuranceCategory = UploadCategory.INSURANCE_CARD_BACK;
    let newInsuranceTag: ObjectId;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateTherapisProfileValidationRules(req, cb);
      },
    });

    async function updateTherapisProfileValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let insuranceDetails = JSON.parse(req.body.insuranceDetails);

        if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
          return cb(Error("Deleting insurance card front image id is required."), null);
        }

        if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
          return cb(Error("Deleting insurance card back image id is required."), null);
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) => cb(error, "destination"));
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        AppLogger.error(`add-Insurance-Info1. userId: ${userId}. Error: ${error}`);
        return cb(Error(error), null);
      }
    }

    async function deleteOldFiles(uploadId: string) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldFile = await UploadDao.getUpload(uploadId);
        await fs.unlink(oldFile.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }
      return isDeleted;
    }

    const upload = multer({ storage: storage }).fields([{ name: "insuranceCardFront" }, { name: "insuranceCardBack" }]);
    const clientData = await ClientDao.getUserById(
      userId
    );
    try {

      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        let isValid = true;
        let isFileDeleted = false;
        const upload: any = req.files
        if (upload.insuranceCardFront || upload.insuranceCardBack) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedFrontImage = null;
          let uploadedBackImage = null;

          if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
            isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

            if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
          }

          if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
            isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

            if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
          }

          const upload: any = req.files;

          let signRequired: boolean = false;
          if (req.body.signRequired !== undefined) {
            signRequired = req.body.signRequired;
          }

          if (upload.insuranceCardFront) {
            const insuranceCard = {
              userId: userId as unknown as Types.ObjectId,
              originalName: upload.insuranceCardFront[0].originalname.replace(/ /g, ""),
              name: upload.insuranceCardFront[0].filename,
              type: upload.insuranceCardFront[0].mimetype,
              path: upload.insuranceCardFront[0].path,
              fileSize: upload.insuranceCardFront[0].size,
              extension: path.extname(upload.insuranceCardFront[0].originalname) || req.body.extension,
              category: uploadCategory,
              signRequired: signRequired,
            };

            uploadedFrontImage = await UploadDao.createUpload(insuranceCard);

            if (!uploadedFrontImage) {
              return res.sendError("Error while uploading insurance front image.");
            }
          }

          if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
            const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
            if (isFound) {
              newInsuranceTag = insuranceDetails.insuranceCompanyId;
            }
          }

          if (upload.insuranceCardBack) {
            const insuranceCard = {
              userId: userId as unknown as Types.ObjectId,
              originalName: upload.insuranceCardBack[0].originalname.replace(/ /g, ""),
              name: upload.insuranceCardBack[0].filename,
              type: upload.insuranceCardBack[0].mimetype,
              path: upload.insuranceCardBack[0].path,
              fileSize: upload.insuranceCardBack[0].size,
              extension: path.extname(upload.insuranceCardBack[0].originalname) || req.body.extension,
              category: uploadInsuranceCategory,
              signRequired: signRequired,
            };

            uploadedBackImage = await UploadDao.createUpload(insuranceCard);

            if (!uploadedBackImage) {
              return res.sendError("Error while uploading insurance back image.");
            }
          }

          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: uploadedFrontImage?._id ? uploadedFrontImage?._id : null,
            insuranceCardBackId: uploadedBackImage?._id ? uploadedBackImage?._id : null,
          };

          const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));
          const user = await AdminDao.getUserById(userId);
          const previousCopaymentAmount = user.copaymentAmount;


          if (insuranceCompanyData) {
            const updatedClient = await ClientDao.updateClientCoPayment(userId, insuranceCompanyData?.coPayment)

            let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(userId, insuranceCompanyData?.coPayment, previousCopaymentAmount);
          }

          const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)


          let updatedInsurance = await InsuranceDao.addInsuranceInfo(userId, insurance);

          if (updatedInsurance) {
            clientData.premiumStatus = PremiumStatus.ACTIVE
          }
          clientData.save();
          if (!updatedInsurance) {
            return res.sendError("Failed to update the insurance details.");
          }

          if (resData && 'error' in resData && resData.error === "error") {
            AppLogger.error(`add-Insurance-Info2. userId: ${userId}. Error: ${JSON.stringify(resData)}.`);
            return res.sendError(resData.msg);
          } else {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }

        } else {

          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardId is required.");
          }

          if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardId is required.");
          }

          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }
          }

          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
            }
          }

          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: insuranceDetails.insuranceCardId ? insuranceDetails.insuranceCardId : null,
            insuranceCardBackId: insuranceDetails.insuranceCardBackId ? insuranceDetails.insuranceCardBackId : null,
          };
          const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));

          const user = await AdminDao.getUserById(userId);
          const previousCopaymentAmount = user.copaymentAmount;


          if (insuranceCompanyData) {
            const updatedClient = await ClientDao.updateClientCoPayment(userId, insuranceCompanyData?.coPayment)

            let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(userId, insuranceCompanyData?.coPayment, previousCopaymentAmount);
          }

          const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)

          let updatedInsurance = await InsuranceDao.addInsuranceInfo(userId, insurance);
          if (updatedInsurance) {
            clientData.premiumStatus = PremiumStatus.ACTIVE
          }
          clientData.save();
          if (!updatedInsurance) {
            return res.sendError("Failed to update the therapist.");
          }
          if (resData && 'error' in resData && resData.error === "error" && resData.error === "error") {
            AppLogger.error(`add-Insurance-Info3. userId: ${userId}. Error: ${JSON.stringify(resData)}.`);
            return res.sendError(resData.msg);
          } else {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }
        }
      });
    } catch (error) {
      AppLogger.error(`add-Insurance-Info4. userId: ${userId}. Error: ${error}.`);
      return res.sendError(error);
    }
  }


  export async function updateInsuranceInfo(req: Request, res: Response, next: NextFunction) {
    const userId = req.user._id;
    let uploadCategory = UploadCategory.INSURANCE_CARD;
    let uploadInsuranceCategory = UploadCategory.INSURANCE_CARD_BACK;
    let newInsuranceTag: ObjectId;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateTherapisProfileValidationRules(req, cb);
      },
    });

    async function updateTherapisProfileValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let insuranceDetails = JSON.parse(req.body.insuranceDetails);

        if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
          return cb(Error("Deleting insurance card front id is required."), null);
        }

        if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
          return cb(Error("Deleting insurance card back id is required."), null);
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) => cb(error, "destination"));
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    async function deleteOldFiles(uploadId: string) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldFile = await UploadDao.getUpload(uploadId);
        await fs.unlink(oldFile.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }
      return isDeleted;
    }

    const upload = multer({ storage: storage }).fields([{ name: "insuranceCardFront" }, { name: "insuranceCardBack" }]);
    const client = await ClientDao.getUserById(req.user._id);
    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        let isValid = true;
        let isFileDeleted = false;
        const upload: any = req.files;

        if (upload.insuranceCardFront == undefined && upload.insuranceCardBack == undefined) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let plan = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceDetails.insuranceId));

          if (!plan) {
            return res.sendError("There's no insurance plan for the provided Id");
          }

          if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardFrontId is required.");
          }

          if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardBackId is required.");
          }

          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }
            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
            }
          }

          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: insuranceDetails.insuranceCardId ? insuranceDetails.insuranceCardId : null,
            insuranceCardBackId: insuranceDetails.insuranceCardBackId ? insuranceDetails.insuranceCardBackId : null,
          };
          let updatedInsurance = await InsuranceDao.updateInsurancePlan(insuranceDetails.insuranceId, insurance);

          if (!updatedInsurance) {
            return res.sendError("Failed to update the insurance details.");
          }

          return res.sendSuccess(updatedInsurance, "Successfully updated.");
        } else if (upload.insuranceCardBack == undefined) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedFrontImage = null;

          let plan = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceDetails.insuranceId));

          if (!plan) {
            return res.sendError("There's no insurance plan for the provided Id");
          }

          const user = await UserDao.getUser(userId);

          if (user.username) {
            if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            const upload: any = req.files;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
            }

            if (upload.insuranceCardFront) {
              const insuranceCard = {
                userId: userId as unknown as Types.ObjectId,
                originalName: upload.insuranceCardFront[0].originalname.replace(/ /g, ""),
                name: upload.insuranceCardFront[0].filename,
                type: upload.insuranceCardFront[0].mimetype,
                path: upload.insuranceCardFront[0].path,
                fileSize: upload.insuranceCardFront[0].size,
                extension: path.extname(upload.insuranceCardFront[0].originalname) || req.body.extension,
                category: uploadCategory,
                signRequired: signRequired,
              };

              uploadedFrontImage = await UploadDao.createUpload(insuranceCard);

              if (!uploadedFrontImage) {
                return res.sendError("Error while uploading insurance front image.");
              }
            }

            const insurance: DInsurance = {
              clientId: insuranceDetails.clientId,
              insuranceCompanyId: insuranceDetails.insuranceCompanyId,
              subscriber: insuranceDetails.subscriber,
              dependent: insuranceDetails.dependent,
              insuranceCardId: uploadedFrontImage._id,
              insuranceCardBackId: insuranceDetails.insuranceCardBackId ? insuranceDetails.insuranceCardBackId : null,
            };
            let updatedInsurance = await InsuranceDao.updateInsurancePlan(insuranceDetails.insuranceId, insurance);

            if (!updatedInsurance) {
              return res.sendError("Failed to update the insurance details.");
            }

            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }
        } else if (upload.insuranceCardFront == undefined) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedBackImage = null;

          let plan = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceDetails.insuranceId));

          if (!plan) {
            return res.sendError("There's no insurance plan for the provided Id");
          }

          const user = await UserDao.getUser(userId);

          if (user.username) {
            if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            const upload: any = req.files;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
            }

            if (upload.insuranceCardBack) {
              const insuranceCard = {
                userId: userId as unknown as Types.ObjectId,
                originalName: upload.insuranceCardBack[0].originalname.replace(/ /g, ""),
                name: upload.insuranceCardBack[0].filename,
                type: upload.insuranceCardBack[0].mimetype,
                path: upload.insuranceCardBack[0].path,
                fileSize: upload.insuranceCardBack[0].size,
                extension: path.extname(upload.insuranceCardBack[0].originalname) || req.body.extension,
                category: uploadInsuranceCategory,
                signRequired: signRequired,
              };

              uploadedBackImage = await UploadDao.createUpload(insuranceCard);

              if (!uploadedBackImage) {
                return res.sendError("Error while uploading profile image.");
              }
            }

            const insurance: DInsurance = {
              clientId: insuranceDetails.clientId,
              insuranceCompanyId: insuranceDetails.insuranceCompanyId,
              subscriber: insuranceDetails.subscriber,
              dependent: insuranceDetails.dependent,
              insuranceCardId: insuranceDetails.insuranceCardId ? insuranceDetails.insuranceCardId : null,
              insuranceCardBackId: uploadedBackImage._id,
            };
            let updatedInsurance = await InsuranceDao.updateInsurancePlan(insuranceDetails.insuranceId, insurance);

            if (!updatedInsurance) {
              return res.sendError("Failed to update the insurance details.");
            }

            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }
        } else {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedFrontImage = null;
          let uploadedBackImage = null;

          let plan = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceDetails.insuranceId));

          if (!plan) {
            return res.sendError("There's no insurance plan for the provided Id");
          }

          const user = await UserDao.getUser(userId);

          if (user.username) {
            if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            const upload: any = req.files;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
            }

            if (upload.insuranceCardFront) {
              const insuranceCard = {
                userId: userId as unknown as Types.ObjectId,
                originalName: upload.insuranceCardFront[0].originalname.replace(/ /g, ""),
                name: upload.insuranceCardFront[0].filename,
                type: upload.insuranceCardFront[0].mimetype,
                path: upload.insuranceCardFront[0].path,
                fileSize: upload.insuranceCardFront[0].size,
                extension: path.extname(upload.insuranceCardFront[0].originalname) || req.body.extension,
                category: uploadCategory,
                signRequired: signRequired,
              };

              uploadedFrontImage = await UploadDao.createUpload(insuranceCard);

              if (!uploadedFrontImage) {
                return res.sendError("Error while uploading insurance front image.");
              }
            }

            if (upload.insuranceCardBack) {
              const insuranceCard = {
                userId: userId as unknown as Types.ObjectId,
                originalName: upload.insuranceCardBack[0].originalname.replace(/ /g, ""),
                name: upload.insuranceCardBack[0].filename,
                type: upload.insuranceCardBack[0].mimetype,
                path: upload.insuranceCardBack[0].path,
                fileSize: upload.insuranceCardBack[0].size,
                extension: path.extname(upload.insuranceCardBack[0].originalname) || req.body.extension,
                category: uploadInsuranceCategory,
                signRequired: signRequired,
              };

              uploadedBackImage = await UploadDao.createUpload(insuranceCard);
              if (!uploadedBackImage) {
                return res.sendError("Error while uploading profile image.");
              }
            }

            const insurance: DInsurance = {
              clientId: insuranceDetails.clientId,
              insuranceCompanyId: insuranceDetails.insuranceCompanyId,
              subscriber: insuranceDetails.subscriber,
              dependent: insuranceDetails.dependent,
              insuranceCardId: uploadedFrontImage._id,
              insuranceCardBackId: uploadedBackImage._id,
            };
            let updatedInsurance = await InsuranceDao.updateInsurancePlan(insuranceDetails.insuranceId, insurance);

            if (!updatedInsurance) {
              return res.sendError("Failed to update the insurance details.");
            }

            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateInsuranceInfoBYAdmin(req: Request, res: Response, next: NextFunction) {
    const userId = req.params.userId;
    let uploadCategory = UploadCategory.INSURANCE_CARD;
    let uploadInsuranceCategory = UploadCategory.INSURANCE_CARD_BACK;
    let newInsuranceTag: ObjectId;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateTherapisProfileValidationRules(req, cb);
      },
    });

    async function updateTherapisProfileValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let insuranceDetails = JSON.parse(req.body.insuranceDetails);
        if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
          return cb(Error("Deleting insurance card front id is required."), null);
        }

        if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
          return cb(Error("Deleting insurance card back id is required."), null);
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) => cb(error, "destination"));
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    async function deleteOldFiles(uploadId: string) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldFile = await UploadDao.getUpload(uploadId);
        await fs.unlink(oldFile.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }
      return isDeleted;
    }

    const upload = multer({ storage: storage }).fields([{ name: "insuranceCardFront" }, { name: "insuranceCardBack" }]);

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        let isValid = true;
        let isFileDeleted = false;
        const upload: any = req.files;

        if (upload.insuranceCardFront == undefined && upload.insuranceCardBack == undefined) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);

          let plan = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceDetails.insuranceId));

          if (!plan) {
            return res.sendError("There's no insurance plan for the provided Id");
          }

          if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardFrontId is required.");
          }

          if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardBackId is required.");
          }

          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);

              if (isFound) {
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
            }
          }

          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: insuranceDetails.insuranceCardId ? insuranceDetails.insuranceCardId : null,
            insuranceCardBackId: insuranceDetails.insuranceCardBackId ? insuranceDetails.insuranceCardBackId : null,
          };

          const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));

          const user = await AdminDao.getUserById(Types.ObjectId(userId));
          const previousCopaymentAmount = user.copaymentAmount;


          if (insuranceCompanyData) {
            const updatedClient = await ClientDao.updateClientCoPayment(Types.ObjectId(userId), insuranceCompanyData?.coPayment)

            let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(Types.ObjectId(userId), insuranceCompanyData?.coPayment, previousCopaymentAmount);
          }


          const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)


          let updatedInsurance = await InsuranceDao.updateInsurancePlan(insuranceDetails.insuranceId, insurance);

          if (!updatedInsurance) {
            return res.sendError("Failed to update the insurance details.");
          }

          if (resData && 'error' in resData && resData.error === "error") {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          } else {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }
        } else if (upload.insuranceCardBack == undefined) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedFrontImage = null;

          let plan = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceDetails.insuranceId));

          if (!plan) {
            return res.sendError("There's no insurance plan for the provided Id");
          }

          const user = await UserDao.getUser(Types.ObjectId(userId));

          if (user.username) {
            if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            const upload: any = req.files;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                // const addedTag = await AdminDao.addInsuranceCompany(insuranceDetails.insuranceCompanyId);
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
              // else {
              //   newInsuranceTag = isFound.insuranceCompany;
              // }
            }

            if (upload.insuranceCardFront) {
              const insuranceCard = {
                userId: userId as unknown as Types.ObjectId,
                originalName: upload.insuranceCardFront[0].originalname.replace(/ /g, ""),
                name: upload.insuranceCardFront[0].filename,
                type: upload.insuranceCardFront[0].mimetype,
                path: upload.insuranceCardFront[0].path,
                fileSize: upload.insuranceCardFront[0].size,
                extension: path.extname(upload.insuranceCardFront[0].originalname) || req.body.extension,
                category: uploadCategory,
                signRequired: signRequired,
              };

              uploadedFrontImage = await UploadDao.createUpload(insuranceCard);

              if (!uploadedFrontImage) {
                return res.sendError("Error while uploading insurance front image.");
              }
            }

            const insurance: DInsurance = {
              clientId: insuranceDetails.clientId,
              insuranceCompanyId: insuranceDetails.insuranceCompanyId,
              subscriber: insuranceDetails.subscriber,
              dependent: insuranceDetails.dependent,
              insuranceCardId: uploadedFrontImage._id,
              insuranceCardBackId: insuranceDetails.insuranceCardBackId ? insuranceDetails.insuranceCardBackId : null,
            };

            const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));

            const user = await AdminDao.getUserById(Types.ObjectId(userId));
            const previousCopaymentAmount = user.copaymentAmount;


            if (insuranceCompanyData) {
              const updatedClient = await ClientDao.updateClientCoPayment(Types.ObjectId(userId), insuranceCompanyData?.coPayment)

              let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(Types.ObjectId(userId), insuranceCompanyData?.coPayment, previousCopaymentAmount);
            }

            const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)


            let updatedInsurance = await InsuranceDao.updateInsurancePlan(insuranceDetails.insuranceId, insurance);

            if (!updatedInsurance) {
              return res.sendError("Failed to update the insurance details.");
            }

            if (resData && 'error' in resData && resData.error === "error") {
              return res.sendSuccess(updatedInsurance, "Successfully updated.");
            } else {
              return res.sendSuccess(updatedInsurance, "Successfully updated.");
            }
          }
        } else if (upload.insuranceCardFront == undefined) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedBackImage = null;

          let plan = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceDetails.insuranceId));

          if (!plan) {
            return res.sendError("There's no insurance plan for the provided Id");
          }

          const user = await UserDao.getUser(Types.ObjectId(userId));

          if (user.username) {
            if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            const upload: any = req.files;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                // const addedTag = await AdminDao.addInsuranceCompany(insuranceDetails.insuranceCompanyId);
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
              // else {
              //   newInsuranceTag = isFound.insuranceCompany;
              // }
            }

            if (upload.insuranceCardBack) {
              const insuranceCard = {
                userId: userId as unknown as Types.ObjectId,
                originalName: upload.insuranceCardBack[0].originalname.replace(/ /g, ""),
                name: upload.insuranceCardBack[0].filename,
                type: upload.insuranceCardBack[0].mimetype,
                path: upload.insuranceCardBack[0].path,
                fileSize: upload.insuranceCardBack[0].size,
                extension: path.extname(upload.insuranceCardBack[0].originalname) || req.body.extension,
                category: uploadInsuranceCategory,
                signRequired: signRequired,
              };

              uploadedBackImage = await UploadDao.createUpload(insuranceCard);

              if (!uploadedBackImage) {
                return res.sendError("Error while uploading profile image.");
              }
            }

            const insurance: DInsurance = {
              clientId: insuranceDetails.clientId,
              insuranceCompanyId: insuranceDetails.insuranceCompanyId,
              subscriber: insuranceDetails.subscriber,
              dependent: insuranceDetails.dependent,
              insuranceCardId: insuranceDetails.insuranceCardId ? insuranceDetails.insuranceCardId : null,
              insuranceCardBackId: uploadedBackImage._id,
            };

            const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));

            const user = await AdminDao.getUserById(Types.ObjectId(userId));
            const previousCopaymentAmount = user.copaymentAmount;


            if (insuranceCompanyData) {
              const updatedClient = await ClientDao.updateClientCoPayment(Types.ObjectId(userId), insuranceCompanyData?.coPayment)

              let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(Types.ObjectId(userId), insuranceCompanyData?.coPayment, previousCopaymentAmount);
            }
            const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)


            let updatedInsurance = await InsuranceDao.updateInsurancePlan(insuranceDetails.insuranceId, insurance);

            if (!updatedInsurance) {
              return res.sendError("Failed to update the insurance details.");
            }

            if (resData && 'error' in resData && resData.error === "error") {
              return res.sendSuccess(updatedInsurance, "Successfully updated.");
            } else {
              return res.sendSuccess(updatedInsurance, "Successfully updated.");
            }
          }
        } else {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedFrontImage = null;
          let uploadedBackImage = null;

          let plan = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceDetails.insuranceId));

          if (!plan) {
            return res.sendError("There's no insurance plan for the provided Id");
          }

          const user = await UserDao.getUser(Types.ObjectId(userId));

          if (user.username) {
            if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            const upload: any = req.files;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                // const addedTag = await AdminDao.addInsuranceCompany(insuranceDetails.insuranceCompanyId);
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
              // else {
              //   newInsuranceTag = isFound.insuranceCompany;
              // }
            }

            if (upload.insuranceCardFront) {
              const insuranceCard = {
                userId: userId as unknown as Types.ObjectId,
                originalName: upload.insuranceCardFront[0].originalname.replace(/ /g, ""),
                name: upload.insuranceCardFront[0].filename,
                type: upload.insuranceCardFront[0].mimetype,
                path: upload.insuranceCardFront[0].path,
                fileSize: upload.insuranceCardFront[0].size,
                extension: path.extname(upload.insuranceCardFront[0].originalname) || req.body.extension,
                category: uploadCategory,
                signRequired: signRequired,
              };

              uploadedFrontImage = await UploadDao.createUpload(insuranceCard);

              if (!uploadedFrontImage) {
                return res.sendError("Error while uploading insurance front image.");
              }
            }

            if (upload.insuranceCardBack) {
              const insuranceCard = {
                userId: userId as unknown as Types.ObjectId,
                originalName: upload.insuranceCardBack[0].originalname.replace(/ /g, ""),
                name: upload.insuranceCardBack[0].filename,
                type: upload.insuranceCardBack[0].mimetype,
                path: upload.insuranceCardBack[0].path,
                fileSize: upload.insuranceCardBack[0].size,
                extension: path.extname(upload.insuranceCardBack[0].originalname) || req.body.extension,
                category: uploadInsuranceCategory,
                signRequired: signRequired,
              };

              uploadedBackImage = await UploadDao.createUpload(insuranceCard);
              if (!uploadedBackImage) {
                return res.sendError("Error while uploading profile image.");
              }
            }

            const insurance: DInsurance = {
              clientId: insuranceDetails.clientId,
              insuranceCompanyId: insuranceDetails.insuranceCompanyId,
              subscriber: insuranceDetails.subscriber,
              dependent: insuranceDetails.dependent,
              insuranceCardId: uploadedFrontImage._id,
              insuranceCardBackId: uploadedBackImage._id,
            };

            const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));

            const user = await AdminDao.getUserById(Types.ObjectId(userId));
            const previousCopaymentAmount = user.copaymentAmount;


            if (insuranceCompanyData) {
              const updatedClient = await ClientDao.updateClientCoPayment(Types.ObjectId(userId), insuranceCompanyData?.coPayment)

              let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(Types.ObjectId(userId), insuranceCompanyData?.coPayment, previousCopaymentAmount);
            }
            const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)


            let updatedInsurance = await InsuranceDao.updateInsurancePlan(insuranceDetails.insuranceId, insurance);

            if (!updatedInsurance) {
              return res.sendError("Failed to update the insurance details.");
            }

            if (resData && 'error' in resData && resData.error === "error") {
              return res.sendSuccess(updatedInsurance, "Successfully updated.");
            } else {
              return res.sendSuccess(updatedInsurance, "Successfully updated.");
            }
          }
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function viewSingleInsurance(req: Request, res: Response, next: NextFunction) {
    const role = req.user.role;
    const clientId = req.params.id;

    if (role == UserRole.CLIENT || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        let insurance = await InsuranceDao.getInsuranceByClientId(clientId);

        return res.sendSuccess(insurance, "Insurance Received Successfully.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function checkInsuranceEligibility(req: Request, res: Response, next: NextFunction) {
    const insuranceId = req.params.insuranceId;
    const serviceId = req.params.serviceId;

    const LAVNI_SERVICE_CODE = "A7";

    if (!mongoose.Types.ObjectId.isValid(insuranceId)) {
      return res.sendError("Invalid insurance Id");
    }

    async function isExpired(token: string | undefined) {
      if (token) {
        try {
          const user = JSON.parse(atob(token.split(".")[1]));

          if (user?.exp * 1000 < Date.now()) {
            return true;
          }

          return false;
        } catch (e) {
          return true;
        }
      } else {
        return true;
      }
    }

    async function getEligibilityDetails(data: any, token: string) {
      const eligibilityDetails = await fetch(process.env.CHANGE_HEALTHCARE_ELIGIBILITY, {
        method: "POST",
        body: JSON.stringify(data),
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });
      return await eligibilityDetails.json();
    }

    function filterLavniServices(benefitInformation: any[]) {
      return benefitInformation.filter((item: any) => {
        return item.serviceTypeCodes?.includes(LAVNI_SERVICE_CODE);
      });
    }

    function generateControlNumber() {
      return Math.floor(********* + Math.random() * *********).toString();
    }

    try {
      let controlNumber = "";

      const insurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceId));

      if (!insurance) {
        return res.sendError("Could not find insurance details for the provided Id!");
      }

      if (!insurance.controlNumber) {
        controlNumber = generateControlNumber();
        await InsuranceDao.updateInsurancePlan(insuranceId, {
          controlNumber: controlNumber,
        });
      } else {
        controlNumber = insurance?.controlNumber;
      }

      const eligibilityDataSet = await InsuranceDao.getDetailsForEligibility(Types.ObjectId(insuranceId));

      const insurancePartner = serviceId as keyof typeof partner;
      let eligibilityObject = null;

      if (eligibilityDataSet[0].dependent) {
        eligibilityObject = {
          controlNumber: controlNumber,
          tradingPartnerServiceId: partner[insurancePartner],
          provider: {
            organizationName: process.env.LAVNI_ORGANIZATION_NAME,
            npi: process.env.LAVNI_NPI,
          },
          subscriber: {
            memberId: eligibilityDataSet[0].subscriber.memberId,
            firstName: eligibilityDataSet[0].subscriber.firstName,
            lastName: eligibilityDataSet[0].subscriber.lastName,
            gender: (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
            dateOfBirth: moment.utc(eligibilityDataSet[0].clientId.dateOfBirth).format("YYYYMMDD"),
          },
          dependent: {
            memberId: eligibilityDataSet[0].dependent.memberId,
            firstName: eligibilityDataSet[0].dependent.firstName,
            lastName: eligibilityDataSet[0].dependent.lastName,
            gender: (eligibilityDataSet[0].dependent.gender == "Male" ? "M" : "F"),
            dateOfBirth: moment.utc(eligibilityDataSet[0].dependent.dateOfBirth).format("YYYYMMDD"),
          },
          encounter: {
            dateOfService: "20230831",
            serviceTypeCodes: ["98"]
          },
        };
      } else {
        eligibilityObject = {
          controlNumber: controlNumber,
          tradingPartnerServiceId: partner[insurancePartner],
          provider: {
            organizationName: process.env.LAVNI_ORGANIZATION_NAME,
            npi: process.env.LAVNI_NPI,
          },
          subscriber: {
            memberId: eligibilityDataSet[0].subscriber.memberId,
            firstName: eligibilityDataSet[0].subscriber.firstName,
            lastName: eligibilityDataSet[0].subscriber.lastName,
            gender: (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
            dateOfBirth: moment.utc(eligibilityDataSet[0].clientId.dateOfBirth).format("YYYYMMDD"),
          },
          encounter: {
            dateOfService: "20230831",
            serviceTypeCodes: ["98"]
          }
        };
      }

      const hasExpired = await isExpired(insurance?.insuranceAccessToken);
      let eligibilityDetails = null;

      if (hasExpired) {
        const token: InsuranceToken = {
          client_id: process.env.CHANGE_HEALTHCARE_CLIENT_ID,
          client_secret: process.env.CHANGE_HEALTHCARE_CLIENT_SECRET,
          grant_type: process.env.CHANGE_HEALTHCARE_GRANT_TYPE,
        };

        const newToken = await fetch(process.env.CHANGE_HEALTHCARE_TOKEN_URL, {
          method: "POST",
          body: JSON.stringify(token),
          headers: {
            "Content-Type": "application/json",
          },
        });

        const accessToken: AccessTokenResponse = await newToken.json();

        await InsuranceDao.updateInsurancePlan(insuranceId, {
          insuranceAccessToken: accessToken.access_token,
        });

        eligibilityDetails = await getEligibilityDetails(eligibilityObject, accessToken.access_token);
      }

      if (!hasExpired) {
        eligibilityDetails = await getEligibilityDetails(eligibilityObject, insurance.insuranceAccessToken);
      }

      const benefitInformationList = filterLavniServices(eligibilityDetails?.benefitsInformation);

      const response: Eligibility = {
        controlNumber: eligibilityDetails?.controlNumber,
        reassociationKey: eligibilityDetails?.reassociationKey,
        tradingPartnerServiceId: eligibilityDetails?.tradingPartnerServiceId,
        provider: {
          serviceProviderNumber: eligibilityDetails?.provider?.serviceProviderNumber,
          entityIdentifier: eligibilityDetails?.provider?.entityIdentifier,
          entityType: eligibilityDetails?.provider?.entityType,
        },
        subscriber: {
          firstName: eligibilityDetails?.subscriber?.firstName,
          lastName: eligibilityDetails?.subscriber?.lastName,
          dateOfBirth: eligibilityDetails?.subscriber?.dateOfBirth,
          gender: eligibilityDetails?.subscriber?.gender,
          memberId: eligibilityDetails?.subscriber?.memberId,
          entityIdentifier: eligibilityDetails?.subscriber?.entityIdentifier,
          entityType: eligibilityDetails?.subscriber?.entityType,
          groupNumber: eligibilityDetails?.subscriber?.groupNumber,
          relationToSubscriber: eligibilityDetails?.subscriber?.relationToSubscriber,
          insuredIndicator: eligibilityDetails?.subscriber?.insuredIndicator,
          maintenanceTypeCode: eligibilityDetails?.subscriber?.maintenanceTypeCode,
          maintenanceReasonCode: eligibilityDetails?.subscriber?.maintenanceReasonCode,
        },
        benefitInformation: benefitInformationList,
      };

      return res.sendSuccess(response, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }
  async function validateClaim(claimDetails: any, token: string) {
    const claimValidation = await fetch(process.env.CHANGE_HEALTHCARE_CLAIM_VALIDATION, {
      method: "POST",
      body: JSON.stringify(claimDetails),
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    return await claimValidation.json();
  }

  async function eligibilityClaim(claimDetails: any, token: string) {
    try {
      const claimValidation = await fetch(process.env.CHANGE_HEALTHCARE_ELIGIBILITY, {
        method: "POST",
        body: JSON.stringify(claimDetails),
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });
      // if (!claimValidation.ok) {
      //   return new Error(`Claim validation failed with status ${claimValidation.status}`);
      // }
      const responseData = await claimValidation.json();
      return responseData;
    } catch (error) {
      console.error("Error in eligibilityClaim:", error);
      throw error;
    }
  }

  async function createCsvFromPayload(payload: any, outputFilePath: any) {
    try {
      let finalFilePath = `${process.env.UPLOAD_PATH}/${UploadCategory.CLAIM_CSV}/${outputFilePath}`
      const csvWriter = createCsvWriter({
        path: finalFilePath,
        header: Object.keys(payload).map(key => ({ id: key, title: key }))
      });

      await csvWriter.writeRecords([payload]);

      const fileData = await fss.readFile(finalFilePath);

      const formData = new FormData();
      formData.append('AccountKey', process.env.CLAIM_MD_ACCOUNT_KEY);
      formData.append('File', fileData, finalFilePath);

      const headers = {
        ...formData.getHeaders(),
        'Accept': 'application/json',
      };

      const response = await axios.post(process.env.CHANGE_HEALTHCARE_CLAIM_MD_UPLOAD, formData, { headers });

      if (response.status === 200) {
        const responseData = await response.data;
        return responseData;
      } else {
        const errData = {
          "error": "Failed to submit claim file."
        }
        return errData;
      }
    } catch (error) {
      throw error;
    }
  }


  async function submitClaim(claimDetails: any, token: string) {
    try {
      const claimSubmission = await fetch(process.env.CHANGE_HEALTHCARE_CLAIM_SUBMISSION, {
        method: "POST",
        body: JSON.stringify(claimDetails),
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      return await claimSubmission.json();
    } catch (error) {
      console.error("Claim submission failed. Please try again later....", error);
      throw error;
    }
  }

  async function isExpired(token: string | undefined) {
    if (token) {
      try {
        const user = JSON.parse(atob(token.split(".")[1]));

        if (user?.exp * 1000 < Date.now()) {
          return true;
        }

        return false;
      } catch (e) {
        return true;
      }
    } else {
      return true;
    }
  }

  export async function setDailySuccessClaimsStatusUpdate() {
    AppLogger.info(`.:: update Cliams Status Every Day ::.`);
    const claimList = await AdminStatisticsDao.getSuccessTeatmentHistoryByAdmin(
    );

    if (claimList) {
      for (const claim of claimList) {
        const isFound = await AdminDao.getInsuranceCompanyById(claim.clientInsuranceDetails?.insuranceCompanyId);

        const insuranceId = claim.clientDetails.insuranceId;
        const serviceId = isFound.insuranceCompany;
        const noteId = claim.diagnosisNoteDetails._id;

        const result = await InsuranceEp.viewClaimStautsDaily(insuranceId, serviceId, noteId)
      }
    }
  }

  export async function checkEligibility(req: Request, res: Response, next: NextFunction) {
    const clientId = req.params.userId;

    function generateControlNumber() {
      return Math.floor(********* + Math.random() * *********).toString();
    }

    try {
      let controlNumber = "";
      const client = await AdminDao.getUserById(Types.ObjectId(clientId));
      const insurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(client?.insuranceId));
      const isFound = await AdminDao.getInsuranceCompanyById(insurance?.insuranceCompanyId);
      if (client?.subscriptionStatus !== "active" && !isFound?.insuranceCompany) {
        return res.sendError("Client insurance company not available. Please try again later.");
      }
      const currentDate = new Date();
      if (!insurance.controlNumber) {
        controlNumber = generateControlNumber();
        await InsuranceDao.updateInsurancePlan(client?.insuranceId, {
          controlNumber: controlNumber,
        });
      } else {
        controlNumber = insurance?.controlNumber;
      }

      const insurancePartner = isFound?.insuranceCompany as keyof typeof partner;
      let eligibility_payload = null;

      eligibility_payload = {
        controlNumber: "*********",
        tradingPartnerServiceId: partner[insurancePartner],
        provider: {
          organizationName: process.env.LAVNI_ORGANIZATION_NAME,
          npi: process.env.LAVNI_NPI,
        },
        subscriber: {
          memberId: insurance?.subscriber?.memberId,
          firstName: client?.firstname,
          lastName: client?.lastname,
          gender: (client?.gender == "Male" ? "M" : "F"),
          dateOfBirth: moment.utc(client?.dateOfBirth).format("YYYYMMDD"),
        },
        encounter: {
          dateOfService: moment(currentDate).utcOffset(-5 * 60).format('YYYYMMDD'), serviceTypeCodes: ['98']
        }
      };

      const hasExpired = await isExpired(insurance?.insuranceAccessToken);

      if (hasExpired) {
        const token: InsuranceToken = {
          client_id: process.env.CHANGE_HEALTHCARE_CLIENT_ID,
          client_secret: process.env.CHANGE_HEALTHCARE_CLIENT_SECRET,
          grant_type: process.env.CHANGE_HEALTHCARE_GRANT_TYPE,
        };

        const newToken = await fetch(process.env.CHANGE_HEALTHCARE_TOKEN_URL, {
          method: "POST",
          body: JSON.stringify(token),
          headers: {
            "Content-Type": "application/json",
          },
        });


        const accessToken: AccessTokenResponse = await newToken.json();

        await InsuranceDao.updateInsurancePlan(client?.insuranceId, {
          insuranceAccessToken: accessToken.access_token,
        });

        const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, accessToken.access_token);
        if (!(claimEligibilityDetails?.errors?.length > 0)) {
          let coPaymentValue = null;

          const identity = [];

          if ('dependents' in claimEligibilityDetails) {
            identity.push('dependents');
          } else {
            identity.push('subscriber');
          }


          const eligibilityData = {
            subscriber: identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0],
            memberId: claimEligibilityDetails.subscriber.memberId,
          }
          let updatedClientEligibility = await ClientDao.updateClientEligibility(clientId, eligibilityData);

          if (claimEligibilityDetails?.benefitsInformation) {
            const coPaymentBenefits = claimEligibilityDetails.benefitsInformation.filter((benefit: any) => benefit.name === 'Co-Payment');

            if (coPaymentBenefits.length > 0) {
              const coPaymentAmounts = coPaymentBenefits.map((benefit: any) => parseFloat(benefit.benefitAmount));
              coPaymentValue = coPaymentAmounts[0];
            }
          }

          const claimEligibilityData = {
            claimEligibilityDetails: claimEligibilityDetails,
            coPaymentValue: coPaymentValue
          }

          return res.sendSuccess(claimEligibilityData, "Sucess");
        } else {
          return res.sendError(JSON.stringify(claimEligibilityDetails?.errors[0]?.description));
        }
      }

      if (!hasExpired) {
        const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, insurance?.insuranceAccessToken);
        if (!(claimEligibilityDetails?.errors?.length > 0)) {
          let coPaymentValue = null;

          const identity = [];

          if ('dependents' in claimEligibilityDetails) {
            identity.push('dependents');
          } else {
            identity.push('subscriber');
          }


          const eligibilityData = {
            subscriber: identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0],
            memberId: claimEligibilityDetails.subscriber.memberId,
          }
          let updatedClientEligibility = await ClientDao.updateClientEligibility(clientId, eligibilityData);

          if (claimEligibilityDetails?.benefitsInformation) {
            const coPaymentBenefits = claimEligibilityDetails.benefitsInformation.filter((benefit: any) => benefit.name === 'Co-Payment');

            if (coPaymentBenefits.length > 0) {
              const coPaymentAmounts = coPaymentBenefits.map((benefit: any) => parseFloat(benefit.benefitAmount));
              coPaymentValue = coPaymentAmounts[0];
            }
          }

          const claimEligibilityData = {
            claimEligibilityDetails: claimEligibilityDetails,
            coPaymentValue: coPaymentValue
          }

          return res.sendSuccess(claimEligibilityData, "Success");
        } else {
          return res.sendError(JSON.stringify(claimEligibilityDetails?.errors[0]?.description));
        }
      }
    } catch (error) {
      return res.sendError(error);
    }
  }


  export async function checkEligibilityMD(req: Request, res: Response, next: NextFunction) {
    try {
      const clientId = req.params.userId;

      if (!clientId) {
        return res.sendError("user id is required");
      }

      const client = await AdminDao.getUserById(Types.ObjectId(clientId));

      if (!client) {
        return res.sendError("Invalid user id");
      }

      const currentDate = new Date();

      // NOTE: Handle for second insurance
      // Check eligibility for secondary insurance (if exists)
      if (client?.secondaryInsuranceId) {
        try {
          // Lấy thông tin secondary insurance
          const secondaryInsurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(client.secondaryInsuranceId));
          if (secondaryInsurance) {
            // Lấy thông tin công ty bảo hiểm của secondary
            const secondaryIsFound = await AdminDao.getInsuranceCompanyById(secondaryInsurance?.insuranceCompanyId);
            const secondaryInsurancePartner = secondaryIsFound?.insuranceCompany as keyof typeof Tradingpartner_ServiceId;

            // Tạo eligibility payload cho secondary insurance
            const secondaryEligibilityPayload = {
              "AccountKey": "18420_X07Qzhib2EmpXA42arGNCR6e",
              "ins_name_f": client?.firstname,
              "ins_name_l": client?.lastname,
              "payerid": Tradingpartner_ServiceId[secondaryInsurancePartner],
              "pat_rel": "18",
              "fdos": moment.utc(currentDate).format('YYYYMMDD'),
              "ins_dob": moment.utc(client?.dateOfBirth).format('YYYYMMDD'),
              "ins_sex": (client?.gender == "Male" ? "M" : "F"),
              "ins_number": secondaryInsurance?.subscriber?.memberId,
              "prov_npi": "**********"
            };

            // Gọi check eligibility cho secondary insurance
            const secondaryEligibilityResult = await VideoChatEP.checkClaimMdEligibility2(secondaryEligibilityPayload, client._id);
            console.log("secondaryEligibilityResult", secondaryEligibilityResult);

            // Nếu secondary insurance ACTIVE thì update thông tin vào bảng insurance
            if (secondaryEligibilityResult?.finalStatus === "ACTIVE") {
              const secondaryEligibilityInfo = secondaryEligibilityResult?.eligibilityInfoData;
              try {
                const secondaryInsuranceRecord = await InsuranceDao.getInsurancePlanById(Types.ObjectId(client.secondaryInsuranceId));
                if (secondaryInsuranceRecord) {
                  const directMongoDBUpdate: Record<string, any> = {};
                  if (secondaryEligibilityInfo.ins_number) {
                    directMongoDBUpdate['subscriber.memberId'] = secondaryEligibilityInfo.ins_number;
                  }
                  if (secondaryEligibilityInfo.ins_name_f) {
                    directMongoDBUpdate['subscriber.firstName'] = secondaryEligibilityInfo.ins_name_f;
                  }
                  if (secondaryEligibilityInfo.ins_name_l) {
                    directMongoDBUpdate['subscriber.lastName'] = secondaryEligibilityInfo.ins_name_l;
                  }
                  if (secondaryEligibilityInfo.ins_addr_1) {
                    directMongoDBUpdate['subscriber.address.address1'] = secondaryEligibilityInfo.ins_addr_1;
                  }
                  if (secondaryEligibilityInfo.ins_city) {
                    directMongoDBUpdate['subscriber.address.city'] = secondaryEligibilityInfo.ins_city;
                  }
                  if (secondaryEligibilityInfo.ins_state) {
                    const fullStateName = getStateNameFromAbbreviation(secondaryEligibilityInfo.ins_state);
                    directMongoDBUpdate['subscriber.address.state'] = fullStateName;
                  }
                  if (secondaryEligibilityInfo.ins_zip) {
                    directMongoDBUpdate['subscriber.address.postalCode'] = secondaryEligibilityInfo.ins_zip;
                  }
                  if (!secondaryInsuranceRecord.subscriber.address && Object.keys(directMongoDBUpdate).some(key => key.startsWith('subscriber.address.'))) {
                    await Insurance.updateOne(
                      { _id: secondaryInsuranceRecord._id },
                      { $set: { 'subscriber.address': {} } }
                    );
                  }
                  if (Object.keys(directMongoDBUpdate).length > 0) {
                    await Insurance.updateOne(
                      { _id: secondaryInsuranceRecord._id },
                      { $set: directMongoDBUpdate },
                      { upsert: false }
                    );
                  }
                }
              } catch (err) {
                console.error("Error updating secondary insurance with eligibility data:", err);
              }
            }else{
              // Send notification email to admins (<EMAIL>, <EMAIL>)
              try {
                const adminEmails = ["<EMAIL>", "<EMAIL>"];
                const subject = `Secondary Insurance eligibility error for client ${client?.firstname || ''} ${client?.lastname || ''}`;
                const body = `Secondary Insurance of user <b>${client?.firstname || ''} ${client?.lastname || ''} (${client?.email || ''})</b> has an eligibility issue.<br><br>Reason: <b>${secondaryEligibilityResult?.finalError || 'Unknown error'}</b><br><br>Please check and update copayment if necessary.`;
                for (const email of adminEmails) {
                  await EmailService.sendEmailToSelectedUsers(email, body, subject);
                }
              } catch (adminMailError) {
                AppLogger.error("Error sending insurance eligibility error email to admins:", adminMailError);
              }
            }
          }
        } catch (err) {
          console.error("Error processing secondary insurance eligibility:", err);
        }
      }


      //  NOTE: Handle for first insurance
      const insurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(client?.insuranceId));

      const isFound = await AdminDao.getInsuranceCompanyById(insurance?.insuranceCompanyId);

      if (client?.subscriptionStatus !== "active" && !isFound?.insuranceCompany) {
        return res.sendError("Client insurance company not available. Please try again later.");
      }
      
      const insurancePartner = isFound?.insuranceCompany as keyof typeof Tradingpartner_ServiceId;

      const eligibility_payload_md = {
        // "AccountKey": process.env.CLAIM_MD_ACCOUNT_KEY,
        "AccountKey": "18420_X07Qzhib2EmpXA42arGNCR6e",
        "ins_name_f": client?.firstname,
        "ins_name_l": client?.lastname,
        "payerid": Tradingpartner_ServiceId[insurancePartner],
        "pat_rel": "18",
        "fdos": moment.utc(currentDate).format('YYYYMMDD'),
        "ins_dob": moment.utc(client?.dateOfBirth).format('YYYYMMDD'),
        "ins_sex": (client?.gender == "Male" ? "M" : "F"),
        "ins_number": insurance?.subscriber?.memberId,
        // "prov_npi": process.env.LAVNI_NPI,
        "prov_npi":"**********"
      }
      
      const claimEligibilityAndActiveData = await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, client?._id);

      if (claimEligibilityAndActiveData?.finalStatus == "ACTIVE") {

        const eligibilityInfo = claimEligibilityAndActiveData?.eligibilityInfoData;
        
        // If client has REVOKED premium status and insurance is now active, restore premium status
        if (client && client.premiumStatus === PremiumStatus.REVOKED) {
          try {
            // Update client's premium status to ACTIVE
            await ClientDao.updateClient(client._id, {
              premiumStatus: PremiumStatus.ACTIVE,
              // premiumMembershipStartedDate: new Date()
            });
            
            AppLogger.info(`Client ${client._id} upgraded to premium due to restored active insurance`);
          } catch (upgradeError) {
            // Log error but continue with the response
            console.error("Error during client premium upgrade process:", upgradeError);
          }
        }

        // Update insurance record with the fetched eligibility data
        if (eligibilityInfo && client?.insuranceId) {
          try {
            // Get the insurance record
            const insuranceRecord = await InsuranceDao.getInsurancePlanById(Types.ObjectId(client.insuranceId));
            
            if (insuranceRecord) {
              // Prepare update object for MongoDB
              const directMongoDBUpdate: Record<string, any> = {};
              
              // Update only if values exist in the eligibility response
              if (eligibilityInfo.ins_number) {
                directMongoDBUpdate['subscriber.memberId'] = eligibilityInfo.ins_number;
              }
              
              if (eligibilityInfo.ins_name_f) {
                directMongoDBUpdate['subscriber.firstName'] = eligibilityInfo.ins_name_f;
              }
              
              if (eligibilityInfo.ins_name_l) {
                directMongoDBUpdate['subscriber.lastName'] = eligibilityInfo.ins_name_l;
              }
              
              if (eligibilityInfo.ins_addr_1) {
                directMongoDBUpdate['subscriber.address.address1'] = eligibilityInfo.ins_addr_1;
              }
              
              if (eligibilityInfo.ins_city) {
                directMongoDBUpdate['subscriber.address.city'] = eligibilityInfo.ins_city;
              }
              
              if (eligibilityInfo.ins_state) {
                // Convert state abbreviation to full state name
                const fullStateName = getStateNameFromAbbreviation(eligibilityInfo.ins_state);
                directMongoDBUpdate['subscriber.address.state'] = fullStateName;
              }
              
              if (eligibilityInfo.ins_zip) {
                directMongoDBUpdate['subscriber.address.postalCode'] = eligibilityInfo.ins_zip;
              }
              
              // Address fields need special handling to ensure they exist
              if (!insuranceRecord.subscriber.address && Object.keys(directMongoDBUpdate).some(key => key.startsWith('subscriber.address.'))) {
                // Create the address object if it doesn't exist and we have address fields to update
                await Insurance.updateOne(
                  { _id: insuranceRecord._id },
                  { $set: { 'subscriber.address': {} } }
                );
              }
              
              // Only update if we have fields to update
              if (Object.keys(directMongoDBUpdate).length > 0) {
                // Perform the update directly with MongoDB driver
                await Insurance.updateOne(
                  { _id: insuranceRecord._id },
                  { $set: directMongoDBUpdate },
                  { upsert: false }
                );
              }

              // Update client profile with address information from primary insurance only if fields are empty
              try {
                  const userUpdateFields: Record<string, any> = {};
                  
                  // Add all available fields from eligibility data
                  if (eligibilityInfo.ins_addr_1 && !client.streetAddress) {
                    userUpdateFields.streetAddress = eligibilityInfo.ins_addr_1;
                  }
                  
                  if (eligibilityInfo.ins_city && !client.city) {
                    userUpdateFields.city = eligibilityInfo.ins_city;
                  }
                  
                  if (eligibilityInfo.ins_state && !client.state) {
                    // Convert state abbreviation to full state name
                    const fullStateName = getStateNameFromAbbreviation(eligibilityInfo.ins_state);
                    userUpdateFields.state = fullStateName;
                  }
                  
                  if (eligibilityInfo.ins_zip && !client.zipCode) {
                    userUpdateFields.zipCode = eligibilityInfo.ins_zip;
                  }
                  
                  // Only update user if we have fields to update
                  if (Object.keys(userUpdateFields).length > 0) {
                    await User.updateOne(
                      { _id: client._id },
                      { $set: userUpdateFields },
                      { upsert: false }
                    );
                    console.log(`Updated user ${client._id} address information from primary insurance eligibility data`);
                  }
                
              } catch (userUpdateError) {
                console.error("Error updating user profile with address data:", userUpdateError);
                // Continue with the process even if user update fails
              }
            }
          } catch (error) {
            // Log error but continue with the response
            console.error("Error updating insurance with eligibility data:", error);
          }
        }

        const claimEligibilityData = {
          claimEligibilityDetails: eligibilityInfo,
        }

        return res.sendSuccess(claimEligibilityData, "Success");
      } else {
        // Insurance is inactive - handle downgrading client and sending notifications
        try {
          if (client && client.premiumStatus === PremiumStatus.ACTIVE) {
            // 1. Update client's premium status to REVOKED
            await ClientDao.updateClient(client._id, {
              premiumStatus: PremiumStatus.REVOKED,
              premiumMembershipRevokedDate: new Date()
            });
            
            // 2. Get therapist information for the client (if available)
            let therapist = null;
            if (client.primaryTherapist) {
              therapist = await TherapistDao.getUserById(client.primaryTherapist);
            }
            
            // 3. Send email to the therapist if one is assigned
            // if (therapist) {
            //   await EmailService.sendInsuranceInactiveNotificationToTherapist(
            //     therapist.email,
            //     {
            //       therapistName: `${therapist.firstname} ${therapist.lastname}`,
            //       clientName: `${client.firstname} ${client.lastname}`,
            //       errorMessage: claimEligibilityAndActiveData?.finalError || "Insurance verification failed"
            //     }
            //   );
            // }
            
            // // 4. Send email to the client
            // await EmailService.sendInsuranceInactiveNotificationToClient(
            //   client.email,
            //   {
            //     clientName: `${client.firstname} ${client.lastname}`,
            //     errorMessage: claimEligibilityAndActiveData?.finalError || "Insurance verification failed",
            //     loginUrl: "https://mylavni.com/signin/",
            //   }
            // );
            
            AppLogger.info(`Client ${client._id} downgraded from premium due to inactive insurance`);
          }
        } catch (downgradeError) {
          // Log error but still return the original error to the client
          console.error("Error during client downgrade process:", downgradeError);
        }
        
        // Send notification email to admins (<EMAIL>, <EMAIL>)
        try {
          const adminEmails = ["<EMAIL>", "<EMAIL>"];
          const subject = `Insurance eligibility error for client ${client?.firstname || ''} ${client?.lastname || ''}`;
          const body = `Insurance of user <b>${client?.firstname || ''} ${client?.lastname || ''} (${client?.email || ''})</b> has an eligibility issue.<br><br>Reason: <b>${claimEligibilityAndActiveData?.finalError || 'Unknown error'}</b><br><br>Please check and update copayment if necessary.`;
          for (const email of adminEmails) {
            await EmailService.sendEmailToSelectedUsers(email, body, subject);
          }
        } catch (adminMailError) {
          AppLogger.error("Error sending insurance eligibility error email to admins:", adminMailError);
        }
        
        return res.sendError(claimEligibilityAndActiveData?.finalError);
      }

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function checkEligibilityAdmin(req: Request, res: Response, next: NextFunction) {
    const insuranceId = req.params.insuranceId;
    const submitterOrganizationName = req.body.organizationName;
    const subscriber = req.body.subscriber;
    const diagnosisNoteId = req.body.diagnosisNoteId;
    const serviceLines = req.body.serviceLines;
    const claim = req.body.claim;
    if (!mongoose.Types.ObjectId.isValid(insuranceId)) {
      return res.sendError("Invalid insurance Id");
    }

    function generateControlNumber() {
      return Math.floor(********* + Math.random() * *********).toString();
    }

    try {
      let controlNumber = "";
      const insurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceId));
      const currentDate = new Date();
      if (!insurance.controlNumber) {
        controlNumber = generateControlNumber();
        await InsuranceDao.updateInsurancePlan(insuranceId, {
          controlNumber: controlNumber,
        });
      } else {
        controlNumber = insurance?.controlNumber;
      }

      const eligibilityDataSet = await InsuranceDao.getDetailsForClaimStatus(Types.ObjectId(diagnosisNoteId));
      const treatmentHistoryId = await TherapistDao.viewTreatmentHistorysByMeetingId(eligibilityDataSet[0].meetingDetails?._id);
      const diagnosisNote = await VideoCallDao.getDiagnosisNoteByIdWithOutPopulateFunction(Types.ObjectId(diagnosisNoteId));
      const insurancePartner = submitterOrganizationName as keyof typeof partner;

      if (eligibilityDataSet[0].therapistDetails?.claimOpen === false || eligibilityDataSet[0].therapistDetails?.claimOpen != true) {
        return res.sendError("Therapist claim open status blocked. You cannot submit a claim for this user now.");
      }

      let claimEligibilityDetailsOBJ = JSON.stringify(eligibilityDataSet[0]?.clientId?.claimEligibilityDetails);

      let claimEligibilityDetailsData = null;

      if (claimEligibilityDetailsOBJ) {
        claimEligibilityDetailsData = JSON.parse(claimEligibilityDetailsOBJ);
      }


      let eligibility_payload = null;

      if (eligibilityDataSet[0]?.clientInsuranceDetails?.dependent?.memberId) {
        eligibility_payload = {
          controlNumber: "*********",
          tradingPartnerServiceId: partner[insurancePartner],
          provider: {
            organizationName: process.env.LAVNI_ORGANIZATION_NAME,
            npi: process.env.LAVNI_NPI,
          },
          subscriber: {
            memberId: subscriber?.memberId,
            firstName: eligibilityDataSet[0]?.clientId?.firstname,
            lastName: eligibilityDataSet[0]?.clientId?.lastname,
            gender: (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
            dateOfBirth: moment.utc(eligibilityDataSet[0]?.clientId?.dateOfBirth).format("YYYYMMDD"),
          },
          dependent: {
            memberId: eligibilityDataSet[0].clientInsuranceDetails.dependent.memberId,
            firstName: eligibilityDataSet[0].clientInsuranceDetails.dependent.firstName,
            lastName: eligibilityDataSet[0].clientInsuranceDetails.dependent.lastName,
            gender: (eligibilityDataSet[0].clientInsuranceDetails.dependent.gender == "Male" ? "M" : "F"),
            dateOfBirth: moment.utc(eligibilityDataSet[0].clientInsuranceDetails.dependent.dateOfBirth).format("YYYYMMDD"),
          },
          encounter: {
            dateOfService: moment.utc(serviceLines?.serviceDate).utcOffset(-5 * 60).format("YYYYMMDD"),
            serviceTypeCodes: ["98"]
          },
        };
      } else {
        eligibility_payload = {
          controlNumber: "*********",
          tradingPartnerServiceId: partner[insurancePartner],
          provider: {
            organizationName: process.env.LAVNI_ORGANIZATION_NAME,
            npi: process.env.LAVNI_NPI,
          },
          subscriber: {
            memberId: subscriber?.memberId,
            firstName: eligibilityDataSet[0]?.clientId?.firstname,
            lastName: eligibilityDataSet[0]?.clientId?.lastname,
            gender: (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
            dateOfBirth: moment.utc(eligibilityDataSet[0]?.clientId?.dateOfBirth).format("YYYYMMDD"),
          },
          encounter: {
            dateOfService: moment.utc(serviceLines?.serviceDate).utcOffset(-5 * 60).format("YYYYMMDD"),
            serviceTypeCodes: ["98"]
          }
        };
      }

      const hasExpired = await isExpired(insurance?.insuranceAccessToken);

      if (hasExpired) {
        const token: InsuranceToken = {
          client_id: process.env.CHANGE_HEALTHCARE_CLIENT_ID,
          client_secret: process.env.CHANGE_HEALTHCARE_CLIENT_SECRET,
          grant_type: process.env.CHANGE_HEALTHCARE_GRANT_TYPE,
        };

        const newToken = await fetch(process.env.CHANGE_HEALTHCARE_TOKEN_URL, {
          method: "POST",
          body: JSON.stringify(token),
          headers: {
            "Content-Type": "application/json",
          },
        });

        const accessToken: AccessTokenResponse = await newToken.json();

        await InsuranceDao.updateInsurancePlan(insuranceId, {
          insuranceAccessToken: accessToken.access_token,
        });
        const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, accessToken.access_token);

        if (!(claimEligibilityDetails?.errors?.length > 0)) {
          let coPaymentClientAmount: any = eligibilityDataSet[0]?.clientId?.copaymentAmount;

          const identity = [];

          if ('dependents' in claimEligibilityDetails) {
            identity.push('dependents');
          } else {
            identity.push('subscriber');
          }


          const eligibilityData = {
            subscriber: identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0],
            memberId: claimEligibilityDetails.subscriber.memberId,
          }

          let updatedClientEligibility = await ClientDao.updateClientEligibility(eligibilityDataSet[0]?.clientId?._id, eligibilityData);

          AppLogger.info(`.:: Client Eligibility Update, Client Name : ` + eligibilityDataSet[0]?.clientId?.firstname + `.:: eligibilityData : ` + eligibilityData);
          
          let claimEligibilityData;
          
          if (coPaymentClientAmount) {
            claimEligibilityData = {
              claimEligibilityDetails: claimEligibilityDetails,
              coPaymentValue: coPaymentClientAmount
            }
          } else {
            claimEligibilityData = {
              claimEligibilityDetails: claimEligibilityDetails,
              coPaymentValue: 0
            }
          }

          if (!claimEligibilityDetails?.errors) {
            return res.sendSuccess(claimEligibilityData, "Sucess");
          }
        } else {
          let meetingCreatedAt = JSON.stringify(eligibilityDataSet[0].meetingDetails);
          let meetingDate = null;
          if (meetingCreatedAt) {
            meetingDate = JSON.parse(meetingCreatedAt);
          }

          const createdAtDate = new Date(meetingDate?.createdAt);
          const previousNoteDetailsData = await TreatmentHistory.findOne({
            clientId: eligibilityDataSet[0].clientId?._id,
            therapistId: eligibilityDataSet[0].therapistId?._id,
            meetingStartedTime: {
              $gte: new Date(createdAtDate.setUTCHours(0, 0, 0, 0)),
              $lt: new Date(createdAtDate.setUTCHours(23, 59, 59, 999))
            },
            claimStatus: { $in: ['ACTIVEMD', 'ACTIVE', 'IN_PROGRESS', 'PAID', 'UNPAID'] }
          });

          if (previousNoteDetailsData) {
            return res.sendError(`You have already submitted a diagnosis note for session on this day ${moment.utc(createdAtDate).format("MMM DD YYYY")} with ${eligibilityDataSet[0].clientId.firstname}.`);
          } else {
            let claimEligibilityMDDetails = JSON.stringify(eligibilityDataSet[0]?.clientId?.claimEligibilityMdDetails);
            let claimEligibilityMDData = null;
            if (claimEligibilityMDDetails) {
              claimEligibilityMDData = JSON.parse(claimEligibilityMDDetails);
            }

            if (claimEligibilityMDData && claimEligibilityMDData?.ins_number?.length > 0) {
              if (partner[insurancePartner]) {

                const extractedInfo = {
                  ins_name_f: claimEligibilityMDData.pat_name_f || claimEligibilityMDData.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                  ins_name_l: claimEligibilityMDData.pat_name_l || claimEligibilityMDData.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                  ins_city: claimEligibilityMDData.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                  ins_state: claimEligibilityMDData.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                  ins_zip: claimEligibilityMDData.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                  ins_dob: claimEligibilityMDData.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                  ins_sex: claimEligibilityMDData.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                  ins_addr_1: claimEligibilityMDData.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                  ins_number: claimEligibilityMDData.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                };

                let place_of_service;
                let modifier;

                if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                  place_of_service = "11";
                } else {
                  place_of_service = "10";
                  modifier = "95"
                }

                const randomKey = generateRandomKey(20);


                let claimInformationMD: any = {
                  claim_form: 1500,
                  payer_name: Insurance_company[insurancePartner],
                  accept_assign: 'Y',
                  employment_related: 'N',
                  ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                  ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                  ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                  ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                  ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                  ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                  ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                  ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                  ins_number: extractedInfo.ins_number || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                  bill_taxonomy: '251S00000X',
                  place_of_service_1: place_of_service,
                  prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                  prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                  prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                  prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                  pcn: String(randomKey),
                  charge_1: 180,
                  pat_rel: '18',
                  payerid: Tradingpartner_ServiceId[insurancePartner],
                  total_charge: 180,
                  claimNumber: diagnosisNote?.encounterID || '87654321',
                  proc_code_1: diagnosisNote?.cptCode,
                  mod1_1: modifier,
                  diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                  diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                  from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                  bill_name: 'Lavni Inc',
                  bill_addr_1: '804 South Garnett St.',
                  bill_addr_2: '',
                  bill_city: 'Henderson',
                  bill_state: 'NC',
                  bill_zip: '27536',
                  bill_npi: '**********',
                  bill_id: '',
                  bill_phone: '**********',
                  bill_taxid: '*********',
                  bill_taxid_type: 'E',
                  diag_ref_1: "A",
                  units_1: 1,
                  pat_name_f: extractedInfo.ins_name_f,
                  pat_name_l: extractedInfo.ins_name_l,
                  pat_addr_1: extractedInfo.ins_addr_1,
                  pat_city: extractedInfo.ins_city,
                  pat_state: extractedInfo.ins_state,
                  pat_zip: extractedInfo.ins_zip,
                  pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                  pat_sex: extractedInfo.ins_sex,
                };

                if (treatmentHistoryId?.meetingStartedTime) {
                  if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                    return res.sendError("Claims cannot be submitted on the same day the meeting is created.");
                  } else {
                    const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                    const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                    let coPaymentClientAmount: any = eligibilityDataSet[0]?.clientId?.copaymentAmount
                    let claimEligibilityData;

                    if (coPaymentClientAmount) {
                      claimEligibilityData = {
                        claimEligibilityDetails: claimEligibilityDetails,
                        coPaymentValue: coPaymentClientAmount,
                        eligibilityType: "claimMD",
                        copaymentStatus: eligibilityDataSet[0]?.meetingDetails?.copayment?.status
                      }
                    } else {
                      claimEligibilityData = {
                        claimEligibilityDetails: claimEligibilityDetails,
                        coPaymentValue: 0,
                        eligibilityType: "claimMD",
                        copaymentStatus: eligibilityDataSet[0]?.meetingDetails?.copayment?.status
                      }
                    }

                    if (claimUploadCSV?.error) {
                      let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                      return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                    } else {
                      let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                      return res.sendSuccess(claimEligibilityData, "Success");
                    }
                  }
                }
              }
            } else {
              if (partner[insurancePartner]) {
                let eligibility_payload_md = null;
                if (eligibilityDataSet[0].clientInsuranceDetails) {
                  eligibility_payload_md = {
                    "AccountKey": process.env.CLAIM_MD_ACCOUNT_KEY,
                    "ins_name_f": eligibilityDataSet[0].clientId.firstname,
                    "ins_name_l": eligibilityDataSet[0].clientId.lastname,
                    "payerid": Tradingpartner_ServiceId[insurancePartner],
                    "pat_rel": "18",
                    "fdos": moment.utc(treatmentHistoryId?.meetingStartedTime).format('YYYYMMDD'),
                    "ins_dob": moment.utc(eligibilityDataSet[0].clientId?.dateOfBirth).format('YYYYMMDD'),
                    "ins_sex": (eligibilityDataSet[0].clientId?.gender == "Male" ? "M" : "F"),
                    "ins_number": subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                    "prov_npi": process.env.LAVNI_NPI,
                  }
                }
                
                const claimEligibilityAndActiveData = await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, eligibilityDataSet[0].clientId._id);

                if (claimEligibilityAndActiveData?.finalStatus == "ACTIVE") {

                  const eligibilityInfo = claimEligibilityAndActiveData?.eligibilityInfoData;

                  const extractedInfo = {
                    ins_name_f: eligibilityInfo.pat_name_f || eligibilityInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                    ins_name_l: eligibilityInfo.pat_name_l || eligibilityInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                    ins_city: eligibilityInfo.ins_city || eligibilityDataSet[0].clientId.ins_city || 'Unknown',
                    ins_state: eligibilityInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                    ins_zip: eligibilityInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                    ins_dob: eligibilityInfo.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                    ins_sex: eligibilityInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                    ins_addr_1: eligibilityInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                    ins_number: eligibilityInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                  };

                  let place_of_service;
                  let modifier;

                  if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                    place_of_service = "11";
                  } else {
                    place_of_service = "10";
                    modifier = "95"
                  }

                  const randomKey = generateRandomKey(20);

                  let claimInformationMD: any = {
                    claim_form: 1500,
                    payer_name: Insurance_company[insurancePartner],
                    accept_assign: 'Y',
                    employment_related: 'N',
                    ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                    ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                    ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                    ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                    ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                    ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                    ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                    ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                    ins_number: extractedInfo.ins_number || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                    bill_taxonomy: '251S00000X',
                    place_of_service_1: place_of_service,
                    prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                    prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                    prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                    prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                    pcn: String(randomKey),
                    charge_1: 180,
                    pat_rel: '18',
                    payerid: Tradingpartner_ServiceId[insurancePartner],
                    total_charge: 180,
                    claimNumber: diagnosisNote?.encounterID || '87654321',
                    proc_code_1: diagnosisNote?.cptCode,
                    mod1_1: modifier,
                    diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                    diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                    from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                    bill_name: 'Lavni Inc',
                    bill_addr_1: '804 South Garnett St.',
                    bill_addr_2: '',
                    bill_city: 'Henderson',
                    bill_state: 'NC',
                    bill_zip: '27536',
                    bill_npi: '**********',
                    bill_id: '',
                    bill_phone: '**********',
                    bill_taxid: '*********',
                    bill_taxid_type: 'E',
                    diag_ref_1: "A",
                    units_1: 1,
                    pat_name_f: extractedInfo.ins_name_f,
                    pat_name_l: extractedInfo.ins_name_l,
                    pat_addr_1: extractedInfo.ins_addr_1,
                    pat_city: extractedInfo.ins_city,
                    pat_state: extractedInfo.ins_state,
                    pat_zip: extractedInfo.ins_zip,
                    pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                    pat_sex: extractedInfo.ins_sex,
                  };

                  if (treatmentHistoryId?.meetingStartedTime) {
                    if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                      return res.sendError("Claims cannot be submitted on the same day the meeting is created.");
                    } else {
                      const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                      const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                      let coPaymentClientAmount: any = eligibilityDataSet[0]?.clientId?.copaymentAmount
                      let claimEligibilityData;

                      if (coPaymentClientAmount) {
                        claimEligibilityData = {
                          claimEligibilityDetails: claimEligibilityDetails,
                          coPaymentValue: coPaymentClientAmount,
                          eligibilityType: "claimMD",
                          copaymentStatus: eligibilityDataSet[0]?.meetingDetails?.copayment?.status
                        }
                      } else {
                        claimEligibilityData = {
                          claimEligibilityDetails: claimEligibilityDetails,
                          coPaymentValue: 0,
                          eligibilityType: "claimMD",
                          copaymentStatus: eligibilityDataSet[0]?.meetingDetails?.copayment?.status
                        }
                      }
                      if (claimUploadCSV?.error) {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                        return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                      } else {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                        return res.sendSuccess(claimEligibilityData, "Success");
                      }
                    }
                  }
                } else {
                  await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, claimEligibilityAndActiveData?.finalError);
                  return res.sendError(claimEligibilityAndActiveData?.finalError);
                }
              }
            }
          }
        }
      }

      if (!hasExpired) {
        const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, insurance?.insuranceAccessToken);
        if (!(claimEligibilityDetails?.errors?.length > 0)) {
          let coPaymentClientAmount: any = eligibilityDataSet[0]?.clientId?.copaymentAmount

          const identity = [];

          if ('dependents' in claimEligibilityDetails) {
            identity.push('dependents');
          } else {
            identity.push('subscriber');
          }

          const eligibilityData = {
            subscriber: identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0],
            memberId: claimEligibilityDetails.subscriber.memberId,
          }

          let updatedClientEligibility = await ClientDao.updateClientEligibility(eligibilityDataSet[0]?.clientId?._id, eligibilityData);
          
          AppLogger.info(`.:: Client Eligibility Update, Client Name : ` + eligibilityDataSet[0]?.clientId?.firstname + `.:: eligibilityData : ` + eligibilityData);

          let claimEligibilityData;
          if (coPaymentClientAmount) {
            claimEligibilityData = {
              claimEligibilityDetails: claimEligibilityDetails,
              coPaymentValue: coPaymentClientAmount
            }
          } else {
            claimEligibilityData = {
              claimEligibilityDetails: claimEligibilityDetails,
              coPaymentValue: 0
            }
          }

          if (!claimEligibilityDetails?.errors) {
            return res.sendSuccess(claimEligibilityData, "Sucess");
          }
        } else {
          let meetingCreatedAt = JSON.stringify(eligibilityDataSet[0].meetingDetails);
          let meetingDate = null;

          if (meetingCreatedAt) {
            meetingDate = JSON.parse(meetingCreatedAt);
          }

          const createdAtDate = new Date(meetingDate?.createdAt);

          const previousNoteDetailsData = await TreatmentHistory.findOne({
            clientId: eligibilityDataSet[0].clientId?._id,
            therapistId: eligibilityDataSet[0].therapistId?._id,
            meetingStartedTime: {
              $gte: new Date(createdAtDate.setUTCHours(0, 0, 0, 0)),
              $lt: new Date(createdAtDate.setUTCHours(23, 59, 59, 999))
            },
            claimStatus: { $in: ['ACTIVEMD', 'IN_PROGRESS', 'PAID'] }
          });

          if (previousNoteDetailsData) {
            return res.sendError(`You have already submitted a diagnosis note for session on this day ${moment.utc(createdAtDate).format("MMM DD YYYY")} with ${eligibilityDataSet[0].clientId.firstname}.`);
          } else {
            let claimEligibilityMDDetails = JSON.stringify(eligibilityDataSet[0]?.clientId?.claimEligibilityMdDetails);
            let claimEligibilityMDData = null;
            if (claimEligibilityMDDetails) {
              claimEligibilityMDData = JSON.parse(claimEligibilityMDDetails);
            }

            if (claimEligibilityMDData && claimEligibilityMDData?.ins_number?.length > 0) {
              if (partner[insurancePartner]) {
                const extractedInfo = {
                  ins_name_f: claimEligibilityMDData.pat_name_f || claimEligibilityMDData.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                  ins_name_l: claimEligibilityMDData.pat_name_l || claimEligibilityMDData.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                  ins_city: claimEligibilityMDData.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                  ins_state: claimEligibilityMDData.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                  ins_zip: claimEligibilityMDData.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                  ins_dob: claimEligibilityMDData.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                  ins_sex: claimEligibilityMDData.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                  ins_addr_1: claimEligibilityMDData.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                  ins_number: claimEligibilityMDData.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                };

                let place_of_service;
                let modifier;

                if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                  place_of_service = "11";
                } else {
                  place_of_service = "10";
                  modifier = "95"
                }

                const randomKey = generateRandomKey(20);

                let claimInformationMD: any = {
                  claim_form: 1500,
                  payer_name: Insurance_company[insurancePartner],
                  accept_assign: 'Y',
                  employment_related: 'N',
                  ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                  ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                  ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                  ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                  ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                  ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                  ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                  ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                  ins_number: extractedInfo.ins_number || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                  bill_taxonomy: '251S00000X',
                  place_of_service_1: place_of_service,
                  prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                  prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                  prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                  prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                  pcn: String(randomKey),
                  charge_1: 180,
                  pat_rel: '18',
                  payerid: Tradingpartner_ServiceId[insurancePartner],
                  total_charge: 180,
                  claimNumber: diagnosisNote?.encounterID || '87654321',
                  proc_code_1: diagnosisNote?.cptCode,
                  mod1_1: modifier,
                  diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                  diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                  from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                  bill_name: 'Lavni Inc',
                  bill_addr_1: '804 South Garnett St.',
                  bill_addr_2: '',
                  bill_city: 'Henderson',
                  bill_state: 'NC',
                  bill_zip: '27536',
                  bill_npi: '**********',
                  bill_id: '',
                  bill_phone: '**********',
                  bill_taxid: '*********',
                  bill_taxid_type: 'E',
                  diag_ref_1: "A",
                  units_1: 1,
                  pat_name_f: extractedInfo.ins_name_f,
                  pat_name_l: extractedInfo.ins_name_l,
                  pat_addr_1: extractedInfo.ins_addr_1,
                  pat_city: extractedInfo.ins_city,
                  pat_state: extractedInfo.ins_state,
                  pat_zip: extractedInfo.ins_zip,
                  pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                  pat_sex: extractedInfo.ins_sex,
                };
                if (treatmentHistoryId?.meetingStartedTime) {
                  if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                    return res.sendError("Claims cannot be submitted on the same day the meeting is created.");
                  } else {
                    const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                    const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                    let coPaymentClientAmount: any = eligibilityDataSet[0]?.clientId?.copaymentAmount
                    let claimEligibilityData;

                    if (coPaymentClientAmount) {
                      claimEligibilityData = {
                        claimEligibilityDetails: claimEligibilityDetails,
                        coPaymentValue: coPaymentClientAmount,
                        eligibilityType: "claimMD",
                        copaymentStatus: eligibilityDataSet[0]?.meetingDetails?.copayment?.status
                      }
                    } else {
                      claimEligibilityData = {
                        claimEligibilityDetails: claimEligibilityDetails,
                        coPaymentValue: 0,
                        eligibilityType: "claimMD",
                        copaymentStatus: eligibilityDataSet[0]?.meetingDetails?.copayment?.status
                      }
                    }
                    if (claimUploadCSV?.error) {
                      let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                      return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                    } else {
                      let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                      return res.sendSuccess(claimEligibilityData, "Success");
                    }
                  }
                }
              }
            } else {
              if (partner[insurancePartner]) {
                let eligibility_payload_md = null;

                if (eligibilityDataSet[0].clientInsuranceDetails) {
                  eligibility_payload_md = {
                    "AccountKey": process.env.CLAIM_MD_ACCOUNT_KEY,
                    "ins_name_f": eligibilityDataSet[0].clientId.firstname,
                    "ins_name_l": eligibilityDataSet[0].clientId.lastname,
                    "payerid": Tradingpartner_ServiceId[insurancePartner],
                    "pat_rel": "18",
                    "fdos": moment.utc(treatmentHistoryId?.meetingStartedTime).format('YYYYMMDD'),
                    "ins_dob": moment.utc(eligibilityDataSet[0].clientId?.dateOfBirth).format('YYYYMMDD'),
                    "ins_sex": (eligibilityDataSet[0].clientId?.gender == "Male" ? "M" : "F"),
                    "ins_number": subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                    "prov_npi": process.env.LAVNI_NPI,
                  }
                }
                
                const claimEligibilityAndActiveData = await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, eligibilityDataSet[0].clientId._id);

                if (claimEligibilityAndActiveData?.finalStatus == "ACTIVE") {

                  const eligibilityInfo = claimEligibilityAndActiveData?.eligibilityInfoData;

                  const extractedInfo = {
                    ins_name_f: eligibilityInfo.pat_name_f || eligibilityInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                    ins_name_l: eligibilityInfo.pat_name_l || eligibilityInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                    ins_city: eligibilityInfo.ins_city || eligibilityDataSet[0].clientId.ins_city || 'Unknown',
                    ins_state: eligibilityInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                    ins_zip: eligibilityInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                    ins_dob: eligibilityInfo.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                    ins_sex: eligibilityInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                    ins_addr_1: eligibilityInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                    ins_number: eligibilityInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                  };

                  let place_of_service;
                  let modifier;

                  if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                    place_of_service = "11";
                  } else {
                    place_of_service = "10";
                    modifier = "95"
                  }

                  const randomKey = generateRandomKey(20);

                  let claimInformationMD: any = {
                    claim_form: 1500,
                    payer_name: Insurance_company[insurancePartner],
                    accept_assign: 'Y',
                    employment_related: 'N',
                    ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                    ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                    ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                    ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                    ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                    ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                    ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                    ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                    ins_number: extractedInfo.ins_number || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                    bill_taxonomy: '251S00000X',
                    place_of_service_1: place_of_service,
                    prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                    prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                    prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                    prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                    pcn: String(randomKey),
                    charge_1: 180,
                    pat_rel: '18',
                    payerid: Tradingpartner_ServiceId[insurancePartner],
                    total_charge: 180,
                    claimNumber: diagnosisNote?.encounterID || '87654321',
                    proc_code_1: diagnosisNote?.cptCode,
                    mod1_1: modifier,
                    diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                    diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                    from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                    bill_name: 'Lavni Inc',
                    bill_addr_1: '804 South Garnett St.',
                    bill_addr_2: '',
                    bill_city: 'Henderson',
                    bill_state: 'NC',
                    bill_zip: '27536',
                    bill_npi: '**********',
                    bill_id: '',
                    bill_phone: '**********',
                    bill_taxid: '*********',
                    bill_taxid_type: 'E',
                    diag_ref_1: "A",
                    units_1: 1,
                    pat_name_f: extractedInfo.ins_name_f,
                    pat_name_l: extractedInfo.ins_name_l,
                    pat_addr_1: extractedInfo.ins_addr_1,
                    pat_city: extractedInfo.ins_city,
                    pat_state: extractedInfo.ins_state,
                    pat_zip: extractedInfo.ins_zip,
                    pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                    pat_sex: extractedInfo.ins_sex,
                  };
                  
                  if (treatmentHistoryId?.meetingStartedTime) {
                    if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                      return res.sendError("Claims cannot be submitted on the same day the meeting is created.");
                    } else {
                      const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                      const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);
                      let coPaymentClientAmount: any = eligibilityDataSet[0]?.clientId?.copaymentAmount
                      let claimEligibilityData;

                      if (coPaymentClientAmount) {
                        claimEligibilityData = {
                          claimEligibilityDetails: claimEligibilityDetails,
                          coPaymentValue: coPaymentClientAmount,
                          eligibilityType: "claimMD",
                          copaymentStatus: eligibilityDataSet[0]?.meetingDetails?.copayment?.status
                        }
                      } else {
                        claimEligibilityData = {
                          claimEligibilityDetails: claimEligibilityDetails,
                          coPaymentValue: 0,
                          eligibilityType: "claimMD",
                          copaymentStatus: eligibilityDataSet[0]?.meetingDetails?.copayment?.status
                        }
                      }
                      if (claimUploadCSV?.error) {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                        return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                      } else {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                        return res.sendSuccess(claimEligibilityData, "Success");
                      }
                    }
                  }
                } else {
                  await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, claimEligibilityAndActiveData?.finalError);
                  return res.sendError(claimEligibilityAndActiveData?.finalError);
                }

              }
            }
          }
        }
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function checkoutPaymentLink(req: Request, res: Response, next: NextFunction) {
    const coValue = req.body.coValue;
    const meetingId = req.body.meetingId;
    try {
      const paymentLink = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        mode: 'payment',
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: 'Co-payment',
              },
              unit_amount: parseFloat(coValue) * 100,
            },
            quantity: 1,
          },
        ],
        success_url: `https://mylavni.com/payment-success/${meetingId}/${coValue}`,
        cancel_url: 'https://mylavni.com/payment-failed',
      });
      if (paymentLink.url) {
        res.sendSuccess(paymentLink.url, "Sucess");
      }
    } catch (error) {
      AppLogger.info(`Stripe charge error when co-payment submit:`, error);
    }

  }


  export async function UpdatePaymentWithStripeLink(req: Request, res: Response, next: NextFunction) {
    const coValue = req.body.coValue;
    const meetingId = req.body.meetingId;
    try {
      const updatedReview = await AdminDao.updateMeetingStatusById(meetingId, {
        copayment: {
          amount: coValue,
          status: "PAID",
          details: `PAID`,
        },
      });
      if (updatedReview) {
        res.sendSuccess("Sucess");
      } else {
        res.sendError("Failed to pay co payment amount.");
      }
    } catch (error) {
      AppLogger.info(`Stripe charge error when co-payment submit:`, error);
    }

  }

  export async function checkCoPaymentStatus(req: Request, res: Response, next: NextFunction) {
    const meetingId = req.body.meetingId;
    try {
      const updatedReview = await AdminDao.getMeetingStatusById(meetingId);
      if (updatedReview) {
        res.sendSuccess(updatedReview, "Sucess");
      } else {
        res.sendError("Failed to pay co payment amount.");
      }
    } catch (error) {
      AppLogger.info(`Stripe charge error when co-payment submit:`, error);
    }

  }

  export async function submitProfessionalClaims(req: Request, res: Response, next: NextFunction) {
    const insuranceId = req.params.insuranceId;
    const diagnosisNoteId = req.body.diagnosisNoteId;
    const submitterOrganizationName = req.body.organizationName;
    const submitterPhoneNumber = req.body.submitter.phoneNumber;

    const serviceLines = req.body.serviceLines;

    const claim = req.body.claim;

    const subscriber = req.body.subscriber;
    const renderingProviderNpi = req.body.renderingProvider.npi;
    const renderingProviderFirstName = req.body.renderingProvider.firstName;
    const renderingProviderLastName = req.body.renderingProvider.lastName;
    const renderingProviderTaxonomyCode = req.body.renderingProvider.taxonomyCode;

    if (!mongoose.Types.ObjectId.isValid(insuranceId)) {
      return res.sendError("Invalid insurance Id");
    }

    function generateControlNumber() {
      return Math.floor(********* + Math.random() * *********).toString();
    }

    try {
      let controlNumber = "";
      const insurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceId));

      if (!insurance.controlNumber) {
        controlNumber = generateControlNumber();
        await InsuranceDao.updateInsurancePlan(insuranceId, {
          controlNumber: controlNumber,
        });
      } else {
        controlNumber = insurance?.controlNumber;
      }

      const eligibilityDataSet = await InsuranceDao.getDetailsForClaimStatus(Types.ObjectId(diagnosisNoteId));
      const treatmentHistoryId = await TherapistDao.viewTreatmentHistorysByMeetingId(eligibilityDataSet[0].meetingDetails?._id);
      const transactionData = await TherapistDao.viewTransactionsByMeetingId(eligibilityDataSet[0].meetingDetails?._id);

      if (eligibilityDataSet[0].therapistDetails?.claimOpen === false || eligibilityDataSet[0].therapistDetails?.claimOpen != true) {
        return res.sendError("Therapist claim open status blocked. You cannot submit a claim for this user now.");
      }

      const insurancePartner = submitterOrganizationName as keyof typeof partner;

      if (partner[insurancePartner]) {
        let eligibility_payload = null;

        if (eligibilityDataSet[0]?.clientInsuranceDetails?.dependent?.memberId) {
          eligibility_payload = {
            controlNumber: "*********",
            tradingPartnerServiceId: partner[insurancePartner],
            provider: {
              organizationName: process.env.LAVNI_ORGANIZATION_NAME,
              npi: process.env.LAVNI_NPI,
            },
            subscriber: {
              memberId: subscriber?.memberId,
              firstName: eligibilityDataSet[0]?.clientId?.firstname,
              lastName: eligibilityDataSet[0]?.clientId?.lastname,
              gender: (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
              dateOfBirth: moment.utc(eligibilityDataSet[0]?.clientId?.dateOfBirth).format("YYYYMMDD"),
            },
            dependent: {
              memberId: eligibilityDataSet[0].clientInsuranceDetails.dependent.memberId,
              firstName: eligibilityDataSet[0].clientInsuranceDetails.dependent.firstName,
              lastName: eligibilityDataSet[0].clientInsuranceDetails.dependent.lastName,
              gender: (eligibilityDataSet[0].clientInsuranceDetails.dependent.gender == "Male" ? "M" : "F"),
              dateOfBirth: moment.utc(eligibilityDataSet[0].clientInsuranceDetails.dependent.dateOfBirth).format("YYYYMMDD"),
            },
            encounter: {
              dateOfService: moment.utc(serviceLines?.serviceDate).utcOffset(-5 * 60).format("YYYYMMDD"),
              serviceTypeCodes: ["98"]
            },
          };
        } else {
          eligibility_payload = {
            controlNumber: "*********",
            tradingPartnerServiceId: partner[insurancePartner],
            provider: {
              organizationName: process.env.LAVNI_ORGANIZATION_NAME,
              npi: process.env.LAVNI_NPI,
            },
            subscriber: {
              memberId: subscriber?.memberId,
              firstName: eligibilityDataSet[0]?.clientId?.firstname,
              lastName: eligibilityDataSet[0]?.clientId?.lastname,
              gender: (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
              dateOfBirth: moment.utc(eligibilityDataSet[0]?.clientId?.dateOfBirth).format("YYYYMMDD"),
            },
            encounter: {
              dateOfService: moment.utc(serviceLines?.serviceDate).utcOffset(-5 * 60).format("YYYYMMDD"),
              serviceTypeCodes: ["98"]
            }
          };
        }

        const diagnosisNote = await VideoCallDao.getDiagnosisNoteByIdWithOutPopulateFunction(Types.ObjectId(diagnosisNoteId));

        const organizationName = submitterOrganizationName as keyof typeof Tradingpartner_ServiceId;

        const insuranceCompany = submitterOrganizationName as keyof typeof Insurance_company;

        const hasExpired = await isExpired(insurance?.insuranceAccessToken);

        if (hasExpired) {
          const token: InsuranceToken = {
            client_id: process.env.CHANGE_HEALTHCARE_CLIENT_ID,
            client_secret: process.env.CHANGE_HEALTHCARE_CLIENT_SECRET,
            grant_type: process.env.CHANGE_HEALTHCARE_GRANT_TYPE,
          };

          const newToken = await fetch(process.env.CHANGE_HEALTHCARE_TOKEN_URL, {
            method: "POST",
            body: JSON.stringify(token),
            headers: {
              "Content-Type": "application/json",
            },
          });

          const accessToken: AccessTokenResponse = await newToken.json();

          await InsuranceDao.updateInsurancePlan(insuranceId, {
            insuranceAccessToken: accessToken.access_token,
          });


          let claimEligibilityDetailsOBJ = JSON.stringify(eligibilityDataSet[0]?.clientId?.claimEligibilityDetails);
          let claimEligibilityDetailsData = null;
          if (claimEligibilityDetailsOBJ) {
            claimEligibilityDetailsData = JSON.parse(claimEligibilityDetailsOBJ);
          }

          if (claimEligibilityDetailsData && claimEligibilityDetailsData?.memberId?.length > 0) {
            const updatedReview = await AdminDao.getMeetingStatusById(eligibilityDataSet[0].meetingDetails._id);
            let meetingCreatedAt = JSON.stringify(eligibilityDataSet[0].meetingDetails);

            let meetingDate = null;

            if (meetingCreatedAt) {
              meetingDate = JSON.parse(meetingCreatedAt);
            }

            const createdAtDate = new Date(meetingDate?.createdAt);
            const previousNoteDetailsData = await TreatmentHistory.findOne({
              clientId: eligibilityDataSet[0].clientId?._id,
              therapistId: eligibilityDataSet[0].therapistId?._id,
              meetingStartedTime: {
                $gte: new Date(createdAtDate.setUTCHours(0, 0, 0, 0)),
                $lt: new Date(createdAtDate.setUTCHours(23, 59, 59, 999))
              },
              claimStatus: { $in: ['ACTIVEMD', 'ACTIVE', 'IN_PROGRESS', 'PAID', 'UNPAID'] }
            });


            if (previousNoteDetailsData) {
              return res.sendError(`You have already submitted a diagnosis note for session on this day ${moment.utc(createdAtDate).format("MMM DD YYYY")} with ${eligibilityDataSet[0].clientId.firstname}.`);
            } else {
              if (partner[insurancePartner]) {
                const extractedInfo = {
                  ins_name_f: claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                  ins_name_l: claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                  ins_city: claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city || 'Unknown',
                  ins_state: claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                  ins_zip: claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                  ins_dob: moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                  ins_sex: claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                  ins_addr_1: claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                };

                let place_of_service;
                let modifier;

                if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                  place_of_service = "11";
                } else {
                  place_of_service = "10";
                  modifier = "95"
                }

                const randomKey = generateRandomKey(20);

                let claimInformationMD: any = {
                  claim_form: 1500,
                  payer_name: Insurance_company[insurancePartner],
                  accept_assign: 'Y',
                  employment_related: 'N',
                  ins_name_f: extractedInfo.ins_name_f || claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname,
                  ins_name_l: extractedInfo.ins_name_l || claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname,
                  ins_addr_1: extractedInfo.ins_addr_1 || claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress,
                  ins_city: extractedInfo.ins_city || claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city,
                  ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                  ins_sex: extractedInfo.ins_sex || claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                  ins_state: extractedInfo.ins_state || claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                  ins_zip: extractedInfo.ins_zip || claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode,
                  ins_number: claimEligibilityDetailsData?.subscriber?.memberId || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                  bill_taxonomy: '251S00000X',
                  place_of_service_1: place_of_service,
                  prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                  prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                  prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                  prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                  pcn: String(randomKey),
                  charge_1: 180,
                  pat_rel: '18',
                  payerid: Tradingpartner_ServiceId[insurancePartner],
                  total_charge: 180,
                  claimNumber: diagnosisNote?.encounterID || '87654321',
                  proc_code_1: diagnosisNote?.cptCode,
                  mod1_1: modifier,
                  diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                  diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                  from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                  bill_name: 'Lavni Inc',
                  bill_addr_1: '804 South Garnett St.',
                  bill_addr_2: '',
                  bill_city: 'Henderson',
                  bill_state: 'NC',
                  bill_zip: '27536',
                  bill_npi: '**********',
                  bill_id: '',
                  bill_phone: '**********',
                  bill_taxid: '*********',
                  bill_taxid_type: 'E',
                  diag_ref_1: "A",
                  units_1: 1,
                  pat_name_f: extractedInfo.ins_name_f,
                  pat_name_l: extractedInfo.ins_name_l,
                  pat_addr_1: extractedInfo.ins_addr_1,
                  pat_city: extractedInfo.ins_city,
                  pat_state: extractedInfo.ins_state,
                  pat_zip: extractedInfo.ins_zip,
                  pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                  pat_sex: extractedInfo.ins_sex,
                };
                if (treatmentHistoryId?.meetingStartedTime) {
                  if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                    const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                    await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                    if (saveClaimMd) {
                      return res.sendSuccess(updatedReview, "Success");
                    }
                  } else {
                    const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                    const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                    if (claimUploadCSV?.error) {
                      AppLogger.error('claimUploadCSV 001', claimUploadCSV?.error?.error_mesg);
                      let claimInformation: any = {
                        claimFilingCode: "CI",
                        patientControlNumber: diagnosisNote?.patientID,
                        claimChargeAmount: claim.claimChargeAmount,
                        placeOfServiceCode: "10",
                        claimFrequencyCode: "1",
                        signatureIndicator: "Y",
                        planParticipationCode: "A",
                        benefitsAssignmentCertificationIndicator: "Y",
                        releaseInformationCode: "Y",
                        claimSupplementalInformation: {
                          repricedClaimNumber: "00001",
                          claimNumber: diagnosisNote?.encounterID,
                        },
                        serviceFacilityLocation: {
                          organizationName: "Lavni Inc",
                          address: {
                            address1: "804 South Garnett st.",
                            city: "Henderson",
                            state: "NC",
                            postalCode: "27536",
                          },
                        },
                        serviceLines: [
                          {
                            serviceDate: moment(serviceLines?.serviceDate).utcOffset(-5 * 60).format("YYYYMMDD"),
                            professionalService: {
                              procedureIdentifier: "HC",
                              lineItemChargeAmount: serviceLines?.professionalService?.lineItemChargeAmount,
                              procedureCode: serviceLines?.professionalService?.procedureCode,
                              measurementUnit: "UN",
                              serviceUnitCount: "1",
                              placeOfServiceCode: place_of_service,
                              description: 'mental health',
                              compositeDiagnosisCodePointers: { "diagnosisCodePointers": ["1"] },
                            },
                          },
                        ],
                      };

                      if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                        claimInformation.serviceLines[0].professionalService.procedureModifiers = [""];
                      } else {
                        claimInformation.serviceLines[0].professionalService.procedureModifiers = ["95"];
                      }


                      if (claim?.secondaryDiagnosisCodes && claim?.secondaryDiagnosisCodes.length > 0) {
                        claimInformation.healthCareCodeInformation = [
                          {
                            diagnosisTypeCode: "ABK",
                            diagnosisCode: claim?.diagnosisCode.replace(/\./g, ''),
                          },
                          {
                            diagnosisTypeCode: "ABF",
                            diagnosisCode: claim?.secondaryDiagnosisCodes.replace(/\./g, '')
                          }
                        ]
                      } else {
                        if (claim?.diagnosisCode && claim?.diagnosisCode.length > 0) {
                          claimInformation.healthCareCodeInformation = [
                            {
                              diagnosisTypeCode: "ABK",
                              diagnosisCode: claim?.diagnosisCode.replace(/\./g, ''),
                            }
                          ];
                        } else {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Empty secondaryDiagnosisICDcodes or diagnosisICDcodes");
                        }
                      }

                      const identity = [];
                      if ('dependents' in claimEligibilityDetailsData) {
                        identity.push('dependents');
                      } else {
                        identity.push('subscriber');
                      }
                      const sub = identity[0] === 'subscriber' ? claimEligibilityDetailsData.subscriber : claimEligibilityDetailsData.dependents[0];
                      const memberId = claimEligibilityDetailsData.subscriber.memberId;
                      let subUpdate;


                      let therapistInformation: any = {}

                      if (renderingProviderFirstName == "Brittany" && renderingProviderLastName == "Schopler") {
                        therapistInformation = {
                          providerType: "RenderingProvider",
                          npi: renderingProviderNpi,
                          firstName: "Laura",
                          lastName: "Valentine",
                          taxonomyCode: renderingProviderTaxonomyCode,
                        }
                      } else {
                        therapistInformation = {
                          providerType: "RenderingProvider",
                          npi: renderingProviderNpi,
                          firstName: renderingProviderFirstName,
                          lastName: renderingProviderLastName,
                          taxonomyCode: renderingProviderTaxonomyCode,
                        }
                      }

                      try {
                        subUpdate = {
                          memberId: memberId,
                          paymentResponsibilityLevelCode: 'P',
                          firstName: sub.firstName,
                          lastName: sub.lastName,
                          gender: sub.gender,
                          dateOfBirth: sub.dateOfBirth,
                          address: sub.address,
                        };
                      } catch (error) {
                        subUpdate = {
                          memberId: memberId,
                          paymentResponsibilityLevelCode: 'P',
                          firstName: sub.firstName,
                          lastName: sub.lastName,
                          gender: sub.gender,
                          dateOfBirth: sub.dateOfBirth,
                          address: sub.address,
                        };
                      }

                      const testClaimData = {
                        controlNumber: "*********",
                        tradingPartnerServiceId: Tradingpartner_ServiceId[organizationName],
                        submitter: {
                          organizationName: "Lavni Inc",
                          contactInformation: {
                            name: "Lavni Inc",
                            phoneNumber: "**********",
                          },
                        },
                        receiver: {
                          organizationName: Insurance_company[insuranceCompany],
                        },
                        subscriber: subUpdate,
                        providers: [
                          {
                            providerType: "BillingProvider",
                            npi: "**********",
                            employerId: "*********",
                            taxonomyCode: '251S00000X',
                            organizationName: "Lavni Inc",
                            address: {
                              address1: "804 South Garnett St.",
                              city: "Henderson",
                              state: "NC",
                              postalCode: "*********"
                            },
                            contactInformation: {
                              name: "Angela Arrington",
                              phoneNumber: "**********"
                            },
                          },
                          therapistInformation
                        ],
                        claimInformation: claimInformation
                      };
                    } else {
                      let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                      return res.sendSuccess(updatedReview, "Success");
                    }
                  }
                }
              }
            }
          } else {
            const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, accessToken.access_token);

            if (!(claimEligibilityDetails?.errors?.length > 0)) {

              const updatedReview = await AdminDao.getMeetingStatusById(eligibilityDataSet[0].meetingDetails._id);
              let meetingCreatedAt = JSON.stringify(eligibilityDataSet[0].meetingDetails);
              let meetingDate = null;
              if (meetingCreatedAt) {
                meetingDate = JSON.parse(meetingCreatedAt);
              }

              const createdAtDate = new Date(meetingDate?.createdAt);
              const previousNoteDetailsData = await TreatmentHistory.findOne({
                clientId: eligibilityDataSet[0].clientId?._id,
                therapistId: eligibilityDataSet[0].therapistId?._id,
                meetingStartedTime: {
                  $gte: new Date(createdAtDate.setUTCHours(0, 0, 0, 0)),
                  $lt: new Date(createdAtDate.setUTCHours(23, 59, 59, 999))
                },
                claimStatus: { $in: ['ACTIVEMD', 'ACTIVE', 'IN_PROGRESS', 'PAID', 'UNPAID'] }
              });


              if (previousNoteDetailsData) {
                return res.sendError(`You have already submitted a diagnosis note for session on this day ${moment.utc(createdAtDate).format("MMM DD YYYY")} with ${eligibilityDataSet[0].clientId.firstname}.`);
              } else {
                const identity = [];

                if ('dependents' in claimEligibilityDetails) {
                  identity.push('dependents');
                } else {
                  identity.push('subscriber');
                }

                const eligibilityData = {
                  subscriber: identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0],
                  memberId: claimEligibilityDetails.subscriber.memberId,
                }

                let updatedClientEligibility = await ClientDao.updateClientEligibility(eligibilityDataSet[0].clientId?._id, eligibilityData);
                AppLogger.info(`.:: Client Eligibility Update, Client Name : ` + eligibilityDataSet[0].clientId?.firstname + `.:: eligibilityData : ` + eligibilityData);

                if (partner[insurancePartner]) {
                  const extractedInfo = {
                    ins_name_f: claimEligibilityDetails?.subscriber?.firstName || updatedClientEligibility?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                    ins_name_l: claimEligibilityDetails?.subscriber?.lastName || updatedClientEligibility?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                    ins_city: claimEligibilityDetails?.subscriber?.address?.city || updatedClientEligibility?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city || 'Unknown',
                    ins_state: claimEligibilityDetails?.subscriber?.address?.state || updatedClientEligibility?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                    ins_zip: claimEligibilityDetails?.subscriber?.address?.postalCode || updatedClientEligibility?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                    ins_dob: moment(claimEligibilityDetails?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(updatedClientEligibility?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                    ins_sex: claimEligibilityDetails?.subscriber?.gender || updatedClientEligibility?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                    ins_addr_1: claimEligibilityDetails?.subscriber?.address?.address1 || updatedClientEligibility?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                  };

                  let place_of_service;
                  let modifier;

                  if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                    place_of_service = "11";
                  } else {
                    place_of_service = "10";
                    modifier = "95"
                  }

                  const randomKey = generateRandomKey(20);
                  let claimInformationMD: any = {
                    claim_form: 1500,
                    payer_name: Insurance_company[insurancePartner],
                    accept_assign: 'Y',
                    employment_related: 'N',
                    ins_name_f: extractedInfo.ins_name_f || claimEligibilityDetails?.subscriber?.firstName || updatedClientEligibility?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname,
                    ins_name_l: extractedInfo.ins_name_l || claimEligibilityDetails?.subscriber?.lastName || updatedClientEligibility?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname,
                    ins_addr_1: extractedInfo.ins_addr_1 || claimEligibilityDetails?.subscriber?.address?.address1 || updatedClientEligibility?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress,
                    ins_city: extractedInfo.ins_city || claimEligibilityDetails?.subscriber?.address?.city || updatedClientEligibility?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city,
                    ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || claimEligibilityDetails?.subscriber?.dateOfBirth || moment(updatedClientEligibility?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                    ins_sex: extractedInfo.ins_sex || claimEligibilityDetails?.subscriber?.gender || updatedClientEligibility?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                    ins_state: extractedInfo.ins_state || claimEligibilityDetails?.subscriber?.address?.state || updatedClientEligibility?.subscriber?.address?.state || '' || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState],
                    ins_zip: extractedInfo.ins_zip || claimEligibilityDetails?.subscriber?.address?.postalCode || updatedClientEligibility?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode,
                    ins_number: claimEligibilityDetailsData?.subscriber?.memberId || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                    bill_taxonomy: '251S00000X',
                    place_of_service_1: place_of_service,
                    prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                    prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                    prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                    prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                    pcn: String(randomKey),
                    charge_1: 180,
                    pat_rel: '18',
                    payerid: Tradingpartner_ServiceId[insurancePartner],
                    total_charge: 180,
                    claimNumber: diagnosisNote?.encounterID || '87654321',
                    proc_code_1: diagnosisNote?.cptCode,
                    mod1_1: modifier,
                    diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                    diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                    from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                    bill_name: 'Lavni Inc',
                    bill_addr_1: '804 South Garnett St.',
                    bill_addr_2: '',
                    bill_city: 'Henderson',
                    bill_state: 'NC',
                    bill_zip: '27536',
                    bill_npi: '**********',
                    bill_id: '',
                    bill_phone: '**********',
                    bill_taxid: '*********',
                    bill_taxid_type: 'E',
                    diag_ref_1: "A",
                    units_1: 1,
                    pat_name_f: extractedInfo.ins_name_f,
                    pat_name_l: extractedInfo.ins_name_l,
                    pat_addr_1: extractedInfo.ins_addr_1,
                    pat_city: extractedInfo.ins_city,
                    pat_state: extractedInfo.ins_state,
                    pat_zip: extractedInfo.ins_zip,
                    pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                    pat_sex: extractedInfo.ins_sex,
                  };
                  if (treatmentHistoryId?.meetingStartedTime) {
                    if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                      const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                      await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                      if (saveClaimMd) {
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    } else {
                      const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                      const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);
 
                      if (claimUploadCSV?.error) {
                        AppLogger.error('claimUploadCSV 002', claimUploadCSV?.error?.error_mesg);
                        let claimInformation: any = {
                          claimFilingCode: "CI",
                          patientControlNumber: diagnosisNote?.patientID,
                          claimChargeAmount: claim.claimChargeAmount,
                          placeOfServiceCode: place_of_service,
                          claimFrequencyCode: "1",
                          signatureIndicator: "Y",
                          planParticipationCode: "A",
                          benefitsAssignmentCertificationIndicator: "Y",
                          releaseInformationCode: "Y",
                          claimSupplementalInformation: {
                            repricedClaimNumber: "00001",
                            claimNumber: diagnosisNote?.encounterID,
                          },
                          serviceFacilityLocation: {
                            organizationName: "Lavni Inc",
                            address: {
                              address1: "804 South Garnett st.",
                              city: "Henderson",
                              state: "NC",
                              postalCode: "27536",
                            },
                          },
                          serviceLines: [
                            {
                              serviceDate: moment(serviceLines?.serviceDate).utcOffset(-5 * 60).format("YYYYMMDD"),
                              professionalService: {
                                procedureIdentifier: "HC",
                                lineItemChargeAmount: serviceLines?.professionalService?.lineItemChargeAmount,
                                procedureCode: serviceLines?.professionalService?.procedureCode,
                                measurementUnit: "UN",
                                serviceUnitCount: "1",
                                placeOfServiceCode: place_of_service,
                                description: 'mental health',
                                compositeDiagnosisCodePointers: { "diagnosisCodePointers": ["1"] },
                              },
                            },
                          ],
                        };

                        if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                          claimInformation.serviceLines[0].professionalService.procedureModifiers = [""];
                        } else {
                          claimInformation.serviceLines[0].professionalService.procedureModifiers = ["95"];
                        }

                        if (claim?.secondaryDiagnosisCodes && claim?.secondaryDiagnosisCodes.length > 0) {
                          claimInformation.healthCareCodeInformation = [
                            {
                              diagnosisTypeCode: "ABK",
                              diagnosisCode: claim?.diagnosisCode.replace(/\./g, ''),
                            },
                            {
                              diagnosisTypeCode: "ABF",
                              diagnosisCode: claim?.secondaryDiagnosisCodes.replace(/\./g, '')
                            }
                          ]
                        } else {
                          if (claim?.diagnosisCode && claim?.diagnosisCode.length > 0) {
                            claimInformation.healthCareCodeInformation = [
                              {
                                diagnosisTypeCode: "ABK",
                                diagnosisCode: claim?.diagnosisCode.replace(/\./g, ''),
                              }
                            ];
                          } else {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Empty secondaryDiagnosisICDcodes or diagnosisICDcodes");
                          }
                        }


                        const identity = [];

                        if ('dependents' in claimEligibilityDetails) {
                          identity.push('dependents');
                        } else {
                          identity.push('subscriber');
                        }
                        const sub = identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0];
                        const memberId = claimEligibilityDetails.subscriber.memberId;
                        let subUpdate;

                        let therapistInformation: any = {}

                        if (renderingProviderFirstName == "Brittany" && renderingProviderLastName == "Schopler") {
                          therapistInformation = {
                            providerType: "RenderingProvider",
                            npi: renderingProviderNpi,
                            firstName: "Laura",
                            lastName: "Valentine",
                            taxonomyCode: renderingProviderTaxonomyCode,
                          }
                        } else {
                          therapistInformation = {
                            providerType: "RenderingProvider",
                            npi: renderingProviderNpi,
                            firstName: renderingProviderFirstName,
                            lastName: renderingProviderLastName,
                            taxonomyCode: renderingProviderTaxonomyCode,
                          }
                        }

                        try {
                          subUpdate = {
                            memberId: memberId,
                            paymentResponsibilityLevelCode: 'P',
                            firstName: sub.firstName,
                            lastName: sub.lastName,
                            gender: sub.gender,
                            dateOfBirth: sub.dateOfBirth,
                            address: sub.address,
                          };
                        } catch (error) {
                          subUpdate = {
                            memberId: memberId,
                            paymentResponsibilityLevelCode: 'P',
                            firstName: sub.firstName,
                            lastName: sub.lastName,
                            gender: sub.gender,
                            dateOfBirth: sub.dateOfBirth,
                            address: sub.address,
                          };
                        }

                        const testClaimData = {
                          controlNumber: "*********",
                          tradingPartnerServiceId: Tradingpartner_ServiceId[organizationName],
                          submitter: {
                            organizationName: "Lavni Inc",
                            contactInformation: {
                              name: "Lavni Inc",
                              phoneNumber: "**********",
                            },
                          },
                          receiver: {
                            organizationName: Insurance_company[insuranceCompany],
                          },
                          subscriber: subUpdate,
                          providers: [
                            {
                              providerType: "BillingProvider",
                              npi: "**********",
                              employerId: "*********",
                              taxonomyCode: '251S00000X',
                              organizationName: "Lavni Inc",
                              address: {
                                address1: "804 South Garnett St.",
                                city: "Henderson",
                                state: "NC",
                                postalCode: "*********"
                              },
                              contactInformation: {
                                name: "Angela Arrington",
                                phoneNumber: "**********"
                              },
                            },
                            therapistInformation
                          ],
                          claimInformation: claimInformation
                        };

                        if (!claimEligibilityDetails?.errors) {
                          const claimSubmissionDetails = await submitClaim(testClaimData, accessToken.access_token);

                          if (claimSubmissionDetails?.status == "SUCCESS") {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusById(treatmentHistoryId._id, "SUPER_ADMIN");
                            return res.sendSuccess(updatedReview, "Sucess");
                          } else {
                            AppLogger.error('claimSubmissionDetails 002', claimSubmissionDetails)
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimSubmissionDetails?.errors[0]));
                            return res.sendSuccess(updatedReview, "Success");
                          }
                        }
                      } else {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    }
                  }
                }
              }
            } else {
              let meetingCreatedAt = JSON.stringify(eligibilityDataSet[0].meetingDetails);
              let meetingDate = null;
              if (meetingCreatedAt) {
                meetingDate = JSON.parse(meetingCreatedAt);
              }

              const createdAtDate = new Date(meetingDate?.createdAt);
              const previousNoteDetailsData = await TreatmentHistory.findOne({
                clientId: eligibilityDataSet[0].clientId?._id,
                therapistId: eligibilityDataSet[0].therapistId?._id,
                meetingStartedTime: {
                  $gte: new Date(createdAtDate.setUTCHours(0, 0, 0, 0)),
                  $lt: new Date(createdAtDate.setUTCHours(23, 59, 59, 999))
                },
                claimStatus: { $in: ['ACTIVEMD', 'ACTIVE', 'IN_PROGRESS', 'PAID', 'UNPAID'] }
              });


              if (previousNoteDetailsData) {
                return res.sendError(`You have already submitted a diagnosis note for session on this day ${moment.utc(createdAtDate).format("MMM DD YYYY")} with ${eligibilityDataSet[0].clientId.firstname}.`);
              } else {
                let claimEligibilityMDDetails = JSON.stringify(eligibilityDataSet[0].clientId?.claimEligibilityMdDetails);
                let claimEligibilityMDData = null;
                if (claimEligibilityMDDetails) {
                  claimEligibilityMDData = JSON.parse(claimEligibilityMDDetails);
                }

                if (claimEligibilityMDData && claimEligibilityMDData?.ins_number?.length > 0) {
                  if (partner[insurancePartner]) {
                    const extractedInfo = {
                      ins_name_f: claimEligibilityMDData.pat_name_f || claimEligibilityMDData.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                      ins_name_l: claimEligibilityMDData.pat_name_l || claimEligibilityMDData.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                      ins_city: claimEligibilityMDData.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                      ins_state: claimEligibilityMDData.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                      ins_zip: claimEligibilityMDData.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                      ins_dob: claimEligibilityMDData.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                      ins_sex: claimEligibilityMDData.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                      ins_addr_1: claimEligibilityMDData.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                      ins_number: claimEligibilityMDData.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                    };

                    let place_of_service;
                    let modifier;

                    if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                      place_of_service = "11";
                    } else {
                      place_of_service = "10";
                      modifier = "95"
                    }

                    const randomKey = generateRandomKey(20);
                    let claimInformationMD: any = {
                      claim_form: 1500,
                      payer_name: Insurance_company[insurancePartner],
                      accept_assign: 'Y',
                      employment_related: 'N',
                      ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                      ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                      ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                      ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                      ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                      ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                      ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                      ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                      ins_number: extractedInfo.ins_number || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                      bill_taxonomy: '251S00000X',
                      place_of_service_1: place_of_service,
                      prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                      prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                      prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                      prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                      pcn: String(randomKey),
                      charge_1: 180,
                      pat_rel: '18',
                      payerid: Tradingpartner_ServiceId[insurancePartner],
                      total_charge: 180,
                      claimNumber: diagnosisNote?.encounterID || '87654321',
                      proc_code_1: diagnosisNote?.cptCode,
                      mod1_1: modifier,
                      diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                      diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                      from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                      bill_name: 'Lavni Inc',
                      bill_addr_1: '804 South Garnett St.',
                      bill_addr_2: '',
                      bill_city: 'Henderson',
                      bill_state: 'NC',
                      bill_zip: '27536',
                      bill_npi: '**********',
                      bill_id: '',
                      bill_phone: '**********',
                      bill_taxid: '*********',
                      bill_taxid_type: 'E',
                      diag_ref_1: "A",
                      units_1: 1,
                      pat_name_f: extractedInfo.ins_name_f,
                      pat_name_l: extractedInfo.ins_name_l,
                      pat_addr_1: extractedInfo.ins_addr_1,
                      pat_city: extractedInfo.ins_city,
                      pat_state: extractedInfo.ins_state,
                      pat_zip: extractedInfo.ins_zip,
                      pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                      pat_sex: extractedInfo.ins_sex,
                    };
                    if (treatmentHistoryId?.meetingStartedTime) {
                      if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                        const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                        await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                        if (saveClaimMd) {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                          return res.sendSuccess(updatedTherapist, "Success");
                        }
                      } else {
                        const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                        const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                        if (claimUploadCSV?.error) {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                          return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                        } else {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                          return res.sendSuccess(updatedTherapist, "Success");
                        }
                      }
                    }
                  }
                } else {
                  if (partner[insurancePartner]) {
                    let eligibility_payload_md = null;
                    if (eligibilityDataSet[0].clientInsuranceDetails) {
                      eligibility_payload_md = {
                        "AccountKey": process.env.CLAIM_MD_ACCOUNT_KEY,
                        "ins_name_f": eligibilityDataSet[0].clientId.firstname,
                        "ins_name_l": eligibilityDataSet[0].clientId.lastname,
                        "payerid": Tradingpartner_ServiceId[insurancePartner],
                        "pat_rel": "18",
                        "fdos": moment.utc(treatmentHistoryId?.meetingStartedTime).format('YYYYMMDD'),
                        "ins_dob": moment.utc(eligibilityDataSet[0].clientId?.dateOfBirth).format('YYYYMMDD'),
                        "ins_sex": (eligibilityDataSet[0].clientId?.gender == "Male" ? "M" : "F"),
                        "ins_number": subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                        "prov_npi": process.env.LAVNI_NPI,
                      }
                    }

                    const claimEligibilityAndActiveData = await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, eligibilityDataSet[0].clientId._id);

                    if (claimEligibilityAndActiveData?.finalStatus == "ACTIVE") {

                      const eligibilityInfo = claimEligibilityAndActiveData?.eligibilityInfoData;

                      const extractedInfo = {
                        ins_name_f: eligibilityInfo.pat_name_f || eligibilityInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                        ins_name_l: eligibilityInfo.pat_name_l || eligibilityInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                        ins_city: eligibilityInfo.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                        ins_state: eligibilityInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                        ins_zip: eligibilityInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                        ins_dob: eligibilityInfo.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                        ins_sex: eligibilityInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                        ins_addr_1: eligibilityInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                        ins_number: eligibilityInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                      };

                      let place_of_service;
                      let modifier;

                      if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                        place_of_service = "11";
                      } else {
                        place_of_service = "10";
                        modifier = "95"
                      }

                      const randomKey = generateRandomKey(20);

                      let claimInformationMD: any = {
                        claim_form: 1500,
                        payer_name: Insurance_company[insurancePartner],
                        accept_assign: 'Y',
                        employment_related: 'N',
                        ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                        ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                        ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                        ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                        ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                        ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                        ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                        ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                        ins_number: extractedInfo.ins_number || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                        bill_taxonomy: '251S00000X',
                        place_of_service_1: place_of_service,
                        prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                        prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                        prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                        prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                        pcn: String(randomKey),
                        charge_1: 180,
                        pat_rel: '18',
                        payerid: Tradingpartner_ServiceId[insurancePartner],
                        total_charge: 180,
                        claimNumber: diagnosisNote?.encounterID || '87654321',
                        proc_code_1: diagnosisNote?.cptCode,
                        mod1_1: modifier,
                        diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                        diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                        from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                        bill_name: 'Lavni Inc',
                        bill_addr_1: '804 South Garnett St.',
                        bill_addr_2: '',
                        bill_city: 'Henderson',
                        bill_state: 'NC',
                        bill_zip: '27536',
                        bill_npi: '**********',
                        bill_id: '',
                        bill_phone: '**********',
                        bill_taxid: '*********',
                        bill_taxid_type: 'E',
                        diag_ref_1: "A",
                        units_1: 1,
                        pat_name_f: extractedInfo.ins_name_f,
                        pat_name_l: extractedInfo.ins_name_l,
                        pat_addr_1: extractedInfo.ins_addr_1,
                        pat_city: extractedInfo.ins_city,
                        pat_state: extractedInfo.ins_state,
                        pat_zip: extractedInfo.ins_zip,
                        pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                        pat_sex: extractedInfo.ins_sex,
                      };

                      if (treatmentHistoryId?.meetingStartedTime) {
                        if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                          const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                          await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                          if (saveClaimMd) {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                            return res.sendSuccess(updatedTherapist, "Success");
                          }
                        } else {
                          const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                          const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                          if (claimUploadCSV?.error) {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                            return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                          } else {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                            return res.sendSuccess(updatedTherapist, "Success");
                          }
                        }
                      }
                    } else {
                      await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, claimEligibilityAndActiveData?.finalError);
                      return res.sendError(claimEligibilityAndActiveData?.finalError);
                    }
                  }
                }

              }
            }
          }
        }

        if (!hasExpired) {
          let claimEligibilityDetailsOBJ = JSON.stringify(eligibilityDataSet[0]?.clientId?.claimEligibilityDetails);
          let claimEligibilityDetailsData = null;
          if (claimEligibilityDetailsOBJ) {
            claimEligibilityDetailsData = JSON.parse(claimEligibilityDetailsOBJ);
          }

          if (claimEligibilityDetailsData && claimEligibilityDetailsData?.memberId?.length > 0) {
            const updatedReview = await AdminDao.getMeetingStatusById(eligibilityDataSet[0].meetingDetails._id);
            let meetingCreatedAt = JSON.stringify(eligibilityDataSet[0].meetingDetails);

            let meetingDate = null;

            if (meetingCreatedAt) {
              meetingDate = JSON.parse(meetingCreatedAt);
            }

            const createdAtDate = new Date(meetingDate?.createdAt);

            const previousNoteDetailsData = await TreatmentHistory.findOne({
              clientId: eligibilityDataSet[0].clientId?._id,
              therapistId: eligibilityDataSet[0].therapistId?._id,
              meetingStartedTime: {
                $gte: new Date(createdAtDate.setUTCHours(0, 0, 0, 0)),
                $lt: new Date(createdAtDate.setUTCHours(23, 59, 59, 999))
              },
              claimStatus: { $in: ['ACTIVEMD', 'ACTIVE', 'IN_PROGRESS', 'PAID', 'UNPAID'] }
            });


            if (previousNoteDetailsData) {
              return res.sendError(`You have already submitted a diagnosis note for session on this day ${moment.utc(createdAtDate).format("MMM DD YYYY")} with ${eligibilityDataSet[0].clientId.firstname}.`);
            } else {
              if (partner[insurancePartner]) {
                const extractedInfo = {
                  ins_name_f: claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                  ins_name_l: claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                  ins_city: claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city || 'Unknown',
                  ins_state: claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                  ins_zip: claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                  ins_dob: moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                  ins_sex: claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                  ins_addr_1: claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                };

                let place_of_service;
                let modifier;

                if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                  place_of_service = "11";
                } else {
                  place_of_service = "10";
                  modifier = "95"
                }

                const randomKey = generateRandomKey(20);

                let claimInformationMD: any = {
                  claim_form: 1500,
                  payer_name: Insurance_company[insurancePartner],
                  accept_assign: 'Y',
                  employment_related: 'N',
                  ins_name_f: extractedInfo.ins_name_f || claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname,
                  ins_name_l: extractedInfo.ins_name_l || claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname,
                  ins_addr_1: extractedInfo.ins_addr_1 || claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress,
                  ins_city: extractedInfo.ins_city || claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city,
                  ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                  ins_sex: extractedInfo.ins_sex || claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                  ins_state: extractedInfo.ins_state || claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                  ins_zip: extractedInfo.ins_zip || claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode,
                  ins_number: claimEligibilityDetailsData?.subscriber?.memberId || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                  bill_taxonomy: '251S00000X',
                  place_of_service_1: place_of_service,
                  prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                  prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                  prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                  prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                  pcn: String(randomKey),
                  charge_1: 180,
                  pat_rel: '18',
                  payerid: Tradingpartner_ServiceId[insurancePartner],
                  total_charge: 180,
                  claimNumber: diagnosisNote?.encounterID || '87654321',
                  proc_code_1: diagnosisNote?.cptCode,
                  mod1_1: modifier,
                  diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                  diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                  from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                  bill_name: 'Lavni Inc',
                  bill_addr_1: '804 South Garnett St.',
                  bill_addr_2: '',
                  bill_city: 'Henderson',
                  bill_state: 'NC',
                  bill_zip: '27536',
                  bill_npi: '**********',
                  bill_id: '',
                  bill_phone: '**********',
                  bill_taxid: '*********',
                  bill_taxid_type: 'E',
                  diag_ref_1: "A",
                  units_1: 1,
                  pat_name_f: extractedInfo.ins_name_f,
                  pat_name_l: extractedInfo.ins_name_l,
                  pat_addr_1: extractedInfo.ins_addr_1,
                  pat_city: extractedInfo.ins_city,
                  pat_state: extractedInfo.ins_state,
                  pat_zip: extractedInfo.ins_zip,
                  pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                  pat_sex: extractedInfo.ins_sex,
                };
                if (treatmentHistoryId?.meetingStartedTime) {
                  if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                    const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                    await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                    if (saveClaimMd) {
                      return res.sendSuccess(updatedReview, "Success");
                    }
                  } else {
                    const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                    const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                    if (claimUploadCSV?.error) {
                      AppLogger.error('claimUploadCSV 003', claimUploadCSV?.error?.error_mesg);

                      let claimInformation: any = {
                        claimFilingCode: "CI",
                        patientControlNumber: diagnosisNote?.patientID,
                        claimChargeAmount: claim.claimChargeAmount,
                        placeOfServiceCode: "10",
                        claimFrequencyCode: "1",
                        signatureIndicator: "Y",
                        planParticipationCode: "A",
                        benefitsAssignmentCertificationIndicator: "Y",
                        releaseInformationCode: "Y",
                        claimSupplementalInformation: {
                          repricedClaimNumber: "00001",
                          claimNumber: diagnosisNote?.encounterID,
                        },
                        serviceFacilityLocation: {
                          organizationName: "Lavni Inc",
                          address: {
                            address1: "804 South Garnett st.",
                            city: "Henderson",
                            state: "NC",
                            postalCode: "27536",
                          },
                        },
                        serviceLines: [
                          {
                            serviceDate: moment(serviceLines?.serviceDate).utcOffset(-5 * 60).format("YYYYMMDD"),
                            professionalService: {
                              procedureIdentifier: "HC",
                              lineItemChargeAmount: serviceLines?.professionalService?.lineItemChargeAmount,
                              procedureCode: serviceLines?.professionalService?.procedureCode,
                              measurementUnit: "UN",
                              serviceUnitCount: "1",
                              placeOfServiceCode: place_of_service,
                              description: 'mental health',
                              compositeDiagnosisCodePointers: { "diagnosisCodePointers": ["1"] },
                            },
                          },
                        ],
                      };

                      if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                        claimInformation.serviceLines[0].professionalService.procedureModifiers = [""];
                      } else {
                        claimInformation.serviceLines[0].professionalService.procedureModifiers = ["95"];
                      }


                      if (claim?.secondaryDiagnosisCodes && claim?.secondaryDiagnosisCodes.length > 0) {
                        claimInformation.healthCareCodeInformation = [
                          {
                            diagnosisTypeCode: "ABK",
                            diagnosisCode: claim?.diagnosisCode.replace(/\./g, ''),
                          },
                          {
                            diagnosisTypeCode: "ABF",
                            diagnosisCode: claim?.secondaryDiagnosisCodes.replace(/\./g, '')
                          }
                        ]
                      } else {
                        if (claim?.diagnosisCode && claim?.diagnosisCode.length > 0) {
                          claimInformation.healthCareCodeInformation = [
                            {
                              diagnosisTypeCode: "ABK",
                              diagnosisCode: claim?.diagnosisCode.replace(/\./g, ''),
                            }
                          ];
                        } else {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Empty secondaryDiagnosisICDcodes or diagnosisICDcodes");
                        }
                      }

                      const identity = [];
                      if ('dependents' in claimEligibilityDetailsData) {
                        identity.push('dependents');
                      } else {
                        identity.push('subscriber');
                      }
                      const sub = identity[0] === 'subscriber' ? claimEligibilityDetailsData.subscriber : claimEligibilityDetailsData.dependents[0];
                      const memberId = claimEligibilityDetailsData.subscriber.memberId;
                      let subUpdate;


                      let therapistInformation: any = {}

                      if (renderingProviderFirstName == "Brittany" && renderingProviderLastName == "Schopler") {
                        therapistInformation = {
                          providerType: "RenderingProvider",
                          npi: renderingProviderNpi,
                          firstName: "Laura",
                          lastName: "Valentine",
                          taxonomyCode: renderingProviderTaxonomyCode,
                        }
                      } else {
                        therapistInformation = {
                          providerType: "RenderingProvider",
                          npi: renderingProviderNpi,
                          firstName: renderingProviderFirstName,
                          lastName: renderingProviderLastName,
                          taxonomyCode: renderingProviderTaxonomyCode,
                        }
                      }

                      try {
                        subUpdate = {
                          memberId: memberId,
                          paymentResponsibilityLevelCode: 'P',
                          firstName: sub.firstName,
                          lastName: sub.lastName,
                          gender: sub.gender,
                          dateOfBirth: sub.dateOfBirth,
                          address: sub.address,
                        };
                      } catch (error) {
                        subUpdate = {
                          memberId: memberId,
                          paymentResponsibilityLevelCode: 'P',
                          firstName: sub.firstName,
                          lastName: sub.lastName,
                          gender: sub.gender,
                          dateOfBirth: sub.dateOfBirth,
                          address: sub.address,
                        };
                      }

                      const testClaimData = {
                        controlNumber: "*********",
                        tradingPartnerServiceId: Tradingpartner_ServiceId[organizationName],
                        submitter: {
                          organizationName: "Lavni Inc",
                          contactInformation: {
                            name: "Lavni Inc",
                            phoneNumber: "**********",
                          },
                        },
                        receiver: {
                          organizationName: Insurance_company[insuranceCompany],
                        },
                        subscriber: subUpdate,
                        providers: [
                          {
                            providerType: "BillingProvider",
                            npi: "**********",
                            employerId: "*********",
                            taxonomyCode: '251S00000X',
                            organizationName: "Lavni Inc",
                            address: {
                              address1: "804 South Garnett St.",
                              city: "Henderson",
                              state: "NC",
                              postalCode: "*********"
                            },
                            contactInformation: {
                              name: "Angela Arrington",
                              phoneNumber: "**********"
                            },
                          },
                          therapistInformation
                        ],
                        claimInformation: claimInformation
                      };

                      const claimSubmissionDetails = await submitClaim(testClaimData, insurance?.insuranceAccessToken);

                      if (claimSubmissionDetails?.status == "SUCCESS") {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusById(treatmentHistoryId._id, "SUPER_ADMIN");
                        return res.sendSuccess(updatedReview, "Sucess");
                      } else {
                        AppLogger.error('claimSubmissionDetails 003', claimSubmissionDetails)
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimSubmissionDetails?.errors[0]));
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    } else {
                      let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                      return res.sendSuccess(updatedReview, "Success");
                    }
                  }
                }
              }
            }
          } else {
            const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, insurance?.insuranceAccessToken);
            if (!(claimEligibilityDetails?.errors?.length > 0)) {
              const updatedReview = await AdminDao.getMeetingStatusById(eligibilityDataSet[0].meetingDetails._id);
              let meetingCreatedAt = JSON.stringify(eligibilityDataSet[0].meetingDetails);

              let meetingDate = null;

              if (meetingCreatedAt) {
                meetingDate = JSON.parse(meetingCreatedAt);
              }

              const createdAtDate = new Date(meetingDate?.createdAt);
              const previousNoteDetailsData = await TreatmentHistory.findOne({
                clientId: eligibilityDataSet[0].clientId?._id,
                therapistId: eligibilityDataSet[0].therapistId?._id,
                meetingStartedTime: {
                  $gte: new Date(createdAtDate.setUTCHours(0, 0, 0, 0)),
                  $lt: new Date(createdAtDate.setUTCHours(23, 59, 59, 999))
                },
                claimStatus: { $in: ['ACTIVEMD', 'ACTIVE', 'IN_PROGRESS', 'PAID', 'UNPAID'] }
              });


              if (previousNoteDetailsData) {
                return res.sendError(`You have already submitted a diagnosis note for session on this day ${moment.utc(createdAtDate).format("MMM DD YYYY")} with ${eligibilityDataSet[0].clientId.firstname}.`);
              } else {
                const identity = [];

                if ('dependents' in claimEligibilityDetails) {
                  identity.push('dependents');
                } else {
                  identity.push('subscriber');
                }


                const eligibilityData = {
                  subscriber: identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0],
                  memberId: claimEligibilityDetails.subscriber.memberId,
                }

                let updatedClientEligibility = await ClientDao.updateClientEligibility(eligibilityDataSet[0]?.clientId?._id, eligibilityData);
                AppLogger.info(`.:: Client Eligibility Update 002, Client Name : ` + eligibilityDataSet[0]?.clientId?.firstname + `.:: eligibilityData : ` + eligibilityData);

                if (partner[insurancePartner]) {
                  const extractedInfo = {
                    ins_name_f: claimEligibilityDetails?.subscriber?.firstName || updatedClientEligibility?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                    ins_name_l: claimEligibilityDetails?.subscriber?.lastName || updatedClientEligibility?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                    ins_city: claimEligibilityDetails?.subscriber?.address?.city || updatedClientEligibility?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city || 'Unknown',
                    ins_state: claimEligibilityDetails?.subscriber?.address?.state || updatedClientEligibility?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                    ins_zip: claimEligibilityDetails?.subscriber?.address?.postalCode || updatedClientEligibility?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                    ins_dob: moment(claimEligibilityDetails?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(updatedClientEligibility?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                    ins_sex: claimEligibilityDetails?.subscriber?.gender || updatedClientEligibility?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                    ins_addr_1: claimEligibilityDetails?.subscriber?.address?.address1 || updatedClientEligibility?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                  };

                  let place_of_service;
                  let modifier;

                  if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                    place_of_service = "11";
                  } else {
                    place_of_service = "10";
                    modifier = "95"
                  }
                  const randomKey = generateRandomKey(20);
                  let claimInformationMD: any = {
                    claim_form: 1500,
                    payer_name: Insurance_company[insurancePartner],
                    accept_assign: 'Y',
                    employment_related: 'N',
                    ins_name_f: extractedInfo.ins_name_f || claimEligibilityDetails?.subscriber?.firstName || updatedClientEligibility?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname,
                    ins_name_l: extractedInfo.ins_name_l || claimEligibilityDetails?.subscriber?.lastName || updatedClientEligibility?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname,
                    ins_addr_1: extractedInfo.ins_addr_1 || claimEligibilityDetails?.subscriber?.address?.address1 || updatedClientEligibility?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress,
                    ins_city: extractedInfo.ins_city || claimEligibilityDetails?.subscriber?.address?.city || updatedClientEligibility?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city,
                    ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || claimEligibilityDetails?.subscriber?.dateOfBirth || moment(updatedClientEligibility?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                    ins_sex: extractedInfo.ins_sex || claimEligibilityDetails?.subscriber?.gender || updatedClientEligibility?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                    ins_state: extractedInfo.ins_state || claimEligibilityDetails?.subscriber?.address?.state || updatedClientEligibility?.subscriber?.address?.state || '' || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState],
                    ins_zip: extractedInfo.ins_zip || claimEligibilityDetails?.subscriber?.address?.postalCode || updatedClientEligibility?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode,
                    ins_number: claimEligibilityDetailsData?.subscriber?.memberId || updatedClientEligibility?.subscriber?.memberId || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                    bill_taxonomy: '251S00000X',
                    place_of_service_1: place_of_service,
                    prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                    prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                    prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                    prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                    pcn: String(randomKey),
                    charge_1: 180,
                    pat_rel: '18',
                    payerid: Tradingpartner_ServiceId[insurancePartner],
                    total_charge: 180,
                    claimNumber: diagnosisNote?.encounterID || '87654321',
                    proc_code_1: diagnosisNote?.cptCode,
                    mod1_1: modifier,
                    diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                    diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                    from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                    bill_name: 'Lavni Inc',
                    bill_addr_1: '804 South Garnett St.',
                    bill_addr_2: '',
                    bill_city: 'Henderson',
                    bill_state: 'NC',
                    bill_zip: '27536',
                    bill_npi: '**********',
                    bill_id: '',
                    bill_phone: '**********',
                    bill_taxid: '*********',
                    bill_taxid_type: 'E',
                    diag_ref_1: "A",
                    units_1: 1,
                    pat_name_f: extractedInfo.ins_name_f,
                    pat_name_l: extractedInfo.ins_name_l,
                    pat_addr_1: extractedInfo.ins_addr_1,
                    pat_city: extractedInfo.ins_city,
                    pat_state: extractedInfo.ins_state,
                    pat_zip: extractedInfo.ins_zip,
                    pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                    pat_sex: extractedInfo.ins_sex,
                  };

                  if (treatmentHistoryId?.meetingStartedTime) {
                    if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                      const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                      await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                      if (saveClaimMd) {
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    } else {
                      const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                      const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                      if (claimUploadCSV?.error) {
                        AppLogger.error('claimUploadCSV 004', claimUploadCSV?.error?.error_mesg);

                        if (!claimEligibilityDetails?.errors) {
                          let claimInformation: any = {
                            claimFilingCode: "CI",
                            patientControlNumber: diagnosisNote?.patientID,
                            claimChargeAmount: claim.claimChargeAmount,
                            placeOfServiceCode: place_of_service,
                            claimFrequencyCode: "1",
                            signatureIndicator: "Y",
                            planParticipationCode: "A",
                            benefitsAssignmentCertificationIndicator: "Y",
                            releaseInformationCode: "Y",
                            claimSupplementalInformation: {
                              repricedClaimNumber: "00001",
                              claimNumber: diagnosisNote?.encounterID,
                            },
                            serviceFacilityLocation: {
                              organizationName: "Lavni Inc",
                              address: {
                                address1: "804 South Garnett st.",
                                city: "Henderson",
                                state: "NC",
                                postalCode: "27536",
                              },
                            },
                            serviceLines: [
                              {
                                serviceDate: moment(serviceLines?.serviceDate).utcOffset(-5 * 60).format("YYYYMMDD"),
                                professionalService: {
                                  procedureIdentifier: "HC",
                                  lineItemChargeAmount: serviceLines?.professionalService?.lineItemChargeAmount,
                                  procedureCode: serviceLines?.professionalService?.procedureCode,
                                  measurementUnit: "UN",
                                  serviceUnitCount: "1",
                                  placeOfServiceCode: place_of_service,
                                  description: 'mental health',
                                  compositeDiagnosisCodePointers: { "diagnosisCodePointers": ["1"] },
                                },
                              },
                            ],
                          };

                          if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                            claimInformation.serviceLines[0].professionalService.procedureModifiers = [""];
                          } else {
                            claimInformation.serviceLines[0].professionalService.procedureModifiers = ["95"];
                          }

                          if (claim?.secondaryDiagnosisCodes && claim?.secondaryDiagnosisCodes.length > 0) {
                            claimInformation.healthCareCodeInformation = [
                              {
                                diagnosisTypeCode: "ABK",
                                diagnosisCode: claim?.diagnosisCode.replace(/\./g, ''),
                              },
                              {
                                diagnosisTypeCode: "ABF",
                                diagnosisCode: claim?.secondaryDiagnosisCodes.replace(/\./g, '')
                              }
                            ]
                          } else {
                            if (claim?.diagnosisCode && claim?.diagnosisCode.length > 0) {
                              claimInformation.healthCareCodeInformation = [
                                {
                                  diagnosisTypeCode: "ABK",
                                  diagnosisCode: claim?.diagnosisCode.replace(/\./g, ''),
                                }
                              ];
                            } else {
                              let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Empty secondaryDiagnosisICDcodes or diagnosisICDcodes");
                            }
                          }


                          const identity = [];

                          if ('dependents' in claimEligibilityDetails) {
                            identity.push('dependents');
                          } else {
                            identity.push('subscriber');
                          }
                          const sub = identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0];
                          const memberId = claimEligibilityDetails.subscriber.memberId;
                          let subUpdate;


                          let therapistInformation: any = {}

                          if (renderingProviderFirstName == "Brittany" && renderingProviderLastName == "Schopler") {
                            therapistInformation = {
                              providerType: "RenderingProvider",
                              npi: renderingProviderNpi,
                              firstName: "Laura",
                              lastName: "Valentine",
                              taxonomyCode: renderingProviderTaxonomyCode,
                            }
                          } else {
                            therapistInformation = {
                              providerType: "RenderingProvider",
                              npi: renderingProviderNpi,
                              firstName: renderingProviderFirstName,
                              lastName: renderingProviderLastName,
                              taxonomyCode: renderingProviderTaxonomyCode,
                            }
                          }


                          try {
                            subUpdate = {
                              memberId: memberId,
                              paymentResponsibilityLevelCode: 'P',
                              firstName: sub.firstName,
                              lastName: sub.lastName,
                              gender: sub.gender,
                              dateOfBirth: sub.dateOfBirth,
                              address: sub.address,
                            };
                          } catch (error) {
                            subUpdate = {
                              memberId: memberId,
                              paymentResponsibilityLevelCode: 'P',
                              firstName: sub.firstName,
                              lastName: sub.lastName,
                              gender: sub.gender,
                              dateOfBirth: sub.dateOfBirth,
                              address: sub.address,
                            };
                          }

                          const testClaimData = {
                            controlNumber: "*********",
                            tradingPartnerServiceId: Tradingpartner_ServiceId[organizationName],
                            submitter: {
                              organizationName: "Lavni Inc",
                              contactInformation: {
                                name: "Lavni Inc",
                                phoneNumber: "**********",
                              },
                            },
                            receiver: {
                              organizationName: Insurance_company[insuranceCompany],
                            },
                            subscriber: subUpdate,
                            providers: [
                              {
                                providerType: "BillingProvider",
                                npi: "**********",
                                employerId: "*********",
                                taxonomyCode: '251S00000X',
                                organizationName: "Lavni Inc",
                                address: {
                                  address1: "804 South Garnett St.",
                                  city: "Henderson",
                                  state: "NC",
                                  postalCode: "*********"
                                },
                                contactInformation: {
                                  name: "Angela Arrington",
                                  phoneNumber: "**********"
                                },
                              },
                              therapistInformation,
                            ],
                            claimInformation: claimInformation
                          };

                          const claimSubmissionDetails = await submitClaim(testClaimData, insurance?.insuranceAccessToken);

                          if (claimSubmissionDetails?.status == "SUCCESS") {
                            AppLogger.info('claimEligibilityDetails OPEN success 00122', updatedReview);

                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusById(treatmentHistoryId._id, "SUPER_ADMIN");
                            return res.sendSuccess(updatedReview, "Sucess");
                          } else {
                            AppLogger.error('claimSubmissionDetails 004', claimSubmissionDetails)
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimSubmissionDetails?.errors[0]));
                            return res.sendSuccess(updatedReview, "Success");
                          }
                        }
                      } else {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    }
                  }
                }
              }
            } else {
              AppLogger.error('claimEligibilityDetails OPEN 00123', claimEligibilityDetails)

              let meetingCreatedAt = JSON.stringify(eligibilityDataSet[0].meetingDetails);
              let meetingDate = null;

              if (meetingCreatedAt) {
                meetingDate = JSON.parse(meetingCreatedAt);
              }

              const createdAtDate = new Date(meetingDate?.createdAt);
              const previousNoteDetailsData = await TreatmentHistory.findOne({
                clientId: eligibilityDataSet[0].clientId?._id,
                therapistId: eligibilityDataSet[0].therapistId?._id,
                meetingStartedTime: {
                  $gte: new Date(createdAtDate.setUTCHours(0, 0, 0, 0)),
                  $lt: new Date(createdAtDate.setUTCHours(23, 59, 59, 999))
                },
                claimStatus: { $in: ['ACTIVEMD', 'ACTIVE', 'IN_PROGRESS', 'PAID', 'UNPAID'] }
              });


              if (previousNoteDetailsData) {
                return res.sendError(`You have already submitted a diagnosis note for session on this day ${moment.utc(createdAtDate).format("MMM DD YYYY")} with ${eligibilityDataSet[0].clientId.firstname}.`);
              } else {
                let claimEligibilityMDDetails = JSON.stringify(eligibilityDataSet[0]?.clientId?.claimEligibilityMdDetails);
                let claimEligibilityMDData = null;
                if (claimEligibilityMDDetails) {
                  claimEligibilityMDData = JSON.parse(claimEligibilityMDDetails);
                }

                if (claimEligibilityMDData && claimEligibilityMDData?.ins_number?.length > 0) {
                  if (partner[insurancePartner]) {
                    const extractedInfo = {
                      ins_name_f: claimEligibilityMDData.pat_name_f || claimEligibilityMDData.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                      ins_name_l: claimEligibilityMDData.pat_name_l || claimEligibilityMDData.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                      ins_city: claimEligibilityMDData.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                      ins_state: claimEligibilityMDData.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                      ins_zip: claimEligibilityMDData.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                      ins_dob: claimEligibilityMDData.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                      ins_sex: claimEligibilityMDData.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                      ins_addr_1: claimEligibilityMDData.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                      ins_number: claimEligibilityMDData.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                    };

                    let place_of_service;
                    let modifier;

                    if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                      place_of_service = "11";
                    } else {
                      place_of_service = "10";
                      modifier = "95"
                    }

                    const randomKey = generateRandomKey(20);
                    let claimInformationMD: any = {
                      claim_form: 1500,
                      payer_name: Insurance_company[insurancePartner],
                      accept_assign: 'Y',
                      employment_related: 'N',
                      ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                      ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                      ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                      ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                      ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                      ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                      ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                      ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                      ins_number: extractedInfo.ins_number || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                      bill_taxonomy: '251S00000X',
                      place_of_service_1: place_of_service,
                      prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                      prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                      prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                      prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                      pcn: String(randomKey),
                      charge_1: 180,
                      pat_rel: '18',
                      payerid: Tradingpartner_ServiceId[insurancePartner],
                      total_charge: 180,
                      claimNumber: diagnosisNote?.encounterID || '87654321',
                      proc_code_1: diagnosisNote?.cptCode,
                      mod1_1: modifier,
                      diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                      diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                      from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                      bill_name: 'Lavni Inc',
                      bill_addr_1: '804 South Garnett St.',
                      bill_addr_2: '',
                      bill_city: 'Henderson',
                      bill_state: 'NC',
                      bill_zip: '27536',
                      bill_npi: '**********',
                      bill_id: '',
                      bill_phone: '**********',
                      bill_taxid: '*********',
                      bill_taxid_type: 'E',
                      diag_ref_1: "A",
                      units_1: 1,
                      pat_name_f: extractedInfo.ins_name_f,
                      pat_name_l: extractedInfo.ins_name_l,
                      pat_addr_1: extractedInfo.ins_addr_1,
                      pat_city: extractedInfo.ins_city,
                      pat_state: extractedInfo.ins_state,
                      pat_zip: extractedInfo.ins_zip,
                      pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                      pat_sex: extractedInfo.ins_sex,
                    };
                    if (treatmentHistoryId?.meetingStartedTime) {
                      if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                        const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                        await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                        if (saveClaimMd) {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                          return res.sendSuccess(updatedTherapist, "Success");
                        }
                      } else {
                        const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                        const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                        if (claimUploadCSV?.error) {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                          return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                        } else {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                          return res.sendSuccess(updatedTherapist, "Success");
                        }
                      }
                    }
                  }
                } else {
                  if (partner[insurancePartner]) {
                    let eligibility_payload_md = null;

                    if (eligibilityDataSet[0].clientInsuranceDetails) {
                      eligibility_payload_md = {
                        "AccountKey": process.env.CLAIM_MD_ACCOUNT_KEY,
                        "ins_name_f": eligibilityDataSet[0].clientId.firstname,
                        "ins_name_l": eligibilityDataSet[0].clientId.lastname,
                        "payerid": Tradingpartner_ServiceId[insurancePartner],
                        "pat_rel": "18",
                        "fdos": moment.utc(treatmentHistoryId?.meetingStartedTime).format('YYYYMMDD'),
                        "ins_dob": moment.utc(eligibilityDataSet[0].clientId?.dateOfBirth).format('YYYYMMDD'),
                        "ins_sex": eligibilityDataSet[0].clientId?.gender == "Male" ? "M" : "F",
                        "ins_number": subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                        "prov_npi": process.env.LAVNI_NPI,
                      }
                    }
                    
                    const claimEligibilityAndActiveData = await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, eligibilityDataSet[0].clientId._id);

                    if (claimEligibilityAndActiveData?.finalStatus == "ACTIVE") {

                      const eligibilityInfo = claimEligibilityAndActiveData?.eligibilityInfoData;

                      const extractedInfo = {
                        ins_name_f: eligibilityInfo.pat_name_f || eligibilityInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                        ins_name_l: eligibilityInfo.pat_name_l || eligibilityInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                        ins_city: eligibilityInfo.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                        ins_state: eligibilityInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                        ins_zip: eligibilityInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                        ins_dob: eligibilityInfo.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                        ins_sex: eligibilityInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                        ins_addr_1: eligibilityInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                        ins_number: eligibilityInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                      };

                      let place_of_service;
                      let modifier;

                      if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                        place_of_service = "11";
                      } else {
                        place_of_service = "10";
                        modifier = "95"
                      }

                      const randomKey = generateRandomKey(20);

                      let claimInformationMD: any = {
                        claim_form: 1500,
                        payer_name: Insurance_company[insurancePartner],
                        accept_assign: 'Y',
                        employment_related: 'N',
                        ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                        ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                        ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                        ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                        ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                        ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                        ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                        ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                        ins_number: extractedInfo.ins_number || subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                        bill_taxonomy: '251S00000X',
                        place_of_service_1: place_of_service,
                        prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                        prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                        prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                        prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                        pcn: String(randomKey),
                        charge_1: 180,
                        pat_rel: '18',
                        payerid: Tradingpartner_ServiceId[insurancePartner],
                        total_charge: 180,
                        claimNumber: diagnosisNote?.encounterID || '87654321',
                        proc_code_1: diagnosisNote?.cptCode,
                        mod1_1: modifier,
                        diag_1: claim?.diagnosisCode?.replace(/\./g, '') || '',
                        diag_2: claim?.secondaryDiagnosisCodes?.replace(/\./g, '') || '',
                        from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                        bill_name: 'Lavni Inc',
                        bill_addr_1: '804 South Garnett St.',
                        bill_addr_2: '',
                        bill_city: 'Henderson',
                        bill_state: 'NC',
                        bill_zip: '27536',
                        bill_npi: '**********',
                        bill_id: '',
                        bill_phone: '**********',
                        bill_taxid: '*********',
                        bill_taxid_type: 'E',
                        diag_ref_1: "A",
                        units_1: 1,
                        pat_name_f: extractedInfo.ins_name_f,
                        pat_name_l: extractedInfo.ins_name_l,
                        pat_addr_1: extractedInfo.ins_addr_1,
                        pat_city: extractedInfo.ins_city,
                        pat_state: extractedInfo.ins_state,
                        pat_zip: extractedInfo.ins_zip,
                        pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                        pat_sex: extractedInfo.ins_sex,
                      };

                      if (treatmentHistoryId?.meetingStartedTime) {
                        if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                          const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                          await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                          if (saveClaimMd) {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                            return res.sendSuccess(updatedTherapist, "Success");
                          }
                        } else {
                          const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                          const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                          if (claimUploadCSV?.error) {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                            return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                          } else {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "SUPER_ADMIN", claimInformationMD.pcn);
                            return res.sendSuccess(updatedTherapist, "Success");
                          }
                        }
                      }
                    } else {
                      await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, claimEligibilityAndActiveData?.finalError);
                      return res.sendError(claimEligibilityAndActiveData?.finalError);
                    }
                  }
                }
              }
            }
          }
        }
      } else {
        AppLogger.error('Invalid Organization Name')
        let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Invalid Organization Name");
        return res.sendError("Invalid Organization Name");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function viewClaimStauts(req: Request, res: Response, next: NextFunction) {
    const insuranceId = req.params.insuranceId;
    const serviceId = req.params.serviceId;
    const noteId = req.params.noteId;

    if (!mongoose.Types.ObjectId.isValid(insuranceId)) {
      return res.sendError("Invalid insurance Id");
    }

    async function isExpired(token: string | undefined) {
      if (token) {
        try {
          const user = JSON.parse(atob(token.split(".")[1]));

          if (user?.exp * 1000 < Date.now()) {
            return true;
          }

          return false;
        } catch (e) {
          return true;
        }
      } else {
        return true;
      }
    }

    async function getClaimDetails(data: any, token: string) {
      try {
        const claimStatus = await fetch(process.env.CHANGE_HEALTHCARE_CLAIM_STATUS, {
          method: "POST",
          body: JSON.stringify(data),
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });
        return await claimStatus.json();
      } catch (error) {
        return res.sendError("Claim status failed. Please try again later....");
      }
    }

    function generateControlNumber() {
      return Math.floor(********* + Math.random() * *********).toString();
    }

    try {
      let controlNumber = "";

      const insurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceId));

      if (!insurance.controlNumber) {
        controlNumber = generateControlNumber();
        await InsuranceDao.updateInsurancePlan(insuranceId, {
          controlNumber: controlNumber,
        });
      } else {
        controlNumber = insurance?.controlNumber;
      }

      const claimRequestDetails = await InsuranceDao.getDetailsForClaimStatus(Types.ObjectId(noteId));

      const insurancePartner = serviceId as keyof typeof partner;

      let claimStatusRequest = null;

      if (claimRequestDetails) {
        if (claimRequestDetails[0].dependent) {
          claimStatusRequest = {
            controlNumber: controlNumber,
            tradingPartnerServiceId: partner[insurancePartner],
            providers: [
              {
                organizationName: process.env.LAVNI_ORGANIZATION_NAME,
                taxId: process.env.LAVNI_TAXID,
                providerType: "BillingProvider",
              },
              {
                firstName: claimRequestDetails[0].therapistDetails.firstname,
                lastName: claimRequestDetails[0].therapistDetails.lastname,
                npi: claimRequestDetails[0].therapistDetails.nPI1,
                providerType: "ServiceProvider",
              },
            ],
            subscriber: {
              memberId: claimRequestDetails[0].clientInsuranceDetails.subscriber.memberId,
              firstName: claimRequestDetails[0].clientInsuranceDetails.subscriber.firstname,
              lastName: claimRequestDetails[0].clientInsuranceDetails.subscriber.lastName,
              gender: claimRequestDetails[0].clientInsuranceDetails.subscriber.gender ? (claimRequestDetails[0].clientInsuranceDetails.subscriber.gender == "Male" ? "M" : "F") : (claimRequestDetails[0].clientId.gender == "Male" ? "M" : "F"),
              dateOfBirth: moment.utc(claimRequestDetails[0].clientId.dateOfBirth).format("YYYYMMDD"),
            },
            dependent: {
              memberId: claimRequestDetails[0].clientInsuranceDetails.dependent.memberId,
              firstName: claimRequestDetails[0].clientInsuranceDetails.dependent.firstname,
              lastName: claimRequestDetails[0].clientInsuranceDetails.dependent.lastName,
              gender: (claimRequestDetails[0].clientInsuranceDetails.dependent.gender == "Male" ? "M" : "F"),
              dateOfBirth: moment.utc(claimRequestDetails[0].clientInsuranceDetails.dependent.dateOfBirth).format("YYYYMMDD"),
            },
            encounter: {
              beginningDateOfService: moment.utc(claimRequestDetails[0].encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
              endDateOfService: moment.utc(claimRequestDetails[0].encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
              trackingNumber: "ABCD",
            },
          };
        } else {
          claimStatusRequest = {
            controlNumber: controlNumber,
            tradingPartnerServiceId: partner[insurancePartner],
            providers: [
              {
                organizationName: process.env.LAVNI_ORGANIZATION_NAME,
                taxId: process.env.LAVNI_TAXID,
                providerType: "BillingProvider",
              },
              {
                firstName: claimRequestDetails[0].therapistDetails.firstname,
                lastName: claimRequestDetails[0].therapistDetails.lastname,
                npi: claimRequestDetails[0].therapistDetails.nPI1,
                providerType: "ServiceProvider",
              },
            ],
            subscriber: {
              memberId: claimRequestDetails[0].clientInsuranceDetails.subscriber.memberId,
              firstName: claimRequestDetails[0].clientInsuranceDetails.subscriber.firstName,
              lastName: claimRequestDetails[0].clientInsuranceDetails.subscriber.lastName,
              gender: claimRequestDetails[0].clientInsuranceDetails.subscriber.gender ? (claimRequestDetails[0].clientInsuranceDetails.subscriber.gender == "Male" ? "M" : "F") : (claimRequestDetails[0].clientId.gender == "Male" ? "M" : "F"),
              dateOfBirth: moment.utc(claimRequestDetails[0].clientId.dateOfBirth).format("YYYYMMDD"),
            },
            encounter: {
              beginningDateOfService: moment.utc(claimRequestDetails[0].encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
              endDateOfService: moment.utc(claimRequestDetails[0].encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
              trackingNumber: "ABCD",
            },
          };
        }
      }

      const hasExpired = await isExpired(insurance?.insuranceAccessToken);

      let claimStatus = null;

      if (hasExpired) {
        const token: InsuranceToken = {
          client_id: process.env.CHANGE_HEALTHCARE_CLIENT_ID,
          client_secret: process.env.CHANGE_HEALTHCARE_CLIENT_SECRET,
          grant_type: process.env.CHANGE_HEALTHCARE_GRANT_TYPE,
        };

        const newToken = await fetch(process.env.CHANGE_HEALTHCARE_TOKEN_URL, {
          method: "POST",
          body: JSON.stringify(token),
          headers: {
            "Content-Type": "application/json",
          },
        });

        const accessToken: AccessTokenResponse = await newToken.json();

        await InsuranceDao.updateInsurancePlan(insuranceId, {
          insuranceAccessToken: accessToken.access_token,
        });

        claimStatus = await getClaimDetails(claimStatusRequest, accessToken.access_token);

        if (claimStatus.status !== "success") {
          return res.sendError("Failed to validate the claim content.");
        }
      }

      if (!hasExpired) {
        claimStatus = await getClaimDetails(claimStatusRequest, insurance?.insuranceAccessToken);

        if (claimStatus.status !== "success") {
          return res.sendError("Failed to validate the claim content.");
        }
      }

      return res.sendSuccess(claimStatus, "Successfully submitted!");
    } catch (error) {
      return res.sendError(error);
    }
  }
  export async function checkCoPayment(req: Request, res: Response, next: NextFunction) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    try {
      const claimList = await AdminStatisticsDao.getSuccessTeatmentHistoryByAdminTest(limit,
        offset,);

      if (claimList) {
        for (const claim of claimList) {
          const insuranceId = claim?.clientDetails?.insuranceId;
          const serviceId = claim?.clientInsuranceDetails?.insuranceCompanyId;
          const noteId = claim?.diagnosisNoteId;
          const result = await InsuranceEp.checkEligibilityTest(insuranceId, serviceId, noteId)
        }
      }
    } catch (error) {
      return error;
    }

  }

  export async function checkEligibilityTest(insuranceId: any, serviceId: any, noteId: any) {
    if (!mongoose.Types.ObjectId.isValid(insuranceId)) {
      const error = "Invalid insurance Id"
      return error;
    }


    function generateControlNumber() {
      return Math.floor(********* + Math.random() * *********).toString();
    }

    try {
      let controlNumber = "";
      const insurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceId));
      if (!insurance.controlNumber) {
        controlNumber = generateControlNumber();
        await InsuranceDao.updateInsurancePlan(insuranceId, {
          controlNumber: controlNumber,
        });
      } else {
        controlNumber = insurance?.controlNumber;
      }

      const claimRequestDetails = await InsuranceDao.getDetailsForClaimStatus(Types.ObjectId(noteId));
      const currentDate = new Date();

      const insurancePartner = serviceId as keyof typeof partner;

      let eligibility_payload = null;
      eligibility_payload = {
        controlNumber: "*********",
        tradingPartnerServiceId: partner[insurancePartner],
        provider: {
          organizationName: process.env.LAVNI_ORGANIZATION_NAME,
          npi: process.env.LAVNI_NPI,
        },
        subscriber: {
          memberId: claimRequestDetails[0].clientInsuranceDetails.subscriber.memberId,
          firstName: claimRequestDetails[0].clientId?.firstname,
          lastName: claimRequestDetails[0].clientId?.lastname,
          gender: claimRequestDetails[0].clientInsuranceDetails.subscriber.gender ? (claimRequestDetails[0].clientInsuranceDetails.subscriber.gender == "Male" ? "M" : "F") : (claimRequestDetails[0].clientId.gender == "Male" ? "M" : "F"),
          dateOfBirth: moment.utc(claimRequestDetails[0].clientId.dateOfBirth).format("YYYYMMDD"),
        },
        encounter: {
          dateOfService: moment.utc(currentDate).utcOffset(-5 * 60).format('YYYYMMDD'), serviceTypeCodes: ['98']
        }
      };
      const hasExpired = await isExpired(insurance?.insuranceAccessToken);

      if (hasExpired) {
        const token: InsuranceToken = {
          client_id: process.env.CHANGE_HEALTHCARE_CLIENT_ID,
          client_secret: process.env.CHANGE_HEALTHCARE_CLIENT_SECRET,
          grant_type: process.env.CHANGE_HEALTHCARE_GRANT_TYPE,
        };

        const newToken = await fetch(process.env.CHANGE_HEALTHCARE_TOKEN_URL, {
          method: "POST",
          body: JSON.stringify(token),
          headers: {
            "Content-Type": "application/json",
          },
        });

        const accessToken: AccessTokenResponse = await newToken.json();

        await InsuranceDao.updateInsurancePlan(insuranceId, {
          insuranceAccessToken: accessToken.access_token,
        });

        const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, accessToken.access_token);

        let coPaymentClientAmount: any = claimRequestDetails[0]?.clientId?.copaymentAmount
        try {
          if (coPaymentClientAmount > 0 && coPaymentClientAmount !== undefined) {
            const copaymentData = {
              firstName: claimEligibilityDetails?.subscriber.firstName,
              lastName: claimEligibilityDetails?.subscriber.lastName,
              coPaymentValue: coPaymentClientAmount
            }
            const success = { claimStatus: copaymentData, success: "Successfully submitted!" }
            return success;
          }

        } catch (error) {
          AppLogger.info(`B Stripe charge error when co-payment submit:`, error);
        }


      }

      if (!hasExpired) {
        const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, insurance?.insuranceAccessToken);
        let coPaymentClientAmount: any = claimRequestDetails[0]?.clientId?.copaymentAmount

        try {
          if (coPaymentClientAmount > 0 && coPaymentClientAmount !== undefined) {
            const copaymentData = {
              firstName: claimEligibilityDetails?.claimStatus.subscriber.firstName,
              lastName: claimEligibilityDetails?.claimStatus.subscriber.lastName,
              coPaymentValue: coPaymentClientAmount
            }
            const success = { claimStatus: copaymentData, success: "Successfully submitted!" }
            return success;
          }

        } catch (error) {
          AppLogger.info(`B Stripe charge error when co-payment submit:`, error);
        }
      }
    } catch (error) {
      return error;
    }
  }


  export async function viewClaimStautsDaily(insuranceId: any, serviceId: any, noteId: any) {

    if (!mongoose.Types.ObjectId.isValid(insuranceId)) {
      const error = "Invalid insurance Id"
      return error;
    }

    async function isExpired(token: string | undefined) {
      if (token) {
        try {
          const user = JSON.parse(atob(token.split(".")[1]));

          if (user?.exp * 1000 < Date.now()) {
            return true;
          }

          return false;
        } catch (e) {
          return true;
        }
      } else {
        return true;
      }
    }

    async function getClaimDetails(data: any, token: string) {
      try {
        const claimStatus = await fetch(process.env.CHANGE_HEALTHCARE_CLAIM_STATUS, {
          method: "POST",
          body: JSON.stringify(data),
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });
        return await claimStatus.json();
      } catch (error) {
        const error2 = "Claim status failed. Please try again later...."
        return error2;
      }
    }

    function generateControlNumber() {
      return Math.floor(********* + Math.random() * *********).toString();
    }

    try {
      let controlNumber = "";

      const insurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceId));


      if (!insurance.controlNumber) {
        controlNumber = generateControlNumber();
        await InsuranceDao.updateInsurancePlan(insuranceId, {
          controlNumber: controlNumber,
        });
      } else {
        controlNumber = insurance?.controlNumber;
      }

      const claimRequestDetails = await InsuranceDao.getDetailsForClaimStatus(Types.ObjectId(noteId));
      const insurancePartner = serviceId as keyof typeof partner;

      let claimStatusRequest = null;
      const treatmentHistoryId = await TherapistDao.viewTreatmentHistorysByMeetingId(claimRequestDetails[0].meetingDetails?._id);
      if (claimRequestDetails) {
        if (claimRequestDetails[0].clientInsuranceDetails) {
          claimStatusRequest = {
            controlNumber: controlNumber,
            tradingPartnerServiceId: partner[insurancePartner],
            providers: [
              {
                organizationName: process.env.LAVNI_ORGANIZATION_NAME,
                taxId: process.env.LAVNI_TAXID,
                providerType: "BillingProvider",
              },
              {
                firstName: claimRequestDetails[0].therapistDetails.firstname,
                lastName: claimRequestDetails[0].therapistDetails.lastname,
                npi: claimRequestDetails[0].therapistDetails.nPI1,
                providerType: "ServiceProvider",
              },
            ],
            subscriber: {
              memberId: claimRequestDetails[0].clientInsuranceDetails.subscriber.memberId,
              firstName: claimRequestDetails[0].clientInsuranceDetails.subscriber.firstName,
              lastName: claimRequestDetails[0].clientInsuranceDetails.subscriber.lastName,
              gender: claimRequestDetails[0].clientInsuranceDetails.subscriber.gender ? (claimRequestDetails[0].clientInsuranceDetails.subscriber.gender == "Male" ? "M" : "F") : (claimRequestDetails[0].clientId.gender == "Male" ? "M" : "F"),
              dateOfBirth: moment.utc(claimRequestDetails[0].clientId.dateOfBirth).format("YYYYMMDD"),
            },
            encounter: {
              beginningDateOfService: moment.utc(claimRequestDetails[0].encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
              endDateOfService: moment.utc(claimRequestDetails[0].encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
              trackingNumber: "ABCD",
            },
          };
        } else {
          const error3 = "No clientInsuranceDetails."
          return error3;
        }
      }

      const hasExpired = await isExpired(insurance?.insuranceAccessToken);

      let claimStatus = null;

      if (hasExpired) {
        const token: InsuranceToken = {
          client_id: process.env.CHANGE_HEALTHCARE_CLIENT_ID,
          client_secret: process.env.CHANGE_HEALTHCARE_CLIENT_SECRET,
          grant_type: process.env.CHANGE_HEALTHCARE_GRANT_TYPE,
        };

        const newToken = await fetch(process.env.CHANGE_HEALTHCARE_TOKEN_URL, {
          method: "POST",
          body: JSON.stringify(token),
          headers: {
            "Content-Type": "application/json",
          },
        });

        const accessToken: AccessTokenResponse = await newToken.json();

        await InsuranceDao.updateInsurancePlan(insuranceId, {
          insuranceAccessToken: accessToken.access_token,
        });

        claimStatus = await getClaimDetails(claimStatusRequest, accessToken.access_token);

        if (claimStatus.status !== "success") {
          const error3 = "Failed to validate the claim content."
          return error3;
        }
      }

      if (!hasExpired) {
        claimStatus = await getClaimDetails(claimStatusRequest, insurance?.insuranceAccessToken);

        if (claimStatus.status !== "success") {
          const error3 = "Failed to validate the claim content."
          return error3;
        } else {
          if (parseFloat(claimStatus?.claims[0]?.claimStatus?.amountPaid) > 0) {
            let updatedTherapist = await TherapistDao.updateToPaidTreatmentHistoryStatusById(treatmentHistoryId._id);
            return "Claim status update, claim status success!";
          }
        }
      }

      const success = { claimStatus: claimStatus, success: "Successfully submitted!" }
      return success;
    } catch (error) {
      return error;
    }
  }

  export async function getClaimDetails(req: Request, res: Response) {
    const noteId = req.params.noteId;

    if (!mongoose.Types.ObjectId.isValid(noteId)) {
      return res.sendError("Invalid note Id");
    }

    try {
      const claimDetails = await InsuranceDao.getDetailsForClaimSubmission(Types.ObjectId(noteId));
      return res.sendSuccess(claimDetails, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function addInsuranceInfoByAdmin(req: Request, res: Response, next: NextFunction) {
    const userId = req.params.userId;
    let uploadCategory = UploadCategory.INSURANCE_CARD;
    let uploadInsuranceCategory = UploadCategory.INSURANCE_CARD_BACK;
    let newInsuranceTag: ObjectId;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateTherapisProfileValidationRules(req, cb);
      },
    });

    async function updateTherapisProfileValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let insuranceDetails = JSON.parse(req.body.insuranceDetails);

        if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
          return cb(Error("Deleting insurance card front image id is required."), null);
        }

        if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
          return cb(Error("Deleting insurance card back image id is required."), null);
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) => cb(error, "destination"));
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    async function deleteOldFiles(uploadId: string) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldFile = await UploadDao.getUpload(uploadId);
        await fs.unlink(oldFile.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }
      return isDeleted;
    }

    const upload = multer({ storage: storage }).fields([{ name: "insuranceCardFront" }, { name: "insuranceCardBack" }]);
    const clientData = await ClientDao.getUserById(
      Types.ObjectId(userId)
    );


    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        let isValid = true;
        let isFileDeleted = false;
        const upload: any = req.files
        if (upload.insuranceCardFront || upload.insuranceCardBack) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedFrontImage = null;
          let uploadedBackImage = null;

          if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
            isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

            if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
          }

          if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
            isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

            if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
          }

          const upload: any = req.files;

          let signRequired: boolean = false;
          if (req.body.signRequired !== undefined) {
            signRequired = req.body.signRequired;
          }

          if (upload.insuranceCardFront) {
            const insuranceCard = {
              userId: userId as unknown as Types.ObjectId,
              originalName: upload.insuranceCardFront[0].originalname.replace(/ /g, ""),
              name: upload.insuranceCardFront[0].filename,
              type: upload.insuranceCardFront[0].mimetype,
              path: upload.insuranceCardFront[0].path,
              fileSize: upload.insuranceCardFront[0].size,
              extension: path.extname(upload.insuranceCardFront[0].originalname) || req.body.extension,
              category: uploadCategory,
              signRequired: signRequired,
            };

            uploadedFrontImage = await UploadDao.createUpload(insuranceCard);
            if (!uploadedFrontImage) {
              return res.sendError("Error while uploading insurance front image.");
            }
          }

          if (upload.insuranceCardBack) {
            const insuranceCard = {
              userId: userId as unknown as Types.ObjectId,
              originalName: upload.insuranceCardBack[0].originalname.replace(/ /g, ""),
              name: upload.insuranceCardBack[0].filename,
              type: upload.insuranceCardBack[0].mimetype,
              path: upload.insuranceCardBack[0].path,
              fileSize: upload.insuranceCardBack[0].size,
              extension: path.extname(upload.insuranceCardBack[0].originalname) || req.body.extension,
              category: uploadInsuranceCategory,
              signRequired: signRequired,
            };

            uploadedBackImage = await UploadDao.createUpload(insuranceCard);
            if (!uploadedBackImage) {
              return res.sendError("Error while uploading insurance back image.");
            }

          }

          if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
            const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
            if (isFound) {
              // const addedTag = await AdminDao.addInsuranceCompany(insuranceDetails.insuranceCompanyId);
              newInsuranceTag = insuranceDetails.insuranceCompanyId;
            }
            // else {
            //   newInsuranceTag = isFound.insuranceCompany;
            // }
          }

          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: uploadedFrontImage?._id ? uploadedFrontImage?._id : null,
            insuranceCardBackId: uploadedBackImage?._id ? uploadedBackImage?._id : null,
          };
          const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));

          const user = await AdminDao.getUserById(Types.ObjectId(userId));
          const previousCopaymentAmount = user.copaymentAmount;


          if (insuranceCompanyData) {
            const updatedClient = await ClientDao.updateClientCoPayment(Types.ObjectId(userId), insuranceCompanyData?.coPayment)

            let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(Types.ObjectId(userId), insuranceCompanyData?.coPayment, previousCopaymentAmount);
          }
          const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)

          let updatedInsurance = await InsuranceDao.addInsuranceInfo(Types.ObjectId(userId), insurance);

          if (updatedInsurance) {
            clientData.premiumStatus = PremiumStatus.ACTIVE
          }
          clientData.save();
          if (!updatedInsurance) {
            return res.sendError("Failed to update the insurance details.");
          }

          if (resData && 'error' in resData && resData.error === "error") {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          } else {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }
        } else {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardId is required.");
          }

          if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardId is required.");
          }

          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }
          }
          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                // const addedTag = await AdminDao.addInsuranceCompany(insuranceDetails.insuranceCompanyId);
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
              // else {
              //   newInsuranceTag = isFound.insuranceCompany;
              // }
            }
          }
          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: insuranceDetails.insuranceCardId ? insuranceDetails.insuranceCardId : null,
            insuranceCardBackId: insuranceDetails.insuranceCardBackId ? insuranceDetails.insuranceCardBackId : null,
          };
          const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));

          const user = await AdminDao.getUserById(Types.ObjectId(userId));
          const previousCopaymentAmount = user.copaymentAmount;


          if (insuranceCompanyData) {
            const updatedClient = await ClientDao.updateClientCoPayment(Types.ObjectId(userId), insuranceCompanyData?.coPayment)

            let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(Types.ObjectId(userId), insuranceCompanyData?.coPayment, previousCopaymentAmount);
          }
          const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)
          let updatedInsurance = await InsuranceDao.addInsuranceInfo(Types.ObjectId(userId), insurance);
          if (updatedInsurance) {
            clientData.premiumStatus = PremiumStatus.ACTIVE
          }
          clientData.save();
          if (!updatedInsurance) {
            return res.sendError("Failed to update the therapist.");
          }
          if (resData && 'error' in resData && resData.error === "error" && resData.error === "error") {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          } else {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }


  export async function submitPendingClaimMd(claimMd: any) {
    const claim = claimMd;
    try {
      let claimInformationMD: any = {
        claim_form: claim?.claim_form,
        payer_name: claim?.payer_name,
        accept_assign: claim?.accept_assign,
        employment_related: claim?.employment_related,
        ins_name_f: claim?.ins_name_f,
        ins_name_l: claim?.ins_name_l,
        ins_addr_1: claim?.ins_addr_1,
        ins_city: claim?.ins_city,
        ins_dob: claim?.ins_dob,
        ins_sex: claim?.ins_sex,
        ins_state: claim?.ins_state,
        ins_zip: claim?.ins_zip,
        ins_number: claim?.ins_number,
        bill_taxonomy: claim?.bill_taxonomy,
        place_of_service_1: claim?.place_of_service_1,
        prov_name_l: claim?.prov_name_l,
        prov_name_f: claim?.prov_name_f,
        prov_npi: claim?.prov_npi,
        prov_taxonomy: claim?.prov_taxonomy,
        pcn: claim?.pcn,
        charge_1: claim?.charge_1,
        pat_rel: claim?.pat_rel,
        payerid: claim?.payerid,
        total_charge: claim?.total_charge,
        claimNumber: claim?.claimNumber,
        proc_code_1: claim?.proc_code_1,
        mod1_1: claim?.mod1_1,
        diag_1: claim?.diag_1,
        diag_2: claim?.diag_2,
        from_date_1: claim?.from_date_1,
        bill_name: claim?.bill_name,
        bill_addr_1: claim?.bill_addr_1,
        bill_addr_2: claim?.bill_addr_2,
        bill_city: claim?.bill_city,
        bill_state: claim?.bill_state,
        bill_zip: claim?.bill_zip,
        bill_npi: claim?.bill_npi,
        bill_id: claim?.bill_id,
        bill_phone: claim?.bill_phone,
        bill_taxid: claim?.bill_taxid,
        bill_taxid_type: claim?.bill_taxid_type,
        diag_ref_1: claim?.diag_ref_1,
        units_1: claim?.units_1,
        pat_name_l: claim?.pat_name_l,
        pat_name_f: claim?.pat_name_f,
        pat_addr_1: claim?.pat_addr_1,
        pat_city: claim?.pat_city,
        pat_state: claim?.pat_state,
        pat_zip: claim?.pat_zip,
        pat_dob: claim?.pat_dob,
        pat_sex: claim?.pat_sex,
      };

      const outputFilePath = `${claim?.treatmentHistoryId}output.csv`;
      const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

      if (claimUploadCSV?.error) {
        const data = {
          status: "FAILED"
        }
        AppLogger.info('.::Claim MD update automated for claim ID FAILED: ::.' + claim?._id)
        let updatedTherapist = await AdminDao.updateUnsubmittedClaimsMd(claim._id, data.status);
      } else {
        const data = {
          status: "SUBMITTED"
        }
        AppLogger.info('.::Claim MD update automated for claim ID SUCCESS: ::.' + claim?._id)
        let updatedClaim = await TherapistDao.updateTreatmentHistoryStatusByIdMD(claim?.treatmentHistoryId, "Cronjob", claimInformationMD.pcn);
        let updatedTherapist = await AdminDao.updateUnsubmittedClaimsMd(claim._id, data.status);

      }
    } catch (error) {
      AppLogger.info(`Error: `, error);
    }
  }

  export async function reSubmitPendingClaims(treatment: any, res: Response) {
    const insuranceId = treatment?.clientId?.insuranceId?._id;
    const diagnosisNoteId = treatment?.diagnosisNoteId?._id;
    const submitterOrganizationName = treatment?.clientId?.insuranceId?.insuranceCompanyId?.insuranceCompany;

    const subscriber = treatment?.clientId?.insuranceId?.subscriber;
    const renderingProviderNpi = treatment?.therapistId?.nPI1;
    const renderingProviderFirstName = treatment?.therapistId?.firstname;
    const renderingProviderLastName = treatment?.therapistId?.lastname;
    const renderingProviderTaxonomyCode = treatment?.therapistId?.taxonomyCode;

    if (!mongoose.Types.ObjectId.isValid(insuranceId)) {
      return res.sendError("Invalid insurance Id");
    }

    function generateControlNumber() {
      return Math.floor(********* + Math.random() * *********).toString();
    }

    try {
      let controlNumber = "";
      const insurance = await InsuranceDao.getInsurancePlanById(Types.ObjectId(insuranceId));

      if (!insurance.controlNumber) {
        controlNumber = generateControlNumber();
        await InsuranceDao.updateInsurancePlan(insuranceId, {
          controlNumber: controlNumber,
        });
      } else {
        controlNumber = insurance?.controlNumber;
      }

      const eligibilityDataSet = await InsuranceDao.getDetailsForClaimStatus(Types.ObjectId(diagnosisNoteId));

      const treatmentHistoryId = await TherapistDao.viewTreatmentHistorysByMeetingId(eligibilityDataSet[0].meetingDetails?._id);

      const transactionData = await TherapistDao.viewTransactionsByMeetingId(eligibilityDataSet[0].meetingDetails?._id);

      const insurancePartner = submitterOrganizationName as keyof typeof partner;
      let meetingCreatedAt = JSON.stringify(eligibilityDataSet[0].meetingDetails);
      let meetingDate = null;
      if (meetingCreatedAt) {
        meetingDate = JSON.parse(meetingCreatedAt);
      }

      const createdAtDate = new Date(meetingDate?.createdAt);
      const previousNoteDetailsData = await TreatmentHistory.findOne({
        clientId: eligibilityDataSet[0]?.clientId?._id,
        therapistId: eligibilityDataSet[0]?.therapistId?._id,
        meetingStartedTime: {
          $gte: new Date(createdAtDate.setUTCHours(0, 0, 0, 0)),
          $lt: new Date(createdAtDate.setUTCHours(23, 59, 59, 999))
        },
        claimStatus: { $in: ['ACTIVEMD', 'ACTIVE', 'IN_PROGRESS', 'PAID', 'UNPAID'] }
      });

      if (previousNoteDetailsData) {
        return res.sendError(`You have already submitted a diagnosis note for session on this day ${moment.utc(createdAtDate).format("MMM DD YYYY")} with ${eligibilityDataSet[0]?.clientId?.firstname}.`);
      } else {
        if (partner[insurancePartner]) {
          let eligibility_payload = null;

          if (eligibilityDataSet[0]?.clientInsuranceDetails?.dependent?.memberId) {
            eligibility_payload = {
              controlNumber: "*********",
              tradingPartnerServiceId: partner[insurancePartner],
              provider: {
                organizationName: process.env.LAVNI_ORGANIZATION_NAME,
                npi: process.env.LAVNI_NPI,
              },
              subscriber: {
                memberId: subscriber?.memberId,
                firstName: eligibilityDataSet[0]?.clientId?.firstname,
                lastName: eligibilityDataSet[0]?.clientId?.lastname,
                gender: (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                dateOfBirth: moment.utc(eligibilityDataSet[0]?.clientId?.dateOfBirth).format("YYYYMMDD"),
              },
              dependent: {
                memberId: eligibilityDataSet[0].clientInsuranceDetails.dependent.memberId,
                firstName: eligibilityDataSet[0].clientInsuranceDetails.dependent.firstName,
                lastName: eligibilityDataSet[0].clientInsuranceDetails.dependent.lastName,
                gender: (eligibilityDataSet[0].clientInsuranceDetails.dependent.gender == "Male" ? "M" : "F"),
                dateOfBirth: moment.utc(eligibilityDataSet[0].clientInsuranceDetails.dependent.dateOfBirth).format("YYYYMMDD"),
              },
              encounter: {
                dateOfService: moment.utc(treatment?.meetingStartedTime).utcOffset(-5 * 60).format("YYYYMMDD"),
                serviceTypeCodes: ["98"]
              },
            };
          } else {
            eligibility_payload = {
              controlNumber: "*********",
              tradingPartnerServiceId: partner[insurancePartner],
              provider: {
                organizationName: process.env.LAVNI_ORGANIZATION_NAME,
                npi: process.env.LAVNI_NPI,
              },
              subscriber: {
                memberId: subscriber?.memberId,
                firstName: eligibilityDataSet[0]?.clientId?.firstname,
                lastName: eligibilityDataSet[0]?.clientId?.lastname,
                gender: (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                dateOfBirth: moment.utc(eligibilityDataSet[0]?.clientId?.dateOfBirth).format("YYYYMMDD"),
              },
              encounter: {
                dateOfService: moment.utc(treatment?.meetingStartedTime).utcOffset(-5 * 60).format("YYYYMMDD"),
                serviceTypeCodes: ["98"]
              }
            };
          }


          const diagnosisNote = await VideoCallDao.getDiagnosisNoteByIdWithOutPopulateFunction(Types.ObjectId(diagnosisNoteId));

          const organizationName = submitterOrganizationName as keyof typeof Tradingpartner_ServiceId;

          const insuranceCompany = submitterOrganizationName as keyof typeof Insurance_company;

          const hasExpired = await isExpired(insurance?.insuranceAccessToken);

          if (hasExpired) {
            const token: InsuranceToken = {
              client_id: process.env.CHANGE_HEALTHCARE_CLIENT_ID,
              client_secret: process.env.CHANGE_HEALTHCARE_CLIENT_SECRET,
              grant_type: process.env.CHANGE_HEALTHCARE_GRANT_TYPE,
            };

            const newToken = await fetch(process.env.CHANGE_HEALTHCARE_TOKEN_URL, {
              method: "POST",
              body: JSON.stringify(token),
              headers: {
                "Content-Type": "application/json",
              },
            });

            const accessToken: AccessTokenResponse = await newToken.json();

            await InsuranceDao.updateInsurancePlan(insuranceId, {
              insuranceAccessToken: accessToken.access_token,
            });

            let claimEligibilityDetailsOBJ = JSON.stringify(eligibilityDataSet[0]?.clientId?.claimEligibilityDetails);
            let claimEligibilityDetailsData = null;
            if (claimEligibilityDetailsOBJ) {
              claimEligibilityDetailsData = JSON.parse(claimEligibilityDetailsOBJ);
            }

            if (claimEligibilityDetailsData && claimEligibilityDetailsData?.memberId?.length > 0) {
              const updatedReview = await AdminDao.getMeetingStatusById(eligibilityDataSet[0].meetingDetails._id);

              if (partner[insurancePartner]) {
                const extractedInfo = {
                  ins_name_f: claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                  ins_name_l: claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                  ins_city: claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city || 'Unknown',
                  ins_state: claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                  ins_zip: claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                  ins_dob: moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                  ins_sex: claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                  ins_addr_1: claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                };

                let place_of_service;
                let modifier;

                if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan ") {
                  place_of_service = "11";
                } else {
                  place_of_service = "10";
                  modifier = "95"
                }
                const randomKey = generateRandomKey(20);
                let claimInformationMD: any = {
                  claim_form: 1500,
                  payer_name: Insurance_company[insurancePartner],
                  accept_assign: 'Y',
                  employment_related: 'N',
                  ins_name_f: extractedInfo.ins_name_f || claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname,
                  ins_name_l: extractedInfo.ins_name_l || claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname,
                  ins_addr_1: extractedInfo.ins_addr_1 || claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress,
                  ins_city: extractedInfo.ins_city || claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city,
                  ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                  ins_sex: extractedInfo.ins_sex || claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                  ins_state: extractedInfo.ins_state || claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                  ins_zip: extractedInfo.ins_zip || claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode,
                  ins_number: claimEligibilityDetailsData?.subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                  bill_taxonomy: '251S00000X',
                  place_of_service_1: place_of_service,
                  prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                  prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                  prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                  prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                  pcn: String(randomKey),
                  charge_1: 180,
                  pat_rel: '18',
                  payerid: Tradingpartner_ServiceId[insurancePartner],
                  total_charge: 180,
                  claimNumber: diagnosisNote?.encounterID || '87654321',
                  proc_code_1: diagnosisNote?.cptCode,
                  mod1_1: modifier,
                  diag_1: diagnosisNote?.diagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                  diag_2: diagnosisNote?.secondaryDiagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                  from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                  bill_name: 'Lavni Inc',
                  bill_addr_1: '804 South Garnett St.',
                  bill_addr_2: '',
                  bill_city: 'Henderson',
                  bill_state: 'NC',
                  bill_zip: '27536',
                  bill_npi: '**********',
                  bill_id: '',
                  bill_phone: '**********',
                  bill_taxid: '*********',
                  bill_taxid_type: 'E',
                  diag_ref_1: "A",
                  units_1: 1,
                  pat_name_f: extractedInfo.ins_name_f,
                  pat_name_l: extractedInfo.ins_name_l,
                  pat_addr_1: extractedInfo.ins_addr_1,
                  pat_city: extractedInfo.ins_city,
                  pat_state: extractedInfo.ins_state,
                  pat_zip: extractedInfo.ins_zip,
                  pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                  pat_sex: extractedInfo.ins_sex,
                };
                if (treatmentHistoryId?.meetingStartedTime) {
                  if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                    const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                    await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                    if (saveClaimMd) {
                      return res.sendSuccess(updatedReview, "Success");
                    }
                  } else {
                    const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                    const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                    if (claimUploadCSV?.error) {
                      AppLogger.error('claimUploadCSV 005', claimUploadCSV?.error?.error_mesg);
                      let claimInformation: any = {
                        claimFilingCode: "CI",
                        patientControlNumber: diagnosisNote?.patientID,
                        claimChargeAmount: "175",
                        placeOfServiceCode: place_of_service,
                        claimFrequencyCode: "1",
                        signatureIndicator: "Y",
                        planParticipationCode: "A",
                        benefitsAssignmentCertificationIndicator: "Y",
                        releaseInformationCode: "Y",
                        claimSupplementalInformation: {
                          repricedClaimNumber: "00001",
                          claimNumber: diagnosisNote?.encounterID,
                        },
                        serviceFacilityLocation: {
                          organizationName: "Lavni Inc",
                          address: {
                            address1: "804 South Garnett st.",
                            city: "Henderson",
                            state: "NC",
                            postalCode: "27536",
                          },
                        },
                        serviceLines: [
                          {
                            serviceDate: moment(diagnosisNote.encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
                            professionalService: {
                              procedureIdentifier: "HC",
                              lineItemChargeAmount: "175",
                              procedureCode: treatment?.diagnosisNoteId?.cptCode,
                              measurementUnit: "UN",
                              serviceUnitCount: "1",
                              placeOfServiceCode: "10",
                              description: 'mental health',
                              compositeDiagnosisCodePointers: { "diagnosisCodePointers": ["1"] },
                            },
                          },
                        ],
                      };

                      if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                        claimInformation.serviceLines[0].professionalService.procedureModifiers = [""];
                      } else {
                        claimInformation.serviceLines[0].professionalService.procedureModifiers = ["95"];
                      }


                      if (treatment?.secondaryDiagnosisICDcodes && treatment?.secondaryDiagnosisICDcodes.length > 0) {
                        claimInformation.healthCareCodeInformation = [
                          {
                            diagnosisTypeCode: "ABK",
                            diagnosisCode: treatment?.diagnosisICDcodes.replace(/\./g, ''),
                          },
                          {
                            diagnosisTypeCode: "ABF",
                            diagnosisCode: treatment?.secondaryDiagnosisICDcodes.replace(/\./g, '')
                          }
                        ]
                      } else {
                        if (treatment?.diagnosisICDcodes && treatment?.diagnosisICDcodes.length > 0) {
                          claimInformation.healthCareCodeInformation = [
                            {
                              diagnosisTypeCode: "ABK",
                              diagnosisCode: treatment?.diagnosisICDcodes.replace(/\./g, ''),
                            }
                          ];
                        } else {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Empty secondaryDiagnosisICDcodes or diagnosisICDcodes");
                        }
                      }

                      const identity = [];
                      if ('dependents' in claimEligibilityDetailsData) {
                        identity.push('dependents');
                      } else {
                        identity.push('subscriber');
                      }
                      const sub = identity[0] === 'subscriber' ? claimEligibilityDetailsData.subscriber : claimEligibilityDetailsData.dependents[0];
                      const memberId = claimEligibilityDetailsData.subscriber.memberId;
                      let subUpdate;


                      let therapistInformation: any = {}

                      if (renderingProviderFirstName == "Brittany" && renderingProviderLastName == "Schopler") {
                        therapistInformation = {
                          providerType: "RenderingProvider",
                          npi: renderingProviderNpi,
                          firstName: "Laura",
                          lastName: "Valentine",
                          taxonomyCode: renderingProviderTaxonomyCode,
                        }
                      } else {
                        therapistInformation = {
                          providerType: "RenderingProvider",
                          npi: renderingProviderNpi,
                          firstName: renderingProviderFirstName,
                          lastName: renderingProviderLastName,
                          taxonomyCode: renderingProviderTaxonomyCode,
                        }
                      }

                      try {
                        subUpdate = {
                          memberId: memberId,
                          paymentResponsibilityLevelCode: 'P',
                          firstName: sub.firstName,
                          lastName: sub.lastName,
                          gender: sub.gender,
                          dateOfBirth: sub.dateOfBirth,
                          address: sub.address,
                        };
                      } catch (error) {
                        subUpdate = {
                          memberId: memberId,
                          paymentResponsibilityLevelCode: 'P',
                          firstName: sub.firstName,
                          lastName: sub.lastName,
                          gender: sub.gender,
                          dateOfBirth: sub.dateOfBirth,
                          address: sub.address,
                        };
                      }

                      const testClaimData = {
                        controlNumber: "*********",
                        tradingPartnerServiceId: Tradingpartner_ServiceId[organizationName],
                        submitter: {
                          organizationName: "Lavni Inc",
                          contactInformation: {
                            name: "Lavni Inc",
                            phoneNumber: "**********",
                          },
                        },
                        receiver: {
                          organizationName: Insurance_company[insuranceCompany],
                        },
                        subscriber: subUpdate,
                        providers: [
                          {
                            providerType: "BillingProvider",
                            npi: "**********",
                            employerId: "*********",
                            taxonomyCode: '251S00000X',
                            organizationName: "Lavni Inc",
                            address: {
                              address1: "804 South Garnett St.",
                              city: "Henderson",
                              state: "NC",
                              postalCode: "*********"
                            },
                            contactInformation: {
                              name: "Angela Arrington",
                              phoneNumber: "**********"
                            },
                          },
                          therapistInformation
                        ],
                        claimInformation: claimInformation
                      };

                      const claimSubmissionDetails = await submitClaim(testClaimData, accessToken.access_token);

                      if (claimSubmissionDetails?.status == "SUCCESS") {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusById(treatmentHistoryId._id, "claimOpen");
                        return res.sendSuccess(updatedReview, "Sucess");
                      } else {
                        AppLogger.error('claimSubmissionDetails 005', claimSubmissionDetails)
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimSubmissionDetails?.errors[0]));
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    } else {
                      let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                      return res.sendSuccess(updatedReview, "Success");
                    }
                  }
                }
              }


            } else {
              const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, accessToken.access_token);

              if (!(claimEligibilityDetails?.errors?.length > 0)) {
                const updatedReview = await AdminDao.getMeetingStatusById(eligibilityDataSet[0].meetingDetails._id);

                const identity = [];

                if ('dependents' in claimEligibilityDetails) {
                  identity.push('dependents');
                } else {
                  identity.push('subscriber');
                }

                const eligibilityData = {
                  subscriber: identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0],
                  memberId: claimEligibilityDetails.subscriber.memberId,
                }

                let updatedClientEligibility = await ClientDao.updateClientEligibility(eligibilityDataSet[0].clientId?._id, eligibilityData);
                
                AppLogger.info(`.:: Client Eligibility Update, Client Name : ` + eligibilityDataSet[0].clientId?.firstname + `.:: eligibilityData : ` + eligibilityData);

                if (partner[insurancePartner]) {
                  const extractedInfo = {
                    ins_name_f: claimEligibilityDetails?.subscriber?.firstName || updatedClientEligibility?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                    ins_name_l: claimEligibilityDetails?.subscriber?.lastName || updatedClientEligibility?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                    ins_city: claimEligibilityDetails?.subscriber?.address?.city || updatedClientEligibility?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city || 'Unknown',
                    ins_state: claimEligibilityDetails?.subscriber?.address?.state || updatedClientEligibility?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                    ins_zip: claimEligibilityDetails?.subscriber?.address?.postalCode || updatedClientEligibility?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                    ins_dob: moment(claimEligibilityDetails?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(updatedClientEligibility?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                    ins_sex: claimEligibilityDetails?.subscriber?.gender || updatedClientEligibility?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                    ins_addr_1: claimEligibilityDetails?.subscriber?.address?.address1 || updatedClientEligibility?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                  };

                  let place_of_service;
                  let modifier;

                  if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan ") {
                    place_of_service = "11";
                  } else {
                    place_of_service = "10";
                    modifier = "95"
                  }

                  const randomKey = generateRandomKey(20);

                  let claimInformationMD: any = {
                    claim_form: 1500,
                    payer_name: Insurance_company[insurancePartner],
                    accept_assign: 'Y',
                    employment_related: 'N',
                    ins_name_f: extractedInfo.ins_name_f || claimEligibilityDetails?.subscriber?.firstName || updatedClientEligibility?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname,
                    ins_name_l: extractedInfo.ins_name_l || claimEligibilityDetails?.subscriber?.lastName || updatedClientEligibility?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname,
                    ins_addr_1: extractedInfo.ins_addr_1 || claimEligibilityDetails?.subscriber?.address?.address1 || updatedClientEligibility?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress,
                    ins_city: extractedInfo.ins_city || claimEligibilityDetails?.subscriber?.address?.city || updatedClientEligibility?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city,
                    ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || claimEligibilityDetails?.subscriber?.dateOfBirth || moment(updatedClientEligibility?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                    ins_sex: extractedInfo.ins_sex || claimEligibilityDetails?.subscriber?.gender || updatedClientEligibility?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                    ins_state: extractedInfo.ins_state || claimEligibilityDetails?.subscriber?.address?.state || updatedClientEligibility?.subscriber?.address?.state || '' || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState],
                    ins_zip: extractedInfo.ins_zip || claimEligibilityDetails?.subscriber?.address?.postalCode || updatedClientEligibility?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode,
                    ins_number: eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || claimEligibilityDetails?.subscriber?.memberId || claimEligibilityDetailsData?.subscriber?.memberId,
                    bill_taxonomy: '251S00000X',
                    place_of_service_1: place_of_service,
                    prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                    prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                    prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                    prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                    pcn: String(randomKey),
                    charge_1: 180,
                    pat_rel: '18',
                    payerid: Tradingpartner_ServiceId[insurancePartner],
                    total_charge: 180,
                    claimNumber: diagnosisNote?.encounterID || '87654321',
                    proc_code_1: diagnosisNote?.cptCode,
                    mod1_1: modifier,
                    diag_1: diagnosisNote?.diagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                    diag_2: diagnosisNote?.secondaryDiagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                    from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                    bill_name: 'Lavni Inc',
                    bill_addr_1: '804 South Garnett St.',
                    bill_addr_2: '',
                    bill_city: 'Henderson',
                    bill_state: 'NC',
                    bill_zip: '27536',
                    bill_npi: '**********',
                    bill_id: '',
                    bill_phone: '**********',
                    bill_taxid: '*********',
                    bill_taxid_type: 'E',
                    diag_ref_1: "A",
                    units_1: 1,
                    pat_name_f: extractedInfo.ins_name_f,
                    pat_name_l: extractedInfo.ins_name_l,
                    pat_addr_1: extractedInfo.ins_addr_1,
                    pat_city: extractedInfo.ins_city,
                    pat_state: extractedInfo.ins_state,
                    pat_zip: extractedInfo.ins_zip,
                    pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                    pat_sex: extractedInfo.ins_sex,
                  };
                  if (treatmentHistoryId?.meetingStartedTime) {
                    if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                      const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                      await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                      if (saveClaimMd) {
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    } else {
                      const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                      const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                      if (claimUploadCSV?.error) {
                        AppLogger.error('claimUploadCSV 006', claimUploadCSV?.error?.error_mesg);
                        let claimInformation: any = {
                          claimFilingCode: "CI",
                          patientControlNumber: diagnosisNote?.patientID,
                          claimChargeAmount: "175",
                          placeOfServiceCode: place_of_service,
                          claimFrequencyCode: "1",
                          signatureIndicator: "Y",
                          planParticipationCode: "A",
                          benefitsAssignmentCertificationIndicator: "Y",
                          releaseInformationCode: "Y",
                          claimSupplementalInformation: {
                            repricedClaimNumber: "00001",
                            claimNumber: diagnosisNote?.encounterID,
                          },
                          serviceFacilityLocation: {
                            organizationName: "Lavni Inc",
                            address: {
                              address1: "804 South Garnett st.",
                              city: "Henderson",
                              state: "NC",
                              postalCode: "27536",
                            },
                          },
                          serviceLines: [
                            {
                              serviceDate: moment(diagnosisNote.encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
                              professionalService: {
                                procedureIdentifier: "HC",
                                lineItemChargeAmount: "175",
                                procedureCode: treatment?.diagnosisNoteId?.cptCode,
                                measurementUnit: "UN",
                                serviceUnitCount: "1",
                                placeOfServiceCode: "10",
                                description: 'mental health',
                                compositeDiagnosisCodePointers: { "diagnosisCodePointers": ["1"] },
                              },
                            },
                          ],
                        };

                        if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                          claimInformation.serviceLines[0].professionalService.procedureModifiers = [""];
                        } else {
                          claimInformation.serviceLines[0].professionalService.procedureModifiers = ["95"];
                        }


                        if (treatment?.secondaryDiagnosisICDcodes && treatment?.secondaryDiagnosisICDcodes.length > 0) {
                          claimInformation.healthCareCodeInformation = [
                            {
                              diagnosisTypeCode: "ABK",
                              diagnosisCode: treatment?.diagnosisICDcodes.replace(/\./g, ''),
                            },
                            {
                              diagnosisTypeCode: "ABF",
                              diagnosisCode: treatment?.secondaryDiagnosisCodes.replace(/\./g, '')
                            }
                          ]
                        } else {
                          if (treatment?.diagnosisICDcodes && treatment?.diagnosisICDcodes.length > 0) {
                            claimInformation.healthCareCodeInformation = [
                              {
                                diagnosisTypeCode: "ABK",
                                diagnosisCode: treatment?.diagnosisICDcodes.replace(/\./g, ''),
                              }
                            ];
                          } else {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Empty secondaryDiagnosisICDcodes or diagnosisICDcodes");
                          }
                        }

                        const identity = [];

                        if ('dependents' in claimEligibilityDetails) {
                          identity.push('dependents');
                        } else {
                          identity.push('subscriber');
                        }
                        const sub = identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0];
                        const memberId = claimEligibilityDetails.subscriber.memberId;
                        let subUpdate;

                        let therapistInformation: any = {}

                        if (renderingProviderFirstName == "Brittany" && renderingProviderLastName == "Schopler") {
                          therapistInformation = {
                            providerType: "RenderingProvider",
                            npi: renderingProviderNpi,
                            firstName: "Laura",
                            lastName: "Valentine",
                            taxonomyCode: renderingProviderTaxonomyCode,
                          }
                        } else {
                          therapistInformation = {
                            providerType: "RenderingProvider",
                            npi: renderingProviderNpi,
                            firstName: renderingProviderFirstName,
                            lastName: renderingProviderLastName,
                            taxonomyCode: renderingProviderTaxonomyCode,
                          }
                        }

                        try {
                          subUpdate = {
                            memberId: memberId,
                            paymentResponsibilityLevelCode: 'P',
                            firstName: sub.firstName,
                            lastName: sub.lastName,
                            gender: sub.gender,
                            dateOfBirth: sub.dateOfBirth,
                            address: sub.address,
                          };
                        } catch (error) {
                          subUpdate = {
                            memberId: memberId,
                            paymentResponsibilityLevelCode: 'P',
                            firstName: sub.firstName,
                            lastName: sub.lastName,
                            gender: sub.gender,
                            dateOfBirth: sub.dateOfBirth,
                            address: sub.address,
                          };
                        }

                        const testClaimData = {
                          controlNumber: "*********",
                          tradingPartnerServiceId: Tradingpartner_ServiceId[organizationName],
                          submitter: {
                            organizationName: "Lavni Inc",
                            contactInformation: {
                              name: "Lavni Inc",
                              phoneNumber: "**********",
                            },
                          },
                          receiver: {
                            organizationName: Insurance_company[insuranceCompany],
                          },
                          subscriber: subUpdate,
                          providers: [
                            {
                              providerType: "BillingProvider",
                              npi: "**********",
                              employerId: "*********",
                              taxonomyCode: '251S00000X',
                              organizationName: "Lavni Inc",
                              address: {
                                address1: "804 South Garnett St.",
                                city: "Henderson",
                                state: "NC",
                                postalCode: "*********"
                              },
                              contactInformation: {
                                name: "Angela Arrington",
                                phoneNumber: "**********"
                              },
                            },
                            therapistInformation
                          ],
                          claimInformation: claimInformation
                        };

                        if (!claimEligibilityDetails?.errors) {
                          const claimSubmissionDetails = await submitClaim(testClaimData, accessToken.access_token);

                          if (claimSubmissionDetails?.status == "SUCCESS") {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusById(treatmentHistoryId._id, "claimOpen");

                            return res.sendSuccess(updatedReview, "Sucess");
                          } else {
                            AppLogger.error('claimSubmissionDetails 006', claimSubmissionDetails)
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimSubmissionDetails?.errors[0]));
                            return res.sendSuccess(updatedReview, "Success");
                          }
                        }
                      } else {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    }
                  }
                }
              } else {
                let claimEligibilityMDDetails = JSON.stringify(eligibilityDataSet[0].clientId?.claimEligibilityMdDetails);
                let claimEligibilityMDData = null;

                if (claimEligibilityMDDetails) {
                  claimEligibilityMDData = JSON.parse(claimEligibilityMDDetails);
                }

                if (claimEligibilityMDData && claimEligibilityMDData?.ins_number?.length > 0) {
                  if (partner[insurancePartner]) {
                    const extractedInfo = {
                      ins_name_f: claimEligibilityMDData.pat_name_f || claimEligibilityMDData.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                      ins_name_l: claimEligibilityMDData.pat_name_l || claimEligibilityMDData.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                      ins_city: claimEligibilityMDData.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                      ins_state: claimEligibilityMDData.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                      ins_zip: claimEligibilityMDData.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                      ins_dob: claimEligibilityMDData.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                      ins_sex: claimEligibilityMDData.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                      ins_addr_1: claimEligibilityMDData.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                      ins_number: claimEligibilityMDData.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                    };

                    let place_of_service;
                    let modifier;

                    if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                      place_of_service = "11";
                    } else {
                      place_of_service = "10";
                      modifier = "95"
                    }

                    const randomKey = generateRandomKey(20);

                    let claimInformationMD: any = {
                      claim_form: 1500,
                      payer_name: Insurance_company[insurancePartner],
                      accept_assign: 'Y',
                      employment_related: 'N',
                      ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                      ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                      ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                      ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                      ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                      ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                      ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                      ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                      ins_number: extractedInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                      bill_taxonomy: '251S00000X',
                      place_of_service_1: place_of_service,
                      prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                      prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                      prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                      prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                      pcn: String(randomKey),
                      charge_1: 180,
                      pat_rel: '18',
                      payerid: Tradingpartner_ServiceId[insurancePartner],
                      total_charge: 180,
                      claimNumber: diagnosisNote?.encounterID || '87654321',
                      proc_code_1: diagnosisNote?.cptCode,
                      mod1_1: modifier,
                      diag_1: diagnosisNote?.diagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                      diag_2: diagnosisNote?.secondaryDiagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                      from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                      bill_name: 'Lavni Inc',
                      bill_addr_1: '804 South Garnett St.',
                      bill_addr_2: '',
                      bill_city: 'Henderson',
                      bill_state: 'NC',
                      bill_zip: '27536',
                      bill_npi: '**********',
                      bill_id: '',
                      bill_phone: '**********',
                      bill_taxid: '*********',
                      bill_taxid_type: 'E',
                      diag_ref_1: "A",
                      units_1: 1,
                      pat_name_f: extractedInfo.ins_name_f,
                      pat_name_l: extractedInfo.ins_name_l,
                      pat_addr_1: extractedInfo.ins_addr_1,
                      pat_city: extractedInfo.ins_city,
                      pat_state: extractedInfo.ins_state,
                      pat_zip: extractedInfo.ins_zip,
                      pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                      pat_sex: extractedInfo.ins_sex,
                    };
                    if (treatmentHistoryId?.meetingStartedTime) {
                      if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                        const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                        await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);
   
                        if (saveClaimMd) {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                          return res.sendSuccess(updatedTherapist, "Success");
                        }
                      } else {
                        const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                        const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                        if (claimUploadCSV?.error) {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                          return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                        } else {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                          return res.sendSuccess(updatedTherapist, "Success");
                        }
                      }
                    }
                  }
                } else {
                  if (partner[insurancePartner]) {
                    let eligibility_payload_md = null;
                    if (eligibilityDataSet[0].clientInsuranceDetails) {
                      eligibility_payload_md = {
                        "AccountKey": process.env.CLAIM_MD_ACCOUNT_KEY,
                        "ins_name_f": eligibilityDataSet[0].clientId.firstname,
                        "ins_name_l": eligibilityDataSet[0].clientId.lastname,
                        "payerid": Tradingpartner_ServiceId[insurancePartner],
                        "pat_rel": "18",
                        "fdos": moment.utc(treatmentHistoryId?.meetingStartedTime).format('YYYYMMDD'),
                        "ins_dob": moment.utc(eligibilityDataSet[0].clientId?.dateOfBirth).format('YYYYMMDD'),
                        "ins_sex": (eligibilityDataSet[0].clientId?.gender == "Male" ? "M" : "F"),
                        "ins_number": eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                        "prov_npi": process.env.LAVNI_NPI,
                      }
                    }

                    const claimEligibilityAndActiveData = await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, eligibilityDataSet[0].clientId._id);

                    if (claimEligibilityAndActiveData?.finalStatus == "ACTIVE") {

                      const eligibilityInfo = claimEligibilityAndActiveData?.eligibilityInfoData;

                      const extractedInfo = {
                        ins_name_f: eligibilityInfo.pat_name_f || eligibilityInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                        ins_name_l: eligibilityInfo.pat_name_l || eligibilityInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                        ins_city: eligibilityInfo.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                        ins_state: eligibilityInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                        ins_zip: eligibilityInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                        ins_dob: eligibilityInfo.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                        ins_sex: eligibilityInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                        ins_addr_1: eligibilityInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                        ins_number: eligibilityInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                      };

                      let place_of_service;
                      let modifier;

                      if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                        place_of_service = "11";
                      } else {
                        place_of_service = "10";
                        modifier = "95"
                      }
                      const randomKey = generateRandomKey(20);
                      let claimInformationMD: any = {
                        claim_form: 1500,
                        payer_name: Insurance_company[insurancePartner],
                        accept_assign: 'Y',
                        employment_related: 'N',
                        ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                        ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                        ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                        ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                        ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                        ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                        ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                        ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                        ins_number: eligibilityInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                        bill_taxonomy: '251S00000X',
                        place_of_service_1: place_of_service,
                        prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                        prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                        prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                        prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                        pcn: String(randomKey),
                        charge_1: 180,
                        pat_rel: '18',
                        payerid: Tradingpartner_ServiceId[insurancePartner],
                        total_charge: 180,
                        claimNumber: diagnosisNote?.encounterID || '87654321',
                        proc_code_1: diagnosisNote?.cptCode,
                        mod1_1: modifier,
                        diag_1: diagnosisNote?.diagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                        diag_2: diagnosisNote?.secondaryDiagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                        from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                        bill_name: 'Lavni Inc',
                        bill_addr_1: '804 South Garnett St.',
                        bill_addr_2: '',
                        bill_city: 'Henderson',
                        bill_state: 'NC',
                        bill_zip: '27536',
                        bill_npi: '**********',
                        bill_id: '',
                        bill_phone: '**********',
                        bill_taxid: '*********',
                        bill_taxid_type: 'E',
                        diag_ref_1: "A",
                        units_1: 1,
                        pat_name_f: extractedInfo.ins_name_f,
                        pat_name_l: extractedInfo.ins_name_l,
                        pat_addr_1: extractedInfo.ins_addr_1,
                        pat_city: extractedInfo.ins_city,
                        pat_state: extractedInfo.ins_state,
                        pat_zip: extractedInfo.ins_zip,
                        pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                        pat_sex: extractedInfo.ins_sex,
                      };
                      
                      if (treatmentHistoryId?.meetingStartedTime) {
                        if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                          const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                          await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);
 
                          if (saveClaimMd) {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                            return res.sendSuccess(updatedTherapist, "Success");
                          }
                        } else {
                          const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                          const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                          if (claimUploadCSV?.error) {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                            return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                          } else {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                            return res.sendSuccess(updatedTherapist, "Success");
                          }
                        }
                      }
                    } else {
                      await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, claimEligibilityAndActiveData?.finalError);
                      return res.sendError(claimEligibilityAndActiveData?.finalError);
                    }
                  }
                }
              }
            }
          }

          if (!hasExpired) {
            let claimEligibilityDetailsOBJ = JSON.stringify(eligibilityDataSet[0]?.clientId?.claimEligibilityDetails);
            let claimEligibilityDetailsData = null;
            if (claimEligibilityDetailsOBJ) {
              claimEligibilityDetailsData = JSON.parse(claimEligibilityDetailsOBJ);
            }

            if (claimEligibilityDetailsData && claimEligibilityDetailsData?.memberId?.length > 0) {
              const updatedReview = await AdminDao.getMeetingStatusById(eligibilityDataSet[0].meetingDetails._id);

              if (partner[insurancePartner]) {
                const extractedInfo = {
                  ins_name_f: claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                  ins_name_l: claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                  ins_city: claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city || 'Unknown',
                  ins_state: claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                  ins_zip: claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                  ins_dob: moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                  ins_sex: claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                  ins_addr_1: claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                };

                let place_of_service;
                let modifier;

                if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan ") {
                  place_of_service = "11";
                } else {
                  place_of_service = "10";
                  modifier = "95"
                }

                const randomKey = generateRandomKey(20);

                let claimInformationMD: any = {
                  claim_form: 1500,
                  payer_name: Insurance_company[insurancePartner],
                  accept_assign: 'Y',
                  employment_related: 'N',
                  ins_name_f: extractedInfo.ins_name_f || claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname,
                  ins_name_l: extractedInfo.ins_name_l || claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname,
                  ins_addr_1: extractedInfo.ins_addr_1 || claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress,
                  ins_city: extractedInfo.ins_city || claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city,
                  ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                  ins_sex: extractedInfo.ins_sex || claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                  ins_state: extractedInfo.ins_state || claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                  ins_zip: extractedInfo.ins_zip || claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode,
                  ins_number: claimEligibilityDetailsData?.subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                  bill_taxonomy: '251S00000X',
                  place_of_service_1: place_of_service,
                  prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                  prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                  prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                  prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                  pcn: String(randomKey),
                  charge_1: 180,
                  pat_rel: '18',
                  payerid: Tradingpartner_ServiceId[insurancePartner],
                  total_charge: 180,
                  claimNumber: diagnosisNote?.encounterID || '87654321',
                  proc_code_1: diagnosisNote?.cptCode,
                  mod1_1: modifier,
                  diag_1: diagnosisNote?.diagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                  diag_2: diagnosisNote?.secondaryDiagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                  from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                  bill_name: 'Lavni Inc',
                  bill_addr_1: '804 South Garnett St.',
                  bill_addr_2: '',
                  bill_city: 'Henderson',
                  bill_state: 'NC',
                  bill_zip: '27536',
                  bill_npi: '**********',
                  bill_id: '',
                  bill_phone: '**********',
                  bill_taxid: '*********',
                  bill_taxid_type: 'E',
                  diag_ref_1: "A",
                  units_1: 1,
                  pat_name_f: extractedInfo.ins_name_f,
                  pat_name_l: extractedInfo.ins_name_l,
                  pat_addr_1: extractedInfo.ins_addr_1,
                  pat_city: extractedInfo.ins_city,
                  pat_state: extractedInfo.ins_state,
                  pat_zip: extractedInfo.ins_zip,
                  pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                  pat_sex: extractedInfo.ins_sex,
                };
                if (treatmentHistoryId?.meetingStartedTime) {
                  if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                    const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                    await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                    if (saveClaimMd) {
                      return res.sendSuccess(updatedReview, "Success");
                    }
                  } else {
                    const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                    const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                    if (claimUploadCSV?.error) {
                      AppLogger.error('claimUploadCSV 007', claimUploadCSV?.error?.error_mesg);
                      let claimInformation: any = {
                        claimFilingCode: "CI",
                        patientControlNumber: diagnosisNote?.patientID,
                        claimChargeAmount: "175",
                        placeOfServiceCode: place_of_service,
                        claimFrequencyCode: "1",
                        signatureIndicator: "Y",
                        planParticipationCode: "A",
                        benefitsAssignmentCertificationIndicator: "Y",
                        releaseInformationCode: "Y",
                        claimSupplementalInformation: {
                          repricedClaimNumber: "00001",
                          claimNumber: diagnosisNote?.encounterID,
                        },
                        serviceFacilityLocation: {
                          organizationName: "Lavni Inc",
                          address: {
                            address1: "804 South Garnett st.",
                            city: "Henderson",
                            state: "NC",
                            postalCode: "27536",
                          },
                        },
                        serviceLines: [
                          {
                            serviceDate: moment(diagnosisNote.encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
                            professionalService: {
                              procedureIdentifier: "HC",
                              lineItemChargeAmount: "175",
                              procedureCode: treatment?.diagnosisNoteId?.cptCode,
                              measurementUnit: "UN",
                              serviceUnitCount: "1",
                              placeOfServiceCode: "10",
                              description: 'mental health',
                              compositeDiagnosisCodePointers: { "diagnosisCodePointers": ["1"] },
                            },
                          },
                        ],
                      };

                      if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                        claimInformation.serviceLines[0].professionalService.procedureModifiers = [""];
                      } else {
                        claimInformation.serviceLines[0].professionalService.procedureModifiers = ["95"];
                      }

                      if (treatment?.secondaryDiagnosisICDcodes && treatment?.secondaryDiagnosisICDcodes.length > 0) {
                        claimInformation.healthCareCodeInformation = [
                          {
                            diagnosisTypeCode: "ABK",
                            diagnosisCode: treatment?.diagnosisICDcodes.replace(/\./g, ''),
                          },
                          {
                            diagnosisTypeCode: "ABF",
                            diagnosisCode: treatment?.secondaryDiagnosisCodes.replace(/\./g, '')
                          }
                        ]
                      } else {
                        if (treatment?.diagnosisICDcodes && treatment?.diagnosisICDcodes.length > 0) {
                          claimInformation.healthCareCodeInformation = [
                            {
                              diagnosisTypeCode: "ABK",
                              diagnosisCode: treatment?.diagnosisICDcodes.replace(/\./g, ''),
                            }
                          ];
                        } else {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Empty secondaryDiagnosisICDcodes or diagnosisICDcodes");
                        }
                      }

                      const identity = [];
                      if ('dependents' in claimEligibilityDetailsData) {
                        identity.push('dependents');
                      } else {
                        identity.push('subscriber');
                      }
                      const sub = identity[0] === 'subscriber' ? claimEligibilityDetailsData.subscriber : claimEligibilityDetailsData.dependents[0];
                      const memberId = claimEligibilityDetailsData.subscriber.memberId;
                      let subUpdate;


                      let therapistInformation: any = {}

                      if (renderingProviderFirstName == "Brittany" && renderingProviderLastName == "Schopler") {
                        therapistInformation = {
                          providerType: "RenderingProvider",
                          npi: renderingProviderNpi,
                          firstName: "Laura",
                          lastName: "Valentine",
                          taxonomyCode: renderingProviderTaxonomyCode,
                        }
                      } else {
                        therapistInformation = {
                          providerType: "RenderingProvider",
                          npi: renderingProviderNpi,
                          firstName: renderingProviderFirstName,
                          lastName: renderingProviderLastName,
                          taxonomyCode: renderingProviderTaxonomyCode,
                        }
                      }

                      try {
                        subUpdate = {
                          memberId: memberId,
                          paymentResponsibilityLevelCode: 'P',
                          firstName: sub.firstName,
                          lastName: sub.lastName,
                          gender: sub.gender,
                          dateOfBirth: sub.dateOfBirth,
                          address: sub.address,
                        };
                      } catch (error) {
                        subUpdate = {
                          memberId: memberId,
                          paymentResponsibilityLevelCode: 'P',
                          firstName: sub.firstName,
                          lastName: sub.lastName,
                          gender: sub.gender,
                          dateOfBirth: sub.dateOfBirth,
                          address: sub.address,
                        };
                      }

                      const testClaimData = {
                        controlNumber: "*********",
                        tradingPartnerServiceId: Tradingpartner_ServiceId[organizationName],
                        submitter: {
                          organizationName: "Lavni Inc",
                          contactInformation: {
                            name: "Lavni Inc",
                            phoneNumber: "**********",
                          },
                        },
                        receiver: {
                          organizationName: Insurance_company[insuranceCompany],
                        },
                        subscriber: subUpdate,
                        providers: [
                          {
                            providerType: "BillingProvider",
                            npi: "**********",
                            employerId: "*********",
                            taxonomyCode: '251S00000X',
                            organizationName: "Lavni Inc",
                            address: {
                              address1: "804 South Garnett St.",
                              city: "Henderson",
                              state: "NC",
                              postalCode: "*********"
                            },
                            contactInformation: {
                              name: "Angela Arrington",
                              phoneNumber: "**********"
                            },
                          },
                          therapistInformation
                        ],
                        claimInformation: claimInformation
                      };

                      const claimSubmissionDetails = await submitClaim(testClaimData, insurance?.insuranceAccessToken);

                      if (claimSubmissionDetails?.status == "SUCCESS") {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusById(treatmentHistoryId._id, "claimOpen");
                        return res.sendSuccess(updatedReview, "Sucess");
                      } else {
                        AppLogger.error('claimSubmissionDetails 007', claimSubmissionDetails)
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimSubmissionDetails?.errors[0]));
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    } else {
                      let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                      return res.sendSuccess(updatedReview, "Success");
                    }
                  }
                }
              }


            } else {
              const claimEligibilityDetails = await eligibilityClaim(eligibility_payload, insurance?.insuranceAccessToken);

              if (!(claimEligibilityDetails?.errors?.length > 0)) {
                const updatedReview = await AdminDao.getMeetingStatusById(eligibilityDataSet[0].meetingDetails._id);

                if (partner[insurancePartner]) {
                  const extractedInfo = {
                    ins_name_f: claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                    ins_name_l: claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                    ins_city: claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city || 'Unknown',
                    ins_state: claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                    ins_zip: claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                    ins_dob: moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                    ins_sex: claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                    ins_addr_1: claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                  };

                  let place_of_service;
                  let modifier;

                  if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan ") {
                    place_of_service = "11";
                  } else {
                    place_of_service = "10";
                    modifier = "95"
                  }
                  const randomKey = generateRandomKey(20);
                  let claimInformationMD: any = {
                    claim_form: 1500,
                    payer_name: Insurance_company[insurancePartner],
                    accept_assign: 'Y',
                    employment_related: 'N',
                    ins_name_f: extractedInfo.ins_name_f || claimEligibilityDetailsData?.subscriber?.firstName || eligibilityDataSet[0].clientId.firstname,
                    ins_name_l: extractedInfo.ins_name_l || claimEligibilityDetailsData?.subscriber?.lastName || eligibilityDataSet[0].clientId.lastname,
                    ins_addr_1: extractedInfo.ins_addr_1 || claimEligibilityDetailsData?.subscriber?.address?.address1 || eligibilityDataSet[0].clientId.streetAddress,
                    ins_city: extractedInfo.ins_city || claimEligibilityDetailsData?.subscriber?.address?.city || eligibilityDataSet[0].clientId.city,
                    ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(claimEligibilityDetailsData?.subscriber?.dateOfBirth).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                    ins_sex: extractedInfo.ins_sex || claimEligibilityDetailsData?.subscriber?.gender || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                    ins_state: extractedInfo.ins_state || claimEligibilityDetailsData?.subscriber?.address?.state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                    ins_zip: extractedInfo.ins_zip || claimEligibilityDetailsData?.subscriber?.address?.postalCode || eligibilityDataSet[0].clientId.zipCode,
                    ins_number: claimEligibilityDetailsData?.subscriber?.memberId || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                    bill_taxonomy: '251S00000X',
                    place_of_service_1: place_of_service,
                    prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                    prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                    prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                    prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                    pcn: String(randomKey),
                    charge_1: 180,
                    pat_rel: '18',
                    payerid: Tradingpartner_ServiceId[insurancePartner],
                    total_charge: 180,
                    claimNumber: diagnosisNote?.encounterID || '87654321',
                    proc_code_1: diagnosisNote?.cptCode,
                    mod1_1: modifier,
                    diag_1: diagnosisNote?.diagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                    diag_2: diagnosisNote?.secondaryDiagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                    from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                    bill_name: 'Lavni Inc',
                    bill_addr_1: '804 South Garnett St.',
                    bill_addr_2: '',
                    bill_city: 'Henderson',
                    bill_state: 'NC',
                    bill_zip: '27536',
                    bill_npi: '**********',
                    bill_id: '',
                    bill_phone: '**********',
                    bill_taxid: '*********',
                    bill_taxid_type: 'E',
                    diag_ref_1: "A",
                    units_1: 1,
                    pat_name_f: extractedInfo.ins_name_f,
                    pat_name_l: extractedInfo.ins_name_l,
                    pat_addr_1: extractedInfo.ins_addr_1,
                    pat_city: extractedInfo.ins_city,
                    pat_state: extractedInfo.ins_state,
                    pat_zip: extractedInfo.ins_zip,
                    pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                    pat_sex: extractedInfo.ins_sex,
                  };
                  if (treatmentHistoryId?.meetingStartedTime) {
                    if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                      const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                      await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                      if (saveClaimMd) {
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    } else {
                      const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                      const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                      if (claimUploadCSV?.error) {
                        AppLogger.error('claimUploadCSV 008', claimUploadCSV?.error?.error_mesg);
                        let claimInformation: any = {
                          claimFilingCode: "CI",
                          patientControlNumber: diagnosisNote?.patientID,
                          claimChargeAmount: "175",
                          placeOfServiceCode: place_of_service,
                          claimFrequencyCode: "1",
                          signatureIndicator: "Y",
                          planParticipationCode: "A",
                          benefitsAssignmentCertificationIndicator: "Y",
                          releaseInformationCode: "Y",
                          claimSupplementalInformation: {
                            repricedClaimNumber: "00001",
                            claimNumber: diagnosisNote?.encounterID,
                          },
                          serviceFacilityLocation: {
                            organizationName: "Lavni Inc",
                            address: {
                              address1: "804 South Garnett st.",
                              city: "Henderson",
                              state: "NC",
                              postalCode: "27536",
                            },
                          },
                          serviceLines: [
                            {
                              serviceDate: moment(diagnosisNote.encounterDate).utcOffset(-5 * 60).format("YYYYMMDD"),
                              professionalService: {
                                procedureIdentifier: "HC",
                                lineItemChargeAmount: "175",
                                procedureCode: treatment?.diagnosisNoteId?.cptCode,
                                measurementUnit: "UN",
                                serviceUnitCount: "1",
                                placeOfServiceCode: "10",
                                description: 'mental health',
                                compositeDiagnosisCodePointers: { "diagnosisCodePointers": ["1"] },
                              },
                            },
                          ],
                        };

                        if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                          claimInformation.serviceLines[0].professionalService.procedureModifiers = [""];
                        } else {
                          claimInformation.serviceLines[0].professionalService.procedureModifiers = ["95"];
                        }


                        if (treatment?.secondaryDiagnosisICDcodes && treatment?.secondaryDiagnosisICDcodes.length > 0) {
                          claimInformation.healthCareCodeInformation = [
                            {
                              diagnosisTypeCode: "ABK",
                              diagnosisCode: treatment?.diagnosisICDcodes.replace(/\./g, ''),
                            },
                            {
                              diagnosisTypeCode: "ABF",
                              diagnosisCode: treatment?.secondaryDiagnosisCodes.replace(/\./g, '')
                            }
                          ]
                        } else {
                          if (treatment?.diagnosisICDcodes && treatment?.diagnosisICDcodes.length > 0) {
                            claimInformation.healthCareCodeInformation = [
                              {
                                diagnosisTypeCode: "ABK",
                                diagnosisCode: treatment?.diagnosisICDcodes.replace(/\./g, ''),
                              }
                            ];
                          } else {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Empty secondaryDiagnosisICDcodes or diagnosisICDcodes");
                          }
                        }



                        const identity = [];

                        if ('dependents' in claimEligibilityDetails) {
                          identity.push('dependents');
                        } else {
                          identity.push('subscriber');
                        }
                        const sub = identity[0] === 'subscriber' ? claimEligibilityDetails.subscriber : claimEligibilityDetails.dependents[0];
                        const memberId = claimEligibilityDetails.subscriber.memberId;
                        let subUpdate;


                        let therapistInformation: any = {}

                        if (renderingProviderFirstName == "Brittany" && renderingProviderLastName == "Schopler") {
                          therapistInformation = {
                            providerType: "RenderingProvider",
                            npi: renderingProviderNpi,
                            firstName: "Laura",
                            lastName: "Valentine",
                            taxonomyCode: renderingProviderTaxonomyCode,
                          }
                        } else {
                          therapistInformation = {
                            providerType: "RenderingProvider",
                            npi: renderingProviderNpi,
                            firstName: renderingProviderFirstName,
                            lastName: renderingProviderLastName,
                            taxonomyCode: renderingProviderTaxonomyCode,
                          }
                        }


                        try {
                          subUpdate = {
                            memberId: memberId,
                            paymentResponsibilityLevelCode: 'P',
                            firstName: sub.firstName,
                            lastName: sub.lastName,
                            gender: sub.gender,
                            dateOfBirth: sub.dateOfBirth,
                            address: sub.address,
                          };
                        } catch (error) {
                          subUpdate = {
                            memberId: memberId,
                            paymentResponsibilityLevelCode: 'P',
                            firstName: sub.firstName,
                            lastName: sub.lastName,
                            gender: sub.gender,
                            dateOfBirth: sub.dateOfBirth,
                            address: sub.address,
                          };
                        }

                        const testClaimData = {
                          controlNumber: "*********",
                          tradingPartnerServiceId: Tradingpartner_ServiceId[organizationName],
                          submitter: {
                            organizationName: "Lavni Inc",
                            contactInformation: {
                              name: "Lavni Inc",
                              phoneNumber: "**********",
                            },
                          },
                          receiver: {
                            organizationName: Insurance_company[insuranceCompany],
                          },
                          subscriber: subUpdate,
                          providers: [
                            {
                              providerType: "BillingProvider",
                              npi: "**********",
                              employerId: "*********",
                              taxonomyCode: '251S00000X',
                              organizationName: "Lavni Inc",
                              address: {
                                address1: "804 South Garnett St.",
                                city: "Henderson",
                                state: "NC",
                                postalCode: "*********"
                              },
                              contactInformation: {
                                name: "Angela Arrington",
                                phoneNumber: "**********"
                              },
                            },
                            therapistInformation,
                          ],
                          claimInformation: claimInformation
                        };

                        if (!claimEligibilityDetails?.errors) {
                          const claimSubmissionDetails = await submitClaim(testClaimData, insurance?.insuranceAccessToken);

                          if (claimSubmissionDetails?.status == "SUCCESS") {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusById(treatmentHistoryId._id, "claimOpen");
                            return res.sendSuccess(updatedReview, "Sucess");
                          } else {
                            AppLogger.error('claimSubmissionDetails 008', claimSubmissionDetails)
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimSubmissionDetails?.errors[0]));
                            return res.sendSuccess(updatedReview, "Success");
                          }
                        }
                      } else {
                        let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                        return res.sendSuccess(updatedReview, "Success");
                      }
                    }
                  }
                }
              } else {
                let claimEligibilityMDDetails = JSON.stringify(eligibilityDataSet[0].clientId?.claimEligibilityMdDetails);
                let claimEligibilityMDData = null;
                if (claimEligibilityMDDetails) {
                  claimEligibilityMDData = JSON.parse(claimEligibilityMDDetails);
                }

                if (claimEligibilityMDData && claimEligibilityMDData?.ins_number?.length > 0) {
                  if (partner[insurancePartner]) {
                    const extractedInfo = {
                      ins_name_f: claimEligibilityMDData.pat_name_f || claimEligibilityMDData.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                      ins_name_l: claimEligibilityMDData.pat_name_l || claimEligibilityMDData.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                      ins_city: claimEligibilityMDData.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                      ins_state: claimEligibilityMDData.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                      ins_zip: claimEligibilityMDData.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                      ins_dob: claimEligibilityMDData.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                      ins_sex: claimEligibilityMDData.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                      ins_addr_1: claimEligibilityMDData.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                      ins_number: claimEligibilityMDData.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                    };

                    let place_of_service;
                    let modifier;

                    if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                      place_of_service = "11";
                    } else {
                      place_of_service = "10";
                      modifier = "95"
                    }
                    const randomKey = generateRandomKey(20);
                    let claimInformationMD: any = {
                      claim_form: 1500,
                      payer_name: Insurance_company[insurancePartner],
                      accept_assign: 'Y',
                      employment_related: 'N',
                      ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                      ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                      ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                      ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                      ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                      ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                      ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                      ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                      ins_number: extractedInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                      bill_taxonomy: '251S00000X',
                      place_of_service_1: place_of_service,
                      prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                      prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                      prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                      prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                      pcn: String(randomKey),
                      charge_1: 180,
                      pat_rel: '18',
                      payerid: Tradingpartner_ServiceId[insurancePartner],
                      total_charge: 180,
                      claimNumber: diagnosisNote?.encounterID || '87654321',
                      proc_code_1: diagnosisNote?.cptCode,
                      mod1_1: modifier,
                      diag_1: diagnosisNote?.diagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                      diag_2: diagnosisNote?.secondaryDiagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                      from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                      bill_name: 'Lavni Inc',
                      bill_addr_1: '804 South Garnett St.',
                      bill_addr_2: '',
                      bill_city: 'Henderson',
                      bill_state: 'NC',
                      bill_zip: '27536',
                      bill_npi: '**********',
                      bill_id: '',
                      bill_phone: '**********',
                      bill_taxid: '*********',
                      bill_taxid_type: 'E',
                      diag_ref_1: "A",
                      units_1: 1,
                      pat_name_f: extractedInfo.ins_name_f,
                      pat_name_l: extractedInfo.ins_name_l,
                      pat_addr_1: extractedInfo.ins_addr_1,
                      pat_city: extractedInfo.ins_city,
                      pat_state: extractedInfo.ins_state,
                      pat_zip: extractedInfo.ins_zip,
                      pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                      pat_sex: extractedInfo.ins_sex,
                    };
                    if (treatmentHistoryId?.meetingStartedTime) {
                      if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                        const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                        await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                        if (saveClaimMd) {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                          return res.sendSuccess(updatedTherapist, "Success");
                        }
                      } else {
                        const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                        const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                        if (claimUploadCSV?.error) {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                          return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                        } else {
                          let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                          return res.sendSuccess(updatedTherapist, "Success");
                        }
                      }
                    }
                  }
                } else {
                  if (partner[insurancePartner]) {
                    let eligibility_payload_md = null;
                    if (eligibilityDataSet[0].clientInsuranceDetails) {
                      eligibility_payload_md = {
                        "AccountKey": process.env.CLAIM_MD_ACCOUNT_KEY,
                        "ins_name_f": eligibilityDataSet[0].clientId.firstname,
                        "ins_name_l": eligibilityDataSet[0].clientId.lastname,
                        "payerid": Tradingpartner_ServiceId[insurancePartner],
                        "pat_rel": "18",
                        "fdos": moment.utc(treatmentHistoryId?.meetingStartedTime).format('YYYYMMDD'),
                        "ins_dob": moment.utc(eligibilityDataSet[0].clientId?.dateOfBirth).format('YYYYMMDD'),
                        "ins_sex": (eligibilityDataSet[0].clientId?.gender == "Male" ? "M" : "F"),
                        "ins_number": eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                        "prov_npi": process.env.LAVNI_NPI,
                      }
                    }
                    
                    const claimEligibilityAndActiveData = await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, eligibilityDataSet[0].clientId._id);

                    if (claimEligibilityAndActiveData?.finalStatus == "ACTIVE") {

                      const eligibilityInfo = claimEligibilityAndActiveData?.eligibilityInfoData;

                      const extractedInfo = {
                        ins_name_f: eligibilityInfo.pat_name_f || eligibilityInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname || 'Unknown',
                        ins_name_l: eligibilityInfo.pat_name_l || eligibilityInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname || 'Unknown',
                        ins_city: eligibilityInfo.ins_city || eligibilityDataSet[0].clientId.city || 'Unknown',
                        ins_state: eligibilityInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || 'Unknown',
                        ins_zip: eligibilityInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode || 'Unknown',
                        ins_dob: eligibilityInfo.ins_dob || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY') || 'Unknown',
                        ins_sex: eligibilityInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F") || 'Unknown',
                        ins_addr_1: eligibilityInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress || 'Unknown',
                        ins_number: eligibilityInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId || 'Unknown',
                      };

                      let place_of_service;
                      let modifier;

                      if (insurancePartner == "Alliance Health"|| insurancePartner == "United Health Care Community Plan" || insurancePartner == "United Health Care Community Plan " || insurancePartner == "Partners") {
                        place_of_service = "11";
                      } else {
                        place_of_service = "10";
                        modifier = "95"
                      }
                      const randomKey = generateRandomKey(20);
                      let claimInformationMD: any = {
                        claim_form: 1500,
                        payer_name: Insurance_company[insurancePartner],
                        accept_assign: 'Y',
                        employment_related: 'N',
                        ins_name_f: extractedInfo.ins_name_f || eligibilityDataSet[0].clientId.firstname,
                        ins_name_l: extractedInfo.ins_name_l || eligibilityDataSet[0].clientId.lastname,
                        ins_addr_1: extractedInfo.ins_addr_1 || eligibilityDataSet[0].clientId.streetAddress,
                        ins_city: extractedInfo.ins_city || eligibilityDataSet[0].clientId.city,
                        ins_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY') || moment(eligibilityDataSet[0].clientId.dateOfBirth).format('MM/DD/YYYY'),
                        ins_sex: extractedInfo.ins_sex || (eligibilityDataSet[0].clientId.gender == "Male" ? "M" : "F"),
                        ins_state: extractedInfo.ins_state || InsuranceState[eligibilityDataSet[0]?.clientId?.state as keyof typeof InsuranceState] || '',
                        ins_zip: extractedInfo.ins_zip || eligibilityDataSet[0].clientId.zipCode,
                        ins_number: eligibilityInfo.ins_number || eligibilityDataSet[0].clientInsuranceDetails.subscriber.memberId,
                        bill_taxonomy: '251S00000X',
                        place_of_service_1: place_of_service,
                        prov_name_f: eligibilityDataSet[0].therapistDetails.firstname,
                        prov_name_l: eligibilityDataSet[0].therapistDetails.lastname,
                        prov_npi: eligibilityDataSet[0].therapistDetails.nPI1 || 'default_npi',
                        prov_taxonomy: eligibilityDataSet[0].therapistDetails.taxonomyCode || 'default_taxonomy',
                        pcn: String(randomKey),
                        charge_1: 180,
                        pat_rel: '18',
                        payerid: Tradingpartner_ServiceId[insurancePartner],
                        total_charge: 180,
                        claimNumber: diagnosisNote?.encounterID || '87654321',
                        proc_code_1: diagnosisNote?.cptCode,
                        mod1_1: modifier,
                        diag_1: diagnosisNote?.diagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                        diag_2: diagnosisNote?.secondaryDiagnosisICDcodes[0]?.value.replace(/\./g, '') || '',
                        from_date_1: moment(treatmentHistoryId?.meetingStartedTime).format('MM-DD-YY') || moment(diagnosisNote?.encounterDate).format('MM-DD-YY'),
                        bill_name: 'Lavni Inc',
                        bill_addr_1: '804 South Garnett St.',
                        bill_addr_2: '',
                        bill_city: 'Henderson',
                        bill_state: 'NC',
                        bill_zip: '27536',
                        bill_npi: '**********',
                        bill_id: '',
                        bill_phone: '**********',
                        bill_taxid: '*********',
                        bill_taxid_type: 'E',
                        diag_ref_1: "A",
                        units_1: 1,
                        pat_name_f: extractedInfo.ins_name_f,
                        pat_name_l: extractedInfo.ins_name_l,
                        pat_addr_1: extractedInfo.ins_addr_1,
                        pat_city: extractedInfo.ins_city,
                        pat_state: extractedInfo.ins_state,
                        pat_zip: extractedInfo.ins_zip,
                        pat_dob: moment(extractedInfo.ins_dob).format('MM/DD/YYYY'),
                        pat_sex: extractedInfo.ins_sex,
                      };

                      if (treatmentHistoryId?.meetingStartedTime) {
                        if (moment(eligibilityDataSet[0].meetingDetails.createdAt).isSame(moment(), 'day')) {
                          const saveClaimMd = await AdminDao.addUnsubmittedClaimsMd(claimInformationMD, treatmentHistoryId._id)
                          await TherapistDao.updateTreatmentHistoryStatusByIdDayAfter(treatmentHistoryId._id);

                          if (saveClaimMd) {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                            return res.sendSuccess(updatedTherapist, "Success");
                          }
                        } else {
                          const outputFilePath = `${treatmentHistoryId._id}output.csv`;
                          const claimUploadCSV = await createCsvFromPayload(claimInformationMD, outputFilePath);

                          if (claimUploadCSV?.error) {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, JSON.stringify(claimUploadCSV?.error?.error_mesg));
                            return res.sendError(JSON.stringify(claimUploadCSV?.error?.error_mesg));
                          } else {
                            let updatedTherapist = await TherapistDao.updateTreatmentHistoryStatusByIdMD(treatmentHistoryId._id, "claimOpen", claimInformationMD.pcn);
                            return res.sendSuccess(updatedTherapist, "Success");
                          }
                        }
                      }
                    } else {
                      await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, claimEligibilityAndActiveData?.finalError);
                      return res.sendError(claimEligibilityAndActiveData?.finalError);
                    }
                  }
                }
              }
            }
          }
        } else {
          AppLogger.error('Invalid Organization Name')
          let updatedTherapist = await TherapistDao.updateTreatmentHistoryErrorById(treatmentHistoryId._id, "Invalid Organization Name");
          return res.sendError("Invalid Organization Name");
        }
      }

    } catch (error) {
      AppLogger.info(`Error: `, error);
    }
  }

  export async function getAllPendingCopaymentAmounts(req: Request, res: Response) {
    const userId = req.params.userId;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.sendError("Invalid user Id");
    }

    try {
      const claimDetails = await InsuranceDao.getPendingCopayments(Types.ObjectId(userId));

      return res.sendSuccess(claimDetails, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAlPaidPendingCopaymentAmounts(req: Request, res: Response) {
    const userId = req.params.userId;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.sendError("Invalid user Id");
    }

    try {
      const paidData = await InsuranceDao.getPaidCopayments(Types.ObjectId(userId));

      return res.sendSuccess(paidData, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function payAllPendingCopaymentAmounts(req: Request, res: Response) {
    const userId = req.user._id;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.sendError("Invalid user Id");
    }

    try {
      const claimDetails = await InsuranceDao.getPendingCopayments(userId);

      const client = await UserDao.getUserById(userId);

      let totalAmount: any = 0;
      for (const meeting of claimDetails) {
        const amount = parseFloat(String(meeting?.copayment?.amount)) || 0;
        totalAmount += amount;
      }

      if (client.stripeCustomerId) {
        if (totalAmount > 0) {

          const stripeCustomer = await stripe.customers.retrieve(
            client?.stripeCustomerId
          );
          const chargeForMonth = await stripe.paymentIntents.create({
            amount: parseFloat(totalAmount) * 100,
            currency: "usd",
            customer: client?.stripeCustomerId,
            payment_method:
              stripeCustomer.invoice_settings?.default_payment_method,
            description:
              "Client: " +
              client?.email +
              ", Co-Payment Amount: " +
              totalAmount,
            confirm: true,
          });

          if (chargeForMonth) {
            const promises = claimDetails.map(meeting => {
              return AdminDao.updateMeetingStatusById(meeting._id, {
                "copayment.status": "PAID"
              });
            });

            Promise.all(promises)
              .then(updatedReviews => {
                console.log("All meetings updated successfully");
              })
              .catch(error => {
                console.error("Error updating meetings:", error);
              });
          }

        } else {
          return res.sendError("This session amount is 0.");
        }
      } else {
        return res.sendError("This client does not have a connected Stripe account.");
      }

      return res.sendSuccess(claimDetails, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendSMSAndEmailForClient(req: Request, res: Response) {
    const clientId = req.params.userId;
    const therapistId = req.user._id;
    const copaymentAmount = req.params.copaymentAmount;

    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid user Id");
    }

    try {
      const client = await UserDao.getUserById(clientId);
      const therapist = await UserDao.getUserById(therapistId);

      let isSent = await EmailService.sendPendingCopaymentEmail(
        client,
        "Co-payment Reminder",
        `I hope this message finds you well. We wanted to gently remind you about the outstanding copayment of $${copaymentAmount} from your previous session. Your prompt attention to settling this balance before joining your upcoming session is greatly appreciated. By taking care of this now, we can ensure a seamless continuation of your care without any interruptions. Thank you for your trust and understanding in this matter. We look forward to seeing you soon.`,
        therapist
      );

      if (client?.primaryPhone) {
        await SMSService.sendEventSMS(
          `Hello ${client?.firstname ? client?.firstname : "cleint"},
          \nI hope this message finds you well. We wanted to gently remind you about the outstanding copayment of $${copaymentAmount} from your previous session. Your prompt attention to settling this balance before joining your upcoming session is greatly appreciated. By taking care of this now, we can ensure a seamless continuation of your care without any interruptions. Thank you for your trust and understanding in this matter. We look forward to seeing you soon.
          \nWarm regards, ${therapist?.firstname ? therapist?.firstname : "Therapist"}
          \nhttps://mylavni.com/profile/9`,
          client?.primaryPhone
        );
      }
      return res.sendSuccess(isSent, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function createStripeAccountAndPay(req: Request, res: Response, next: NextFunction) {
    const role = req.user.role;
    const email_text = "Congratulations! Your card information has been successfully added to your account. We are thrilled to welcome you to our community. To ensure that your payments are effortless, secure, and hassle-free, we have partnered with Stripe, the leading online payment platform. We appreciate your choice of our service and hope you have a wonderful experience with us. If you need any further assistance, please don't hesitate to let us know. We are always here to help."
    if (role == UserRole.CLIENT) {
      try {
        const { paymentMethod } = req.body;

        const client = await ClientDao.getUserById(req.user._id);
        let stripeCusId;

        if (client.stripeCustomerId) {
          const stripeCustomer = await stripe.customers.retrieve(client.stripeCustomerId);

          if (!stripeCustomer.deleted) {
            stripeCusId = stripeCustomer.id;
            await stripe.paymentMethods.attach(paymentMethod, { customer: stripeCusId });
          } else {
            const customer = await stripe.customers.create({
              payment_method: paymentMethod,
              email: client.email,
              name: client.firstname + " " + client.lastname,
              invoice_settings: {
                default_payment_method: paymentMethod,
              },
            });

            stripeCusId = customer.id;
          }
        } else {
          const customer = await stripe.customers.create({
            payment_method: paymentMethod,
            email: client.email,
            name: client.firstname + " " + client.lastname,
            invoice_settings: {
              default_payment_method: paymentMethod,
            },
          });

          stripeCusId = customer.id;
        }


        client.stripeCustomerId = stripeCusId;
        client.save();

        const claimDetails = await InsuranceDao.getPendingCopayments(req.user._id);


        let totalAmount: any = 0;
        for (const meeting of claimDetails) {
          const amount = parseFloat(String(meeting?.copayment?.amount)) || 0;
          totalAmount += amount;
        }

        if (client.stripeCustomerId) {
          if (totalAmount > 0) {

            const stripeCustomer = await stripe.customers.retrieve(
              client?.stripeCustomerId
            );
            const chargeForMonth = await stripe.paymentIntents.create({
              amount: parseFloat(totalAmount) * 100,
              currency: "usd",
              customer: client?.stripeCustomerId,
              payment_method:
                stripeCustomer.invoice_settings?.default_payment_method,
              description:
                "Client: " +
                client?.email +
                ", Co-Payment Amount: " +
                totalAmount,
              confirm: true,
            });

            if (chargeForMonth) {
              const promises = claimDetails.map(meeting => {
                return AdminDao.updateMeetingStatusById(meeting._id, {
                  "copayment.status": "PAID"
                });
              });

              Promise.all(promises)
                .then(updatedReviews => {
                  console.log("All meetings updated successfully");
                })
                .catch(error => {
                  console.error("Error updating meetings:", error);
                });
            }

          } else {
            return res.sendError("This session amount is 0.");
          }
        }

        await EmailService.sendSubscriptionEmail(
          client,
          "Congratulations! Your Stripe account has been successfully created.",
          email_text,
          client.firstname + " " + client.lastname
        );

        await SMSService.sendEventSMS(`Congratulations! Your Stripe account has been successfully created.`, client.primaryPhone);

        return res.sendSuccess(
          client.premiumStatus,
          "New stripe account created successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getInsuaranceCoPaymentAndContractPriceById(req: Request, res: Response) {
    const insuranceIdd = req.params.insuranceId;
    if (!mongoose.Types.ObjectId.isValid(insuranceIdd)) {
      return res.sendError("Invalid insurance Id");
    }
    try {
      const insurance = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceIdd));
      if (!insurance) {
        return res.sendError("No insurance found")
      }
      return res.sendSuccess(insurance, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }
  export async function addInsuranceInfoByUser(req: Request, res: Response, next: NextFunction) {
    const userId = req.user._id;
    let uploadCategory = UploadCategory.INSURANCE_CARD;
    let uploadInsuranceCategory = UploadCategory.INSURANCE_CARD_BACK;
    let newInsuranceTag: ObjectId;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateTherapisProfileValidationRules(req, cb);
      },
    });

    async function updateTherapisProfileValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let insuranceDetails = JSON.parse(req.body.insuranceDetails);

        if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
          return cb(Error("Deleting insurance card front image id is required."), null);
        }

        if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
          return cb(Error("Deleting insurance card back image id is required."), null);
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) => cb(error, "destination"));
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    async function deleteOldFiles(uploadId: string) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldFile = await UploadDao.getUpload(uploadId);
        await fs.unlink(oldFile.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }
      return isDeleted;
    }

    const upload = multer({ storage: storage }).fields([{ name: "insuranceCardFront" }, { name: "insuranceCardBack" }]);
    const clientData = await ClientDao.getUserById(
      userId
    );

    try {

      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        let isValid = true;
        let isFileDeleted = false;
        const upload: any = req.files
        if (upload.insuranceCardFront || upload.insuranceCardBack) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedFrontImage = null;
          let uploadedBackImage = null;

          if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
            isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

            if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
          }

          if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
            isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

            if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
          }

          const upload: any = req.files;

          let signRequired: boolean = false;
          if (req.body.signRequired !== undefined) {
            signRequired = req.body.signRequired;
          }

          if (upload.insuranceCardFront) {
            const insuranceCard = {
              userId: userId as unknown as Types.ObjectId,
              originalName: upload.insuranceCardFront[0].originalname.replace(/ /g, ""),
              name: upload.insuranceCardFront[0].filename,
              type: upload.insuranceCardFront[0].mimetype,
              path: upload.insuranceCardFront[0].path,
              fileSize: upload.insuranceCardFront[0].size,
              extension: path.extname(upload.insuranceCardFront[0].originalname) || req.body.extension,
              category: uploadCategory,
              signRequired: signRequired,
            };

            uploadedFrontImage = await UploadDao.createUpload(insuranceCard);

            if (!uploadedFrontImage) {
              return res.sendError("Error while uploading insurance front image.");
            }
          }

          if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
            const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
            if (isFound) {
              newInsuranceTag = insuranceDetails.insuranceCompanyId;
            }
          }

          if (upload.insuranceCardBack) {
            const insuranceCard = {
              userId: userId as unknown as Types.ObjectId,
              originalName: upload.insuranceCardBack[0].originalname.replace(/ /g, ""),
              name: upload.insuranceCardBack[0].filename,
              type: upload.insuranceCardBack[0].mimetype,
              path: upload.insuranceCardBack[0].path,
              fileSize: upload.insuranceCardBack[0].size,
              extension: path.extname(upload.insuranceCardBack[0].originalname) || req.body.extension,
              category: uploadInsuranceCategory,
              signRequired: signRequired,
            };

            uploadedBackImage = await UploadDao.createUpload(insuranceCard);

            if (!uploadedBackImage) {
              return res.sendError("Error while uploading insurance back image.");
            }
          }

          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: uploadedFrontImage?._id ? uploadedFrontImage?._id : null,
            insuranceCardBackId: uploadedBackImage?._id ? uploadedBackImage?._id : null,
          };

          const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));

          const user = await AdminDao.getUserById(userId);
          const previousCopaymentAmount = user.copaymentAmount;


          if (insuranceCompanyData) {
            const updatedClient = await ClientDao.updateClientCoPayment(userId, insuranceCompanyData?.coPayment)

            let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(userId, insuranceCompanyData?.coPayment, previousCopaymentAmount);
          }

          const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)

          let updatedInsurance = await InsuranceDao.addInsuranceInfo(userId, insurance);

          if (updatedInsurance) {
            clientData.premiumStatus = PremiumStatus.ACTIVE
          }
          clientData.save();
          if (!updatedInsurance) {
            return res.sendError("Failed to update the insurance details.");
          }

          if (resData && 'error' in resData && resData.error === "error") {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          } else {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }



        } else {

          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardId is required.");
          }

          if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardId is required.");
          }

          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }
          }

          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
            }
          }

          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: insuranceDetails.insuranceCardId ? insuranceDetails.insuranceCardId : null,
            insuranceCardBackId: insuranceDetails.insuranceCardBackId ? insuranceDetails.insuranceCardBackId : null,
          };
          const insuranceCompanyData = await InsuranceDao.getInsuaranceCoPaymentAndContractPriceById(Types.ObjectId(insuranceDetails.insuranceCompanyId));

          const user = await AdminDao.getUserById(userId);
          const previousCopaymentAmount = user.copaymentAmount;


          if (insuranceCompanyData) {
            const updatedClient = await ClientDao.updateClientCoPayment(userId, insuranceCompanyData?.coPayment)

            let updatedMettings = await AdminDao.getPendingCopaymentsAndUpdate(userId, insuranceCompanyData?.coPayment, previousCopaymentAmount);
          }

          const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, userId, res)

          let updatedInsurance = await InsuranceDao.addInsuranceInfo(userId, insurance);
          if (updatedInsurance) {
            clientData.premiumStatus = PremiumStatus.ACTIVE
          }
          clientData.save();
          if (!updatedInsurance) {
            return res.sendError("Failed to update the therapist.");
          }
          if (resData && 'error' in resData && resData.error === "error" && resData.error === "error") {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          } else {
            return res.sendSuccess(updatedInsurance, "Successfully updated.");
          }
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getInsuranceInfoByClient(req: Request, res: Response, next: NextFunction) {
    const userId = req.user._id;
    try {
      const insuarances = await UserDao.getInsuranceListByClientId(userId);
      return res.sendSuccess(insuarances);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateClientInsurance(req: Request, res: Response, next: NextFunction) {
    const clientId = req.body.clientId;
    const insuranceId = req.body.insuranceId;
    const secondaryId = req.body.secondaryInsuranceId;

    if (!clientId) {
      return res.sendError("Client Id is required");
    }
    if (!insuranceId) {
      return res.sendError("Insurance Id is required");
    }
    try {
      const insuranceData: any = await UserDao.getInsuranceById(
        insuranceId
      );
      if (!insuranceData) {
        return res.sendError("Invalid insurance Id");
      }

      // Get insurance company information from insuranceCompanyId
      const insuranceCompanyId = insuranceData.insuranceCompanyId._id;
      const insuranceCompany = await InsuranceDao.getInsuranceCompanyById(insuranceCompanyId);
      console.log({insuranceCompany})
      console.log("insuranceCompany.isMedicaid",insuranceCompany.isMedicaid)

      // Check if current user is admin
      const isAdmin = req.user.role === UserRole.SUPER_ADMIN || req.user.role === UserRole.SUB_ADMIN;

      // Check if it's not Medicaid insurance
      if (!insuranceCompany.isMedicaid) {
        // Get client information to check card
        const clientData = await ClientDao.getUserById(clientId);
        if (!clientData) {
          return res.sendError("Invalid client Id");
        }

        // Admin bypass: Skip payment method validation for admin users
        if (!isAdmin) {
          // Check if client has a card in Stripe
          if (!clientData.stripeCustomerId) {
            return res.sendError("You need to add a payment card before switching to this insurance.");
          }

          try {
            // Check if client has any payment method in Stripe
            const paymentMethods = await stripe.paymentMethods.list({
              customer: clientData.stripeCustomerId,
              type: "card",
            });

            if (!paymentMethods || !paymentMethods.data || paymentMethods.data.length === 0) {
              return res.sendError("You need to add a payment card before switching to this insurance.");
            }
          } catch (stripeError) {
            return res.sendError("Unable to verify the payment method: " + stripeError.message);
          }
        }
      } else {
        // If it's Medicaid, check client exists
        const clientData = await ClientDao.getUserById(clientId);
        if (!clientData) {
          return res.sendError("Invalid client Id");
        }
      }

      const copaymentAmount = insuranceData?.insuranceCompanyId?.coPayment ? insuranceData?.insuranceCompanyId?.coPayment : 0;

      const insurance: DInsurance = {
        clientId: insuranceData.clientId,
        insuranceCompanyId: insuranceData.insuranceCompanyId,
        subscriber: insuranceData.subscriber,
        dependent: insuranceData.dependent,
      };

      const updatedUser = await UserDao.updateUserInsurance(clientId, insuranceId, secondaryId, copaymentAmount)

      const resData: { error?: string; msg?: any; } | { claimEligibilityDetails: any; coPaymentValue: any; } | void = await InsuranceEp.checkEligibilityBeforInsuranceDetails(insurance, clientId, res)


      const data = {
        updatedUser: updatedUser,
        insuranceData: insuranceData,
      }

      if (resData && 'error' in resData && resData.error === "error" && resData.error === "error") {
        return res.sendSuccess(data);
      } else {
        return res.sendSuccess(data);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getInsuranceInfoByAdmin(req: Request, res: Response, next: NextFunction) {
    const userId = req.body.clientId;
    try {
      const insuarances = await UserDao.getInsuranceListByClientId(userId);
      return res.sendSuccess(insuarances);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getUserDetailsByUser(req: Request, res: Response, next: NextFunction) {
    const userId = req.user._id;
    try {
      const userDetails = await UserDao.getUser(userId);
      return res.sendSuccess(userDetails);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function addClientSecondaryInsurance(req: Request, res: Response, next: NextFunction) {
    const clientId = req.body.clientId;
    const secondaryId = req.body.secondaryInsuranceId;

    if (!clientId) {
      return res.sendError("Client Id is required");
    }
    if (!secondaryId) {
      return res.sendError("Secondary Insurance Id is required");
    }
    try {
      const insuranceData: any = await UserDao.getInsuranceById(
        secondaryId
      );
      if (!insuranceData) {
        return res.sendError("Invalid secondary insurance Id");
      }
      const clientData = await ClientDao.getUserById(
        clientId
      );
      if (!clientData) {
        return res.sendError("Invalid client Id");
      }


      const updatedUser = await UserDao.addUserSecondaryInsurance(clientId, secondaryId)
      const data = {
        updatedUser: updatedUser,
        insuranceData: insuranceData,
      }
      return res.sendSuccess(data);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function addSecondaryInsuranceInfo(req: Request, res: Response, next: NextFunction) {
    const userId = req.params.userId;
    let uploadCategory = UploadCategory.INSURANCE_CARD;
    let uploadInsuranceCategory = UploadCategory.INSURANCE_CARD_BACK;
    let newInsuranceTag: ObjectId;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateTherapisProfileValidationRules(req, cb);
      },
    });

    async function updateTherapisProfileValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let insuranceDetails = JSON.parse(req.body.insuranceDetails);

        if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
          return cb(Error("Deleting insurance card front image id is required."), null);
        }

        if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
          return cb(Error("Deleting insurance card back image id is required."), null);
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) => cb(error, "destination"));
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    async function deleteOldFiles(uploadId: string) {
      let isDeleted = false;
      let resultHandler = async function (error: any) {
        if (error) {
          console.log("Unlink failed.", error);
        } else {
          console.log("File deleted.");
        }
      };

      try {
        let oldFile = await UploadDao.getUpload(uploadId);
        await fs.unlink(oldFile.path, resultHandler);
        await UploadDao.deleteUploadById(uploadId);
        isDeleted = true;
      } catch (error) {
        isDeleted = false;
      }
      return isDeleted;
    }

    const upload = multer({ storage: storage }).fields([{ name: "insuranceCardFront" }, { name: "insuranceCardBack" }]);
    const client = await ClientDao.getUserById(userId);


    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        let isValid = true;
        let isFileDeleted = false;
        const upload: any = req.files
        if (upload.insuranceCardFront || upload.insuranceCardBack) {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          let isFileDeleted = false;
          let uploadedFrontImage = null;
          let uploadedBackImage = null;

          const user = await UserDao.getUser(Types.ObjectId(userId));

          if (insuranceDetails.deletingInsuranceCardFrontId !== "none") {
            isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardFrontId);

            if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
          }

          if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
            isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

            if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
          }

          const upload: any = req.files;

          let signRequired: boolean = false;
          if (req.body.signRequired !== undefined) {
            signRequired = req.body.signRequired;
          }

          if (upload.insuranceCardFront) {
            const insuranceCard = {
              userId: userId as unknown as Types.ObjectId,
              originalName: upload.insuranceCardFront[0].originalname.replace(/ /g, ""),
              name: upload.insuranceCardFront[0].filename,
              type: upload.insuranceCardFront[0].mimetype,
              path: upload.insuranceCardFront[0].path,
              fileSize: upload.insuranceCardFront[0].size,
              extension: path.extname(upload.insuranceCardFront[0].originalname) || req.body.extension,
              category: uploadCategory,
              signRequired: signRequired,
            };

            uploadedFrontImage = await UploadDao.createUpload(insuranceCard);
            if (!uploadedFrontImage) {
              return res.sendError("Error while uploading insurance front image.");
            }
          }

          if (upload.insuranceCardBack) {
            const insuranceCard = {
              userId: userId as unknown as Types.ObjectId,
              originalName: upload.insuranceCardBack[0].originalname.replace(/ /g, ""),
              name: upload.insuranceCardBack[0].filename,
              type: upload.insuranceCardBack[0].mimetype,
              path: upload.insuranceCardBack[0].path,
              fileSize: upload.insuranceCardBack[0].size,
              extension: path.extname(upload.insuranceCardBack[0].originalname) || req.body.extension,
              category: uploadInsuranceCategory,
              signRequired: signRequired,
            };

            uploadedBackImage = await UploadDao.createUpload(insuranceCard);
            if (!uploadedBackImage) {
              return res.sendError("Error while uploading insurance back image.");
            }

          }

          if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
            const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
            if (isFound) {
              newInsuranceTag = insuranceDetails.insuranceCompanyId;
            }
          }

          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: uploadedFrontImage?._id ? uploadedFrontImage?._id : null,
            insuranceCardBackId: uploadedBackImage?._id ? uploadedBackImage?._id : null,
          };

          let updatedInsurance = await InsuranceDao.addInsuranceInfo(Types.ObjectId(userId), insurance);

          if (updatedInsurance) {
            client.premiumStatus = PremiumStatus.ACTIVE
          }
          client.save();
          if (!updatedInsurance) {
            return res.sendError("Failed to update the insurance details.");
          }

          return res.sendSuccess(updatedInsurance, "Successfully updated.");
        } else {
          let insuranceDetails = JSON.parse(req.body.insuranceDetails);
          if (!insuranceDetails.deletingInsuranceCardFrontId || typeof insuranceDetails.deletingInsuranceCardFrontId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardId is required.");
          }

          if (!insuranceDetails.deletingInsuranceCardBackId || typeof insuranceDetails.deletingInsuranceCardBackId !== "string") {
            isValid = false;
            return res.sendError("deletingInsuranceCardId is required.");
          }

          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }
          }
          if (isValid) {
            if (insuranceDetails.deletingInsuranceCardBackId !== "none") {
              isFileDeleted = await deleteOldFiles(insuranceDetails.deletingInsuranceCardBackId);

              if (!isFileDeleted) return res.sendError("Error while deleting the previous file");
            }

            if (insuranceDetails.insuranceCompanyId && insuranceDetails.insuranceCompanyId.length > 0) {
              const isFound = await AdminDao.getInsuranceCompanyById(insuranceDetails.insuranceCompanyId);
              if (isFound) {
                newInsuranceTag = insuranceDetails.insuranceCompanyId;
              }
            }
          }
          const insurance: DInsurance = {
            clientId: insuranceDetails.clientId,
            insuranceCompanyId: insuranceDetails.insuranceCompanyId,
            subscriber: insuranceDetails.subscriber,
            dependent: insuranceDetails.dependent,
            insuranceCardId: insuranceDetails.insuranceCardId ? insuranceDetails.insuranceCardId : null,
            insuranceCardBackId: insuranceDetails.insuranceCardBackId ? insuranceDetails.insuranceCardBackId : null,
          };

          let updatedInsurance = await InsuranceDao.addSeondaryInsuranceInfo(Types.ObjectId(userId), insurance);
          if (updatedInsurance) {
            client.premiumStatus = PremiumStatus.ACTIVE
          }
          client.save();
          if (!updatedInsurance) {
            return res.sendError("Failed to update the therapist.");
          }

          return res.sendSuccess(updatedInsurance, "Successfully updated.");

        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getInsuranceByClientIdViaTherapist(req: Request, res: Response, next: NextFunction) {
    try {
      const clientId = Types.ObjectId(req.params.clientId);
      let insurance = await InsuranceDao.getInsuranceWithAuthorizationForms(clientId);

      if (!insurance) {
        return res.sendError("No authorization forms available.");
      }

      return res.sendSuccess(insurance, "Insurance with authorization forms details.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function checkEligibilityMDOnFirstOfMonth() {
    try {
      AppLogger.info(`.:: Check EligibilityMD On First Of Month ::.`);

      const currentDate = new Date();

      const clientList = await Client.find({ insuranceId: { $exists: true , $ne: null } });
      
      await Promise.all(
        clientList.map(async (client: IClient ) => {
          try {
            
            const insurance = await InsuranceDao.getInsurancePlanById(client?.insuranceId);

            if (!insurance?.insuranceCompanyId) {
              return;
            }

            const isFound = await AdminDao.getInsuranceCompanyById(insurance?.insuranceCompanyId);
      
            if (!isFound?.insuranceCompany) {
              return;
            }
            
            const insurancePartner = isFound?.insuranceCompany as keyof typeof Tradingpartner_ServiceId;
      
            const eligibility_payload_md = {
              "AccountKey": process.env.CLAIM_MD_ACCOUNT_KEY,
              "ins_name_f": client?.firstname,
              "ins_name_l": client?.lastname,
              "payerid": Tradingpartner_ServiceId[insurancePartner],
              "pat_rel": "18",
              "fdos": moment.utc(currentDate).format('YYYYMMDD'),
              "ins_dob": moment.utc(client?.dateOfBirth).format('YYYYMMDD'),
              "ins_sex": (client?.gender == "Male" ? "M" : "F"),
              "ins_number": insurance?.subscriber?.memberId,
              "prov_npi": process.env.LAVNI_NPI,
            }
            
            await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, client?._id);
          } catch (error) {
            AppLogger.error(`.:: Check EligibilityMD On First Of Month Failed for ${client?.firstname ?? ""} ${client?.lastname ?? ""} ::.`);
          }
        })
      );

    } catch (error) {
      AppLogger.error(`.:: Check EligibilityMD On First Of Month Failed ::.`);
    }
  }
}