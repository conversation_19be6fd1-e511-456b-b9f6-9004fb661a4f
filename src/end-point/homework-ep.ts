import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import multer = require("multer");
import path = require("path");
import { UploadDao } from "../dao/upload-dao";
import { UserDao } from "../dao/user-dao";
import { DUpload } from "../models/upload-model";
import { UserRole } from "../models/user-model";
import { UploadCategory } from "./user-ep";
import { DHomework, HStatus, IHomework } from '../models/homework-model';
import { HomeworkDao } from "../dao/homework-dao";
import { send } from "process";
import Homework from '../schemas/homework-schema';
import { EmailService } from "../mail/config";
import { SMSService } from "../sms/config";
let mongoose = require("mongoose");
let jwt = require("jsonwebtoken");

let fs = require("fs");

export namespace HomeworkEp {
  export async function createHomework(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    let uploadCategory = UploadCategory.THERAPIST_HOMEWORK_DOCUMENTS;
    let isValid: boolean = true;
    let uploadedFiles: any[] = [];
    let createdBy: any = "";

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await homeworkValidationRules(req, cb);
      },
    });

    async function homeworkValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let homeworkDetails = JSON.parse(req.body.homeworkDetails);
        createdBy = homeworkDetails.createdBy;

        const user = await UserDao.getUserById(createdBy);

        if (!user) {
          return cb(Error("User not found the provided user Id."));
        }

        // only therapist can create homework
        if (user.role !== UserRole.THERAPIST) {
          return cb(Error("Invalid user role."));
        }

        if (!homeworkDetails.assignedFor) {
          return cb(Error("Homework sholud be assign to client"), null);
        }

        if (!mongoose.Types.ObjectId.isValid(homeworkDetails.createdBy)) {
          return cb(Error("Invalid Therapist Id."), null);
        }

        if (!mongoose.Types.ObjectId.isValid(homeworkDetails.assignedFor)) {
          return cb(Error("Invalid Client Id."), null);
        }

        if (!homeworkDetails.title || typeof homeworkDetails.title !== "string") {
          return cb(Error("Title is required."), null);
        }

        if (!homeworkDetails.description || typeof homeworkDetails.description !== "string") {
          return cb(Error("Description is required."), null);
        }

        if (!homeworkDetails.dueDate || typeof homeworkDetails.dueDate !== "string") {
          return cb(Error("Due Date is required."), null);
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) =>
              cb(error, "destination")
            );
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    const upload = multer({ storage: storage }).array("uploads", 3);

    try {
      upload(req, res, async (error: any) => {
        if (error) {
          return res.sendError(error + "");
        }

        try {
          // if (req.files.length === 0) {
          //     return res.sendError("Upload files not found.");
          // } else {
          try {
            let homeworkDetails = JSON.parse(req.body.homeworkDetails);

            if (!homeworkDetails.createdBy) {
              isValid = false;
              return res.sendError("Therapist Id is required.");
            }

            if (!homeworkDetails.assignedFor) {
              isValid = false;
              return res.sendError("Client Id is required.");
            }

            if (!mongoose.Types.ObjectId.isValid(homeworkDetails.createdBy)) {
              isValid = false;
              return res.sendError("Therapist Id is invalid.");
            }

            if (!mongoose.Types.ObjectId.isValid(homeworkDetails.assignedFor)) {
              isValid = false;
              return res.sendError("Client Id is invalid.");
            }

            if (!homeworkDetails.title || typeof homeworkDetails.title !== "string") {
              isValid = false;
              return res.sendError("Homework title is required.");
            }

            if (!homeworkDetails.description || typeof homeworkDetails.description !== "string") {
              isValid = false;
              return res.sendError("Homework description is required.");
            }

            if (!homeworkDetails.dueDate || typeof homeworkDetails.dueDate !== "string") {
              isValid = false;
              return res.sendError("Homework dueDate is required.");
            }
          } catch (error) {
            isValid = false;
            return res.sendError("Invalid Homework Details json.");
          }

          if (isValid) {
            let request;

            try {
              request = JSON.parse(req.body.homeworkDetails);
            } catch (error) {
              res.sendError(error);
            }

            const uploads: any = req.files;

            let signRequired: boolean = false;

            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            for (const upload of uploads) {
              const data: DUpload = {
                userId: createdBy as unknown as Types.ObjectId,
                originalName: upload.originalname.replace(/ /g, ''),
                name: upload.filename,
                type: upload.mimetype,
                path: upload.path,
                fileSize: upload.size,
                extension:
                  path.extname(upload.originalname) || req.body.extension,
                category: uploadCategory,
                signRequired: signRequired,
              };

              let uploadedFile = await UploadDao.createUpload(data);

              uploadedFiles.push(uploadedFile);
            }

            // if (uploadedFiles.length === 0) {
            //   return res.sendError("Error while saving homework documents.");
            // } else {
            let uploadedIds: any = uploadedFiles.map((item: any) => {
              return item._id;
            });

            const homework: DHomework = {
              createdBy: request.createdBy,
              assignedFor: request.assignedFor,
              title: request.title,
              description: request.description,
              dueDate: request.dueDate,
              uploads: uploadedIds,
              isComplete: false,
              status: HStatus.NEW,
            };

            try {
              let response = await HomeworkDao.createHomework(homework);
              if (response == null) {
                return res.sendError(
                  "Homework could not be added."
                );
              } else {
                const user1 = await UserDao.getUserById(request.createdBy);
                const user2 = await UserDao.getUserById(request.assignedFor);

                // await EmailService.sendEventEmail(
                //   user1,
                //   "New Homework is assigned!",
                //   "New Homework `" + homework.title + "` is assigned by",
                //   "Login to view more information.",
                //   user1?.firstname +
                //   " " +
                //   user1?.lastname
                // );
                
                // SMSService.sendEventSMS(
                //   `New Homework ${homework.title} is assigned by ${user1.firstname} ${user1.lastname}`,
                //   user1.primaryPhone
                // );

                if (user2.reminderType && user2.reminderType.email == true) {
                  await EmailService.sendEventEmail(
                    user2,
                    "New Homework is assigned!",
                    "New Homework `" + homework.title + "` is assigned by",
                    "Login to view more information.",
                    user1?.firstname +
                    " " +
                    user1?.lastname
                  );
                }

                if (user2.reminderType && user2.reminderType.text == true) {
                  await SMSService.sendEventSMS(
                    `New Homework ${homework.title} is assigned by ${user1.firstname} ${user1.lastname}`,
                    user2.primaryPhone
                  );
                }

                return res.sendSuccess(
                  response,
                  "homework added."
                );
              }
            } catch (error) {
              return res.sendError(error);
            }
            // }
          }
          // }
        } catch (error) {
          return res.sendError(error);
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllHomeworkByTherapistId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = req.params.userId;
    const therapistId = req.user._id;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    try {
      const homeworks = await HomeworkDao.getHomeworksByUserId(Types.ObjectId(clientId), therapistId, limit, offset);

      return res.sendSuccess(
        homeworks,
        "all homeworks"
      );
    } catch (error) {
      return res.sendError(error);
    }

  }

  export async function getAllHomeworkByClientId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapistId = req.params.userId;
    const clientId = req.user._id;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    try {
      const homeworks = await HomeworkDao.getHomeworksByUserId(clientId, Types.ObjectId(therapistId), limit, offset);
      return res.sendSuccess(
        homeworks,
        "all homeworks"
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getHomeworkById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const hId = req.params.hId;
    try {
      const homework = await HomeworkDao.getHomeworkById(hId);

      return res.sendSuccess(
        homework,
        "selected homework"
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteHomeworkById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const hId = req.params.hId;
    const homework = await HomeworkDao.deleteHomeworkById(hId);

    const user1 = await UserDao.getUserById(homework.createdBy);
    const user2 = await UserDao.getUserById(homework.assignedFor);
    if (user2?.reminderType?.email == true) {
      await EmailService.sendEventEmail(
        user2,
        "Homework is removed!",
        "Homework `" + homework.title + "` has been deleted by",
        "Login to view more information.",
        user1?.firstname +
        " " +
        user1?.lastname
      );
    }
    if (user2?.reminderType?.text == true) {
      await SMSService.sendEventSMS(
        `Homework ${homework.title} has been deleted by ${user1.firstname} ${user1.lastname}`,
        user2.primaryPhone
      );
    }
    return res.sendSuccess(
      homework,
      "delete homework"
    );
  }

  export async function updateHomework(req: Request, res: Response, next: NextFunction) {
    const user = UserDao.getUserById(req.body.user);

    try {
      switch ((await user).role) {
        case UserRole.CLIENT:
          updateHomeworkByClient(req, res, next);
          break;
        case UserRole.THERAPIST:
          updateHomeworkByTherapist(req, res, next);
          break;
        default:
          break;
      }
    } catch (error) {
      return send(error);
    }
  }

  export async function updateHomeworkByClient(req: Request, res: Response, next: NextFunction) {
    // update homework status and upload ...
    let uploadCategory = UploadCategory.CLIENT_HOMEWORK_DOCUMENTS;
    // let uploadCount: number = 0;
    let deletingFiles = [];

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await editHomeworkValidationRules(req, cb);
      },
    });

    async function editHomeworkValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let homeworkDetails = JSON.parse(req.body.homeworkDetails); // convert to json object

        try {
          const homeworkData = await HomeworkDao.getHomeworkByIdWithoutPoppulate(homeworkDetails._id);

          deletingFiles = homeworkData.uploads;
          // uploadCount = deletingFiles.length;

          if (!homeworkData) {
            return cb(
              Error(
                "No homework details found for the provided homework Id."
              )
            );
          }

        } catch (error) {
          return cb(Error(error), null);
        }

        // to validate deleting uploads array...
        if (
          !homeworkDetails.deletingUploadIds ||
          !(homeworkDetails.deletingUploadIds instanceof Array)
        ) {
          return cb(Error("Deleting upload id should be an array.")), null;
        }

        // to check provided id and uploads id...
        if (homeworkDetails.deletingUploadIds.length !== 0) {
          for (let upload of homeworkDetails.deletingUploadIds) {
            if (!deletingFiles.includes(upload)) {
              return cb(Error("Invalid deleting upload Ids"), null);
            }
          }
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) =>
              cb(error, "destination error")
            );
          } else {
            return cb(null, destination);
          }
        });

      } catch (error) {
        return cb(Error("Invalid homework details."), null);
      }
    }

    const upload = multer({ storage });

    try {
      upload.array("uploads", 10)(req, res, async function (error: any) {
        if (error) {
          return res.send(error + " ");
        }

        let homeworkDetails: any;

        try {
          if (req.files.length === 0) {
            try {
              homeworkDetails = JSON.parse(req.body.homeworkDetails);
            } catch (error) {
              return res.send(error);
            }

            if (
              !mongoose.Types.ObjectId.isValid(homeworkDetails._id)
            ) {
              return res.sendError("Invalid homework Id.");
            }

            try {
              const homeworkData = await HomeworkDao.getHomeworkByIdWithoutPoppulate(homeworkDetails._id);

              deletingFiles = homeworkData.uploads;
              // uploadCount = deletingFiles.length;

              if (!homeworkData) {
                return send(
                  Error(
                    "No homework details found for the provided homework Id."
                  )
                );
              }
            } catch (error) {
              return res.sendError(error);
            }

            if (
              !homeworkDetails.deletingUploadIds ||
              !(homeworkDetails.deletingUploadIds instanceof Array)
            ) {
              return res.sendError("Deleting upload id should be an array.");
            }

            if (homeworkDetails.deletingUploadIds.length !== 0) {
              for (let upload of homeworkDetails.deletingUploadIds) {
                if (!deletingFiles.includes(upload)) {
                  return res.sendError("Invalid deleting upload Ids");
                }
              }
            }

            let previousHomeworkDetails: IHomework = null;
            let previousUploadIds: any[] = null;

            try {
              previousHomeworkDetails =
                await HomeworkDao.getHomeworkById(
                  homeworkDetails._id
                );
            } catch (error) {
              return res.sendError("Invalid homework id");
            }

            previousUploadIds = previousHomeworkDetails.uploads;

            async function trimData(id: any) {
              for (
                let i = 0;
                i < previousHomeworkDetails.uploads.length;
                i++
              ) {
                if (previousUploadIds[i].toString() === id.toString()) {
                  previousUploadIds.splice(i, 1);
                }
              }
            }

            async function deleteFiles() {
              for (let id of homeworkDetails.deletingUploadIds) {
                let resultHandler = async function (err: any) {
                  if (err) {
                    throw err;
                  }
                };
                try {
                  let upload = await UploadDao.getUpload(id);
                  await fs.unlink(upload.path, resultHandler);
                  await UploadDao.deleteUploadById(id);
                  await trimData(id);
                } catch (error) {
                  return res.sendError(error);
                }
              }
            }

            try {
              await deleteFiles();
            } catch (error) {
              return res.send(
                "Error while deleting previous files" + error
              );
            }

            const newHomeworkDetails: DHomework = {
              title: previousHomeworkDetails.title,
              description: previousHomeworkDetails.description,
              dueDate: previousHomeworkDetails.dueDate,
              isComplete: homeworkDetails.isComplete,
              createdBy: previousHomeworkDetails.createdBy,
              assignedFor: previousHomeworkDetails.assignedFor,
              status: homeworkDetails.status,
              uploads: previousUploadIds
            };

            try {
              let updatedHomeworkDetails =
                await HomeworkDao.updateHomework(
                  homeworkDetails._id,
                  newHomeworkDetails
                );

              if (updatedHomeworkDetails !== null) {
                const user1 = await UserDao.getUserById(newHomeworkDetails.createdBy);
                const user2 = await UserDao.getUserById(newHomeworkDetails.assignedFor);
                if (user1?.reminderType?.email == true) {
                  await EmailService.sendEventEmail(
                    user1,
                    "Homework has been finished!",
                    "Homework `" + newHomeworkDetails.title + "` has been finished by",
                    "Login to view more information.",
                    user2?.firstname +
                    " " +
                    user2?.lastname
                  );
                }
                if (user1?.reminderType?.text == true) {
                  await SMSService.sendEventSMS(
                    `Homework ${newHomeworkDetails.title} has been finished by ${user2.firstname} ${user2.lastname}`,
                    user1.primaryPhone
                  );
                }


                return res.sendSuccess(
                  updatedHomeworkDetails,
                  "homework updated."
                );
              }
            } catch (error) {
              return res.send(error);
            }
          } else {
            const uploads: any = req.files;
            let newUploads = [];

            try {
              homeworkDetails = JSON.parse(req.body.homeworkDetails);
            } catch (error) {
              return res.sendError(error);
            }

            let signRequired: boolean = false;
            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            for (const upload of uploads) {
              const data: DUpload = {
                userId: req.user as unknown as Types.ObjectId,
                originalName: upload.originalname.replace(/ /g, ''),
                name: upload.filename,
                type: upload.mimetype,
                path: upload.path,
                fileSize: upload.size,
                extension:
                  path.extname(upload.originalname) || req.body.extension,
                category: uploadCategory,
                signRequired: signRequired,
              };

              let uploadedFile = await UploadDao.createUpload(data);

              newUploads.push(uploadedFile);
            }

            if (newUploads.length === 0) {
              return res.sendError("Error while saving uploads");
            } else {
              let uploadResult: any = newUploads.map((item: any) => {
                return item._id;
              });

              let previousHomeworkDetails: IHomework = null;
              let previousUploadIds: any[] = null;

              try {
                previousHomeworkDetails =
                  await HomeworkDao.getHomeworkById(
                    homeworkDetails._id
                  );
              } catch (error) {
                return res.sendError("Invalid homework id");
              }

              previousUploadIds = previousHomeworkDetails.uploads;

              async function trimData(id: any) {
                for (
                  let i = 0;
                  i < previousHomeworkDetails.uploads.length;
                  i++
                ) {
                  if (previousUploadIds[i].toString() === id.toString()) {
                    previousUploadIds.splice(i, 1);
                  }
                }
              }

              async function deleteFiles() {
                for (let id of homeworkDetails.deletingUploadIds) {
                  let resultHandler = async function (err: any) {
                    if (err) {
                      throw err;
                    }
                  };
                  try {
                    let upload = await UploadDao.getUpload(id);
                    await fs.unlink(upload.path, resultHandler);
                    await UploadDao.deleteUploadById(id);
                    await trimData(id);
                  } catch (error) {
                    return res.sendError(error);
                  }
                }
              }

              async function updateDetails() {
                await deleteFiles();
                let finalUploads = previousUploadIds.concat(uploadResult);
                let requestBody = null;

                try {
                  requestBody = JSON.parse(req.body.homeworkDetails);
                } catch (error) {
                  return res.sendError("Invalid homework details");
                }

                const newHomeWorkDetails: DHomework = {
                  title: previousHomeworkDetails.title,
                  description: previousHomeworkDetails.description,
                  dueDate: previousHomeworkDetails.dueDate,
                  isComplete: homeworkDetails.isComplete,
                  createdBy: previousHomeworkDetails.createdBy,
                  assignedFor: previousHomeworkDetails.assignedFor,
                  status: homeworkDetails.status,
                  uploads: finalUploads
                };

                try {
                  let updatedHomeworkDetails =
                    await HomeworkDao.updateHomework(
                      homeworkDetails._id,
                      newHomeWorkDetails
                    );


                  if (updatedHomeworkDetails !== null) {

                    const user1 = await UserDao.getUserById(newHomeWorkDetails.createdBy);
                    const user2 = await UserDao.getUserById(newHomeWorkDetails.assignedFor);
                    if (user1?.reminderType?.email == true) {
                      await EmailService.sendEventEmail(
                        user1,
                        "Homework has been updated!",
                        "Homework `" + newHomeWorkDetails.title + "` has been finished by",
                        "Login to view more information.",
                        user2?.firstname +
                        " " +
                        user2?.lastname
                      );
                    }
                    if (user1?.reminderType?.text == true) {
                      await SMSService.sendEventSMS(
                        `Homework ${newHomeWorkDetails.title} has been finished by ${user2.firstname} ${user2.lastname}`,
                        user1.primaryPhone
                      );
                    }
                    return res.sendSuccess(
                      updatedHomeworkDetails,
                      "homework updated."
                    );
                  }
                } catch (error) {
                  return res.send(error);
                }
              }

              try {
                await updateDetails();
              } catch (error) {
                return res.send(error);
              }

            }
          }
        } catch (error) {
          return res.send(error);
        }
      });
    } catch (error) {
      return res.send(error);
    }
  }

  export async function updateHomeworkByTherapist(req: Request, res: Response, next: NextFunction) { // update homework status and upload ...
    let uploadCategory = UploadCategory.CLIENT_HOMEWORK_DOCUMENTS;
    let createdBy: any = "";
    // let uploadCount: number = 0;
    let deletingFiles = [];

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await editHomeworkValidationRules(req, cb);
      },
    });

    async function editHomeworkValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        let homeworkDetails = JSON.parse(req.body.homeworkDetails); // convert to json object
        createdBy = homeworkDetails.createdBy; // get userid form json

        if (!homeworkDetails.createdBy) {
          return cb(Error("User Id is required."), null);
        }

        if (!mongoose.Types.ObjectId.isValid(homeworkDetails.createdBy)) {
          return cb(Error("Invalid user Id."), null);
        }

        if (!homeworkDetails.assignedFor) {
          return cb(Error("User Id is required."), null);
        }

        if (!mongoose.Types.ObjectId.isValid(homeworkDetails.assignedFor)) {
          return cb(Error("Invalid user Id."), null);
        }

        if (!homeworkDetails.title) {
          return cb(Error("Title is required!"), null);
        }

        if (!homeworkDetails.description) {
          return cb(Error("Description is required!"), null);
        }

        if (!homeworkDetails.dueDate) {
          return cb(Error("Due date is required!"), null);
        }

        if (!homeworkDetails.dueDate) {
          return cb(Error("Due date is required!"), null);
        }

        try {
          const homeworkData = await HomeworkDao.getHomeworkByIdWithoutPoppulate(homeworkDetails._id);

          deletingFiles = homeworkData.uploads;
          // uploadCount = deletingFiles.length;

          if (!homeworkData) {
            return cb(
              Error(
                "No homework details found for the provided homework Id."
              )
            );
          }

        } catch (error) {
          return cb(Error(error), null);
        }

        if (
          !homeworkDetails.deletingUploadIds ||
          !(homeworkDetails.deletingUploadIds instanceof Array)
        ) {
          return cb(Error("Deleting upload id should be an array.")), null;
        }

        // to check provided id and uploads id...
        if (homeworkDetails.deletingUploadIds.length !== 0) {
          for (let upload of homeworkDetails.deletingUploadIds) {
            if (!deletingFiles.includes(upload)) {
              return cb(Error("Invalid deleting upload Ids"), null);
            }
          }
        }

        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) =>
              cb(error, "destination error")
            );
          } else {
            return cb(null, destination);
          }
        });

      } catch (error) {
        return cb(Error("Invalid homework details."), null);
      }
    }

    const upload = multer({ storage });

    try {
      upload.array("uploads", 3)(req, res, async function (error: any) {
        if (error) {
          return res.send(error + " ");
        }

        let homeworkDetails: any;

        try {
          if (req.files.length === 0) {
            try {
              homeworkDetails = JSON.parse(req.body.homeworkDetails);
              createdBy = homeworkDetails.createdBy;
            } catch (error) {
              return res.sendError(error);
            }
            if (!homeworkDetails.createdBy) {
              return res.sendError("Created user id is required!");
            }

            if (!mongoose.Types.ObjectId.isValid(homeworkDetails.createdBy)) {
              return res.sendError("Created id is invalied!");
            }

            if (!homeworkDetails.assignedFor) {
              return res.sendError("Assigned user id is required!");
            }

            if (!mongoose.Types.ObjectId.isValid(homeworkDetails.assignedFor)) {
              return res.sendError("Assigned user id is invalied!");
            }

            if (!homeworkDetails.title) {
              return res.sendError("Title is required!");
            }

            if (!homeworkDetails.description) {
              return res.sendError("Description is required!");
            }

            if (!homeworkDetails.dueDate) {
              return res.sendError("Due date is required!");
            }

            if (!homeworkDetails.dueDate) {
              return res.sendError("Due date is required!");
            }

            try {
              const user = await UserDao.getUserById(createdBy);

              if (!user) {
                return res.sendError("No user for the provided user Id.");
              }

              if (user.role !== UserRole.THERAPIST) {
                return res.sendError("Invalid user role.");
              }

            } catch (error) {
              return res.sendError(error);
            }

            if (
              !mongoose.Types.ObjectId.isValid(homeworkDetails._id)
            ) {
              return res.sendError("Invalid homework Id.");
            }

            try {
              const homeworkData = await HomeworkDao.getHomeworkByIdWithoutPoppulate(homeworkDetails._id);

              deletingFiles = homeworkData.uploads;
              // uploadCount = deletingFiles.length;

              if (!homeworkData) {
                return send(
                  Error(
                    "No homework details found for the provided homework Id."
                  )
                );
              }
            } catch (error) {
              return res.sendError(error);
            }

            if (
              !homeworkDetails.deletingUploadIds ||
              !(homeworkDetails.deletingUploadIds instanceof Array)
            ) {
              return res.sendError("Deleting upload id should be an array.");
            }

            if (homeworkDetails.deletingUploadIds.length !== 0) {
              for (let upload of homeworkDetails.deletingUploadIds) {
                if (!deletingFiles.includes(upload)) {
                  return res.sendError("Invalid deleting upload Ids");
                }
              }
            }

            let previousHomeworkDetails: IHomework = null;
            let previousUploadIds: any[] = null;

            try {
              previousHomeworkDetails =
                await HomeworkDao.getHomeworkById(
                  homeworkDetails._id
                );
            } catch (error) {
              return res.sendError("Invalid homework id");
            }

            previousUploadIds = previousHomeworkDetails.uploads;

            async function trimData(id: any) {
              for (
                let i = 0;
                i < previousHomeworkDetails.uploads.length;
                i++
              ) {
                if (previousUploadIds[i].toString() === id.toString()) {
                  previousUploadIds.splice(i, 1);
                }
              }
            }

            async function deleteFiles() {
              for (let id of homeworkDetails.deletingUploadIds) {
                let resultHandler = async function (err: any) {
                  if (err) {
                    throw err;
                  }
                };
                try {
                  let upload = await UploadDao.getUpload(id);
                  await fs.unlink(upload.path, resultHandler);
                  await UploadDao.deleteUploadById(id);
                  await trimData(id);
                } catch (error) {
                  return res.sendError(error);
                }
              }
            }

            try {
              await deleteFiles();
            } catch (error) {
              return res.send(
                "Error while deleting previous files" + error
              );
            }

            const newHomeworkDetails: DHomework = {
              title: homeworkDetails.title,
              description: homeworkDetails.description,
              dueDate: homeworkDetails.dueDate,
              isComplete: homeworkDetails.isComplete,
              createdBy: homeworkDetails.createdBy,
              assignedFor: homeworkDetails.assignedFor,
              status: homeworkDetails.status,
              uploads: previousUploadIds
            };

            try {
              let updatedHomeworkDetails =
                await HomeworkDao.updateHomework(
                  homeworkDetails._id,
                  newHomeworkDetails
                );

              if (updatedHomeworkDetails !== null) {
                const user1 = await UserDao.getUserById(newHomeworkDetails.createdBy);
                const user2 = await UserDao.getUserById(newHomeworkDetails.assignedFor);

                if (homeworkDetails.status == HStatus.APPROVED) {
                  if (user2?.reminderType?.email == true) {
                    await EmailService.sendEventEmail(
                      user2,
                      "Homework has been approved!",
                      "Homework `" + newHomeworkDetails.title + "` has been approved by",
                      "Login to view more information.",
                      user1?.firstname +
                      " " +
                      user1?.lastname
                    );
                  }
                  if (user2?.reminderType?.text == true) {
                    await SMSService.sendEventSMS(
                      `Homework ${newHomeworkDetails.title} has been approved by ${user1.firstname} ${user1.lastname}`,
                      user2.primaryPhone
                    );
                  }
                } else if (homeworkDetails.status == HStatus.REJECT) {
                  if (user2?.reminderType?.email == true) {
                    await EmailService.sendEventEmail(
                      user2,
                      "Homework has been rejected!",
                      "Homework `" + newHomeworkDetails.title + "` has been rejected by",
                      "Login to view more information.",
                      user1?.firstname +
                      " " +
                      user1?.lastname
                    );
                  }
                  if (user2?.reminderType?.text == true) {
                    await SMSService.sendEventSMS(
                      `Homework ${newHomeworkDetails.title} has been rejected by ${user1.firstname} ${user1.lastname}`,
                      user2.primaryPhone
                    );
                  }
                } else {
                  if (user2?.reminderType?.email == true) {
                    await EmailService.sendEventEmail(
                      user2,
                      "Homework has been updated!",
                      "Homework `" + newHomeworkDetails.title + "` has been updated by",
                      "Login to view more information.",
                      user1?.firstname +
                      " " +
                      user1?.lastname
                    );
                  }
                  if (user2?.reminderType?.text == true) {
                    await SMSService.sendEventSMS(
                      `Homework ${newHomeworkDetails.title} has been updated by ${user1.firstname} ${user1.lastname}`,
                      user2.primaryPhone
                    );
                  }
                }

                return res.sendSuccess(
                  updatedHomeworkDetails,
                  "homework updated."
                );
              }
            } catch (error) {
              return res.send(error);
            }
          } else {
            const uploads: any = req.files;
            let newUploads = [];

            try {
              homeworkDetails = JSON.parse(req.body.homeworkDetails);
            } catch (error) {
              return res.sendError(error);
            }

            let signRequired: boolean = false;
            if (req.body.signRequired !== undefined) {
              signRequired = req.body.signRequired;
            }

            for (const upload of uploads) {
              const data: DUpload = {
                userId: req.user as unknown as Types.ObjectId,
                originalName: upload.originalname.replace(/ /g, ''),
                name: upload.filename,
                type: upload.mimetype,
                path: upload.path,
                fileSize: upload.size,
                extension:
                  path.extname(upload.originalname) || req.body.extension,
                category: uploadCategory,
                signRequired: signRequired,
              };

              let uploadedFile = await UploadDao.createUpload(data);

              newUploads.push(uploadedFile);
            }

            if (newUploads.length === 0) {
              return res.sendError("Error while saving uploads");
            } else {
              let uploadResult: any = newUploads.map((item: any) => {
                return item._id;
              });

              let previousHomeworkDetails: IHomework = null;
              let previousUploadIds: any[] = null;

              try {
                previousHomeworkDetails =
                  await HomeworkDao.getHomeworkById(
                    homeworkDetails._id
                  );
              } catch (error) {
                return res.sendError("Invalid homework id");
              }

              previousUploadIds = previousHomeworkDetails.uploads;

              async function trimData(id: any) {
                for (
                  let i = 0;
                  i < previousHomeworkDetails.uploads.length;
                  i++
                ) {
                  if (previousUploadIds[i].toString() === id.toString()) {
                    previousUploadIds.splice(i, 1);
                  }
                }
              }

              async function deleteFiles() {
                for (let id of homeworkDetails.deletingUploadIds) {
                  let resultHandler = async function (err: any) {
                    if (err) {
                      throw err;
                    }
                  };
                  try {
                    let upload = await UploadDao.getUpload(id);
                    await fs.unlink(upload.path, resultHandler);
                    await UploadDao.deleteUploadById(id);
                    await trimData(id);
                  } catch (error) {
                    return res.sendError(error);
                  }
                }
              }

              async function updateDetails() {
                await deleteFiles();
                let finalUploads = previousUploadIds.concat(uploadResult);
                let requestBody = null;

                try {
                  requestBody = JSON.parse(req.body.homeworkDetails);
                } catch (error) {
                  return res.sendError("Invalid homework details");
                }

                const newHomeWorkDetails: DHomework = {
                  title: requestBody.title,
                  description: requestBody.description,
                  dueDate: requestBody.dueDate,
                  isComplete: requestBody.isComplete,
                  createdBy: requestBody.createdBy,
                  assignedFor: homeworkDetails.assignedFor,
                  status: requestBody.status,
                  uploads: finalUploads
                };

                try {
                  let updatedHomeworkDetails =
                    await HomeworkDao.updateHomework(
                      homeworkDetails._id,
                      newHomeWorkDetails
                    );

                  if (updatedHomeworkDetails !== null) {

                    const user1 = await UserDao.getUserById(newHomeWorkDetails.createdBy);
                    const user2 = await UserDao.getUserById(newHomeWorkDetails.assignedFor);

                    if (homeworkDetails.status == HStatus.APPROVED) {
                      if (user2.reminderType && user2.reminderType.email == true) {
                        await EmailService.sendEventEmail(
                          user2,
                          "Homework has been approved!",
                          "Homework `" + newHomeWorkDetails.title + "` has been approved by",
                          "Login to view more information.",
                          user1?.firstname +
                          " " +
                          user1?.lastname
                        );
                      }

                      if (user2.reminderType && user2?.reminderType?.text == true) {
                        await SMSService.sendEventSMS(
                          `Homework ${newHomeWorkDetails.title} has been approved by ${user1.firstname} ${user1.lastname}`,
                          user2.primaryPhone
                        );
                      }
                    } else if (homeworkDetails.status == HStatus.REJECT) {
                      if (user2?.reminderType && user2?.reminderType?.email == true) {
                        await EmailService.sendEventEmail(
                          user2,
                          "Homework has been rejected!",
                          "Homework `" + newHomeWorkDetails.title + "` has been rejected by",
                          "Login to view more information.",
                          user1?.firstname +
                          " " +
                          user1?.lastname
                        );
                      }

                      if (user2?.reminderType && user2?.reminderType?.text == true) {
                        SMSService.sendEventSMS(
                          `Homework ${newHomeWorkDetails.title} has been rejected by ${user1.firstname} ${user1.lastname}`,
                          user2.primaryPhone
                        );
                      }
                    } else {
                      if (user2?.reminderType && user2?.reminderType?.email == true) {
                        await EmailService.sendEventEmail(
                          user2,
                          "Homework has been updated!",
                          "Homework `" + newHomeWorkDetails.title + "` has been updated by",
                          "Login to view more information.",
                          user1?.firstname +
                          " " +
                          user1?.lastname
                        );
                      }

                      if (user2?.reminderType && user2?.reminderType?.text == true) {
                        await SMSService.sendEventSMS(
                          `Homework ${newHomeWorkDetails.title} has been updated by ${user1.firstname} ${user1.lastname}`,
                          user2.primaryPhone
                        );
                      }
                    }

                    return res.sendSuccess(
                      updatedHomeworkDetails,
                      "homework updated."
                    );
                  }
                } catch (error) {
                  return res.send(error);
                }
              }

              try {
                await updateDetails();
              } catch (error) {
                return res.send(error);
              }

            }
          }
        } catch (error) {
          return res.send(error);
        }
      });
    } catch (error) {
      return res.send(error);
    }

  }
}