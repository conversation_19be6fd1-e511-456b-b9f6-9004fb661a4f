import { NextFunction, Request, Response } from "express";
import { check, Valida<PERSON><PERSON>hain, validationR<PERSON>ult } from "express-validator";
import FriendRequest from "../schemas/friend-request-schema";
import { FriendRequestStatus } from "../models/friend-request-model";
import Appointment from "../schemas/appointment-schema";
import { MeetingStatus, AppointmentStatus, ApprovalStatus} from "../models/appointment-model";
import { v4 as uuidv4 } from 'uuid';
import { CallingStatus, DMeeting, IMeeting } from "../models/meeting-model";
import { VideoCallDao } from "../dao/videocall-dao";
import { UserRole } from "../models/user-model";
import Meeting from "../schemas/meeting-schema";
import { VonageNativeCallDao } from "../dao/vonage-native-dao";import {
    IClient,
    PremiumStatus,
    SubscriptionStatus,
  } from "../models/client-model";
import User from "../schemas/user-schema";
import { ZoomVideoCallEP } from "./zoom-video-call-ep";
import { UserDao } from "../dao/user-dao";
import { NotificationDao } from "../dao/notification-dao";
import * as mongoose from 'mongoose';
import { Types } from 'mongoose';
import { AppLogger } from "../common/logging";
import  TranscribeSchema  from "../schemas/transcribe-schema";
import { captionsDBModel } from "../models/vonage-native-call-model";
import { ITranscribe } from "../models/transcribe-model";
import { SMSService } from "../sms/config";
import { EmailService } from "../mail/config";
const { projectToken } = require('opentok-jwt');
const axios = require('axios');

const apiKey = process.env.TOKBOX_API_KEY;
const secret = process.env.TOKBOX_SECRET;
const opentokUrl = 'https://api.opentok.com/v2/project';
const OpenTok = require('opentok');
const opentok = new OpenTok(apiKey, secret);

export namespace VonageNativeCallEp {
    export function initializeVonageVideoCallValidation(): ValidationChain[] {
        return [
          check("clientId")
            .notEmpty()
            .withMessage("clientId is required")
            .isMongoId()
            .withMessage("Invalid clientId."),
          check("therapistId")
            .notEmpty()
            .withMessage("therapistId is required")
            .isMongoId()
            .withMessage("Invalid therapistId."),
          check("callDuration")
            .notEmpty()
            .withMessage("callDuration is required")
            .isInt({ min: 0, max: 60 })
            .withMessage("Your meeting duration is not within 0 and 60 minutes."),
          check("isTranscribeAllowed")
            .notEmpty()
            .withMessage("isTranscribeAllowed is required/..")
            .isBoolean()
            .withMessage("isTranscribeAllowed is not a boolean type."),
          check("isAppointmentBased")
            .notEmpty()
            .withMessage("isAppointmentBased is required")
            .isBoolean()
            .withMessage("isAppointmentBased is not a boolean type."),
          check("isAudioCall")
            .notEmpty()
            .withMessage("isAudioCall is required")
            .isBoolean()
            .withMessage("isAudioCall is not a boolean type."),
        ];
    }
    export async function initializeVonageNativeVideoCall(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            AppLogger.info(`Vonage Native Call | The meeting initiation has started. Started By: ${req.user._id}`);
            const errors = validationResult(req);
            console.log(errors);
            
            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]);
            }
            console.log("xxx01 ", req.body);
            
            let appointmentId = null;
            let isAppointmentBased = req.body.isAppointmentBased;
            const transcribeAllowed = req.body.isTranscribeAllowed;
            let callDuration = parseInt(req.body.callDuration);
            if (isAppointmentBased) {
                appointmentId = req.body.appointmentId;
            }
            let appointmentData;
            let clientId = req.body.clientId;
            let therapistId = req.body.therapistId;
            const isAudioCall = req.body.isAudioCall;
            const vonageSessionName = req.body.vonageSessionName;
            let meetingDetails: DMeeting;
            const newClientIdentifier = uuidv4();
            const newTherapistIdentifier = uuidv4();
            const newPassword= uuidv4();
            const linkToJoinToMeetingForShare = `${process.env.APP_URL}/${req.body.middlePartOfLink}/${vonageSessionName}/${req.body.secretKeyForJoinMeeting}`
            const friendRequest = await FriendRequest.findOne({
                clientId: clientId,
                therapistId: therapistId,
                status: FriendRequestStatus.APPROVED,
            });
            const recieverId = req?.user._id == therapistId ? clientId: therapistId;
            console.log("recieverId ", recieverId);
            console.log("xxx02 ", friendRequest);
            if (friendRequest == null) {
                return res.sendError("You are not friend with other user.", 453);
            }
            const recieverData = await UserDao.getUserFCMTokenById(recieverId);
            console.log("recieverData ",recieverData);
            
            if(!recieverData){
                console.log("XXXXXXXXXXXXX");
                return res.sendError('Reciever data not found!')
            }
            // ----------START----------
            // This line of code check meeting status is ongoing or not when user joined the meeting second time using URL.
            const checkIsMeetingAlreadyOngoing = await Meeting.findOne(
                { vonageSessionName },
            ).select("callingStatus participantCount");

            if(checkIsMeetingAlreadyOngoing && checkIsMeetingAlreadyOngoing.callingStatus == CallingStatus.ONGOING && checkIsMeetingAlreadyOngoing.participantCount == 2) {
                return res.sendSuccess('Meeting is already in progress. Join now to participate.')
            };
            
            // ----------End----------
            if (isAppointmentBased) {
                if (appointmentId == null) {
                    return res.sendError(
                      "appointmentId id required for appointment based calls."
                    );
                }
                const appointmentDataById = await Appointment.findById(appointmentId);
                if (!appointmentDataById) {
                    return res.sendError("Invalid appointmentId ");
                }
                appointmentData = appointmentDataById;
                if (appointmentData.meetingStatus && appointmentData.meetingId && (appointmentData.meetingStatus == MeetingStatus.STARTED || appointmentData.meetingStatus != MeetingStatus.PENDING)){
                    return res.sendError("meeting already started");
                }
    
                if (appointmentData.therapistId.toString() != therapistId || appointmentData.clientId.toString() != clientId ) {
                    return res.sendError(
                      "You don't have permission to access this appointment"
                    );
                }
    
                if (appointmentData.typeOfMeeting !== "VIDEO") {
                    return res.sendError("Not a type of video appointment.");
                }
                if (appointmentData.status === AppointmentStatus.COMPLETED) {
                    return res.sendError("Already Completed.", 455);
                } else if (appointmentData.status === AppointmentStatus.OVERDUE) {
                    return res.sendError("Overdue Appointment.");
                } else if (appointmentData.status === AppointmentStatus.REJECTED) {
                    return res.sendError("Rejected Appointment.");
                }

                await VideoCallDao.updateAppointmentCallingTries(
                    appointmentId,
                    req.user.role
                );

                meetingDetails = {
                    clientId: clientId,
                    therapistId: therapistId,
                    transcribeAllowed: transcribeAllowed,
                    // transcribeAllowed: false,
                    recordingAllowed:true,
                    transcribingInProcess: false,
                    accepted: false,
                    noOfVideose: 0,
                    transcribeCreated: false,
                    meetingDuration: callDuration,
                    isAppointmentBased: true,
                    appointmentId: appointmentId,
                    audioFiles: [],
                    // recordingAllowed: false,
                    callingStatus: CallingStatus.STARTED,
                    createdBy: req.user._id,
                    recordingSharedWithClient: false,
                    clientIdentifier: newClientIdentifier.toString(),
                    therapistIdentifier: newTherapistIdentifier.toString(),
                    password: newPassword.toString(),
                    isAudioCall,
                    participantCount: 1,
                    clientAllowedTranscribe: true,
                    therapistAllowedTranscribe: true,
                    vonageSessionName,
                    isVonageNativeSDKCall: true,
                    firstSpeaker: therapistId
                };
            } else {
                console.log("xxx03");
                meetingDetails = {
                    clientId: clientId,
                    therapistId: therapistId,
                    transcribeAllowed: transcribeAllowed,
                    // transcribeAllowed: false,
                    transcribingInProcess: false,
                    accepted: false,
                    noOfVideose: 0,
                    transcribeCreated: false,
                    meetingDuration: callDuration,
                    isAppointmentBased: false,
                    audioFiles: [],
                    recordingAllowed: true,
                    // recordingAllowed: false,
                    callingStatus: CallingStatus.STARTED,
                    createdBy: req.user._id,
                    recordingSharedWithClient: false,
                    clientIdentifier: newClientIdentifier.toString(),
                    therapistIdentifier: newTherapistIdentifier.toString(),
                    password: newPassword.toString(),
                    isAudioCall,
                    participantCount: 1,
                    clientAllowedTranscribe: true,
                    therapistAllowedTranscribe: true,
                    vonageSessionName,
                    isVonageNativeSDKCall: true,
                    firstSpeaker: therapistId
                };
            }
            const createdMeeting = await VideoCallDao.createMeeting(meetingDetails);
            if (!createdMeeting) {
                return res.sendError("Meeting Creation Failed.");
            }

            const userForSendEmails = await User.findById(recieverId).select('email primaryPhone');
            const ownDetails = await User.findById(req.user._id).select('firstname lastname');
            if (userForSendEmails && ownDetails) {
              await EmailService.sendEventMeetingLinkEmail(
                userForSendEmails,
                "New Meeting Started",
                "New Meeting Started by",
                // `Please login to ${process.env.APP_URL}/signin to join meeting.
                `\n\nJoin Meeting: ${linkToJoinToMeetingForShare}`,
                ownDetails.firstname + " " + ownDetails.lastname
              );

              if (userForSendEmails.primaryPhone) {
                await SMSService.sendEventSMS(
                  `New Meeting Started by ${ownDetails.firstname} ${ownDetails.lastname}
                  \n\nJoin Meeting: ${linkToJoinToMeetingForShare}`,
                  userForSendEmails.primaryPhone,
                  "Vonage-call-ep 01"
                );
              }
            }

            console.log("linkToJoinToMeetingForShare ", linkToJoinToMeetingForShare)
            
            if(recieverData?.FCMToken) {
                const sendFirebasePushNotification = await NotificationDao.sendFirebasePushNotification(recieverData?.FCMToken, 'Link to join', linkToJoinToMeetingForShare)
            }
            const dataForSend = {
                alreadyStarted: false,
                password: createdMeeting.password,
                meetingId: createdMeeting._id,
                clientIdentifier: createdMeeting.clientIdentifier,
                therapistIdentifier: createdMeeting.therapistIdentifier,
            };
            AppLogger.info(`Vonage Native Call | Successfully initiate the meeting, Meeting ID: ${createdMeeting._id}, Created By: ${req.user._id} `)
            return res.sendSuccess(dataForSend, "call initialization details");  
        } catch (error) {
            AppLogger.error(`Vonage Native Call | Initiate meeting error occured, Session Name: ${req.body.vonageSessionName}, Error: ${error.message} `)
            return res.sendError("call initialization failed. Reason: "+ error.message);
        }
    }

    export function joinVonageNativeVideoCallValidation(): ValidationChain[] {
        return [
          check("vonageSessionName")
            .notEmpty()
            .withMessage("vonageSessionName is required")
            .isString()
            .withMessage("Invalid vonageSessionName."),
        ];
    }

    export async function joinVonageNativeVideoCall(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            AppLogger.info(`Vonage Native Call | Joining the meeting has started. Session Name: ${req.body.vonageSessionName}`);
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]);
            }
            const date = new Date();
            const vonageSessionName = req.body?.vonageSessionName;
            // ----------START----------
            // This line of code check meeting status is ongoing or not when user joined the meeting second time using URL.
            const checkMeetingStatus = await Meeting.findOne(
                { vonageSessionName },
            ).select("callingStatus participantCount");
            
            if (!checkMeetingStatus) {
                return res.sendError('Please wait for the call to be started!');
            }

            if (checkMeetingStatus && checkMeetingStatus.callingStatus== CallingStatus.ONGOING && checkMeetingStatus.participantCount == 2) {
                return res.sendSuccess('Meeting is already in progress. Join now to participate.')
            };

            if (checkMeetingStatus.callingStatus == CallingStatus.CANCELLED || checkMeetingStatus.callingStatus == CallingStatus.COMPLETED) {
                return res.sendError('The meeting has already ended!')
            }
            // ----------END----------
            let preMeetingData;
            const meeting = await Meeting.findOneAndUpdate(
                { vonageSessionName },
                { $set: {
                    accepted: true,
                    callingStatus: CallingStatus.ONGOING,
                    bothJoinedAt: date,
                    participantCount: 2
                  } 
                },
                { new: true }
            )
            if(!meeting){
                return res.sendError('Meeting still not started!');
            }
            preMeetingData = meeting;
            const isAppointmentBased = preMeetingData.isAppointmentBased;
            const clientId = preMeetingData?.clientId;
            const therapistId = preMeetingData?.therapistId;
            const preMeetingCreatedBy = preMeetingData?.createdBy;
            let loggedUser;
            // const transcribeAllowedForUser = req.body?.transcribeAllowedForUser;
            let recieverId;
            if(preMeetingCreatedBy === therapistId){
                recieverId = therapistId;
                loggedUser = clientId;
            } else {
                recieverId = clientId;
                loggedUser = therapistId;
            }
            const friendRequest = await FriendRequest.findOne({
                clientId: clientId,
                therapistId: therapistId,
                status: FriendRequestStatus.APPROVED,
            });
            if (friendRequest == null) {
                return res.sendError("You are not friend with other user.", 453);
            }
            if(isAppointmentBased) {
                const appointmentId = preMeetingData?.appointmentId;
                if (preMeetingData.appointmentId == null) {
                    return res.sendError(
                      "appointmentId id required for appointment based calls."
                    );
                }
                const appointmentData = await Appointment.findById(appointmentId);
                if (!appointmentData || !appointmentData.meetingId) {
                    return res.sendError("Invalid appointmentId ");
                }
                if (appointmentData.typeOfMeeting !== "VIDEO") {
                    return res.sendError("Not a type of video appointment.");
                }
                if (appointmentData.status === AppointmentStatus.COMPLETED) {
                    return res.sendError("Already Completed.", 455);
                }
                if (appointmentData.status === AppointmentStatus.OVERDUE) {
                    return res.sendError("Overdue Appointment.");
                }
                if (appointmentData.status === AppointmentStatus.REJECTED) {
                    return res.sendError("Rejected Appointment.");
                }
            }

            if (preMeetingData) {
                if (preMeetingData.callingStatus !== CallingStatus.STARTED && preMeetingData.callingStatus !== CallingStatus.ONGOING){
                    return res.sendError("Meeting Cancelled", 333);
                }
            } else {
                return res.sendError("Invalid Meeting Id.");
            }
            
            const avatarDetailsAndClientStatus = await VonageNativeCallDao.getAvatarDetailsAndClientStatus(recieverId, loggedUser);
            if (!avatarDetailsAndClientStatus) {
                return res.sendError("invalid recieverId.");
            }

            const { clientPremiumStatus, testSubscriptionStatus, clientSubscriptionId,clientSubStatus, } = avatarDetailsAndClientStatus;
            const isSubscriptionInvalid = !clientSubscriptionId || clientSubscriptionId === "" || clientSubStatus !== SubscriptionStatus.ACTIVE;
            
            if (clientPremiumStatus !== PremiumStatus.ACTIVE && testSubscriptionStatus !== "active") {
                if (isSubscriptionInvalid) {
                  return res.sendError("No valid subscription for client.", 456);
                }
            }

            const avatarDetailsOfOwnUser = await VideoCallDao.getAvatarDetailsForStartCall(loggedUser);
            if (!avatarDetailsOfOwnUser) {
                return res.sendError("invalid user.");
            }

            const recieversLatestDetails = await User.findById(recieverId);
            if (!recieversLatestDetails){
                return res.sendError("No reciever data found!")
            }
            const recieverSocketId = recieversLatestDetails.socketId;
            const finalMeetingData = {
                recordingAllowed: true,
                // sdkToken: sdkToken,
                callIdFromVideoSDK: preMeetingData.meetingId,
                clientSubscriptionId: avatarDetailsAndClientStatus?.clientSubscriptionId,
                clientSubStatus: avatarDetailsAndClientStatus?.clientSubStatus,
                clientPremiumStatus: avatarDetailsAndClientStatus?.clientPremiumStatus,
                isMeetingTimeRemained: avatarDetailsAndClientStatus?.isMeetingTimeRemained,
                remainingMeetingTime: avatarDetailsAndClientStatus?.remainingMeetingTime,
                password: preMeetingData.password,
                meetingId: preMeetingData._id,
                sessionId: preMeetingData.meetingId,
                clientIdentifier: preMeetingData.clientIdentifier,
                therapistIdentifier: preMeetingData.therapistIdentifier,
                isAudioCall: preMeetingData?.isAudioCall,
            };
        
            const finalRecieverData = {
                userId: recieverId,
                useDefaultAvatar: avatarDetailsAndClientStatus?.useDefaultAvatar,
                avatarId: avatarDetailsAndClientStatus?.avatarId,
                avatarBackgroundId: avatarDetailsAndClientStatus?.avatarBackgroundId,
                incognito: avatarDetailsAndClientStatus?.incognito,
                socketId: recieverSocketId,
                callerName: avatarDetailsAndClientStatus?.callerName,
                callRecordingAllowed: avatarDetailsAndClientStatus?.callRecordingAllowed,
            };

            const finalOwnUserData = {
                useDefaultAvatar: avatarDetailsOfOwnUser.useDefaultAvatar,
                avatarId: avatarDetailsOfOwnUser.avatarId,
                avatarBackgroundId: avatarDetailsOfOwnUser.avatarBackgroundId,
            };

            const finalData = {
                meetingData: finalMeetingData,
                recieverData: finalRecieverData,
                ownData: finalOwnUserData,
            };
            AppLogger.info(`Vonage Native Call | Joined to meeting successfully, Session Name: ${vonageSessionName}`)
            return res.sendSuccess(finalData, "Call Details");
        } catch (error) {
            AppLogger.error(`Vonage Native Call | Joined to meeting error occured, Session Name: ${req.body?.vonageSessionName}, Error: ${error.message}`)
            console.log(error);
            
            // await EmailService.sendAdminEmailWhenCallingExeptionEmail(
            //     "Join Zoom Meeting Server Error Occured",
            //     error
            // );
            // return res.sendError("Server Error Occured. " + error);
        }

    }

    export async function cancelVonageNativeCall( req: Request, res: Response, next: NextFunction ) {
        try {
            AppLogger.info(`Vonage Native Call | Meeting ending has started. Session Name: ${req.body.sessionName}`);
            const preMeetingData: any = await Meeting.findOne({
                vonageSessionName: req.body.sessionName,
                callingStatus: { $nin: ['completed', 'cancelled'] }
            });
            if (!preMeetingData) {
                return res.sendError("Meeting already completed or cancelled!")
            }
            let participantCount = preMeetingData.participantCount;
            if(!participantCount){
                participantCount = 1
            }
            let isBothUserJoined = false;
            if(participantCount > 1) {
                isBothUserJoined = true;
            } else {
                isBothUserJoined = false;
            }
            if (preMeetingData.isAppointmentBased) {
                if (
                  preMeetingData.callingStatus === CallingStatus.ONGOING ||
                  preMeetingData.callingStatus === CallingStatus.STARTED
                ) {
                  await Meeting.findByIdAndUpdate(preMeetingData._id, {
                    callingStatus:
                      preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                        ? CallingStatus.COMPLETED
                        : CallingStatus.CANCELLED
                  });
                  await Appointment.findByIdAndUpdate(preMeetingData.appointmentId, {
                    meetingStatus:
                      preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                        ? MeetingStatus.COMPLETED
                        : MeetingStatus.PENDING,
                    status:
                      preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                        ? AppointmentStatus.COMPLETED
                        : AppointmentStatus.PENDING,
                  });
                }
                const result = await ZoomVideoCallEP.createDiagnosisNote(
                  preMeetingData._id
                );
            } else {
                if (
                  preMeetingData.callingStatus === CallingStatus.ONGOING ||
                  preMeetingData.callingStatus === CallingStatus.STARTED
                ) {
      
                  await Meeting.findByIdAndUpdate(preMeetingData._id, {
                    callingStatus:
                      preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                        ? CallingStatus.COMPLETED
                        : CallingStatus.CANCELLED
                  });
                }
                const result = await ZoomVideoCallEP.createDiagnosisNote(preMeetingData._id);
            }
            AppLogger.info(`Vonage Native Call | Meeting ended successfully, Session Name: ${req.body.sessionName}`);
            const finalRes = {
                participantCount,
                _id:preMeetingData._id
            }
            return res.sendSuccess(finalRes, "Canceled vonage call success");
        } catch (error) {
            AppLogger.error(`Vonage Native Call | Meeting ended error occurred, Session Name: ${req.body.sessionName}, Error: ${error}`);
        }
        
    }

    export async function updateUserFCMToken( req: Request, res: Response, next: NextFunction ) {
        try {
            AppLogger.info(`FCM Token | Updating FCM token has started.`);
            const userId = req.user._id;
            const fcmToken = req?.body?.userFCMToken;

            if (userId && fcmToken) {
                const updateResult = await UserDao.updateUserFCMToken(userId, fcmToken);
                if (updateResult) {
                    AppLogger.info(`FCM Token | FCM token updated successfully, User ID: ${userId}`);
                    return res.sendSuccess('FCM token updated successfully')
                } else {
                    AppLogger.error(`FCM Token | FCM token updated error occured, User ID: ${userId}`);
                    return res.sendError(updateResult.error);
                }
            }
        } catch (error) {
            AppLogger.error(`FCM Token | FCM token updated error occured, User ID: ${req.user._id}, Error: ${error.message}`);
            return res.sendError(error.message);
        }
    }

    export async function getAllOngoingMeetingsByUserId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            AppLogger.info(`Vonage Native Call | Retrieving ongoing meetings has started.`);
            const userRole = req?.user.role;
            const userId = new Types.ObjectId(req?.user._id)
        
            const incompletedMeetingsResponse = await VonageNativeCallDao.getIncompleteMeetingsByUserId(userId, userRole)
            const finalResponse = {
                ongoingMeetingData: incompletedMeetingsResponse,
            };
            AppLogger.info(`Vonage Native Call | Retreive ongoing meetings successfully, User ID: ${userId}`);
            return res.sendSuccess(finalResponse, "Success");
        } catch (error) {
            AppLogger.error(`Vonage Native Call | Retreive ongoing meetings error occured, User ID: ${req?.user.role}, Error: ${error.message}`);
            return res.sendError(error.message);
        }
    }

    export async function startCaptionsVonageNativeSDK(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const sessionId = req.body?.sessionId;
        const token = req.body?.token;
        if (!sessionId || !token) {
            return res.sendError('SessionId or token not found for start captions.');
        }
        AppLogger.info(`Vonage Native Call | start captions. | sessionId : ${sessionId}`);
            
        const expires: number = Math.floor(new Date().getTime() / 1000) + (24 * 60 * 60);
        const projectJWT = projectToken(apiKey, secret, expires);
        const captionURL = `${opentokUrl}/${apiKey}/captions`;
        const captionPostBody = {
            sessionId,
            token: req.body.token,
            languageCode: 'en-US',
            partialCaptions: 'false',
        };
            
        try {
            const captionResponse = await axios.post(captionURL, captionPostBody, {
                headers: {
                  'X-OPENTOK-AUTH': projectJWT,
                  'Content-Type': 'application/json',
                },
            });
            console.log("captionResponse captionResponse captionResponse ",captionResponse.data);
              
            const captionsId = captionResponse.data.captionsId;
            return res.sendSuccess({captionsId}, "Successfully started captions");
        } catch (error) {
            AppLogger.error(`Vonage Native Call | Retreive ongoing meetings error occured, User ID: ${req?.user.role}, Error: ${error.message}, sessionId: ${req.body.sessionId}`);
            return res.sendError(error.message);
        }
    }

    export async function stopCaptionsVonageNativeSDK(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const captionsId = req.params.captionsId;

        // With custom expiry (Default 30 days)
        const expires: number = Math.floor(new Date().getTime() / 1000) + (24 * 60 * 60);
        const projectJWT = projectToken(apiKey, secret, expires);
      
        const captionURL = `${opentokUrl}/${apiKey}/captions/${captionsId}/stop`;
      
        try {
          const captionResponse = await axios.post(captionURL, {}, {
            headers: {
              'X-OPENTOK-AUTH': projectJWT,
              'Content-Type': 'application/json',
            },
          });
          console.log(captionResponse.data);
          
          return res.sendSuccess({ status: captionResponse.status }, "transcription stopped successfully");
        } catch (err) {
          console.warn(err);
          return res.sendError(`Error stopping transcription services: ${err}`);
        }
    }

    // export async function updateTranscribesWhenCallEnds(
    //     req: Request,
    //     res: Response,
    //     next: NextFunction
    // ) {
    //     const { vonageSessionName, transcripts } = req.body;
    //     console.log(req.body);
    //     console.log("transcripts ", transcripts);

    //     if (!vonageSessionName) {
    //         return res.sendError('No vonageSessionName found');
    //     }

    //     if(!transcripts) {
    //         return res.sendError('No transcript found for update');
    //     }

    //     const preMeetingData = await VonageNativeCallDao.getMeetingDetailsBySessionId(vonageSessionName);

    //     if (!preMeetingData){
    //         AppLogger.error(`No meeting details found for vonageSessionName: ${vonageSessionName}`);
    //         return res.sendError(`No meeting details found.`)
    //     }
    //     const session = await mongoose.startSession(); // Start a database session
    //     session.startTransaction();

    //     try {
    //         console.log("01");
    //         // Find the meeting transcript (if exists)
    //         let transcriptRecord = await TranscribeSchema.findOne({ meetingId: preMeetingData.meetingId }).session(session);
    //         console.log("02")
    //         if (transcriptRecord) {
    //           // If transcript exists, append new transcripts safely
    //           await TranscribeSchema.updateOne(
    //             { meetingId: preMeetingData.meetingId },
    //             {
    //               $push: { transcripts: { $each: transcripts } }, // Append new transcripts
    //             },
    //             { session }
    //           );
    //           console.log("03")
    //         } else {
    //           // If transcript doesn't exist, create a new one
    //           console.log("04")
    //           const dataForTranscribe: any = {
    //             transcriptText: transcripts,
    //             clientId: preMeetingData?.clientId,
    //             meetingId: preMeetingData?.meetingId,
    //             therapistId: preMeetingData?.therapistId,
    //             transCribeInProcess: false,
    //             speakersArray: [
    //               preMeetingData?.clientId,
    //               preMeetingData?.therapistId
    //             ],
    //             meetingStartedTime: preMeetingData?.createdAt,
    //             videoUrl: "",
    //             speakersDetected: false
    //           }
    //           console.log("05")
    //           transcriptRecord = new TranscribeSchema(dataForTranscribe);
    //           console.log("06")
    //           await transcriptRecord.save({ session });
    //           console.log("07")
    //         }
        
    //         // Commit transaction (save changes)
    //         await session.commitTransaction();
    //         session.endSession();
    //         AppLogger.info(`Vonage Native, Transcribes updated successfully for meetingId: ${preMeetingData.meetingId}`);
    //         return res.sendSuccess(`Transcripts updated successfully for meetingId: ${preMeetingData.meetingId}`);
    //     } catch (error) {
    //         // Rollback transaction if error occurs
    //         await session.abortTransaction();
    //         session.endSession();
    //         AppLogger.error(`Vonage Native, Internal Server Error when update transcribes, meetingId: ${preMeetingData.meetingId}, error: ${error}`);
    //         return res.sendError(`Internal Server Error when update transcribes ${error}`);
    //     }
    // }

    export async function cleanupSpecificSession(key: string) {
        if (key) {
            try {
                if (captionsDB.has(key)) {
                    captionsDB.delete(key);
                    AppLogger.info(`Successfully deleted captions for session: ${key}`);
                }else {
                    AppLogger.warn(`No captions found for session: ${key}`);
                }
            } catch (error) {
                AppLogger.error(`Error deleting captions for session ${key}: ${error}`);
            }
            
        }
    }

    const captionsDB = captionsDBModel;
    export async function updateTranscribesWhenCallEnds(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const { vonageSessionName, transcripts, loggedUserRole } = req.body;
        

        if (!vonageSessionName) {
            return res.sendError('No vonageSessionName found');
        }

        if(!Array.isArray(transcripts) || transcripts.length === 0) {
            AppLogger.error(`Vonage Native, Invalid or empty transcripts array for vonageSessionName: ${vonageSessionName}, loggedUserRole: ${loggedUserRole}.`);
            return res.sendError('Invalid or empty transcripts array');
        }

        const preMeetingData = await VonageNativeCallDao.getMeetingDetailsBySessionId(vonageSessionName);
        AppLogger.info(`Vonage Native, Transcribes updated function called for meetingId: ${preMeetingData.meetingId}`);
        if (!preMeetingData){
            AppLogger.error(`No meeting details found for vonageSessionName: ${vonageSessionName}`);
            return res.sendError(`No meeting details found.`)
        }
        try {

            const key = `${vonageSessionName}`;
            if (!captionsDB.has(key)) {
                captionsDB.set(key, { timestamp: Date.now() });
            }

            setTimeout(() => cleanupSpecificSession(key), 60000);

            const sessionData  = captionsDB.get(key);

            if (loggedUserRole === UserRole.THERAPIST) {
                sessionData.therapistCaptions = transcripts;
            } else {
                sessionData.clientCaptions = transcripts;
            }

            sessionData.timestamp = Date.now();

            if (sessionData.therapistCaptions && sessionData.clientCaptions) {
                const combinedCaptions = [ ...sessionData.therapistCaptions, ...sessionData.clientCaptions];
                const arr = combinedCaptions
                    .sort((a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()) // Use .getTime()
                    .map((item: any) => item.text);
                captionsDB.delete(key);
                console.log("04", arr)
                const dataForTranscribe: any = {
                    transcriptText: arr,
                    clientId: preMeetingData?.clientId,
                    meetingId: preMeetingData?.meetingId,
                    therapistId: preMeetingData?.therapistId,
                    transCribeInProcess: false,
                    speakersArray: [
                        preMeetingData?.clientId,
                        preMeetingData?.therapistId
                    ],
                    meetingStartedTime: preMeetingData?.createdAt,
                    videoUrl: "",
                    speakersDetected: false
                }
                console.log("05")
                const transcriptRecord = new TranscribeSchema(dataForTranscribe);
                console.log("06")
                await transcriptRecord.save();

                const preMeetingId = new Types.ObjectId(preMeetingData._id);
                await VonageNativeCallDao.updateTranscribeCreatedFieldById(preMeetingId);
                console.log("07")
                AppLogger.info(`Vonage Native, Transcribes updated successfully for meetingId: ${preMeetingData.meetingId}`);
                return res.sendSuccess(`Transcripts updated successfully for meetingId: ${preMeetingData.meetingId}`);
            }
            AppLogger.info(`Vonage Native, First transcribe updated for meetingId: ${preMeetingData.meetingId}`);
            return res.sendSuccess(`First transcribe updated`);
              
        } catch (error) {
            AppLogger.error(`Vonage Native, Internal Server Error when update transcribes, meetingId: ${preMeetingData.meetingId}, error: ${error}`);
            return res.sendError(`Internal Server Error when update transcribes ${error}`);
        }
    }

    export async function startArchiveVonageNativeSdkCall(req: Request, res: Response) {
        const sessionId = req.params.sessionId;
        const date = new Date();
        AppLogger.info(`start archive function called for session ID: ${sessionId}`)
        try {
          var archiveOptions = {
            name: sessionId,
            hasVideo: false, // Record audio only
          };
          opentok.startArchive(sessionId, archiveOptions, async function (err: any, archive: any) {
            if (err) {
                AppLogger.error(`Vonage Native | Start archive error occured for sssion ID: ${sessionId} error: ${err}`);
                return res.sendError("Start archive error occured");
            } else {
                const updateArchiveIdInMeeting = await Meeting.findOneAndUpdate(
                    { meetingId: sessionId, },
                    { $set: {
                      vonageArchiveId: archive.id
                    } },
                    { new: true }
                  );
                AppLogger.info(`Vonage Native | Started archive successfully for sssion ID: ${sessionId}`);
                return res.sendSuccess({archiveId: archive.id}, "Archive started successfully");
            }
          });
        } catch (error) {
            AppLogger.error(`Vonage Native | Error occured when start archieve for sssion ID: ${sessionId} error: ${error}`);
            return res.sendError(error.message)
        }
      }
    
      export async function stopArchiveVonageNativeSdkCall(req: Request, res: Response) {
        const sessionName = req.params.sessionName;
        try {
            const preMeeting = await Meeting.findOne(
                { vonageSessionName: sessionName },
                { vonageArchiveId: 1, _id: 0 } 
            );
            if (!preMeeting) {
                return res.sendError("Meeting not found!")
            }
            opentok.stopArchive(preMeeting.vonageArchiveId, function (err: any, archive: any) {
                if (err) return console.log(err);
        
                return res.sendSuccess(`Stop archive for sessionName: ${sessionName}.`)
            });
    
        } catch (error) {
          return res.sendError(error.message)
        }
      }
}
