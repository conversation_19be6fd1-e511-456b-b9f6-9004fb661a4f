import { NextFunction, Request, Response } from "express";
import { UserRole } from "../models/user-model";
import axios from 'axios';
import { Types } from 'mongoose';
import JotformSubmission, { IJotformSubmission } from '../schemas/jotform-submissions';
import Jotform from '../schemas/jotforms';
import User from '../schemas/user-schema';
import Insurance from '../schemas/insurance-schema';
import InsuranceCompany from '../schemas/Insurance-company-schema';
import DiagnosisNote from '../schemas/diagnosis-note-schema';
import { AdminSubmitClaimByJotFormModel } from "../models/admin-submit-claim-jotform-model";

// Interface for client information
interface ClientInfo {
    _id: string;
    name: string;
    firstname?: string;
    lastname?: string;
    email?: string;
    memberId?: string;
    organizationName?: string;
    providerName?: string;
    providerNPI?: string;
    dateOfBirth?: string;
    gender?: string;
    city?: string;
    address1?: string;
    state?: string;
    postalCode?: string;
    diag_1?: string;
    diag_2?: string;
}

// Interface for the enriched JotForm submission with client information
interface EnrichedJotformSubmission extends Omit<IJotformSubmission, '_id'> {
    _id: string;
    client?: ClientInfo;
    claims?: number;
}

const JOTFORM_API_KEY = process.env.JOTFORM_API_KEY;
const JOTFORM_API_BASE_URL = process.env.JOTFORM_API_BASE_URL;


export class JotFormEp {
    /**
     * Get enabled forms
     */
    static async getEnabledForms(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            // Find all forms with status ENABLED
            const forms = await Jotform.find({
                status: "ENABLED"
            }).sort({ createdAt: -1 }); // Sort by creation date, newest first

            return res.status(200).json({
                success: true,
                message: "Enabled forms retrieved successfully",
                data: forms
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: "Error retrieving enabled forms",
                error: error.message
            });
        }
    }

    /**
     * Get submissions by client and therapist
     */
    static async getJotFormByClientIdViaTherapist(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { therapistId,clientId } = req.params;

            // Validate ObjectIds
            if (!Types.ObjectId.isValid(clientId) || !Types.ObjectId.isValid(therapistId)) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid clientId or therapistId format"
                });
            }

            // Find submissions matching both clientId and therapistId
            const submissions = await JotformSubmission.find({
                clientId: new Types.ObjectId(clientId),
                $or: [
                    { therapistId: new Types.ObjectId(therapistId) },
                    { sharedTherapist: new Types.ObjectId(therapistId) }
                ],
                status: "ACTIVE"
            }).sort({ submissionDate: -1 }); // Sort by submission date, newest first

            return res.status(200).json({
                success: true,
                message: "JotForms retrieved successfully",
                data: submissions
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: "Error retrieving JotForms",
                error: error.message
            });
        }
    }

    /**
     * Get submissions by client ID
     */
    static async getJotFormByClientId(req: Request, res: Response) {
        try {
            const { clientId } = req.params;

            // Validate ObjectId format
            if (!Types.ObjectId.isValid(clientId)) {
                return res.status(400).json({ message: "Invalid clientId format" });
            }

            // Find submissions for this client
            const submissions = await JotformSubmission.find({
                clientId: new Types.ObjectId(clientId),
                status: "ACTIVE"
            }).sort({ submissionDate: -1 }); // Sort by submission date, newest first

            return res.json(submissions);
        } catch (error) {
            console.error("Error getting submissions:", error);
            return res.status(500).json({ message: "Internal server error" });
        }
    }

    /**
     * Get submissions by client ID only without therapist filter
     * This is a new endpoint that ignores therapist restrictions
     */
    static async getJotFormByClientIdOnly(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { clientId } = req.params;

            // Validate ObjectId format
            if (!Types.ObjectId.isValid(clientId)) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid clientId format"
                });
            }

            // Find submissions only filtering by clientId
            const submissions = await JotformSubmission.find({
                clientId: new Types.ObjectId(clientId),
                status: "ACTIVE"
            }).sort({ submissionDate: -1 }); // Sort by submission date, newest first

            return res.status(200).json({
                success: true,
                message: "JotForms retrieved successfully",
                data: submissions
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: "Error retrieving JotForms",
                error: error.message
            });
        }
    }

    /**
     * Get all JotForm submissions - Admin only access
     * This endpoint is restricted to admin users only
     * @param req Request object
     * @param res Response object
     * @param next NextFunction
     */
    static async getAllJotFormsAdmin(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            // Check if user is admin
            if (
                req.user.role !== UserRole.SUPER_ADMIN &&
                req.user.role !== UserRole.SUB_ADMIN &&
                req.user.role !== UserRole.ADMIN
            ) {
                return res.status(403).json({
                    success: false,
                    message: "Access denied. Admin privileges required."
                });
            }

            // Find all active submissions
            const submissions = await JotformSubmission.find({
                status: "ACTIVE"
            }).sort({ submissionDate: -1 }); // Sort by submission date, newest first

            // Get client information for each submission
            const enrichedSubmissions = await Promise.all(
                submissions.map(async (submission) => {
                    // Convert submission to a plain JavaScript object
                    const submissionObj = submission.toObject();
                    
                    // Create a new object with the proper type
                    const enrichedObj = submissionObj as unknown as EnrichedJotformSubmission;
                    
                    // If clientId exists, find the client information
                    if (enrichedObj.clientId) {
                        try {
                            const client = await User.findById(enrichedObj.clientId);
                            if (client) {
                                // Format date of birth if it exists
                                let formattedDateOfBirth = '';
                                if (client.dateOfBirth) {
                                    const dob = new Date(client.dateOfBirth);
                                    formattedDateOfBirth = dob.toLocaleDateString('en-US', {
                                        month: '2-digit',
                                        day: '2-digit',
                                        year: 'numeric'
                                    });
                                }
                                
                                // Add nested client information to the enriched object
                                enrichedObj.client = {
                                    _id: client._id.toString(),
                                    name: `${client.firstname} ${client.lastname}`,
                                    firstname: client.firstname,
                                    lastname: client.lastname,
                                    email: client.email,
                                    dateOfBirth: formattedDateOfBirth,
                                    gender: client.gender || "",
                                    providerName: process.env.LAVNI_ORGANIZATION_NAME || "lavni",
                                    providerNPI: process.env.LAVNI_NPI || ""
                                };
                                
                                // Find insurance information for the client
                                const insurance = await Insurance.findOne({ clientId: client._id });
                                if (insurance) {
                                    // Add memberId from insurance subscriber
                                    if (insurance.subscriber && insurance.subscriber.memberId) {
                                        enrichedObj.client.memberId = insurance.subscriber.memberId;
                                    }
                                    
                                    // Thêm city và address1 từ insurance
                                    const subscriber = insurance.subscriber || {};
                                    // Kiểm tra subscriber.address tồn tại
                                    const addr = typeof subscriber === 'object' && subscriber !== null && 'address' in subscriber ? 
                                        subscriber.address || {} : {};
                                    
                                    // Đảm bảo addr có các thuộc tính cần thiết
                                    const address1 = typeof addr === 'object' && addr !== null && 'address1' in addr ? addr.address1 as string : "";
                                    const city = typeof addr === 'object' && addr !== null && 'city' in addr ? addr.city as string : "";
                                    const state = typeof addr === 'object' && addr !== null && 'state' in addr ? addr.state as string : "";
                                    const postalCode = typeof addr === 'object' && addr !== null && 'postalCode' in addr ? addr.postalCode as string : "";
                                    
                                    enrichedObj.client.address1 = address1;
                                    enrichedObj.client.city = city;
                                    enrichedObj.client.state = state;
                                    enrichedObj.client.postalCode = postalCode;
                                    
                                    // Tìm thông tin chẩn đoán từ DiagnosisNote
                                    try {
                                        const diagnosisNotes = await DiagnosisNote.findOne({ clientId: client._id })
                                            .sort({ createdAt: -1 }); // Lấy bản ghi mới nhất
                                            
                                        if (diagnosisNotes) {
                                            let diag1 = "";
                                            let diag2 = "";
                                            
                                            // Lấy mã chẩn đoán chính
                                            if (diagnosisNotes.diagnosisICDcodes && diagnosisNotes.diagnosisICDcodes.length > 0) {
                                                diag1 = diagnosisNotes.diagnosisICDcodes[0].value?.replace(".", "") || "";
                                            }
                                            
                                            // Lấy mã chẩn đoán phụ
                                            if (diagnosisNotes.secondaryDiagnosisICDcodes && diagnosisNotes.secondaryDiagnosisICDcodes.length > 0) {
                                                diag2 = diagnosisNotes.secondaryDiagnosisICDcodes[0].value?.replace(".", "") || "";
                                            }
                                            
                                            enrichedObj.client.diag_1 = diag1;
                                            enrichedObj.client.diag_2 = diag2;
                                        } else {
                                            // Nếu không tìm thấy bản ghi thì đặt giá trị null
                                            enrichedObj.client.diag_1 = null;
                                            enrichedObj.client.diag_2 = null;
                                        }
                                    } catch (error) {
                                        console.error(`Error fetching diagnosis data for client ID ${client._id}:`, error);
                                        enrichedObj.client.diag_1 = null;
                                        enrichedObj.client.diag_2 = null;
                                    }
                                    
                                    // Find insurance company information
                                    if (insurance.insuranceCompanyId) {
                                        const insuranceCompany = await InsuranceCompany.findById(insurance.insuranceCompanyId);
                                        if (insuranceCompany && insuranceCompany.organizationName) {
                                            enrichedObj.client.organizationName = insuranceCompany.organizationName;
                                        }
                                    }
                                }
                                
                                // Keep clientId for backward compatibility
                                // delete enrichedObj.clientId;
                            }
                        } catch (error) {
                            console.error(`Error fetching client data for ID ${enrichedObj.clientId}:`, error);
                        }
                    }
                    
                    // Đếm số lượng claims dựa trên clientId
                    if (enrichedObj.clientId) {
                        try {
                            const claimsCount = await AdminSubmitClaimByJotFormModel.countDocuments({
                                clientId: enrichedObj.clientId
                            });
                            enrichedObj.claims = claimsCount;
                        } catch (error) {
                            console.error(`Error counting claims for client ID ${enrichedObj.clientId}:`, error);
                            enrichedObj.claims = 0;
                        }
                    } else {
                        enrichedObj.claims = 0;
                    }
                    
                    return enrichedObj;
                })
            );

            return res.status(200).json({
                success: true,
                message: "All JotForms retrieved successfully",
                data: enrichedSubmissions
            });
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: "Error retrieving JotForms",
                error: error.message
            });
        }
    }

    /**
     * Generate PDF from JotForm submission
     * @param req Request object containing formId and submissionId in params
     * @param res Response object
     * @param next NextFunction
     * @returns PDF file stream
     */
    static async generatePDF(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { formId, submissionId } = req.params;
            const { pdf_type = 'smart' } = req.query; // Default to 'smart' if not specified

            let response;
            
            if (pdf_type === 'smart') {
                // Use the smart PDF converter endpoint
                response = await axios({
                    method: 'get',
                    url: `${JOTFORM_API_BASE_URL}/pdf-converter/${formId}/fill-pdf`,
                    params: {
                        submissionID: submissionId,
                        apiKey: JOTFORM_API_KEY,
                        download: 1
                    },
                    responseType: 'stream'
                });
            } else {
                // Use the traditional PDF generator endpoint
                response = await axios({
                    method: 'get',
                    url: `${JOTFORM_API_BASE_URL}/generatePDF`,
                    params: {
                        formid: formId,
                        submissionid: submissionId,
                        apiKey: JOTFORM_API_KEY,
                        download: 1
                    },
                    responseType: 'stream'
                });
            }

            // Forward the PDF to the client
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', 'inline');  
            
            response.data.pipe(res);
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: "Error generating PDF from JotForm",
                error: error.message
            });
        }
    }

    /**
     * Update shared therapists for a submission
     */
    static async updateSharedTherapists(req: Request, res: Response) {
        try {
            const { submissionId } = req.params;
            const { sharedTherapists } = req.body;

            if (!Array.isArray(sharedTherapists)) {
                return res.status(400).json({ message: "sharedTherapists must be an array" });
            }

            // Validate all therapist IDs
            const validTherapistIds = sharedTherapists.filter(id => Types.ObjectId.isValid(id));

            const submission = await JotformSubmission.findOneAndUpdate(
                { submissionId },
                { 
                    sharedTherapist: validTherapistIds.map(id => new Types.ObjectId(id))
                },
                { new: true }
            );

            if (!submission) {
                return res.status(404).json({ message: "Submission not found" });
            }

            return res.json(submission);
        } catch (error) {
            console.error("Error updating shared therapists:", error);
            return res.status(500).json({ message: "Internal server error" });
        }
    }
}
