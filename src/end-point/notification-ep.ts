import { NextFunction, Request, Response } from "express";
import { check, validationResult } from "express-validator";
import { Types } from "mongoose";
import multer = require("multer");
import path = require("path");
import { NotificationDao } from "../dao/notification-dao";
import { UserDao } from "../dao/user-dao";
import { DNotification, NotificationEvent } from "../models/notification-model";
import { UserRole } from "../models/user-model";
import Notification from "../schemas/notification-schema";
let mongoose = require("mongoose");
var fs = require("fs");

export namespace NotificationEp {
  export async function getNotifications(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const user = req.user;
      const userId = req.user._id;
      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);

      if (user.role == UserRole.SUPER_ADMIN || user.role == UserRole.SUB_ADMIN) {
        if(req.user.role == UserRole.SUB_ADMIN){
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if(ownUser.adminPermission.notifications != true){
            return res.sendError(
              "You don't have permission for Notifications!"
            );
          }
        }
      } 

      const notifications = await NotificationDao.getNotifications(
        userId,
        limit,
        offset
      );

      const unreadCount = await NotificationDao.getUnreadNotificationsCount(userId);

      return res.sendSuccess(
        { notifications: notifications, unreadCount: unreadCount },
        "Notifications Found."
      );

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function markAllNotificationsAsRead(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const user = req.user;
      const userId = req.user._id;
      let notifications;
      if (user.role == UserRole.SUPER_ADMIN || user.role == UserRole.SUB_ADMIN) {
        notifications = await Notification.updateMany(
          { receiverId: userId },
          { $set: { readStatus: true } },
          { new: true }
        );

        if (notifications) {
          notifications = true;
        } else {
          notifications = false;
        }
        
      } else {
        notifications = await NotificationDao.markAllNotificationsAsRead(
          userId
        );
      }

      return res.sendSuccess(notifications, "Notifications Marked As Read.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function notificationValidationRules() {
    return [
      check("receiverId")
        .not()
        .isEmpty()
        .withMessage("receiverId is required."),
      check("event").not().isEmpty().withMessage("event is required."),
      check("link").not().isEmpty().withMessage("link is required."),
      check("content").not().isEmpty().withMessage("content is required."),
    ];
  }

  export async function createNotification(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.user._id;
    const receiverId = req.body.receiverId;
    const event = req.body.event;
    const link = req.body.link;
    const content = req.body.content;
    const variant = req.body.variant;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!mongoose.Types.ObjectId.isValid(receiverId)) {
      return res.sendError("Invalid receiver Id.");
    }

    if (!Object.values(NotificationEvent).includes(event)) {
      return res.sendError("Invalid event selected.");
    }

    if (
      role == UserRole.CLIENT ||
      role == UserRole.THERAPIST ||
      role == UserRole.SUPER_ADMIN ||
      role == UserRole.SUB_ADMIN
    ) {
      const notificationDetails: DNotification = {
        senderId: userId,
        receiverId: receiverId,
        event: event,
        link: link,
        content: content,
        variant: variant,
        readStatus: false,
      };

      try {
        await NotificationDao.createNotification(notificationDetails);

        const receiverUser = await UserDao.getUserById(receiverId);

        return res.sendSuccess(
          receiverUser,
          "Notification Is Created Successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function deleteNotification(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const user = req.user;
      const userId = req.user._id;
      const notificationId = req.params.notificationId;
      if (!mongoose.Types.ObjectId.isValid(notificationId)) {
        return res.sendError("Invalid notificationId.");
      }

      let notification: any = await NotificationDao.getNotificationById(
        notificationId
      );

      if (notification == null) {
        return res.sendError("No notification found for the provided Id.");
      }

      
      if (notification.receiverId != userId.toString()) {
        return res.sendError(
          "You are not authorized to delete this notification."
        );
      }
      

      try {
        let deletedNotification = await NotificationDao.deleteNotification(
          notificationId
        );

        return res.sendSuccess(deletedNotification, "Notification deleted.");
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function markClientAllTwilioSmsMessageNotificationsAsRead(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const clientId = req.params.clientId;
      const userId = req.user._id;

      if (!clientId || !userId) {
        return res.sendError("Invalid data ");
      }

      if (!mongoose.Types.ObjectId.isValid(clientId)) {
        return res.sendError("Invalid data.");
      }

      await Notification.updateMany(
        {
          receiverId: userId,
          senderId: Types.ObjectId(clientId),
          event: NotificationEvent.NEW_TWILIO_MESSAGE_FROM_CLIENT,
        },
        { $set: { readStatus: true, twilioMessageReadStatus: true } },
        { new: true }
      );

      return res.sendSuccess("Messages Marked As Read.");
    } catch (error) {
      return res.sendError(error);
    }
  }
}
