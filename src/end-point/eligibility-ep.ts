import { Request, Response, NextFunction } from "express";
import { AdminDao } from "../dao/admin-dao";
import { Types } from "mongoose";
import { AppLogger } from "../common/logging";
import { checkClientEligibility } from "../services/eligibility-service";
import Client from "../schemas/client-schema";

// Hàm sleep để đợi một khoảng thời gian
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export namespace EligibilityEp {
  /**
   * API endpoint để kiểm tra eligibility của bảo hiểm cho một client
   */
  export async function checkEligibilityMD(req: Request, res: Response, next: NextFunction) {
    try {
      const clientId = req.params.userId;
      AppLogger.info(`EligibilityEp.checkEligibilityMD called for clientId: ${clientId}`);

      if (!clientId) {
        return res.sendError("user id is required");
      }

      const client = await AdminDao.getUserById(Types.ObjectId(clientId));

      if (!client) {
        return res.sendError("Invalid user id");
      }

      // Sử dụng service chung để kiểm tra eligibility
      const eligibilityResults = await checkClientEligibility(client);

      if (eligibilityResults.success) {
        // Tạo định dạng phản hồi giống với API cũ
        const claimEligibilityData = {
          claimEligibilityDetails: eligibilityResults.primaryInsurance.eligibilityInfoData,
          // Thêm các thông tin bổ sung để theo dõi nhưng không phá vỡ compatibility
          _meta: {
            primaryInsurance: {
              status: eligibilityResults.primaryInsurance.status,
              updated: eligibilityResults.primaryInsurance.updated
            },
            secondaryInsurance: {
              status: eligibilityResults.secondaryInsurance.status,
              updated: eligibilityResults.secondaryInsurance.updated
            },
            premiumStatusUpdated: eligibilityResults.premiumStatusUpdated,
            clientAddressUpdated: eligibilityResults.clientAddressUpdated
          }
        };

        AppLogger.info(`EligibilityEp.checkEligibilityMD successful for clientId: ${clientId}, client address updated: ${eligibilityResults.clientAddressUpdated}`);
        return res.sendSuccess(claimEligibilityData, "Success");
      } else if (eligibilityResults.primaryInsurance.error) {
        AppLogger.error(`EligibilityEp.checkEligibilityMD failed for clientId: ${clientId}, error: ${eligibilityResults.primaryInsurance.error}`);
        return res.sendError(eligibilityResults.primaryInsurance.error);
      } else {
        AppLogger.error(`EligibilityEp.checkEligibilityMD failed for clientId: ${clientId}, insurance company not available`);
        return res.sendError("Client insurance company not available. Please try again later.");
      }
    } catch (error) {
      AppLogger.error(`EligibilityEp.checkEligibilityMD exception for clientId: ${req.params.userId}`, error);
      return res.sendError(error);
    }
  }

  /**
   * Cron job để kiểm tra eligibility của bảo hiểm cho tất cả client vào đầu tháng
   */
  export async function checkEligibilityMDOnFirstOfMonth() {
    try {
      AppLogger.info(`.:: Check EligibilityMD On First Of Month ::.`);

      const clientList = await Client.find({ insuranceId: { $exists: true, $ne: null } });
      
      const results = {
        total: clientList.length,
        success: 0,
        failed: 0
      };

      // Xử lý theo batches với 50 client mỗi lần
      const BATCH_SIZE = 1;
      const batches = [];
      
      // Chia danh sách thành các batches
      for (let i = 0; i < clientList.length; i += BATCH_SIZE) {
        batches.push(clientList.slice(i, i + BATCH_SIZE));
      }
      
      // Xử lý từng batch một
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        AppLogger.info(`.:: Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} clients ::.`);
        
        await Promise.all(
          batch.map(async (client) => {
            try {
              const eligibilityResult = await checkClientEligibility(client);
              
              if (eligibilityResult.success) {
                results.success++;
                AppLogger.info(`Eligibility check successful for client ${client._id}`);
              } else {
                results.failed++;
                AppLogger.error(`Eligibility check failed for client ${client._id}: ${eligibilityResult.primaryInsurance.error}`);
              }
            } catch (error) {
              results.failed++;
              AppLogger.error(`.:: Check EligibilityMD On First Of Month Failed for ${client?.firstname ?? ""} ${client?.lastname ?? ""} ::.`, error);
            }
          })
        );
        
        // Đợi 1 giây trước khi xử lý batch tiếp theo
        await sleep(1000);
      }

      AppLogger.info(`.:: Check EligibilityMD On First Of Month Completed Successfully ::. Total: ${results.total}, Success: ${results.success}, Failed: ${results.failed}`);
    } catch (error) {
      AppLogger.error(`.:: Check EligibilityMD On First Of Month Failed ::.`, error);
    }
  }
} 