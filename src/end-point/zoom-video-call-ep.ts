import { NextFunction, Request, Response } from "express";
import { check, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, validation<PERSON><PERSON>ult } from "express-validator";
import moment = require("moment");
import { Util } from "../common/util";
import { VideoCallDao } from "../dao/videocall-dao";
import { ZoomVideoCallHelper } from "../helpers/zoom-video-call-helper";
import { EmailService } from "../mail/config";
import { Types } from "mongoose";
import {
  AppointmentStatus,
  ApprovalStatus,
  MeetingStatus,
} from "../models/appointment-model";
import {
  IClient,
  PremiumStatus,
  SubscriptionStatus,
} from "../models/client-model";
import { FriendRequestStatus } from "../models/friend-request-model";
import { CallingStatus, DMeeting, IMeeting } from "../models/meeting-model";
import { UserRole } from "../models/user-model";
import Appointment from "../schemas/appointment-schema";
import FriendRequest from "../schemas/friend-request-schema";
import Meeting from "../schemas/meeting-schema";
import Therapist from "../schemas/therapist-schema";
import User from "../schemas/user-schema";
import { SMSService } from "../sms/config";
import fetch from "node-fetch";
import { TransactionsDao } from "../dao/transactions-dao";
import { TransactionType } from "../models/transaction-model";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import { DTreatmentHistory } from "../models/treatment-history-model";
import { TherapistDao } from "../dao/therapist-dao";
import { AppLogger } from "../common/logging";
import { DDiagnosisNote } from "../models/diagnosis-note-model";
import { UploadCategory } from "./user-ep";
import { DUpload } from "../models/upload-model";
import { UploadDao } from "../dao/upload-dao";
import Transcribe from "../schemas/transcribe-schema";
import { DTranscribe } from "../models/transcribe-model";
const https = require("https");
const fs = require("fs");
// const { Deepgram } = require("@deepgram/sdk");
// const deepgram = new Deepgram(process.env.DEEPGRAM_KEY);

export namespace ZoomVideoCallEP {
  export async function startZoomVideoCallTest(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const token = await ZoomVideoCallHelper.genarateZoomSDKToken(
        req.body.topic,
        req.body.role,
        req.body.userId,
        req.body.sessionId,
        "1234"
      );
      return res.sendSuccess({ token: token });
    } catch (error) {
      return res.sendError(error);
    }
  }
  export async function getApiJWT(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const token = await ZoomVideoCallHelper.genarateZoomApiToken();
      return res.sendSuccess({ token: token });
    } catch (error) {
      return res.sendError(error);
    }
  }
  // ////
  export function initializeZoomVideoCallValidation(): ValidationChain[] {
    return [
      check("recieverId")
        .notEmpty()
        .withMessage("recieverId is required")
        .isMongoId()
        .withMessage("Invalid recieverId."),
      // check("appointmentId")
      //   .optional()
      //   .isMongoId()
      //   .withMessage("Invalid appointmentId."),
      check("callDuration")
        .notEmpty()
        .withMessage("callDuration is required")
        .isInt({ min: 0, max: 60 })
        .withMessage("Your meeting duration is not within 0 and 60 minutes."),
      check("isTranscribeAllowed")
        .notEmpty()
        .withMessage("isTranscribeAllowed is required/..")
        .isBoolean()
        .withMessage("isTranscribeAllowed is not a boolean type."),
      check("isAppointmentBased")
        .notEmpty()
        .withMessage("isAppointmentBased is required")
        .isBoolean()
        .withMessage("isAppointmentBased is not a boolean type."),
    ];
  }

  export async function initializeZoomVideoCall(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      let appointmentId;
      let appointmentData;
      let clientId;
      let therapistId;
      let callDuration = parseInt(req.body.callDuration);
      const transcribeAllowedForLoggedUser =
        req.body.transcribeAllowedForLoggedUser;
      const isAudioCall = req.body.isAudioCall;
      if (req.user.role == UserRole.CLIENT) {
        clientId = req.user._id;
        therapistId = req.body.recieverId;
      } else {
        therapistId = req.user._id;
        clientId = req.body.recieverId;
      }

      const friendRequest = await FriendRequest.findOne({
        clientId: clientId,
        therapistId: therapistId,
        status: FriendRequestStatus.APPROVED,
      });

      if (friendRequest == null) {
        return res.sendError("You are not friend with other user.", 453);
      }

      const avatarDetailsOfRecieverWithMeetingData =
        await VideoCallDao.getAvatarDetailsForStartCallWithMeetingDetals(
          req.body.recieverId,
          req.user.role,
          req.user._id,
          callDuration
        );
      if (!avatarDetailsOfRecieverWithMeetingData) {
        return res.sendError("invalid recieverId.");
      }
      if (
        avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus !==
        PremiumStatus.ACTIVE
      ) {
        if (
          avatarDetailsOfRecieverWithMeetingData.testSubscriptionStatus !==
          "active"
        ) {
          if (
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ==
            null ||
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ===
            "" ||
            avatarDetailsOfRecieverWithMeetingData.clientSubStatus !==
            SubscriptionStatus.ACTIVE
          ) {
            return res.sendError("No Valid Subscription for client.", 456);
          }

          if (!avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained) {
            return res.sendError("Meeting time exceeded.", 457);
          }
        }
      }

      const avatarDetailsOfOwnUser =
        await VideoCallDao.getAvatarDetailsForStartCall(req.user._id);

      if (!avatarDetailsOfOwnUser) {
        return res.sendError("invalid user.");
      }

      if (req.body.isAppointmentBased) {
        if (req.body.appointmentId == null) {
          return res.sendError(
            "appointmentId id required for appointment based calls."
          );
        }
        appointmentId = req.body.appointmentId;

        appointmentData = await Appointment.findById(appointmentId);

        if (!appointmentData) {
          return res.sendError("Invalid appointmentId ");
        }

        if (
          appointmentData.meetingStatus &&
          appointmentData.meetingId &&
          appointmentData.meetingStatus == MeetingStatus.STARTED
        ) {
          return res.sendError("meeting already started");
        }
        if (
          appointmentData.meetingStatus &&
          appointmentData.meetingStatus != MeetingStatus.PENDING
        ) {
          return res.sendError("meeting already started");
        }

        if (
          appointmentData.therapistId.toString() != therapistId ||
          appointmentData.clientId.toString() != clientId
        ) {
          return res.sendError(
            "You don't have permission to access this appointment"
          );
        }

        if (appointmentData.typeOfMeeting !== "VIDEO") {
          return res.sendError("Not a type of video appointment.");
        }

        // if (appointmentData.approvedStatus !== ApprovalStatus.APPROVED) {
        //   return res.sendError("Appointment Not Approved yet.", 454);
        // }

        if (appointmentData.status === AppointmentStatus.COMPLETED) {
          return res.sendError("Already Completed.", 455);
        }
        if (appointmentData.status === AppointmentStatus.OVERDUE) {
          return res.sendError("Overdue Appointment.");
        }

        if (appointmentData.status === AppointmentStatus.REJECTED) {
          return res.sendError("Rejected Appointment.");
        }

        // if (appointmentData.status === AppointmentStatus.WAITING_FOR_APPROVAL) {
        //   return res.sendError("Appointment is not approved yet.");
        // }

        // if (appointmentData.status !== AppointmentStatus.PENDING) {
        //   return res.sendError("Invalid appointment status found.");
        // }

        if (
          appointmentData.meetingId &&
          appointmentData.meetingStatus == MeetingStatus.STARTED
        ) {
          // if (
          //   !appointmentData.meetingStatus ||
          //   appointmentData.meetingStatus != MeetingStatus.STARTED ||
          //   appointmentData.meetingId == ""
          // ) {
          //   return res.sendError("Invalid appointment status found.");
          // }

          const previousMeetingData = await Meeting.findOne({
            meetingId: appointmentData.meetingId,
          });
          if (!previousMeetingData) {
            return res.sendError("Invalid meeting found");
          }
          if (
            (previousMeetingData.callingStatus != CallingStatus.STARTED,
              !previousMeetingData.clientIdentifier ||
              !previousMeetingData.therapistIdentifier ||
              !previousMeetingData.password)
          ) {
            return res.sendError(
              "Meeting id found in appointment but not in DB"
            );
          }

          const previousMeetingId = previousMeetingData._id.toString();

          const sdkToken = await ZoomVideoCallHelper.genarateZoomSDKToken(
            previousMeetingId,
            1,
            req.user.role == UserRole.CLIENT
              ? previousMeetingData.clientIdentifier
              : previousMeetingData.therapistIdentifier,
            previousMeetingId,
            previousMeetingData.password
          );

          const dataForSend = {
            alreadyStarted: false,
            sdkToken: sdkToken,
            password: previousMeetingData.password,
            meetingId: previousMeetingData._id,
            sessionId: previousMeetingData.meetingId,
            clientIdentifier: previousMeetingData.clientIdentifier,
            therapistIdentifier: previousMeetingData.therapistIdentifier,
          };

          return res.sendSuccess(dataForSend, "call initialization details");
        }
      }

      let newClientIdentifier;
      let newTherapistIdentifier;
      let newPassword;

      newClientIdentifier = await Util.getRandomInt(10000, 100000);
      newTherapistIdentifier = await Util.getRandomInt(10000, 100000);

      while (newClientIdentifier == newTherapistIdentifier) {
        newTherapistIdentifier = await Util.getRandomInt(10000, 100000);
      }

      newPassword = await Util.getRandomInt(10000, 100000);

      let meetingDetails: DMeeting;

      if (req.body.isAppointmentBased) {
        meetingDetails = {
          clientId: clientId,
          therapistId: therapistId,
          transcribeAllowed: req.body.isTranscribeAllowed,
          // transcribeAllowed: false,
          transcribingInProcess: false,
          accepted: false,
          noOfVideose: 0,
          transcribeCreated: false,
          meetingDuration: callDuration,
          isAppointmentBased: true,
          appointmentId: appointmentId,
          audioFiles: [],
          recordingAllowed:
            avatarDetailsOfRecieverWithMeetingData.callRecordingAllowed &&
              avatarDetailsOfOwnUser.callRecordingAllowed
              ? true
              : false,
          // recordingAllowed: false,
          callingStatus: CallingStatus.INITIALIZED,
          createdBy: req.user._id,
          recordingSharedWithClient: false,
          clientIdentifier: newClientIdentifier.toString(),
          therapistIdentifier: newTherapistIdentifier.toString(),
          password: newPassword.toString(),
          isAudioCall,
        };

        if (req.user.role == UserRole.CLIENT) {
          meetingDetails.clientAllowedTranscribe =
            transcribeAllowedForLoggedUser;
        } else {
          meetingDetails.therapistAllowedTranscribe =
            transcribeAllowedForLoggedUser;
        }
      } else {
        meetingDetails = {
          clientId: clientId,
          therapistId: therapistId,
          transcribeAllowed: req.body.isTranscribeAllowed,
          // transcribeAllowed: false,
          transcribingInProcess: false,
          accepted: false,
          noOfVideose: 0,
          transcribeCreated: false,
          meetingDuration: callDuration,
          isAppointmentBased: false,
          audioFiles: [],
          recordingAllowed:
            avatarDetailsOfRecieverWithMeetingData.callRecordingAllowed &&
              avatarDetailsOfOwnUser.callRecordingAllowed
              ? true
              : false,
          // recordingAllowed: false,
          callingStatus: CallingStatus.INITIALIZED,
          createdBy: req.user._id,
          recordingSharedWithClient: false,
          clientIdentifier: newClientIdentifier.toString(),
          therapistIdentifier: newTherapistIdentifier.toString(),
          password: newPassword.toString(),
          isAudioCall,
          therapistAllowedTranscribe: transcribeAllowedForLoggedUser,
        };
      }
      if (req.body.regularMeetingDate) {
        meetingDetails.regularMeetingDate = req.body.regularMeetingDate;
      }
      if (req.body.regularMeetingStartTime) {
        meetingDetails.regularMeetingStartTime =
          req.body.regularMeetingStartTime;
      }

      const createdMeeting = await VideoCallDao.createMeeting(meetingDetails);

      if (!createdMeeting) {
        return res.sendError("Meeting Creation Failed.");
      }

      const createdMeetingId = createdMeeting._id.toString();

      const sdkToken = await ZoomVideoCallHelper.genarateZoomSDKToken(
        createdMeetingId,
        1,
        req.user.role == UserRole.CLIENT
          ? createdMeeting.clientIdentifier
          : createdMeeting.therapistIdentifier,
        createdMeetingId,
        createdMeeting.password
      );

      const dataForSend = {
        alreadyStarted: false,
        sdkToken: sdkToken,
        password: createdMeeting.password,
        meetingId: createdMeeting._id,
        clientIdentifier: createdMeeting.clientIdentifier,
        therapistIdentifier: createdMeeting.therapistIdentifier,
      };

      return res.sendSuccess(dataForSend, "call initialization details");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Call initialization Server Error Occured",
        error
      );
      return res.sendError("call initialization failed.");
    }
  }

  export function startCallValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isMongoId()
        .withMessage("Invalid appointmentId."),
      check("sessionId")
        .notEmpty()
        .withMessage("sessionId is required")
        .isString()
        .withMessage("Invalid sessionId."),
      check("recieverId")
        .notEmpty()
        .withMessage("recieverId is required")
        .isMongoId()
        .withMessage("Invalid recieverId."),
      // check("appointmentId")
      //   .optional()
      //   .isMongoId()
      //   .withMessage("Invalid appointmentId."),
      check("callDuration")
        .notEmpty()
        .withMessage("callDuration is required")
        .isInt({ min: 0, max: 60 })
        .withMessage("Your meeting duration is not within 0 and 60 minutes."),
      check("isTranscribeAllowed")
        .notEmpty()
        .withMessage("isTranscribeAllowed is required/..")
        .isBoolean()
        .withMessage("isTranscribeAllowed is not a boolean type."),
      check("isAppointmentBased")
        .notEmpty()
        .withMessage("isAppointmentBased is required")
        .isBoolean()
        .withMessage("isAppointmentBased is not a boolean type."),
    ];
  }

  export async function startZoomVideoCall(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      let appointmentId;
      let clientId;
      let therapistId;
      let appointmentData;
      let callDuration = parseInt(req.body.callDuration);
      // global
      // let hasValidMeetingId = false;
      let callIdFromVideoSDK;
      let preMeetingData;

      if (req.user.role == UserRole.CLIENT) {
        clientId = req.user._id;
        therapistId = req.body.recieverId;
      } else {
        therapistId = req.user._id;
        clientId = req.body.recieverId;
      }

      const alreadyCreatedMeetingId = req.body.meetingId;
      const sessionId = req.body.sessionId;

      const alreadyCreatedMeetingData = await Meeting.findById(
        alreadyCreatedMeetingId
      );

      if (!alreadyCreatedMeetingData) {
        return res.sendError("invalid meeting id");
      }
      if (alreadyCreatedMeetingData.meetingId) {
        return res.sendError("invalid meeting id");
      }

      if (
        alreadyCreatedMeetingData.callingStatus != CallingStatus.INITIALIZED
      ) {
        return res.sendError("invalid meeting status");
      }

      const friendRequest = await FriendRequest.findOne({
        clientId: clientId,
        therapistId: therapistId,
        status: FriendRequestStatus.APPROVED,
      });

      if (friendRequest == null) {
        return res.sendError("You are not friend with other user.", 453);
      }

      if (alreadyCreatedMeetingData.isAppointmentBased) {
        if (!alreadyCreatedMeetingData.appointmentId) {
          return res.sendError("appointmentId id not found.");
        }
        appointmentId = alreadyCreatedMeetingData.appointmentId;

        appointmentData = await Appointment.findById(appointmentId);

        if (!appointmentData) {
          return res.sendError("Invalid appointmentId ");
        }

        if (
          appointmentData.meetingStatus &&
          appointmentData.meetingId &&
          appointmentData.meetingStatus == MeetingStatus.STARTED
        ) {
          return res.sendError("meeting already started");
        }
        if (
          appointmentData.meetingStatus &&
          appointmentData.meetingStatus != MeetingStatus.PENDING
        ) {
          return res.sendError("meeting already started");
        }

        if (appointmentData.typeOfMeeting !== "VIDEO") {
          return res.sendError("Not a type of video appointment.");
        }

        // if (appointmentData.approvedStatus !== ApprovalStatus.APPROVED) {
        //   return res.sendError("Appointment Not Approved yet.", 454);
        // }

        if (appointmentData.status === AppointmentStatus.COMPLETED) {
          return res.sendError("Already Completed.", 455);
        }
        if (appointmentData.status === AppointmentStatus.OVERDUE) {
          return res.sendError("Overdue Appointment.");
        }

        if (appointmentData.status === AppointmentStatus.REJECTED) {
          return res.sendError("Rejected Appointment.");
        }

        // if (appointmentData.status === AppointmentStatus.WAITING_FOR_APPROVAL) {
        //   return res.sendError("Appointment is not approved yet.");
        // }

        // if (appointmentData.status !== AppointmentStatus.PENDING) {
        //   return res.sendError("Invalid appointment status found.");
        // }
      }

      const avatarDetailsOfRecieverWithMeetingData =
        await VideoCallDao.getAvatarDetailsForStartCallWithMeetingDetals(
          req.body.recieverId,
          req.user.role,
          req.user._id,
          callDuration
        );
      if (!avatarDetailsOfRecieverWithMeetingData) {
        return res.sendError("invalid recieverId.");
      }
      if (
        avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus !==
        PremiumStatus.ACTIVE
      ) {
        if (
          avatarDetailsOfRecieverWithMeetingData.testSubscriptionStatus !==
          "active"
        ) {
          if (
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ==
            null ||
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ===
            "" ||
            avatarDetailsOfRecieverWithMeetingData.clientSubStatus !==
            SubscriptionStatus.ACTIVE
          ) {
            return res.sendError("No Valid Subscription for client.", 456);
          }

          if (!avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained) {
            return res.sendError("Meeting time exceeded.", 457);
          }
        }
      }

      const avatarDetailsOfOwnUser =
        await VideoCallDao.getAvatarDetailsForStartCall(req.user._id);

      if (!avatarDetailsOfOwnUser) {
        return res.sendError("invalid user.");
      }

      const updatedMeetingData = await Meeting.findByIdAndUpdate(
        alreadyCreatedMeetingId,
        { meetingId: sessionId, callingStatus: CallingStatus.STARTED }
      );
      if (!updatedMeetingData) {
        return res.sendError("meeting details update failed.");
      }
      if (req.body.isAppointmentBased) {
        await Appointment.findByIdAndUpdate(appointmentId, {
          meetingStatus: MeetingStatus.STARTED,
          meetingId: sessionId,
          meetingStartedBy: req.user._id,
        });
      }

      if (req.body.isAppointmentBased) {
        await VideoCallDao.updateAppointmentCallingTries(
          appointmentId,
          req.user.role
        );
      }

      const recieversLatestDetails = await User.findById(req.body.recieverId);
      const recieverSocketId = recieversLatestDetails.socketId;
      const finalMeetingData = {
        recordingAllowed: updatedMeetingData.recordingAllowed,
        callIdFromVideoSDK: callIdFromVideoSDK,
        clientSubscriptionId:
          avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId,
        clientSubStatus: avatarDetailsOfRecieverWithMeetingData.clientSubStatus,
        clientPremiumStatus:
          avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus,
        isMeetingTimeRemained:
          avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained,
        remainingMeetingTime:
          avatarDetailsOfRecieverWithMeetingData.remainingMeetingTime,
        isAudioCall: updatedMeetingData?.isAudioCall,
      };

      const finalRecieverData = {
        useDefaultAvatar:
          avatarDetailsOfRecieverWithMeetingData.useDefaultAvatar,
        avatarId: avatarDetailsOfRecieverWithMeetingData.avatarId,
        avatarBackgroundId:
          avatarDetailsOfRecieverWithMeetingData.avatarBackgroundId,
        incognito: avatarDetailsOfRecieverWithMeetingData.incognito,
        socketId: recieverSocketId,
        callerName: avatarDetailsOfRecieverWithMeetingData.callerName,
        callRecordingAllowed:
          avatarDetailsOfRecieverWithMeetingData.callRecordingAllowed,
      };

      const finalOwnUserData = {
        useDefaultAvatar: avatarDetailsOfOwnUser.useDefaultAvatar,
        avatarId: avatarDetailsOfOwnUser.avatarId,
        avatarBackgroundId: avatarDetailsOfOwnUser.avatarBackgroundId,
      };

      const finalData = {
        meetingData: finalMeetingData,
        recieverData: finalRecieverData,
        ownData: finalOwnUserData,
      };
      // send email notifications and sms

      const userForSendEmails = await User.findById(req.body.recieverId);
      const ownDetails = await User.findById(req.user._id);

      if (userForSendEmails && ownDetails) {
        await EmailService.sendEventMeetingLinkEmail(
          userForSendEmails,
          "New Meeting Started",
          "New Meeting Started by",
          `Please login to ${process.env.APP_URL}/signin to join meeting.`,
          // `${process.env.APP_URL}/lavni_session/${alreadyCreatedMeetingId}`,
          ownDetails.firstname + " " + ownDetails.lastname
        );

        if (userForSendEmails.primaryPhone) {
          await SMSService.sendEventSMS(
            `New Meeting Started by ${ownDetails.firstname} ${ownDetails.lastname}
             \n\nPlease login to ${process.env.APP_URL}/signin to join meeting.`,
            // Join Meeting: ${process.env.APP_URL}/lavni_session/${alreadyCreatedMeetingId}`,
            userForSendEmails.primaryPhone
          );
        }
      }

      // Send email notifications and sms end

      return res.sendSuccess(finalData, "Call Details");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Start Zoom Video Call Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function joinZoomMeetingValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isString()
        .withMessage("Invalid meetingId."),
      // check("recieverId")
      //   .notEmpty()
      //   .withMessage("recieverId is required")
      //   .isMongoId()
      //   .withMessage("Invalid recieverId."),
      check("isAppointmentBased")
        .notEmpty()
        .withMessage("isAppointmentBased is required")
        .isBoolean()
        .withMessage("isAppointmentBased is not a boolean type."),
      // check("appointmentId")
      //   .optional()
      //   .isMongoId()
      //   .withMessage("Invalid appointmentId."),
      // check("callDuration"),
      //   .notEmpty()
      //   .withMessage("callDuration is required")
      //   .isInt({ min: 0, max: 60 })
      //   .withMessage("Your meeting duration is not within 0 and 60 minutes."),
    ];
  }

  export async function joinZoomMeeting(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }
      let remainingCallDurationJoinedCall;
      let appointmentId;
      let clientId: any;
      let therapistId: any;
      let appointmentData;
      let recieverId;

      let meetingId;

      // global
      let hasValidMeetingId = false;
      let preMeetingData;
      const transcribeAllowedForUser = req.body?.transcribeAllowedForUser;
      const userId = req.user?._id;
      const userRole = req.user?.role;
      AppLogger.info(`Joining Ep | User attempting the join to the session. userId: ${userId}, userRole: ${userRole}`);

      // check meeting and update spent duration
      if (req.body.isAppointmentBased) {
        if (req.body.appointmentId == null) {
          AppLogger.error(`Joining Ep | AppointmentId id required for appointment based calls. userId: ${userId}`);
          return res.sendError(
            "appointmentId id required for appointment based calls."
          );
        }
        appointmentId = req.body.appointmentId;
        appointmentData = await Appointment.findById(appointmentId);
        if (!appointmentData || !appointmentData.meetingId) {
          AppLogger.error(`Joining Ep | Appointment data not found. appointmentId: ${appointmentId}, userId: ${userId}`);
          return res.sendError("Invalid appointmentId ");
        }
        meetingId = appointmentData.meetingId;
      } else {
        meetingId = req.body.meetingId;
      }
      if (req.body.isAppointmentBased) {
        if (req.user.role == UserRole.CLIENT) {
          preMeetingData = await Meeting.findOneAndUpdate(
            { meetingId: meetingId }, 
            { clientAllowedTranscribe: transcribeAllowedForUser }
          );
        } else {
          preMeetingData = await Meeting.findOneAndUpdate(
            { meetingId: meetingId },
            { therapistAllowedTranscribe: transcribeAllowedForUser }
          );
        }
      } else {
        if (req.user.role == UserRole.CLIENT) {
          console.log("meetingId ", meetingId)
          preMeetingData = await Meeting.findByIdAndUpdate(
            meetingId,
            {
              clientAllowedTranscribe: transcribeAllowedForUser,
            },
            { new: true }
          );
        } else {
          preMeetingData = await Meeting.findByIdAndUpdate(
            meetingId,
            {
              therapistAllowedTranscribe: transcribeAllowedForUser,
            },
            { new: true }
          );
        }
      }

      if (preMeetingData) {
        remainingCallDurationJoinedCall = preMeetingData.meetingDuration;

        clientId = preMeetingData.clientId;
        therapistId = preMeetingData.therapistId;

        if (req.user.role == UserRole.CLIENT) {
          recieverId = therapistId;
        } else {
          recieverId = clientId;
        }

        if (
          preMeetingData.callingStatus != CallingStatus.ONGOING &&
          preMeetingData.callingStatus != CallingStatus.STARTED
        ) {
          AppLogger.error(`Joining Ep | Invalid meeting calling status. meetingId: ${preMeetingData._id}, userId: ${userId}`);
          return res.sendError("invalid meeting calling status");
        }
        if (
          !preMeetingData.clientIdentifier ||
          !preMeetingData.therapistIdentifier ||
          !preMeetingData.password
        ) {
          AppLogger.error(`Joining Ep | Meeting id found in appointment but not in DB. meetingId: ${preMeetingData._id}, userId: ${userId}`);
          return res.sendError("Meeting id found in appointment but not in DB");
        }
        remainingCallDurationJoinedCall = preMeetingData.meetingDuration;
        if (
          preMeetingData.callingStatus === CallingStatus.ONGOING &&
          preMeetingData.bothJoinedAt &&
          preMeetingData.meetingDuration
        ) {
          let remainingTime = preMeetingData.meetingDuration;
          const totalMeetingDuration = preMeetingData.meetingDuration;
          const bothJoinedTime = preMeetingData.bothJoinedAt;
          const currentTime = new Date();
          const timeDifference = moment.duration(
            moment(currentTime).diff(moment(bothJoinedTime))
          );
          const timeDifferenceAsMinutes = timeDifference.asMinutes();
          const finalTimeDifference = Math.round(timeDifferenceAsMinutes);
          if (finalTimeDifference >= 0) {
            remainingTime = totalMeetingDuration - finalTimeDifference;
            if (remainingTime >= 0) {
              remainingCallDurationJoinedCall = remainingTime;
              const updatedMeeting = await Meeting.findOneAndUpdate(
                { meetingId: meetingId },
                { spentDuration: finalTimeDifference },
                { new: true }
              );
            }
            if (remainingTime <= 0) {
              if (preMeetingData.isAppointmentBased) {
                await Meeting.findByIdAndUpdate(preMeetingData._id, {
                  callingStatus:
                    preMeetingData.callingStatus === CallingStatus.ONGOING
                      ? CallingStatus.COMPLETED
                      : CallingStatus.CANCELLED,
                });
                await Appointment.findByIdAndUpdate(
                  preMeetingData.appointmentId,
                  {
                    meetingStatus:
                      preMeetingData.callingStatus === CallingStatus.ONGOING
                        ? MeetingStatus.COMPLETED
                        : MeetingStatus.PENDING,
                  }
                );
              } else {
                await Meeting.findByIdAndUpdate(preMeetingData._id, {
                  callingStatus:
                    preMeetingData.callingStatus === CallingStatus.ONGOING
                      ? CallingStatus.COMPLETED
                      : CallingStatus.CANCELLED,
                });
              }
              
              AppLogger.error(`Joining Ep | Meeting time exceeded for this call. meetingId: ${preMeetingData._id}, userId: ${userId}`);
              return res.sendError("Meeting time exceeded for this call");
            }
          } else {
            AppLogger.error(`Joining Ep | Invalid meeting time. meetingId: ${preMeetingData._id}, userId: ${userId}`);
            res.sendError("invalid meeting time");
          }

          // update spent time in current meeting
          // const optionsForGetTimeSpend = {
          //   method: "GET",
          //   headers: {
          //     Authorization: authTokenForCreateMeeting,
          //     "Content-Type": "application/json",
          //   },
          // };
          // const urlForGetTimeSpend = `${process.env.VIDEO_SDK_BASE_URL}/v2/sessions/?roomId=${meetingId}`;
          // const responseOfGetTimeSpend = await fetch(
          //   urlForGetTimeSpend,
          //   optionsForGetTimeSpend
          // );
          // if (true) {
          // previouslySpentDuration = await calculateSpentDuration(
          //   responseOfGetTimeSpend,
          //   therapistId,
          //   clientId,
          //   previouslySpentDuration
          // );

          //   if (previouslySpentDuration >= 0) {
          //     const updatedMeeting = await Meeting.findOneAndUpdate(
          //       { meetingId: meetingId },
          //       { spentDuration: previouslySpentDuration },
          //       { new: true }
          //     );
          //   }

          //   if (remainingCallDurationJoinedCall >= previouslySpentDuration) {
          //     remainingCallDurationJoinedCall =
          //       remainingCallDurationJoinedCall - previouslySpentDuration;
          //   }
          //   if (remainingCallDurationJoinedCall <= 0) {
          //     if (preMeetingData) {
          //       if (preMeetingData.isAppointmentBased) {
          //         await Meeting.findByIdAndUpdate(preMeetingData._id, {
          //           callingStatus:
          //             preMeetingData.callingStatus === CallingStatus.ONGOING
          //               ? CallingStatus.COMPLETED
          //               : CallingStatus.CANCELLED,
          //         });
          //         await Appointment.findByIdAndUpdate(
          //           preMeetingData.appointmentId,
          //           {
          //             meetingStatus:
          //               preMeetingData.callingStatus === CallingStatus.ONGOING
          //                 ? MeetingStatus.COMPLETED
          //                 : MeetingStatus.PENDING,
          //           }
          //         );
          //       } else {
          //         await Meeting.findByIdAndUpdate(preMeetingData._id, {
          //           callingStatus:
          //             preMeetingData.callingStatus === CallingStatus.ONGOING
          //               ? CallingStatus.COMPLETED
          //               : CallingStatus.CANCELLED,
          //         });
          //       }
          //     } else {
          //       return res.sendError("Invalid Meeting Id.");
          //     }
          //     return res.sendError("Meeting time exceeded for this call");
          //   }
          // } else {
          //   return res.sendError("Meeting data fetching failed");
          // }
        }
      } else {
        AppLogger.error(`Joining Ep | Invalid Meeting Id. meetingId: ${preMeetingData._id}, userId: ${userId}`);
        return res.sendError("Invalid Meeting Id");
      }

      const friendRequest = await FriendRequest.findOne({
        clientId: clientId,
        therapistId: therapistId,
        status: FriendRequestStatus.APPROVED,
      });
      if (friendRequest == null) {
        AppLogger.error(`Joining Ep | You are not friend with other user. clientId: ${clientId}, therapistId: ${therapistId}`);
        return res.sendError("You are not friend with other user.", 453);
      }

      if (preMeetingData.isAppointmentBased) {
        if (preMeetingData.appointmentId == null) {
          AppLogger.error(`Joining Ep | AppointmentId id required for appointment based calls. meetingId: ${preMeetingData._id}, userId: ${userId}`);
          return res.sendError(
            "appointmentId id required for appointment based calls."
          );
        }
        appointmentId = preMeetingData.appointmentId;

        appointmentData = await Appointment.findById(appointmentId);
        if (!appointmentData) {
          AppLogger.error(`Joining Ep | Appointment data not found. appointmentId: ${appointmentId}, userId: ${userId}`);
          return res.sendError("Invalid appointmentId ");
        }
        if (appointmentData.typeOfMeeting !== "VIDEO") {
          AppLogger.error(`Joining Ep | Not a type of video appointment. appointmentId: ${appointmentId}, userId: ${userId}`);
          return res.sendError("Not a type of video appointment.");
        }
        // if (appointmentData.approvedStatus !== ApprovalStatus.APPROVED) {
        //   return res.sendError("Appointment Not Approved yet.", 454);
        // }
        if (appointmentData.status === AppointmentStatus.COMPLETED) {
          AppLogger.error(`Joining Ep | Already completed appointment. appointmentId: ${appointmentId}, userId: ${userId}`);
          return res.sendError("Already Completed.", 455);
        }
        if (appointmentData.status === AppointmentStatus.OVERDUE) {
          AppLogger.error(`Joining Ep | Overdue appointment. appointmentId: ${appointmentId}, userId: ${userId}`);
          return res.sendError("Overdue Appointment.");
        }
        if (appointmentData.status === AppointmentStatus.REJECTED) {
          AppLogger.error(`Joining Ep | Rejected appointment. appointmentId: ${appointmentId}, userId: ${userId}`);
          return res.sendError("Rejected Appointment.");
        }
        // if (appointmentData.status === AppointmentStatus.WAITING_FOR_APPROVAL) {
        //   return res.sendError("Appointment is not approved yet.");
        // }

        // if (appointmentData.status !== AppointmentStatus.PENDING) {
        //   return res.sendError("Invalid appointment status found.");
        // }

        if (
          appointmentData.meetingStatus &&
          (appointmentData.meetingStatus === MeetingStatus.STARTED ||
            appointmentData.meetingStatus === MeetingStatus.ONGOING) &&
          appointmentData.meetingId
        ) {
          if (preMeetingData) {
            hasValidMeetingId = true;
          } else {
            AppLogger.error(`Joining Ep | Invalid Meeting Id1. appointmentId: ${appointmentId}, userId: ${userId}`);
            return res.sendError("Invalid Meeting Id");
          }
        } else {
          AppLogger.error(`Joining Ep | Meeting Cancelled1. appointmentId: ${appointmentId}, userId: ${userId}`);
          return res.sendError("Meeting Cancelled", 332);
        }
      } else {
        if (preMeetingData) {
          hasValidMeetingId = true;
          if (
            preMeetingData.callingStatus === CallingStatus.STARTED ||
            preMeetingData.callingStatus === CallingStatus.ONGOING
          ) {
          } else {
            AppLogger.error(`Joining Ep | Meeting Cancelled2. meetingId: ${preMeetingData._id}, userId: ${userId}`);
            return res.sendError("Meeting Cancelled", 333);
          }
        } else {
          AppLogger.error(`Joining Ep | Invalid Meeting Id2. meetingId: ${preMeetingData._id}, userId: ${userId}`);
          return res.sendError("Invalid Meeting Id.");
        }
      }

      const avatarDetailsOfRecieverWithMeetingData =
        await VideoCallDao.getAvatarDetailsForStartCallWithMeetingDetals(
          recieverId,
          req.user.role,
          req.user._id,
          remainingCallDurationJoinedCall
        );
      if (!avatarDetailsOfRecieverWithMeetingData) {
        AppLogger.error(`Joining Ep | Invalid recieverId. meetingId: ${preMeetingData._id}, userId: ${userId}`);
        return res.sendError("invalid recieverId.");
      }
      if (
        avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus !==
        PremiumStatus.ACTIVE
      ) {
        if (
          avatarDetailsOfRecieverWithMeetingData.testSubscriptionStatus !==
          "active"
        ) {
          if (
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ==
            null ||
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ===
            "" ||
            avatarDetailsOfRecieverWithMeetingData.clientSubStatus !==
            SubscriptionStatus.ACTIVE
          ) {
            // return res.sendError("No Valid Subscription for client.", 456);
          }

          if (!avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained) {
            AppLogger.error(`Joining Ep | Meeting time exceeded. meetingId: ${preMeetingData._id}, userId: ${userId}`);
            return res.sendError("Meeting time exceeded.", 457);
          }
        }
      }

      const avatarDetailsOfOwnUser =
        await VideoCallDao.getAvatarDetailsForStartCall(req.user._id);

      if (!avatarDetailsOfOwnUser) {
        AppLogger.error(`Joining Ep | Invalid user. meetingId: ${preMeetingData._id}, userId: ${userId}`);
        return res.sendError("invalid user.");
      }

      const previousMeetingId = preMeetingData._id.toString();

      // const sdkToken = await ZoomVideoCallHelper.genarateZoomSDKToken(
      //   previousMeetingId,
      //   1,
      //   req.user.role == UserRole.CLIENT
      //     ? preMeetingData.clientIdentifier
      //     : preMeetingData.therapistIdentifier,
      //   previousMeetingId,
      //   preMeetingData.password
      // );

      const recieversLatestDetails = await User.findById(recieverId);
      const recieverSocketId = recieversLatestDetails.socketId;
      const finalMeetingData = {
        remainingMeetingTimeForCurrentMeeting: remainingCallDurationJoinedCall,
        recordingAllowed: hasValidMeetingId
          ? preMeetingData.recordingAllowed
          : preMeetingData.recordingAllowed,
        // sdkToken: sdkToken,
        callIdFromVideoSDK: meetingId,
        clientSubscriptionId:
          avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId,
        clientSubStatus: avatarDetailsOfRecieverWithMeetingData.clientSubStatus,
        clientPremiumStatus:
          avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus,
        isMeetingTimeRemained:
          avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained,
        remainingMeetingTime:
          avatarDetailsOfRecieverWithMeetingData.remainingMeetingTime,
        password: preMeetingData.password,
        meetingId: preMeetingData._id,
        sessionId: preMeetingData.meetingId,
        clientIdentifier: preMeetingData.clientIdentifier,
        therapistIdentifier: preMeetingData.therapistIdentifier,
        isAudioCall: preMeetingData?.isAudioCall,
      };

      const finalRecieverData = {
        userId: recieverId,
        useDefaultAvatar:
          avatarDetailsOfRecieverWithMeetingData.useDefaultAvatar,
        avatarId: avatarDetailsOfRecieverWithMeetingData.avatarId,
        avatarBackgroundId:
          avatarDetailsOfRecieverWithMeetingData.avatarBackgroundId,
        incognito: avatarDetailsOfRecieverWithMeetingData.incognito,
        socketId: recieverSocketId,
        callerName: avatarDetailsOfRecieverWithMeetingData.callerName,
        callRecordingAllowed:
          avatarDetailsOfRecieverWithMeetingData.callRecordingAllowed,
      };

      const finalOwnUserData = {
        useDefaultAvatar: avatarDetailsOfOwnUser.useDefaultAvatar,
        avatarId: avatarDetailsOfOwnUser.avatarId,
        avatarBackgroundId: avatarDetailsOfOwnUser.avatarBackgroundId,
      };

      const finalData = {
        meetingData: finalMeetingData,
        recieverData: finalRecieverData,
        ownData: finalOwnUserData,
      };
      AppLogger.info(`Joining Ep | Successfully joined to session. meetingId: ${preMeetingData._id}, userId: ${userId}`)
      return res.sendSuccess(finalData, "Call Details");
    } catch (error) {
      AppLogger.info(`Joining Ep | Join Zoom Meeting Server Error Occured. userId: ${req.user._id}`)
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Join Zoom Meeting Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function cancelZoomCallValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isMongoId()
        .withMessage("Invalid meetingId."),
      check("isAppointmentBased")
        .notEmpty()
        .withMessage("isAppointmentBased is required")
        .isBoolean()
        .withMessage("isAppointmentBased is not a boolean type."),
      // check("appointmentId")
      //   .if(check("isAppointmentBased").toBoolean().equals())
      //   .optional()
      //   .isMongoId()
      //   .withMessage("Invalid appointmentId."),
    ];
  }

  export async function cancelZoomCall(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      let meetingId;
      let preMeetingData;
      let appointmentData;
      let appointmentId;
      if (req.body.isAppointmentBased) {
        if (req.body.appointmentId == null) {
          return res.sendError(
            "appointmentId id required for appointment based calls."
          );
        }
        appointmentId = req.body.appointmentId;
        appointmentData = await Appointment.findById(appointmentId);
        if (!appointmentData || !appointmentData.meetingId) {
          return res.sendError("Invalid appointmentId ");
        }
        meetingId = appointmentData.meetingId;
      } else {
        meetingId = req.body.meetingId;
      }
      if (req.body.isAppointmentBased) {
        preMeetingData = await Meeting.findOne({
          meetingId: meetingId,
        });
      } else {
        preMeetingData = await Meeting.findOne({
          _id: meetingId,
        });
      }
      if (preMeetingData) {
        let participantCount = preMeetingData.participantCount;
        if(!participantCount){
          participantCount = 1
        }
        let isBothUserJoined = false;
        if(participantCount > 1) {
          isBothUserJoined = true;
        } else {
          isBothUserJoined = false;
        }
        if (preMeetingData.isAppointmentBased) {
          if (
            preMeetingData.callingStatus === CallingStatus.ONGOING ||
            preMeetingData.callingStatus === CallingStatus.STARTED
          ) {
            await Meeting.findByIdAndUpdate(preMeetingData._id, {
              callingStatus:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? CallingStatus.COMPLETED
                  : CallingStatus.CANCELLED,
            });
            await Appointment.findByIdAndUpdate(preMeetingData.appointmentId, {
              meetingStatus:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? MeetingStatus.COMPLETED
                  : MeetingStatus.PENDING,
              status:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? AppointmentStatus.COMPLETED
                  : AppointmentStatus.PENDING,
            });
          }
          const result = await ZoomVideoCallEP.createDiagnosisNote(
            preMeetingData._id
          );
        } else {
          if (
            preMeetingData.callingStatus === CallingStatus.ONGOING ||
            preMeetingData.callingStatus === CallingStatus.STARTED
          ) {
            await Meeting.findByIdAndUpdate(preMeetingData._id, {
              callingStatus:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? CallingStatus.COMPLETED
                  : CallingStatus.CANCELLED,
            });
          }
          const result = await ZoomVideoCallEP.createDiagnosisNote(meetingId);
        }
        // update spend time
        // if (preMeetingData.callingStatus === CallingStatus.ONGOING) {
        //   const API_KEY = process.env.VIDEO_SDK_API_KEY;
        //   const SECRET = process.env.VIDEO_SDK_SECRET;
        //   const authTokenForCreateMeeting = jwt.sign(
        //     {
        //       apikey: API_KEY,
        //       permissions: ["allow_join"],
        //     },
        //     SECRET,
        //     {
        //       algorithm: "HS256",
        //       expiresIn: "24h",
        //     }
        //   );
        //   if (!authTokenForCreateMeeting) {
        //     return res.sendError("Auth token creation failed for call.");
        //   }
        //   let previouslySpentDuration = 0;
        //   const optionsForGetTimeSpend = {
        //     method: "GET",
        //     headers: {
        //       Authorization: authTokenForCreateMeeting,
        //       "Content-Type": "application/json",
        //     },
        //   };
        //   const urlForGetTimeSpend = `${process.env.VIDEO_SDK_BASE_URL}/v2/sessions/?roomId=${meetingId}`;
        //   const responseOfGetTimeSpend = await fetch(
        //     urlForGetTimeSpend,
        //     optionsForGetTimeSpend
        //   );
        //   if (responseOfGetTimeSpend.ok) {
        //     previouslySpentDuration = await calculateSpentDuration(
        //       responseOfGetTimeSpend,
        //       preMeetingData.therapistId,
        //       preMeetingData.clientId,
        //       previouslySpentDuration
        //     );

        //     if (previouslySpentDuration >= 0) {
        //       const updatedMeeting = await Meeting.findOneAndUpdate(
        //         { meetingId: meetingId },
        //         { spentDuration: previouslySpentDuration },
        //         { new: true }
        //       );
        //     }
        //   }
        // }
      } else {
        return res.sendError("Invalid Meeting Id.");
      }

      return res.sendSuccess("Call Cancelled");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Call Cancelled Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export async function createDiagnosisNote(meetingId: Types.ObjectId): Promise<any> {
    const meeting = await VideoCallDao.getMeetingByMeetingIdOnlyTest(meetingId);
    if (
      !meeting ||
      !meeting.clientIdentifier ||
      !meeting.therapistIdentifier ||
      !meeting.bothJoinedAt
    ) {
      AppLogger.error("Wrong id");
    }
    try {
      console.log("createDiagnosisNotecreateDiagnosisNote ", meetingId);
      
      // const updatedMeeting = await Meeting.findOneAndUpdate(
      //   { meetingId: meetingId },
      //   { accepted: true, callingStatus: CallingStatus.COMPLETED },
      //   { new: true }
      // );

      // if (updatedMeeting && updatedMeeting.isAppointmentBased) {
      //   await Appointment.findByIdAndUpdate(updatedMeeting.appointmentId, {
      //     status: AppointmentStatus.COMPLETED,
      //     meetingStatus: MeetingStatus.COMPLETED,
      //   });
      // }

      await VideoCallDao.updateMeetingTranscribingInProcessStatus(
        meetingId,
        true
      );

      let preTreatmentHistory = await TreatmentHistory.findOne({
        meetingId: meeting._id,
      });
      if (preTreatmentHistory == null) {
        const diagnosisNoteData: DDiagnosisNote = {
          clientId: meeting.clientId,
          meetingId: meeting._id,
          therapistId: meeting.therapistId,
          updated: false,
          updatedByTherapist: false,
          isVonageTranscribe: true,
        };
        const diagnosisNote = await VideoCallDao.createDiagnosisNote(
          diagnosisNoteData
        );
        const treatmentHistoryDetails: DTreatmentHistory = {
          clientId: meeting.clientId,
          therapistId: meeting.therapistId,
          note: "Meeting Transcribe",
          isMeetingTranscribe: true,
          meetingStartedTime: meeting.createdAt,
          meetingId: meeting._id,
          diagnosisNoteId: diagnosisNote._id,
        };
        let addedTreatmentHistory = await TherapistDao.addTreatmentHistory(
          treatmentHistoryDetails
        );
      }
    } catch (error) {
      AppLogger.error("Something went wrong" + error);
    }
  }

  export async function createDiagnosisNoteForGroup(meetingId: Types.ObjectId, clientId: Types.ObjectId): Promise<any> {
    try {
      const meeting = await Meeting.findById(meetingId);
      if (!meeting) {
        throw new Error("Meeting not found");
      }

      // Update meeting transcribing status
      await VideoCallDao.updateMeetingTranscribingInProcessStatus(
        meetingId,
        true
      );

      // Check for existing treatment history
      let preTreatmentHistory = await TreatmentHistory.findOne({
        meetingId: meeting._id,
      });
      
      if (preTreatmentHistory != null) {
        return preTreatmentHistory.diagnosisNoteId;
      }

      const diagnosisNoteData: DDiagnosisNote = {
        clientId: clientId,
        meetingId: meeting._id,
        therapistId: meeting.therapistId,
        updated: false,
        updatedByTherapist: false,
        isVonageTranscribe: true,
      };

      const diagnosisNote = await VideoCallDao.createDiagnosisNote(diagnosisNoteData);

      const treatmentHistoryDetails: DTreatmentHistory = {
        clientId: clientId,
        therapistId: meeting.therapistId,
        note: "Meeting Transcribe",
        isMeetingTranscribe: true,
        meetingStartedTime: meeting.createdAt,
        meetingId: meeting._id,
        diagnosisNoteId: diagnosisNote._id,
      };
      let addedTreatmentHistory = await TherapistDao.addTreatmentHistory(
        treatmentHistoryDetails
      );

      return diagnosisNote;
    } catch (error) {
      throw error;
    }
  }

  export function acceptZoomCallValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isMongoId()
        .withMessage("meetingId is not a Type of string"),
    ];
  }

  export async function acceptZoomCall(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      let meetingId = req.body.meetingId;
      // const date = new Date();
      const previousData = await Meeting.findOne({ _id: meetingId });
      if(!previousData) {
        return res.sendError("Invalid meeting data found!")
      }
      // if (req?.user?._id && previousData &&  previousData?.createdBy) {
      //     if ( req?.user?._id != previousData?.createdBy){
      //       const updatedMeeting = await Meeting.findOneAndUpdate(
      //         { _id: meetingId },
      //         {
      //           accepted: true,
      //           callingStatus: CallingStatus.ONGOING,
      //           bothJoinedAt: date,
      //           participantCount: 2
      //         },
      //         { new: true }
      //       );
      //     } 
      // } else {
      //   return res.sendError("Invalid meeting data found!")
      // }

      return res.sendSuccess("Meeting and appointment data updated");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Accept Zoom Call Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function checkRemainingMeetingTimeValidation(): ValidationChain[] {
    return [
      check("recieverId")
        .notEmpty()
        .withMessage("recieverId is required")
        .isMongoId()
        .withMessage("Invalid recieverId."),
      check("extendingDuration")
        .notEmpty()
        .withMessage("extendingDuration is required.")
        .isInt({ min: 0, max: 60 })
        .withMessage("Extending duration is not within 0 and 60 minutes."),
    ];
  }

  export async function checkRemainingMeetingTime(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      let isMeetingTimeRemained = false;
      let extendingDuration = parseInt(req.body.extendingDuration);

      const avatarDetailsOfRecieverWithMeetingData =
        await VideoCallDao.checkRemainingMeetingTime(
          req.body.recieverId,
          req.user.role,
          req.user._id,
          extendingDuration
        );
      if (!avatarDetailsOfRecieverWithMeetingData) {
        return res.sendError("invalid recieverId");
      }

      if (
        avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus ===
        PremiumStatus.ACTIVE ||
        avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus === "active"
      ) {
        isMeetingTimeRemained = true;
      } else {
        isMeetingTimeRemained =
          avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained;
      }

      const finalData = {
        isMeetingTimeRemained: isMeetingTimeRemained,
        remainingMeetingTime:
          avatarDetailsOfRecieverWithMeetingData.remainingMeetingTime,
      };

      return res.sendSuccess(finalData, "Remaining meeting time");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Check Remaining Meeting Time Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function updateMeetingTimeWhenExtendValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isMongoId()
        .withMessage("meetingId is not a Type of string"),
      check("extendingDuration")
        .notEmpty()
        .withMessage("extendingDuration is required.")
        .isInt({ min: 0, max: 60 })
        .withMessage("Extending duration is not within 0 and 60 minutes."),
    ];
  }

  export async function updateMeetingTimeWhenExtend(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }
      let meetingId = req.body.meetingId;
      let extendingDuration = parseInt(req.body.extendingDuration);
      let preMeetingDetails = await Meeting.findOne({
        therapistId: req.user._id,
        _id: meetingId,
        accepted: true,
      });
      if (!preMeetingDetails) {
        return res.sendError("Invalid meeting id");
      }
      let newMeetingTime =
        extendingDuration + preMeetingDetails.meetingDuration;
      let updatedMeetingDetails = await Meeting.findByIdAndUpdate(
        preMeetingDetails._id,
        {
          meetingDuration: newMeetingTime,
        }
      );
      return res.sendSuccess("Meeting time updated");
    } catch (error) {
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function finaliseZoomMeetingParamValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isString()
        .withMessage("meetingId is not a Type of string"),
    ];
  }

  export async function finaliseZoomMeetingNew(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      // param validation
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }
      const meetingId = req.body.meetingId;
      const meeting = await VideoCallDao.getMeetingByMeetingId(
        meetingId,
        req.user._id
      );
      if (
        !meeting ||
        !meeting.clientIdentifier ||
        !meeting.therapistIdentifier ||
        !meeting.bothJoinedAt
      ) {
        return res.sendError("Wrong id");
      }
      try {
        // update meeting and appointment status
        const updatedMeeting = await Meeting.findOneAndUpdate(
          { meetingId: meetingId },
          { accepted: true, callingStatus: CallingStatus.COMPLETED },
          { new: true }
        );

        if (updatedMeeting && updatedMeeting.isAppointmentBased) {
          await Appointment.findByIdAndUpdate(updatedMeeting.appointmentId, {
            status: AppointmentStatus.COMPLETED,
            meetingStatus: MeetingStatus.COMPLETED,
          });
        }
        // update meeting and appointment status end

        await VideoCallDao.updateMeetingTranscribingInProcessStatus(
          meetingId,
          true
        );
        // param validation end
        // get therapist
        const therapistData = await Therapist.findById(meeting.therapistId);
        if (therapistData == null) {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );
          return res.sendError("Therapist not exists");
        }
        // get therapist end
        // Create token
        const payRateOfTherapist = therapistData.payRate
          ? therapistData.payRate
          : Number(process.env.DEFAULT_PAY_RATE_OF_THERAPIST);

        const token = await ZoomVideoCallHelper.genarateZoomApiToken();
        if (!token) {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );
          return res.sendError("api token genaration error occured");
        }

        const encodedSessionId =
          ZoomVideoCallHelper.doubleEncodeString(meetingId);
        if (!encodedSessionId || encodedSessionId == "") {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );
          return res.sendError("session id encode error happend");
        }
        // get meeting time that spent by therapist
        const optionsForGetTimeSpend = {
          method: "GET",
          headers: {
            Authorization: "Bearer" + token,
            "Content-Type": "application/json",
          },
        };

        // let previouslySpentDuration = 0;
        let finalTime = 0;
        // update spent time in current meeting

        const urlForGetTimeSpend = `${process.env.ZOOM_API_BASE_URL}/sessions/${encodedSessionId}?type=past`;

        const responseOfGetTimeSpend = await fetch(
          urlForGetTimeSpend,
          optionsForGetTimeSpend
        );

        if (responseOfGetTimeSpend.ok) {
          const responseData = await responseOfGetTimeSpend.json();
          if (
            !responseData ||
            !responseData.end_time ||
            responseData.end_time == undefined ||
            responseData.end_time == null ||
            responseData.end_time == "" ||
            !responseData.id ||
            responseData.id.toString() != meetingId
          ) {
            await VideoCallDao.updateMeetingTranscribingInProcessStatus(
              meetingId,
              false
            );
            return res.sendError("Meeting data fetching faileded");
          }
          const bothStartedTime = new Date(meeting.bothJoinedAt);
          const endTimeOfMeeting = new Date(responseData.end_time);
          const timeDifferenceWithStartAndEnd = moment
            .duration(moment(endTimeOfMeeting).diff(moment(bothStartedTime)))
            .asMinutes();
          const finalTimeDifference = Math.round(timeDifferenceWithStartAndEnd);
          finalTime = finalTimeDifference;
        } else {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );
          return res.sendError("Meeting data fetching failed");
        }

        if (finalTime >= 0) {
          const differenceOfTimes = Math.abs(
            meeting.meetingDuration - finalTime
          );
          if (finalTime < 95) {
            await VideoCallDao.updateMeetingTime(meetingId, finalTime);
          } else {
            await Meeting.findOneAndUpdate(
              { meetingId: meetingId },
              {
                spentDuration: meeting.meetingDuration,
                isInvalidMeetingTime: true,
                invalidDuration: finalTime,
              },
              { new: true }
            );
            finalTime = meeting.meetingDuration;
          }
        }


        let peviousTransaction = await VideoCallDao.getTransactionBuyMeetingId(
          meeting._id,
          req.user._id
        );

        // create treatment history and create audio
        let preTreatmentHistory = await TreatmentHistory.findOne({
          meetingId: meeting._id,
        });
        if (preTreatmentHistory == null) {
          // create session diagnosis report

          const diagnosisNoteData: DDiagnosisNote = {
            clientId: meeting.clientId,
            meetingId: meeting._id,
            therapistId: meeting.therapistId,
            updated: false,
            updatedByTherapist: false,
            isVonageTranscribe: true,
          };

          const diagnosisNote = await VideoCallDao.createDiagnosisNote(
            diagnosisNoteData
          );

          // create session diagnosis report end

          const treatmentHistoryDetails: DTreatmentHistory = {
            clientId: meeting.clientId,
            therapistId: meeting.therapistId,
            note: "Meeting Transcribe",
            isMeetingTranscribe: true,
            meetingStartedTime: meeting.createdAt,
            meetingId: meeting._id,
            diagnosisNoteId: diagnosisNote._id,
          };

          let addedTreatmentHistory = await TherapistDao.addTreatmentHistory(
            treatmentHistoryDetails
          );
        }
        // create treatment history and create audio end

        // create audio files

        if (meeting.recordingAllowed && meeting.recordingAllowed === true) {
          let optionsForGetRecordings = {
            method: "GET",
            headers: {
              Authorization: "Bearer" + token,
              "Content-Type": "application/json",
            },
          };
          const urlForGetRecordings = `${process.env.ZOOM_API_BASE_URL}/sessions/${encodedSessionId}/recordings`;
          const responseOfGetRecordings = await fetch(
            urlForGetRecordings,
            optionsForGetRecordings
          );
          if (responseOfGetRecordings.ok) {
            const fileResponse = await responseOfGetRecordings.json();

            let fileArray: any[] = [];
            let filePathArray: any[] = [];

            for (let video of fileResponse.recording_files) {
              if (
                video.id != null &&
                video.status == "completed" &&
                video.recording_type == "audio_only" &&
                video.file_extension == "M4A" &&
                video.download_url != null &&
                video.download_url != ""
              ) {
                fileArray.push(video);
                filePathArray.push(video.id + "." + "m4a");
              }
            }
            if (fileArray.length === 0) {
              await VideoCallDao.updateMeetingTranscribingInProcessStatus(
                meetingId,
                false
              );
              return res.sendError("No Recordings found", 600);
            }

            if (
              fileArray.length !== 0 &&
              fileArray.length > meeting.audioFiles.length
            ) {
              await VideoCallDao.updateMeetingVideoCountWithVideoUrls(
                meetingId,
                fileArray.length,
                filePathArray
              );
              // download and save mp3
              let newAudioArray: any[] = [];

              for (let audioData of fileArray) {
                const fileNameExtensionInUrl = "m4a";
                let audioId = audioData.id + "." + fileNameExtensionInUrl;
                let downloadUrl = audioData.download_url;

                const filePathForSaveInServer = `${process.env.UPLOAD_PATH}/${UploadCategory.CALL_RECORDS}/${audioId}`;
                if (fs.existsSync(filePathForSaveInServer)) {
                  continue;
                  // already exists
                } else {
                  try {
                    const localFilePathOfVideoResponse =
                      await downloadAndSaveM4AFilesFromZoom(
                        downloadUrl,
                        filePathForSaveInServer,
                        token
                      );
                    if (fs.existsSync(filePathForSaveInServer)) {
                      const data: DUpload = {
                        userId: req.user._id,
                        originalName: audioId,
                        name: audioId,
                        type: "audio",
                        path: filePathForSaveInServer,
                        extension: fileNameExtensionInUrl,
                        category: UploadCategory.CALL_RECORDS,
                        signRequired: false,
                      };

                      let uploadedFile = await UploadDao.createUpload(data);
                      newAudioArray.push(uploadedFile._id);
                    } else {
                      continue;
                    }
                  } catch (error) {
                    continue;
                  }
                }
              }

              let previous: string | any[] = [];
              let previousAudioIds = previous.concat(meeting.audioFiles);
              let finalAudioIdsArray = previousAudioIds.concat(newAudioArray);
              await VideoCallDao.updateMeetingAudioArray(
                meeting._id,
                finalAudioIdsArray
              );

              // download and save mp3 end
            }
            const updatedMeeting = await Meeting.findById(meeting._id);
            if (updatedMeeting != null) {
              if (
                updatedMeeting.recordingAllowed &&
                updatedMeeting.recordingAllowed === true
              ) {
                if (
                  updatedMeeting.audioFiles != null &&
                  updatedMeeting.audioFiles.length !== 0 &&
                  updatedMeeting.audioFiles.length ===
                  updatedMeeting.noOfVideose
                ) {
                  await VideoCallDao.updateBothTranscribeStatusManually(
                    meeting._id,
                    false,
                    true
                  );
                } else {
                  await VideoCallDao.updateBothTranscribeStatusManually(
                    meeting._id,
                    false,
                    false
                  );
                }
              } else {
                await VideoCallDao.updateBothTranscribeStatusManually(
                  meeting._id,
                  false,
                  true
                );
              }
            } else {
              await VideoCallDao.updateBothTranscribeStatusManually(
                meeting._id,
                false,
                false
              );
            }

            return res.sendSuccess("Transcribe success");
          } else {
            await VideoCallDao.updateMeetingTranscribingInProcessStatus(
              meetingId,
              false
            );
            return res.sendError("Recording request error to video sdk", 600);
          }
        } else {
          await VideoCallDao.updateBothTranscribeStatusManually(
            meeting._id,
            false,
            true
          );
          return res.sendSuccess("Transcribe success");
        }
      } catch (error) {
        await VideoCallDao.updateMeetingTranscribingInProcessStatus(
          meetingId,
          false
        );
        return res.sendError("Something went wrong" + error);
      }
    } catch (error) {
      return res.sendError("Something went wrong" + error);
    }
  }

  export async function completeAndFinalizeZoomMeetingsEvery30MinutesNew() {
    try {
      AppLogger.info(
        `.:: Complete and Finalize Meetings Every 30 Minutes With Audio Files new ::.`
      );
      let incompletedMeetings = await Meeting.aggregate([
        {
          $match: {
            transcribingInProcess: false,
            transcribeCreated: false,
            callingStatus: {
              $in: [
                CallingStatus.STARTED,
                CallingStatus.ONGOING,
                CallingStatus.COMPLETED,
              ],
            },
          },
        },
        {
          $project: {
            meetingId: 1,
            meetingDuration: 1,
            clientId: 1,
            therapistId: 1,
            accepted: 1,
            noOfVideose: 1,
            transcribeAllowed: 1,
            transcribingInProcess: 1,
            transcribeCreated: 1,
            isAppointmentBased: 1,
            appointmentId: 1,
            audioFiles: 1,
            recordingAllowed: 1,
            createdBy: 1,
            callingStatus: 1,
            createdAt: 1,
            bothJoinedAt: 1,
            clientIdentifier: 1,
            therapistIdentifier: 1,
            timeDifference: {
              $subtract: [
                {
                  $divide: [{ $subtract: ["$$NOW", "$createdAt"] }, 1000 * 60],
                },
                "$meetingDuration",
              ],
            },
          },
        },
        { $match: { timeDifference: { $gt: 60 } } },
      ]).sort({ createdAt: -1 });

      incompletedMeetings.map(async function (preMeetingData: IMeeting) {
        const meetingId = preMeetingData.meetingId;
        try {
          if (
            !preMeetingData ||
            !preMeetingData.clientIdentifier ||
            !preMeetingData.therapistIdentifier ||
            !preMeetingData.bothJoinedAt
          ) {
            return false;
          }
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            true
          );
          if (
            preMeetingData.callingStatus === CallingStatus.STARTED ||
            preMeetingData.callingStatus === CallingStatus.ONGOING
          ) {
            if (preMeetingData.isAppointmentBased) {
              await Meeting.findByIdAndUpdate(preMeetingData._id, {
                callingStatus:
                  preMeetingData.callingStatus === CallingStatus.ONGOING
                    ? CallingStatus.COMPLETED
                    : CallingStatus.CANCELLED,
              });
              await Appointment.findByIdAndUpdate(
                preMeetingData.appointmentId,
                {
                  meetingStatus:
                    preMeetingData.callingStatus === CallingStatus.ONGOING
                      ? MeetingStatus.COMPLETED
                      : MeetingStatus.PENDING,
                  status:
                    preMeetingData.callingStatus === CallingStatus.ONGOING
                      ? AppointmentStatus.COMPLETED
                      : AppointmentStatus.PENDING,
                }
              );
            } else {
              await Meeting.findByIdAndUpdate(preMeetingData._id, {
                callingStatus:
                  preMeetingData.callingStatus === CallingStatus.ONGOING
                    ? CallingStatus.COMPLETED
                    : CallingStatus.CANCELLED,
              });
            }
          }
          // update spend time and get recording

          if (
            (preMeetingData.callingStatus === CallingStatus.ONGOING ||
              preMeetingData.callingStatus === CallingStatus.COMPLETED) &&
            preMeetingData.accepted === true
          ) {
            try {
              const therapistData = await Therapist.findById(
                preMeetingData.therapistId
              );
              if (therapistData == null) {
                await VideoCallDao.updateMeetingTranscribingInProcessStatus(
                  meetingId,
                  false
                );

                return false;
              }
              // get therapist end
              // Create token
              const payRateOfTherapist = therapistData.payRate
                ? therapistData.payRate
                : Number(process.env.DEFAULT_PAY_RATE_OF_THERAPIST);
              const token = await ZoomVideoCallHelper.genarateZoomApiToken();
              if (!token) {
                await VideoCallDao.updateMeetingTranscribingInProcessStatus(
                  meetingId,
                  false
                );
                return false;
              }
              const encodedSessionId =
                ZoomVideoCallHelper.doubleEncodeString(meetingId);
              if (!encodedSessionId || encodedSessionId == "") {
                await VideoCallDao.updateMeetingTranscribingInProcessStatus(
                  meetingId,
                  false
                );
                return false;
              }
              // create token
              // get meeting time that spent by therapist
              const optionsForGetTimeSpend = {
                method: "GET",
                headers: {
                  Authorization: "Bearer" + token,
                  "Content-Type": "application/json",
                },
              };

              // let previouslySpentDuration = 0;
              let finalTime = 0;
              // update spent time in current meeting

              const urlForGetTimeSpend = `${process.env.ZOOM_API_BASE_URL}/sessions/${encodedSessionId}?type=past`;
              const responseOfGetTimeSpend = await fetch(
                urlForGetTimeSpend,
                optionsForGetTimeSpend
              );
              if (responseOfGetTimeSpend.ok) {
                const responseData = await responseOfGetTimeSpend.json();
                if (
                  !responseData ||
                  !responseData.end_time ||
                  responseData.end_time == undefined ||
                  responseData.end_time == null ||
                  responseData.end_time == "" ||
                  !responseData.id ||
                  responseData.id.toString() != meetingId
                ) {
                  await VideoCallDao.updateMeetingTranscribingInProcessStatus(
                    meetingId,
                    false
                  );
                  return false;
                }
                const bothStartedTime = new Date(preMeetingData.bothJoinedAt);
                const endTimeOfMeeting = new Date(responseData.end_time);
                const timeDifferenceWithStartAndEnd = moment
                  .duration(
                    moment(endTimeOfMeeting).diff(moment(bothStartedTime))
                  )
                  .asMinutes();
                const finalTimeDifference = Math.round(
                  timeDifferenceWithStartAndEnd
                );
                finalTime = finalTimeDifference;
              } else {
                await VideoCallDao.updateMeetingTranscribingInProcessStatus(
                  meetingId,
                  false
                );

                return false;
              }

              if (finalTime >= 0) {
                const differenceOfTimes = Math.abs(
                  preMeetingData.meetingDuration - finalTime
                );
                if (finalTime < 95) {
                  await VideoCallDao.updateMeetingTime(meetingId, finalTime);
                } else {
                  await Meeting.findOneAndUpdate(
                    { meetingId: meetingId },
                    {
                      spentDuration: preMeetingData.meetingDuration,
                      isInvalidMeetingTime: true,
                      invalidDuration: finalTime,
                    },
                    { new: true }
                  );
                  finalTime = preMeetingData.meetingDuration;
                }
              }

              if (finalTime <= 0) {
                await VideoCallDao.updateMeetingTranscribingInProcessStatus(
                  meetingId,
                  false
                );
                if (finalTime <= 0) {
                  await VideoCallDao.updateBothTranscribeStatus(
                    preMeetingData._id
                  );
                }
                if (finalTime < 0) {
                  return false;
                }
              }


              let peviousTransaction =
                await VideoCallDao.getTransactionBuyMeetingId(
                  preMeetingData._id,
                  preMeetingData.therapistId
                );

              // create treatment history and create audio
              let preTreatmentHistory = await TreatmentHistory.findOne({
                meetingId: preMeetingData._id,
              });
              if (preTreatmentHistory == null) {
                // create session diagnosis report

                const diagnosisNoteData: DDiagnosisNote = {
                  clientId: preMeetingData.clientId,
                  meetingId: preMeetingData._id,
                  therapistId: preMeetingData.therapistId,
                  updated: false,
                  updatedByTherapist: false,
                  isVonageTranscribe: true,
                };
                const diagnosisNote = await VideoCallDao.createDiagnosisNote(
                  diagnosisNoteData
                );
                // create session diagnosis report end

                const treatmentHistoryDetails: DTreatmentHistory = {
                  clientId: preMeetingData.clientId,
                  therapistId: preMeetingData.therapistId,
                  note: "Meeting Transcribe",
                  isMeetingTranscribe: true,
                  meetingStartedTime: preMeetingData.createdAt,
                  meetingId: preMeetingData._id,
                  diagnosisNoteId: diagnosisNote._id,
                };

                let addedTreatmentHistory =
                  await TherapistDao.addTreatmentHistory(
                    treatmentHistoryDetails
                  );
              }
              // create treatment history and create audio end

              // create audio files
              if (
                preMeetingData.recordingAllowed &&
                preMeetingData.recordingAllowed === true
              ) {
                let optionsForGetRecordings = {
                  method: "GET",
                  headers: {
                    Authorization: "Bearer" + token,
                    "Content-Type": "application/json",
                  },
                };
                const urlForGetRecordings = `${process.env.ZOOM_API_BASE_URL}/sessions/${encodedSessionId}/recordings`;
                const responseOfGetRecordings = await fetch(
                  urlForGetRecordings,
                  optionsForGetRecordings
                );
                if (responseOfGetRecordings.ok) {
                  const fileResponse = await responseOfGetRecordings.json();

                  let fileArray: any[] = [];
                  let filePathArray: any[] = [];

                  for (let video of fileResponse.recording_files) {
                    if (
                      video.id != null &&
                      video.status == "completed" &&
                      video.recording_type == "audio_only" &&
                      video.file_extension == "M4A" &&
                      video.download_url != null &&
                      video.download_url != ""
                    ) {
                      fileArray.push(video);
                      filePathArray.push(video.id + "." + "m4a");
                    }
                  }
                  if (fileArray.length === 0) {
                    await VideoCallDao.updateMeetingIfNoRecordingFoundInSchedular(
                      preMeetingData._id
                    );
                    return false;
                  }

                  if (
                    fileArray.length !== 0 &&
                    fileArray.length > preMeetingData.audioFiles.length
                  ) {
                    await VideoCallDao.updateMeetingVideoCountWithVideoUrls(
                      meetingId,
                      fileArray.length,
                      filePathArray
                    );
                    // download and save mp3
                    let newAudioArray: any[] = [];

                    for (let audioData of fileArray) {
                      const fileNameExtensionInUrl = "m4a";
                      let audioId = audioData.id + "." + fileNameExtensionInUrl;
                      let downloadUrl = audioData.download_url;

                      const filePathForSaveInServer = `${process.env.UPLOAD_PATH}/${UploadCategory.CALL_RECORDS}/${audioId}`;
                      if (fs.existsSync(filePathForSaveInServer)) {
                        continue;
                        // already exists
                      } else {
                        try {
                          const localFilePathOfVideoResponse =
                            await downloadAndSaveM4AFilesFromZoom(
                              downloadUrl,
                              filePathForSaveInServer,
                              token
                            );
                          if (fs.existsSync(filePathForSaveInServer)) {
                            const data: DUpload = {
                              userId: preMeetingData.therapistId,
                              originalName: audioId,
                              name: audioId,
                              type: "audio",
                              path: filePathForSaveInServer,
                              extension: fileNameExtensionInUrl,
                              category: UploadCategory.CALL_RECORDS,
                              signRequired: false,
                            };

                            let uploadedFile = await UploadDao.createUpload(
                              data
                            );
                            newAudioArray.push(uploadedFile._id);
                          } else {
                            continue;
                          }
                        } catch (error) {
                          continue;
                        }
                      }
                    }

                    let previous: string | any[] = [];
                    let previousAudioIds = previous.concat(
                      preMeetingData.audioFiles
                    );
                    let finalAudioIdsArray =
                      previousAudioIds.concat(newAudioArray);
                    await VideoCallDao.updateMeetingAudioArray(
                      preMeetingData._id,
                      finalAudioIdsArray
                    );

                    // download and save mp3 end
                  }
                  const updatedMeeting = await Meeting.findById(
                    preMeetingData._id
                  );
                  if (updatedMeeting != null) {
                    if (
                      updatedMeeting.recordingAllowed &&
                      updatedMeeting.recordingAllowed === true
                    ) {
                      if (
                        updatedMeeting.audioFiles != null &&
                        updatedMeeting.audioFiles.length !== 0 &&
                        updatedMeeting.audioFiles.length ===
                        updatedMeeting.noOfVideose
                      ) {
                        await VideoCallDao.updateBothTranscribeStatusManually(
                          preMeetingData._id,
                          false,
                          true
                        );
                      } else {
                        await VideoCallDao.updateBothTranscribeStatusManually(
                          preMeetingData._id,
                          false,
                          false
                        );
                      }
                    } else {
                      await VideoCallDao.updateBothTranscribeStatusManually(
                        preMeetingData._id,
                        false,
                        true
                      );
                    }
                  } else {
                    await VideoCallDao.updateBothTranscribeStatusManually(
                      preMeetingData._id,
                      false,
                      false
                    );
                  }

                  return false;
                } else {
                  await VideoCallDao.updateMeetingTranscribingInProcessStatus(
                    meetingId,
                    false
                  );
                  return false;
                }
              } else {
                await VideoCallDao.updateBothTranscribeStatusManually(
                  preMeetingData._id,
                  false,
                  true
                );
                return false;
              }
            } catch (error) {
              await VideoCallDao.updateMeetingTranscribingInProcessStatus(
                meetingId,
                false
              );

              return false;
            }
          } else {
            await VideoCallDao.updateMeetingTranscribingInProcessStatus(
              meetingId,
              false
            );

            return false;
          }
        } catch (error) {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );

          return false;
        }
      });
    } catch (err) {
      return;
    }
  }

  // export async function completeAndFinalizeZoomMeetingsEvery30Minutes() {
  //   try {
  //     AppLogger.info(
  //       `.:: Complete and Finalize Meetings Every 30 Minutes With Audio Files ::.`
  //     );
  //     let incompletedMeetings = await Meeting.aggregate([
  //       {
  //         $match: {
  //           transcribingInProcess: false,
  //           transcribeCreated: false,
  //           callingStatus: {
  //             $in: [
  //               CallingStatus.STARTED,
  //               CallingStatus.ONGOING,
  //               CallingStatus.COMPLETED,
  //             ],
  //           },
  //         },
  //       },
  //       {
  //         $project: {
  //           meetingId: 1,
  //           meetingDuration: 1,
  //           clientId: 1,
  //           therapistId: 1,
  //           accepted: 1,
  //           noOfVideose: 1,
  //           transcribeAllowed: 1,
  //           transcribingInProcess: 1,
  //           transcribeCreated: 1,
  //           isAppointmentBased: 1,
  //           appointmentId: 1,
  //           audioFiles: 1,
  //           recordingAllowed: 1,
  //           createdBy: 1,
  //           callingStatus: 1,
  //           createdAt: 1,
  //           bothJoinedAt: 1,
  //           clientIdentifier: 1,
  //           therapistIdentifier: 1,
  //           timeDifference: {
  //             $subtract: [
  //               {
  //                 $divide: [{ $subtract: ["$$NOW", "$createdAt"] }, 1000 * 60],
  //               },
  //               "$meetingDuration",
  //             ],
  //           },
  //         },
  //       },
  //       { $match: { timeDifference: { $gt: 60 } } },
  //     ]);

  //     incompletedMeetings.map(async function (preMeetingData: IMeeting) {
  //       const meetingId = preMeetingData.meetingId;
  //       try {
  //         if (
  //           !preMeetingData ||
  //           !preMeetingData.clientIdentifier ||
  //           !preMeetingData.therapistIdentifier ||
  //           !preMeetingData.bothJoinedAt
  //         ) {
  //           return false;
  //         }
  //         await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //           meetingId,
  //           true
  //         );
  //         if (
  //           preMeetingData.callingStatus === CallingStatus.STARTED ||
  //           preMeetingData.callingStatus === CallingStatus.ONGOING
  //         ) {
  //           if (preMeetingData.isAppointmentBased) {
  //             await Meeting.findByIdAndUpdate(preMeetingData._id, {
  //               callingStatus:
  //                 preMeetingData.callingStatus === CallingStatus.ONGOING
  //                   ? CallingStatus.COMPLETED
  //                   : CallingStatus.CANCELLED,
  //             });
  //             await Appointment.findByIdAndUpdate(
  //               preMeetingData.appointmentId,
  //               {
  //                 meetingStatus:
  //                   preMeetingData.callingStatus === CallingStatus.ONGOING
  //                     ? MeetingStatus.COMPLETED
  //                     : MeetingStatus.PENDING,
  //                 status:
  //                   preMeetingData.callingStatus === CallingStatus.ONGOING
  //                     ? AppointmentStatus.COMPLETED
  //                     : AppointmentStatus.PENDING,
  //               }
  //             );
  //           } else {
  //             await Meeting.findByIdAndUpdate(preMeetingData._id, {
  //               callingStatus:
  //                 preMeetingData.callingStatus === CallingStatus.ONGOING
  //                   ? CallingStatus.COMPLETED
  //                   : CallingStatus.CANCELLED,
  //             });
  //           }
  //         }
  //         // update spend time and get recording

  //         if (
  //           (preMeetingData.callingStatus === CallingStatus.ONGOING ||
  //             preMeetingData.callingStatus === CallingStatus.COMPLETED) &&
  //           preMeetingData.accepted === true
  //         ) {
  //           try {
  //             const therapistData = await Therapist.findById(
  //               preMeetingData.therapistId
  //             );
  //             if (therapistData == null) {
  //               await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //                 meetingId,
  //                 false
  //               );

  //               return false;
  //             }
  //             // get therapist end
  //             // Create token
  //             const payRateOfTherapist = therapistData.payRate
  //               ? therapistData.payRate
  //               : Number(process.env.DEFAULT_PAY_RATE_OF_THERAPIST);
  //             const token = await ZoomVideoCallHelper.genarateZoomApiToken();
  //             if (!token) {
  //               await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //                 meetingId,
  //                 false
  //               );
  //               return false;
  //             }
  //             const encodedSessionId =
  //               ZoomVideoCallHelper.doubleEncodeString(meetingId);
  //             if (!encodedSessionId || encodedSessionId == "") {
  //               await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //                 meetingId,
  //                 false
  //               );
  //               return false;
  //             }
  //             // create token
  //             // get meeting time that spent by therapist
  //             const optionsForGetTimeSpend = {
  //               method: "GET",
  //               headers: {
  //                 Authorization: "Bearer" + token,
  //                 "Content-Type": "application/json",
  //               },
  //             };

  //             // let previouslySpentDuration = 0;
  //             let finalTime = 0;
  //             // update spent time in current meeting

  //             const urlForGetTimeSpend = `${process.env.ZOOM_API_BASE_URL}/sessions/${encodedSessionId}?type=past`;
  //             const responseOfGetTimeSpend = await fetch(
  //               urlForGetTimeSpend,
  //               optionsForGetTimeSpend
  //             );
  //             if (responseOfGetTimeSpend.ok) {
  //               const responseData = await responseOfGetTimeSpend.json();
  //               if (
  //                 !responseData ||
  //                 !responseData.end_time ||
  //                 responseData.end_time == undefined ||
  //                 responseData.end_time == null ||
  //                 responseData.end_time == "" ||
  //                 !responseData.id ||
  //                 responseData.id.toString() != meetingId
  //               ) {
  //                 await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //                   meetingId,
  //                   false
  //                 );
  //                 return false;
  //               }
  //               const bothStartedTime = new Date(preMeetingData.bothJoinedAt);
  //               const endTimeOfMeeting = new Date(responseData.end_time);
  //               const timeDifferenceWithStartAndEnd = moment
  //                 .duration(
  //                   moment(endTimeOfMeeting).diff(moment(bothStartedTime))
  //                 )
  //                 .asMinutes();
  //               const finalTimeDifference = Math.round(
  //                 timeDifferenceWithStartAndEnd
  //               );
  //               finalTime = finalTimeDifference;
  //             } else {
  //               await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //                 meetingId,
  //                 false
  //               );

  //               return false;
  //             }

  //             if (finalTime >= 0) {
  //               const differenceOfTimes = Math.abs(
  //                 preMeetingData.meetingDuration - finalTime
  //               );
  //               if (finalTime < 95) {
  //                 await VideoCallDao.updateMeetingTime(meetingId, finalTime);
  //               } else {
  //                 await Meeting.findOneAndUpdate(
  //                   { meetingId: meetingId },
  //                   {
  //                     spentDuration: preMeetingData.meetingDuration,
  //                     isInvalidMeetingTime: true,
  //                     invalidDuration: finalTime,
  //                   },
  //                   { new: true }
  //                 );
  //                 finalTime = preMeetingData.meetingDuration;
  //               }
  //             }

  //             if (finalTime <= 0) {
  //               await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //                 meetingId,
  //                 false
  //               );
  //               if (finalTime <= 0) {
  //                 await VideoCallDao.updateBothTranscribeStatus(
  //                   preMeetingData._id
  //                 );
  //               }
  //               if (finalTime < 0) {
  //                 return false;
  //               }
  //             }

  //             let priceForSession = 0;
  //             // calculate payment

  //             if (finalTime >= 20) {
  //               if (finalTime >= 20 && finalTime < 45) {
  //                 const halfRate = payRateOfTherapist / 2;
  //                 priceForSession = halfRate;
  //               } else if (finalTime >= 45) {
  //                 const fullRate = payRateOfTherapist;
  //                 priceForSession = fullRate;
  //               }
  //             } else {
  //               priceForSession = 0;
  //             }
  //             // calculate payment

  //             // get meeting time that spent by therapist

  //             let peviousTransaction =
  //               await VideoCallDao.getTransactionBuyMeetingId(
  //                 preMeetingData._id,
  //                 preMeetingData.therapistId
  //               );
  //             if (peviousTransaction == null) {
  //               let transactionCreated =
  //                 await TransactionsDao.createTransaction(
  //                   preMeetingData.therapistId,
  //                   priceForSession.toString(),
  //                   TransactionType.EARNING,
  //                   preMeetingData._id.toString()
  //                 );
  //             }

  //             // create treatment history and create audio
  //             let preTreatmentHistory = await TreatmentHistory.findOne({
  //               meetingId: preMeetingData._id,
  //             });
  //             if (preTreatmentHistory == null) {
  //               // create session diagnosis report

  //               const diagnosisNoteData: DDiagnosisNote = {
  //                 clientId: preMeetingData.clientId,
  //                 meetingId: preMeetingData._id,
  //                 therapistId: preMeetingData.therapistId,
  //                 updated: false,
  //                 updatedByTherapist: false,
  //               };
  //               const diagnosisNote = await VideoCallDao.createDiagnosisNote(
  //                 diagnosisNoteData
  //               );
  //               // create session diagnosis report end

  //               const treatmentHistoryDetails: DTreatmentHistory = {
  //                 clientId: preMeetingData.clientId,
  //                 therapistId: preMeetingData.therapistId,
  //                 note: "Meeting Transcribe",
  //                 isMeetingTranscribe: true,
  //                 meetingStartedTime: preMeetingData.createdAt,
  //                 meetingId: preMeetingData._id,
  //                 diagnosisNoteId: diagnosisNote._id,
  //               };

  //               let addedTreatmentHistory =
  //                 await TherapistDao.addTreatmentHistory(
  //                   treatmentHistoryDetails
  //                 );
  //             }
  //             // create treatment history and create audio end

  //             // create audio files

  //             // if (
  //             //   preMeetingData.recordingAllowed &&
  //             //   preMeetingData.recordingAllowed === true
  //             // ) {
  //             //   let optionsForGetRecordings = {
  //             //     method: "GET",
  //             //     headers: {
  //             //       Authorization: token,
  //             //       "Content-Type": "application/json",
  //             //     },
  //             //   };
  //             //   const urlForGetRecordings = `${process.env.VIDEO_SDK_BASE_URL}/v2/recordings?roomId=${meetingId}`;
  //             //   const responseOfGetRecordings = await fetch(
  //             //     urlForGetRecordings,
  //             //     optionsForGetRecordings
  //             //   );
  //             //   if (responseOfGetRecordings.ok) {
  //             //     const fileResponse = await responseOfGetRecordings.json();
  //             //     let fileArray: any[] = [];
  //             //     let filePathArray: any[] = [];

  //             //     for (let video of fileResponse.data) {
  //             //       if (
  //             //         video.id &&
  //             //         video.file != null &&
  //             //         video.file.filePath != null
  //             //       ) {
  //             //         fileArray.push(video);
  //             //         filePathArray.push(video.file.filePath);
  //             //       }
  //             //     }
  //             //     if (fileArray.length === 0) {
  //             //       await VideoCallDao.updateMeetingIfNoRecordingFoundInSchedular(
  //             //         preMeetingData._id
  //             //       );
  //             //       return false;
  //             //     }

  //             //     if (
  //             //       fileArray.length !== 0 &&
  //             //       fileArray.length > preMeetingData.audioFiles.length
  //             //     ) {
  //             //       await VideoCallDao.updateMeetingVideoCountWithVideoUrls(
  //             //         meetingId,
  //             //         fileArray.length,
  //             //         filePathArray
  //             //       );
  //             //       // download and save mp3
  //             //       let newAudioArray: any[] = [];
  //             //       for (let audioData of fileArray) {
  //             //         let audioFileName = audioData.file.filePath;

  //             //         const fileNameExtensionInUrl = audioFileName
  //             //           .toString()
  //             //           .split(".")[1];
  //             //         const filePathForSaveInServer = `${process.env.UPLOAD_PATH}/${UploadCategory.CALL_RECORDS}/${audioFileName}`;

  //             //         if (fs.existsSync(filePathForSaveInServer)) {
  //             //           continue;
  //             //           // already exists
  //             //         } else {
  //             //           // convert and save mp3
  //             //           try {
  //             //             const localFilePathOfVideoResponse =
  //             //               await downloadAndSaveMP3FileFromAWS(
  //             //                 audioFileName,
  //             //                 filePathForSaveInServer
  //             //               );

  //             //             if (fs.existsSync(filePathForSaveInServer)) {
  //             //               const data: DUpload = {
  //             //                 userId: preMeetingData.therapistId,
  //             //                 originalName: audioFileName,
  //             //                 name: audioFileName,
  //             //                 type: "audio",
  //             //                 path: filePathForSaveInServer,
  //             //                 extension: fileNameExtensionInUrl,
  //             //                 category: UploadCategory.CALL_RECORDS,
  //             //                 signRequired: false,
  //             //               };

  //             //               let uploadedFile = await UploadDao.createUpload(
  //             //                 data
  //             //               );
  //             //               newAudioArray.push(uploadedFile._id);
  //             //             } else {
  //             //               continue;
  //             //             }
  //             //           } catch (error) {
  //             //             continue;
  //             //           }
  //             //         }
  //             //       }

  //             //       let previous: string | any[] = [];
  //             //       let previousAudioIds = previous.concat(
  //             //         preMeetingData.audioFiles
  //             //       );
  //             //       let finalAudioIdsArray =
  //             //         previousAudioIds.concat(newAudioArray);
  //             //       await VideoCallDao.updateMeetingAudioArray(
  //             //         preMeetingData._id,
  //             //         finalAudioIdsArray
  //             //       );

  //             //       // download and save mp3 end
  //             //     }
  //             //     const updatedMeeting = await Meeting.findById(
  //             //       preMeetingData._id
  //             //     );
  //             //     if (updatedMeeting != null) {
  //             //       if (
  //             //         updatedMeeting.recordingAllowed &&
  //             //         updatedMeeting.recordingAllowed === true
  //             //       ) {
  //             //         if (
  //             //           updatedMeeting.audioFiles != null &&
  //             //           updatedMeeting.audioFiles.length !== 0 &&
  //             //           updatedMeeting.audioFiles.length ===
  //             //             updatedMeeting.noOfVideose
  //             //         ) {
  //             //           await VideoCallDao.updateBothTranscribeStatusManually(
  //             //             preMeetingData._id,
  //             //             false,
  //             //             true
  //             //           );
  //             //         } else {
  //             //           await VideoCallDao.updateBothTranscribeStatusManually(
  //             //             preMeetingData._id,
  //             //             false,
  //             //             false
  //             //           );
  //             //         }
  //             //       } else {
  //             //         await VideoCallDao.updateBothTranscribeStatusManually(
  //             //           preMeetingData._id,
  //             //           false,
  //             //           true
  //             //         );
  //             //       }
  //             //     } else {
  //             //       await VideoCallDao.updateBothTranscribeStatusManually(
  //             //         preMeetingData._id,
  //             //         false,
  //             //         false
  //             //       );
  //             //     }

  //             //     return false;
  //             //   } else {
  //             //     await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //             //       meetingId,
  //             //       false
  //             //     );

  //             //     return false;
  //             //   }
  //             // } else {
  //             //   await VideoCallDao.updateBothTranscribeStatusManually(
  //             //     preMeetingData._id,
  //             //     false,
  //             //     true
  //             //   );

  //             //   return false;
  //             // }
  //             await VideoCallDao.updateBothTranscribeStatusManually(
  //               preMeetingData._id,
  //               false,
  //               true
  //             );
  //             return false;
  //           } catch (error) {
  //             await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //               meetingId,
  //               false
  //             );

  //             return false;
  //           }
  //         } else {
  //           await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //             meetingId,
  //             false
  //           );

  //           return false;
  //         }
  //       } catch (error) {
  //         await VideoCallDao.updateMeetingTranscribingInProcessStatus(
  //           meetingId,
  //           false
  //         );

  //         return false;
  //       }
  //     });
  //   } catch (err) {
  //     return;
  //   }
  // }

  export async function finaliseZoomMeetingWithoutSavingRecordings(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      // param validation
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }
      const meetingId = req.body.meetingId;
      const meeting = await Meeting.findOne({
        meetingId: meetingId,
        accepted: true,
        therapistId: req.user._id,
        transcribingInProcess: false,
        transcribeCreated: false,
        callingStatus: {
          $in: [CallingStatus.ONGOING, CallingStatus.COMPLETED],
        },
      });
      if (
        !meeting ||
        !meeting.clientIdentifier ||
        !meeting.therapistIdentifier ||
        !meeting.bothJoinedAt
      ) {
        return res.sendError("Wrong id");
      }
      try {
        // update meeting and appointment status
        const updatedMeeting = await Meeting.findOneAndUpdate(
          { meetingId: meetingId },
          { accepted: true, callingStatus: CallingStatus.COMPLETED },
          { new: true }
        );

        if (updatedMeeting && updatedMeeting.isAppointmentBased) {
          await Appointment.findByIdAndUpdate(updatedMeeting.appointmentId, {
            status: AppointmentStatus.COMPLETED,
            meetingStatus: MeetingStatus.COMPLETED,
          });
        }
        // update meeting and appointment status end

        await VideoCallDao.updateMeetingTranscribingInProcessStatus(
          meetingId,
          true
        );
        // param validation end
        // get therapist
        const therapistData = await Therapist.findById(meeting.therapistId);
        if (therapistData == null) {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );
          return res.sendError("Therapist not exists");
        }
        // get therapist end
        // Create token
        const payRateOfTherapist = therapistData.payRate
          ? therapistData.payRate
          : Number(process.env.DEFAULT_PAY_RATE_OF_THERAPIST);

        const token = await ZoomVideoCallHelper.genarateZoomApiToken();
        if (!token) {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );
          return res.sendError("api token genaration error occured");
        }
        const encodedSessionId =
          ZoomVideoCallHelper.doubleEncodeString(meetingId);
        if (!encodedSessionId || encodedSessionId == "") {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );
          return res.sendError("session id encode error happend");
        }
        // create token
        // get meeting time that spent by therapist
        const optionsForGetTimeSpend = {
          method: "GET",
          headers: {
            Authorization: "Bearer" + token,
            "Content-Type": "application/json",
          },
        };

        // let previouslySpentDuration = 0;
        let finalTime = 0;
        // update spent time in current meeting

        const urlForGetTimeSpend = `${process.env.ZOOM_API_BASE_URL}/sessions/${encodedSessionId}?type=past`;
        const responseOfGetTimeSpend = await fetch(
          urlForGetTimeSpend,
          optionsForGetTimeSpend
        );
        if (responseOfGetTimeSpend.ok) {
          const responseData = await responseOfGetTimeSpend.json();
          if (
            !responseData ||
            !responseData.end_time ||
            responseData.end_time == undefined ||
            responseData.end_time == null ||
            responseData.end_time == "" ||
            !responseData.id ||
            responseData.id.toString() != meetingId
          ) {
            await VideoCallDao.updateMeetingTranscribingInProcessStatus(
              meetingId,
              false
            );
            return res.sendError("Meeting data fetching failed");
          }
          const bothStartedTime = new Date(meeting.bothJoinedAt);
          const endTimeOfMeeting = new Date(responseData.end_time);
          const timeDifferenceWithStartAndEnd = moment
            .duration(moment(endTimeOfMeeting).diff(moment(bothStartedTime)))
            .asMinutes();
          const finalTimeDifference = Math.round(timeDifferenceWithStartAndEnd);
          finalTime = finalTimeDifference;
        } else {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );
          return res.sendError("Meeting data fetching failed");
        }

        if (finalTime >= 0) {
          const differenceOfTimes = Math.abs(
            meeting.meetingDuration - finalTime
          );
          if (finalTime < 95) {
            await VideoCallDao.updateMeetingTime(meetingId, finalTime);
          } else {
            await Meeting.findOneAndUpdate(
              { meetingId: meetingId },
              {
                spentDuration: meeting.meetingDuration,
                isInvalidMeetingTime: true,
                invalidDuration: finalTime,
              },
              { new: true }
            );
            finalTime = meeting.meetingDuration;
          }
        }

        if (finalTime <= 0) {
          await VideoCallDao.updateMeetingTranscribingInProcessStatus(
            meetingId,
            false
          );
          if (finalTime <= 0) {
            await VideoCallDao.updateBothTranscribeStatus(meeting._id);
          }
          if (finalTime < 0) {
            return res.sendError("Invalid spend time");
          }
        }




        let peviousTransaction = await VideoCallDao.getTransactionBuyMeetingId(
          meeting._id,
          req.user._id
        );


        // create treatment history and create audio
        let preTreatmentHistory = await TreatmentHistory.findOne({
          meetingId: meeting._id,
        });
        if (preTreatmentHistory == null) {
          // create session diagnosis report
          const diagnosisNoteData: DDiagnosisNote = {
            clientId: meeting.clientId,
            meetingId: meeting._id,
            therapistId: meeting.therapistId,
            updated: false,
            updatedByTherapist: false,
            isVonageTranscribe: true,
          };
          const diagnosisNote = await VideoCallDao.createDiagnosisNote(
            diagnosisNoteData
          );
          // create session diagnosis report end
          let treatmentHistoryDetails: DTreatmentHistory = {
            clientId: meeting.clientId,
            therapistId: meeting.therapistId,
            note: "Meeting Transcribe",
            isMeetingTranscribe: true,
            meetingStartedTime: meeting.createdAt,
            meetingId: meeting._id,
            diagnosisNoteId: diagnosisNote._id,
          };

          let addedTreatmentHistory = await TherapistDao.addTreatmentHistory(
            treatmentHistoryDetails
          );
        }
        // create treatment history and create audio end
        await VideoCallDao.updateMeetingTranscribingInProcessStatus(
          meetingId,
          false
        );

        return res.sendSuccess("Transcribe success");
      } catch (error) {
        await VideoCallDao.updateMeetingTranscribingInProcessStatus(
          meetingId,
          false
        );
        return res.sendError("Something went wrong" + error);
      }
    } catch (error) {
      return res.sendError("Something went wrong" + error);
    }
  }

  async function downloadAndSaveM4AFilesFromZoom(
    downloadUrl: any,
    filPathForSave: String,
    token: String
  ) {
    return new Promise(async (resolve, reject) => {
      let file: any;
      try {
        const audioFilePathInServer = filPathForSave;
        file = fs.createWriteStream(audioFilePathInServer);

        file.on("finish", () => {
          file.close();
          resolve(audioFilePathInServer);
        });

        file.on("error", (err: any) => {
          file.close();
          if (fs.existsSync(audioFilePathInServer)) {
            fs.unlinkSync(audioFilePathInServer);
          }
          reject(new Error("File saving error in local server "));
        });

        let optionsForDownlodRecordings = {
          method: "GET",
          headers: {
            Authorization: "Bearer" + token,
            "Content-Type": "application/json",
          },
        };
        const urlForDownloadRecordings = downloadUrl;
        const responseOfDownloadRecordings = await fetch(
          urlForDownloadRecordings,
          optionsForDownlodRecordings
        );
        if (
          responseOfDownloadRecordings.ok &&
          responseOfDownloadRecordings.body
        ) {
          const fileResponse = responseOfDownloadRecordings.body;
          fileResponse.pipe(file);
        } else {
          if (file) {
            file.close();
            if (fs.existsSync(filPathForSave)) {
              fs.unlinkSync(filPathForSave);
            }
          }
          reject(new Error("Download recording error"));
        }
      } catch (error) {
        if (file) {
          file.close();
          if (fs.existsSync(filPathForSave)) {
            fs.unlinkSync(filPathForSave);
          }
        }
        reject(new Error("File saving error in local server "));
      }
    });
  }

  export function createTranscribeTextManuallyWithZoomParamValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isString()
        .withMessage("meetingId is not a Type of string"),
    ];
  }
  // export async function createTranscribeTextManuallyWithZoom(
  //   req: Request,
  //   res: Response,
  //   next: NextFunction
  // ) {
  //   try {
  //     // param validation
  //     const errors = validationResult(req);
  //     if (!errors.isEmpty()) {
  //       return res.sendError(errors.array()[0]);
  //     }
  //     const meetingId = req.body.meetingId;
  //     const meeting = await Meeting.findOne({
  //       meetingId: meetingId,
  //       accepted: true,
  //       therapistId: req.user._id,
  //       transcribingInProcess: false,
  //       transcribeCreated: true,
  //       callingStatus: CallingStatus.COMPLETED,
  //       transcribeAllowed: true,
  //       recordingAllowed: true,
  //       $and: [
  //         {
  //           $or: [
  //             { transcriptTextInProcess: { $exists: false } },
  //             { transcriptTextInProcess: false },
  //           ],
  //         },
  //         {
  //           $or: [
  //             { transcriptTextCreated: { $exists: false } },
  //             { transcriptTextCreated: false },
  //           ],
  //         },
  //       ],
  //     });
  //     if (meeting == null) {
  //       return res.sendError("Wrong id");
  //     }
  //     try {
  //       if (
  //         meeting.audioFiles &&
  //         meeting.audioFiles.length &&
  //         meeting.audioFiles.length > 0 &&
  //         meeting.videoUrls &&
  //         meeting.videoUrls.length &&
  //         meeting.videoUrls.length > 0
  //       ) {
  //         const statusUpdated = await Meeting.findByIdAndUpdate(meeting._id, {
  //           transcriptTextInProcess: true,
  //         });
  //         let preTreatmentHistory = await TreatmentHistory.findOne({
  //           meetingId: meeting._id,
  //         });
  //         if (preTreatmentHistory == null) {
  //           const statusUpdated = await Meeting.findByIdAndUpdate(meeting._id, {
  //             transcriptTextInProcess: false,
  //           });
  //           return res.sendError("No treatment history found for meeting");
  //         }
  //         let previousTranscribeId;

  //         let preTrans = await Transcribe.findOne({
  //           meetingId: meeting.meetingId,
  //         });
  //         if (preTrans == null) {
  //           const transcribeData: DTranscribe = {
  //             clientId: meeting.clientId,
  //             meetingId: meeting.meetingId,
  //             therapistId: meeting.therapistId,
  //             transcriptText: [],
  //             videoUrl: "",
  //             transCribeInProcess: true,
  //             meetingStartedTime: meeting.createdAt,
  //             speakersDetected: false,
  //             speakersArray: [],
  //           };
  //           const newTranscribe = await VideoCallDao.createTranscribe(
  //             transcribeData
  //           );
  //           previousTranscribeId = newTranscribe._id;
  //         } else {
  //           previousTranscribeId = preTrans._id;
  //         }
  //         const updatedTreatmentHistory =
  //           await TreatmentHistory.findByIdAndUpdate(preTreatmentHistory._id, {
  //             transcribeId: previousTranscribeId,
  //           });

  //         // create transcribe text with deepgram
  //         let speakersDetectedPreviously = false;
  //         let oneOfTranscriptSuccess = false;
  //         let speakerOne;
  //         let speakerTwo;
  //         let speakerOneName;
  //         let speakerTwoName;
  //         let finalText: string | any[] = [];

  //         const previousCreatedTranscribe = await Transcribe.findById(
  //           previousTranscribeId
  //         );
  //         if (previousCreatedTranscribe == null) {
  //           const statusUpdated = await Meeting.findByIdAndUpdate(meeting._id, {
  //             transcriptTextInProcess: false,
  //           });
  //           return res.sendError("No transcribe found for meeting");
  //         }
  //         if (
  //           previousCreatedTranscribe.speakersDetected &&
  //           previousCreatedTranscribe.speakersDetected === true &&
  //           previousCreatedTranscribe.speakersArray &&
  //           previousCreatedTranscribe.speakersArray.length &&
  //           previousCreatedTranscribe.speakersArray.length === 2
  //         ) {
  //           speakersDetectedPreviously = true;
  //           speakerOne = previousCreatedTranscribe.speakersArray[0];
  //           speakerTwo = previousCreatedTranscribe.speakersArray[1];
  //           let userOne = await User.findById(speakerOne);
  //           let userTwo = await User.findById(speakerTwo);
  //           speakerOneName = userOne.firstname;
  //           speakerTwoName = userTwo.firstname;
  //         }
  //         if (previousCreatedTranscribe.transcriptText) {
  //           finalText = previousCreatedTranscribe.transcriptText;
  //         }

  //         for (let videoPath of meeting.videoUrls) {
  //           const fileName = videoPath;
  //           const filePath = `${process.env.UPLOAD_PATH}/${UploadCategory.CALL_RECORDS}/${fileName}`;

  //           if (fs.existsSync(filePath)) {
  //             try {
  //               const transcript = await transcribeLocalVideoWithStream(
  //                 filePath
  //               );
  //               if (transcript.utterances !== undefined) {
  //                 if (!speakersDetectedPreviously) {
  //                   let speakerHistoryFromDeepgram: string | any[] = [];
  //                   let speakerHistoryFromDB: string | any[] = [];
  //                   speakerOne = meeting.therapistId.toString();
  //                   speakerTwo = meeting.clientId.toString();
  //                   let therapist = meeting.therapistId.toString();
  //                   let client = meeting.clientId.toString();
  //                   let therapistIsSpeakerOne = 0;
  //                   let therapistIsSpeakerTwo = 0;

  //                   if (
  //                     meeting.speakerHistory &&
  //                     meeting.speakerHistory.length &&
  //                     meeting.speakerHistory.length > 0 &&
  //                     transcript.utterances !== undefined
  //                   ) {
  //                     speakerHistoryFromDB = meeting.speakerHistory;

  //                     for (let i = 0; i < transcript.utterances.length; i++) {
  //                       if (
  //                         speakerHistoryFromDB.length ===
  //                         speakerHistoryFromDeepgram.length
  //                       ) {
  //                         break;
  //                       }
  //                       if (i === 0) {
  //                         speakerHistoryFromDeepgram.push(
  //                           transcript.utterances[i].speaker
  //                         );
  //                       } else {
  //                         if (
  //                           speakerHistoryFromDeepgram[
  //                             speakerHistoryFromDeepgram.length - 1
  //                           ] !== transcript.utterances[i].speaker
  //                         ) {
  //                           speakerHistoryFromDeepgram.push(
  //                             transcript.utterances[i].speaker
  //                           );
  //                         }
  //                       }
  //                     }

  //                     for (let i = 0; i < speakerHistoryFromDB.length; i++) {
  //                       if (speakerHistoryFromDeepgram.length > i) {
  //                         if (
  //                           speakerHistoryFromDB[i] === therapist &&
  //                           speakerHistoryFromDeepgram[i] &&
  //                           speakerHistoryFromDeepgram[i] === 0
  //                         ) {
  //                           therapistIsSpeakerOne = therapistIsSpeakerOne + 1;
  //                         } else if (
  //                           speakerHistoryFromDB[i] === therapist &&
  //                           speakerHistoryFromDeepgram[i] &&
  //                           speakerHistoryFromDeepgram[i] === 1
  //                         ) {
  //                           therapistIsSpeakerTwo = therapistIsSpeakerTwo + 1;
  //                         } else if (
  //                           speakerHistoryFromDB[i] === client &&
  //                           speakerHistoryFromDeepgram[i] &&
  //                           speakerHistoryFromDeepgram[i] === 0
  //                         ) {
  //                           therapistIsSpeakerTwo = therapistIsSpeakerTwo + 1;
  //                         } else if (
  //                           speakerHistoryFromDB[i] === client &&
  //                           speakerHistoryFromDeepgram[i] &&
  //                           speakerHistoryFromDeepgram[i] === 1
  //                         ) {
  //                           therapistIsSpeakerOne = therapistIsSpeakerOne + 1;
  //                         }
  //                       }
  //                     }
  //                   }
  //                   if (therapistIsSpeakerOne < therapistIsSpeakerTwo) {
  //                     speakerOne = client;
  //                     speakerTwo = therapist;
  //                   }

  //                   let userOne = await User.findById(speakerOne);
  //                   let userTwo = await User.findById(speakerTwo);

  //                   speakerOneName = userOne.firstname;
  //                   speakerTwoName = userTwo.firstname;
  //                   speakersDetectedPreviously = true;
  //                   let speakerArrayForSave = [
  //                     Types.ObjectId(speakerOne),
  //                     Types.ObjectId(speakerTwo),
  //                   ];

  //                   const updatedTranscribe =
  //                     await Transcribe.findByIdAndUpdate(previousTranscribeId, {
  //                       speakersDetected: true,
  //                       speakersArray: speakerArrayForSave,
  //                     });
  //                 }
  //                 // determine speakers end
  //                 if (transcript.utterances !== undefined) {
  //                   oneOfTranscriptSuccess = true;
  //                   let previousTextStartTime;
  //                   let firstElement = true;
  //                   for (let text of transcript.utterances) {
  //                     if (firstElement) {
  //                       previousTextStartTime = text.start;
  //                       if (text.transcript !== "") {
  //                         finalText.push(
  //                           text.speaker === 0
  //                             ? speakerOneName + " : " + text.transcript
  //                             : speakerTwoName + " : " + text.transcript
  //                         );
  //                       }

  //                       firstElement = false;
  //                     } else {
  //                       if (previousTextStartTime !== text.start) {
  //                         previousTextStartTime = text.start;
  //                         if (text.transcript !== "") {
  //                           finalText.push(
  //                             text.speaker === 0
  //                               ? speakerOneName + " : " + text.transcript
  //                               : speakerTwoName + " : " + text.transcript
  //                           );
  //                         }
  //                       }
  //                     }
  //                   }
  //                   oneOfTranscriptSuccess = true;
  //                 }
  //               } else {
  //                 continue;
  //               }
  //             } catch (error) {
  //               continue;
  //             }
  //           } else {
  //             continue;
  //           }
  //         }

  //         if (oneOfTranscriptSuccess) {
  //           await Transcribe.findByIdAndUpdate(
  //             previousTranscribeId,
  //             {
  //               transCribeInProcess: false,
  //               transcriptText: finalText,
  //               videoUrl: "",
  //             },
  //             { new: true }
  //           );
  //           await Meeting.findByIdAndUpdate(meeting._id, {
  //             transcriptTextInProcess: false,
  //             transcriptTextCreated: true,
  //           });
  //         } else {
  //           const statusUpdated = await Meeting.findByIdAndUpdate(meeting._id, {
  //             transcriptTextInProcess: false,
  //           });
  //           return res.sendError("Transcribe from deepgram failed");
  //         }
  //         return res.sendSuccess("Transcribe success");
  //       } else {
  //         const statusUpdated = await Meeting.findByIdAndUpdate(meeting._id, {
  //           transcriptTextInProcess: false,
  //         });
  //         return res.sendError("No recordings found");
  //       }
  //     } catch (error) {
  //       const statusUpdated = await Meeting.findByIdAndUpdate(meeting._id, {
  //         transcriptTextInProcess: false,
  //       });
  //       return res.sendError("Something went wrong" + error);
  //     }
  //   } catch (error) {
  //     return res.sendError("Something went wrong" + error);
  //   }
  // }
  // async function transcribeLocalVideoWithStream(filePath: any) {
  //   try {
  //     if (fs.existsSync(filePath)) {
  //       const audioFile = {
  //         stream: fs.createReadStream(filePath),
  //         mimetype: ".M4A",
  //       };
  //       const response = await deepgram.transcription.preRecorded(audioFile, {
  //         punctuation: true,
  //         utterances: true,
  //         ner: true,
  //         diarize: true,
  //       });
  //       return response.results;
  //     } else {
  //       throw new Error("File not exist for transcribe");
  //     }
  //   } catch (error) {
  //     throw new Error(error);
  //   }
  // }

  export async function meetingBothJoinedTime(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const meetingId = req.body.meetingId;
    if (meetingId) {
      try {
        const meetingDetails = await VideoCallDao.getEndMeetingByMeetingId(
          meetingId
        );

        return res.sendSuccess(meetingDetails, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid meetingId");
    }
  }

  export async function createTranscribeNew(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      const meetingId = req.body.meetingId;
      const conversation = req.body.conversation;

      const meeting = await Meeting.findById(meetingId);

      if (meeting == null) {
        return res.sendError("Wrong meeting id");
      }

      const transcribeData: DTranscribe = {
        clientId: meeting.clientId,
        meetingId: meeting.meetingId,
        therapistId: meeting.therapistId,
        transcriptText: conversation,
        videoUrl: "",
        transCribeInProcess: false,
        meetingStartedTime: meeting.createdAt,
        speakersDetected: false,
        speakersArray: [meeting.clientId, meeting.therapistId],
      };
      const newTranscribe = await VideoCallDao.createTranscribe(transcribeData);

      return res.sendSuccess(newTranscribe, "Success");
    } catch (error) { }
  }
}
