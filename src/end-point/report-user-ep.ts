var mongoose = require("mongoose");
import { NextFunction, Request, Response } from "express";
import { check, validationResult } from "express-validator";
import { ReportDao } from "../dao/report-user-dao";
import { UserDao } from "../dao/user-dao";
import {  DReport, ReviewStatus } from "../models/report-user-model";
import { UserRole } from "../models/user-model";

export namespace ReportEp {
  export function reportUserValidationRules() {
    return [
      check("userId")
        .not()
        .isEmpty()
        .withMessage("User Id cannot be empty.")
        .isString()
        .withMessage("User Id can only be a string."),
      check("reason")
        .not()
        .isEmpty()
        .withMessage("Reason cannot be empty.")
        .isString()
        .withMessage("Reason can only be a string."),
    ];
  }
  export async function reportUser(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const reportedBy = req.user._id;
    const role = req.user.role;
    const reportedUser = req.body.userId;
    const reportedReason = req.body.reason;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!mongoose.Types.ObjectId.isValid(reportedUser)) {
      return res.sendError("Invalid object Id");
    } else {
      try {
        let user = await UserDao.getUserByUserId(reportedUser);

        if (!user) {
          return res.sendError("A user does not exist for the provided Id.");
        }

        if (user.role === role) {
          return res.sendError(`You cannot report a ${user.role}`);
        }

        if (role == UserRole.THERAPIST || role == UserRole.CLIENT) {
          const reportDetails: DReport = {
            reported: reportedUser,
            reportedBy: reportedBy,
            reason: reportedReason,
            status: ReviewStatus.PENDING
          };
          try {
            let reported = await ReportDao.reportUser(reportDetails);

            if (!reported) {
              return res.sendError(
                "User could not be reported. Please try again later."
              );
            }
            return res.sendSuccess(reported, "Success");
          } catch (error) {
            return res.sendError(error);
          }
        }
      } catch (error) {
        return res.sendError(error);
      }
    }
  }
}
