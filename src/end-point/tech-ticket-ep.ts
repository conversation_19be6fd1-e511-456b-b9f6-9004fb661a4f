import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import multer = require("multer");
import path = require("path");
import { UploadDao } from "../dao/upload-dao";
import { DUpload } from "../models/upload-model";
import { UploadCategory } from "./user-ep";
import { DTechTicket } from "../models/tech-ticket-model";
import { TechTicketDao } from "../dao/tech-ticket-dao";
import { UserRole } from "../models/user-model";
import { UserDao } from "../dao/user-dao";
let fs = require("fs");

export namespace TechTicketEp {
  export async function createTechTicket( req: Request, res: Response, next: NextFunction ) {
    const userId = req.user._id;
    let destination1 = `${process.env.UPLOAD_PATH}/${UploadCategory.TECH_TICKET_IMAGE}`;

    fs.access(destination1, (error: any) => {
      if (error) {
        return fs.mkdir(destination1, (error: any) => {
          return true;
        });
      }
    });

    const storage = multer.diskStorage({
      destination: async (req, file, cb) => {
        await techTicketValidationRules(req, file, cb);
      },
    });

    async function techTicketValidationRules(req: any, file: any, cb: any) {
      try {
        let techSupportDetails = JSON.parse(req.body.techSupportDetails);

        if (
          !techSupportDetails.title ||
          typeof techSupportDetails.title !== "string"
        ) {
          return cb(Error("Tech ticket title is required."));
        }

        if (
          !techSupportDetails.message ||
          typeof techSupportDetails.message !== "string"
        ) {
          return cb(Error("Tech ticket message is required."));
        }

        if (file.fieldname === "techTicketFile") {
          cb(null, destination1);
        }
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    const upload = multer({
      storage: storage,
    }).fields([{ name: "techTicketFile", maxCount: 1 }]);

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }

        const fileType = req.body.fileType;
        let uploadedTechTicketFile;

        const upload: any = (req.files as any).techTicketFile[0];

        if (upload) {
          if(fileType != "IMAGE") {
            return res.sendError("Invalid file type. You can upload only images!");
          }

          const techTicketFile: DUpload = {
            userId: userId as unknown as Types.ObjectId,
            originalName: upload.originalname.replace(/ /g, ""),
            name: upload.filename,
            type: upload.mimetype,
            path: upload.path,
            fileSize: upload.size,
            extension: path.extname(upload.originalname),
            category: UploadCategory.TECH_TICKET_IMAGE,
            isUrl: true
          };

          uploadedTechTicketFile = await UploadDao.createUpload(techTicketFile);
        } else {
          return res.sendError("Please upload issue file");
        }

        let requestBody: any;

        try {
          requestBody = JSON.parse(req.body.techSupportDetails);
        } catch (error) {
          return res.sendError("Submitted invalid tech ticket details.");
        }

        let techTicket: DTechTicket;

        techTicket = {
          createdBy: userId,
          title: requestBody.title,
          message: requestBody.message,
          isRead: false,
          uploadId: uploadedTechTicketFile._id
        };

        try {
          let savedTechTicket = await TechTicketDao.createTechTicket(techTicket);

          if (!savedTechTicket) {
            return res.sendError("Error while saving tech ticket details.");
          }

          return res.sendSuccess(savedTechTicket, "Tech support request is created & you will be contacted via email!");
        } catch (error) {
          return res.sendError(error);
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllTechTickets(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    if(req.user.role == UserRole.SUB_ADMIN){
      const ownUser = await UserDao.getUserByUserId(req.user._id);
      if(ownUser.adminPermission.techTickets != true){
        return res.sendError(
          "You don't have permission for Tech Tickets!"
        );
      }
    }

    try {
      const techTicketsList = await TechTicketDao.getAllTechTickets(
        limit,
        offset
      );

      let techTicketUnreadCount = await TechTicketDao.getAllTechTicketUnreadCount();

      const techTicketsData: any = {
        techTicketsList: techTicketsList,
        count: techTicketUnreadCount,
      };

      return res.sendSuccess(techTicketsData, "Successfully Retrived.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateTechTicketStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      let techTicket = await TechTicketDao.updateTechTicket(
        Types.ObjectId(req.params.techTicketId),
        { isRead: true }
      );

      return res.sendSuccess(techTicket, "Successfully Updated.");
    } catch (error) {
        return res.sendError(error);
    }
  }
}
