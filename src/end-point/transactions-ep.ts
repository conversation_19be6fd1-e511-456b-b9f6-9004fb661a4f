import { NextFunction, Request, Response } from "express";
import { check, validationResult } from "express-validator";
import { AppLogger } from "../common/logging";
import { AdminDao } from "../dao/admin-dao";
import { NotificationDao } from "../dao/notification-dao";
import { TherapistDao } from "../dao/therapist-dao";
import { Types } from "mongoose";
import { TransactionsDao } from "../dao/transactions-dao";
import Therapist from "../schemas/therapist-schema";
import TherapistReferral from '../schemas/therapist-referral-schema';
import Transaction from "../schemas/transaction-schema";
import { WithdrawalDao } from "../dao/withdrawal-dao";
import { EmailService } from "../mail/config";
import { DNotification, NotificationEvent } from "../models/notification-model";
import { DTransaction, TransactionType, ITransaction } from "../models/transaction-model";
import { UserRole } from "../models/user-model";
import { SMSService } from "../sms/config";
import { UserDao } from "../dao/user-dao";
import { CallingStatus, DMeeting } from "../models/meeting-model";
import { VideoCallDao } from "../dao/videocall-dao";
import { Util, StringOrObjectId } from "../common/util";
import { DTreatmentHistory } from "../models/treatment-history-model";
import { DDiagnosisNote } from "../models/diagnosis-note-model";
import { UploadDao } from "../dao/upload-dao";
import { AppointmentDao } from "../dao/appointment-dao";
import Meeting from "../schemas/meeting-schema";
import ReferralEarning from "../schemas/referral-earning-schema";
import { RewardType } from "../models/referral-earning-model";
import TreatmentHistory from "../schemas/treatment-history-scheama";
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

export namespace TransactionsEp {
  export async function getRecentBalance(req: Request, res: Response) {
    const therapistId = req.user._id;
    const role = req.user.role;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (role == UserRole.THERAPIST) {
      try {
        let recentBalance = await TransactionsDao.getRecentBalance(therapistId);

        if (!recentBalance) {
          return res.sendError("Unable to get recent balance.");
        }

        return res.sendSuccess(recentBalance, "Recent balance of Therapist.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllTransactions(
    req: Request, 
    res: Response, 
    next: NextFunction) {
    const therapistId = req.user._id;
    const role = req.user.role;
    const limit = req.query.limit? parseInt(req.query.limit as string): undefined;
    const offset = req.query.offset? parseInt(req.query.offset as string): undefined;
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (role == UserRole.THERAPIST) {
      try {

        let recentBalance;
        if(limit && offset){
          recentBalance = await TransactionsDao.getAllTransactionsTherapist(
            therapistId,
            limit,
            offset
          );
        }else{
           recentBalance = await TransactionsDao.getAllTransactionsTherapist(therapistId);
        }

        if (!recentBalance) {
          return res.sendError("Unable to get recent balance.");
        }

        return res.sendSuccess(recentBalance, "Recent balance of Therapist.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllTransactionsByTherapistId(req: Request, res: Response, next: NextFunction) {
    const therapistId = req.params.tId;
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (role == UserRole.ADMIN || UserRole.SUPER_ADMIN || UserRole.SUB_ADMIN) {
      try {
        let recentBalance = await TransactionsDao.getAllTransactionsAdmin(therapistId, limit, offset);

        if (!recentBalance) {
          return res.sendError("Unable to get recent balance.");
        }

        return res.sendSuccess(recentBalance, "Recent balance of Therapist.");

      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export function createTransactionValidationRules() {
    return [
      check("type").isString().not().isEmpty().withMessage("Transaction type is required."),
      check("transactionAmount").isNumeric().not().isEmpty().withMessage("Transaction amount is required."),
    ];
  }

  export async function createStripeConnectedAccount(req: Request, res: Response, next: NextFunction) {
    const therapistId = req.user._id;
    const therapistEmail = req.user.email;
    const role = req.user.role;

    if (role == UserRole.THERAPIST) {
      try {
        const therapist = await TherapistDao.getUserById(therapistId);

        const account = await stripe.accounts.create({
          type: "express",
          email: therapistEmail,
        });

        therapist.stripeConnectedAccountId = account.id;

        await therapist.save();

        const accountLink = await stripe.accountLinks.create({
          account: account.id,
          refresh_url: process.env.APP_URL + "/earnings",
          return_url: process.env.APP_URL + "/earnings/stripe-connected",
          type: "account_onboarding",
        });

        return res.sendSuccess(accountLink, "New stripe connected account is created.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function verifyStripeAccount(req: Request, res: Response, next: NextFunction) {
    const therapistId = req.user._id;
    const role = req.user.role;

    if (role == UserRole.THERAPIST) {
      try {
        const therapist = await TherapistDao.getUserById(therapistId);

        if (!therapist.stripeConnectedAccountId) {
          return res.sendError("No stripe connected account id is generated yet.");
        }

        const account = await stripe.accounts.retrieve(therapist.stripeConnectedAccountId);

        if (account.charges_enabled && account.details_submitted) {
          therapist.stripeChargesEnabled = true;
          therapist.stripeDetailsSubmitted = true;

          await therapist.save();

          return res.sendSuccess(
            {
              enabledForPayments: true,
            },
            "Stripe enabled for payments."
          );
        } else {
          const accountLink = await stripe.accountLinks.create({
            account: account.id,
            refresh_url: process.env.APP_URL + "/earnings",
            return_url: process.env.APP_URL + "/earnings/stripe-connected",
            type: "account_onboarding",
          });

          return res.sendSuccess(
            {
              enabledForPayments: false,
              accountLink: accountLink,
            },
            "Stripe is not enabled for payments yet."
          );
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function createStripeLoginLink(req: Request, res: Response, next: NextFunction) {
    const therapistId = req.user._id;
    const role = req.user.role;

    if (role == UserRole.THERAPIST) {
      try {
        const therapist = await TherapistDao.getUserById(therapistId);

        if (!therapist.stripeConnectedAccountId) {
          return res.sendError("No stripe connected account id is generated yet!");
        }

        const loginLink = await stripe.accounts.createLoginLink(therapist.stripeConnectedAccountId);

        therapist.stripeConnectedLoginLink = loginLink.url;

        await therapist.save();

        return res.sendSuccess(loginLink.url, "New stripe login is created.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }



  export async function getAllAccumulatedBalance(req: Request, res: Response, next: NextFunction) {
    try {
      if(req.user.role == UserRole.SUB_ADMIN){
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if(ownUser.adminPermission.availableBalances != true && ownUser.adminPermission.adminDashboard != true && ownUser.adminPermission.statistics != true){
          return res.sendError(
            "You don't have permission!"
          );
        }
      }
      const therapists = await TherapistDao.getAllVerifiedTherapists();
      return res.sendSuccess(therapists, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllTotalRevenue(req: Request, res: Response, next: NextFunction) {
    try {
      if(req.user.role == UserRole.SUB_ADMIN){
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if(ownUser.adminPermission.adminDashboard != true && ownUser.adminPermission.statistics != true){
          return res.sendError(
            "You don't have permission!"
          );
        }
      }
      const therapists = await TherapistDao.getAllVerifiedTherapists();
      let totalAccumulatedWithdrawals = 0;
      therapists.map(async function (t) {
        const recentTransaction = t.toJSON().recentTransaction;
        if (recentTransaction !== null) {
          totalAccumulatedWithdrawals += recentTransaction.accumulatedWithdrawals;
        }
      });
      totalAccumulatedWithdrawals = parseFloat(totalAccumulatedWithdrawals.toFixed(1));
      return res.sendSuccess(totalAccumulatedWithdrawals, "statistics data.");
    } catch (error) {
      return res.sendError(error);
    }
  }


  export async function declareMonthlyAllAccumulatedBalance(req: Request, res: Response, next: NextFunction) {
    try {
      const paymentsData = await TherapistDao.getAllMonthlyPaynemts();
      if (paymentsData.length !== 0) {
        const data = {
          verifiedStatus: "DECLINED"
        }
        paymentsData.map(async function (t) {
          const therapistsPayment = await TherapistDao.updateMonthlyPaynemt(t?._id, data);
          return res.sendSuccess(therapistsPayment, "Success");
        })
      }

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteMonthlyAllAccumulatedRecord(req: Request, res: Response, next: NextFunction) {
    const amountRecordId = req.params.amountRecordId;
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        let monthlyPaynemts: any = await TherapistDao.getMonthlyPaynemtsById(amountRecordId);

        if (monthlyPaynemts == null) {
          return res.sendError("No monthly paynemts for the Id.");
        }

        try {
          let deletedMonthlyPaynemts = await TherapistDao.deleteMonthlyPaynemtsById(amountRecordId);

          return res.sendSuccess(deletedMonthlyPaynemts, "Monthly paynemt record deleted.");
        } catch (error) {
          return res.sendError(error);
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  // crownjob run 1st of monthly
  export async function setMonthlyAllAccumulatedBalanceCrownjobIn1stOfMonth() {
    try {
      AppLogger.info(`.:: Monthly Accumulated Balance Process Started : ` + new Date() + " ::.");
      const paymentsData = await TherapistDao.getAllMonthlyPaynemts();
      if (paymentsData.length !== 0) {
        const data = {
          verifiedStatus: "DECLINED"
        }
        paymentsData.map(async function (t) {
          const therapistsPayment = await TherapistDao.updateMonthlyPaynemt(t?._id, data);
          if (therapistsPayment) {
            const therapists = await TherapistDao.saveAllVerifiedTherapists1stOfMonth();
            if (therapists) {
              AppLogger.info(`Monthly Accumulated Balance Added. Date: ` + new Date());
            }
          }
        })
      } else {
        const therapists = await TherapistDao.saveAllVerifiedTherapists1stOfMonth();

        if (therapists) {
          AppLogger.info(`Monthly Accumulated Balance Added. Date: ` + new Date());
        }
      }

    } catch (error) {
      AppLogger.error(`Error - Accumulated Balance initiate Error. Date: ` + new Date());
    }
  }

  export async function setMonthlyAllAccumulatedBalanceCrownjobIn1stOfMonthByAdmin(req: Request, res: Response, next: NextFunction) {
    try {
      AppLogger.info(`.:: Monthly Accumulated Balance Process Started : ` + new Date() + " ::.");
      const paymentsData = await TherapistDao.getAllMonthlyPaynemts();

      if (paymentsData.length !== 0) {
        const data = {
          verifiedStatus: "DECLINED"
        }
        paymentsData.map(async function (t) {
          const therapistsPayment = await TherapistDao.updateMonthlyPaynemt(t?._id, data);
          if (therapistsPayment) {
            const therapists = await TherapistDao.saveAllVerifiedTherapists1stOfMonthAdmin();
            if (therapists) {
              return res.sendSuccess(therapists, "Success");
            }
          }
        })
      } else {
        const therapists = await TherapistDao.saveAllVerifiedTherapists1stOfMonthAdmin();
        if (therapists) {
          return res.sendSuccess(therapists, "Success");
        }
      }

    } catch (error) {
      return res.sendError(error);
    }
  }

  // crownjob run 15th of monthly
  export async function setMonthlyAllAccumulatedBalanceCrownjobIn15thOfMonth() {
    try {
      AppLogger.info(`.:: Monthly Accumulated Balance Process Started : ` + new Date() + " ::.");
      const paymentsData = await TherapistDao.getAllMonthlyPaynemts();
      if (paymentsData.length !== 0) {
        const data = {
          verifiedStatus: "DECLINED"
        }
        paymentsData.map(async function (t) {
          const therapistsPayment = await TherapistDao.updateMonthlyPaynemt(t?._id, data);
          if (therapistsPayment) {
            const therapists = await TherapistDao.getAllMonthlyPaynemtsLastMonth1stAnd15th();
            if (therapists) {
              AppLogger.info(`Monthly Accumulated Balance Added. Date: ` + new Date());
            }
          }
        })
      } else {
        const therapists = await TherapistDao.getAllMonthlyPaynemtsLastMonth1stAnd15th();
        if (therapists) {
          AppLogger.info(`Monthly Accumulated Balance Added. Date: ` + new Date());
        }
      }

    } catch (error) {
      AppLogger.error(`Error - Accumulated Balance initiate Error. Date: ` + new Date());
    }
  }

  export async function setMonthlyAllAccumulatedBalanceCrownjobIn15thOfMonthByAdmin(req: Request, res: Response, next: NextFunction) {
    try {
      AppLogger.info(`.:: Monthly Accumulated Balance Process Started : ` + new Date() + " ::.");
      const paymentsData = await TherapistDao.getAllMonthlyPaynemts();

      if (paymentsData.length !== 0) {
        const data = {
          verifiedStatus: "DECLINED"
        }
        paymentsData.map(async function (t) {
          const therapistsPayment = await TherapistDao.updateMonthlyPaynemt(t?._id, data);
          if (therapistsPayment) {
            const therapists = await TherapistDao.getAllMonthlyPaynemtsLastMonth1stAnd15th();
            if (therapists) {
              return res.sendSuccess(therapists, "Success");
            }
          }
        })
      } else {
        const therapists = await TherapistDao.getAllMonthlyPaynemtsLastMonth1stAnd15th();
        if (therapists) {
          return res.sendSuccess(therapists, "Success");
        }
      }

    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getMonthlyAllAccumulatedBalance(req: Request, res: Response, next: NextFunction) {
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    try {
      if(req.user.role == UserRole.SUB_ADMIN){
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if(ownUser.adminPermission.adminApprovePayment != true){
          return res.sendError(
            "You don't have permission for Admin Approval!"
          );
        }
      }
      const therapists = await TherapistDao.getAllMonthlyPaynemtsWithoutFilter(limit,offset);
      return res.sendSuccess(therapists, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function payMonthlyAllAccumulatedBalance(req: Request, res: Response, next: NextFunction) {
    AppLogger.info(`.:: Monthly withdrawal Process Started : ` + new Date() + " ::.");
    const paymentId = req.body.paymentId;
    const verifiedStatus = req.body.verifiedStatus;
    try {
      const systemAdmin = (await AdminDao.getAllAdmins())[0];
      const therapists = await TherapistDao.getAllMonthlyPaynemtsById(paymentId);
      const data = {
        verifiedStatus: verifiedStatus
      }
      if (therapists?.verifiedStatus == "PENDING") {
        therapists.therapistTransaction.map(async function (t) {
          const recentTransaction = t?.recentTransaction;

          if (recentTransaction != null) {
            const totalPayment = await t?.recentTransaction?.reduce((total: any, transaction: any) => {
              const earnings = transaction?.transactionAmount || 0;
              return total + earnings;
            }, 0);
            if (t?.stripeConnectedAccountId && totalPayment > 0) {
              const transfer = await stripe.transfers.create({
                amount: Math.round(totalPayment * 100),
                currency: "usd",
                destination: t?.stripeConnectedAccountId,
                description: "Lavni transfers Initiated To " + t?.firstname + " " + t?.lastname,
                transfer_group: "ORDER_BALANCE_" + totalPayment,
              });

              let withdrawalTransactionCreated = await TransactionsDao.withdrawTransaction(
                t?._id,
                totalPayment.toString(),
                TransactionType.WITHDRAWAL
              );

              if (withdrawalTransactionCreated) {
                await WithdrawalDao.createWithdrawal({
                  therapistId: Types.ObjectId(t?._id),
                  withdrawnAmount: totalPayment,
                });

                const notificationDetails: DNotification = {
                  senderId: systemAdmin._id,
                  receiverId: Types.ObjectId(t?._id),
                  event: NotificationEvent.MONTHLY_WITHDRAWAL,
                  link: process.env.APP_URL + "/earnings",
                  content: "Your monthly withdrawal initiated. Date: " + new Date(),
                  variant: "info",
                  readStatus: false,
                };

                await NotificationDao.createNotification(notificationDetails);

                await EmailService.monthlyWithdrawalInitiated("Your monthly withdrawal initiated.", t.email, t.firstname);

                await SMSService.sendEventSMS(
                  `Hi ${t.firstname}! Your monthly withdrawal initiated. Login to the dashboard to check more details.`,
                  t.primaryPhone
                );
                // start bank transfer
                const externalAccounts = await stripe.accounts.listExternalAccounts(
                  t?.stripeConnectedAccountId,
                  { object: 'bank_account' }
                );

                if (externalAccounts && externalAccounts.data[0] && externalAccounts.data[0].id) {
                  const bankAccountID = externalAccounts.data[0].id;

                  const payout = await stripe.payouts.create({
                    amount: Math.round(totalPayment * 100),
                    currency: 'usd',
                    destination: bankAccountID,
                  }, {
                    stripeAccount: t?.stripeConnectedAccountId,
                  });
                }

                // end bank transfer
                const data = {
                  paidStatus: "paid"
                }
                const updatedMonthlyPayment = await TherapistDao.updateMonthlyPaymentPaidStatus(paymentId, Types.ObjectId(t._id), data);
                AppLogger.info(`Monthly withdrawal initiated. Date: ` + new Date());

              } else {
                AppLogger.error(`Error - Monthly withdrawal initiate Error. Date: ` + new Date());
                return res.sendError(`Error - Monthly withdrawal initiate Error. Date: ` + new Date());
              }
            }
            t?.recentTransaction.map(async function (transaction: any) {
              const transactionData = {
                paidStatus: "PAID",
              }

              await TransactionsDao.updateTransaction(transaction._id, transactionData)
            });
          }
        });
        const therapistsPayment = await TherapistDao.updateMonthlyPaynemt(paymentId, data);
        const updatedMonthlyPay = await TherapistDao.getAllMonthlyPaynemtsById(paymentId);
        return res.sendSuccess(updatedMonthlyPay, "Monthly withdrawal initiated.");
      } else {
        return res.sendError(`Error - Monthly withdrawal initiate Error. Date: ` + new Date());
      }


    } catch (error) {
      return res.sendError(error);
    }
  }

  // Transefer Ammount Admin

  export async function transeferAmmount(req: Request, res: Response, next: NextFunction) {
    try {
      const therapistId = req.body.userId;
      const accumulatedBalance = req.body.accumulatedBalance;
      if (!therapistId) {
        return res.sendError("No userId given.");
      }

      const therapist = await UserDao.getTherapistById(therapistId);
      if (therapist.stripeConnectedAccountId && accumulatedBalance > 0) {
        await stripe.transfers.create({
          amount: Math.round(accumulatedBalance * 100),
          currency: "usd",
          destination: therapist.stripeConnectedAccountId,
          description: "Lavni transfers Initiated To " + therapist.firstname + " " + therapist.lastname,
          transfer_group: "ORDER_BALANCE_" + accumulatedBalance,
        });

        let withdrawalTransactionCreated = await TransactionsDao.withdrawTransaction(
          therapist._id,
          accumulatedBalance,
          TransactionType.TRANSFER
        );

        if (withdrawalTransactionCreated) {
          const data = await WithdrawalDao.createWithdrawal({
            therapistId: therapist._id,
            withdrawnAmount: accumulatedBalance,
          });

          const notificationDetails: DNotification = {
            senderId: req.body._id,
            receiverId: therapist._id,
            event: NotificationEvent.TRANSFER,
            link: process.env.APP_URL + "/earnings",
            content: "Your monthly transfers initiated. Date: " + new Date(),
            variant: "info",
            readStatus: false,
          };

          await NotificationDao.createNotification(notificationDetails);

          await EmailService.monthlyWithdrawalInitiated("Your monthly transfers initiated.", therapist.email, therapist.firstname);

          await SMSService.sendEventSMS(
            `Hi ${therapist.firstname}! Your monthly transfers are initiated. Login to the dashboard to check more details.`,
            therapist.primaryPhone
          );

          return res.sendSuccess(data, `Payment of $${Math.round(accumulatedBalance * 100) / 100} successfully transferred to this Therapist.`);

        }
      } else {
        return res.sendError("This therapist does not have a connected Stripe account.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  // Transfer Ammount Admin By Session

  export async function transeferAmmountBySession(req: Request, res: Response, next: NextFunction) {
    try {
      const therapistId = req.body.userId;
      const transactionId = req.body.transactionId
      const transferAmount = req.body.amount
      // const accumulatedBalance = req.body.accumulatedBalance;
      if (!therapistId) {
        return res.sendError("No userId given.");
      }
      if (!transactionId) {
        return res.sendError("No transactionId given.");
      }
      if (!transferAmount) {
        return res.sendError("No amount given.");
      }
      const therapist = await UserDao.getTherapistById(therapistId);
      const transaction = await VideoCallDao.getTransactionById(transactionId, therapistId);
      if (!transaction) {
        return res.sendError("No transactionId given.");
      }
      if (transferAmount > transaction.transactionAmount) {
        return res.sendError("Transfer amount is less than or equal session amount.");
      }
      // const transactionData = {
      //   paidStatus: "PAID",
      // }
      // await TransactionsDao.updateTransaction(transaction._id,transactionData)
      if (therapist.stripeConnectedAccountId) {
        if (transferAmount > 0) {
          await stripe.transfers.create({
            amount: Math.round(transferAmount * 100),
            currency: "usd",
            destination: therapist.stripeConnectedAccountId,
            description: "Lavni transfers Initiated To " + therapist.firstname + " " + therapist.lastname,
            transfer_group: "ORDER_BALANCE_" + transaction.transactionAmount,
          });

          let withdrawalTransactionCreated = await TransactionsDao.withdrawTransaction(
            therapist._id,
            transferAmount.toString(),
            TransactionType.TRANSFER
          );

          if (withdrawalTransactionCreated) {
            const data = await WithdrawalDao.createWithdrawal({
              therapistId: therapist._id,
              withdrawnAmount: transferAmount,
            });

            const notificationDetails: DNotification = {
              senderId: req.body._id,
              receiverId: therapist._id,
              event: NotificationEvent.TRANSFER,
              link: process.env.APP_URL + "/earnings",
              content: "Your monthly transfers initiated. Date: " + new Date(),
              variant: "info",
              readStatus: false,
            };
            if (transferAmount == transaction.transactionAmount) {
              const transactionData = {
                paidStatus: "PAID",
              }
              await TransactionsDao.updateTransaction(transaction._id, transactionData)
            } else {
              const transactionData = {
                transactionAmount: transaction.transactionAmount - transferAmount,
              }
              await TransactionsDao.updateTransaction(transaction._id, transactionData)
            }

            await NotificationDao.createNotification(notificationDetails);

            await EmailService.monthlyWithdrawalInitiated("Your monthly transfers initiated.", therapist.email, therapist.firstname);

            await SMSService.sendEventSMS(
              `Hi ${therapist.firstname}! Your monthly transfers are initiated. Login to the dashboard to check more details.`,
              therapist.primaryPhone
            );
            // start bank transfer
            const externalAccounts = await stripe.accounts.listExternalAccounts(
              therapist.stripeConnectedAccountId,
              { object: 'bank_account' }
            );

            if (externalAccounts && externalAccounts.data[0] && externalAccounts.data[0].id) {
              const bankAccountID = externalAccounts.data[0].id;

              const payout = await stripe.payouts.create({
                amount: Math.round(transferAmount * 100),
                currency: 'usd',
                destination: bankAccountID,
              }, {
                stripeAccount: therapist.stripeConnectedAccountId,
              });
            }

            // end bank transfer

            return res.sendSuccess(data, `Payment of $${Math.round(transferAmount * 100) / 100} successfully transferred to this Therapist.`);

          }
        } else {
          return res.sendError("This session amount is 0.");
        }
      } else {
        return res.sendError("This therapist does not have a connected Stripe account.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }
  // Update Ammount Admin
  export async function markAsPaidTranseferAmmountBySession(req: Request, res: Response, next: NextFunction) {
    try {
      const therapistId = req.body.userId;
      const transactionId = req.body.transactionId

      if (!therapistId) {
        return res.sendError("No userId given.");
      }
      if (!transactionId) {
        return res.sendError("No transactionId given.");
      }
      const therapist = await UserDao.getTherapistById(therapistId);
      const transaction = await VideoCallDao.getTransactionById(transactionId, therapistId);
      if (!transaction) {
        return res.sendError("No transactionId given.");
      }
      // const transactionData = {
      //   paidStatus: "PAID",
      // }
      // await TransactionsDao.updateTransaction(transaction._id,transactionData)
      if (transaction.transactionAmount > 0) {

        let withdrawalTransactionCreated = await TransactionsDao.withdrawTransaction(
          therapist._id,
          transaction.transactionAmount.toString(),
          TransactionType.TRANSFER
        );

        if (withdrawalTransactionCreated) {
          const data = await WithdrawalDao.createWithdrawal({
            therapistId: therapist._id,
            withdrawnAmount: transaction.transactionAmount,
          });

          const transactionData = {
            paidStatus: "PAID",
          }
          await TransactionsDao.updateTransaction(transaction._id, transactionData)

          return res.sendSuccess(data, `Payment of $${Math.round(transaction.transactionAmount * 100) / 100} successfully transferred to this Therapist.`);

        }
      } else {
        return res.sendError("This session amount is 0.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function markAsPaidAmmount(req: Request, res: Response, next: NextFunction) {
    try {
      const therapistId = req.body.userId;
      const accumulatedBalance = req.body.accumulatedBalance;
      if (!therapistId) {
        return res.sendError("No userId given.");
      }

      const therapist = await UserDao.getTherapistById(therapistId);
      if (therapist.stripeConnectedAccountId && accumulatedBalance > 0) {
        let withdrawalTransactionCreated = await TransactionsDao.withdrawTransaction(
          therapist._id,
          accumulatedBalance,
          TransactionType.TRANSFER
        );

        if (withdrawalTransactionCreated) {
          const data = await WithdrawalDao.createWithdrawal({
            therapistId: therapist._id,
            withdrawnAmount: accumulatedBalance,
          });

          const notificationDetails: DNotification = {
            senderId: req.body._id,
            receiverId: therapist._id,
            event: NotificationEvent.TRANSFER,
            link: process.env.APP_URL + "/earnings",
            content: "Your monthly transfers initiated. Date: " + new Date(),
            variant: "info",
            readStatus: false,
          };

          await NotificationDao.createNotification(notificationDetails);

          await EmailService.monthlyWithdrawalInitiated("Your monthly transfers are initiated.", therapist.email, therapist.firstname);

          await SMSService.sendEventSMS(
            `Hi ${therapist.firstname}! Your monthly transfers are initiated. Login to the dashboard to check more details.`,
            therapist.primaryPhone
          );

          return res.sendSuccess(data, `Payment of  $${accumulatedBalance} mark as paid for this Therapist.`);

        }
      } else {
        return res.sendError("This therapist does not have a connected Stripe account.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }


  export async function getAllTotalEarnings(_id: any, userRole: any,) {
    const userId = _id;
    const role = userRole;
    if (
      role == UserRole.THERAPIST
    ) {
      try {
        const user = await UserDao.getUserById(userId);
        if (user) {
          const stat =
            await AppointmentDao.getAllTherapistRevanuMonthlyStats(
              user,
            );
          return stat;
        } else {
          const error3 = "User not found"
          return error3;
        }
      } catch (error) {
        return error;
      }
    } else {
      const error3 = "Invalid user role."
      return error3;
    }
  }

  export async function getAllEarnings(_id: any, userRole: any, filterValue: any) {
    const userId = _id;
    const role = userRole;
    if (
      role == UserRole.THERAPIST
    ) {
      try {
        const user = await UserDao.getUserById(userId);

        if (user) {
          const stat =
            await AppointmentDao.getAllTherapistRevanuStats(
              user,
              filterValue
            );

          if (stat.length === 0) {
            const error3 = "Something went wrong! Could not load appointment statistics"
            return error3;
          }

          return stat;
        } else {
          const error3 = "User not found"
          return error3;
        }
      } catch (error) {
        return error;
      }
    } else {
      const error3 = "Invalid user role."
      return error3;
    }
  }

  export async function getAllEarningsTherapist(req: Request, res: Response, next: NextFunction) {

    const role = req.user.role;
    const userId = req.user._id;
    const filterValue = req.body.filterValue;
    if (
      role == UserRole.THERAPIST ||
      role == UserRole.CLIENT
    ) {
      try {
        const user = await UserDao.getUserById(userId);

        if (user) {
          const stat =
            await AppointmentDao.getAllTherapistRevanuStats(
              user,
              filterValue
            );
          return res.sendSuccess(stat, "Earnings data.");
        } else {
          return res.sendError("Invalid user role.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  // Transfer Ammount Admin By Selected payments

  export async function transeferAmmountForSelectedTherapist(req: Request, res: Response, next: NextFunction) {
    try {
      const therapistId = req.body.therapistId;
      const transactionIds = req.body.transactionIds
      const monthlyPaymentId = req.body.monthlyPaymentId

      if (!therapistId) {
        return res.sendError("Invalid therapist id.");
      }
      if (!monthlyPaymentId) {
        return res.sendError("Invalid monthly payment id.");
      }
      if (!transactionIds || transactionIds?.length <= 0) {
        return res.sendError("Transaction ids required.");
      }

      if (transactionIds && transactionIds?.length > 0) {
        let totalAmount: any = 0;
        const amountsPromise = await Promise.all(transactionIds?.map(async (transaction: DTransaction) => {
          const transactionData = await VideoCallDao.getTransactionById(transaction, therapistId);
          return transactionData.transactionAmount;
        }));
        const amounts = await Promise.all(amountsPromise);

        if (amounts) {
          totalAmount = amounts.reduce((acc, amount) => acc + amount, 0);
        }

        const therapist = await UserDao.getTherapistById(therapistId);
        if (therapist.stripeConnectedAccountId) {
          if (totalAmount > 0) {
            try {
              const paidData = await stripe.transfers.create({
                amount: Math.round(totalAmount * 100),
                currency: "usd",
                destination: therapist.stripeConnectedAccountId,
                description: "Lavni transfers Initiated To " + therapist.firstname + " " + therapist.lastname,
                transfer_group: "ORDER_BALANCE_" + totalAmount,
              });
              if (paidData) {
                let withdrawalTransactionCreated = await TransactionsDao.withdrawTransaction(
                  therapist._id,
                  totalAmount.toString(),
                  TransactionType.TRANSFER
                );

                if (withdrawalTransactionCreated) {
                  const data = await WithdrawalDao.createWithdrawal({
                    therapistId: therapist._id,
                    withdrawnAmount: totalAmount,
                  });

                  const notificationDetails: DNotification = {
                    senderId: req.body._id,
                    receiverId: therapist._id,
                    event: NotificationEvent.TRANSFER,
                    link: process.env.APP_URL + "/earnings",
                    content: "Your monthly transfers initiated. Date: " + new Date(),
                    variant: "info",
                    readStatus: false,
                  };

                  await NotificationDao.createNotification(notificationDetails);

                  await EmailService.monthlyWithdrawalInitiated("Your monthly transfers initiated.", therapist.email, therapist.firstname);

                  await SMSService.sendEventSMS(
                    `Hi ${therapist.firstname}! Your monthly transfers are initiated. Login to the dashboard to check more details.`,
                    therapist.primaryPhone
                  );

                  // start bank transfer
                  const externalAccounts = await stripe.accounts.listExternalAccounts(
                    therapist.stripeConnectedAccountId,
                    { object: 'bank_account' }
                  );

                  if (externalAccounts && externalAccounts.data[0] && externalAccounts.data[0].id) {
                    const bankAccountID = externalAccounts.data[0].id;

                    const payout = await stripe.payouts.create({
                      amount: Math.round(totalAmount * 100),
                      currency: 'usd',
                      destination: bankAccountID,
                    }, {
                      stripeAccount: therapist.stripeConnectedAccountId,
                    });
                  }

                  // end bank transfer
                }
                await TransactionsDao.updateTransactionList(transactionIds)

                const data = {
                  paidStatus: "paid"
                }
                const updatedMonthlyPayment = await TherapistDao.updateMonthlyPaymentPaidStatus(monthlyPaymentId, therapistId, data);
                return res.sendSuccess(updatedMonthlyPayment);
              } else {
                return res.sendError("Transaction failed.");
              }
            } catch (error) {
              return res.sendError(error);
            }
          } else {
            return res.sendError("Total amount is 0.");
          }
        } else {
          return res.sendError("No stripe account id.");
        }

      } else {
        return res.sendError("Transaction ids required.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getRecentMonthlyAllAccumulatedBalance(req: Request, res: Response, next: NextFunction) {
    try {
      const therapistId = req.user._id;
      if (!therapistId) {
        return res.sendError('Invalid therapist.');
      }
      const therapists = await TherapistDao.getRecentMonthlyPaynemtsWithoutFilter();
      const arr = [
        {
          document: {
            createdAt: "2024",
            therapistTransaction: [
              { email: "<EMAIL>", _id: "64abb726d0c48b3a2446a492" },
              { email: "<EMAIL>", _id: "54" },
            ]
          }
        },
        {
          document: {
            createdAt: "2024",
            therapistTransaction: [
              { email: "<EMAIL>", _id: "64abb726d0c48b3a2446a492" },
              { email: "<EMAIL>", _id: "54" },
            ]
          }
        }
      ];
      const filteredArr = therapists?.map(item => ({
        document: item.document && item.document.therapistTransaction ? {
          ...item.document,
          therapistTransaction: item.document.therapistTransaction.filter((transaction: any) => (transaction._id) == therapistId)
        } : {}
      }));

      const data = {
        filteredArr: filteredArr,
        therapists: therapists,
      }
      return res.sendSuccess(filteredArr, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

}
