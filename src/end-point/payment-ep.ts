import { Request, Response, NextFunction } from "express";
import { UserDao } from "../dao/user-dao";
import { AppLogger } from "../common/logging";
import { AdminDao } from "../dao/admin-dao";
import Meeting from "../schemas/meeting-schema";
import * as jwt from "jsonwebtoken";
import { JwtPayload } from "jsonwebtoken";
import { EmailService } from "../mail/config";
import { SMSService } from "../sms/config";
import { ClientDao } from "../dao/client-dao";

// Extend JwtPayload interface for application token
interface LavniJwtPayload extends JwtPayload {
  user_id: string;
}

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

export class PaymentEp {
  /**
   * Pay client copayment using their default payment method
   * @param req Request object with clientId and copayment amount
   * @param res Response object
   * @param next NextFunction
   */
  public static async payClientCopayment(req: Request, res: Response, next: NextFunction) {
    try {
      const { clientId, copayment, vonageSessionId } = req.body;

      // Validate input
      if (!clientId || !copayment || typeof copayment !== 'number' || copayment <= 0) {
        return res.sendError("Invalid request. Please provide valid clientId and copayment amount.");
      }

      // Verify authentication from JWT token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.sendError("Unauthorized. Missing or invalid token format.", 401);
      }

      const token = authHeader.split(' ')[1];
      
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-jwt-secret') as LavniJwtPayload;
        
        if (!decoded || typeof decoded !== 'object' || !decoded.user_id) {
          AppLogger.error('Invalid token payload structure');
          return res.sendError("Unauthorized. Invalid token structure.", 401);
        }
        
        const tokenClientId = decoded.user_id;
        
        // Compare clientId from token with clientId from request body
        if (tokenClientId !== clientId) {
          AppLogger.warn(`Token client ID (${tokenClientId}) does not match request client ID (${clientId})`);
          return res.sendError("Unauthorized. You can only pay for your own copayment.", 403);
        }
      } catch (jwtError) {
        AppLogger.error(`JWT validation error: ${jwtError.message}`);
        return res.sendError("Unauthorized. Invalid token.", 401);
      }

      // Get client information
      const client = await UserDao.getUserByUserId(clientId);
      if (!client) {
        return res.sendError("Client not found.");
      }

      // Check if the requesting user is the same as the client in the request
      if (!req.user || req.user._id.toString() !== clientId) {
        return res.sendError("Unauthorized. You can only pay for your own copayment.");
      }

      // If vonageSessionId exists, find meeting information and send notification to therapist
      let meeting, therapist;
      if (vonageSessionId) {
        meeting = await Meeting.findOne({ vonageSessionName: vonageSessionId });
        
        if (meeting && meeting.therapistId) {
          therapist = await UserDao.getUserByUserId(meeting.therapistId);
          
          if (therapist) {
            // Send notification to therapist about client processing payment
            const message = "The client is currently processing their outstanding co-payment and will be joining the session shortly.";
            
            // Send email
            await EmailService.sendEventReminderEmail(
              therapist,
              "Client Processing Co-payment",
              message
            );
            
            // Send SMS if phone number exists
            if (therapist.primaryPhone) {
              await SMSService.sendEventSMS(
                message,
                therapist.primaryPhone,
                "Payment-ep copayment notification"
              );
            }
            
            AppLogger.info(`Sent co-payment processing notification to therapist ${therapist._id}`);
          }
        }
      }

      // Get Stripe customer ID
      if (!client.stripeCustomerId) {
        return res.sendError("No payment method found. Please add a payment method first.");
      }

      // Fetch Stripe customer to check for default payment method
      const stripeCustomer = await stripe.customers.retrieve(client.stripeCustomerId);
      
      if (!stripeCustomer || stripeCustomer.deleted) {
        return res.sendError("Customer account not found. Please contact support.");
      }

      let paymentMethodId = stripeCustomer.invoice_settings?.default_payment_method;
      
      // If no default payment method, check if client has any payment methods
      if (!paymentMethodId) {
        // Get all payment methods for the customer
        const paymentMethods = await stripe.paymentMethods.list({
          customer: client.stripeCustomerId,
          type: "card",
        });
        
        // If there's at least one payment method, use the first one
        if (paymentMethods && paymentMethods.data && paymentMethods.data.length > 0) {
          paymentMethodId = paymentMethods.data[0].id;
          AppLogger.info(`Using first available payment method ${paymentMethodId} for client ${clientId}`);
        } else {
          return res.sendError("No payment methods found. Please add a payment method.");
        }
      }

      // Create payment intent with the copayment amount
      // Convert amount to cents (Stripe requires amounts in the smallest currency unit)
      const amountInCents = Math.round(copayment * 100);

      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: amountInCents,
        currency: 'usd',
        customer: client.stripeCustomerId,
        payment_method: paymentMethodId,
        confirm: true, // Confirm the payment immediately
        description: `Copayment for client: ${client.fullName || client.email}`,
        metadata: {
          clientId: clientId,
          type: 'copayment'
        },
      });

      // Process the payment result
      if (paymentIntent.status === 'succeeded') {
        // Payment successful
        AppLogger.info(`Copayment successful for client ${clientId}: $${copayment}`);
        
        // Update client with last payment information
        await ClientDao.updateClient(clientId, {
          last_pay_copayment_time: new Date(),
          last_pay_copayment_amount: copayment
        });
        AppLogger.info(`Updated client ${clientId} with last payment information`);
        
        // Update meeting status if vonageSessionId is provided
        if (vonageSessionId && meeting) {
          const updatedMeeting = await AdminDao.updateMeetingStatusById(meeting._id, {
            copayment: {
              amount: copayment,
              status: "PAID",
              details: ""
            }
          });
          
          AppLogger.info(`Updated meeting (${meeting._id}) with copayment information`);
        } else if (vonageSessionId) {
          AppLogger.warn(`Meeting with vonageSessionName ${vonageSessionId} not found`);
          
          // Add background task to retry update after 10s, 1 minute and 5 minutes
          // First attempt after 10s
          setTimeout(async () => {
            try {
              // Try to find the meeting again after 10s
              const delayedMeeting = await Meeting.findOne({ vonageSessionName: vonageSessionId });
              
              if (delayedMeeting && (!delayedMeeting.copayment || delayedMeeting.copayment.status !== "PAID")) {
                const updatedMeeting = await AdminDao.updateMeetingStatusById(delayedMeeting._id, {
                  copayment: {
                    amount: copayment,
                    status: "PAID",
                    details: ""
                  }
                });
                
                AppLogger.info(`First delayed update for meeting (${delayedMeeting._id}) with copayment information`);
              } else if (delayedMeeting && delayedMeeting.copayment && delayedMeeting.copayment.status === "PAID") {
                AppLogger.info(`Meeting (${delayedMeeting._id}) already has PAID status - no update needed at 1 minute mark`);
              } else {
                AppLogger.warn(`Meeting with vonageSessionName ${vonageSessionId} not found at 1 minute mark`);
              }
            } catch (retryError) {
              AppLogger.error(`Error in first delayed meeting update: ${retryError.message}`);
            }
          }, 10*1000); // Retry after 10s
          
          // First attempt after 1 minute
          setTimeout(async () => {
            try {
              // Try to find the meeting again after 1 minute
              const delayedMeeting = await Meeting.findOne({ vonageSessionName: vonageSessionId });
              
              if (delayedMeeting && (!delayedMeeting.copayment || delayedMeeting.copayment.status !== "PAID")) {
                const updatedMeeting = await AdminDao.updateMeetingStatusById(delayedMeeting._id, {
                  copayment: {
                    amount: copayment,
                    status: "PAID",
                    details: ""
                  }
                });
                
                AppLogger.info(`First delayed update for meeting (${delayedMeeting._id}) with copayment information`);
              } else if (delayedMeeting && delayedMeeting.copayment && delayedMeeting.copayment.status === "PAID") {
                AppLogger.info(`Meeting (${delayedMeeting._id}) already has PAID status - no update needed at 1 minute mark`);
              } else {
                AppLogger.warn(`Meeting with vonageSessionName ${vonageSessionId} not found at 1 minute mark`);
              }
            } catch (retryError) {
              AppLogger.error(`Error in first delayed meeting update: ${retryError.message}`);
            }
          }, 1*60*1000); // Retry after 1 minute
          
          // Second attempt after 5 minutes
          setTimeout(async () => {
            try {
              // Try to find the meeting again after 5 minutes
              const delayedMeeting = await Meeting.findOne({ vonageSessionName: vonageSessionId });
              
              if (delayedMeeting && (!delayedMeeting.copayment || delayedMeeting.copayment.status !== "PAID")) {
                const updatedMeeting = await AdminDao.updateMeetingStatusById(delayedMeeting._id, {
                  copayment: {
                    amount: copayment,
                    status: "PAID",
                    details: ""
                  }
                });
                
                AppLogger.info(`Second delayed update for meeting (${delayedMeeting._id}) with copayment information`);
              } else if (delayedMeeting && delayedMeeting.copayment && delayedMeeting.copayment.status === "PAID") {
                AppLogger.info(`Meeting (${delayedMeeting._id}) already has PAID status - no update needed at 5 minute mark`);
              } else {
                AppLogger.warn(`Meeting with vonageSessionName ${vonageSessionId} still not found after 5 minute delay`);
              }
            } catch (retryError) {
              AppLogger.error(`Error in second delayed meeting update: ${retryError.message}`);
            }
          }, 5*60*1000); // Retry after 5 minutes
          
          AppLogger.info(`Scheduled delayed update for meeting with vonageSessionName ${vonageSessionId}`);
        }
        
        return res.sendSuccess({
          status: 'success',
          paymentIntent: paymentIntent.id,
          amount: copayment,
        }, "Copayment processed successfully.");
      } else {
        // Payment failed or requires action
        return res.sendError(`Payment not completed. Status: ${paymentIntent.status}`);
      }
    } catch (error) {
      AppLogger.error(`Error in payClientCopayment: ${error.message}`);
      
      // Handle Stripe specific errors
      if (error.type && error.type.startsWith('Stripe')) {
        return res.sendError(`Payment failed: ${error.message}`);
      }
      
      return res.sendError("An error occurred while processing the payment.");
    }
  }

  /**
   * Get copayment status of a meeting
   * @param req Request object with vonageSessionId
   * @param res Response object
   * @param next NextFunction
   */
  public static async getCopaymentStatusByMeeting(req: Request, res: Response, next: NextFunction) {
    try {
      const { vonageSessionId } = req.body;

      // Validate input
      if (!vonageSessionId) {
        return res.sendError("Invalid request. Please provide valid vonageSessionId.");
      }

      // Tìm cuộc họp dựa trên vonageSessionName
      const meeting = await Meeting.findOne({ vonageSessionName: vonageSessionId });
      
      if (!meeting) {
        AppLogger.warn(`Meeting with vonageSessionName ${vonageSessionId} not found`);
        return res.sendSuccess({ copayment: null }, "Meeting not found");
      }

      // Trả về thông tin copayment
      return res.sendSuccess({
        copayment: meeting.copayment || null
      }, "Copayment status retrieved successfully");
      
    } catch (error) {
      AppLogger.error(`Error in getCopaymentStatusByMeeting: ${error.message}`);
      return res.sendError("An error occurred while getting the copayment status.");
    }
  }

  /**
   * Get copayment status of any meeting in the last 24 hours
   * @param req Request object
   * @param res Response object
   * @param next NextFunction
   */
  public static async getRecentMeetingCopayment(req: Request, res: Response, next: NextFunction) {
    try {
      // Lấy client ID từ JWT token đã xác thực
      const clientId = req.user._id;
      
      if (!clientId) {
        return res.sendError("Unauthorized. User ID not found in token.");
      }
      
      // Tính thời gian 24 giờ trước
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      // Tìm meeting trong khoảng 24 giờ qua có copayment của client hiện tại
      const recentMeeting = await Meeting.findOne({
        createdAt: { $gte: twentyFourHoursAgo },
        clientId: clientId,
        copayment: { $exists: true, $ne: null }
      }).sort({ createdAt: -1 });

      if (!recentMeeting) {
        return res.sendSuccess({ copayment: null }, "No recent meeting with copayment found");
      }

      // Trả về thông tin copayment của meeting gần nhất
      return res.sendSuccess({
        copayment: recentMeeting.copayment || null
      }, "Recent meeting copayment retrieved successfully");
      
    } catch (error) {
      AppLogger.error(`Error in getRecentMeetingCopayment: ${error.message}`);
      return res.sendError("An error occurred while getting the recent meeting copayment.");
    }
  }
} 