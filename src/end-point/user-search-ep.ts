import { Request, Response, NextFunction } from "express";
import { UserDao } from "../dao/user-dao";

export async function getUserByFirstNameLastName(
    req: Request,
    res: Response,
    next: NextFunction
) {
    const names = req.params.name.split('-');
    if (names.length !== 2) {
        return res.sendError("Invalid name format. Please use firstname-lastname format");
    }

    const firstName = names[0];
    const lastName = names[1];

    try {
        let user = await UserDao.getUserByFirstNameLastName(firstName, lastName);
        return res.sendSuccess(user, "Success");
    } catch (error) {
        return res.sendError(error);
    }
}
