import { NextFunction, Request, Response } from "express";
import { AdminStatisticsDao } from "../dao/admin-statistics-dao";
import { validationResult } from "express-validator";
import { DUser, IUser, UserRole, UserStatus } from "../models/user-model";
import { UserDao } from "../dao/user-dao";
import { EmailService } from "../mail/config";
import { SMSService } from "../sms/config";
import { Util } from "../common/util";
import { DAdminStatistics } from "../models/admin-statistics-model";
import { AppLogger } from "../common/logging";
import AdminStatisticsHourly from "../schemas/admin-statistic-hourly";

export namespace AdminStatisticsEp {
    export async function getTotalUsers(req: Request, res: Response, next: NextFunction) {
        const role = req.user.role;

        if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
            try {
                if (req.user.role == UserRole.SUB_ADMIN) {
                    const ownUser = await UserDao.getUserByUserId(req.user._id);
                    if (ownUser.adminPermission.adminDashboard != true) {
                        return res.sendError(
                            "You don't have permission for Dashboard!"
                        );
                    }
                }
                let clientsCount = await AdminStatisticsDao.getAllClientCount();

                let therapistCount = await AdminStatisticsDao.getAllTherapistCount();

                let pendingClientCount = await AdminStatisticsDao.getAllPendingClientCount();

                let data = {
                    clientCount: clientsCount,
                    pendingClientCount: pendingClientCount,
                    therapistCount: therapistCount,
                };

                return res.sendSuccess(data, "statistics data.");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("you have not permission");
        }
    }

    export async function getAllAppointmentStatisctics(req: Request, res: Response, next: NextFunction) {
        const role = req.user.role;

        try {
            if (req.user.role == UserRole.SUB_ADMIN) {
                const ownUser = await UserDao.getUserByUserId(req.user._id);
                if (ownUser.adminPermission.adminDashboard != true) {
                    return res.sendError(
                        "You don't have permission for Dashboard!"
                    );
                }
            }
            let allAppointmentStatistics = await AdminStatisticsDao.getaAllAppointmentStatistics();

            return res.sendSuccess(allAppointmentStatistics, "statistics data.");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getAllAdminStatistics(req: Request, res: Response, next: NextFunction) {
        const date = Number(req.body.date);
        const role = req.user.role;

        if (role === UserRole.SUB_ADMIN) {
            const ownUser = await UserDao.getUserByUserId(req.user._id);
            if (!ownUser.adminPermission.statistics) {
                return res.sendError("You don't have permission for Statistics!");
            }
        }

        const period = date === 365 ? "yearly" : date === 30 ? "monthly" : "weekly";

        try {
            const data = await AdminStatisticsHourly.findOne({ type: period }).exec();
            return res.sendSuccess(data, "Statistics Data.");
        } catch (error) {
            console.error("Error retrieving statistics data:", error);
            return res.sendError(error);
        }
    }

    export async function updateAdminStatisctics() {
        try {
            let yearlyRecurringRevenueCount = await AdminStatisticsDao.getAllYearlyRecurringRevenueCount();

            let monthlyRecurringRevenueCount = await AdminStatisticsDao.getAllMonthlyRecurringRevenueCount();

            let lifeTimeSalesSessions = await AdminStatisticsDao.getAllLifeTimeSalesCount();

            let averageCustomerLifetimeValue = await AdminStatisticsDao.getAverageCustomerLifetimeValue();

            let data: DAdminStatistics = {
                yearlyRecurringRevenueCount: yearlyRecurringRevenueCount.toFixed(2),
                monthlyRecurringRevenueCount: monthlyRecurringRevenueCount.toFixed(2),
                lifeTimeSales: lifeTimeSalesSessions.toFixed(2),
                averageCustomerLifetimeValue: averageCustomerLifetimeValue.toFixed(2)
            };

            AppLogger.info(`Admin Statistics Update: ` + new Date());

            AdminStatisticsDao.updateAdminStatistics(data);
        } catch (error) {
            AppLogger.error(`An error occured updating admin statistics at ${new Date()}. Error details: ${error}`);
        }

    }

    export async function getAllSubscriptionCounts(req: Request, res: Response, next: NextFunction) {
        const role = req.user.role;

        if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
            try {
                if (req.user.role == UserRole.SUB_ADMIN) {
                    const ownUser = await UserDao.getUserByUserId(req.user._id);
                    if (ownUser.adminPermission.adminDashboard != true && ownUser.adminPermission.statistics != true) {
                        return res.sendError(
                            "You don't have permission!"
                        );
                    }
                }
                let subscribers = await AdminStatisticsDao.getAllSubscribersCount();
                return res.sendSuccess(subscribers, "statistics data.");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("you have not permission");
        }
    }


    export async function adminCreateUser(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const email = req?.body?.email;
            const primaryPhone = req?.body?.primaryPhone;
            const firstname = req?.body?.firstname;
            const lastname = req?.body?.lastname;
            const username = req?.body?.username;
            const gender = req?.body?.gender;
            const password = req?.body?.password;
            const role = req?.body?.role;
            const message = req?.body?.message;
            const premiumUser = req?.body?.premiumUser && role == UserRole.CLIENT ? req?.body?.premiumUser : false;
            const adminPermission = req?.body?.adminPermissions;

            const clientLocationState = req?.body?.state;
            if (!email) {
                return res.sendError("Email is required.");
            }

            if (!role || (role != UserRole.CLIENT && role != UserRole.THERAPIST && role != UserRole.SUPER_ADMIN && role != UserRole.SUB_ADMIN)) {
                return res.sendError("Role is required.");
            }

            if (!password) {
                return res.sendError("Password is required.");
            }

            if (role == UserRole.CLIENT && !clientLocationState) {
                return res.sendError("State is required.");
            }

            let isEmailUsed = await UserDao.getUserByEmail(email);

            if (isEmailUsed) {
                return res.sendError("Provided email is already taken.");
            }

            let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
                primaryPhone
            );

            if (isprimaryPhoneUsed) {
                return res.sendError("Provided primary phone is already taken.");
            }

            const user = await UserDao.signUpWithEmailAdmin(
                email,
                primaryPhone,
                firstname,
                lastname,
                username,
                gender,
                password,
                role,
                UserStatus.VERIFIED,
                message,
                premiumUser,
                adminPermission,
                clientLocationState
            );

            if (!user) {
                AppLogger.info(`adminCreateUser - User creation failed for email: ${email}`);
                return res.sendError("User creation failed");
            }

            if (user?.primaryPhone) {
                await SMSService.sendEventSMS(
                    `Welcome to Lavni! Your login credentials:\n Email: ${user?.email}\nPassword: ${password} For security\n change your password after the first login in account settings.\nThank you for choosing Lavni!`,
                    user?.primaryPhone
                );
            }

            await EmailService.sendAdminEmailUserSignup(
                "Welcome to Lavni!",
                user?.email,
                password
            );

            return res.sendSuccess(user, "User created successfully.");

        } catch (error) {
            AppLogger.error(`adminCreateUser - User cannot be created. Error Details: ${error}`);
            return res.sendError(error);
        }
    }


    export async function getAllMissedAppointmentsCounts(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const role = req.user.role;
        const userId = req.params.userId;

        if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
            try {
                const user = await UserDao.getUserById(userId);

                if (user) {
                    let pastAppointmentCount;

                    if (user.role == UserRole.CLIENT) {
                        pastAppointmentCount = await AdminStatisticsDao.getAllPastAppointmentsByClientIdCount(userId);
                    } else {
                        pastAppointmentCount = await AdminStatisticsDao.getAllPastAppointmentsByTherapistIdCount(userId);
                    }

                    if (pastAppointmentCount) {
                        return res.sendSuccess(pastAppointmentCount, "statistics data.");
                    } else {
                        return res.sendError("Sorry! You haven't connected with this Therapist yet.");
                    }
                } else {
                    return res.sendError("Invalid user id.");
                }
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function changePasswordByAdmin(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.params.userId;
        const role = req.user.role;
        const newPassword = req.body.newPassword;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
            try {
                const user: IUser = await UserDao.getUserById(userId);

                if (user) {
                    let hashedPassword = await Util.passwordHashing(newPassword);

                    const updatedPassword: DUser = {
                        password: hashedPassword,
                    };

                    try {
                        let updatedUser = await UserDao.updateUser(
                            userId,
                            updatedPassword
                        );

                        if (!updatedUser) {
                            return res.sendError("Password could not be changed.");
                        }

                        return res.sendSuccess(updatedUser, "Password changed.");
                    } catch (error) {
                        return res.sendError(error);
                    }
                } else {
                    return res.sendError("User could not be found.");
                }
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function updateAdminStatisticsData() {
        const periods = [
            { type: "yearly", days: 365 },
            { type: "monthly", days: 30 },
            { type: "weekly", days: 7 },
        ];

        const adminStatistics = await AdminStatisticsDao.getAdminStatistics();

        for (const period of periods) {
            try {
                const [pastMissedAppointmentsCount, scheduledAppointmentsCount, completedSessions] = await Promise.all([
                    await AdminStatisticsDao.getPastMissedAppointmentsCount(period.days) ?? 0,
                    await AdminStatisticsDao.getAllScheduledAppointmentsCount(period.days) ?? 0,
                    await AdminStatisticsDao.getAllCompletedSessionsCount(period.days) ?? 0
                ]);

                const data = {
                    type: period.type,
                    pastMissedAppointmentsCount,
                    scheduledAppointmentsCount,
                    completedSessions,
                    yearlyRecurringRevenueCount: adminStatistics?.yearlyRecurringRevenueCount ?? "0",
                    monthlyRecurringRevenueCount: adminStatistics?.monthlyRecurringRevenueCount ?? "0",
                    lifeTimeSales: adminStatistics?.lifeTimeSales ?? "0",
                    averageCustomerLifetimeValue: adminStatistics?.averageCustomerLifetimeValue ?? "0",
                };
                await AdminStatisticsDao.updateAdminStatisticsDataHourly(data);
            } catch (error) {
                AppLogger.error(`Updating ${period.type} admin statistic failed at ${new Date()}. Error details: ${error}`);
            }
        }
    }

    export async function updateAdminStatisticsOnDemand(req: Request, res: Response, next: NextFunction) {
        try {
            const userId = req.user._id;

            let yearlyRecurringRevenueCount = await AdminStatisticsDao.getAllYearlyRecurringRevenueCount();

            let monthlyRecurringRevenueCount = await AdminStatisticsDao.getAllMonthlyRecurringRevenueCount();

            let lifeTimeSalesSessions = await AdminStatisticsDao.getAllLifeTimeSalesCount();

            let averageCustomerLifetimeValue = await AdminStatisticsDao.getAverageCustomerLifetimeValue();

            let data: DAdminStatistics = {
                yearlyRecurringRevenueCount: yearlyRecurringRevenueCount.toFixed(2),
                monthlyRecurringRevenueCount: monthlyRecurringRevenueCount.toFixed(2),
                lifeTimeSales: lifeTimeSalesSessions.toFixed(2),
                averageCustomerLifetimeValue: averageCustomerLifetimeValue.toFixed(2)
            };

            AppLogger.info(`Admin Statistics Updated on Demand by admin. AdminId: ${userId}. Date: ` + new Date());
            await AdminStatisticsDao.updateAdminStatistics(data);
            await AdminStatisticsEp.updateAdminStatisticsData();

            return res.sendSuccess({}, "Success");
        } catch (error) {
            AppLogger.error(`An error occurred updating admin statistics on demand at ${new Date()}. Error details: ${error}`);
            return res.sendError(error);
        }

    }
}
