var mongoose = require("mongoose");
import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import { FriendRequestDao } from "../dao/friend-request-dao";
import { UserDao } from "../dao/user-dao";
import {
  DFriendRequest,
  FriendRequestStatus,
  IFriendRequest,
} from "../models/friend-request-model";
import { UserRole } from "../models/user-model";
import fetch from "node-fetch";
import { ClientDao } from "../dao/client-dao";
import { DClient } from "../models/client-model";
import { TherapistDao } from "../dao/therapist-dao";
import { DTherapist } from "../models/therapist-model";
import { EmailService } from "../mail/config";
import { SMSService } from "../sms/config";
import { AppointmentDao } from "../dao/appointment-dao";
import { AdminDao } from "../dao/admin-dao";
import { ChatEp } from "./chat-ep";
import { StringOrObjectId } from "../common/util";
import { Client } from "socket.io/dist/client";
import { DDeletedFriendRequest } from "../models/deleted-friend-request-model";

export namespace FriendRequestEp {
  export async function createRequestByClientOld(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = req.user._id;
    const role = req.user.role;
    const therapistId = Types.ObjectId(req.params.therapistId);

    if (role == UserRole.CLIENT) {
      try {
        const therapist = TherapistDao.getUserById(therapistId);

        if (!therapist) {
          return res.sendError("Invalid therapist id.");
        }

        const requestDetails: DFriendRequest = {
          clientId: clientId,
          therapistId: therapistId,
          status: FriendRequestStatus.PENDING,
        };

        let addedFriendRequest = await FriendRequestDao.createRequestByClient(
          requestDetails
        );

        if (addedFriendRequest) {
          let updatedTherapist = await UserDao.updateRequestByUserId(
            therapistId,
            addedFriendRequest._id
          );

          let updatedClient = await UserDao.updateRequestByUserId(
            clientId,
            addedFriendRequest._id
          );

          if (updatedTherapist && updatedClient) {
            if (updatedTherapist.reminderType && updatedTherapist.reminderType.email == true) {
              await EmailService.sendEventEmail(
                updatedTherapist,
                "You have received a new request!",
                "You have received a request from",
                "Click here to connect with the client.",
                updatedClient.firstname + " " + updatedClient.lastname
              );
            }
            if (updatedTherapist.reminderType && updatedTherapist.reminderType.text == true) {
              await SMSService.sendEventSMS(
                `You have received a request from ${updatedClient.firstname} ${updatedClient.lastname}`,
                updatedTherapist.primaryPhone
              );
            }

            return res.sendSuccess(addedFriendRequest, "Request sent.");
          } else {
            return res.sendError("Could not update the users.");
          }
        } else {
          return res.sendError("Failed to send the request.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }
  export async function createRequestByClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = req.user._id;
    const role = req.user.role;
    const therapistId = Types.ObjectId(req.params.therapistId);

    if (role == UserRole.CLIENT) {
      try {
        const therapist = TherapistDao.getUserById(therapistId);

        if (!therapist) {
          return res.sendError("Invalid therapist id.");
        }

        const requestDetails: DFriendRequest = {
          clientId: clientId,
          therapistId: therapistId,
          status: FriendRequestStatus.PENDING,
        };

        let addedFriendRequest = await FriendRequestDao.createRequestByClient(
          requestDetails
        );

        if (addedFriendRequest) {
          let updatedTherapist = await UserDao.updateRequestByUserId(
            therapistId,
            addedFriendRequest._id
          );

          let updatedClient = await UserDao.updateRequestByUserId(
            clientId,
            addedFriendRequest._id
          );

          let requestDetails: DFriendRequest = {
            status: FriendRequestStatus.APPROVED,
          };

          let updatedFriendRequest: any = await FriendRequestDao.updateRequest(
            addedFriendRequest._id,
            requestDetails
          );

          let client: any = await AdminDao.getUserById(
            clientId
          );

          if (
            client?.friendRequests?.length == 1
          ) {
            const updateUser: any = {
              primaryTherapist: therapistId
            };
            await AdminDao.updateUser(clientId.toString(), updateUser);
          }

          if (
            updatedTherapist &&
            updatedClient &&
            updatedFriendRequest &&
            updatedFriendRequest?.status == FriendRequestStatus.APPROVED
          ) {
            await ChatEp.createChatInnerFunction(
              updatedFriendRequest?.therapistId._id,
              updatedFriendRequest?.clientId._id
            );

            if (updatedTherapist.reminderType && updatedTherapist.reminderType.email == true) {
              await EmailService.sendEventEmail(
                updatedTherapist,
                "You have received a new request!",
                "You have received a request from",
                "Click here to connect with the client.",
                updatedClient.firstname + " " + updatedClient.lastname
              );
            }

            if (updatedTherapist.reminderType && updatedTherapist.reminderType.text == true) {
              await SMSService.sendEventSMS(
                `You have received a request from ${updatedClient.firstname} ${updatedClient.lastname}`,
                updatedTherapist.primaryPhone
              );
            }

            if (updatedFriendRequest?.clientId.reminderType && updatedFriendRequest?.clientId.reminderType.email == true) {
              await EmailService.sendEventEmail(
                updatedFriendRequest?.clientId,
                "Your request has been approved!",
                "Your request has been approved by",
                "Click here to connect with the therapist.",
                updatedFriendRequest?.therapistId.firstname +
                " " +
                updatedFriendRequest?.therapistId.lastname
              );
            }

            if (updatedFriendRequest?.clientId.reminderType && updatedFriendRequest?.clientId.reminderType.text == true) {
              await SMSService.sendEventSMS(
                `You request has been approved by ${updatedFriendRequest?.therapistId.firstname} ${updatedFriendRequest?.therapistId.lastname}`,
                updatedFriendRequest?.clientId.primaryPhone
              );
            }

            return res.sendSuccess(addedFriendRequest, "Request sent.");
          } else {
            return res.sendError("Could not update the users.");
          }
        } else {
          return res.sendError("Failed to send the request.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function createRequestByClientPublic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = Types.ObjectId(req.params.clientId);
    const therapistId = Types.ObjectId(req.params.therapistId);

    try {
      const therapist = TherapistDao.getUserById(therapistId);

      if (!therapist) {
        return res.sendError("Invalid therapist id.");
      }

      const requestDetails: DFriendRequest = {
        clientId: clientId,
        therapistId: therapistId,
        status: FriendRequestStatus.PENDING,
      };

      let addedFriendRequest = await FriendRequestDao.createRequestByClient(
        requestDetails
      );

      if (addedFriendRequest) {
        let updatedTherapist = await UserDao.updateRequestByUserId(
          therapistId,
          addedFriendRequest._id
        );

        let updatedClient = await UserDao.updateRequestByUserId(
          clientId,
          addedFriendRequest._id
        );

        let requestDetails: DFriendRequest = {
          status: FriendRequestStatus.APPROVED,
        };

        let updatedFriendRequest: any = await FriendRequestDao.updateRequest(
          addedFriendRequest._id,
          requestDetails
        );

        if (
          updatedTherapist &&
          updatedClient &&
          updatedFriendRequest &&
          updatedFriendRequest?.status === FriendRequestStatus.APPROVED
        ) {
          await ChatEp.createChatInnerFunction(
            updatedFriendRequest?.therapistId._id,
            updatedFriendRequest?.clientId._id
          );

          return res.sendSuccess(addedFriendRequest, "Request sent.");
        } else {
          return res.sendError("Could not update the users.");
        }
      } else {
        return res.sendError("Failed to send the request.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateRequestByTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const requestId = req.body.requestId;
    const status = req.body.status;

    if (role != UserRole.THERAPIST) {
      return res.sendError("Invalid user role.");
    }

    try {
      let requestDetails: DFriendRequest = {
        status: status,
      };

      let updatedFriendRequest: any = await FriendRequestDao.updateRequest(
        requestId,
        requestDetails
      );

      if (updatedFriendRequest?.status === FriendRequestStatus.APPROVED) {
        await ChatEp.createChatInnerFunction(
          updatedFriendRequest?.therapistId._id,
          updatedFriendRequest?.clientId._id
        );
        if (updatedFriendRequest?.clientId.reminderType && updatedFriendRequest?.clientId.reminderType.email == true) {
          await EmailService.sendEventEmail(
            updatedFriendRequest?.clientId,
            "You request has been approved!",
            "You request has been approved by",
            "Click here to connect with the therapist.",
            updatedFriendRequest?.therapistId.firstname +
            " " +
            updatedFriendRequest?.therapistId.lastname
          );
        }
        if (updatedFriendRequest?.clientId.reminderType && updatedFriendRequest?.clientId.reminderType.text == true) {
          await SMSService.sendEventSMS(
            `You request has been approved by ${updatedFriendRequest?.therapistId.firstname} ${updatedFriendRequest?.therapistId.lastname}`,
            updatedFriendRequest?.clientId.primaryPhone
          );
        }

        return res.sendSuccess(updatedFriendRequest, "Request is approved.");
      } else {
        if (updatedFriendRequest?.clientId.reminderType && updatedFriendRequest?.clientId.reminderType.email == true) {
          await EmailService.sendEventEmail(
            updatedFriendRequest?.clientId,
            "Your request has been rejected!",
            "Sorry to inform! Your request has been rejected by",
            "Login to view more information.",
            updatedFriendRequest?.therapistId.firstname +
            " " +
            updatedFriendRequest?.therapistId.lastname
          );
        }

        if (updatedFriendRequest?.clientId.reminderType && updatedFriendRequest?.clientId.reminderType.text == true) {
          await SMSService.sendEventSMS(
            `Sorry to inform! Your request has been rejected by ${updatedFriendRequest?.therapistId.firstname} ${updatedFriendRequest?.therapistId.lastname}`,
            updatedFriendRequest?.clientId.primaryPhone
          );
        }
        return res.sendSuccess(updatedFriendRequest, "Request updated.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function removeFriendRequest(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const requestId = req.params.requestId;
    let clientFriendRequests: IFriendRequest[] = [];
    let therapistsFriendRequests: IFriendRequest[] = [];

    if (role == UserRole.THERAPIST || role == UserRole.CLIENT) {
      try {
        let friendRequest: any = await FriendRequestDao.getRequestById(
          requestId
        );

        if (!friendRequest) {
          return res.sendError("Invalid request id.");
        }

        let selectedClient = await ClientDao.getUserById(
          friendRequest.clientId
        );

        clientFriendRequests = selectedClient.friendRequests;

        if (
          clientFriendRequests.some(
            (el: any) => el.toString() === friendRequest._id
          )
        ) {
          let newFriendRequestsList = clientFriendRequests.filter(
            (fR) => fR.toString() !== friendRequest._id.toString()
          );

          const updatedClient: DClient = {
            friendRequests: newFriendRequestsList,
          };

          await UserDao.updateUser(selectedClient._id, updatedClient);
        }

        let selectedTherapist = await TherapistDao.getUserById(
          friendRequest.therapistId
        );

        therapistsFriendRequests = selectedTherapist.friendRequests;

        if (
          therapistsFriendRequests.some(
            (el: any) => el.toString() === friendRequest._id
          )
        ) {
          let newFriendRequestsList = therapistsFriendRequests.filter(
            (fR) => fR.toString() !== friendRequest._id.toString()
          );

          const updatedTherapist: DTherapist = {
            friendRequests: newFriendRequestsList,
          };

          await UserDao.updateUser(selectedTherapist._id, updatedTherapist);
        }
        await ChatEp.changeChatIsActiveStatusInnerFunctionByUserIds(
          friendRequest.therapistId,
          friendRequest.clientId,
          false
        );
        await FriendRequestDao.deleteRequestById(friendRequest._id);

        return res.sendSuccess("", "Friend request is successfully removed.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function viewAllRequestsByTherapistStats(userRole: any, _id: any, userLimit: any, userOffset: any) {
    const therapistId = _id
    const role = userRole;
    const limit = Number(userLimit);
    const offset = Number(userOffset);

    if (role == UserRole.THERAPIST) {
      try {
        let requestsList = await FriendRequestDao.getAllRequestsByTherapistIdStats(
          therapistId,
          limit,
          offset
        );
        return requestsList;
      } catch (error) {
        return error;
      }
    } else {
      const error3 = "Invalid user role."
      return error3;
    }
  }

  export async function getTherapistMeetingStats(userRole: any, _id: any) {
    const therapistId = _id
    const role = userRole;

    if (role == UserRole.THERAPIST) {
      try {
        const therapist = await TherapistDao.getUserById(therapistId);
        if (!therapist) {
          const error3 = "No therapist found with given userId."
          return error3;
        }
        let requestsList = await FriendRequestDao.getTherapistMeetingStatsById(therapistId);
        return requestsList;
      } catch (error) {
        return error;
      }
    } else {
      const error3 = "Invalid user role."
      return error3;
    }
  }


  export async function viewAllRequestsByTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapistId = req.user._id;
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    const gender = req.body.gender;
    const ethnicity = req.body.ethnicity;
    const searchClientName = req.body.searchClientName;
    const sortAlphabetically = req.body.sortAlphabetically;

    if (role == UserRole.THERAPIST) {
      try {
        let requestsList = await FriendRequestDao.getAllRequestsByTherapistId(
          therapistId,
          gender,
          ethnicity,
          searchClientName,
          sortAlphabetically,
          limit,
          offset
        );

        return res.sendSuccess(requestsList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function viewAllRequestsByTherapistAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapistId = Types.ObjectId(req.params.therapistId);
    const role = req.user.role;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        let requestsList =
          await FriendRequestDao.getAllFriendsByTherapistIdAdmin(therapistId);
        return res.sendSuccess(requestsList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }
  
  export async function viewAllRequestsByClientStats(_id: any, userLimit?: any, userOffset?: any) {
    const clientId = _id;

    try {
      if(userLimit) {
        const limit = Number(userLimit);
        const offset = Number(userOffset);

        let requestsList = await FriendRequestDao.getAllRequestsByClient(
          clientId,
          limit,
          offset
        );
  
        return requestsList;
      } else {
        let requestsList = await FriendRequestDao.getAllRequestsByClient(
          clientId
        );
  
        return requestsList;
      }
      
    } catch (error) {
      return error;
    }
  }

  //  remmove this
  export async function viewAllRequestsByClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = req.user._id;
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.CLIENT) {
      try {
        let requestsList = await FriendRequestDao.getAllRequestsByClient(
          clientId,
          limit,
          offset
        );

        return res.sendSuccess(requestsList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function checkIfUserIsFriend(req: Request, res: Response) {
    const role = req.user.role;
    const userId = Types.ObjectId(req.params.userId);

    try {
      const user = await AdminDao.getUserById(userId);

      if (user) {
        let isFriend;
        if (role == UserRole.CLIENT) {
          isFriend = await FriendRequestDao.checkIfUserIsFriend(
            req.user._id,
            user._id
          );
        } else {
          isFriend = await FriendRequestDao.checkIfUserIsFriend(
            user._id,
            req.user._id
          );
        }
        if (isFriend) {
          return res.sendSuccess(
            isFriend,
            "Yes. You have connected with this Therapist."
          );
        } else {
          return res.sendError(
            "Sorry! You haven't connected with this Therapist yet."
          );
        }
      } else {
        return res.sendError("Invalid user id.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function checkIfUserIsFriendPublic(req: Request, res: Response) {
    const userId = req.params.userId;
    const clientId = Types.ObjectId(req.params.clientId);

    try {
      const user = await UserDao.getUserById(userId);

      if (user) {
        let isFriend;

        isFriend = await FriendRequestDao.checkIfUserIsFriend(
          clientId,
          user._id
        );

        if (isFriend) {
          return res.sendSuccess(
            isFriend,
            "Yes. You have connected with this Therapist."
          );
        } else {
          return res.sendError(
            "Sorry! You haven't connected with this Therapist yet."
          );
        }
      } else {
        return res.sendError("Invalid user id.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function checkIfUserIsFriendAdmin(req: Request, res: Response) {
    const role = req.user.role;
    const clientId = Types.ObjectId(req.params.clientId);
    const therapistId = Types.ObjectId(req.params.therapistId);

    try {
      let isFriend;

      if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
        isFriend = await FriendRequestDao.checkIfUserIsFriend(
          clientId,
          therapistId
        );
      }

      if (isFriend) {
        return res.sendSuccess(
          isFriend,
          "Yes. You have connected with this Therapist."
        );
      } else {
        return res.sendError(
          "Sorry! You haven't connected with this Therapist yet."
        );
      }
    } catch (error) {
      return res.sendError(error);
    }
  }
  export async function viewAllSentRequestsByClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = req.user._id;
    const role = req.user.role;

    if (role == UserRole.CLIENT) {
      try {
        let requestsList = await FriendRequestDao.getAllRequestsByClientId(
          clientId
        );

        return res.sendSuccess(requestsList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function unfriendUser(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    const role = req.user.role;
    const requestId = req.params.requestId;

    if (role == UserRole.THERAPIST || role == UserRole.CLIENT) {
      try {
        let request = await FriendRequestDao.getRequestByIdAndUserId(requestId, userId, role);

        if (!request) {
          return res.sendError("Invalid attempt to unfriend!");
        }

        if (role == UserRole.THERAPIST) {
          const sessions = await AppointmentDao.getAllAppointmentBySpecipicUsers(userId, request.clientId);

          let deleteAppointments: StringOrObjectId[] = [];

          for (const s of sessions) {
            deleteAppointments.push(s._id);
          }

          await AppointmentDao.deleteMultipleAppointments(deleteAppointments);
        }

        if (role == UserRole.CLIENT) {
          const sessions = await AppointmentDao.getAllAppointmentBySpecipicUsers(request.therapistId, userId);

          let deleteAppointments: StringOrObjectId[] = [];

          for (const s of sessions) {
            deleteAppointments.push(s._id);
          }

          await AppointmentDao.deleteMultipleAppointments(deleteAppointments);
        }

        await ChatEp.changeChatIsActiveStatusInnerFunctionByUserIds(
          request.therapistId,
          request.clientId,
          false
        );

        const deletedRequest: DDeletedFriendRequest = {
          clientId: request.clientId,
          therapistId: request.therapistId,
          unfriendedBy: userId
        }

        await FriendRequestDao.createDeletedFriendRequest(deletedRequest);

        await FriendRequestDao.deleteRequestById(
          requestId
        );

        return res.sendSuccess("Succesfully removed friend request & deleted appointments.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role!");
    }
  }

  export async function getFriendRequestByTherapistIdAndClientId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const therapistId = req.params.therapistId;
    const clientId = req.params.clientId;

    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid client Id");
    }

    if (!mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid therapist Id");
    }

    try {
      let client = await UserDao.getUserById(clientId);
      let therapist = await UserDao.getUserById(therapistId);

      if (!client) {
        return res.sendError("Could not find a client with the given Id.");
      }

      if (!therapist) {
        return res.sendError("Could not find a therapist with the given Id.");
      }

      if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
        let friendRequest =
          await FriendRequestDao.getFriendRequestByTherapistIdAndClientId(
            Types.ObjectId(clientId),
            Types.ObjectId(therapistId)
          );

        return res.sendSuccess(friendRequest, "Success");
      } else {
        return res.sendError("Invalid user role!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function viewAllRequestsForShare(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = req.body.clientId;
    const role = req.user.role;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.THERAPIST || role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        let requestsList = await FriendRequestDao.getAllRequestsByClient(
          clientId,
          limit,
          offset
        );

        return res.sendSuccess(requestsList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function unmatchMatchedClientOrTherapistByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = Types.ObjectId(req.params.clientId);
    const therapistId = Types.ObjectId(req.params.therapistId);
    const userId = req.user._id;

    try {
      const client = UserDao.getUserById(clientId);

      if (!client) {
        return res.sendError("Invalid client id.");
      }

      let request = await FriendRequestDao.getFriendRequestByTherapistIdAndClientId(clientId, therapistId);

      if (!request) {
        return res.sendError("Invalid attempt to unfriend!");
      }

      const sessions = await AppointmentDao.getAllAppointmentBySpecipicUsers(request[0]?.therapistId, request[0]?.clientId);

      let deleteAppointments: StringOrObjectId[] = [];

      for (const s of sessions) {
        deleteAppointments.push(s._id);
      }

      await AppointmentDao.deleteMultipleAppointments(deleteAppointments);

      await ChatEp.changeChatIsActiveStatusInnerFunctionByUserIds(
        therapistId,
        clientId,
        false
      );

      const deletedRequest: DDeletedFriendRequest = {
        clientId: clientId,
        therapistId: therapistId,
        unfriendedBy: userId
      }

      await FriendRequestDao.createDeletedFriendRequest(deletedRequest);

      await FriendRequestDao.deleteRequestById(
        request[0]?._id
      );

      return res.sendSuccess("Succesfully removed friend request & deleted appointments.");
    } catch (error) {
      return res.sendError(error);
    }
  }
}
