import { NextFunction, Request, Response } from "express";
import { check, validationResult } from "express-validator";
import { UserDao } from "../dao/user-dao";
import { TherapistReferralDao } from "../dao/therapist-referral-dao";
import { Validation } from "../common/validation";
require("dotenv").config();
const { MessagingResponse } = require("twilio").twiml;
import {
    AppointmentSMSStatus,
    DUser,
    IUser,
    Medium,
    UserRole,
    UserStatus,
} from "../models/user-model";
import multer = require("multer");
const moment = require('moment-timezone');
import { DClient, IClient } from "../models/client-model";
import { DTherapist, ITherapist, RegistrationApprovalStatus } from "../models/therapist-model";
import { DContact } from "../models/contact-us-model";
import { DLavniReview, LavniReviewStatus } from "../models/lavni-review-model";
import { UploadDao } from "../dao/upload-dao";
import { DUpload, IUpload } from "../models/upload-model";
import { Types } from "mongoose";
import * as path from "path";
import { StringOrObjectId, Util } from "../common/util";
import { EmailService } from "../mail/config";
import { EducationDao } from "../dao/education-dao";
import { LicenseDao } from "../dao/license-dao";
import { DLicense, ILicense } from "../models/license-model";
import { DEducation, IEducation } from "../models/education-model";
import { TherapistDao } from "../dao/therapist-dao";
import { Payment } from "../models/sub-models/payment-model";
import fetch from "node-fetch";
import { AdminDao } from "../dao/admin-dao";
import { SMSService } from "../sms/config";
import { AppointmentDao } from "../dao/appointment-dao";

import {
    DNotification,
    NotificationEvent,
    NotificationVarient,
} from "../models/notification-model";
import { DCustomerReview } from "../models/customer-reviews-model";
import { AppointmentStatus, ApprovalStatus, DAppointment, IAppointment, RepeatType } from "../models/appointment-model";
import { ReviewStatus } from "../models/report-user-model";
import { VideoCallDao } from "../dao/videocall-dao";
import { DFriendRequest, FriendRequestStatus } from "../models/friend-request-model";
import { FriendRequestDao } from "../dao/friend-request-dao";
import { ChatEp } from "./chat-ep";
import { ClientDao } from "../dao/client-dao";
import Transaction from "../schemas/transaction-schema";
import DiagnosisNote from "../schemas/diagnosis-note-schema";
import MonthlyPayment from "../schemas/monthly-payment-schema";
import User from "../schemas/user-schema";
import { REMINDER_SMS_TYPE } from "../models/reminder-sms-model";
import { TransactionsDao } from "../dao/transactions-dao";
import Meeting from "../schemas/meeting-schema";
import Insurance from "../schemas/insurance-schema";
import { SmsChatEp } from "./sms-chat-ep";
import { AdminClientSmsChatEp } from "./admin-client-sms-chat-ep";
import Therapist from "../schemas/therapist-schema";
import { ClientReferralDao } from "../dao/client-referral-dao";
import { AppLogger } from "../common/logging";
const { OAuth2Client } = require("google-auth-library");
let mongoose = require("mongoose");
let jwt = require("jsonwebtoken");
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const sharp = require("sharp");
var fs = require("fs");
const { google } = require("googleapis");
const uuid = require('uuid'); // Importing the UUID library
// Generating a unique ID
const uniqueID = uuid.v4();
const oAuth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.APP_URL
);

export enum UploadCategory {
    PROFILE_IMAGE = "PROFILE_IMAGE",
    PROFILE_IMAGE_THUMBNAIL = "PROFILE_IMAGE_THUMBNAIL",
    EDUCATIONAL_DOCUMENTS = "EDUCATIONAL_DOCUMENTS",
    LICENSE_DOCUMENTS = "LICENSE_DOCUMENTS",
    THERAPIST_HOMEWORK_DOCUMENTS = "THERAPIST_HOMEWORK_DOCUMENTS",
    CLIENT_HOMEWORK_DOCUMENTS = "CLIENT_HOMEWORK_DOCUMENTS",
    ARTICLE_IMAGE = "ARTICLE_IMAGE",
    ARTICLE_VIDEO = "ARTICLE_VIDEO",
    ARTICLE_FILE = "ARTICLE_FILE",
    PROFILE_VIDEO = "PROFILE_VIDEO",
    PROFILE_COVER_IMAGE = "PROFILE_COVER_IMAGE",
    PROFILE_CONTENT = "PROFILE_CONTENT",
    GOAL_DOCUMENTS = "GOAL_DOCUMENTS",
    THEMES = "THEMES",
    AVATARS = "AVATARS",
    DISCLOSURE_STATEMENT = "DISCLOSURE_STATEMENT",
    GROUP_ICON = "GROUP_ICON",
    DIAGNOSISNOTE_DOCUMENTS = "DIAGNOSISNOTE_DOCUMENTS",
    CALL_RECORDS = "CALL_RECORDS",
    CALL_RECORDS_TEMPORY_VIDEOS = "CALL_RECORDS_TEMPORY_VIDEOS",
    GROUP_CHAT_MEDIA = "GROUP_CHAT_MEDIA",
    CLAIM_CSV = "CLAIM_CSV",
    TECH_TICKET_IMAGE = "TECH_TICKET_IMAGE",
    TECH_TICKET_VIDEO = "TECH_TICKET_VIDEO",
    CLINICAL_ASSESSMENT = "CLINICAL_ASSESSMENT",
    THERAPY_PLAN = "THERAPY_PLAN",
    AUTHORIZATION_FORM = "AUTHORIZATION_FORM",
}

type GoogleCalendarData = {
    title: string;
    start: string;
    end: string;
};

export namespace UserEp {
    export function loginWithEmailValidationRules() {
        return [
            Validation.email(),
            Validation.password(),
            Validation.Medium(Medium.EMAIL, Medium.FACEBOOK, Medium.GOOGLE),
        ];
    }

    export function signUpValidationRules() {
        return [
            check("firstName")
                .not()
                .isEmpty()
                .withMessage("First name is required."),
            check("lastName")
                .not()
                .isEmpty()
                .withMessage("Last name is required."),
            check("email")
                .not()
                .isEmpty()
                .withMessage("Email is required.")
                .isEmail()
                .normalizeEmail({ gmail_remove_dots: false })
                .withMessage("Invalid email address and please try again."),
            check("phone")
                .not()
                .isEmpty()
                .withMessage("Primary Phone Number is required.")
                .matches(/^\+[1-9]{1}[0-9]{7,11}$/)
                .withMessage("Invalid Primary Phone Number and please try again."),
            check("password")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Password is required.")
                .isLength({ min: 6, max: 40 })
                .withMessage(
                    "Password must be at least 6 chars long & not more than 40 chars long."
                )
                .not()
                .isIn(["123", "password", "god", "abc"])
                .withMessage("Do not use a common word as the password")
                .matches(/\d/)
                .withMessage("Password must contain a number."),
            check("role")
                .not()
                .isEmpty()
                .withMessage("User Role is required.")
                .isIn([UserRole.CLIENT, UserRole.THERAPIST])
                .withMessage("User role has to be either a CLIENT or a THERAPIST"),
        ];
    }

    export function signUpValidationRulesTemp() {
        return [
            check("userData.email")
                .not()
                .isEmpty()
                .withMessage("Email is required.")
                .isEmail()
                .normalizeEmail({ gmail_remove_dots: false })
                .withMessage("Invalid email address and please try again."),
            check("userData.primaryPhone")
                .not()
                .isEmpty()
                .withMessage("Primary Phone Number is required.")
                .matches(/^\+[1-9]{1}[0-9]{7,11}$/)
                .withMessage("Invalid Primary Phone Number and please try again."),
            check("userData.password")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Password is required.")
                .isLength({ min: 6, max: 40 })
                .withMessage(
                    "Password must be at least 6 chars long & not more than 40 chars long."
                )
                .not()
                .isIn(["123", "password", "god", "abc"])
                .withMessage("Do not use a common word as the password")
                .matches(/\d/)
                .withMessage("Password must contain a number."),
            check("userData.role")
                .not()
                .isEmpty()
                .withMessage("User Role is required.")
                .isIn([UserRole.CLIENT, UserRole.THERAPIST])
                .withMessage("User role has to be either a CLIENT or a THERAPIST"),
        ];
    }
    export function signUpValidationRulesResendVerificationTemp() {
        return [
            check("userId")
                .not()
                .isEmpty()
                .withMessage("Invalid userId."),
        ];
    }
    export function registerClientValidationRules() {
        return [
            check("registerDetails.firstname")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Firstname is required.")
                .isLength({ max: 100 })
                .withMessage("Name not more than 1000 chars long.")
                .not()
                .isNumeric()
                .withMessage("Only letters are allowed."),
            check("registerDetails.lastname")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Lastname is required.")
                .isLength({ max: 100 })
                .withMessage("Name not more than 1000 chars long.")
                .not()
                .isNumeric()
                .withMessage("Only letters are allowed."),
            check("registerDetails.username")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Username is required.")
                .isLength({ max: 100 })
                .withMessage("Name not more than 1000 chars long."),
            check("registerDetails.dateOfBirth")
                .not()
                .isEmpty()
                .withMessage("DOB is required."),
            check("registerDetails.gender")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Gender is required."),
            check("registerDetails.role")
                .isIn([UserRole.CLIENT, UserRole.THERAPIST])
                .notEmpty()
                .withMessage("User Role is required."),
            check("registerDetails.medium")
                .isIn([Medium.EMAIL, Medium.FACEBOOK, Medium.GOOGLE])
                .notEmpty()
                .withMessage("User Role is required."),
        ];
    }

    export function contactValidationRules() {
        return [
            check("name")
                .not()
                .isEmpty()
                .withMessage("Name is required.")
                .isString()
                .isLength({ max: 100 })
                .withMessage("Name field should not be more than 1000 chars long."),
            check("email")
                .not()
                .isEmpty()
                .withMessage("Email is required.")
                .isEmail()
                .normalizeEmail({ gmail_remove_dots: false })
                .withMessage("Invalid email address and please try again."),
            check("problem")
                .not()
                .isEmpty()
                .withMessage("Problem is required.")
                .isString()
                .isLength({ max: 1000 })
                .withMessage("Problem field should not be more than 1000 chars long."),
            check("phoneNumber")
                .not()
                .isEmpty()
                .withMessage("Phone number is required.")
                .isString()
                .isLength({ max: 15 })
                .withMessage("Phone number can't be more than 15 numbers")
                .isNumeric()
                .withMessage("Phone number can only have digits."),
            check("ip")
                .optional()
                .isIP()
                .withMessage("Invalid IP address format"),
        ];
    }

    export function ratingValidationRules() {
        return [
            check("name")
                .not()
                .isEmpty()
                .withMessage("Name is required.")
                .isString()
                .isLength({ max: 100 })
                .withMessage("Name field should not be more than 1000 chars long."),
            check("email")
                .not()
                .isEmpty()
                .withMessage("Email is required.")
                .isEmail()
                .normalizeEmail({ gmail_remove_dots: false })
                .withMessage("Invalid email address and please try again."),
            check("reviewMessage")
                .not()
                .isEmpty()
                .withMessage("Review message is required.")
                .isString()
                .isLength({ max: 1000 })
                .withMessage("Review message should not be more than 1000 chars long.")
        ];
    }

    export function reviewValidationRules() {
        return [
            check("review")
                .not()
                .isEmpty()
                .withMessage("Review is required.")
                .isString()
                .isLength({ max: 1000 })
                .withMessage("Review field should not be more than 1000 chars long."),
        ];
    }

    export function forgotPasswordValidationRules() {
        return [Validation.email()];
    }

    export function updateClientValidationRules() {
        return [
            check("firstname").not().isEmpty().withMessage("Firstname is required."),
            check("email")
                .isEmail()
                .normalizeEmail({ gmail_remove_dots: false })
                .withMessage("Invalid email address and please try again."),
        ];
    }

    export function updateClientDobValidationRules() {
        return [
            check("dateOfBirth").not().isEmpty().withMessage("Date 0f Birth is required."),
        ];
    }

    export function changePasswordValidationRulesAdmim() {
        return [
            check("newPassword")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Password is required.")
                .isLength({ min: 6, max: 40 })
                .withMessage(
                    "Password must be at least 6 chars long & not more than 40 chars long."
                )
                .not()
                .isIn(["123", "password", "god", "abc"])
                .withMessage("Do not use a common word as the password")
                .matches(/\d/)
                .withMessage("Password must contain a number."),
        ];
    }


    export function changePasswordValidationRules() {
        return [
            check("oldPassword")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Password is required.")
                .isLength({ min: 6, max: 40 })
                .withMessage(
                    "Password should be at least 6 chars long & not more than 40 chars long."
                )
                .not()
                .isIn(["123", "password", "god", "abc"])
                .withMessage("Do not use a common word as the password")
                .matches(/\d/)
                .withMessage("Password must contain a number."),
            check("newPassword")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Password is required.")
                .isLength({ min: 6, max: 40 })
                .withMessage(
                    "Password should be at least 6 chars long & not more than 40 chars long."
                )
                .not()
                .isIn(["123", "password", "god", "abc"])
                .withMessage("Do not use a common word as the password")
                .matches(/\d/)
                .withMessage("Password must contain a number."),
            check("confirmPassword")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Password is required.")
                .isLength({ min: 6, max: 40 })
                .withMessage(
                    "Password must be at least 6 chars long & not more than 40 chars long."
                )
                .not()
                .isIn(["123", "password", "god", "abc"])
                .withMessage("Do not use a common word as the password")
                .matches(/\d/)
                .withMessage("Password must contain a number."),
        ];
    }

    export function addPaymentMethodValidationRules() {
        return [
            check("cardNo")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Card number is required.")
                .isNumeric()
                .withMessage("Card number should be a number"),
            check("expMonth")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Expiry date is required.")
                .isNumeric()
                .withMessage("Expiry month should be a number"),
            check("expYear")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Expiry year is required.")
                .isNumeric()
                .withMessage("Expiry year should be a number"),
            check("cvv")
                .isString()
                .not()
                .isEmpty()
                .withMessage("CVV is required.")
                .isNumeric()
                .withMessage("CVV should be a number"),
            check("isDefault")
                .isBoolean()
                .not()
                .isEmpty()
                .withMessage("Default type is required."),
        ];
    }

    export function updateTherapistProfileValidationRules() {
        return [
            check("firstname")
                .isString()
                .not()
                .isEmpty()
                .withMessage("First name is required."),
            check("lastname")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Last name is required."),
            check("username")
                .isString()
                .not()
                .isEmpty()
                .withMessage("Username is required."),
        ];
    }

    export async function getGoogleCalendarRefreshToken(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            AppLogger.info(`Get google calendar refresh token, userId: ${req.user?._id}`)
            const code = req.body.code;

            if (!code) {
                return res.sendError("Code is required");
            }
            console.log("code ", code);
            
            const tokens = await oAuth2Client.getToken(code);
            console.log(tokens)
            if (!tokens?.tokens?.refresh_token) {
                AppLogger.info(`Error retrieving token, userId: ${req.user?._id}`)
                return res.sendError("Error retrieving token");
            }

            const googleCalendarAccessToken = tokens?.tokens?.refresh_token;
            
            let responseUser = await UserDao.createGoolgeCalendarRefreshToken(
                req.user._id,
                googleCalendarAccessToken,
                true
            );

            if (!responseUser) {
                return res.sendError("Error updating token");
            }
            
            return res.sendSuccess("sucess");
            
        } catch (error) {
            AppLogger.info(`Rretrieving token error occured, userId: ${req.user?._id}`)
            return res.sendError(error);
        }
    }

    export async function createGoogleCalendarEventWhenCreateAppointment(
        calendarData: GoogleCalendarData,
        therapistId: string,
    ) {
        try {
            const calendar = google.calendar("v3");
            const { googleCalendarRefreshToken } = await UserDao.getUserGoogleCalendarRefreshTokenById(therapistId);
            if (!googleCalendarRefreshToken) {
                AppLogger.error(`No refresh token found for therapistId: ${therapistId}`);
                return false; 
            }

            oAuth2Client.setCredentials({
                refresh_token: googleCalendarRefreshToken,
            });

            const event = {
                summary: calendarData.title,
                location: "virtual meeting with client",
                description: `<a href="${process.env.APP_URL}">Click here</a> for ${calendarData.title}`,
                start: {
                    dateTime: calendarData.start,
                    timeZone: "America/Los_Angeles",
                },
                end: {
                    dateTime: calendarData.end,
                    timeZone: "America/Los_Angeles",
                },
            };

            try {
                AppLogger.info(`Calendar sync started, therapist Id: ${therapistId}`);
                const insertedEvent = await calendar.events.insert({
                    auth: oAuth2Client,
                    calendarId: "primary",
                    resource: event,
                }); 
                AppLogger.info(`Google Calendar sync successfully, therapist Id: ${therapistId}`);
                return true;
                
            } catch (error) {
                AppLogger.error(`Google calendar sync error occured 01, therapist Id: ${therapistId}, error: ${error}`);
                return false;
            }
            

        } catch (err) {
            AppLogger.error(`Google calendar sync error occured 02, therapist Id: ${therapistId}, error: ${err}`);
            console.error("Error creating event:", err);
            return false;
        }
    }
    
    export async function createGoogleCalendarEvent(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const calendarData = req.body;
            const calendar = google.calendar("v3");
            const id = req.body.therapistId._id;

            const responseUser = await UserDao.getUserByIdGooleClendar(id);
            const getRefreshToken = JSON.parse(JSON.stringify(responseUser));

            oAuth2Client.setCredentials({
                refresh_token: getRefreshToken.googleCalendarRefreshToken,
            });

            const event = {
                summary: calendarData.title,
                location: "virtual meeting with client",
                description: `<a href="${process.env.APP_URL}">Click here</a> for ${calendarData.title}`,
                start: {
                    dateTime: calendarData.start,
                    timeZone: "America/Los_Angeles",
                },
                end: {
                    dateTime: calendarData.end,
                    timeZone: "America/Los_Angeles",
                },
            };

            const insertedEvent = await calendar.events.insert({
                auth: oAuth2Client,
                calendarId: "primary",
                resource: event,
            });

            return res.sendSuccess(insertedEvent.data.id, "Success");
        } catch (err) {
            console.error("Error creating event:", err);
            return res.sendError(err);
        }
    }

    export async function getGoogleCalendarActiveStatus(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const id = req.body;

        let responseUser = await UserDao.getUserByIdGooleClendar(id);
        res.send(responseUser);
    }

    export async function googleCalendarLogout(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const id = req.user._id;
        let responseUserToken = await UserDao.getUserByIdGooleClendar(id);
        const getRefreshToken = JSON.parse(JSON.stringify(responseUserToken));

        try {
            oAuth2Client.setCredentials({ refresh_token: getRefreshToken });
            const https = require("https");

            let postData = "token=" + getRefreshToken.googleCalendarRefreshToken;

            let postOptions = {
                host: "oauth2.googleapis.com",
                port: "443",
                path: "/revoke",
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Content-Length": Buffer.byteLength(postData),
                },
            };

            const postReq = https.request(postOptions, function (res: any) {
                res.setEncoding("utf8");
                res.on("data", (d: any) => { });
            });

            postReq.on("error", (error: any) => {
                res.send(error);
            });

            postReq.write(postData);
            postReq.end();
            const googleCalendarAccessToken = false;
            let responseUser = await UserDao.inactiveGoogleCalendarStatus(
                id,
                googleCalendarAccessToken
            );
            res.send(responseUser);
        } catch (err) {
            res.send(err);
        }
    }

    export async function updateGoogleCalendarEvent(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const calendarData = req.body;
        try {
            const calendar = google.calendar("v3");
            const id = req.body.therapistId._id;

            let responseUser = await UserDao.getUserByIdGooleClendar(id);
            try {
                const getRefreshToken = JSON.parse(JSON.stringify(responseUser));

                oAuth2Client.setCredentials({
                    refresh_token: getRefreshToken.googleCalendarRefreshToken,
                });
                const event = {
                    summary: calendarData.title,
                    location: "virtual meeting with client",
                    description: calendarData.title,
                    start: {
                        dateTime: calendarData.start,
                        timeZone: "America/Los_Angeles",
                    },
                    end: {
                        dateTime: calendarData.end,
                        timeZone: "America/Los_Angeles",
                    },
                };

                const insertedEvent = await calendar.events.update({
                    auth: oAuth2Client,
                    calendarId: "primary",
                    eventId: calendarData.GoogleCalendarEventId,
                    resource: event,
                });

                return res.sendSuccess(insertedEvent.data.id, "Success");
            } catch (err) {
                console.error("Error creating event:", err);
                return res.sendError(err);
            }
        } catch (err) {
            res.sendError(err);
        }
    }

    export async function deleteGoogleCalendarEvent(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const calendarData = req.body;
        try {
            const calendar = google.calendar("v3");
            const id = req.body.therapistId._id;

            let responseUser = await UserDao.getUserByIdGooleClendar(id);
            try {
                const getRefreshToken = JSON.parse(JSON.stringify(responseUser));

                oAuth2Client.setCredentials({
                    refresh_token: getRefreshToken.googleCalendarRefreshToken,
                });

                calendar.events.delete(
                    {
                        auth: oAuth2Client,
                        calendarId: "primary",
                        eventId: calendarData.GoogleCalendarEventId,
                    },
                    function (err: any, event: any) {
                        if (err) {
                            return;
                        }
                        res.send(event);
                    }
                );
            } catch (err) {
                res.send(err);
            }
        } catch (err) {
            res.send(err);
        }
    }

    export async function authenticate(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        UserDao.authenticateUser(req.body.email, req.body.password)
            .then((token: string) => {
                res.sendSuccess(token, "Token sent successfully.");
            })
            .catch(next);
    }

    export async function getMe(req: Request, res: Response, next: NextFunction) {
        let user = await UserDao.getUser(req.user._id);

        if (!user) {
            return res.sendError("User not found.");
        }

        if (user.role == UserRole.CLIENT) {
            let client: IClient = user as IClient;

            if (client.subscriptionId) {
                try {
                    const subscription = await stripe.subscriptions.retrieve(
                        client.subscriptionId
                    );

                    if (client.subscriptionStatus !== subscription.status) {
                        client.subscriptionStatus = subscription.status;
                        client.save();
                    }
                } catch (error) {
                    client.subscriptionStatus = "canceled";
                    client.save();
                }
            }

            return res.sendSuccess(client, "Success");
        }

        if (user.role == UserRole.THERAPIST) {

            const therapist = await Therapist.findById(user._id);

            if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                return res.sendError(
                    therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                );
            }
        }

        return res.sendSuccess(user, "Success");
    }

    export async function signUp(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }

            const email = req.body.email;
            const primaryPhone = req.body.phone;
            const password = req.body.password;
            const role = req.body.role;
            const referralCode = req.body.referralCode || null;

            let user = null;
            let verificationCode = null;

            const firstName = req.body.firstName;
            const lastName = req.body.lastName;

            if (!firstName || !lastName || firstName.trim() == "" || lastName.trim() == "") {
                return res.sendError("Please provide valid first name and last name");
            }

            let existingUser = await UserDao.getUserByEmail(email);

            if (existingUser) {
                return res.sendError("Provided email is already taken.");
            }

            let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
                primaryPhone
            );

            if (isprimaryPhoneUsed) {
                return res.sendError("Provided primary phone is already taken.");
            }

            if (role == UserRole.THERAPIST) {
                user = await UserDao.signUpWithEmailForTherapistWithName(
                    email,
                    primaryPhone,
                    password,
                    role,
                    UserStatus.VERIFIED,
                    firstName,
                    lastName
                );

                if (referralCode && user) {
                    await TherapistReferralDao.addDetails(
                        referralCode,
                        user._id
                    );
                }

                // await EmailService.sendWelcomeEmail(user, "Welcome To Lavni");

                if (user.primaryPhone) {
                    await SMSService.sendEventSMS(
                        `Welcome To Lavni Find a therapist that aligns best with you. We strive to create a future where it is easy for anyone to access a mental health clinician. A space where you can be your authentic self while protecting your privacy. 
                        \nTransparency and compliance is at the core of our electronic health records. We are a community filled with support to guide you every step of the way. Your experience matters.`,
                        user.primaryPhone
                    );
                }

                await EmailService.sendAdminEmailWhenTherapistOrClientSignUp(
                    "New Therapist Registration",
                    "Welcome! We have a new therapist on board. The therapist has signed up with the email address: ",
                    user.email
                );

                await EmailService.sendAdminEmailWhenTherapistToSubAdminLevel1(
                    "New Therapist Registration",
                    "Welcome! We have a new therapist on board. The therapist has signed up with the email address: ",
                    user.email
                );

                return res.sendSuccess(user, "Therapist Registered Successfully.");
            } else {
                user = await UserDao.signUpWithEmail(
                    email,
                    primaryPhone,
                    password,
                    role
                );

                let code = Math.floor(Math.random() * (999999 - 100000 + 1) + 100000);

                verificationCode = code.toString();

                const updatedUser: any = {
                    verificationCode: await Util.passwordHashing(verificationCode),
                };

                let userWithVerificationCode = await UserDao.updateUser(
                    user._id,
                    updatedUser
                );

                if (!userWithVerificationCode) {
                    return res.sendError(
                        "Something went wrong with verification code."
                    );
                }

                let isEmailSent = await EmailService.sendVerifyEmail(
                    user,
                    "Lavni - Verify your email.",
                    verificationCode,
                    "Thank you for signing up with Lavni!",
                    "To proceed with your account you have to verify your email. Please enter the following OTP in the verify section."
                );

                if (user.primaryPhone) {
                    await SMSService.sendEventSMS(
                        `Thank you for signing up with Lavni!, To proceed with your account you have to verify your email. Please enter the following OTP in the verify section ${verificationCode}.`,
                        user.primaryPhone
                    );
                }

                if (!user) {
                    return res.sendError("User signup failed! Please try again later.");
                }

                return res.sendSuccess(user, "Success");
            }

        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function signUpPublic(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        try {
            const firstname = req.body.userData.firstname.trim();
            const lastname = req.body.userData.lastname.trim();
            const email = req.body.userData.email;
            const username = req.body.userData.username.trim();
            const dateOfBirth = req.body.userData.dateOfBirth;
            const ethnicityId = req.body.userData.ethnicityId;
            const gender = req.body.userData.gender;
            const primaryPhone = req.body.userData.primaryPhone;
            const state = req.body.userData.state;
            const zipCode = req.body.userData.zipCode;
            const password = req.body.userData.password;
            const role = req.body.userData.role;
            const skip = req.body.userData.skip;
            const PersonalizeMatchData = req.body.userData.PersonalizeMatchData;
            const therapistId = req.body.likeTherapist?.therapistId;
            const appointment = req.body.appointmentObj;
            const referralCode = req.body.referralInfo?.referralCode;

            let user = null;
            let verificationCode = null;

            try {
                let isEmailUsed = await UserDao.getUserByEmail(email);

                if (isEmailUsed) {
                    return res.sendError("Provided email is already taken.");
                }

                const foundUser = await UserDao.getUserByUsername(username);

                if (foundUser) {
                    return res.sendError("Username is already taken.");
                }

                let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
                    primaryPhone
                );

                if (isprimaryPhoneUsed) {
                    return res.sendError("Provided primary phone is already taken.");
                }

                const enteredBirthDate = new Date(dateOfBirth);

                const currentDate = new Date();
                const maxBirthDate = new Date(
                    currentDate.getFullYear() - 100,
                    currentDate.getMonth(),
                    currentDate.getDate()
                );

                if (enteredBirthDate < maxBirthDate) {
                    return res.sendError(
                        "Date of birth is more recent than 100 years ago."
                    );
                }

                const minBirthDate = new Date(
                    currentDate.getFullYear() - 13,
                    currentDate.getMonth(),
                    currentDate.getDate()
                );

                if (enteredBirthDate > minBirthDate) {
                    return res.sendError("You must be at least 13 years old to continue.");
                }

                // Chỉ kiểm tra therapist nếu có therapistId
                if (therapistId) {
                    const therapist = TherapistDao.getUserById(therapistId);

                    if (!therapist) {
                        return res.sendError("Invalid therapist id.");
                    }
                }
                
                let appointmentCreateResMessage = '';

                // Chỉ tạo appointment nếu có therapistId, appointment data và skip != true
                if (skip != true && therapistId && appointment) {
                    const beginsAt = moment(appointment.start);
                    const endsAt = moment(appointment.start);

                    const startTime = moment(appointment.start).format("H:mm A");

                    const appointmentStartTime = moment(appointment.start).format(
                        "YYYY-MM-DD HH:mm"
                    );

                    const mST = moment(startTime, "HH:mm").minute();

                    if (mST != 0 && mST != 30) {
                        return res.sendError("Please select valid start time.");
                    }

                    if (!startTime) {
                        return res.sendError("Please select valid start time.");
                    }

                    const tomorrowDate: Date = moment(new Date()).add(1, "day").startOf("day").toDate();

                    if (
                        !moment(tomorrowDate).isSameOrBefore(
                            moment(
                                new Date(appointmentStartTime).setHours(
                                    parseInt(startTime.split(":")[0]),
                                    parseInt(startTime.split(":")[1]),
                                    0,
                                    0
                                )
                            )
                        )
                    ) {
                        return res.sendError(
                            "Sorry! You can only create appointment starting from tomorrow!"
                        );
                    }

                    const timeDifferenceInHours = moment
                        .duration(endsAt.diff(beginsAt))
                        .asHours();

                    if (timeDifferenceInHours > 1) {
                        return res.sendError(
                            "You can create 1 hour sessions only."
                        );
                    }

                    const sessionStart = moment(appointment.start);
                    const sessionEnd = moment(appointment.end);
                    const sessionDuration = moment.duration(sessionEnd.diff(sessionStart)).asMinutes();

                    let appointementsInTheCurrentSlots =
                        await AppointmentDao.getAppointmentsOfTherapistByStartTime(
                            new Date(appointment.start),
                            new Date(appointment.end),
                            sessionDuration,
                            Types.ObjectId(appointment.therapistId)
                        );

                    if (appointementsInTheCurrentSlots.length > 0) {
                        return res.sendError("Therapist has already scheduled an appointment during the selected time slot.");
                    }

                    user = await UserDao.signUpWithEmailPublic(
                        firstname,
                        lastname,
                        email,
                        username,
                        dateOfBirth,
                        ethnicityId,
                        gender,
                        primaryPhone,
                        state,
                        zipCode,
                        password,
                        role,
                        PersonalizeMatchData,
                        skip
                    );

                    if (user) {
                        const appointmentRes = await scheduleAppointmentWhenClientSignup(therapistId, user?._id, appointment);
                        
                        if(!appointmentRes.success){
                            appointmentCreateResMessage = appointmentRes?.message;
                            // return res.sendError(appointmentRes?.message)
                        }
                    }
                } else {
                    user = await UserDao.signUpWithEmailPublic(
                        firstname,
                        lastname,
                        email,
                        username,
                        dateOfBirth,
                        ethnicityId,
                        gender,
                        primaryPhone,
                        state,
                        zipCode,
                        password,
                        role,
                        PersonalizeMatchData,
                        skip
                    );
                }

                if (!user) {
                    return res.sendError("User signup failed! Please try again later.");
                }

                if (referralCode && user) {
                    await ClientReferralDao.addDetails(
                        referralCode,
                        user._id
                    );
                }

                let code = Math.floor(Math.random() * (999999 - 100000 + 1) + 100000);

                verificationCode = code.toString();

                const updatedUser: any = {
                    verificationCode: await Util.passwordHashing(verificationCode),
                };

                let userWithVerificationCode = await UserDao.updateUser(
                    user._id,
                    updatedUser
                );
                if (!userWithVerificationCode) {
                    return res.sendError("Something went wrong with verification code.");
                }

                let isEmailSent = await EmailService.sendVerifyEmail(
                    user,
                    "Lavni - Verify your email.",
                    verificationCode,
                    "Thank you for signing up with Lavni!",
                    "To proceed with your account you have to verify your email. Please enter the following OTP in the verify section."
                );

                await EmailService.sendAdminEmailWhenTherapistOrClientSignUp(
                    "New Client Registration",
                    "Welcome! We have a new client on board. The client has signed up with the email address: ",
                    user.email
                );

                if (user.primaryPhone) {
                    await SMSService.sendEventSMS(
                        `Thank you for signing up with Lavni!, To proceed with your account you have to verify your email. Please enter the following OTP in the verify section ${verificationCode}.`,
                        user.primaryPhone
                    );
                }


                if (user) {
                    const finalMessage = appointmentCreateResMessage
                        ? `${appointmentCreateResMessage}`
                        : `You have been registered successfully.`;
                    return res.sendSuccess(user, finalMessage);
                } else {
                    return res.sendError("Registered failed. Please try again.");
                }
            } catch (error) {
                return res.sendError(error);
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    async function scheduleAppointmentWhenClientSignup(
        therapistId: any,
        clientId: any,
        appointment: any,
    ): Promise<any> {
        try {
            const requestDetails: DFriendRequest = {
                clientId: clientId,
                therapistId: therapistId,
                status: FriendRequestStatus.APPROVED
            };

            let addedFriendRequest = await FriendRequestDao.createRequestByClient(
                requestDetails
            );

            await UserDao.updateRequestByUserId(therapistId, addedFriendRequest._id);

            await UserDao.updateRequestByUserId(clientId, addedFriendRequest._id);
            const updateUser: any = {
                primaryTherapist: Types.ObjectId(therapistId)
            };
            await AdminDao.updateUser(clientId, updateUser);

            await ChatEp.createChatInnerFunction(therapistId, clientId);

            const appointmentCreateRes = await createAppointmentByClientPublicSignup(appointment, clientId);

            if (!appointmentCreateRes.success){
                AppLogger.error(`schedule-Appointment-When-Client-Signup 1. clientId: ${clientId}. therapistId: ${therapistId}. - Error: ${appointmentCreateRes?.message}`);
                return {
                    success: false,
                    message: appointmentCreateRes?.message
                }
            }
            return {
                success: true,
                message: appointmentCreateRes?.message
            }
        } catch (error) {
            // return res.sendError(error);
            AppLogger.error(`schedule-Appointment-When-Client-Signup 2. clientId: ${clientId}. therapistId: ${therapistId}. - Error: ${error}`);
            return {
                success: false,
                message: error
            }
        }
    }

    function generateWeeklyAndBiWeeklyMessageForSent (appointmentDates: string[], type: string) {
        const formattedDatesString = appointmentDates
          .map(dateStr => moment.tz(dateStr, "America/New_York").format("MM/DD/YYYY"))
          .join(", ");
    
        const messageToSent = `Congratulations! New ${type} appointments have been scheduled on ${formattedDatesString} by`;
        return messageToSent;
      }

    async function createAppointmentByClientPublicSignup(
        appointment: any,
        clientId: any,
    ): Promise<any> {
        const therapistId = appointment.therapistId;

        try {
            let therapist = await TherapistDao.getUserById(therapistId);

            let client = await ClientDao.getUserById(clientId);

            if (appointment.repeatInfo?.repeatType == RepeatType.DOES_NOT_REPEAT) {

                
                if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
                    return {
                        success: false,
                        message: "Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date."
                    };
                    // return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
                }

                const appointmentDetails: DAppointment = {
                    therapistId: therapistId,
                    clientId: clientId,
                    start: new Date(appointment.start),
                    end: new Date(appointment.end),
                    title: appointment.title,
                    reminders: appointment.reminders,
                    typeOfMeeting: "VIDEO",
                    createdBy: clientId,
                    color: appointment.color,
                    groupId: clientId + appointment.groupId,
                    status: AppointmentStatus.PENDING,
                    approvedStatus: ApprovalStatus.APPROVED,
                    repeatInfo: {
                        repeatType: appointment.repeatInfo.repeatType,
                        interval: appointment.repeatInfo.interval,
                        repeatDays: {
                            sunday: appointment.repeatInfo.repeatDays.sunday,
                            monday: appointment.repeatInfo.repeatDays.monday,
                            tuesday: appointment.repeatInfo.repeatDays.tuesday,
                            wednesday: appointment.repeatInfo.repeatDays.wednesday,
                            thursday: appointment.repeatInfo.repeatDays.thursday,
                            friday: appointment.repeatInfo.repeatDays.friday,
                            saturday: appointment.repeatInfo.repeatDays.saturday,
                        },
                        endingDate: appointment.repeatInfo.endingDate,
                        endingAfter: appointment.repeatInfo.endingAfter,
                        endingType: appointment.repeatInfo.endingType,
                    }
                };

                const newAppointment = await AppointmentDao.createAppointment(
                    appointmentDetails
                );

                const utcTime = moment.utc(appointment.start);

                const estTime = utcTime.tz('America/New_York');

                await EmailService.sendEventEmail(
                    therapist,
                    "New appointment is created!",
                    `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by`,
                    "Click here to connect with the client.",
                    client.firstname + " " + client.lastname
                );

                if (therapist?.primaryPhone) {
                    await SMSService.sendEventSMS(
                        `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by ${client.firstname} ${client.lastname}`,
                        therapist?.primaryPhone
                    );
                }

                return {
                    success: true,
                    message: 'Your appointment is scheduled successfully.'
                }

            } else if (appointment.repeatInfo?.repeatType == RepeatType.WEEKLY) {
                const sessionStart = moment(appointment.start);
                const sessionEnd = moment(appointment.end);

                const sessionDuration = moment.duration(sessionEnd.diff(sessionStart)).asMinutes();

                const appointmentStartTime = moment(appointment.start).format("YYYY-MM-DD HH:mm");
                const appointmentEndTime = moment(appointment.end).format("YYYY-MM-DD HH:mm");

                const dateAStart = moment(appointmentStartTime);
                const dateAEnd = moment(appointmentEndTime);

                const dateBStart = moment(appointmentStartTime).add(7, "days");
                const dateBEnd = moment(appointmentEndTime).add(7, "days");

                const dateCStart = moment(appointmentStartTime).add(14, "days");
                const dateCEnd = moment(appointmentEndTime).add(14, "days");

                const dateDStart = moment(appointmentStartTime).add(21, "days");
                const dateDEnd = moment(appointmentEndTime).add(21, "days");

                const blockedWeeks: any = [];
                const dates = [
                    { start: dateAStart, week: "first" },
                    { start: dateBStart, week: "second" },
                    { start: dateCStart, week: "third" },
                    { start: dateDStart, week: "fourth" }
                ];

                dates.forEach((date, index) => {
                    if (Util.validateBlockedDates(date.start, therapist.blockedDates)) {
                    blockedWeeks.push(date.week); 
                    }
                });

                const scheduledWeeks: string[] = [];
                const timeSlotsForCheck: any = [
                    { start: dateAStart, end: dateAEnd, week: "first" },
                    { start: dateBStart, end: dateBEnd, week: "second" },
                    { start: dateCStart, end: dateCEnd, week: "third" },
                    { start: dateDStart, end: dateDEnd, week: "fourth" }
                ];

                for (const date of timeSlotsForCheck) {
                    const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
                        date.start,
                        date.end,
                        sessionDuration,
                        Types.ObjectId(appointment.therapistId)
                    );

                    if (appointmentsInCurrentSlot.length > 0) {
                        scheduledWeeks.push(date.week); // Push the week if appointments are found
                    }
                }

                if (scheduledWeeks.length > 0) {
                    console.log("Already scheduled appointments in above weeks for therapist", scheduledWeeks)
                }

                const appointmentDetailsObj: DAppointment = {
                    therapistId: therapistId,
                    clientId: clientId,
                    start: dateAStart,
                    end: dateAEnd,
                    title: appointment.title,
                    reminders: appointment.reminders,
                    typeOfMeeting: "VIDEO",
                    createdBy: clientId,
                    color: appointment.color,
                    groupId: appointment.groupId,
                    status: AppointmentStatus.PENDING,
                    approvedStatus: ApprovalStatus.APPROVED,
                    repeatInfo: {
                        repeatType: appointment.repeatInfo.repeatType,
                        interval: appointment.repeatInfo.interval,
                        repeatDays: {
                            sunday: appointment.repeatInfo.repeatDays.sunday,
                            monday: appointment.repeatInfo.repeatDays.monday,
                            tuesday: appointment.repeatInfo.repeatDays.tuesday,
                            wednesday: appointment.repeatInfo.repeatDays.wednesday,
                            thursday: appointment.repeatInfo.repeatDays.thursday,
                            friday: appointment.repeatInfo.repeatDays.friday,
                            saturday: appointment.repeatInfo.repeatDays.saturday,
                        },
                        endingDate: appointment.repeatInfo.endingDate,
                        endingAfter: appointment.repeatInfo.endingAfter,
                        endingType: appointment.repeatInfo.endingType,
                    },
                };
                const appointmentScheduledDatesForSendWeeklyMessage = [];
                if( !blockedWeeks.includes('first') && !scheduledWeeks.includes('first')) {
                    await AppointmentDao.createAppointment(appointmentDetailsObj);
                    appointmentScheduledDatesForSendWeeklyMessage.push(dateAStart);
                }
                
                if( !blockedWeeks.includes('second') && !scheduledWeeks.includes('second')) {
                    appointmentDetailsObj.start = dateBStart;
                    appointmentDetailsObj.end = dateBEnd;
                    await AppointmentDao.createAppointment(appointmentDetailsObj);
                    appointmentScheduledDatesForSendWeeklyMessage.push(dateBStart);
                }

                if( !blockedWeeks.includes('third') && !scheduledWeeks.includes('third')) {
                    appointmentDetailsObj.start = dateCStart;
                    appointmentDetailsObj.end = dateCEnd;
                    await AppointmentDao.createAppointment(appointmentDetailsObj);
                    appointmentScheduledDatesForSendWeeklyMessage.push(dateCStart);
                }
                

                if( !blockedWeeks.includes('fourth') && !scheduledWeeks.includes('fourth')) {
                    appointmentDetailsObj.start = dateDStart;
                    appointmentDetailsObj.end = dateDEnd;
                    await AppointmentDao.createAppointment(appointmentDetailsObj);
                    appointmentScheduledDatesForSendWeeklyMessage.push(dateDStart);
                }
                
                if (appointmentScheduledDatesForSendWeeklyMessage.length > 0){
                    const messageStringForWeeklyAppointments = generateWeeklyAndBiWeeklyMessageForSent(appointmentScheduledDatesForSendWeeklyMessage, "weekly");
                    AppLogger.info(`createAppointmentByClientPublicSignup weekly schedule, ${messageStringForWeeklyAppointments} ${client.firstname} ${client.lastname}, therapistId: ${therapistId}, clientId: ${clientId}`);
                    await EmailService.sendEventEmail(
                        therapist,
                        "New weekly appointments are created!",
                        messageStringForWeeklyAppointments,
                        "Click here to connect with the client.",
                        client.firstname + " " + client.lastname
                    );
    
                    if (therapist?.primaryPhone) {
                        await SMSService.sendEventSMS(
                            `${messageStringForWeeklyAppointments} ${client.firstname} ${client.lastname}`,
                            therapist?.primaryPhone,
                            'user-ep 01'
                        );
                    }
                }        
                
                const combinedArray = [...blockedWeeks, ...scheduledWeeks];
                const uniqueCombinedArray = [...new Set(combinedArray)];
                const uniqueToMainArray = dates.filter(item => uniqueCombinedArray.includes(item.week));
                const combinedStartString = (() => {
                    if (uniqueToMainArray.length === 0) return '';
                    const formattedDates = uniqueToMainArray.map(item =>
                      moment(item.start).tz(moment.tz.guess()).format('MMMM DD')
                    );
                    if (formattedDates.length === 1) {
                      return formattedDates[0];
                    } else if (formattedDates.length === 2) {
                      return `${formattedDates[0]} and ${formattedDates[1]}`;
                    } else if (formattedDates.length === 3) {
                      return `${formattedDates[0]}, ${formattedDates[1]} and ${formattedDates[2]}`;
                    } else if (formattedDates.length === 4) {
                      return `${formattedDates[0]}, ${formattedDates[1]}, ${formattedDates[2]} and ${formattedDates[3]}`;
                    }
                })();


                if (uniqueToMainArray.length > 0){
                    if (uniqueToMainArray.length == 4) {
                        return {
                            success: false,
                            message: `Your weekly appointments have not been scheduled due to a time conflict. Your therapist will reach out to help schedule for that weeks.`
                        }
                    } else {
                        return {
                            success: false,
                            // message: `Your weekly appointments are scheduled successfully. ${combinedStartString !== '' ? `No sessions are scheduled on ${combinedStartString} due to a scheduling conflict. Your therapist will reach out to reschedule.` : ''}`
                             message : `Your weekly appointments are scheduled successfully.${
                                combinedStartString !== ''
                                  ? `<br /><br />Note: No sessions are scheduled on ${combinedStartString} due to a scheduling conflict. Your therapist will reach out to reschedule.`
                                  : ''
                              }`
                        }
                    }
                } 

                return {
                    success: true,
                    message: 'Your weekly appointments are scheduled successfully.'
                }
            }
        } catch (error) {
            // return res.sendError(error);
            AppLogger.error(`create-Appointment-By-Client-Public-Signup. clientId: ${clientId}. therapistId: ${appointment.therapistId}. - Error: ${error}`);
            return {
                success: false,
                message: error
            }
        }
    }

    export async function resendSignUpVerification(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            const userId = req.body.userId;

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }
            if (!userId) {
                return res.sendError('Invalid UserId');
            }
            let user = await UserDao.getUserById(userId);

            if (!user) {
                return res.sendError("User signup failed! Please try again later.");
            }

            if (user.role == UserRole.THERAPIST) {

                const therapist = await Therapist.findById(user._id);

                if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                    return res.sendError(
                        therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                    );
                }

            }

            let code = Math.floor(Math.random() * (999999 - 100000 + 1) + 100000);
            const verificationCode = code.toString();

            const updatedUser: any = {
                verificationCode: await Util.passwordHashing(verificationCode),
            };

            let userWithVerificationCode = await UserDao.updateUser(
                user._id,
                updatedUser
            );

            if (!userWithVerificationCode) {
                return res.sendError("Something went wrong with verification code.");
            }

            let isEmailSent = await EmailService.sendVerifyEmail(
                user,
                "Lavni - Verify your email.",
                verificationCode,
                "Thank you for signing up with Lavni!",
                "To proceed with your account you have to verify your email. Please enter the following OTP in the verify section."
            );

            if (user.primaryPhone) {
                await SMSService.sendEventSMS(
                    `Thank you for signing up with Lavni!, To proceed with your account you have to verify your email. Please enter the following OTP in the verify section ${verificationCode}.`,
                    user.primaryPhone
                );
            }

            return res.sendSuccess(user, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function loginWithEmail(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const emailData = req.body.email;
        const email = emailData;
        const password = req.body.password;
        const medium = req.body.medium;
        let verificationCode = null;
        console.log("email", email);
        console.log("password", password);
        console.log("medium", medium);

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        try {
            if (medium == Medium.EMAIL) {
                let user = await UserDao.getUserByEmail(email);
                console.log("user ", user);

                if (user) {
                    if (!user.blockedByAdmin) {
                        if (user.verifiedStatus == UserStatus.VERIFIED) {
                            let isMatch;

                            if (user.password) {
                                isMatch = await user.comparePassword(password);
                            } else {
                                return res.sendError(
                                    "You have signed up to this account using social login. Please check."
                                );
                            }

                            if (isMatch) {
                                if (user.role == UserRole.THERAPIST) {
                                    const therapist = await Therapist.findById(user._id);

                                    if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                                        return res.sendError(
                                            therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                                        );
                                    }

                                    let code = Math.floor(
                                        Math.random() * (999999 - 100000 + 1) + 100000
                                    );

                                    await EmailService.sendVerifyEmail(
                                        user,
                                        "Lavni - Confirmation Code",
                                        code.toString(),
                                        "Welcome To Lavni!",
                                        "Here is your login verification code."
                                    );

                                    if (user.primaryPhone) {
                                        await SMSService.sendEventSMS(
                                            `Welcome To Lavni!, Here is your login verification code ${code.toString()}.`,
                                            user.primaryPhone
                                        );
                                    }

                                    try {
                                        verificationCode = code.toString();

                                        const updatedUser: any = {
                                            verificationCode: await Util.passwordHashing(
                                                verificationCode
                                            ),
                                            loginVerification: "PENDING",
                                        };

                                        let userWithVerificationCode = await UserDao.updateUser(
                                            user._id,
                                            updatedUser
                                        );

                                        return res.sendSuccess(userWithVerificationCode, "Success");
                                    } catch (error) {
                                        return res.sendError(error);
                                    }
                                } else if (user.role == UserRole.CLIENT) {
                                    let authToken = user.createAccessToken();

                                    res.cookie("token", authToken, {
                                        httpOnly: true,
                                        secure: false,
                                        maxAge: 24 * 60 * 60 * 1000
                                    });

                                    const data = {
                                        authToken: authToken,
                                        user: user
                                    };

                                    return res.sendSuccess(data, "Successfully Logged.");
                                } else if (user.role == UserRole.SUPER_ADMIN) {
                                    let authToken = user.createAccessToken();

                                    res.cookie("token", authToken, {
                                        httpOnly: true,
                                        secure: false,
                                        maxAge: 24 * 60 * 60 * 1000
                                    });

                                    const data = {
                                        authToken: authToken,
                                        user: user
                                    };

                                    return res.sendSuccess(data, "Successfully Logged.");
                                } else if (user.role == UserRole.SUB_ADMIN) {
                                    let authToken = user.createAccessToken();

                                    res.cookie("token", authToken, {
                                        httpOnly: true,
                                        secure: false,
                                        maxAge: 24 * 60 * 60 * 1000,
                                    });

                                    const data = {
                                        authToken: authToken,
                                        user: user
                                    };

                                    return res.sendSuccess(data, "Successfully Logged.");
                                } else {
                                    return res.sendError("Invalid user role");
                                }
                            } else {
                                return res.sendError("Incorrect email/password combination.");
                            }
                        } else {
                            return res.sendError("User not verified.");
                        }
                    } else {
                        return res.sendError(
                            "Your account has been suspended. Please reach out to <NAME_EMAIL> for further assistance."
                        );
                    }
                } else {
                    return res.sendError("User not found.");
                }
            } else {
                return res.sendError("Invalid login attempt.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function therapistLoginWithEmail(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const verificationCode = req.body.verificationCode;
        const role = req.body.role;
        const userId = req.body.userId;

        if (role == UserRole.THERAPIST) {
            try {
                let user = await UserDao.getUserByUserId(userId);

                if (user) {
                    if (user.role == UserRole.THERAPIST) {

                        const therapist = await Therapist.findById(user._id);

                        if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                            return res.sendError(
                                therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                            );
                        }

                    }

                    if (user.role !== UserRole.CLIENT) {
                        if (user.verifiedStatus == UserStatus.VERIFIED) {
                            if (user.verificationCode) {
                                const isMatch = await user.compareVerificationCode(
                                    verificationCode
                                );

                                if (isMatch) {
                                    const data: any = {
                                        loginVerification: UserStatus.VERIFIED,
                                    };

                                    var isLoginVerified = await UserDao.updateUser(
                                        user._id,
                                        data
                                    );

                                    var isVerificationCodeDeleted = await UserDao.unSetField(
                                        user._id
                                    );

                                    if (isLoginVerified && isVerificationCodeDeleted) {

                                        const token = user.createAccessToken();

                                        res.cookie("token", token, {
                                            httpOnly: true,
                                            secure: false,
                                            maxAge: 24 * 60 * 60 * 1000,
                                        });

                                        return res.sendSuccess(token, "Success");
                                    }
                                } else {
                                    return res.sendError("Invalid user verification code.");
                                }
                            } else {
                                return res.sendError("Already verified");
                            }
                        } else {
                            return res.sendError("User not verified.");
                        }
                    } else {
                        return res.sendError("Invalid user role");
                    }
                } else {
                    return res.sendError("User not found");
                }
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role");
        }
    }

    export async function therapistLoginWithoutVerification(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const emailData = req.body.email;
        const email = emailData;
        const password = req.body.password;
        const medium = req.body.medium;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        try {
            if (medium == Medium.EMAIL) {
                let user = await UserDao.getUserByEmail(email);

                if (user) {
                    if (!user.blockedByAdmin) {
                        if (user.verifiedStatus === UserStatus.VERIFIED) {
                            let isMatch;

                            if (user.password) {
                                isMatch = await user.comparePassword(password);
                            } else {
                                return res.sendError(
                                    "You have signed up to this account using social login. Please check."
                                );
                            }

                            if (isMatch) {
                                if (user.role == UserRole.THERAPIST) {

                                    const therapist = await Therapist.findById(user._id);

                                    if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                                        return res.sendError(
                                            therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                                        );
                                    }

                                    let authToken = user.createAccessToken();

                                    res.cookie("token", authToken, {
                                        httpOnly: true,
                                        secure: false,
                                        maxAge: 24 * 60 * 60 * 1000,
                                    });

                                    const data = {
                                        authToken: authToken,
                                        user: user,
                                    };
                                    return res.sendSuccess(data, "Successfully Logged.");
                                } else if (user.role == UserRole.CLIENT) {
                                    let authToken = user.createAccessToken();

                                    res.cookie("token", authToken, {
                                        httpOnly: true,
                                        secure: false,
                                        maxAge: 24 * 60 * 60 * 1000,
                                    });

                                    const data = {
                                        authToken: authToken,
                                        user: user,
                                    };
                                    return res.sendSuccess(data, "Successfully Logged.");
                                } else if (user.role == UserRole.SUPER_ADMIN || user.role == UserRole.SUB_ADMIN) {
                                    let authToken = user.createAccessToken();

                                    res.cookie("token", authToken, {
                                        httpOnly: true,
                                        secure: false,
                                        maxAge: 24 * 60 * 60 * 1000,
                                    });

                                    const data = {
                                        authToken: authToken,
                                        user: user,
                                    };
                                    return res.sendSuccess(data, "Successfully Logged.");
                                } else {
                                    return res.sendError("Invalid user role");
                                }
                            } else {
                                return res.sendError("Incorrect email/password combination.");
                            }
                        } else {
                            return res.sendError("User not verified.");
                        }
                    } else {
                        return res.sendError(
                            "Your account has been suspended. Please reach out to <NAME_EMAIL> for further assistance."
                        );
                    }
                } else {
                    return res.sendError("User not found.");
                }
            } else {
                return res.sendError("Invalid login attempt.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function logoutTherapist(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const role = req.user.role;

        if (role == UserRole.THERAPIST) {
            try {
                let user = await UserDao.getUserByUserId(userId);

                if (user.loginVerification === UserStatus.VERIFIED) {
                    const data: any = {
                        loginVerification: UserStatus.PENDING,
                    };

                    try {
                        await UserDao.updateUser(userId, data);
                    } catch (error) {
                        return res.sendError(error);
                    }
                }
            } catch (error) {
                return res.sendError(error);
            }
        }
        res.cookie("token", "", { httpOnly: true, secure: false, maxAge: 10 });
        res.sendSuccess(null, "Successfully logged out.");
    }

    export async function addClientDetailsInSignUp(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const storage = multer.diskStorage({
            destination: async (req, FileRes, cb) => {
                await clientValidationRules(req, cb);
            },
        });

        async function clientValidationRules(req: any, cb: any) {
            let destination = `${process.env.UPLOAD_PATH}/PROFILE_IMAGES`;

            try {
                let registerDetails = JSON.parse(req.body.registerDetails);

                if (!registerDetails.userId) {
                    return cb(Error("User Id is required."));
                }
                if (!mongoose.Types.ObjectId.isValid(registerDetails.userId)) {
                    return cb(Error("Invalid user Id."), null);
                }

                if (
                    !registerDetails.firstname ||
                    typeof registerDetails.firstname !== "string"
                ) {
                    return cb(Error("Fistname is required."));
                }

                if (
                    !registerDetails.lastname ||
                    typeof registerDetails.lastname !== "string"
                ) {
                    return cb(Error("Lastname is required."));
                }

                if (
                    !registerDetails.username ||
                    typeof registerDetails.username !== "string"
                ) {
                    return cb(Error("Username is required."));
                }

                if (
                    !registerDetails.dateOfBirth ||
                    typeof registerDetails.dateOfBirth !== "string" ||
                    !moment(registerDetails.dateOfBirth, "YYYY-MM-DD").isValid()
                ) {
                    return cb(Error("Date of birth is required."));
                }

                if (
                    !registerDetails.gender ||
                    typeof registerDetails.gender !== "string"
                ) {
                    return cb(Error("Gender is required."));
                }

                fs.access(destination, (error: any) => {
                    if (error) {
                        return fs.mkdir(destination, (error: any) =>
                            cb(error, "destination")
                        );
                    } else {
                        return cb(null, destination);
                    }
                });
            } catch (error) {
                return cb(Error(error), null);
            }
        }

        const upload = multer({ storage: storage }).single("photo");

        try {
            upload(req, res, async function (error: any) {
                if (error) {
                    return res.sendError(error);
                } else {
                    let isValid = true;
                    if (!req.file) {
                        try {
                            let registerDetails = JSON.parse(req.body.registerDetails);

                            if (!registerDetails.userId) {
                                isValid = false;
                                return res.sendError("User Id is required.");
                            }
                            if (!mongoose.Types.ObjectId.isValid(registerDetails.userId)) {
                                isValid = false;
                                return res.sendError("Invalid user Id.");
                            }

                            if (
                                !registerDetails.firstname ||
                                typeof registerDetails.firstname !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Firstname is required.");
                            }

                            if (
                                !registerDetails.lastname ||
                                typeof registerDetails.lastname !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Lastname is required.");
                            }

                            if (
                                !registerDetails.username ||
                                typeof registerDetails.username !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Username is required.");
                            }

                            if (
                                !registerDetails.dateOfBirth ||
                                typeof registerDetails.dateOfBirth !== "string" ||
                                !moment(registerDetails.dateOfBirth, "YYYY-MM-DD").isValid()
                            ) {
                                isValid = false;
                                return res.sendError("Date of birth is required.");
                            }

                            if (
                                !registerDetails.gender ||
                                typeof registerDetails.gender !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Gender is required.");
                            }
                        } catch (error) {
                            isValid = false;
                            return res.sendError(error);
                        }
                    }

                    if (isValid) {
                        let requestBody;
                        try {
                            requestBody = JSON.parse(req.body.registerDetails);
                        } catch (error) {
                            return res.sendError("Provided details are not valid.");
                        }

                        let signRequired: boolean = false;

                        if (req.body.signRequired !== undefined) {
                            signRequired = req.body.signRequired;
                        }

                        const photoDetails: any = {
                            photo: req.file,
                            signRequired: signRequired,
                        };

                        const client: DClient = {
                            firstname: requestBody.firstname,
                            lastname: requestBody.lastname,
                            dateOfBirth: requestBody.dateOfBirth,
                            gender: requestBody.gender,
                            username: requestBody.username,
                        };

                        UserDao.addClientDetailsInSignUp(
                            requestBody.userId,
                            photoDetails,
                            client
                        )
                            .then((data) => {
                                res.sendSuccess(data, "Registration Completed.");
                            })
                            .catch(next);
                    }
                }
            });
        } catch (error) {
            return res.sendError("Registration Failed! Please try again later.");
        }
    }

    export async function addTherapistDetailsInSignUp(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const storage = multer.diskStorage({
            destination: async (req, FileRes, cb) => {
                await therapistValidationRules(req, cb);
            },
        });

        async function therapistValidationRules(req: any, cb: any) {
            let destination = `${process.env.UPLOAD_PATH}/PROFILE_IMAGES`;

            try {
                let registerDetails = JSON.parse(req.body.registerDetails);

                if (!registerDetails.userId) {
                    return cb(Error("User Id is required."));
                }
                if (!mongoose.Types.ObjectId.isValid(registerDetails.userId)) {
                    return cb(Error("Invalid user Id."), null);
                }

                if (
                    !registerDetails.firstname ||
                    typeof registerDetails.firstname !== "string"
                ) {
                    return cb(Error("Fistname is requried."), null);
                }

                if (
                    !registerDetails.lastname ||
                    typeof registerDetails.lastname !== "string"
                ) {
                    return cb(Error("Lastname is requried."), null);
                }

                if (
                    !registerDetails.dateOfBirth ||
                    typeof registerDetails.dateOfBirth !== "string" ||
                    !moment(registerDetails.dateOfBirth, "YYYY-MM-DD").isValid()
                ) {
                    return cb(Error("Date of birth is requried."), null);
                }

                if (
                    !registerDetails.gender ||
                    typeof registerDetails.gender !== "string"
                ) {
                    return cb(Error("Gender is requried."), null);
                }

                if (!registerDetails.ethnicityId) {
                    return cb(Error("Ethnicity Id is required."));
                }
                if (!mongoose.Types.ObjectId.isValid(registerDetails.ethnicityId)) {
                    return cb(Error("Invalid ethnicity Id."), null);
                }

                const user = await UserDao.getUserByEmail(registerDetails.email);

                if (user != null) {
                    return res.sendError("Provided email is already taken.");
                }

                fs.access(destination, (error: any) => {
                    if (error) {
                        return fs.mkdir(destination, (error: any) =>
                            cb(error, "destination")
                        );
                    } else {
                        return cb(null, destination);
                    }
                });
            } catch (error) {
                return cb(Error(error), null);
            }
        }

        const upload = multer({ storage: storage }).single("photo");

        try {
            upload(req, res, async function (error: any) {
                if (error) {
                    return res.sendError(error);
                } else {
                    let isValid = true;
                    if (!req.file) {
                        try {
                            let registerDetails = JSON.parse(req.body.registerDetails);

                            if (!registerDetails.userId) {
                                isValid = false;
                                return res.sendError("User Id is required.");
                            }
                            if (!mongoose.Types.ObjectId.isValid(registerDetails.userId)) {
                                isValid = false;
                                return res.sendError("Invalid user Id.");
                            }

                            if (
                                !registerDetails.firstname ||
                                typeof registerDetails.firstname !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Firstname is required.");
                            }

                            if (
                                !registerDetails.lastname ||
                                typeof registerDetails.lastname !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Lastname is required.");
                            }

                            if (
                                !registerDetails.dateOfBirth ||
                                typeof registerDetails.dateOfBirth !== "string" ||
                                !moment(registerDetails.dateOfBirth, "YYYY-MM-DD").isValid()
                            ) {
                                isValid = false;
                                return res.sendError("Date of birth is required.");
                            }

                            if (
                                !registerDetails.gender ||
                                typeof registerDetails.gender !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Gender is required.");
                            }

                            if (!registerDetails.ethnicityId) {
                                isValid = false;
                                return res.sendError("Ethnicity Id is required.");
                            }
                            if (
                                !mongoose.Types.ObjectId.isValid(registerDetails.ethnicityId)
                            ) {
                                isValid = false;
                                return res.sendError("Invalid ethnicity Id.");
                            }

                            const user = await UserDao.getUserByEmail(registerDetails.email);

                            if (user != null) {
                                return res.sendError("Provided email is already taken.");
                            }
                        } catch (error) {
                            isValid = false;
                            return res.sendError(error);
                        }
                    }

                    if (isValid) {
                        let requestBody;
                        try {
                            requestBody = JSON.parse(req.body.registerDetails);
                        } catch (error) {
                            return res.sendError("Provided details are not valid.");
                        }

                        let signRequired: boolean = false;

                        if (req.body.signRequired !== undefined) {
                            signRequired = req.body.signRequired;
                        }

                        const photoDetails: any = {
                            photo: req.file,
                            signRequired: signRequired,
                        };

                        const therapist: DTherapist = {
                            firstname: requestBody.firstname,
                            lastname: requestBody.lastname,
                            dateOfBirth: requestBody.dateOfBirth,
                            gender: requestBody.gender,
                            ethnicityId: requestBody.ethnicityId,
                        };

                        UserDao.addTherapistDetailsInSignUp(
                            requestBody.userId,
                            photoDetails,
                            therapist
                        )
                            .then((data) => {
                                res.sendSuccess(data, "Registration Completed.");
                            })
                            .catch(next);
                    }
                }
            });
        } catch (error) {
            return res.sendError("Registration Failed! Please try again later.");
        }
    }

    export async function addEducationalInfo(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        let uploadCategory = UploadCategory.EDUCATIONAL_DOCUMENTS;
        let isValid: boolean = true;
        let uploadedFiles: any[] = [];
        let userId: any = "";

        const storage = multer.diskStorage({
            destination: async (req, FileRes, cb) => {
                await educationalInfoValidationRules(req, cb);
            },
        });

        async function educationalInfoValidationRules(req: any, cb: any) {
            let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

            try {
                let educationalDetails = JSON.parse(req.body.educationalDetails);
                userId = educationalDetails.userId;

                const user = await UserDao.getUserById(userId);

                if (!user) {
                    return cb(Error("User not found the provided user Id."));
                }
                if (user.role == UserRole.CLIENT) {
                    return cb(Error("Invalid user role."));
                }

                if (!educationalDetails.userId) {
                    return cb(Error("University is required."), null);
                }

                if (!mongoose.Types.ObjectId.isValid(educationalDetails.userId)) {
                    return cb(Error("Invalid user Id."), null);
                }

                if (
                    !educationalDetails.university ||
                    typeof educationalDetails.university !== "string"
                ) {
                    return cb(Error("University is required."), null);
                }

                if (
                    !educationalDetails.degree ||
                    typeof educationalDetails.degree !== "string"
                ) {
                    return cb(Error("Degree is required."), null);
                }

                if (
                    !educationalDetails.fieldOfStudy ||
                    typeof educationalDetails.fieldOfStudy !== "string"
                ) {
                    return cb(Error("Field of study is required."), null);
                }

                if (
                    !educationalDetails.startYear ||
                    typeof educationalDetails.startYear !== "string"
                ) {
                    return cb(Error("Start year is required."), null);
                }

                if (
                    !educationalDetails.endYear ||
                    typeof educationalDetails.endYear !== "string"
                ) {
                    return cb(Error("End year is required."), null);
                }

                fs.access(destination, (error: any) => {
                    if (error) {
                        return fs.mkdir(destination, (error: any) =>
                            cb(error, "destination")
                        );
                    } else {
                        return cb(null, destination);
                    }
                });
            } catch (error) {
                return cb(Error(error), null);
            }
        }

        const upload = multer({ storage: storage }).array("uploads", 3);

        try {
            upload(req, res, async function (error: any) {
                if (error) {
                    return res.sendError(error + "");
                }
                try {
                    if (req.files.length === 0) {
                        return res.sendError("Upload files not found.");
                    } else {
                        try {
                            let educationalDetails = JSON.parse(req.body.educationalDetails);

                            if (!educationalDetails.userId) {
                                isValid = false;
                                return res.sendError("User Id is required.");
                            }

                            if (!mongoose.Types.ObjectId.isValid(educationalDetails.userId)) {
                                isValid = false;
                                return res.sendError("User Id is invalid.");
                            }

                            if (
                                !educationalDetails.university ||
                                typeof educationalDetails.university !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("University is required.");
                            }

                            if (
                                !educationalDetails.degree ||
                                typeof educationalDetails.degree !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Degree is required.");
                            }

                            if (
                                !educationalDetails.fieldOfStudy ||
                                typeof educationalDetails.fieldOfStudy !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Field of study is required.");
                            }

                            if (
                                !educationalDetails.startYear ||
                                typeof educationalDetails.startYear !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Start year is required.");
                            }

                            if (
                                !educationalDetails.endYear ||
                                typeof educationalDetails.endYear !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Start year is required.");
                            }
                        } catch (error) {
                            isValid = false;
                            return res.sendError("Invalid educationalDetails json.");
                        }

                        if (isValid) {
                            let request;
                            try {
                                request = JSON.parse(req.body.educationalDetails);
                            } catch (error) {
                                res.sendError(error);
                            }

                            const uploads: any = req.files;

                            let signRequired: boolean = false;
                            if (req.body.signRequired !== undefined) {
                                signRequired = req.body.signRequired;
                            }

                            for (const upload of uploads) {
                                const data: DUpload = {
                                    userId: userId as unknown as Types.ObjectId,
                                    originalName: upload.originalname.replace(/ /g, ""),
                                    name: upload.filename,
                                    type: upload.mimetype,
                                    path: upload.path,
                                    fileSize: upload.size,
                                    extension:
                                        path.extname(upload.originalname) || req.body.extension,
                                    category: uploadCategory,
                                    signRequired: signRequired,
                                };

                                let uploadedFile = await UploadDao.createUpload(data);

                                uploadedFiles.push(uploadedFile);
                            }

                            if (uploadedFiles.length === 0) {
                                return res.sendError("Error while saving uploaded documents.");
                            } else {
                                let uploadedIds: any = uploadedFiles.map((item: any) => {
                                    return item._id;
                                });

                                const education: any = {
                                    userId: request.userId,
                                    university: request.university,
                                    degree: request.degree,
                                    fieldOfStudy: request.fieldOfStudy,
                                    startYear: request.startYear,
                                    endYear: request.endYear,
                                    reviewStatus: "PENDING",
                                    uploadId: uploadedIds,
                                };

                                try {
                                    let response = await UserDao.addEducationalInfo(education);
                                    if (response == null) {
                                        return res.sendError(
                                            "Education qualificationss could not be added."
                                        );
                                    } else {
                                        return res.sendSuccess(
                                            response,
                                            "Education qualificationss added."
                                        );
                                    }
                                } catch (error) {
                                    AppLogger.error(`Education Add 1 - Error details: ${error}`);
                                    return res.sendError(error);
                                }
                            }
                        }
                    }
                } catch (error) {
                    AppLogger.error(`Education Add 2 - Error details: ${error}`);
                    return res.sendError(error);
                }
            });
        } catch (error) {
            AppLogger.error(`Education Add 3 - Error details: ${error}`);
            return res.sendError(error);
        }
    }

    export async function editEducationalInfo(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        let uploadCategory = UploadCategory.EDUCATIONAL_DOCUMENTS;
        let userId: any = "";
        let uploadCount: number = 0;
        let deletingFiles = [];

        const storage = multer.diskStorage({
            destination: async (req, FileRes, cb) => {
                await editLicenseInfoValidationRules(req, cb);
            },
        });

        async function editLicenseInfoValidationRules(req: any, cb: any) {
            let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

            try {
                let educationalDetails = JSON.parse(req.body.educationalDetails);
                userId = educationalDetails.userId;

                if (!educationalDetails.userId) {
                    return cb(Error("User Id is required."), null);
                }

                if (!mongoose.Types.ObjectId.isValid(educationalDetails.userId)) {
                    return cb(Error("Invalid user Id."), null);
                }

                try {
                    const user = await UserDao.getUserById(userId);

                    if (!user) {
                        return cb(Error("No user for the provided user Id."), null);
                    }
                    if (user.role == UserRole.CLIENT) {
                        return cb(Error("Invalid user role."), null);
                    }
                } catch (error) {
                    return cb(Error(error), null);
                }

                if (!educationalDetails.educationId) {
                    return cb(Error("Education Id is required."), null);
                }

                if (!mongoose.Types.ObjectId.isValid(educationalDetails.educationId)) {
                    return cb(Error("Invalid license Id."), null);
                }

                try {
                    const educationData = await EducationDao.getEducationalDetailsById(
                        educationalDetails.educationId
                    );

                    deletingFiles = educationData.uploadId;
                    uploadCount = deletingFiles.length;

                    if (!educationData) {
                        return cb(
                            Error(
                                "No educational details found for the provided education id."
                            )
                        );
                    }
                } catch (error) {
                    return cb(Error(error), null);
                }

                if (
                    !educationalDetails.university ||
                    typeof educationalDetails.university !== "string"
                ) {
                    return cb(Error("University is required."), null);
                }

                if (
                    !educationalDetails.degree ||
                    typeof educationalDetails.degree !== "string"
                ) {
                    return cb(Error("Degree is required."), null);
                }

                if (
                    !educationalDetails.fieldOfStudy ||
                    typeof educationalDetails.fieldOfStudy !== "string"
                ) {
                    return cb(Error("Field of study is required."), null);
                }

                if (
                    !educationalDetails.startYear ||
                    typeof educationalDetails.startYear !== "string"
                ) {
                    return cb(Error("Start year is required."), null);
                }

                if (
                    !educationalDetails.endYear ||
                    typeof educationalDetails.endYear !== "string"
                ) {
                    return cb(Error("End year is required."), null);
                }

                if (
                    !educationalDetails.deletingUploadIds ||
                    !(educationalDetails.deletingUploadIds instanceof Array)
                ) {
                    return cb(Error("Deleting upload id should be an array.")), null;
                }

                if (educationalDetails.deletingUploadIds.length !== 0) {
                    for (let upload of educationalDetails.deletingUploadIds) {
                        if (!deletingFiles.includes(upload)) {
                            return cb(Error("Invalid deleting upload ids."), null);
                        }
                    }
                }

                fs.access(destination, (error: any) => {
                    if (error) {
                        return fs.mkdir(destination, (error: any) =>
                            cb(error, "destination")
                        );
                    } else {
                        return cb(null, destination);
                    }
                });
            } catch (error) {
                return cb(Error("Invalid lisence details."), null);
            }
        }

        const upload = multer({ storage });

        try {
            upload.array("uploads", 3)(req, res, async function (error: any) {
                if (error) {
                    return res.sendError(error + " ");
                }

                let educationalDetails: any;
                try {
                    if (req.files.length === 0) {
                        try {
                            educationalDetails = JSON.parse(req.body.educationalDetails);
                            userId = educationalDetails.userId;
                        } catch (error) {
                            return res.sendError(error);
                        }

                        if (!educationalDetails.userId) {
                            return res.sendError("User id is required.");
                        }

                        if (!mongoose.Types.ObjectId.isValid(educationalDetails.userId)) {
                            return res.sendError("Invalid user Id.");
                        }

                        try {
                            const user = await UserDao.getUserById(userId);

                            if (!user) {
                                return res.sendError("No user for the provided user Id.");
                            }
                            if (user.role == UserRole.CLIENT) {
                                return res.sendError("Invalid user role.");
                            }
                        } catch (error) {
                            return res.sendError(error);
                        }

                        if (!educationalDetails.educationId) {
                            return res.sendError("License Id is required.");
                        }

                        if (
                            !mongoose.Types.ObjectId.isValid(educationalDetails.educationId)
                        ) {
                            return res.sendError("Invalid license Id.");
                        }

                        try {
                            const educationData =
                                await EducationDao.getEducationalDetailsById(
                                    educationalDetails.educationId
                                );

                            if (!educationData) {
                                return res.sendError(
                                    "Cannot find educational details for the given Id."
                                );
                            }

                            deletingFiles = educationData.uploadId;
                            uploadCount = deletingFiles.length;
                        } catch (error) {
                            return res.sendError(error);
                        }

                        if (
                            !educationalDetails.university ||
                            typeof educationalDetails.university !== "string"
                        ) {
                            return res.sendError("University is required.");
                        }

                        if (
                            !educationalDetails.degree ||
                            typeof educationalDetails.degree !== "string"
                        ) {
                            return res.sendError("Degree is required");
                        }

                        if (
                            !educationalDetails.fieldOfStudy ||
                            typeof educationalDetails.fieldOfStudy !== "string"
                        ) {
                            return res.sendError("Field of study is required.");
                        }

                        if (
                            !educationalDetails.startYear ||
                            typeof educationalDetails.startYear !== "string"
                        ) {
                            return res.sendError("Start year is required.");
                        }

                        if (
                            !educationalDetails.endYear ||
                            typeof educationalDetails.endYear !== "string"
                        ) {
                            return res.sendError("End year is required.");
                        }

                        if (
                            !educationalDetails.deletingUploadIds ||
                            !(educationalDetails.deletingUploadIds instanceof Array)
                        ) {
                            return res.sendError("Deleting upload id should be an array.");
                        }

                        if (educationalDetails.deletingUploadIds.length !== 0) {
                            for (let upload of educationalDetails.deletingUploadIds) {
                                if (!deletingFiles.includes(upload)) {
                                    return res.sendError("Invalid deleting upload Ids");
                                }
                            }
                        }

                        let previouseducationalDetails: IEducation = null;
                        let previousUploadIds: any[] = null;

                        try {
                            previouseducationalDetails =
                                await EducationDao.getEducationalDetailsById(
                                    educationalDetails.educationId
                                );
                        } catch (error) {
                            return res.sendError("Invalid education id");
                        }

                        previousUploadIds = previouseducationalDetails.uploadId;

                        if (
                            previousUploadIds.length ===
                            educationalDetails.deletingUploadIds.length
                        ) {
                            return res.sendError("At least one document should be attached.");
                        }

                        async function trimData(id: any) {
                            for (
                                var i = 0;
                                i < previouseducationalDetails.uploadId.length;
                                i++
                            ) {
                                if (previousUploadIds[i].toString() === id.toString()) {
                                    previousUploadIds.splice(i, 1);
                                }
                            }
                        }

                        async function deleteFiles() {
                            for (let id of educationalDetails.deletingUploadIds) {
                                let resultHandler = async function (err: any) {
                                    if (err) {
                                        throw err;
                                    }
                                };
                                try {
                                    let upload = await UploadDao.getUpload(id);
                                    await fs.unlink(upload.path, resultHandler);
                                    await UploadDao.deleteUploadById(id);
                                    await trimData(id);
                                } catch (error) {
                                    return res.sendError(error);
                                }
                            }
                        }

                        try {
                            await deleteFiles();
                        } catch (error) {
                            return res.sendError(
                                "Error while deleting previous files" + error
                            );
                        }

                        const newEducationalDetails: DEducation = {
                            university: educationalDetails.university,
                            degree: educationalDetails.degree,
                            fieldOfStudy: educationalDetails.fieldOfStudy,
                            startYear: educationalDetails.startYear,
                            endYear: educationalDetails.endYear,
                            uploadId: previousUploadIds,
                        };

                        try {
                            let updatedEducationalDetails =
                                await EducationDao.updateEducationalDetails(
                                    educationalDetails.educationId,
                                    newEducationalDetails
                                );

                            if (updatedEducationalDetails !== null) {
                                return res.sendSuccess(
                                    updatedEducationalDetails,
                                    "Educational qualification updated."
                                );
                            }
                        } catch (error) {
                            AppLogger.error(`Education Update (length=0) - Error details: ${error}`);
                            return res.sendError(error);
                        }
                    } else {
                        const uploads: any = req.files;
                        let newUploads = [];

                        try {
                            educationalDetails = JSON.parse(req.body.educationalDetails);
                        } catch (error) {
                            return res.sendError(error);
                        }

                        let signRequired: boolean = false;
                        if (req.body.signRequired !== undefined) {
                            signRequired = req.body.signRequired;
                        }

                        for (const upload of uploads) {
                            const data: DUpload = {
                                userId: userId as unknown as Types.ObjectId,
                                originalName: upload.originalname.replace(/ /g, ""),
                                name: upload.filename,
                                type: upload.mimetype,
                                path: upload.path,
                                fileSize: upload.size,
                                extension:
                                    path.extname(upload.originalname) || req.body.extension,
                                category: UploadCategory.EDUCATIONAL_DOCUMENTS,
                                signRequired: signRequired,
                            };

                            let uploadedFile = await UploadDao.createUpload(data);

                            newUploads.push(uploadedFile);
                        }

                        if (newUploads.length === 0) {
                            return res.sendError("Error while saving uploads");
                        } else {
                            var uploadResult: any = newUploads.map((item: any) => {
                                return item._id;
                            });

                            let previousEducationalDetails: IEducation = null;
                            let previousUploadIds: any[] = null;
                            try {
                                previousEducationalDetails =
                                    await EducationDao.getEducationalDetailsById(
                                        educationalDetails.educationId
                                    );
                            } catch (error) {
                                return res.sendError("Invalid education id");
                            }

                            previousUploadIds = previousEducationalDetails.uploadId;

                            async function trimData(id: any) {
                                for (
                                    var i = 0;
                                    i < previousEducationalDetails.uploadId.length;
                                    i++
                                ) {
                                    if (previousUploadIds[i].toString() === id.toString()) {
                                        previousUploadIds.splice(i, 1);
                                    }
                                }
                            }

                            async function deleteFiles() {
                                for (let id of educationalDetails.deletingUploadIds) {
                                    let resultHandler = async function (err: any) {
                                        if (err) {
                                            throw err;
                                        }
                                    };
                                    try {
                                        let upload = await UploadDao.getUpload(id);
                                        await fs.unlink(upload.path, resultHandler);
                                        await UploadDao.deleteUploadById(id);
                                        await trimData(id);
                                    } catch (error) {
                                        return res.sendError(error);
                                    }
                                }
                            }

                            async function updateDetails() {
                                await deleteFiles();
                                let finalUploads = previousUploadIds.concat(uploadResult);
                                let requestBody = null;

                                try {
                                    requestBody = JSON.parse(req.body.educationalDetails);
                                } catch (error) {
                                    return res.sendError("Invalid educational details");
                                }

                                const newEducationalDetails: DEducation = {
                                    university: requestBody.university,
                                    degree: requestBody.degree,
                                    fieldOfStudy: requestBody.fieldOfStudy,
                                    startYear: requestBody.startYear,
                                    endYear: requestBody.endYear,
                                    uploadId: finalUploads,
                                };

                                try {
                                    let updatedEducationalDetails =
                                        await EducationDao.updateEducationalDetails(
                                            requestBody.educationId,
                                            newEducationalDetails
                                        );

                                    if (updatedEducationalDetails) {
                                        return res.sendSuccess(
                                            updatedEducationalDetails,
                                            "Your educational details have been updated successfully."
                                        );
                                    }
                                } catch (error) {
                                    AppLogger.error(`Education Update 1 - Error details: ${error}`);
                                    return res.sendError(error);
                                }
                            }
                            try {
                                await updateDetails();
                            } catch (error) {
                                AppLogger.error(`Education Update 2 - Error details: ${error}`);
                                return res.sendError(error);
                            }
                        }
                    }
                } catch (error) {
                    AppLogger.error(`Education Update 3 - Error details: ${error}`);
                    return res.sendError(error);
                }
            });
        } catch (error) {
            AppLogger.error(`Education Update 4 - Error details: ${error}`);
            return res.sendError(error);
        }
    }

    export async function getEducationalDetailsByUserId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.params.id;

        try {
            let educationalDetails = await EducationDao.getEducationalDetailsByUserId(
                userId
            );

            return res.sendSuccess(educationalDetails, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function addLicenseInfo(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        let uploadCategory = UploadCategory.LICENSE_DOCUMENTS;
        let isValid: boolean = true;
        let uploadedFiles: any[] = [];
        let userId: any = "";

        const storage = multer.diskStorage({
            destination: async (req, FileRes, cb) => {
                await licenseInfoValidationRules(req, cb);
            },
        });

        async function licenseInfoValidationRules(req: any, cb: any) {
            let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

            try {
                let licenseDetails = JSON.parse(req.body.licenseDetails);
                userId = licenseDetails.userId;

                const user = await UserDao.getUserById(userId);

                if (!user) {
                    return cb(Error("User not found the provided user Id."));
                }
                if (user.role == UserRole.CLIENT) {
                    return cb(Error("Invalid user role."));
                }

                if (!licenseDetails.userId) {
                    return cb(Error("University is required."), null);
                }

                if (!mongoose.Types.ObjectId.isValid(licenseDetails.userId)) {
                    return cb(Error("Invalid user Id."), null);
                }

                if (!licenseDetails.title || typeof licenseDetails.title !== "string") {
                    return cb(Error("Title is required."), null);
                }

                fs.access(destination, (error: any) => {
                    if (error) {
                        return fs.mkdir(destination, (error: any) =>
                            cb(error, "destination")
                        );
                    } else {
                        return cb(null, destination);
                    }
                });
            } catch (error) {
                return cb(Error(error), null);
            }
        }

        const upload = multer({ storage: storage }).array("uploads", 3);

        try {
            upload(req, res, async function (error: any) {
                if (error) {
                    return res.sendError(error + "");
                }
                try {
                    if (req.files.length === 0) {
                        return res.sendError("Upload files not found.");
                    } else {
                        try {
                            let licenseDetails = JSON.parse(req.body.licenseDetails);

                            if (!licenseDetails.userId) {
                                isValid = false;
                                return res.sendError("User Id is required.");
                            }

                            if (!mongoose.Types.ObjectId.isValid(licenseDetails.userId)) {
                                isValid = false;
                                return res.sendError("User Id is invalid.");
                            }

                            if (
                                !licenseDetails.title ||
                                typeof licenseDetails.title !== "string"
                            ) {
                                isValid = false;
                                return res.sendError("Document title is required.");
                            }
                        } catch (error) {
                            isValid = false;
                            return res.sendError("Invalid licenseDetails json.");
                        }

                        if (isValid) {
                            let request;
                            try {
                                request = JSON.parse(req.body.licenseDetails);
                            } catch (error) {
                                res.sendError(error);
                            }

                            const uploads: any = req.files;

                            let signRequired: boolean = false;
                            if (req.body.signRequired !== undefined) {
                                signRequired = req.body.signRequired;
                            }

                            for (const upload of uploads) {
                                const data: DUpload = {
                                    userId: userId as unknown as Types.ObjectId,
                                    originalName: upload.originalname.replace(/ /g, ""),
                                    name: upload.filename,
                                    type: upload.mimetype,
                                    path: upload.path,
                                    fileSize: upload.size,
                                    extension:
                                        path.extname(upload.originalname) || req.body.extension,
                                    category: uploadCategory,
                                    signRequired: signRequired,
                                };

                                let uploadedFile = await UploadDao.createUpload(data);

                                uploadedFiles.push(uploadedFile);
                            }

                            if (uploadedFiles.length === 0) {
                                return res.sendError("Error while saving uploaded documents.");
                            } else {
                                let uploadedIds: any = uploadedFiles.map((item: any) => {
                                    return item._id;
                                });

                                const license: DLicense = {
                                    userId: request.userId,
                                    title: request.title,
                                    reviewStatus: "PENDING",
                                    uploadId: uploadedIds,
                                };

                                try {
                                    let response = await UserDao.addLicenseInfo(license);
                                    if (response == null) {
                                        return res.sendError(
                                            "License and certificates could not be added."
                                        );
                                    } else {
                                        return res.sendSuccess(
                                            response,
                                            "License and certificates added."
                                        );
                                    }
                                } catch (error) {
                                    AppLogger.error(`License Add 1 - Error details: ${error}`);
                                    return res.sendError(error);
                                }
                            }
                        }
                    }
                } catch (error) {
                    AppLogger.error(`License Add 2 - Error details: ${error}`);
                    return res.sendError(error);
                }
            });
        } catch (error) {
            AppLogger.error(`License Add 3 - Error details: ${error}`);
            return res.sendError(error);
        }
    }

    export async function editLicenseInfo(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        let uploadCategory = UploadCategory.LICENSE_DOCUMENTS;
        let userId: any = "";
        let uploadCount: number = 0;
        let deletingFiles = [];

        const storage = multer.diskStorage({
            destination: async (req, FileRes, cb) => {
                await editLicenseInfoValidationRules(req, cb);
            },
        });

        async function editLicenseInfoValidationRules(req: any, cb: any) {
            let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

            try {
                let licenseDetails = JSON.parse(req.body.licenseDetails);
                userId = licenseDetails.userId;

                if (!licenseDetails.userId) {
                    return cb(Error("University is required."), null);
                }

                if (!mongoose.Types.ObjectId.isValid(licenseDetails.userId)) {
                    return cb(Error("Invalid user Id."), null);
                }

                try {
                    const user = await UserDao.getUserById(userId);

                    if (!user) {
                        return cb(Error("No user for the provided user Id."));
                    }
                    if (user.role == UserRole.CLIENT) {
                        return cb(Error("Invalid user role."));
                    }
                } catch (error) {
                    return cb(Error(error), null);
                }

                if (!licenseDetails.licenseId) {
                    return cb(Error("License Id is required."), null);
                }

                if (!mongoose.Types.ObjectId.isValid(licenseDetails.licenseId)) {
                    return cb(Error("Invalid license Id."), null);
                }

                try {
                    const license = await LicenseDao.getLicenseDetailsById(
                        licenseDetails.licenseId
                    );

                    deletingFiles = license.uploadId;
                    uploadCount = deletingFiles.length;

                    if (!license) {
                        return cb(Error("No license for the provided license Id."));
                    }
                } catch (error) {
                    return cb(Error(error), null);
                }

                if (!licenseDetails.title || typeof licenseDetails.title !== "string") {
                    return cb(Error("Title is required."), null);
                }

                if (
                    !licenseDetails.deletingUploadIds ||
                    !(licenseDetails.deletingUploadIds instanceof Array)
                ) {
                    return cb(Error("Deleting upload id should be an array."));
                }

                if (licenseDetails.deletingUploadIds.length !== 0) {
                    for (let upload of licenseDetails.deletingUploadIds) {
                        if (!deletingFiles.includes(upload)) {
                            return cb(Error("Invalid deleting upload Ids"), null);
                        }
                    }
                }
                fs.access(destination, (error: any) => {
                    if (error) {
                        return fs.mkdir(destination, (error: any) =>
                            cb(error, "destination")
                        );
                    } else {
                        return cb(null, destination);
                    }
                });
            } catch (error) {
                return cb(Error("Invalid lisence details"), null);
            }
        }

        const upload = multer({ storage });

        try {
            upload.array("uploads", 3)(req, res, async function (error: any) {
                if (error) {
                    return res.sendError(error + "");
                }

                let licenseDetails: any;
                try {
                    if (req.files.length === 0) {
                        try {
                            licenseDetails = JSON.parse(req.body.licenseDetails);
                            userId = licenseDetails.userId;
                        } catch (error) {
                            return res.sendError(error);
                        }

                        if (!licenseDetails.userId) {
                            return res.sendError("User id is required.");
                        }

                        if (!mongoose.Types.ObjectId.isValid(licenseDetails.userId)) {
                            return res.sendError("Invalid user Id.");
                        }

                        try {
                            const user = await UserDao.getUserById(userId);

                            if (!user) {
                                return res.sendError("No user for the provided user Id.");
                            }
                            if (user.role == UserRole.CLIENT) {
                                return res.sendError("Invalid user role.");
                            }
                        } catch (error) {
                            return res.sendError(error);
                        }

                        if (!licenseDetails.licenseId) {
                            return res.sendError("License Id is required.");
                        }

                        if (!mongoose.Types.ObjectId.isValid(licenseDetails.licenseId)) {
                            return res.sendError("Invalid license Id.");
                        }

                        try {
                            const license = await LicenseDao.getLicenseDetailsById(
                                licenseDetails.licenseId
                            );

                            deletingFiles = license.uploadId;
                            uploadCount = deletingFiles.length;

                            if (!license) {
                                return res.sendError("No license for the provided license Id.");
                            }
                        } catch (error) {
                            return res.sendError(error);
                        }

                        if (
                            !licenseDetails.title ||
                            typeof licenseDetails.title !== "string"
                        ) {
                            return res.sendError("Title is required.");
                        }

                        if (
                            !licenseDetails.deletingUploadIds ||
                            !(licenseDetails.deletingUploadIds instanceof Array)
                        ) {
                            return res.sendError("Deleting upload id should be an array.");
                        }

                        if (licenseDetails.deletingUploadIds.length !== 0) {
                            for (let upload of licenseDetails.deletingUploadIds) {
                                if (!deletingFiles.includes(upload)) {
                                    return res.sendError("Invalid deleting upload Ids");
                                }
                            }
                        }

                        let previousLicesnceDetails: ILicense = null;
                        let previousUploadIds: any[] = null;
                        try {
                            previousLicesnceDetails = await LicenseDao.getLicenseDetailsById(
                                licenseDetails.licenseId
                            );
                        } catch (error) {
                            return res.sendError("Invalid license id");
                        }

                        previousUploadIds = previousLicesnceDetails.uploadId;

                        if (
                            previousUploadIds.length ===
                            licenseDetails.deletingUploadIds.length
                        ) {
                            return res.sendError("At least one document should be attached.");
                        }

                        async function trimData(id: any) {
                            for (
                                var i = 0;
                                i < previousLicesnceDetails.uploadId.length;
                                i++
                            ) {
                                if (previousUploadIds[i].toString() === id.toString()) {
                                    previousUploadIds.splice(i, 1);
                                }
                            }
                        }

                        async function deleteFiles() {
                            for (let id of licenseDetails.deletingUploadIds) {
                                let resultHandler = async function (err: any) {
                                    if (err) {
                                        throw err;
                                    }
                                };
                                try {
                                    let upload = await UploadDao.getUpload(id);
                                    await fs.unlink(upload.path, resultHandler);
                                    await UploadDao.deleteUploadById(id);
                                    await trimData(id);
                                } catch (error) {
                                    return res.sendError(error);
                                }
                            }
                        }

                        try {
                            await deleteFiles();
                        } catch (error) {
                            return res.sendError(
                                "Error while deleting previous files" + error
                            );
                        }

                        const licenseData: DLicense = {
                            title: licenseDetails.title,
                            uploadId: previousUploadIds,
                        };

                        try {
                            let updatedLicense = await LicenseDao.updatedLicenseDetails(
                                licenseDetails.licenseId,
                                licenseData
                            );

                            if (updatedLicense !== null) {
                                return res.sendSuccess(updatedLicense, "License details updated successfully.");
                            }
                        } catch (error) {
                            AppLogger.error(`License Update (length=0) - Error details: ${error}`);
                            return res.sendError(error);
                        }
                    } else {
                        const uploads: any = req.files;
                        let newUploads = [];

                        try {
                            licenseDetails = JSON.parse(req.body.licenseDetails);
                        } catch (error) {
                            return res.sendError(error);
                        }

                        let signRequired: boolean = false;
                        if (req.body.signRequired !== undefined) {
                            signRequired = req.body.signRequired;
                        }

                        for (const upload of uploads) {
                            const data: DUpload = {
                                userId: userId as unknown as Types.ObjectId,
                                originalName: upload.originalname.replace(/ /g, ""),
                                name: upload.filename,
                                type: upload.mimetype,
                                path: upload.path,
                                fileSize: upload.size,
                                extension:
                                    path.extname(upload.originalname) || req.body.extension,
                                category: UploadCategory.LICENSE_DOCUMENTS,
                                signRequired: signRequired,
                            };

                            let uploadedFile = await UploadDao.createUpload(data);

                            newUploads.push(uploadedFile);
                        }

                        if (newUploads.length === 0) {
                            return res.sendError("Error while saving uploads");
                        } else {
                            var uploadResult: any = newUploads.map((item: any) => {
                                return item._id;
                            });

                            let previousLicesnceDetails: ILicense = null;
                            let previousUploadIds: any[] = null;
                            try {
                                previousLicesnceDetails =
                                    await LicenseDao.getLicenseDetailsById(
                                        licenseDetails.licenseId
                                    );
                            } catch (error) {
                                return res.sendError("Invalid license id");
                            }

                            previousUploadIds = previousLicesnceDetails.uploadId;

                            async function trimData(id: any) {
                                for (
                                    var i = 0;
                                    i < previousLicesnceDetails.uploadId.length;
                                    i++
                                ) {
                                    if (previousUploadIds[i].toString() === id.toString()) {
                                        previousUploadIds.splice(i, 1);
                                    }
                                }
                            }

                            async function deleteFiles() {
                                for (let id of licenseDetails.deletingUploadIds) {
                                    let resultHandler = async function (err: any) {
                                        if (err) {
                                            throw err;
                                        }
                                    };
                                    try {
                                        let upload = await UploadDao.getUpload(id);
                                        await fs.unlink(upload.path, resultHandler);
                                        await UploadDao.deleteUploadById(id);
                                        await trimData(id);
                                    } catch (error) {
                                        return res.sendError(error);
                                    }
                                }
                            }

                            async function updateDetails() {
                                await deleteFiles();
                                let finalUploads = previousUploadIds.concat(uploadResult);
                                let requestBody = null;

                                try {
                                    requestBody = JSON.parse(req.body.licenseDetails);
                                } catch (error) {
                                    return res.sendError("Invalid license details.");
                                }

                                const newLicensce: DLicense = {
                                    title: requestBody.title,
                                    uploadId: finalUploads,
                                };

                                try {
                                    let updatedLicense = await LicenseDao.updatedLicenseDetails(
                                        requestBody.licenseId,
                                        newLicensce
                                    );
                                    if (updatedLicense) {
                                        return res.sendSuccess(updatedLicense, "License details updated successfully.");
                                    }
                                } catch (error) {
                                    AppLogger.error(`License Update 1 - Error details: ${error}`);
                                    return res.sendError(error);
                                }
                            }
                            try {
                                await updateDetails();
                            } catch (error) {
                                AppLogger.error(`License Update 2 - Error details: ${error}`);
                                return res.sendError(error);
                            }
                        }
                    }
                } catch (error) {
                    AppLogger.error(`License Update 3 - Error details: ${error}`);
                    return res.sendError(error);
                }
            });
        } catch (error) {
            AppLogger.error(`License Update 4 - Error details: ${error}`);
            return res.sendError(error);
        }
    }

    export async function getLisenceDetailsByUserId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.params.id;

        try {
            let licenseDetails = await LicenseDao.getLicenseDetailsByUserId(userId);

            return res.sendSuccess(licenseDetails, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function verifyUserByToken(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const token = req.params.token;

        if (!token) {
            return res.sendError("Token not found.");
        }

        let user = null;

        try {
            user = jwt.verify(token, process.env.JWT_SECRET);
        } catch (error) {
            return res.sendError(error);
        }

        let updatedUser;

        try {
            let data = await UserDao.getUserById(user._id);

            if (data.verifiedStatus !== UserStatus.VERIFIED) {
                const details: any = {
                    verifiedStatus: UserStatus.VERIFIED,
                };

                const userDetails = await UserDao.updateUser(user._id, details);

                if (!userDetails) {
                    return res.sendError("Something went wrong! Please try again later.");
                }

                try {
                    updatedUser = await UserDao.unSetField(user._id);

                    if (!updatedUser) {
                        return res.sendError(
                            "Verification code could not be removed from the document"
                        );
                    }
                } catch (error) {
                    return res.sendError(error);
                }

                await EmailService.sendWelcomeEmailClient(userDetails, "Welcome to Lavni - Your Partner in Mental Health Wellness");

                if (userDetails.primaryPhone) {
                    await SMSService.sendEventSMS(
                        `Welcome To Lavni Find a therapist that aligns best with you. We strive to create a future where it is easy for anyone to access a mental health clinician. A space where you can be your authentic self while protecting your privacy.
                        \nTransparency and compliance is at the core of our electronic health records. We are a community filled with support to guide you every step of the way. Your experience matters.`,
                        userDetails.primaryPhone
                    );
                }

                return res.sendSuccess(null, "Successfully verified.");
            } else {
                return res.sendError("User is alread verified.");
            }
        } catch (error) {
            return res.sendError("User verification failed.");
        }
    }

    export async function verifyUserByCode(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const verificationCode = req.body.verificationCode;
            const userId = req.body.userId;
            let user = await UserDao.getUserById(userId);
            let updatedUser = null;

            if (!user) {
                return res.sendError("Invalid user.");
            }

            if (user.role == UserRole.THERAPIST) {

                const therapist = await Therapist.findById(user._id);

                if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                    return res.sendError(
                        therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                    );
                }
            }

            if (!user.verificationCode) {
                return res.sendError("User is already verified.");
            }

            let isMatch = await user.compareVerificationCode(verificationCode);

            if (isMatch) {
                const details: any = {
                    verifiedStatus: UserStatus.VERIFIED,
                };

                const userDetails = await UserDao.updateUser(userId, details);

                if (!userDetails) {
                    return res.sendError(
                        "Something went wrong! Please try again later."
                    );
                }


                updatedUser = await UserDao.unSetField(userId);

                if (!updatedUser) {
                    return res.sendError(
                        "Verification code could not be removed from the document"
                    );
                }


                await EmailService.sendWelcomeEmailClient(user, "Welcome to Lavni - Your Partner in Mental Health Wellness");

                if (user.primaryPhone) {
                    await SMSService.sendEventSMS(
                        `Welcome To Lavni Find a therapist that aligns best with you. We strive to create a future where it is easy for anyone to access a mental health clinician. A space where you can be your authentic self while protecting your privacy.
                        \nTransparency and compliance is at the core of our electronic health records. We are a community filled with support to guide you every step of the way. Your experience matters.`,
                        user.primaryPhone
                    );
                }

                return res.sendSuccess(null, "Successfully verified.");

            } else {
                return res.sendError("Invalid verification code.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function verifyUserByLink(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const userId = req.body.userId;

            let user = await UserDao.getUserById(userId);
            let updatedUser = null;

            if (!user) {
                return res.sendError("Invalid user.");
            }

            if (user.role == UserRole.THERAPIST) {

                const therapist = await Therapist.findById(user._id);

                if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                    return res.sendError(
                        therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                    );
                }
            }

            if (!user.verificationCode) {
                return res.sendError("User is already verified.");
            }

            const details: any = {
                verifiedStatus: UserStatus.VERIFIED,
            };

            const userDetails = await UserDao.updateUser(userId, details);

            if (!userDetails) {
                return res.sendError(
                    "Something went wrong! Please try again later."
                );
            }

            await EmailService.sendWelcomeEmailClient(user, "Welcome to Lavni - Your Partner in Mental Health Wellness");

            if (user.primaryPhone) {
                await SMSService.sendEventSMS(
                    `Welcome To Lavni Find a therapist that aligns best with you. We strive to create a future where it is easy for anyone to access a mental health clinician. A space where you can be your authentic self while protecting your privacy.
                    \nTransparency and compliance is at the core of our electronic health records. We are a community filled with support to guide you every step of the way. Your experience matters.`,
                    user.primaryPhone
                );
            }

            return res.sendSuccess(null, "Successfully verified.");

        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function contactUs(req: Request, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }

            const name = req.body.name;
            const email = req.body.email;
            const problem = req.body.problem;
            const phoneNumber = req.body.phoneNumber;
            const ip = req.body.ip;

            const contactRequest: DContact = {
                name: name,
                email: email,
                problem: problem,
                phoneNumber: phoneNumber,
                ip: ip,
                isRead: false,
            };

            let response = await UserDao.contactRequest(contactRequest);

            if (response) {
                return res.sendSuccess(response, "Your contact message has been received. One of our agents will reach out to you soon.");
            } else {
                return res.sendError("Request could not be submitted. Please try again later.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function submitRating(req: Request, res: Response, next: NextFunction) {
        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }

            const name = req.body.name;
            const email = req.body.email;
            const reviewMessage = req.body.reviewMessage;
            const ratingValue = req.body.ratingValue;

            const review: DLavniReview = {
                name: name,
                email: email,
                reviewMessage: reviewMessage,
                ratingValue: ratingValue,
                status: LavniReviewStatus.PENDING
            };

            let response = await UserDao.submitReview(review);

            if (response) {
                return res.sendSuccess(response, "Thank You for your valuable review!");
            } else {
                return res.sendError("Unable to submit your review. Please try again later.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateContactUsStatus(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            let contactRequest = await UserDao.updateContactRequest(
                Types.ObjectId(req.params.contactRequestId),
                { isRead: true }
            );

            return res.sendSuccess(contactRequest, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function sendForgotPasswordMail(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);
            const email = req.body.email;

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }

            let user = await UserDao.getUserByEmail(email);

            if (!user) {
                AppLogger.error(`Reset Password - User not found for the provided email: ${email}.`);
                return res.sendError("User not found for the provided email.");
            }

            if (user.role == UserRole.THERAPIST) {

                const therapist = await Therapist.findById(user._id);

                if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                    return res.sendError(
                        therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                    );
                }
            }

            let isEmailSent = await EmailService.sendForgetPasswordEmail(
                user,
                "Reset Password"
            );

            if (isEmailSent) {
                AppLogger.info(`Reset Password - An email has been sent to userId: ${user._id}.`);
                return res.sendSuccess(null, "An email has been sent. Please check your inbox.");
            } else {
                AppLogger.error(`Reset Password - An error occured while sending the email to userId: ${user?._id}, email: ${user?.email}.`);
                return res.sendError("Error sending email. Try again.");
            }

        } catch (error) {
            AppLogger.error(`Reset Password - An error occured while sending the email. Error Detials: ${error}.`);
            return res.sendError(error);
        }
    }

    export async function resetPassword(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const password = req.body.password;
        const confirmPassword = req.body.confirmPassword;
        const token = req.body.token;
        try {
            let data = null;
            try {
                data = jwt.verify(token, process.env.JWT_SECRET);
            } catch (error) {
                return res.sendError(error);
            }

            let existingUser = await UserDao.getUserByEmail(data.email);

            if (!existingUser) {
                return res.sendError("User not found for the provided email.");
            }

            if (password === confirmPassword) {
                const newPassword = await Util.passwordHashing(password);
                const updatedUser: any = {
                    password: newPassword,
                };
                let user = await UserDao.updateUser(data._id, updatedUser);
                return res.sendSuccess(null, "Password changed.");
            } else {
                return res.sendError("Password does not match.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function viewUserProfileById(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const role = req.user.role;
        const userId = req.params.id;

        if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
            try {
                let currentUser = await UserDao.getUserByUserId(req.user._id);
                let user = await UserDao.getUserByUserId(userId);

                if (!currentUser.lavniTestAccount) {
                    if (user.lavniTestAccount) {
                        user = null;
                    }
                }

                if (!user) {
                    return res.sendError("No user found for the provided user Id.");
                }

                if (user.role == UserRole.THERAPIST) {
                    let therapist: ITherapist = user as ITherapist;

                    if (therapist.socialSecurity) {
                        let updateSocialSecurity =
                            "#####" + therapist?.socialSecurity?.slice(5, 9);
                        therapist.socialSecurity = updateSocialSecurity;
                    }

                    return res.sendSuccess(therapist, "Success");
                }

                return res.sendSuccess(user, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function viewUserProfileByIdPublic(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.params.id;

        try {
            let user = await UserDao.getUserByUserId(userId);

            if (!user) {
                return res.sendError("No user found for the provided user Id.");
            }

            return res.sendSuccess(user, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function viewUserProfileByIdAdmin(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const role = req.user.role;
        const therapistId = req.params.therapistId;
        const clientId = req.params.clientId;

        if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
            try {
                let currentUser = await UserDao.getUserByUserId(clientId);
                let user = await UserDao.getUserByUserId(therapistId);

                if (!currentUser.lavniTestAccount) {
                    if (user.lavniTestAccount) {
                        user = null;
                    }
                }

                if (!user) {
                    return res.sendError("No user found for the provided user Id.");
                }

                if (user.role == UserRole.THERAPIST) {
                    let therapist: ITherapist = user as ITherapist;

                    if (therapist.socialSecurity) {
                        let updateSocialSecurity =
                            "#####" + therapist?.socialSecurity?.slice(5, 9);
                        therapist.socialSecurity = updateSocialSecurity;
                    }

                    return res.sendSuccess(therapist, "Success");
                }

                return res.sendSuccess(user, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function deleteEducationInfo(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const educationId = Types.ObjectId(req.params.id);
        let upload: Types.ObjectId;
        let deletedFiles: any[] = [];

        try {
            let educationDetails = await EducationDao.getEducationalDetailsById(
                educationId
            );

            if (!educationDetails) {
                return res.sendError("Invalid education Id.");
            }

            for (upload of educationDetails.uploadId) {
                let deletedFile = await UploadDao.deleteUploadById(upload);
                deletedFiles.push(deletedFile);
            }

            if (deletedFiles.length === educationDetails.uploadId.length) {
                try {
                    let deletedEducationDetails =
                        await EducationDao.deleteEducationDetailsById(educationId);

                    if (deletedEducationDetails) {
                        try {
                            let therapist = await TherapistDao.getUserById(
                                educationDetails.userId
                            );
                            let educationIdList: Types.ObjectId[] = therapist.qualifications;
                            let newEducationList = educationIdList.filter(
                                (qualifications) =>
                                    qualifications.toString() !== educationId.toString()
                            );
                            if (newEducationList) {
                                const updatedList: DTherapist = {
                                    qualifications: newEducationList,
                                };
                                await UserDao.updateUser(therapist._id, updatedList);
                                return res.sendSuccess(deletedEducationDetails, "Success");
                            } else {
                                return res.sendError("Error in filtered list");
                            }
                        } catch (error) {
                            return res.sendError(error);
                        }
                    } else {
                        return res.sendError("Education details could not be deleted.");
                    }
                } catch (error) {
                    return res.sendError(error);
                }
            }
            return res.sendSuccess(educationDetails, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function deleteLicenseInfo(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const licenseId = Types.ObjectId(req.params.id);
        let upload: Types.ObjectId;
        let deletedFiles: any[] = [];

        try {
            let licenseDetails = await LicenseDao.getLicenseDetailsById(licenseId);

            if (!licenseDetails) {
                return res.sendError("Invalid license Id.");
            }

            for (upload of licenseDetails.uploadId) {
                let deletedFile = await UploadDao.deleteUploadById(upload);
                deletedFiles.push(deletedFile);
            }

            if (deletedFiles.length === licenseDetails.uploadId.length) {
                try {
                    let deletedLicenseDetails = await LicenseDao.deleteLicenseDetailsById(
                        licenseId
                    );

                    if (deletedLicenseDetails) {
                        try {
                            let therapist = await TherapistDao.getUserById(
                                licenseDetails.userId
                            );
                            let licenseIdList: Types.ObjectId[] = therapist.licenseId;
                            let newLicenseIdList = licenseIdList.filter(
                                (lisence) => lisence.toString() !== licenseId.toString()
                            );
                            if (newLicenseIdList) {
                                const updatedList: DTherapist = {
                                    licenseId: newLicenseIdList,
                                };
                                let updatedTherapist = await UserDao.updateUser(
                                    therapist._id,
                                    updatedList
                                );
                                return res.sendSuccess(deletedLicenseDetails, "Success");
                            } else {
                                return res.sendError("Error in filtered list");
                            }
                        } catch (error) {
                            return res.sendError(error);
                        }
                    } else {
                        return res.sendError("License details could not be deleted.");
                    }
                } catch (error) {
                    return res.sendError(error);
                }
            }
            return res.sendSuccess(licenseDetails, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateSocketId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        try {
            let user = await UserDao.getUserByUserId(userId);

            if (!user) {
                return res.sendError("User not found for the proivided user Id");
            }

            const newSocketId = req.body.socketId;

            let updatedUser: DClient = null;

            if (newSocketId) {
                updatedUser = {
                    socketId: newSocketId,
                };

                let user = await UserDao.updateUserForSocketIo(userId, updatedUser);

                if (!user) {
                    return res.sendError("User could not be updated.");
                }

                return res.sendSuccess(user, "Socket Id is updated successfully.");
            } else {
                return res.sendError("Invalid Socket Id");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function changePassword(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const userId = req.user._id;
            const role = req.user.role;
            const oldPassword = req.body.oldPassword;
            const newPassword = req.body.newPassword;
            const confirmPassword = req.body.confirmPassword;

            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }

            if (newPassword === confirmPassword) {

                const user: IUser = await UserDao.getUserById(userId);

                if (user) {

                    if (user.role == UserRole.THERAPIST) {

                        const therapist = await Therapist.findById(user._id);

                        if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                            return res.sendError(
                                therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                            );
                        }
                    }

                    let isMatch = await user.comparePassword(oldPassword);

                    if (isMatch) {
                        let hashedPassword = await Util.passwordHashing(newPassword);

                        const updatedPassword: DUser = {
                            password: hashedPassword,
                        };

                        try {
                            let updatedUser = await UserDao.updateUser(
                                userId,
                                updatedPassword
                            );

                            if (!updatedUser) {
                                return res.sendError("Password could not be changed.");
                            }

                            return res.sendSuccess(updatedUser, "Password changed successfully.");
                        } catch (error) {
                            return res.sendError(error);
                        }
                    } else {
                        return res.sendError("Invalid old password.");
                    }
                } else {
                    return res.sendError("User could not be found.");
                }

            } else {
                return res.sendError("Entered passwords mismatched.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function addPaymentMethod(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const cardNo = req.body.cardNo;
        const expMonth = req.body.expMonth;
        const expYear = req.body.expYear;
        const cvv = req.body.cvv;
        const isDefault: boolean = req.body.isDefault;
        const userId = req.user._id;

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        if (UserRole.CLIENT || UserRole.THERAPIST) {
            async function isDefaultCardSet(data: any) {
                if (data.length > 0) {
                    for (let item of data) {
                        if (item.isDefault) {
                            return true;
                        }
                    }
                }
                return false;
            }

            if (parseInt(expMonth) > 12) {
                return res.sendError("Invalid combination of Month and Year");
            }

            let user = null;
            let isDefaultSet = false;
            let paymentDetails = null;

            const data: Payment = {
                cardNo: cardNo,
                expMonth: expMonth,
                expYear: expYear,
                cvv: cvv,
                isDefault: isDefault,
            };

            if (isDefault) {
                try {
                    user = await UserDao.getUserById(userId);
                } catch (error) {
                    return res.sendError(error);
                }

                isDefaultSet = await isDefaultCardSet(user.paymentDetails);

                if (isDefaultSet) {
                    let list = user.paymentDetails;

                    list.forEach((item, index) => {
                        if (item.isDefault === true) {
                            list[index].isDefault = false;
                        }
                    });

                    list.push(data);

                    paymentDetails = await UserDao.updateUser(userId, {
                        paymentDetails: list,
                    });
                } else {
                    paymentDetails = await UserDao.updatePaymentDetails(userId, data);
                }
            } else {
                paymentDetails = await UserDao.updatePaymentDetails(userId, data);
            }
            return res.sendSuccess(paymentDetails, "Success");
        } else {
            return res.sendError("Invalid user role.");
        }
    }

    export async function signUpWithFacebook(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const accessToken = req.body.accessToken;
            const medium = req.body.medium;
            const facebookId = req.body.facebookId;
            const selectedRole = req.body.role;
            const referralCode = req.body.referralCode || null;

            const firstName = req.body.firstName;
            const lastName = req.body.lastName;

            if (!firstName || !lastName || firstName.trim() == "" || lastName.trim() == "") {
                return res.sendError("Please provide valid first name and last name");
            }

            if (medium === Medium.FACEBOOK) {

                const data = await fetch(
                    `https://graph.facebook.com/${facebookId}?fields=id,name,email,picture&access_token=${accessToken}`
                );

                const facebookData = await data.json();

                if (!facebookData || !facebookData.email) {
                    return res.sendError("Invalid account.");
                }

                if (facebookData.id === facebookId) {
                    const existingUser = await UserDao.getUserByEmail(facebookData.email);

                    if (!existingUser) {

                        const userData = await UserDao.signUpwithSocialMedia(
                            facebookData.id,
                            facebookData.email,
                            UserStatus.VERIFIED,
                            selectedRole,
                            Medium.FACEBOOK,
                            firstName,
                            lastName
                        );

                        if (!userData) {
                            return res.sendError("Sign up failed");
                        }

                        // res.cookie("token", data.authToken, {
                        //     httpOnly: true,
                        //     secure: false,
                        //     maxAge: 24 * 60 * 60 * 1000,
                        // });

                        if (selectedRole == UserRole.THERAPIST && referralCode && userData) {
                            await TherapistReferralDao.addDetails(
                                referralCode,
                                userData._id
                            );
                        }

                        // await EmailService.sendWelcomeEmail(
                        //     userData,
                        //     "Welcome To Lavni"
                        // );

                        await EmailService.sendAdminEmailWhenTherapistOrClientSignUp(
                            "New Therapist Registration",
                            "Welcome! We have a new therapist on board. The therapist has signed up with the email address: ",
                            userData.email
                        );

                        await EmailService.sendAdminEmailWhenTherapistToSubAdminLevel1(
                            "New Therapist Registration",
                            "Welcome! We have a new therapist on board. The therapist has signed up with the email address: ",
                            userData.email
                        );

                        return res.sendSuccess(
                            {},
                            "Successfully Registered."
                        );
                    } else {
                        return res.sendError("Provided email is already taken.");
                    }

                } else {
                    return res.sendError("Invalid user id.");
                }
            } else {
                return res.sendError("Invalid login attempt.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function signUpWithFacebookReact(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const accessToken = req.body.userData.accessToken;
        const medium = req.body.userData.medium;
        const facebookId = req.body.userData.facebookId;
        const selectedRole = req.body.userData.role;
        const firstname = req.body.userData.firstname.trim();
        const lastname = req.body.userData.lastname.trim();
        const gender = req.body.userData.gender;
        const dateOfBirth = req.body.userData.dateOfBirth;
        const ethnicityId = req.body.userData.ethnicityId;
        const username = req.body.userData.username.trim();
        const state = req.body.userData.state;
        const zipCode = req.body.userData.zipCode;
        const primaryPhone = req.body.primaryPhone;
        const skip = req.body.userData.skip;
        const PersonalizeMatchData = req.body.userData.PersonalizeMatchData;
        const therapistId = req.body.likeTherapist?.therapistId;
        const appointment = req.body.appointmentObj;
        const referralCode = req.body.referralInfo?.referralCode;
        let userData = null;

        if (medium == Medium.FACEBOOK) {
            try {
                const data = await fetch(
                    `https://graph.facebook.com/${facebookId}?fields=id,name,email,picture&access_token=${accessToken}`
                );

                const facebookData = await data.json();

                if (facebookData.id == facebookId) {
                    const existingUser = await UserDao.getUserByEmail(facebookData.email);

                    if (existingUser) {
                        return res.sendError("Provided email is already taken.");
                    }

                    const foundUser = await UserDao.getUserByUsername(username);

                    if (foundUser) {
                        return res.sendError("Username is already taken.");
                    }

                    let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
                        primaryPhone
                    );

                    if (isprimaryPhoneUsed) {
                        return res.sendError("Provided primary phone is already taken.");
                    }

                    const enteredBirthDate = new Date(dateOfBirth);

                    const currentDate = new Date();
                    const maxBirthDate = new Date(
                        currentDate.getFullYear() - 100,
                        currentDate.getMonth(),
                        currentDate.getDate()
                    );

                    if (enteredBirthDate < maxBirthDate) {
                        return res.sendError(
                            "Date of birth is more recent than 100 years ago."
                        );
                    }

                    const minBirthDate = new Date(
                        currentDate.getFullYear() - 13,
                        currentDate.getMonth(),
                        currentDate.getDate()
                    );

                    if (enteredBirthDate > minBirthDate) {
                        return res.sendError(
                            "You must be at least 13 years old to continue."
                        );
                    }

                    // Chỉ kiểm tra therapist nếu có therapistId
                    if (therapistId) {
                        const therapist = TherapistDao.getUserById(therapistId);

                        if (!therapist) {
                            return res.sendError("Invalid therapist id.");
                        }
                    }
                    
                    let appointmentCreateResMessage = '';

                    // Chỉ tạo appointment nếu có therapistId, appointment data và skip != true
                    if (skip != true && therapistId && appointment) {
                        const beginsAt = moment(appointment.start);
                        const endsAt = moment(appointment.start);

                        const startTime = moment(appointment.start).format("H:mm A");

                        const appointmentStartTime = moment(appointment.start).format(
                            "YYYY-MM-DD HH:mm"
                        );

                        const mST = moment(startTime, "HH:mm").minute();

                        if (mST != 0 && mST != 30) {
                            return res.sendError("Please select valid start time.");
                        }

                        if (!startTime) {
                            return res.sendError("Please select valid start time.");
                        }

                        const tomorrowDate: Date = moment(new Date()).add(1, "day").startOf("day").toDate();

                        if (
                            !moment(tomorrowDate).isSameOrBefore(
                                moment(
                                    new Date(appointmentStartTime).setHours(
                                        parseInt(startTime.split(":")[0]),
                                        parseInt(startTime.split(":")[1]),
                                        0,
                                        0
                                    )
                                )
                            )
                        ) {
                            return res.sendError(
                                "Sorry! You can only create appointment starting from tomorrow!"
                            );
                        }

                        const timeDifferenceInHours = moment
                            .duration(endsAt.diff(beginsAt))
                            .asHours();

                        if (timeDifferenceInHours > 1) {
                            return res.sendError(
                                "You can create 1 hour sessions only."
                            );
                        }

                        const sessionStart = moment(appointment.start);
                        const sessionEnd = moment(appointment.end);
                        const sessionDuration = moment.duration(sessionEnd.diff(sessionStart)).asMinutes();

                        let appointementsInTheCurrentSlots =
                            await AppointmentDao.getAppointmentsOfTherapistByStartTime(
                                new Date(appointment.start),
                                new Date(appointment.end),
                                sessionDuration,
                                Types.ObjectId(appointment.therapistId)
                            );

                        if (appointementsInTheCurrentSlots.length > 0) {
                            return res.sendError("Therapist has already scheduled an appointment during the selected time slot.");
                        }

                        userData = await UserDao.signUpwithClientSocialMedia(
                            facebookData.id,
                            facebookData.email,
                            UserStatus.VERIFIED,
                            selectedRole,
                            Medium.FACEBOOK,
                            firstname,
                            lastname,
                            gender,
                            dateOfBirth,
                            ethnicityId,
                            username,
                            state,
                            zipCode,
                            primaryPhone,
                            PersonalizeMatchData,
                            skip
                        );

                        const appointmentRes = await scheduleAppointmentWhenClientSignup(therapistId, userData?.userId, appointment);
                        if(!appointmentRes.success){
                            appointmentCreateResMessage = appointmentRes?.message;
                        }
                    } else {
                        userData = await UserDao.signUpwithClientSocialMedia(
                            facebookData.id,
                            facebookData.email,
                            UserStatus.VERIFIED,
                            selectedRole,
                            Medium.FACEBOOK,
                            firstname,
                            lastname,
                            gender,
                            dateOfBirth,
                            ethnicityId,
                            username,
                            state,
                            zipCode,
                            primaryPhone,
                            PersonalizeMatchData,
                            skip
                        );
                    }

                    if (!userData || !userData.userId || !userData.authToken) {
                        return res.sendError("Registration failed");
                    }

                    if (referralCode && userData) {
                        await ClientReferralDao.addDetails(
                            referralCode,
                            userData.userId
                        );
                    }

                    const savedUser = await User.findById(userData.userId);

                    if (savedUser) {
                        let isEmailSent = await EmailService.sendWelcomeEmailClient(savedUser, "Welcome to Lavni - Your Partner in Mental Health Wellness");

                        await EmailService.sendAdminEmailWhenTherapistOrClientSignUp(
                            "New Client Registration",
                            "Welcome! We have a new client on board. The client has signed up with the email address: ",
                            savedUser.email
                        );
                    }

                    const finalMessage = appointmentCreateResMessage
                        ? `${appointmentCreateResMessage}`
                        : `You have been registered successfully.`;

                    return res.sendSuccess(userData.authToken, finalMessage);

                } else {
                    return res.sendError("Invalid user id.");
                }
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid login attempt.");
        }
    }

    export async function loginWithFacebook(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const accessToken = req.body.accessToken;
        const facebookId = req.body.facebookId;
        const medium = req.body.medium;

        if (medium != Medium.FACEBOOK) {
            return res.sendError("Invalid login attempt.");
        }

        try {
            const data = await fetch(
                `https://graph.facebook.com/${facebookId}?fields=id,name,email,picture&access_token=${accessToken}`
            );

            const facebookData = await data.json();

            if (facebookData.id != facebookId) {
                return res.sendError("Login with facebook failed!");
            }
            const user = await UserDao.getUserByFacebookId(facebookId);

            if (!user) {
                return res.sendError("User does not exist in the system.");
            }

            if (user.blockedByAdmin) {
                return res.sendError("You have been banned from the Lavni app.");
            }

            if (user.role == UserRole.THERAPIST) {

                const therapist = await Therapist.findById(user._id);

                if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                    return res.sendError(
                        therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                    );
                }

            }

            let token = user.createAccessToken();

            res.cookie("token", token, {
                httpOnly: true,
                secure: false,
                maxAge: 24 * 60 * 60 * 1000,
            });

            return res.sendSuccess(token, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function signUpWithGoogle(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const idToken = req.body.idToken;
            const role = req.body.role;
            const client = new OAuth2Client(idToken.clientId);
            const referralCode = req.body.referralCode || null;

            const firstName = req.body.firstName.trim();
            const lastName = req.body.lastName.trim();

            if (!firstName || !lastName || firstName.trim() == "" || lastName.trim() == "") {
                return res.sendError("Please provide valid first name and last name");
            }

            const ticket = await client.verifyIdToken({
                idToken: idToken.credential,
                audience: idToken.clientId,
            });

            const payload = ticket.getPayload();

            if (payload.email_verified) {
                const existingUser: IUser = await UserDao.getUserByEmail(payload.email);

                if (!existingUser) {
                    const userData = await UserDao.signUpwithSocialMedia(
                        payload.sub,
                        payload.email,
                        UserStatus.VERIFIED,
                        role,
                        Medium.GOOGLE,
                        firstName,
                        lastName
                    );

                    if (!userData) {
                        return res.sendError("Sign up failed");
                    }

                    if (role == UserRole.THERAPIST && referralCode && userData) {
                        await TherapistReferralDao.addDetails(
                            referralCode,
                            userData._id
                        );
                    }

                    // await EmailService.sendWelcomeEmail(userData, "Welcome To Lavni");

                    await EmailService.sendAdminEmailWhenTherapistOrClientSignUp(
                        "New Therapist Registration",
                        "Welcome! We have a new therapist on board. The therapist has signed up with the email address: ",
                        userData.email
                    );

                    await EmailService.sendAdminEmailWhenTherapistToSubAdminLevel1(
                        "New Therapist Registration",
                        "Welcome! We have a new therapist on board. The therapist has signed up with the email address: ",
                        userData.email
                    );

                    return res.sendSuccess({}, "Successfully Registered.");
                } else {
                    return res.sendError("Provided email is already taken.");
                }
            } else {
                return res.sendError(
                    "Google account you are trying to use is not verified. Please verify the account or try signing up with your email instead."
                );
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function signUpWithGoogleReact(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const idToken = req.body.userData.idToken;
        const role = req.body.userData.role;
        
        // Tương thích ngược cho clientId
        let clientId: string;
        if (typeof idToken === 'object' && idToken !== null && 'clientId' in idToken) {
            // Format cũ: idToken = {credential: "JWT", clientId: "CLIENT_ID"}
            clientId = (idToken as any).clientId;
        } else {
            // Format mới: clientId riêng biệt trong userData
            clientId = req.body.userData.clientId;
        }
        
        const client = new OAuth2Client(clientId);
        const firstname = req.body.userData.firstname.trim();
        const lastname = req.body.userData.lastname.trim();
        const gender = req.body.userData.gender;
        const dateOfBirth = req.body.userData.dateOfBirth;
        const ethnicityId = req.body.userData.ethnicityId;
        const username = req.body.userData.username.trim();
        const state = req.body.userData.state;
        const zipCode = req.body.userData.zipCode;
        const primaryPhone = req.body.userData.primaryPhone;
        const skip = req.body.userData.skip;
        const PersonalizeMatchData = req.body.userData.PersonalizeMatchData;
        const therapistId = req.body.likeTherapist?.therapistId;
        const appointment = req.body.appointmentObj;
        const referralCode = req.body.referralInfo?.referralCode;
        let userData = null;
    
        try {
            // Tương thích ngược cho idToken
            let tokenToVerify: string;
            let audienceToUse: string;
    
            if (typeof idToken === 'object' && idToken !== null && 'credential' in idToken) {
                // Format cũ: idToken = {credential: "JWT", clientId: "CLIENT_ID"}
                tokenToVerify = (idToken as any).credential;
                audienceToUse = (idToken as any).clientId;
            } else {
                // Format mới: idToken = "JWT_STRING", clientId riêng biệt
                tokenToVerify = idToken as string;
                audienceToUse = clientId;
            }
    
            const ticket = await client.verifyIdToken({
                idToken: tokenToVerify,
                audience: audienceToUse,
            });
    
            const payload = ticket.getPayload();
    
            if (payload.email_verified) {
                const user: IUser = await UserDao.getUserByEmail(payload.email);
    
                if (user) {
                    return res.sendError("Provided email is already taken.");
                }
    
                const foundUser = await UserDao.getUserByUsername(username);
    
                if (foundUser) {
                    return res.sendError("Username is already taken.");
                }
    
                let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
                    primaryPhone
                );
    
                if (isprimaryPhoneUsed) {
                    return res.sendError("Provided primary phone is already taken.");
                }
    
                const enteredBirthDate = new Date(dateOfBirth);
    
                const currentDate = new Date();
    
                const maxBirthDate = new Date(
                    currentDate.getFullYear() - 100,
                    currentDate.getMonth(),
                    currentDate.getDate()
                );
    
                if (enteredBirthDate < maxBirthDate) {
                    return res.sendError("Date of birth is more recent than 100 years ago.");
                }
    
                const minBirthDate = new Date(
                    currentDate.getFullYear() - 13,
                    currentDate.getMonth(),
                    currentDate.getDate()
                );
    
                if (enteredBirthDate > minBirthDate) {
                    return res.sendError("You must be at least 13 years old to continue.");
                }
    
                // Chỉ kiểm tra therapist nếu có therapistId
                if (therapistId) {
                    const therapist = TherapistDao.getUserById(therapistId);
    
                    if (!therapist) {
                        return res.sendError("Invalid therapist id.");
                    }
                }
                
                let appointmentCreateResMessage = '';
    
                // Chỉ tạo appointment nếu có therapistId, appointment data và skip != true
                if (skip != true && therapistId && appointment) {
                    const beginsAt = moment(appointment.start);
                    const endsAt = moment(appointment.start);
    
                    const startTime = moment(appointment.start).format("H:mm A");
    
                    const appointmentStartTime = moment(appointment.start).format(
                        "YYYY-MM-DD HH:mm"
                    );
    
                    const mST = moment(startTime, "HH:mm").minute();
    
                    if (mST != 0 && mST != 30) {
                        return res.sendError("Please select valid start time.");
                    }
    
                    if (!startTime) {
                        return res.sendError("Please select valid start time.");
                    }
    
                    const tomorrowDate: Date = moment(new Date()).add(1, "day").startOf("day").toDate();
    
                    if (
                        !moment(tomorrowDate).isSameOrBefore(
                            moment(
                                new Date(appointmentStartTime).setHours(
                                    parseInt(startTime.split(":")[0]),
                                    parseInt(startTime.split(":")[1]),
                                    0,
                                    0
                                )
                            )
                        )
                    ) {
                        return res.sendError(
                            "Sorry! You can only create appointment starting from tomorrow!"
                        );
                    }
    
                    const timeDifferenceInHours = moment
                        .duration(endsAt.diff(beginsAt))
                        .asHours();
    
                    if (timeDifferenceInHours > 1) {
                        return res.sendError(
                            "You can create 1 hour sessions only."
                        );
                    }
    
                    const sessionStart = moment(appointment.start);
                    const sessionEnd = moment(appointment.end);
                    const sessionDuration = moment.duration(sessionEnd.diff(sessionStart)).asMinutes();
    
                    let appointementsInTheCurrentSlots =
                        await AppointmentDao.getAppointmentsOfTherapistByStartTime(
                            new Date(appointment.start),
                            new Date(appointment.end),
                            sessionDuration,
                            Types.ObjectId(appointment.therapistId)
                        );
    
                    if (appointementsInTheCurrentSlots.length > 0) {
                        return res.sendError("Therapist has already scheduled an appointment during the selected time slot.");
                    }
    
                    userData = await UserDao.signUpwithClientSocialMedia(
                        payload.sub,
                        payload.email,
                        UserStatus.VERIFIED,
                        role,
                        Medium.GOOGLE,
                        firstname,
                        lastname,
                        gender,
                        dateOfBirth,
                        ethnicityId,
                        username,
                        state,
                        zipCode,
                        primaryPhone,
                        PersonalizeMatchData,
                        skip
                    );
    
                    const appointmentRes = await scheduleAppointmentWhenClientSignup(therapistId, userData?.userId, appointment);
                    if(!appointmentRes.success){
                        appointmentCreateResMessage = appointmentRes?.message;
                    }
                } else {
                    userData = await UserDao.signUpwithClientSocialMedia(
                        payload.sub,
                        payload.email,
                        UserStatus.VERIFIED,
                        role,
                        Medium.GOOGLE,
                        firstname,
                        lastname,
                        gender,
                        dateOfBirth,
                        ethnicityId,
                        username,
                        state,
                        zipCode,
                        primaryPhone,
                        PersonalizeMatchData,
                        skip
                    );
                }
    
                if (!userData || !userData.userId || !userData.authToken) {
                    return res.sendError("Registration failed");
                }
    
                if (referralCode && userData) {
                    await ClientReferralDao.addDetails(
                        referralCode,
                        userData.userId
                    );
                }
    
                const savedUser = await User.findById(userData.userId);
    
                if (savedUser) {
                    const isEmailSent = await EmailService.sendWelcomeEmailClient(savedUser, "Welcome to Lavni - Your Partner in Mental Health Wellness");
    
                    await EmailService.sendAdminEmailWhenTherapistOrClientSignUp(
                        "New Client Registration",
                        "Welcome! We have a new client on board. The client has signed up with the email address: ",
                        savedUser.email
                    );
                }
                const finalMessage = appointmentCreateResMessage
                    ? `${appointmentCreateResMessage}.`
                    : `You have been registered successfully.`;
    
                return res.sendSuccess(userData.authToken, finalMessage);
            } else {
                return res.sendError(
                    "Google account you are trying to use is not verified. Please verify the account or try signing up with your email instead."
                );
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function loginWithGoogle(
        req: Request,
        res: Response,
        next: NextFunction
    ) {

        const idToken = req.body.idToken;

        const client = new OAuth2Client(idToken.clientId);

        try {
            const ticket = await client.verifyIdToken({
                idToken: idToken.credential,
                audience: idToken.clientId,
            });

            const payload = ticket.getPayload();

            if (!payload.email_verified) {
                return res.sendError(
                    "Google account you are trying to use is not verified. Please verify the account or try signing up with your email instead."
                );
            }
            if (payload?.email) {
                const userMediumByEmail = await UserDao.getUserMediumByEmail(payload?.email);
                console.log(userMediumByEmail)
                if (userMediumByEmail) {
                    AppLogger.info(`User trying to log in to the system using Google. But need to use ${userMediumByEmail?.medium}. Email: ${payload?.email}`)
                    if (userMediumByEmail?.medium == Medium.EMAIL) {
                        return res.sendError('Please log in to the system using email and password!');
                    } else if (userMediumByEmail?.medium == Medium.FACEBOOK){
                        return res.sendError('Please log in to the system using Facebook!');
                    }
                }
            }

            const user: IUser = await UserDao.getUserByGoogleId(payload.sub);

            if (!user) {
                return res.sendError("User does not exist in the system");
            }

            if (user.blockedByAdmin) {
                return res.sendError("You have been banned from Lavni app.");
            }

            if (user.role == UserRole.THERAPIST) {

                const therapist = await Therapist.findById(user._id);

                if (!therapist || !therapist.registrationApprovalStatus || therapist.registrationApprovalStatus != RegistrationApprovalStatus.APPROVED) {

                    return res.sendError(
                        therapist.registrationApprovalStatus == RegistrationApprovalStatus.REJECTED ? "Application rejected" : "Your account is on review state, our team member will reach out to you soon."
                    );
                }

            }

            let token = user.createAccessToken();

            res.cookie("token", token, {
                httpOnly: true,
                secure: false,
                maxAge: 24 * 60 * 60 * 1000,
            });

            return res.sendSuccess(token, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateUserNotification(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]);
            }
            const userDetails: DUser = {
                reminderType: req.body.reminderType,
                reminderTime: req.body.reminderTime,
            };

            let updatedUser = await UserDao.updateUser(userId, userDetails);
            return res.sendSuccess(updatedUser, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateProfileImage(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const uploadCategory = UploadCategory.PROFILE_IMAGE;

        const storage = multer.diskStorage({
            destination: async (req, FileRes, cb) => {
                await updateProfileImageValidationRules(req, cb);
            },
        });


        async function updateProfileImageValidationRules(req: any, cb: any) {
            let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

            try {
                fs.access(destination, (error: any) => {
                    if (error) {
                        return fs.mkdir(destination, (error: any) =>
                            cb(error, "destination")
                        );
                    } else {
                        return cb(null, destination);
                    }
                });
            } catch (error) {
                return cb(Error(error), null);
            }
        }

        async function deleteOldPhoto(uploadId: StringOrObjectId) {
            let isDeleted = false;
            let resultHandler = async function (error: any) {
                if (error) {
                    console.log("Unlink failed.", error);
                } else {
                    console.log("File deleted.");
                }
            };

            try {
                let oldPhoto = await UploadDao.getUpload(uploadId.toString());
                await fs.unlink(oldPhoto.path, resultHandler);
                await UploadDao.deleteUploadById(uploadId);
                isDeleted = true;
            } catch (error) {
                isDeleted = false;
            }

            return isDeleted;
        }

        const upload = multer({ storage: storage }).single(
            "profileImage"
        );

        try {
            upload(req, res, async function (error: any) {
                if (error) {
                    return res.sendError(error + " ");
                } else {
                    if (req.file == null || req.file === undefined) {
                        return res.sendError("Image not found.");
                    } else {
                        const image = req.file;

                        let signRequired: boolean = false;

                        if (req.body.signRequired !== undefined) {
                            signRequired = req.body.signRequired;
                        }

                        const data: DUpload = {
                            userId: userId as unknown as Types.ObjectId,
                            originalName: image.originalname.replace(/ /g, ""),
                            name: image.filename,
                            type: image.mimetype,
                            path: image.path,
                            fileSize: image.size,
                            extension: path.extname(image.originalname) || req.body.extension,
                            category: uploadCategory,
                            signRequired: signRequired,
                        };


                        try {
                            let user = await UserDao.getUserById(userId);
                            let uploadedImage: IUpload = null;

                            if (user.photoId) {
                                let isFileDeleted = await deleteOldPhoto(user.photoId);


                                if (isFileDeleted) {
                                    uploadedImage = await UploadDao.createUpload(data);

                                    if (uploadedImage == null) {
                                        return res.sendError("Error while uploading the image.");
                                    }

                                    const userDetails: DUser = {
                                        photoId: uploadedImage._id,
                                    };

                                    let updatedUser = await UserDao.updateUser(
                                        userId,
                                        userDetails
                                    );

                                    if (updatedUser == null) {
                                        return res.sendError("User could not be updated.");
                                    }

                                    return res.sendSuccess(updatedUser, "Success");
                                } else {
                                    return res.sendError("Error while deleting the file.");
                                }
                            } else if (user.photoId) {
                                let isFileDeleted = await deleteOldPhoto(user.photoId);

                                if (isFileDeleted) {
                                    uploadedImage = await UploadDao.createUpload(data);

                                    if (uploadedImage == null) {
                                        return res.sendError("Error while uploading the image.");
                                    }

                                    const userDetails: DUser = {
                                        photoId: uploadedImage._id
                                    };

                                    let updatedUser = await UserDao.updateUser(
                                        userId,
                                        userDetails
                                    );

                                    if (updatedUser == null) {
                                        return res.sendError("User could not be updated.");
                                    }

                                    return res.sendSuccess(updatedUser, "Success");
                                }
                            } else {
                                uploadedImage = await UploadDao.createUpload(data);


                                if (uploadedImage == null) {
                                    return res.sendError("Error while uploading the image.");
                                }

                                const userDetails: DUser = {
                                    photoId: uploadedImage._id
                                };

                                let updatedUser = await UserDao.updateUser(userId, userDetails);

                                if (updatedUser == null) {
                                    return res.sendError("User could not be updated.");
                                }

                                return res.sendSuccess(updatedUser, "Success");
                            }
                        } catch (error) {
                            return res.sendError(error);
                        }
                    }
                }
            });
        } catch (error) {
            return res.sendError("Failed to upload image. " + error);
        }
    }

    export async function updateProfileImageTest(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            let user = await UserDao.getAllUsers();
            user.map(async (session: any) => {
                if (session.photoId) {
                    let signRequired: boolean = false;

                    if (req.body.signRequired !== undefined) {
                        signRequired = req.body.signRequired;
                    }

                    const userDetails: DUser = {
                        photoId: session.photoId._id
                    };

                    let updatedUser = await UserDao.updateUser(
                        session.photoId.userId,
                        userDetails
                    );

                    if (updatedUser == null) {
                        return res.sendError("User could not be updated.");
                    }
                    return res.sendSuccess(updatedUser, "Success");
                }
            });
        } catch (error) {
            return res.sendError("Failed to upload image. " + error);
        }
    }

    export async function updateProfileCoverImage(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const themeId = req.body.themeId;
        const uploadCategory = UploadCategory.PROFILE_COVER_IMAGE;

        const storage = multer.diskStorage({
            destination: async (req, FileRes, cb) => {
                await updateCoverImageValidationRules(req, cb);
            },
        });

        async function updateCoverImageValidationRules(req: any, cb: any) {
            let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

            try {
                fs.access(destination, (error: any) => {
                    if (error) {
                        return fs.mkdir(destination, (error: any) =>
                            cb(error, "destination")
                        );
                    } else {
                        return cb(null, destination);
                    }
                });
            } catch (error) {
                return cb(Error(error), null);
            }
        }

        async function deleteOldPhoto(uploadId: StringOrObjectId) {
            let isDeleted = false;
            let resultHandler = async function (error: any) {
                if (error) {
                    console.log("Unlink failed.", error);
                } else {
                    console.log("File deleted.");
                }
            };

            try {
                let oldPhoto = await UploadDao.getUpload(uploadId.toString());
                if (oldPhoto.category === UploadCategory.THEMES) {
                    isDeleted = true;
                } else {
                    await fs.unlink(oldPhoto.path, resultHandler);
                    await UploadDao.deleteUploadById(uploadId);
                    isDeleted = true;
                }
            } catch (error) {
                isDeleted = false;
            }

            return isDeleted;
        }

        const upload = multer({ storage: storage }).single("profileCoverImage");

        try {
            upload(req, res, async function (error: any) {
                if (error) {
                    return res.sendError(error + " ");
                } else {
                    if (req.file == null || req.file === undefined) {
                        if (themeId) {
                            let user: any = await UserDao.getUserById(userId);

                            if (
                                user.coverPhotoId &&
                                user.coverPhotoId.category !== UploadCategory.THEMES
                            ) {
                                await deleteOldPhoto(user.coverPhotoId);
                            }

                            let updatedUser = await UserDao.updateUser(userId, {
                                coverPhotoId: themeId,
                            });

                            return res.sendSuccess(updatedUser, "Success");
                        } else {
                            return res.sendError(
                                "You have to either upload an image or select an already provided image."
                            );
                        }
                    } else {
                        const image = req.file;

                        let signRequired: boolean = false;

                        if (req.body.signRequired !== undefined) {
                            signRequired = req.body.signRequired;
                        }

                        const data: DUpload = {
                            userId: userId as unknown as Types.ObjectId,
                            originalName: image.originalname.replace(/ /g, ""),
                            name: image.filename,
                            type: image.mimetype,
                            path: image.path,
                            fileSize: image.size,
                            extension: path.extname(image.originalname) || req.body.extension,
                            category: uploadCategory,
                            signRequired: signRequired,
                        };

                        try {
                            let user = await UserDao.getUserById(userId);

                            if (
                                user.coverPhotoId !== null &&
                                user.coverPhotoId !== undefined
                            ) {
                                let isFileDeleted = await deleteOldPhoto(user.coverPhotoId);

                                if (isFileDeleted) {
                                    let uploadedCoverImage: IUpload =
                                        await UploadDao.createUpload(data);

                                    if (uploadedCoverImage == null) {
                                        return res.sendError(
                                            "Error while uploading the cover image."
                                        );
                                    }

                                    const userDetails: DUser = {
                                        coverPhotoId: uploadedCoverImage._id,
                                    };

                                    let updatedUser = await UserDao.updateUser(
                                        userId,
                                        userDetails
                                    );

                                    if (updatedUser == null) {
                                        return res.sendError("User could not be updated.");
                                    }

                                    return res.sendSuccess(updatedUser, "Success");
                                } else {
                                    return res.sendError("Error while deleting the cover image.");
                                }
                            } else {
                                let uploadedCoverImage: IUpload = await UploadDao.createUpload(
                                    data
                                );

                                if (uploadedCoverImage == null) {
                                    return res.sendError(
                                        "Error while uploading the cover image."
                                    );
                                }

                                const userDetails: DUser = {
                                    coverPhotoId: uploadedCoverImage._id,
                                };

                                let updatedUser = await UserDao.updateUser(userId, userDetails);

                                if (updatedUser == null) {
                                    return res.sendError("User could not be updated.");
                                }

                                return res.sendSuccess(updatedUser, "Success");
                            }
                        } catch (error) {
                            return res.sendError(error);
                        }
                    }
                }
            });
        } catch (error) {
            return res.sendError("Failed to upload image. " + error);
        }
    }

    export async function updateTherapistProfile(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        let newExpTags: Types.ObjectId[] = [];
        let newInsuranceCompanyTags: Types.ObjectId[] = [];
        let uploadCategory = UploadCategory.DISCLOSURE_STATEMENT;

        const storage = multer.diskStorage({
            destination: async (req, FileRes, cb) => {
                await updateTherapisProfileValidationRules(req, cb);
            },
        });

        async function updateTherapisProfileValidationRules(req: any, cb: any) {
            let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

            try {
                let profileDetails = JSON.parse(req.body.profileDetails);

                let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
                    profileDetails.primaryPhone
                );

                if (
                    isprimaryPhoneUsed &&
                    userId.toString() !== isprimaryPhoneUsed._id.toString()
                ) {
                    return res.sendError("Provided primary phone is already taken.");
                }

                if (
                    !profileDetails.firstname ||
                    typeof profileDetails.firstname !== "string"
                ) {
                    return cb(Error("Firstname is required."), null);
                }

                if (
                    !profileDetails.lastname ||
                    typeof profileDetails.lastname !== "string"
                ) {
                    return cb(Error("Lastname is required."), null);
                }

                if (
                    !profileDetails.username ||
                    typeof profileDetails.username !== "string"
                ) {
                    return cb(Error("Username is required."), null);
                }

                if (!profileDetails.email || typeof profileDetails.email !== "string") {
                    return cb(Error("Email is required."), null);
                }

                if (
                    !profileDetails.gender ||
                    typeof profileDetails.gender !== "string"
                ) {
                    return cb(Error("Gender is required."), null);
                }

                if (
                    !profileDetails.timeZone ||
                    typeof profileDetails.timeZone !== "string"
                ) {
                    return cb(Error("Timezone is required."), null);
                }

                if (
                    !profileDetails.deletingStatementId ||
                    typeof profileDetails.deletingStatementId !== "string"
                ) {
                    return cb(Error("Deleting statement id is required."), null);
                }

                fs.access(destination, (error: any) => {
                    if (error) {
                        return fs.mkdir(destination, (error: any) =>
                            cb(error, "destination")
                        );
                    } else {
                        return cb(null, destination);
                    }
                });
            } catch (error) {
                return cb(Error(error), null);
            }
        }

        async function deleteOldFiles(uploadId: string) {
            let isDeleted = false;

            let resultHandler = async function (error: any) {
                if (error) {
                    console.log("Unlink failed.", error);
                } else {
                    console.log("File deleted.");
                }
            };

            try {
                let oldFile = await UploadDao.getUpload(uploadId);
                await fs.unlink(oldFile.path, resultHandler);
                await UploadDao.deleteUploadById(uploadId);
                isDeleted = true;
            } catch (error) {
                isDeleted = false;
            }
            return isDeleted;
        }

        const upload = multer({ storage: storage }).single("disclosureStatement");

        try {
            upload(req, res, async function (error: any) {
                if (error) {
                    return res.sendError(error + "");
                }

                let isValid = true;
                let isFileDeleted = false;

                if (!req.file) {
                    let profileDetails = JSON.parse(req.body.profileDetails);

                    let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
                        profileDetails.primaryPhone
                    );

                    if (
                        isprimaryPhoneUsed &&
                        userId.toString() !== isprimaryPhoneUsed._id.toString()
                    ) {
                        return res.sendError("Provided primary phone is already taken.");
                    }

                    if (
                        !profileDetails.firstname ||
                        typeof profileDetails.firstname !== "string"
                    ) {
                        isValid = false;
                        return res.sendError("Firstname is required.");
                    }

                    if (
                        !profileDetails.lastname ||
                        typeof profileDetails.lastname !== "string"
                    ) {
                        isValid = false;
                        return res.sendError("lastname is required.");
                    }

                    if (
                        !profileDetails.username ||
                        typeof profileDetails.username !== "string"
                    ) {
                        isValid = false;
                        return res.sendError("Username is required.");
                    }

                    if (
                        !profileDetails.email ||
                        typeof profileDetails.email !== "string"
                    ) {
                        isValid = false;
                        return res.sendError("email is required.");
                    }

                    if (
                        !profileDetails.gender ||
                        typeof profileDetails.gender !== "string"
                    ) {
                        isValid = false;
                        return res.sendError("gender is required.");
                    }

                    if (
                        !profileDetails.primaryPhone ||
                        typeof profileDetails.primaryPhone !== "string"
                    ) {
                        isValid = false;
                        return res.sendError("primary phone number is required.");
                    }

                    if (
                        !profileDetails.dateOfBirth ||
                        typeof profileDetails.dateOfBirth !== "string"
                    ) {
                        isValid = false;
                        return res.sendError("dateOfBirth is required.");
                    }

                    if (
                        !profileDetails.deletingStatementId ||
                        typeof profileDetails.deletingStatementId !== "string"
                    ) {
                        isValid = false;
                        return res.sendError("deletingStatementId is required.");
                    }

                    if (
                        !profileDetails.timeZone ||
                        typeof profileDetails.timeZone !== "string"
                    ) {
                        isValid = false;
                        return res.sendError("Timezone is required.");
                    }

                    const user = await TherapistDao.getUserById(userId);

                    if (isValid) {
                        if (!user.username) {
                            if (!profileDetails.username) {
                                return res.sendError("Username field is required.");
                            }

                            const foundUser = await UserDao.getUserByUsername(
                                profileDetails.username
                            );

                            if (foundUser) {
                                return res.sendError("Username is already taken.");
                            }
                        }

                        if (user._id.toString() === userId.toString()) {
                            if (
                                profileDetails.experiencedIn &&
                                profileDetails.experiencedIn.length > 0
                            ) {
                                await Promise.all(
                                    profileDetails.experiencedIn.map(async (tag: any) => {
                                        const isFound = await AdminDao.getExperienceTagsByName(tag);
                                        if (!isFound || isFound == null) {
                                            const addedTag = await AdminDao.addExperienceTag(tag);
                                            newExpTags.push(addedTag._id);
                                        } else {
                                            newExpTags.push(isFound._id);
                                        }
                                    })
                                );
                            }
                        }

                        if (user._id.toString() === userId.toString()) {
                            if (
                                profileDetails.insuranceCompanies &&
                                profileDetails.insuranceCompanies.length > 0
                            ) {
                                await Promise.all(
                                    profileDetails.insuranceCompanies.map(
                                        async (company: any) => {
                                            const isFound = await AdminDao.getInsuranceCompanyByType(
                                                company
                                            );
                                            if (!isFound || isFound == null) {
                                                const addedCompany = await AdminDao.addInsuranceCompany(
                                                    company
                                                );
                                                newInsuranceCompanyTags.push(addedCompany._id);
                                            } else {
                                                newInsuranceCompanyTags.push(isFound._id);
                                            }
                                        }
                                    )
                                );
                            }
                        }
                        if (profileDetails.deletingStatementId !== "none") {
                            isFileDeleted = await deleteOldFiles(
                                profileDetails.deletingStatementId
                            );

                            if (!isFileDeleted)
                                return res.sendError("Error while deleting the previous file");
                        }
                    }

                    const therapistDetails: ITherapist = await UserDao.getUserById(
                        userId
                    );
                    const therapist: DTherapist = {
                        description: profileDetails.description,
                        firstname: profileDetails.firstname.trim(),
                        lastname: profileDetails.lastname.trim(),
                        middleInitials: profileDetails.middleInitials,
                        email: profileDetails.email,
                        gender: profileDetails.gender,
                        ethnicityId:
                            profileDetails.ethnicityId !== null ||
                                profileDetails.ethnicityId !== undefined ||
                                profileDetails.ethnicityId !== ""
                                ? Types.ObjectId(profileDetails.ethnicityId)
                                : null,
                        dateOfBirth: profileDetails.dateOfBirth,
                        experiencedIn: newExpTags,
                        profession:
                            profileDetails.profession !== null ||
                                profileDetails.profession !== undefined ||
                                profileDetails.profession !== ""
                                ? Types.ObjectId(profileDetails.profession)
                                : null,
                        professionLicense:
                            profileDetails.professionLicense !== null ||
                                profileDetails.professionLicense !== undefined ||
                                profileDetails.professionLicense !== ""
                                ? Types.ObjectId(profileDetails.professionLicense)
                                : null,
                        yearsOfExperience: profileDetails.yearsOfExperience,
                        workingHours: profileDetails.workingHours,
                        insuranceCompanies: newInsuranceCompanyTags,
                        username: profileDetails.username,
                        socialSecurity: profileDetails.socialSecurity,
                        cAQH: profileDetails.cAQH,
                        nPI1: profileDetails.nPI1,
                        taxIdentification: profileDetails.taxIdentification,
                        license: profileDetails.license,
                        issueDate: profileDetails.issueDate,
                        expirationDate: profileDetails.expirationDate,
                        schoolName: profileDetails.schoolName,
                        dateOfGraduation: profileDetails.dateOfGraduation,
                        schoolStreetAddress: profileDetails.schoolStreetAddress,
                        schoolCity: profileDetails.schoolCity,
                        schoolState: profileDetails.schoolState,
                        schoolZipCode: profileDetails.schoolZipCode,
                        taxonomyCode: profileDetails.taxonomyCode,
                        malpracticePolicy: profileDetails.malpracticePolicy,
                        malpracticeExpirationDate: profileDetails.malpracticeExpirationDate,
                        disclosureStatementId: user.disclosureStatementId
                            ? user.disclosureStatementId
                            : null,
                        primaryPhone: profileDetails.primaryPhone,
                        streetAddress: profileDetails.streetAddress,
                        city: profileDetails.city,
                        state: profileDetails.state,
                        zipCode: profileDetails.zipCode,
                        timeZone: profileDetails.timeZone,
                        googleCalendarAccess: profileDetails.googleCalendarAccess,
                        caqhUserName: profileDetails.caqhUserName,
                        caqhPassword: profileDetails.caqhPassword,
                        medicaidUsername: profileDetails.medicaidUsername,
                        MedicaidPassword: profileDetails.MedicaidPassword,
                        medicaidId: profileDetails.medicaidId,
                        psychologyTodayUsername: profileDetails.psychologyTodayUsername,
                        psychologyTodayPassword: profileDetails.psychologyTodayPassword,
                        therapyState: [...new Set([...(profileDetails.therapyState || []), profileDetails.state])]
                    };

                    let updatedTherapist = await UserDao.updateUser(userId, therapist);

                    if (!updatedTherapist) {
                        return res.sendError("Failed to update the therapist.");
                    }

                    return res.sendSuccess(
                        updatedTherapist,
                        "Your profile has been updated successfully."
                    );
                } else {
                    let profileDetails = JSON.parse(req.body.profileDetails);

                    let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
                        profileDetails.primaryPhone
                    );

                    if (
                        isprimaryPhoneUsed &&
                        userId.toString() !== isprimaryPhoneUsed._id.toString()
                    ) {
                        return res.sendError("Provided primary phone is already taken.");
                    }

                    let isFileDeleted = false;
                    let uploadedDcoument = null;

                    const user = await UserDao.getUser(userId);

                    if (!user.username) {
                        if (!profileDetails.username) {
                            return res.sendError("Username field is required.");
                        }

                        const foundUser = await UserDao.getUserByUsername(
                            profileDetails.username
                        );

                        if (foundUser) {
                            return res.sendError("Username is already taken.");
                        }
                    }

                    if (user._id.toString() === userId.toString()) {
                        if (
                            profileDetails.experiencedIn &&
                            profileDetails.experiencedIn.length > 0
                        ) {
                            await Promise.all(
                                profileDetails.experiencedIn.map(async (tag: any) => {
                                    const isFound = await AdminDao.getExperienceTagsByName(tag);
                                    if (!isFound || isFound == null) {
                                        const addedTag = await AdminDao.addExperienceTag(tag);
                                        newExpTags.push(addedTag._id);
                                    } else {
                                        newExpTags.push(isFound._id);
                                    }
                                })
                            );
                        }
                    }

                    if (user._id.toString() === userId.toString()) {
                        if (
                            profileDetails.insuranceCompanies &&
                            profileDetails.insuranceCompanies.length > 0
                        ) {
                            await Promise.all(
                                profileDetails.insuranceCompanies.map(async (company: any) => {
                                    const isFound = await AdminDao.getInsuranceCompanyByType(
                                        company
                                    );

                                    if (!isFound || isFound == null) {
                                        const addedCompany = await AdminDao.addInsuranceCompany(
                                            company
                                        );
                                        newInsuranceCompanyTags.push(addedCompany._id);
                                    } else {
                                        newInsuranceCompanyTags.push(isFound._id);
                                    }
                                })
                            );
                        }
                    }

                    if (profileDetails.deletingStatementId !== "none") {
                        isFileDeleted = await deleteOldFiles(
                            profileDetails.deletingStatementId
                        );

                        if (!isFileDeleted)
                            return res.sendError("Error while deleting the previous file");
                    }

                    const upload: any = req.file;

                    let signRequired: boolean = false;
                    if (req.body.signRequired !== undefined) {
                        signRequired = req.body.signRequired;
                    }

                    if (req.file) {
                        const disclosureStatement = {
                            userId: userId as unknown as Types.ObjectId,
                            originalName: upload.originalname.replace(/ /g, ""),
                            name: upload.filename,
                            type: upload.mimetype,
                            path: upload.path,
                            fileSize: upload.size,
                            extension:
                                path.extname(upload.originalname) || req.body.extension,
                            category: uploadCategory,
                            signRequired: signRequired,
                        };

                        uploadedDcoument = await UploadDao.createUpload(
                            disclosureStatement
                        );

                        if (!uploadedDcoument) {
                            return res.sendError("Error while uploading profile image.");
                        }
                    }

                    const therapist: DTherapist = {
                        description: profileDetails.description,
                        firstname: profileDetails.firstname,
                        lastname: profileDetails.lastname,
                        middleInitials: profileDetails.middleInitials,
                        email: profileDetails.email,
                        gender: profileDetails.gender,
                        ethnicityId:
                            profileDetails.ethnicityId !== null ||
                                profileDetails.ethnicityId !== undefined ||
                                profileDetails.ethnicityId !== ""
                                ? Types.ObjectId(profileDetails.ethnicityId)
                                : null,
                        dateOfBirth: profileDetails.dateOfBirth,
                        experiencedIn: newExpTags,
                        insuranceCompanies: newInsuranceCompanyTags,
                        profession:
                            profileDetails.profession !== null ||
                                profileDetails.profession !== undefined ||
                                profileDetails.profession !== ""
                                ? Types.ObjectId(profileDetails.profession)
                                : null,
                        professionLicense:
                            profileDetails.professionLicense !== null ||
                                profileDetails.professionLicense !== undefined ||
                                profileDetails.professionLicense !== ""
                                ? Types.ObjectId(profileDetails.professionLicense)
                                : null,
                        yearsOfExperience: profileDetails.yearsOfExperience,
                        workingHours: profileDetails.workingHours,
                        username: profileDetails.username,
                        socialSecurity: profileDetails.socialSecurity,
                        cAQH: profileDetails.cAQH,
                        nPI1: profileDetails.nPI1,
                        taxIdentification: profileDetails.taxIdentification,
                        license: profileDetails.license,
                        issueDate: profileDetails.issueDate,
                        expirationDate: profileDetails.expirationDate,
                        schoolName: profileDetails.schoolName,
                        dateOfGraduation: profileDetails.dateOfGraduation,
                        schoolStreetAddress: profileDetails.schoolStreetAddress,
                        schoolCity: profileDetails.schoolCity,
                        schoolState: profileDetails.schoolState,
                        schoolZipCode: profileDetails.schoolZipCode,
                        taxonomyCode: profileDetails.taxonomyCode,
                        malpracticePolicy: profileDetails.malpracticePolicy,
                        malpracticeExpirationDate: profileDetails.malpracticeExpirationDate,
                        disclosureStatementId: uploadedDcoument._id,
                        primaryPhone: profileDetails.primaryPhone,
                        streetAddress: profileDetails.streetAddress,
                        city: profileDetails.city,
                        state: profileDetails.state,
                        zipCode: profileDetails.zipCode,
                        timeZone: profileDetails.timeZone,
                        reminderType: profileDetails.reminderType,
                        reminderTime: profileDetails.reminderTime,
                        caqhUserName: profileDetails.caqhUserName,
                        caqhPassword: profileDetails.caqhPassword,
                        medicaidUsername: profileDetails.medicaidUsername,
                        MedicaidPassword: profileDetails.MedicaidPassword,
                        medicaidId: profileDetails.medicaidId,
                        psychologyTodayUsername: profileDetails.psychologyTodayUsername,
                        psychologyTodayPassword: profileDetails.psychologyTodayPassword,
                        therapyState: [...new Set([...(profileDetails.therapyState || []), profileDetails.state])]
                    };

                    let updatedTherapist = await UserDao.updateUser(userId, therapist);

                    if (!updatedTherapist) {
                        return res.sendError("Failed to update the therapist.");
                    }

                    return res.sendSuccess(
                        updatedTherapist,
                        "Your profile has been updated successfully."
                    );
                }
            });
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateClientProfile(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const description = req.body.description;
        const firstname = req.body.firstname;
        const lastname = req.body.lastname;
        const email = req.body.email;
        const gender = req.body.gender;
        const ethnicityId = req.body.ethnicityId;
        const dateOfBirth = req.body.dateOfBirth;
        const username = req.body.username;
        const incognito = req.body.incognito;
        const middleInitials = req.body.middleInitials;
        const maritalStatus = req.body.maritalStatus;
        const streetAddress = req.body.streetAddress;
        const unit = req.body.unit;
        const city = req.body.city;
        const freeUser = req.body.freeUser;
        const state = req.body.state;
        const zipCode = req.body.zipCode;
        const primaryPhone = req.body.primaryPhone;
        const homePhone = req.body.homePhone;
        const workPhone = req.body.workPhone;
        const voiceMail = req.body.voiceMail;
        const reminderType = req.body.reminderType;
        const reminderTime = req.body.reminderTime;
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        try {
            if (email) {
                const user = await UserDao.getUserByEmail(email);

                if (primaryPhone) {
                    let isprimaryPhoneUsed = await UserDao.getUserByPrimaryPhone(
                        primaryPhone
                    );

                    if (
                        isprimaryPhoneUsed &&
                        userId.toString() !== isprimaryPhoneUsed._id.toString()
                    ) {
                        return res.sendError("Provided primary phone is already taken.");
                    }
                }

                if (user._id.toString() === userId.toString()) {
                    const client: DClient = {
                        description: description,
                        firstname: firstname.trim(),
                        lastname: lastname.trim(),
                        email: email,
                        gender: gender,
                        ethnicityId: Types.ObjectId(ethnicityId),
                        dateOfBirth: dateOfBirth,
                        username: username.trim(),
                        incognito: incognito,
                        middleInitials: middleInitials,
                        maritalStatus: maritalStatus,
                        streetAddress: streetAddress,
                        unit: unit,
                        city: city,
                        state: state,
                        zipCode: zipCode,
                        primaryPhone: primaryPhone,
                        homePhone: homePhone,
                        workPhone: workPhone,
                        voiceMail: voiceMail,
                        reminderType: reminderType,
                        reminderTime: reminderTime,
                        freeUser: freeUser,
                    };

                    if (primaryPhone) {
                        const isValidPhoneNumber = await SmsChatEp.validatePhoneNumberFromTwilio(primaryPhone.toString());

                        if (!isValidPhoneNumber) {
                            return res.sendError(
                                "Invalid phone number"
                            );
                        }
                    }

                    let updatedClient = await UserDao.updateUser(userId, client);

                    if (updatedClient == null) {
                        return res.sendError(
                            "Something went wrong. Please try again later."
                        );
                    }


                    if (primaryPhone) {
                        SmsChatEp.changePhoneNumberFromTwilio(primaryPhone.toString(), req.user.role.toString(), userId.toString());
                    }

                    return res.sendSuccess(
                        updatedClient,
                        "Your profile has been updated successfully."
                    );
                } else {
                    return res.sendError("Prvided email is already in use.");
                }
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateClientPackage(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;
        const freeUser = req.body.freeUser;
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.sendError(errors.array()[0]["msg"]);
        }

        try {
            const user = await UserDao.getUserById(userId);

            if (user._id.toString() === userId.toString()) {
                const client: DClient = {
                    freeUser: freeUser,
                };

                let updatedClient = await UserDao.updateUser(userId, client);

                if (updatedClient == null) {
                    return res.sendError("Something went wrong. Please try again later.");
                }
                return res.sendSuccess(updatedClient, "Your profile has been updated successfully.");
                // if (freeUser) {
                //     let appointmentList =
                //         await AppointmentDao.getAllAppointmentsListByClientId(userId);

                //     let deletedAppointment: IAppointment = null;

                //     if (appointmentList.length == 0 || !appointmentList || appointmentList == null) {
                //         return res.sendSuccess(updatedClient, "Your profile has been updated successfully.");
                //     } else {
                //         deletedAppointment = await AppointmentDao.deleteAppointment(
                //             appointmentList[0]?._id
                //         );

                //         return res.sendSuccess(updatedClient, "Your profile has been updated successfully.");
                //     }
                // }
            } else {
                return res.sendError("Prvided email is already in use.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getAllThemeImages(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const role = req.user.role;

        if (
            role == UserRole.CLIENT ||
            role == UserRole.THERAPIST ||
            role == UserRole.SUPER_ADMIN ||
            role == UserRole.SUB_ADMIN
        ) {
            try {
                if (req.user.role == UserRole.SUB_ADMIN) {
                    const ownUser = await UserDao.getUserByUserId(req.user._id);
                    if (ownUser.adminPermission.viewThemeImage != true) {
                        return res.sendError(
                            "You don't have permission for View Theme Images!"
                        );
                    }
                }
                let themeList = await UserDao.getAllThemeImages();

                return res.sendSuccess(themeList, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role");
        }
    }

    export async function addCompleteGuide(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userID = req.user._id;
        const guideComplete = req.body.guideComplete;

        try {
            const userDetails: DUser = {
                guideComplete: guideComplete,
            };

            let addCompleteGuide = await UserDao.updateUser(userID, userDetails);
            return res.sendSuccess(addCompleteGuide, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getUserDetailsWithAvatar(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const userId = req.params.id;
            let user = await UserDao.getAvatarDetailsWithMeetingDetails(
                userId,
                req.user.role,
                req.user._id
            );

            if (!user) {
                return res.sendError("No user found for the provided user Id.");
            }
            return res.sendSuccess(user, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export function updateClientIncognitoModeParamValidation() {
        return [
            check("mode")
                .not()
                .isEmpty()
                .isBoolean()
                .withMessage("mode is required."),
        ];
    }

    export async function updateClientIncognitoMode(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]);
            }

            const status = req.body.mode;

            let response = await UserDao.updateClientIncognitoMode(
                req.user._id,
                status
            );
            return res.sendSuccess(response, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getUserIncognitoModeById(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const ids = req.params.id;
            let response = await UserDao.getUserIncognitoModeById(ids);

            return res.sendSuccess(response, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateClientIncognitoPopupShow(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]);
            }
            let response = await UserDao.updateClientIncognitoPopupShow(req.user._id);
            return res.sendSuccess(response, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export function updateCallRecordingAllowedParamValidation() {
        return [
            check("value")
                .not()
                .isEmpty()
                .isBoolean()
                .withMessage("value is required."),
        ];
    }
    export async function updateCallRecordingAllowed(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]);
            }

            const status = req.body.mode;

            let response = UserDao.updateCallRecordingAllowed(req.user._id, status);
            return res.sendSuccess(response, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export function updateHideCallTimerParamValidation() {
        return [
            check("value")
                .not()
                .isEmpty()
                .isBoolean()
                .withMessage("value is required."),
        ];
    }

    export async function updateHideCallTimer(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]);
            }
            const status = req.body.mode;
            let response = UserDao.updateHideCallTimer(req.user._id, status);
            return res.sendSuccess(response, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function customerReview(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const userId = req.user._id;

        try {
            const errors = validationResult(req);

            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }

            const review = req.body.review;
            const stars = req.body.noOfStars;
            const therapistId = req.body.therapistId;

            const cusReview: DCustomerReview = {
                clientId: userId,
                therapistId: therapistId,
                review: review,
                stars: stars,
                status: ReviewStatus.PENDING,
            };

            let response = await UserDao.customerReviewDao(cusReview);

            if (response) {
                return res.sendSuccess(response, "Review added Successfully.");
            } else {
                return res.sendError("Review could not be added. Please try again.");
            }
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getAllApprovedLavniReviews(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const limit = Number(req.params.limit);
        const offset = Number(req.params.offset);

        try {
            const reviewList = await UserDao.getAllApprovedLavniReviews(limit, offset);

            return res.sendSuccess(reviewList, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getAllTherapistApprovedReviews(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const limit = Number(req.params.limit);
        const offset = Number(req.params.offset);
        const therapistId = req.body.therapistId



        if (req.user.role == UserRole.CLIENT || UserRole.THERAPIST) {
            try {
                const reportList = await UserDao.getAllApprovedTherapistReviews(
                    limit,
                    offset,
                    therapistId
                );

                return res.sendSuccess(reportList, "Success");
            } catch (error) {
                return res.sendError(error);
            }
        } else {
            return res.sendError("Invalid user role.");
        }
    }



    export async function sendReplySMS(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {

            // send message to admin panel via socket
            AdminClientSmsChatEp.sendAdminClientChatMessageSocketEventWhenIncomingMessage(req);

            const twiml = new MessagingResponse();
            const replySMS = req.body.From;
            console.log("replySMS", replySMS);
            let user = await UserDao.getUserByPhoneNumber(replySMS);
            let appointmentList = await UserDao.getAllAppointmentsByClientId(
                user._id
            );
            if (appointmentList?.length > 0) {
                if (appointmentList[0].smsStatus == "BEFORE24SMS") {
                    if (req.body.Body.includes("1")) {
                        if (replySMS) {
                            if (user) {
                                if (appointmentList) {
                                    let appointment = await AppointmentDao.updateAppointmentData(
                                        appointmentList[0]._id,
                                        AppointmentSMSStatus.CONFIRMED
                                    );
                                }
                            }
                        }
                        twiml.message("Your confirmation done.");
                    } else if (req.body.Body.includes("2")) {
                        if (replySMS) {
                            if (user) {
                                if (appointmentList) {
                                    let appointment = await AppointmentDao.updateAppointmentData(
                                        appointmentList[0]._id,
                                        AppointmentSMSStatus.RESCHEDULED
                                    );
                                }
                            }
                        }
                        twiml.message(
                            "Lavni Reminder: To reschedule your appointment, please visit: https://mylavni.com/appointments"
                        );
                    } else if (req.body.Body.includes("Lavni Stop")) {
                        if (replySMS) {
                            if (user) {
                                let updateUser = await UserDao.updateUserSmsStop(user._id);
                            }
                        }
                        twiml.message("You Successfully unsubscribe notifications.");
                    }
                } else {
                    return res.sendError(
                        "The response SMS does not contain a valid appointment. Please try again."
                    );
                }
            }

            let reminderSmsList = await AdminDao.getReminderSmsByClientId(
                user._id
            )
            if (reminderSmsList?.length > 0) {
                if (reminderSmsList[0]?.smsType == REMINDER_SMS_TYPE?.MATCH_APPOINTMENT_NO_INSUARANCE && !reminderSmsList[0]?.reply) {
                    if (req.body.Body.trim() === "1" || /^1(\s|$)/.test(req.body.Body.trim())) {
                        if (replySMS) {
                            if (user) {
                                if (reminderSmsList) {
                                    const smsReplyList = reminderSmsList[0]?.replySMS || [];
                                    const smsNewArray = ["Upload your insurance card. Please visit: https://mylavni.com/subscription"];
                                    const combinedArray = [...smsReplyList, ...smsNewArray];

                                    const data = {
                                        replySMS: combinedArray,
                                        reply: 1
                                    };
                                    await AdminDao.UpdateReminderSmsDataByID(reminderSmsList[0]._id, data)
                                }
                            }
                        }
                        //send them a link to upload their insurance card
                        twiml.message("Upload your insurance card. Please visit: https://mylavni.com/subscription");
                    } else if (req.body.Body.trim() === "2" || /^2(\s|$)/.test(req.body.Body.trim())) {
                        if (replySMS) {
                            if (user) {
                                if (reminderSmsList) {
                                    const smsReplyList = reminderSmsList[0]?.replySMS || [];
                                    const smsNewArray = ["Subscribe to your account. Please visit: https://mylavni.com/subscription"];
                                    const combinedArray = [...smsReplyList, ...smsNewArray];

                                    const data = {
                                        replySMS: combinedArray,
                                        reply: 2
                                    };
                                    await AdminDao.UpdateReminderSmsDataByID(reminderSmsList[0]._id, data)
                                }
                            }
                        }
                        //  send them a link to subscribe
                        twiml.message(
                            "Subscribe to your account. Please visit: https://mylavni.com/subscription"
                        );
                    }
                } else if (reminderSmsList[0]?.smsType == REMINDER_SMS_TYPE?.NO_MATCH_INSUARANCE && !reminderSmsList[0]?.reply) {
                    if (req.body.Body.trim() === "1" || /^1(\s|$)/.test(req.body.Body.trim())) {
                        if (replySMS) {
                            if (user) {
                                if (reminderSmsList) {
                                    const smsReplyList = reminderSmsList[0]?.replySMS || [];
                                    // const smsNewArray = ["Match with a therapist. Please visit: https://mylavni.com/dashboard"];
                                    const smsNewArray = [`Great! Please log-in to your account on https://mylavni.com and head to the "Discover Qualified Professionals" tab to match with a therapist and schedule your appointment.`];
                                    const combinedArray = [...smsReplyList, ...smsNewArray];

                                    const data = {
                                        replySMS: combinedArray,
                                        reply: 1
                                    };
                                    await AdminDao.UpdateReminderSmsDataByID(reminderSmsList[0]._id, data)
                                }
                            }
                        }
                        //Then we send them a link to match with a therapist
                        // twiml.message("Match with a therapist. Please visit: https://mylavni.com/dashboard");
                        twiml.message(`Great! Please log-in to your account on https://mylavni.com and head to the "Discover Qualified Professionals" tab to match with a therapist and schedule your appointment.`);
                    } else if (req.body.Body.trim() === "2" || /^2(\s|$)/.test(req.body.Body.trim())) {
                        if (replySMS) {
                            if (user) {
                                if (reminderSmsList) {
                                    const smsReplyList = reminderSmsList[0]?.replySMS || [];
                                    const smsNewArray = ["Thank you for letting us know"];
                                    const combinedArray = [...smsReplyList, ...smsNewArray];

                                    const data = {
                                        replySMS: combinedArray,
                                        reply: 2
                                    };
                                    await AdminDao.UpdateReminderSmsDataByID(reminderSmsList[0]._id, data)
                                }
                            }
                        }
                        //  We say thank you for letting us know
                        twiml.message(
                            "Thank you for letting us know"
                        );
                    }
                } else if (reminderSmsList[0]?.smsType == REMINDER_SMS_TYPE?.NO_SHOW_FIRST_APPOINTMENT && !reminderSmsList[0]?.reply) {
                    if (req.body.Body.trim() === "1" || /^1(\s|$)/.test(req.body.Body.trim())) {
                        if (replySMS) {
                            if (user) {
                                if (reminderSmsList) {
                                    const smsReplyList = reminderSmsList[0]?.replySMS || [];
                                    const smsNewArray = ["Schedule a new appointment. Please visit: https://mylavni.com/appointments"];
                                    const combinedArray = [...smsReplyList, ...smsNewArray];

                                    const data = {
                                        replySMS: combinedArray,
                                        reply: 1
                                    };
                                    await AdminDao.UpdateReminderSmsDataByID(reminderSmsList[0]._id, data)
                                }
                            }
                        }
                        //send them a link to schedule a new appiontment 
                        twiml.message("Schedule a new appointment. Please visit: https://mylavni.com/appointments");
                    } else if (req.body.Body.trim() === "2" || /^2(\s|$)/.test(req.body.Body.trim())) {
                        if (replySMS) {
                            if (user) {
                                if (reminderSmsList) {
                                    const smsReplyList = reminderSmsList[0]?.replySMS || [];
                                    const smsNewArray = ["Thank you for letting us know"];
                                    const combinedArray = [...smsReplyList, ...smsNewArray];

                                    const data = {
                                        replySMS: combinedArray,
                                        reply: 2
                                    };
                                    await AdminDao.UpdateReminderSmsDataByID(reminderSmsList[0]._id, data)
                                }
                            }
                        }
                        //   lets say thank you for letting us know
                        twiml.message(
                            "Thank you for letting us know"
                        );
                    }
                }
            }
            res.set("Content-Type", "application/xml");
            return res.send(twiml.toString());
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function viewTherapistProfileByIdPublicLimited(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const userId = req.params.id;

            if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
                return res.sendError("Invalid Id");
            }

            let user = await UserDao.getTherapistByUserIdLimited(
                mongoose.Types.ObjectId(userId)
            );

            if (!user) {
                return res.sendError("No user found for the provided user Id.");
            }

            return res.sendSuccess(user, "Success");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function setGoogleCalendarAccessToFalse(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const id = req.user._id;
        try {
            const googleCalendarAccessToken = false;
            let responseUser = await UserDao.inactiveGoogleCalendarStatus(
                id,
                googleCalendarAccessToken
            );
            res.sendSuccess(responseUser);
        } catch (err) {
            res.send(err);
        }
    }

    export async function test12345(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const clientTreatmentList = await UserDao.searchInsuranceData();

            console.log("clientTreatmentList", clientTreatmentList.length);

            const csvData = clientTreatmentList.flatMap(client => {
                const clientEmail = client.clientId?.email || '';
                const firstname = client.clientId?.firstname || '';
                const meetingStatus = client.meeting?.callingStatus || '';
                const utcClaimDate = client.clientTreatmenthistories?.createdAt || '';
                const utcMeetingStartedTime = client.clientTreatmenthistories?.meetingStartedTime || '';
                const claimDate = moment.utc(utcClaimDate).tz('America/New_York').format('YYYY-MM-DD HH:mm:ss');
                const meetingStartedTime = moment.utc(utcMeetingStartedTime).tz('America/New_York').format('YYYY-MM-DD HH:mm:ss');
                return {
                    clientEmail,
                    firstname,
                    meetingStatus,
                    claimDate,
                    meetingStartedTime,
                };
            });
            return res.sendSuccess(csvData, "Success");
        } catch (error) {
            console.error('Error:', error);
            res.status(500).send('Internal Server Error');
        }
    }

    export async function getAllExperienceTagSymptomsPublic(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const allIssues = await UserDao.getAllExperienceTagSymptomsPublic();
            return res.sendSuccess(allIssues);
        } catch (error) {
            console.error('Error:', error);
            res.status(500).send('Internal Server Error');
        }
    }

    export async function getAllStatesPublic(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const allStates = await AdminDao.getAllStatesByAdmin();
            console.log(allStates)
            if (allStates.success){
                return res.sendSuccess(allStates?.states, "States successfully retrieved public")
              } else {
                return res.sendError(`Error occured while retrieving state in public. error: ${allStates?.error}`)
              }
        } catch (error) {
            console.error('Error:', error);
            return res.sendError('Internal Server Error');
        }
    }

    export async function getAllClientStates(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const users = await UserDao.getAllUsers();
            const clientUsers = users.filter(user => user.role === UserRole.CLIENT);
            const states = clientUsers
                .map(user => user.state)
                .filter(state => state && state.trim()) // Remove null, undefined and empty states
                .filter((state, index, self) => self.indexOf(state) === index) // Remove duplicates
                .sort((a, b) => a.localeCompare(b)); // Sort alphabetically

            return res.sendSuccess(states, "Client states retrieved successfully");
        } catch (error) {
            console.error('Error:', error);
            return res.sendError('Internal Server Error');
        }
    }

}
