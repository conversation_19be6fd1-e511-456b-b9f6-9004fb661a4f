import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
let mongoose = require("mongoose");
import { check, validationResult } from "express-validator";
import { DClinicalAssesment } from "../models/clinicalAssesment-model";
import { ClinicalAssesmentDao } from "../dao/clinical-assestment-dao";
import { ClientDao } from "../dao/client-dao";
import { DClient } from "../models/client-model";
import { EmailService } from "../mail/config";
import { TherapistDao } from "../dao/therapist-dao";
import ClinicalAssesment from "../schemas/clinical-assesment-schema";
import { AdminDao } from "../dao/admin-dao";
import Meeting from "../schemas/meeting-schema";
import moment = require("moment");
import ClinicalAssesmentVersion from "../schemas/clinical-assesment-version-schema";

export namespace ClinicalAssestmentEp {

    export async function addClinicalAssestmentDetails(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req)
            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }
            const therapistId = req.user._id;
            const clientId = req.body.clientId;
            const assesmentHeader = req.body.assesmentHeader;
            const lengthOfAssestment = req.body.lengthOfAssestment;
            const dateOfAssesment = req.body.dateOfAssesment;
            const comprehensiveAssessment = req.body.comprehensiveAssessment;
            const other = req.body.other;
            const generalInfor = req.body.generalInfor;
            const chiefComplaint = req.body.chiefComplaint;
            // const currentServices = req.body.currentServices;
            // const religiousCulturalLanguagePref = req.body.religiousCulturalLanguagePref;
            // const leisureActivity = req.body.leisureActivity;
            // const livingSituation = req.body.livingSituation;
            // const currentAddressDuration = req.body.currentAddressDuration;
            // const frequentMoves = req.body.FrequentMoves;
            // const strengths = req.body.strengths;
            // const needs = req.body.needs;
            // const houseHoldMember = req.body.houseHoldMember;
            // const immediateFamilyOutside = req.body.immediateFamilyOutside;
            // const feelingUnsafe = req.body.feelingUnsafe;
            // const incomeSource = req.body.incomeSource;
            // const transportSource = req.body.transportSource;
            // const adultDevelopmentalAbnormalities = req.body.AdultDevelopmentalAbnormalities;
            // const historyofAbuse = req.body.historyofAbuse;
            // const pSAServiceHistory = req.body.PSAServiceHistory;
            // const historyofPsychiatricDiagnoses = req.body.historyofPsychiatricDiagnoses;
            // const historyofSymptoms = req.body.historyofSymptoms;
            // const currentModications = req.body.currentModications;
            // const medicalHistory = req.body.medicalHistory;
            // const healthNotes = req.body.healthNotes;
            // const legalHistory = req.body.legalHistory;
            // const substanceAbuse = req.body.substanceAbuse;
            // const asamDimensions = req.body.asamDimensions;
            // const schoolName = req.body.schoolName;
            // const highestEducation = req.body.highestEducation;
            // const employmentVocational = req.body.employmentVocational;
            // const rank = req.body.rank;
            // const yearsServed = req.body.yearsServed;
            // const reasonforDischarge = req.body.reasonforDischarge;
            // const serviceConnectedDisability = req.body.serviceConnectedDisability;
            // const independentLiving = req.body.independentLiving;
            // const mentalStatusExam = req.body.mentalStatusExam;
            // const suicideRiskPotential = req.body.suicideRiskPotential;
            // const summaryofNeeds = req.body.summaryofNeeds;
            // const recoveryHistoryandEnvironment = req.body.recoveryHistoryandEnvironment;
            // const identifiedStrengths = req.body.identifiedStrengths;
            // const identifiedNeeds = req.body.identifiedNeeds;
            // const wnl = req.body.wnl;
            // const noPSASserviceHistoryReported = req.body.noPSASserviceHistoryReported;
            // const noHistrotypsyDiagnosesReported = req.body.noHistrotypsyDiagnosesReported;
            // const noReportHistoryOfAbuse = req.body.noReportHistoryOfAbuse;
            // const mortivationEngageInServices = req.body.mortivationEngageInServices;
            // const currentImpairments = req.body.currentImpairments;
            // const psychiatricSymptom = req.body.psychiatricSymptom;
            // const summeryOfFindings = req.body.summeryOfFindings;
            // const clientSaftyPlan = req.body.clientSaftyPlan;



            console.log("generalInfor >>>>>>>>>>>>>>>>>>", generalInfor)

            const ClinicalAssesmentDetails: DClinicalAssesment = {
                therapistId: therapistId,
                clientId: clientId,
                assesmentHeader: assesmentHeader,
                lengthOfAssestment: lengthOfAssestment,
                dateOfAssesment: dateOfAssesment,
                comprehensiveAssessment: comprehensiveAssessment,
                other: other,
                generalInfor: generalInfor,
                chiefComplaint: chiefComplaint,
            }

            console.log("ClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", ClinicalAssesmentDetails)
            let ClinicalDetails = await ClinicalAssesmentDao.addDetails(ClinicalAssesmentDetails);

            if (!ClinicalDetails) {
                return res.sendError("Details could not be submitted. Please try again later.");
            }

            return res.sendSuccess(ClinicalDetails, "Details submitted.");

        } catch (error) {
            if (error instanceof mongoose.Error.ValidationError) {
                const errorMessages = Object.values(error.errors).map((val: any) => val.message);
                return res.sendError(errorMessages.join(", "));
            } else {
                return res.sendError(error);
            }
        }

    }

    export async function updateClinicalAssestmentDetails(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const errors = validationResult(req)
            if (!errors.isEmpty()) {
                return res.sendError(errors.array()[0]["msg"]);
            }
            const stepCount = req.body.stepCount;
            const _id = req.body._id;
            const therapistId = req.user._id;
            const clientId = req.body.clientId
            const assesmentHeader = req.body.assesmentHeader;
            const lengthOfAssestment = req.body.lengthOfAssestment;
            const dateOfAssesment = req.body.dateOfAssesment;
            const comprehensiveAssessment = req.body.comprehensiveAssessment;
            const other = req.body.other;
            const generalInfor = req.body.generalInfor;
            const chiefComplaint = req.body.chiefComplaint;
            const currentServices = req.body.currentServices;
            const religiousCulturalLanguagePref = req.body.religiousCulturalLanguagePref;
            const leisureActivity = req.body.leisureActivity;
            const currentLivingSituation = req.body.currentLivingSituation;
            const currentAddressDuration = req.body.currentAddressDuration;
            const frequentMoves = req.body.frequentMoves;
            const strengths = req.body.strengths;
            const needs = req.body.needs;
            const houseHoldMember = req.body.houseHoldMember;
            const immediateFamilyOutside = req.body.immediateFamilyOutside;
            const feelingUnsafe = req.body.feelingUnsafe;
            const incomeSource = req.body.incomeSource;
            const transportSource = req.body.transportSource;
            const adultDevelopmentalAbnormalities = req.body.adultDevelopmentalAbnormalities;
            const historyofAbuse = req.body.historyofAbuse;
            const pSAServiceHistory = req.body.pSAServiceHistory;
            const historyofPsychiatricDiagnoses = req.body.historyofPsychiatricDiagnoses;
            const historyofSymptoms = req.body.historyofSymptoms;
            const currentModications = req.body.currentModications;
            const medicalHistory = req.body.medicalHistory;
            const healthNotes = req.body.healthNotes;
            const legalHistory = req.body.legalHistory;
            const substanceAbuse = req.body.substanceAbuse;
            const asamDimensions = req.body.asamDimensions;
            const schoolName = req.body.schoolName;
            const highestEducation = req.body.highestEducation;
            const employmentVocational = req.body.employmentVocational;
            const noCurrentMedicationsReported = req.body.noCurrentMedicationsReported;
            const rank = req.body.rank;
            const yearsServed = req.body.yearsServed;
            const reasonforDischarge = req.body.reasonforDischarge;
            const serviceConnectedDisability = req.body.serviceConnectedDisability;
            const independentLiving = req.body.independentLiving;
            const mentalStatusExam = req.body.mentalStatusExam;
            const suicideRiskPotential = req.body.suicideRiskPotential;
            const summaryofNeeds = req.body.summaryofNeeds;
            const recoveryHistoryandEnvironment = req.body.recoveryHistoryandEnvironment;
            const identifiedStrengths = req.body.identifiedStrengths;
            const identifiedNeeds = req.body.identifiedNeeds;
            const wnl = req.body.wnl;
            const noPSASserviceHistoryReported = req.body.noPSASserviceHistoryReported;
            const noHistrotypsyDiagnosesReported = req.body.noHistrotypsyDiagnosesReported;
            const noReportHistoryOfAbuse = req.body.noReportHistoryOfAbuse;
            const mortivationEngageInServices = req.body.mortivationEngageInServices;
            const currentImpairments = req.body.currentImpairments;
            const psychiatricSymptom = req.body.psychiatricSymptom;
            const summeryOfFindings = req.body.summeryOfFindings;
            const eligibilityRecommendations = req.body.eligibilityRecommendations;
            const treatmentRecommendations = req.body.treatmentRecommendations;
            const recommendationNotes = req.body.recommendationNotes;
            const saftyPlanNotNeeded = req.body.saftyPlanNotNeeded;
            const clientSaftyPlanCompleted = req.body.clientSaftyPlanCompleted;
            const clientSaftyPlan = req.body.clientSaftyPlan;
            const signatureDetails = req.body.signatureDetails;
            const gad7anxiety = req.body.gad7anxiety;
            const patientHealthQuestionaire = req.body.patientHealthQuestionaire;
            const aceScore = req.body.aceScore;
            const findingAceScore = req.body.findingAceScore;
            const recoveryAssesmentScale = req.body.recoveryAssesmentScale;
            const isSignature = req.body.isSignature;
            const therapistSignature = req.body.therapistSignature
            const militaryInfo = req.body.militaryInfo;

            console.log("isSignature >>>>>>>>>>>>>>>>>>", isSignature)

            const step1Update: DClinicalAssesment = {
                assesmentHeader: assesmentHeader,
                lengthOfAssestment: lengthOfAssestment,
                dateOfAssesment: dateOfAssesment,
                comprehensiveAssessment: comprehensiveAssessment,
                other: other,
                generalInfor: generalInfor,
                chiefComplaint: chiefComplaint,

            }
            const step2Update: DClinicalAssesment = {
                currentServices: currentServices,
                religiousCulturalLanguagePref: religiousCulturalLanguagePref,
                leisureActivity: leisureActivity,
                identifiedStrengths: identifiedStrengths,
                identifiedNeeds: identifiedNeeds,
                currentLivingSituation: currentLivingSituation,
                currentAddressDuration: currentAddressDuration,
                frequentMoves: frequentMoves,
                strengths: strengths,
                needs: needs,
                houseHoldMember: houseHoldMember,
                immediateFamilyOutside: immediateFamilyOutside,
                feelingUnsafe: feelingUnsafe,
                incomeSource: incomeSource,
                transportSource: transportSource,
                adultDevelopmentalAbnormalities: adultDevelopmentalAbnormalities,
                historyofAbuse: historyofAbuse,
            }

            const step3Update: DClinicalAssesment = {
                wnl: wnl,
                adultDevelopmentalAbnormalities: adultDevelopmentalAbnormalities,
                historyofAbuse: historyofAbuse,
                pSAServiceHistory: pSAServiceHistory,
                historyofPsychiatricDiagnoses: historyofPsychiatricDiagnoses,
                historyofSymptoms: historyofSymptoms,
                currentModications: currentModications,
                noReportHistoryOfAbuse: noReportHistoryOfAbuse,
                noPSASserviceHistoryReported: noPSASserviceHistoryReported,
                noHistrotypsyDiagnosesReported: noHistrotypsyDiagnosesReported,
                noCurrentMedicationsReported: noCurrentMedicationsReported
            }

            const step4Update: DClinicalAssesment = {
                medicalHistory: medicalHistory,
                healthNotes: healthNotes,
                legalHistory: legalHistory,
            }

            const step5Update: DClinicalAssesment = {
                substanceAbuse: substanceAbuse
            }

            const step6Update: DClinicalAssesment = {
                asamDimensions: asamDimensions,
                schoolName: schoolName,
                highestEducation: highestEducation,
                employmentVocational: employmentVocational,
                militaryInfo: militaryInfo,
                rank: rank,
                yearsServed: yearsServed,
                reasonforDischarge: reasonforDischarge,
                serviceConnectedDisability: serviceConnectedDisability,
                independentLiving: independentLiving,
            }

            const step7Update: DClinicalAssesment = {
                mentalStatusExam: mentalStatusExam,
                suicideRiskPotential: suicideRiskPotential,
            }

            const step8Update: DClinicalAssesment = {
                summaryofNeeds: summaryofNeeds,
                recoveryHistoryandEnvironment: recoveryHistoryandEnvironment,
                mortivationEngageInServices: mortivationEngageInServices,
                currentImpairments: currentImpairments,
                psychiatricSymptom: psychiatricSymptom,
            }

            const step9Update: DClinicalAssesment = {
                summeryOfFindings: summeryOfFindings,
                eligibilityRecommendations: eligibilityRecommendations,
                treatmentRecommendations: treatmentRecommendations,
                recommendationNotes: recommendationNotes,
                saftyPlanNotNeeded: saftyPlanNotNeeded,
                clientSaftyPlanCompleted: clientSaftyPlanCompleted,
            }

            const step10Update: DClinicalAssesment = {
                clientSaftyPlan: clientSaftyPlan
            }

            const step11Update: DClinicalAssesment = {
                signatureDetails: signatureDetails,
                therapistSignature: therapistSignature,
                isSignature: isSignature
            }

            const step12Update: DClinicalAssesment = {
                gad7anxiety: gad7anxiety
            }

            const step13Update: DClinicalAssesment = {
                findingAceScore: findingAceScore,
                aceScore: aceScore,
            }

            const step14Update: DClinicalAssesment = {
                patientHealthQuestionaire: patientHealthQuestionaire
            }

            const step16Update: DClinicalAssesment = {
                recoveryAssesmentScale: recoveryAssesmentScale,
            }

            if (stepCount === 1) {

                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step1Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }

                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            } else if (stepCount === 2) {
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step2Update);
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step2Update)
                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            } else if (stepCount === 3) {
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step3Update);
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step3Update)

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            } else if (stepCount === 4) {
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step4Update);
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step4Update)
                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            } else if (stepCount === 5) {
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step5Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 6) {
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step6Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 7) {
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step7Update)
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step7Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 8) {
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step8Update)
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step8Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 9) {
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step9Update)
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step9Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 10) {
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step10Update)
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step10Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 11) {
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step11Update)

                if (isSignature == false) {
                    let client = await ClientDao.getUserById(clientId);
                    let therapist = await TherapistDao.getUserById(therapistId);

                    if (client) {
                        console.log("client email:::::::::::::::::::::::::::::::::", client.email)
                        await EmailService.sendClinicalAssesmentSignatureEmail(
                            client,
                            therapist,
                            "Clinical Assesment Client Signature Request",
                            _id
                        )
                    } else {
                        return res.sendError("client cannot find")
                    }
                }

                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step11Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 12) {
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step12Update)
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step12Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 13) {
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step13Update)
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step13Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 14) {
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step14Update)
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step14Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }
            else if (stepCount === 16) {
                console.log("UpdatingClinicalAssesmentDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>", step16Update)
                let ClinicalDetails = await ClinicalAssesmentDao.updateDetails(_id, step16Update);

                if (!ClinicalDetails) {
                    return res.sendError("Details could not be updated. Please try again later.");
                }
                return res.sendSuccess(ClinicalDetails, "Details Updated.");
            }


        } catch (error) {
            return res.sendError(error);
        }

    }

    export async function getClinicalAssesment(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        const clientId = req.body.clientId
        const therapistId = String(req.user._id);
        try {
            let ClinicalDetails = await ClinicalAssesmentDao.getClinicalAssesmentByClientId(clientId, therapistId);

            if (!ClinicalDetails) {
                return res.sendError("There is no record matching the specified therapist and client!");
            }
            return res.sendSuccess(ClinicalDetails, "Clinical assessment data.");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function updateClientAssesmentSignature(req: Request, res: Response, next: NextFunction) {
        const clientSignature = req.body.clientSignature;
        const _id = req.body._id;
        const isSignature = true;
        const data = {
            clientSignature: clientSignature,
            isSignature: isSignature,
            _id: _id
        }

        let updateAssesmentSignature = await ClinicalAssesmentDao.updateClientAssesmentSignature(_id, data)
        
        if (!updateAssesmentSignature) {
            return res.sendError("cannot update signature ")
        } else {
            return res.sendSuccess(updateAssesmentSignature, "Signature successfuly updated!")
        }
    }

    export async function getClinicalAssesmentByAdmin(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const assesmentId = req.body.assesmentId;

            if (!assesmentId || !mongoose.Types.ObjectId.isValid(assesmentId)) {
                return res.sendError("Invalid assesment Id");
            }
        
            let clinicalDetails = await ClinicalAssesment.findById(assesmentId).populate({ path: 'clientId',select: 'assesmentSignature' });

            if (!clinicalDetails) {
                return res.sendError("There is no record matching the specified assesment id!");
            }

            return res.sendSuccess(clinicalDetails, "Clinical assessment data.");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getClinicalAssestmentDetailsWithSessionData(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const clientId = req?.body?.clientId;
            const therapistId = req?.user?._id;
            // const assesmentId = req?.body?.assesmentId;
            // const isCreatingNewForm = req?.body?.isCreatingNewForm;


            let dateOfSession = "";

            if(!therapistId ){
                return res.sendError("Invalid therapist id ");
            }

            if(!clientId || !mongoose.Types.ObjectId.isValid(clientId)){
                return res.sendError("Invalid client id ");
            }

            const therapistIdString = req?.user?._id.toString();

            const completedFirstSession = await Meeting.findOne({clientId: clientId, therapistId: therapistId, callingStatus: "COMPLETED"}).sort({ createdAt: 1 });

            if(completedFirstSession){
                if(completedFirstSession?.regularMeetingDate) {
                    dateOfSession = completedFirstSession?.regularMeetingDate.toString();
                } else {
                    dateOfSession = completedFirstSession?.createdAt.toString();
                }
            }

            // const ClinicalDetails = (assesmentId || !isCreatingNewForm) ? await ClinicalAssesment.findById(assesmentId) : null;
            let ClinicalDetails = await ClinicalAssesmentDao.getClinicalAssesmentByClientId(clientId, therapistIdString);

            let clinicalAssesmentVersion = await ClinicalAssesmentVersion.find({ clientId:clientId, therapistId: therapistId }).sort({ createdAt: -1 });
    
            const today = moment().startOf("day");
    
            let isVersionCreatedToday = clinicalAssesmentVersion.some((version) =>
                moment(version?.versionCreatedAt).isSame(today, "day")
            );

            let finalDataObject  = {};

            if(ClinicalDetails){
                finalDataObject = {
                    dateOfSession: dateOfSession,
                    asessmentData: ClinicalDetails,
                    isVersionCreatedToday: isVersionCreatedToday,
                }
            }else{
                finalDataObject = {
                    dateOfSession: dateOfSession,
                }
            }

            return res.sendSuccess(finalDataObject, "Clinical assessment data.");

        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getClientUsingClinicalAssessmentId(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const assesmentId = req.body.assesmentId;

            if (!assesmentId || !mongoose.Types.ObjectId.isValid(assesmentId)) {
                return res.sendError("Invalid assesment Id");
            }
        
            let clinicalDetails = await ClinicalAssesment.findById(assesmentId).select('clientId');

            if (!clinicalDetails) {
                return res.sendError("There is no record matching the specified assesment id!");
            }
            const client = await AdminDao.getClientById(clinicalDetails?.clientId);
            if (!client) {
                return res.sendError("There is no record matching the specified client id!");
            }
            return res.sendSuccess(client, "Client data.");
        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getClinicalAssestmentDetailsWithSessionDataByAdmin(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {

            const assesmentId = req.body.assesmentId;

            if (!assesmentId || !mongoose.Types.ObjectId.isValid(assesmentId)) {
                return res.sendError("Invalid assesment Id");
            }
        
            let prevClinicalDetails = await ClinicalAssesment.findById(assesmentId);

            if (!prevClinicalDetails) {
                return res.sendError("There is no record matching the specified assesment id!");
            }

            const clientId = prevClinicalDetails?.clientId;
            const therapistId = prevClinicalDetails?.therapistId;

            let dateOfSession = "";

            if(!therapistId ){
                return res.sendError("Invalid therapist id ");
            }

            if(!clientId){
                return res.sendError("Invalid client id ");
            }

            const completedFirstSession = await Meeting.findOne({clientId: clientId, therapistId: therapistId, callingStatus: "COMPLETED"}).sort({ createdAt: 1 });

            if(completedFirstSession){
                if(completedFirstSession?.regularMeetingDate) {
                    dateOfSession = completedFirstSession?.regularMeetingDate.toString();
                } else {
                    dateOfSession = completedFirstSession?.createdAt.toString();
                }
            }

            let clinicalDetailsPopulated = await ClinicalAssesment.findById(assesmentId).populate({
                path: 'clientId',
                select: 'assesmentSignature'
            });

            if (!clinicalDetailsPopulated) {
                return res.sendError("There is no record matching the specified assesment id!");
            }

            const clinicalAssesmentVersion = await ClinicalAssesmentVersion.find({ clientId:clientId, therapistId: therapistId }).sort({ createdAt: -1 });
    
            const today = moment().startOf("day");
    
            const isVersionCreatedToday = clinicalAssesmentVersion.some((version) =>
                moment(version?.versionCreatedAt).isSame(today, "day")
            );

            const finalDataObject = {
                dateOfSession: dateOfSession,
                asessmentData: clinicalDetailsPopulated,
                isVersionCreatedToday: isVersionCreatedToday,
            }

            return res.sendSuccess(finalDataObject, "Clinical assessment data.");

        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getClinicalAssestmentDetailsForDownload(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {
            const assesmentId = req?.body?.assesmentId;
            // const therapistId = req?.user?._id;

            let dateOfSession = "";
            let dateOfBirth = "";

            // if(!therapistId ){
            //     return res.sendError("Invalid therapist id ");
            // }

            if(!assesmentId || !mongoose.Types.ObjectId.isValid(assesmentId)){
                return res.sendError("Invalid assesment id ");
            }

            let clinicalDetails: any = await ClinicalAssesment.findById(assesmentId).populate({
                path: 'clientId',
                select: 'assesmentSignature dateOfBirth'
            });

            if (!clinicalDetails) {
                return res.sendError("Please complete the assessment before downloading!");
            }

            const clientId = clinicalDetails?.clientId;
            const therapistId = clinicalDetails?.therapistId;

            if(clinicalDetails?.clientId?.dateOfBirth){
                dateOfBirth = clinicalDetails?.clientId?.dateOfBirth;
            }

            const completedFirstSession = await Meeting.findOne({clientId: clientId, therapistId: therapistId, callingStatus: "COMPLETED"}).sort({ createdAt: 1 });

            if(completedFirstSession){
                if(completedFirstSession?.regularMeetingDate) {
                    dateOfSession = completedFirstSession?.regularMeetingDate.toString();
                } else {
                    dateOfSession = completedFirstSession?.createdAt.toString();
                }
            }

            const finalDataObject = {
                dateOfSession: dateOfSession,
                dateOfBirth: dateOfBirth,
                asessmentData: clinicalDetails
            }

            return res.sendSuccess(finalDataObject, "Clinical assessment data.");

        } catch (error) {
            return res.sendError(error);
        }
    }

    export async function getClinicalAssestmentDetailsForDownloadByAdmin(
        req: Request,
        res: Response,
        next: NextFunction
    ) {
        try {

            const assesmentId = req.body.assesmentId;

            if (!assesmentId || !mongoose.Types.ObjectId.isValid(assesmentId)) {
                return res.sendError("Invalid assesment Id");
            }
        
            let prevClinicalDetails = await ClinicalAssesment.findById(assesmentId);

            if (!prevClinicalDetails) {
                return res.sendError("Please complete the assessment before downloading!");
            }

            const clientId = prevClinicalDetails?.clientId;
            const therapistId = prevClinicalDetails?.therapistId;

            let dateOfSession = "";
            let dateOfBirth = "";

            if(!therapistId ){
                return res.sendError("Invalid therapist id ");
            }

            if(!clientId){
                return res.sendError("Invalid client id ");
            }

            const completedFirstSession = await Meeting.findOne({clientId: clientId, therapistId: therapistId, callingStatus: "COMPLETED"}).sort({ createdAt: 1 });

            if(completedFirstSession){
                if(completedFirstSession?.regularMeetingDate) {
                    dateOfSession = completedFirstSession?.regularMeetingDate.toString();
                } else {
                    dateOfSession = completedFirstSession?.createdAt.toString();
                }
            }

            let clinicalDetailsPopulated: any = await ClinicalAssesment.findById(assesmentId).populate({
                path: 'clientId',
                select: 'assesmentSignature dateOfBirth'
            });

            if (!clinicalDetailsPopulated) {
                return res.sendError("Please complete the assessment before downloading!");
            }

            if(clinicalDetailsPopulated?.clientId?.dateOfBirth){
                dateOfBirth = clinicalDetailsPopulated?.clientId?.dateOfBirth;
            }

            const finalDataObject = {
                dateOfSession: dateOfSession,
                dateOfBirth: dateOfBirth,
                asessmentData: clinicalDetailsPopulated
            }

            return res.sendSuccess(finalDataObject, "Clinical assessment data.");

        } catch (error) {
            return res.sendError(error);
        }
    }
}