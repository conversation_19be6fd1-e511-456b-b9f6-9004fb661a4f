import { NextFunction, Request, Response } from "express";
import Meeting from "../schemas/meeting-schema";
const axios = require('axios');
const { projectToken } = require('opentok-jwt');
const _ = require('lodash');
import { v4 as uuidv4 } from 'uuid';
import { CallingStatus, DMeeting, IMeeting } from "../models/meeting-model";
import Appointment from '../schemas/appointment-schema'; import {
  AppointmentStatus,
  ApprovalStatus,
  MeetingStatus,
} from "../models/appointment-model";
import { check, ValidationChain, validationResult } from "express-validator";
import { UserRole } from "../models/user-model";
import moment = require("moment");
import FriendRequest from "../schemas/friend-request-schema";
import { FriendRequestStatus } from "../models/friend-request-model";
import { VideoCallDao } from "../dao/videocall-dao";
import { PremiumStatus, SubscriptionStatus } from "../models/client-model";
import { ZoomVideoCallHelper } from "../helpers/zoom-video-call-helper";
import User from "../schemas/user-schema";
import { EmailService } from "../mail/config";
import { Util } from "../common/util";
import { AppLogger } from "../common/logging";
import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import AWS = require("aws-sdk");
import { ZoomVideoCallEP } from "./zoom-video-call-ep";
import { SMSService } from "../sms/config";
import { Types } from "mongoose";
import Upload from './../schemas/upload-schema';
import * as fs from 'fs';
import { FeedbackDao } from "../dao/feedback-dao";

const { createClient } = require('@deepgram/sdk');
const apiKey = process.env.TOKBOX_API_KEY;
const secret = process.env.TOKBOX_SECRET;
const opentokUrl = 'https://api.opentok.com/v2/project';
const OpenTok = require('opentok');
const opentok = new OpenTok(apiKey, secret);

export namespace VonageCallEp {
  const roomToSessionIdDictionary: any = {};

  export async function createVonageSession(req: Request, res: Response) {

    const roomName = req.params.name;
    let sessionId;
    let token;

    const tokenOptions = {
      role: "moderator",
      expireTime: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60,
    };

    if (roomToSessionIdDictionary[roomName]) {
      sessionId = roomToSessionIdDictionary[roomName];
      token = opentok.generateToken(sessionId, tokenOptions);
      res.setHeader('Content-Type', 'application/json');
      res.send({
        apiKey: apiKey,
        sessionId: sessionId,
        token: token,
      });
    } else {
      opentok.createSession({ mediaMode: 'routed' }, function (err: any, session: any) {
        if (err) {
          res.status(500).send({ error: 'createSession error:' + err });

          return;
        }

        roomToSessionIdDictionary[roomName] = session.sessionId;

        // generate token
        token = opentok.generateToken(session.sessionId, tokenOptions);
        res.setHeader('Content-Type', 'application/json');
        res.send({
          apiKey: apiKey,
          sessionId: session.sessionId,
          token: token,
        });
      });
    }
  }

  export async function generateVonageToken(req: Request, res: Response) {
    try {
      const tokenOptions = {
        role: "moderator",
        expireTime: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60,
      };
      const sessionId = req.params.token;

      const token = opentok.generateToken(sessionId, tokenOptions,);

      return res.sendSuccess({
        apiKey: apiKey,
        sessionId: sessionId,
        token: token,
      }, "Success");
    } catch (error) {
      console.log(error);
    }
  }

  export async function getMeetingByMeetingIdForVonage(req: Request, res: Response) {
    const meetingId = req.params.id;
    if (!meetingId) {
      return res.sendError(
        "Meeting id is required!"
      )
    }

    const meeting = await Meeting.findById(meetingId)
    if (!meeting) {
      return res.sendError("Meeting not found!")
    }

    return res.sendSuccess(meeting, "Success")

  }

  export async function startArchive(req: Request, res: Response) {
    const sessionId = req.params.sessionId;
    const date = new Date();
    AppLogger.info(`start archive function called for session ID: ${sessionId}`)
    try {
      var archiveOptions = {
        name: "Important Presentation",
        hasVideo: false, // Record audio only
      };
      opentok.startArchive(sessionId, archiveOptions, async function (err: any, archive: any) {
        if (err) {
          AppLogger.error(`Start archive error occured for sssion ID: ${sessionId} error: ${err}`)
          const updateArchiveIdInMeeting = await Meeting.findOneAndUpdate(
            { meetingId: sessionId, },
            { $set: {
              accepted: true,
              callingStatus: CallingStatus.ONGOING,
              bothJoinedAt: date,
              participantCount: 2
            } },
            { new: true }
          )
          return res.sendError("Start archive error occured");
        } else {
          AppLogger.info(`Started archive for session ID: ${sessionId}`)
          const updateArchiveIdInMeeting = await Meeting.findOneAndUpdate(
            { meetingId: sessionId, },
            { $set: {
              vonageArchiveId: archive.id,
              accepted: true,
              callingStatus: CallingStatus.ONGOING,
              bothJoinedAt: date,
              participantCount: 2
            } },
            { new: true }
          )
          AppLogger.info(`Updated participant count and changed calling status for session ID: ${sessionId}`)
          return res.sendSuccess(archive.id)
        }
      });
    } catch (error) {
      return res.sendError(error.message)
    }
  }

  export async function stopArchive(req: Request, res: Response) {
    const roomName = req.params.roomName;
    AppLogger.info(`archive stopped function called for roomName: ${roomName}, role: ${req.user.role}`)
    try {
      const preMeetingData: any = await Meeting.findOne({
        vonageSessionName: roomName
      }).select('vonageArchiveId');
      console.log(preMeetingData);
      opentok.stopArchive(preMeetingData.vonageArchiveId, function (err: any, archive: any) {
        if (err) {
          console.log(err);
          return res.sendError('No archive id found');
        }

        return res.sendSuccess(`Archive stopped successfully for archieve ID: ${preMeetingData.vonageArchiveId}`)
      });

    } catch (error) {
      return res.sendError(error.message)
    }
  }

  export async function subscribeForVonage(req: Request, res: Response) {
    const sessionId = req.params.sessionId;

    try {
      const opentokStream = opentok.listStreams(sessionId, (err: any, stream: any) => {
        if (err) {
          console.log(err);
        }
      });
    } catch (error) {
      console.log(error);
    }
  }

  export async function cancelVonageCall( req: Request, res: Response, next: NextFunction ) {
    try {
      AppLogger.info(`Cancel vonage call function called for sessionId :${req?.body?.sessionName}`);
      const preMeetingData: any = await Meeting.findOne({
        vonageSessionName: req.body.sessionName,
        callingStatus: { $nin: ['completed', 'cancelled'] }
      })
      if (!preMeetingData) {
        return res.sendError("Meeting already completed or cancelled!")
      }
      let participantCount = preMeetingData.participantCount;
      if(!participantCount){
        participantCount = 1
      }
      let isBothUserJoined = false;
      if(participantCount > 1) {
        isBothUserJoined = true;
      } else {
        isBothUserJoined = false;
      }
      AppLogger.info(`sessionId :${req?.body?.sessionName}, participantCount: ${participantCount}, isAppointmentBased: ${preMeetingData?.isAppointmentBased}, callingStatus: ${preMeetingData?.callingStatus}`);
      AppLogger.info(`sessionId :${req?.body?.sessionName}, isBothUserJoined: ${isBothUserJoined}`);
        if (preMeetingData.isAppointmentBased) {
          if (
            preMeetingData.callingStatus === CallingStatus.ONGOING ||
            preMeetingData.callingStatus === CallingStatus.STARTED
          ) {
            await Meeting.findByIdAndUpdate(preMeetingData._id, {
              callingStatus:
                (preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined)
                  ? CallingStatus.COMPLETED
                  : CallingStatus.CANCELLED
            });
            await Appointment.findByIdAndUpdate(preMeetingData.appointmentId, {
              meetingStatus:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? MeetingStatus.COMPLETED
                  : MeetingStatus.PENDING,
              status:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? AppointmentStatus.COMPLETED
                  : AppointmentStatus.PENDING,
            });
          }
          const result = await ZoomVideoCallEP.createDiagnosisNote(
            preMeetingData._id
          );
        } else {
          if (
            preMeetingData.callingStatus === CallingStatus.ONGOING ||
            preMeetingData.callingStatus === CallingStatus.STARTED
          ) {

            await Meeting.findByIdAndUpdate(preMeetingData._id, {
              callingStatus:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? CallingStatus.COMPLETED
                  : CallingStatus.CANCELLED
            });
          }
          const result = await ZoomVideoCallEP.createDiagnosisNote(preMeetingData._id);
        }
      return res.sendSuccess(preMeetingData);

    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Call Cancelled Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  const listArchivesAsync = () => {
    return new Promise((resolve, reject) => {
      opentok.listArchives((error: any, archives: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(archives);
        }
      });
    });
  };

  const transcribeUrl = async (recordUrl: any) => {

    try {
      const deepgram = createClient(process.env.DEEPGRAM_API_KEY);
      const { result, error } = await deepgram.listen.prerecorded.transcribeUrl(
        {
          url: recordUrl,
        },
        {
          smart_format: true,
          model: "nova-2",
          diarize: true
        }
      );

      if (error) throw error;
      if (!error) {
        return result
      }
    } catch (error) {
      console.log(error);
    }
  };

  //start
  const transcribeVoiceRecordUrl = async (filePath: string) => {

    try {
      const deepgram = createClient(process.env.DEEPGRAM_API_KEY);

      if (!fs.existsSync(filePath)) {
        console.log("path not in their")
        return
        // proceed with reading the file
       }

      fs.createReadStream(filePath)
        .on('error', err => {
              console.error('Error reading the file:', err);
       })
         .on('data', chunk => {
               console.log('File is being read without issues.');
       });

      const {result, error} = await deepgram.listen.prerecorded.transcribeFile(

        fs.readFileSync(filePath),
        {
          smart_format: true,
          model: "nova-2",
          // diarize: true
        }
      );

      console.log("$$$$$$$$")

      if (error) throw error;
      if (!error) {
         console.log(result)
        return result
      }

    } catch (error) {
      console.error(error)
    }
  }
  //end

  const generateAWSPresignedURL = async ({ region, bucket, key }: any) => {

    try {
      const client = new S3Client({
        region, credentials: {
          accessKeyId: process.env.AWS_S3_ACCESSKEY_ID,
          secretAccessKey: process.env.AWS_S3_SECRET_ACCESSKEY,
        }
      });

      const command = new GetObjectCommand({ Bucket: bucket, Key: key });

      return getSignedUrl(client, command, { expiresIn: 10 * 60 });

    } catch (error) {
      console.log("Error generating AWS presigned URL: ", error);
    }
  }

  const processDeepgramData = async (preMeetingData: any, archiveData: any, sessionId: string): Promise<void> => {

      try {
        const region = process.env.AWS_S3_REGION;
        const bucket = process.env.AWS_S3_BUCKET;
        const key = `47819541/${preMeetingData?.vonageArchiveId}/archive.mp4`;
        const clientAWSURL = await generateAWSPresignedURL({ region, bucket, key });
        if (!clientAWSURL) {
          AppLogger.error(`Vonage Express | Error when generating presigned URL. SessionId: ${sessionId}, archiveId: ${preMeetingData?.vonageArchiveId}`);
          return;
        }

        const transcribeList = await transcribeUrl(clientAWSURL);

        if (!transcribeList) {
          AppLogger.error(`Vonage Express | Transcribes not found!. SessionId: ${sessionId}, archiveId: ${preMeetingData?.vonageArchiveId}`);
          return;
        }

        const conversationArray = transcribeList?.results?.channels[0]?.alternatives[0]?.paragraphs?.paragraphs;
        const deepgramText = [];
        let speaker0Count = 0;
        let speaker1Count = 0;
        let speaker2Count = 0;
        let speaker0: any;
        let speaker1: any;
        const constructedSentences = [];

        if (Array.isArray(conversationArray) && conversationArray.length > 0) {
          for (const conversation of conversationArray) {
            const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
            deepgramText.push(`${conversation?.speaker} : ${sentences}`);
          }
          conversationArray?.forEach((paragraph: any) => {
            if (paragraph?.speaker == 0) {
              speaker0Count += 1;
            }
            if (paragraph?.speaker == 1) {
              speaker1Count += 1;
            }
            if (paragraph?.speaker == 2) {
              speaker2Count += 1;
            }
          });
          if (preMeetingData?.firstSpeaker?.role == 'THERAPIST') {
            speaker0 = 'THERAPIST';
            speaker1 = 'CLIENT';
          } else {
            speaker0 = 'CLIENT';
            speaker1 = 'THERAPIST';
          }
          if (speaker2Count == 0) {
            AppLogger.info(`Vonage Express | Deepgram ,There are 1 or 2 speakers in the deepgram!`);
            for (const conversation of conversationArray) {
              const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
              if (conversation.speaker == 0) {
                constructedSentences.push(`${speaker0} : ${sentences}`)
              } else {
                constructedSentences.push(`${speaker1} : ${sentences}`)
              }
            }
          } else {
            AppLogger.info(`Vonage Express | Deepgram ,There is a 3 speakers in the deepgram. Accuracy can be low!`);
            let smallestSpeakerCount;
            let smallestSpeaker;
            let filteredConversationArray;
            // Find the smallest count among speakers
            if (speaker0Count <= speaker1Count && speaker0Count <= speaker2Count) {
              smallestSpeakerCount = speaker0Count;
              smallestSpeaker = 0;
              filteredConversationArray = conversationArray.filter((paragraph: any) => paragraph.speaker !== 0);
              for (const conversation of filteredConversationArray) {
                const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
                if (conversation.speaker == 1) {
                  constructedSentences.push(`${speaker0} : ${sentences}`)
                } else {
                  constructedSentences.push(`${speaker1} : ${sentences}`)
                }
              }
            } else if (speaker1Count <= speaker0Count && speaker1Count <= speaker2Count) {
              smallestSpeakerCount = speaker1Count;
              smallestSpeaker = 1;
              filteredConversationArray = conversationArray.filter((paragraph: any) => paragraph.speaker !== 1);
              for (const conversation of filteredConversationArray) {
                const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
                if (conversation.speaker == 0) {
                  constructedSentences.push(`${speaker0} : ${sentences}`)
                } else {
                  constructedSentences.push(`${speaker1} : ${sentences}`)
                }
              }
            } else {
              smallestSpeakerCount = speaker2Count;
              smallestSpeaker = 2;
              filteredConversationArray = conversationArray.filter((paragraph: any) => paragraph.speaker !== 2);
              for (const conversation of filteredConversationArray) {
                const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
                if (conversation.speaker == 0) {
                  constructedSentences.push(`${speaker0} : ${sentences}`)
                } else {
                  constructedSentences.push(`${speaker1} : ${sentences}`)
                }
              }
            }
          }
        } else {
          console.error("conversationArray is not an array or is null/undefined");
        }


        const dataForTranscribe: any = {
          transcriptText: constructedSentences,
          clientId: preMeetingData?.clientId,
          meetingId: preMeetingData?.meetingId,
          therapistId: preMeetingData?.therapistId,
          transCribeInProcess: false,
          speakersArray: [
            preMeetingData?.clientId,
            preMeetingData?.therapistId
          ],
          meetingStartedTime: preMeetingData?.createdAt,
          videoUrl: "",
          speakersDetected: false,
          deepgramText: deepgramText
        }

        await VideoCallDao.createTranscribe(dataForTranscribe);

        if (archiveData?.duration && archiveData?.duration !== null && archiveData?.duration > 0) {
          const meetingCreatedAt: Date = preMeetingData?.createdAt;
          const now: Date = new Date();
          const differenceInMilliseconds: number = now.getTime() - meetingCreatedAt.getTime();
          const differenceInHours: number = differenceInMilliseconds / (1000 * 60 * 60);

          if(preMeetingData?.participantCount == 2 && differenceInHours > 2 && preMeetingData?.callingStatus == CallingStatus.CANCELLED){
            if (preMeetingData?.isAppointmentBased){
              await Meeting.findByIdAndUpdate(preMeetingData._id, {
                transcribeCreated: true,
                spentDuration: parseFloat((archiveData?.duration / 60).toFixed(2)),
                callingStatus: CallingStatus.COMPLETED
              })
              await Appointment.findOneAndUpdate(
                { meetingId: preMeetingData?.meetingId },
                { status: AppointmentStatus.COMPLETED, meetingStatus: MeetingStatus.COMPLETED }
              )
            } else {
              await Meeting.findByIdAndUpdate(preMeetingData._id, {
                transcribeCreated: true,
                spentDuration: parseFloat((archiveData?.duration / 60).toFixed(2)),
                callingStatus: CallingStatus.COMPLETED
              })
            }

          } else {
            await Meeting.findByIdAndUpdate(preMeetingData._id, {
              transcribeCreated: true,
              spentDuration: parseFloat((archiveData?.duration / 60).toFixed(2))
            })
          }
          AppLogger.info(`Vonage Express | Updated transcribe successfully for meetingId: ${preMeetingData._id}`)

        }
      } catch (error) {
        console.log(error);
      }
  }

  //start
  const processDeepgramDataForVoiceRecord = async (preMeetingData: any, sessionId: string) => {


      try {
        const region = process.env.AWS_S3_REGION;
        const bucket = process.env.AWS_S3_BUCKET_REGULER_AUDIO_CALLS;
        const key = preMeetingData.s3AudioPath;

        const clientAWSURL = await generateAWSPresignedURL({ region, bucket, key });

        if (!clientAWSURL) {
          AppLogger.error(`Deepgram ,Error when generating presigned URL. SessionId: ${sessionId}`);
          return;
        }

        const transcribeList = await transcribeUrl(clientAWSURL);

        if (!transcribeList) {
          AppLogger.error(`Deepgram ,Transcribes not found!. SessionId: ${sessionId}, archiveId: ${preMeetingData?.vonageArchiveId}`);
          return;
        }

        const conversationArray = transcribeList?.results?.channels[0]?.alternatives[0]?.paragraphs?.paragraphs;
        const deepgramText = [];
        let speaker0Count = 0;
        let speaker1Count = 0;
        let speaker2Count = 0;
        let speaker0: any;
        let speaker1: any;
        const constructedSentences = [];

        if (Array.isArray(conversationArray) && conversationArray.length > 0) {
          for (const conversation of conversationArray) {
            const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
            deepgramText.push(`${conversation?.speaker} : ${sentences}`);
          }
          conversationArray?.forEach((paragraph: any) => {
            if (paragraph?.speaker == 0) {
              speaker0Count += 1;
            }
            if (paragraph?.speaker == 1) {
              speaker1Count += 1;
            }
            if (paragraph?.speaker == 2) {
              speaker2Count += 1;
            }
          });

          if (preMeetingData?.firstSpeaker?.role == 'THERAPIST') {

            speaker0 = 'THERAPIST';
            speaker1 = 'CLIENT';
          } else {

            speaker0 = 'CLIENT';
            speaker1 = 'THERAPIST';
          }
          if (speaker2Count == 0) {
            AppLogger.info(`Deepgram ,There are 1 or 2 speakers in the deepgram!`);
            for (const conversation of conversationArray) {
              const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
              if (conversation.speaker == 0) {
                constructedSentences.push(`${speaker0} : ${sentences}`)
              } else {
                constructedSentences.push(`${speaker1} : ${sentences}`)
              }
            }
          } else {
            AppLogger.info(`Deepgram ,There is a 3 speakers in the deepgram. Accuracy can be low!`);
            let smallestSpeakerCount;
            let smallestSpeaker;
            let filteredConversationArray;
            // Find the smallest count among speakers
            if (speaker0Count <= speaker1Count && speaker0Count <= speaker2Count) {
              smallestSpeakerCount = speaker0Count;
              smallestSpeaker = 0;
              filteredConversationArray = conversationArray.filter((paragraph: any) => paragraph.speaker !== 0);
              for (const conversation of filteredConversationArray) {
                const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
                if (conversation.speaker == 1) {
                  constructedSentences.push(`${speaker0} : ${sentences}`)
                } else {
                  constructedSentences.push(`${speaker1} : ${sentences}`)
                }
              }
            } else if (speaker1Count <= speaker0Count && speaker1Count <= speaker2Count) {
              smallestSpeakerCount = speaker1Count;
              smallestSpeaker = 1;
              filteredConversationArray = conversationArray.filter((paragraph: any) => paragraph.speaker !== 1);
              for (const conversation of filteredConversationArray) {
                const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
                if (conversation.speaker == 0) {
                  constructedSentences.push(`${speaker0} : ${sentences}`)
                } else {
                  constructedSentences.push(`${speaker1} : ${sentences}`)
                }
              }
            } else {
              smallestSpeakerCount = speaker2Count;
              smallestSpeaker = 2;
              filteredConversationArray = conversationArray.filter((paragraph: any) => paragraph.speaker !== 2);
              for (const conversation of filteredConversationArray) {
                const sentences = conversation.sentences.map((sentence: any) => sentence.text).join(' ');
                if (conversation.speaker == 0) {
                  constructedSentences.push(`${speaker0} : ${sentences}`)
                } else {
                  constructedSentences.push(`${speaker1} : ${sentences}`)
                }
              }
            }
          }
        } else {
          console.error("conversationArray is not an array or is null/undefined");
        }


        const dataForTranscribe: any = {
          transcriptText: constructedSentences,
          clientId: preMeetingData?.clientId,
          meetingId: preMeetingData?.meetingId,
          therapistId: preMeetingData?.therapistId,
          transCribeInProcess: false,
          speakersArray: [
            preMeetingData?.clientId,
            preMeetingData?.therapistId
          ],
          meetingStartedTime: preMeetingData?.createdAt,
          videoUrl: "",
          speakersDetected: false,
          deepgramText: deepgramText
        }

        //check already create transcribe
        const transcribe = await VideoCallDao.getTranscribeByMeetingId(preMeetingData?.meetingId);

        if(!transcribe) {
           await VideoCallDao.createTranscribe(dataForTranscribe);

           await Meeting.findByIdAndUpdate(preMeetingData._id, {
              transcribeCreated: true,
           });

          AppLogger.info(`Updated transcribe successfully for meetingId: ${preMeetingData._id}`)
        }

      } catch (error) {
         console.log(error)
      }

    }
  //end

  export async function updateSpentDurationForVonageNativeCalls() {
    try {
      AppLogger.info(`Vonage Native | Started spent duration update job for vonage native meetings`);

      const allMeetings = await Meeting.find({
        vonageArchiveId: { $exists: true },
        callingStatus: { $in: [CallingStatus.COMPLETED] },
        isVonageNativeSDKCall: { $eq : true},
        spentDuration: { $exists: false },
        participantCount: { $eq: 2 }
      });

      console.log(allMeetings.length);
      
      const promises = allMeetings.map(async (item: any) => {
        const vonageArchiveId = item.vonageArchiveId;
        const sessionId = item.meetingId;
        const archiveDataRes = await getArchiveByArchiveId(vonageArchiveId);
        if (archiveDataRes.success){
          const archiveData = archiveDataRes.archive;
          if (!archiveData) {
            AppLogger.error(`Vonage Native | No archive data found for sessionId: ${sessionId}`);
            return;
          }
  
          if (archiveData?.duration && archiveData?.duration !== null && archiveData?.duration > 0) {
            const meeting = await Meeting.findOneAndUpdate(
              {meetingId: sessionId}, 
              {spentDuration: parseFloat((archiveData?.duration / 60).toFixed(2)),}
            )
            AppLogger.info(`Vonage Native | Updated spent duration successfully for meetingId: ${item._id}`);
          } else {
            AppLogger.error(`Vonage Native | Spent duration updated error occured for sessionId: ${sessionId}`);
            return;
          }
        } else {
          AppLogger.error(`Vonage Native | Error occured when recieving archive info, sessionId: ${sessionId}, error: ${archiveDataRes.error}`);
          return;
        }
        
      });

      await Promise.all(promises);

      AppLogger.info(`Vonage Native | All meetings spent durations updated for meetings`);
    } catch (error) {
      AppLogger.error('Vonage Native | spent durations update failed!')
    }
  }

  export async function getArchiveByDeepgramForVonageExpressCalls() {
    try {
      AppLogger.info(`Vonage Express | Started Transcribe update job`);

      const allMeetings = await Meeting.find({
        transcribeCreated: false,
        firstSpeaker: { $exists: true },
        vonageArchiveId: { $exists: true },
        callingStatus: { $in: [CallingStatus.COMPLETED, CallingStatus.CANCELLED] },
        isVonageNativeSDKCall: { $ne : true}
      }).populate({
        path: 'firstSpeaker',
        select: 'role'
      });

      const archives: any = await listArchivesAsync();

      const promises = allMeetings.map(async (item: any) => {
        const vonageArchiveId = item.vonageArchiveId;
        const sessionId = item.meetingId;
        const archiveDataRes = await getArchiveByArchiveId(vonageArchiveId);
        if (archiveDataRes.success){
          const archiveData = archiveDataRes.archive;
          if (!archiveData) {
            AppLogger.error(`Vonage Express | Deepgram ,No archive data found for sessionId: ${sessionId}`);
            return;
          }
  
          await processDeepgramData(item, archiveData, sessionId);
        } else {
          AppLogger.error(`Vonage Express | Error occured when recieving archive info, sessionId: ${sessionId}, error: ${archiveDataRes.error}`);
          return;
        }
      });

      await Promise.all(promises);

      AppLogger.info(`Vonage Express | All meetings transcribes updated for meetings`);
    } catch (error) {
      AppLogger.error('Vonage Express | Deepgram ,Transcribes update failed!')
    }
  }

  //start
  export async function getAchiveAudioRecordsByDeepgram() {
    try {
      AppLogger.info(`Session Records | Start Transcribe Update for voice Records job`);

      const allMeetings = await Meeting.find({
        transcribeCreated: false,
        vonageArchiveId: { $exists: false },
        firstSpeaker: { $exists: true },
        s3AudioPath: {$exists: true},
        meetingId: /Regular Call/ ,
        callingStatus: { $in: [CallingStatus.COMPLETED] }
      }).populate({
        path: 'firstSpeaker',
        select: 'role'
      });




      const promises = allMeetings.map(async (item: any) => {
        const sessionId = item.meetingId;
         await processDeepgramDataForVoiceRecord(item, sessionId);

      });

     await Promise.all(promises);
     AppLogger.info(`Session Records | All meetings transcribes updated for meetings`);

    } catch (error) {
       AppLogger.error('Session Records | Deepgram ,Transcribes Voice Records update failed!');
    }
  }

  //end

  export function joinVonageMeetingValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isString()
        .withMessage("Invalid meetingId."),
      check("isAppointmentBased")
        .notEmpty()
        .withMessage("isAppointmentBased is required")
        .isBoolean()
        .withMessage("isAppointmentBased is not a boolean type."),
    ];
  }

  export async function joinVonageMeeting(req: Request, res: Response, next: NextFunction) {
    try {
      let remainingCallDurationJoinedCall;
      let appointmentId;
      let clientId: any;
      let therapistId: any;
      let appointmentData;
      let recieverId;

      let meetingId;
      let hasValidMeetingId = false;
      let preMeetingData;
      const transcribeAllowedForUser = req.body?.transcribeAllowedForUser;

      // Check meeting and update spent duration
      if (req.body.isAppointmentBased) {
        if (req.body.appointmentId == null) {
          return res.sendError(
            "appointmentId id required for appointment based calls."
          );
        }
        appointmentId = req.body.appointmentId;
        appointmentData = await Appointment.findById(appointmentId);

        if (!appointmentData || !appointmentData.meetingId) {
          return res.sendError("Invalid appointmentId ");
        }

        meetingId = appointmentData.meetingId;
      } else {
        meetingId = req.body.meetingId;
      }

      if (req.body.isAppointmentBased) {
        if (req.user.role === UserRole.CLIENT) {
          preMeetingData = await Meeting.findOneAndUpdate(
            { meetingId: meetingId },
            { clientAllowedTranscribe: transcribeAllowedForUser }
          );
        } else {
          preMeetingData = await Meeting.findOneAndUpdate(
            { meetingId: meetingId },
            { therapistAllowedTranscribe: transcribeAllowedForUser }
          );
        }
      } else {
        if (req.user.role === UserRole.CLIENT) {
          preMeetingData = await Meeting.findByIdAndUpdate(
            meetingId,
            {
              clientAllowedTranscribe: transcribeAllowedForUser,
            },
            { new: true }
          );
        } else {
          preMeetingData = await Meeting.findByIdAndUpdate(
            meetingId,
            {
              therapistAllowedTranscribe: transcribeAllowedForUser,
            },
            { new: true }
          );
        }
      }

      if (preMeetingData) {
        remainingCallDurationJoinedCall = preMeetingData.meetingDuration;

        clientId = preMeetingData.clientId;
        therapistId = preMeetingData.therapistId;

        if (req.user.role === UserRole.CLIENT) {
          recieverId = therapistId;
        } else {
          recieverId = clientId;
        }

        if (
          preMeetingData.callingStatus != CallingStatus.ONGOING &&
          preMeetingData.callingStatus != CallingStatus.STARTED
        ) {
          return res.sendError("invalid meeting calling status");
        }
        if (
          !preMeetingData.clientIdentifier ||
          !preMeetingData.therapistIdentifier ||
          !preMeetingData.password
        ) {
          return res.sendError("Meeting id found in appointment but not in DB");
        }

        remainingCallDurationJoinedCall = preMeetingData.meetingDuration;

        if (
          preMeetingData.callingStatus === CallingStatus.ONGOING &&
          preMeetingData.bothJoinedAt &&
          preMeetingData.meetingDuration
        ) {
          let remainingTime = preMeetingData.meetingDuration;
          const totalMeetingDuration = preMeetingData.meetingDuration;
          const bothJoinedTime = preMeetingData.bothJoinedAt;
          const currentTime = new Date();
          const timeDifference = moment.duration(
            moment(currentTime).diff(moment(bothJoinedTime))
          );

          const timeDifferenceAsMinutes = timeDifference.asMinutes();
          const finalTimeDifference = Math.round(timeDifferenceAsMinutes);

          if (finalTimeDifference >= 0) {
            remainingTime = totalMeetingDuration - finalTimeDifference;
            if (remainingTime >= 0) {
              remainingCallDurationJoinedCall = remainingTime;
              const updatedMeeting = await Meeting.findOneAndUpdate(
                { meetingId: meetingId },
                { spentDuration: finalTimeDifference },
                { new: true }
              );
            }

            if (remainingTime <= 0) {
              if (preMeetingData.isAppointmentBased) {
                await Meeting.findByIdAndUpdate(preMeetingData._id, {
                  callingStatus:
                    preMeetingData.callingStatus === CallingStatus.ONGOING
                      ? CallingStatus.COMPLETED
                      : CallingStatus.CANCELLED,
                });
                await Appointment.findByIdAndUpdate(
                  preMeetingData.appointmentId,
                  {
                    meetingStatus:
                      preMeetingData.callingStatus === CallingStatus.ONGOING
                        ? MeetingStatus.COMPLETED
                        : MeetingStatus.PENDING,
                  }
                );
              } else {
                await Meeting.findByIdAndUpdate(preMeetingData._id, {
                  callingStatus:
                    preMeetingData.callingStatus === CallingStatus.ONGOING
                      ? CallingStatus.COMPLETED
                      : CallingStatus.CANCELLED,
                });
              }

              return res.sendError("Meeting time exceeded for this call");
            }
          } else {
            res.sendError("invalid meeting time");
          }

        }
      } else {
        return res.sendError("Invalid Meeting Id");
      }

      const friendRequest = await FriendRequest.findOne({
        clientId: clientId,
        therapistId: therapistId,
        status: FriendRequestStatus.APPROVED,
      });

      if (friendRequest == null) {
        return res.sendError("You are no longer matched with this client.", 453);
      }

      if (preMeetingData.isAppointmentBased) {
        if (preMeetingData.appointmentId == null) {
          return res.sendError(
            "appointmentId id required for appointment based calls."
          );
        }
        appointmentId = preMeetingData.appointmentId;

        appointmentData = await Appointment.findById(appointmentId);
        if (!appointmentData) {
          return res.sendError("Invalid appointmentId ");
        }

        if (appointmentData.typeOfMeeting !== "VIDEO") {
          return res.sendError("Not a type of video appointment.");
        }

        if (appointmentData.status === AppointmentStatus.COMPLETED) {
          return res.sendError("Already Completed.", 455);
        }

        if (appointmentData.status === AppointmentStatus.OVERDUE) {
          return res.sendError("Overdue Appointment.");
        }

        if (appointmentData.status === AppointmentStatus.REJECTED) {
          return res.sendError("Rejected Appointment.");
        }

        if (
          appointmentData.meetingStatus &&
          (appointmentData.meetingStatus === MeetingStatus.STARTED ||
            appointmentData.meetingStatus === MeetingStatus.ONGOING) &&
          appointmentData.meetingId
        ) {
          if (preMeetingData) {
            hasValidMeetingId = true;
          } else {
            return res.sendError("Invalid Meeting Id");
          }
        } else {
          return res.sendError("Meeting Cancelled", 332);
        }
      } else {
        if (preMeetingData) {
          hasValidMeetingId = true;
          if (
            preMeetingData.callingStatus === CallingStatus.STARTED ||
            preMeetingData.callingStatus === CallingStatus.ONGOING
          ) {
          } else {
            return res.sendError("Meeting Cancelled", 333);
          }
        } else {
          return res.sendError("Invalid Meeting Id.");
        }
      }

      const avatarDetailsOfRecieverWithMeetingData =
        await VideoCallDao.getAvatarDetailsForStartCallWithMeetingDetals(
          recieverId,
          req.user.role,
          req.user._id,
          remainingCallDurationJoinedCall
        );

      if (!avatarDetailsOfRecieverWithMeetingData) {
        return res.sendError("invalid recieverId.");
      }

      if (
        avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus !==
        PremiumStatus.ACTIVE
      ) {
        if (
          avatarDetailsOfRecieverWithMeetingData.testSubscriptionStatus !==
          "active"
        ) {
          if (
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ==
            null ||
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ===
            "" ||
            avatarDetailsOfRecieverWithMeetingData.clientSubStatus !==
            SubscriptionStatus.ACTIVE
          ) {
            // return res.sendError("No Valid Subscription for client.", 456);
          }

          if (!avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained) {
            return res.sendError("Meeting time exceeded.", 457);
          }
        }
      }

      const avatarDetailsOfOwnUser =
        await VideoCallDao.getAvatarDetailsForStartCall(req.user._id);

      if (!avatarDetailsOfOwnUser) {
        return res.sendError("invalid user.");
      }

      const previousMeetingId = preMeetingData._id.toString();

      const sdkToken = await ZoomVideoCallHelper.genarateZoomSDKToken(
        previousMeetingId,
        1,
        req.user.role === UserRole.CLIENT
          ? preMeetingData.clientIdentifier
          : preMeetingData.therapistIdentifier,
        previousMeetingId,
        preMeetingData.password
      );

      const recieversLatestDetails = await User.findById(recieverId);
      const recieverSocketId = recieversLatestDetails.socketId;
      const finalMeetingData = {
        remainingMeetingTimeForCurrentMeeting: remainingCallDurationJoinedCall,
        recordingAllowed: hasValidMeetingId
          ? preMeetingData.recordingAllowed
          : preMeetingData.recordingAllowed,
        sdkToken: sdkToken,
        callIdFromVideoSDK: meetingId,
        clientSubscriptionId:
          avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId,
        clientSubStatus: avatarDetailsOfRecieverWithMeetingData.clientSubStatus,
        clientPremiumStatus:
          avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus,
        isMeetingTimeRemained:
          avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained,
        remainingMeetingTime:
          avatarDetailsOfRecieverWithMeetingData.remainingMeetingTime,
        password: preMeetingData.password,
        meetingId: preMeetingData._id,
        sessionId: preMeetingData.meetingId,
        clientIdentifier: preMeetingData.clientIdentifier,
        therapistIdentifier: preMeetingData.therapistIdentifier,
        isAudioCall: preMeetingData?.isAudioCall,
      };

      const finalRecieverData = {
        userId: recieverId,
        useDefaultAvatar:
          avatarDetailsOfRecieverWithMeetingData.useDefaultAvatar,
        avatarId: avatarDetailsOfRecieverWithMeetingData.avatarId,
        avatarBackgroundId:
          avatarDetailsOfRecieverWithMeetingData.avatarBackgroundId,
        incognito: avatarDetailsOfRecieverWithMeetingData.incognito,
        socketId: recieverSocketId,
        callerName: avatarDetailsOfRecieverWithMeetingData.callerName,
        callRecordingAllowed:
          avatarDetailsOfRecieverWithMeetingData.callRecordingAllowed,
      };

      const finalOwnUserData = {
        useDefaultAvatar: avatarDetailsOfOwnUser.useDefaultAvatar,
        avatarId: avatarDetailsOfOwnUser.avatarId,
        avatarBackgroundId: avatarDetailsOfOwnUser.avatarBackgroundId,
      };

      const finalData = {
        meetingData: finalMeetingData,
        recieverData: finalRecieverData,
        ownData: finalOwnUserData,
      };

      return res.sendSuccess(finalData, "Call Details");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Join Zoom Meeting Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function initializeVonageVideoCallValidation(): ValidationChain[] {
    return [
      check("recieverId")
        .notEmpty()
        .withMessage("recieverId is required")
        .isMongoId()
        .withMessage("Invalid recieverId."),
      // check("appointmentId")
      //   .optional()
      //   .isMongoId()
      //   .withMessage("Invalid appointmentId."),
      check("callDuration")
        .notEmpty()
        .withMessage("callDuration is required")
        .isInt({ min: 0, max: 60 })
        .withMessage("Your meeting duration is not within 0 and 60 minutes."),
      check("isTranscribeAllowed")
        .notEmpty()
        .withMessage("isTranscribeAllowed is required/..")
        .isBoolean()
        .withMessage("isTranscribeAllowed is not a boolean type."),
      check("isAppointmentBased")
        .notEmpty()
        .withMessage("isAppointmentBased is required")
        .isBoolean()
        .withMessage("isAppointmentBased is not a boolean type."),
    ];
  }

  export async function initializeVonageVideoCall(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }
      AppLogger.info(`Initiate call | User attempted to Initiate call. userId: ${req.user?._id}`);

      let appointmentId;
      let appointmentData;
      let clientId;
      let therapistId;
      let callDuration = parseInt(req.body.callDuration);
      const transcribeAllowedForLoggedUser =
        req.body.transcribeAllowedForLoggedUser;
      const isAudioCall = req.body.isAudioCall;
      if (req.user.role === UserRole.CLIENT) {
        clientId = req.user._id;
        therapistId = req.body.recieverId;
      } else {
        therapistId = req.user._id;
        clientId = req.body.recieverId;
      }

      const friendRequest = await FriendRequest.findOne({
        clientId: clientId,
        therapistId: therapistId,
        status: FriendRequestStatus.APPROVED,
      });

      if (friendRequest == null) {
        AppLogger.error(`Initiate call | You are not friend with other user. therapitId: ${therapistId}, clientId: ${clientId}`);
        return res.sendError("You are no longer matched with this client.", 453);
      }

      const avatarDetailsOfRecieverWithMeetingData =
        await VideoCallDao.getAvatarDetailsForStartCallWithMeetingDetals(
          req.body.recieverId,
          req.user.role,
          req.user._id,
          callDuration
        );
      if (!avatarDetailsOfRecieverWithMeetingData) {
        AppLogger.error(`Initiate call | Avatar details not found for recieverId: ${req.body?.recieverId}, userId: ${req.user?._id}`);
        return res.sendError("invalid recieverId.");
      }
      // if (
      //   avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus !==
      //   PremiumStatus.ACTIVE
      // ) {
      //   if (
      //     avatarDetailsOfRecieverWithMeetingData.testSubscriptionStatus !==
      //     "active"
      //   ) {
      //     if (
      //       avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ==
      //         null ||
      //       avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ===
      //         "" ||
      //       avatarDetailsOfRecieverWithMeetingData.clientSubStatus !==
      //         SubscriptionStatus.ACTIVE
      //     ) {
      //       return res.sendError("No Valid Subscription for client.", 456);
      //     }

      //     if (!avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained) {
      //       return res.sendError("Meeting time exceeded.", 457);
      //     }
      //   }
      // }

      const avatarDetailsOfOwnUser =
        await VideoCallDao.getAvatarDetailsForStartCall(req.user._id);

      if (!avatarDetailsOfOwnUser) {
        AppLogger.error(`Initiate call | Avatar details not found for userId: ${req.user._id}`);
        return res.sendError("invalid user.");
      }

      if (req.body.isAppointmentBased) {
        if (req.body.appointmentId == null) {
          AppLogger.error(`Initiate call | appointmentId id required for appointment based calls. userId: ${req.user?._id}`);
          return res.sendError(
            "appointmentId id required for appointment based calls."
          );
        }
        appointmentId = req.body.appointmentId;

        appointmentData = await Appointment.findById(appointmentId);

        if (!appointmentData) {
          AppLogger.error(`Initiate call | No appointment data found. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
          return res.sendError("Invalid appointmentId ");
        }

        if (
          appointmentData.meetingStatus &&
          appointmentData.meetingId &&
          appointmentData.meetingStatus == MeetingStatus.STARTED
        ) {
          AppLogger.error(`Initiate call | session already started. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
          return res.sendError("This session already started. Check your inbox to find URL");
        }
        if (
          appointmentData.meetingStatus &&
          appointmentData.meetingStatus != MeetingStatus.PENDING
        ) {
          AppLogger.error(`Initiate call | session already started. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
          return res.sendError("This session already started. Check your inbox to find URL");
        }

        if (
          appointmentData.therapistId.toString() != therapistId ||
          appointmentData.clientId.toString() != clientId
        ) {
          AppLogger.error(`Initiate call | You dont have permission to access this appointment. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
          return res.sendError(
            "You don't have permission to access this appointment"
          );
        }

        if (appointmentData.typeOfMeeting !== "VIDEO") {
          AppLogger.error(`Initiate call | Not a type of video appointment. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
          return res.sendError("Not a type of video appointment.");
        }

        // if (appointmentData.approvedStatus !== ApprovalStatus.APPROVED) {
        //   return res.sendError("Appointment Not Approved yet.", 454);
        // }

        if (appointmentData.status === AppointmentStatus.COMPLETED) {
          AppLogger.error(`Initiate call | Appointment already Completed. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
          return res.sendError("Already Completed.", 455);
        }
        if (appointmentData.status === AppointmentStatus.OVERDUE) {
          AppLogger.error(`Initiate call | Overdue Appointment. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
          return res.sendError("Overdue Appointment.");
        }

        if (appointmentData.status === AppointmentStatus.REJECTED) {
          AppLogger.error(`Initiate call | Rejected Appointment. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
          return res.sendError("Rejected Appointment.");
        }

        // if (appointmentData.status === AppointmentStatus.WAITING_FOR_APPROVAL) {
        //   return res.sendError("Appointment is not approved yet.");
        // }

        // if (appointmentData.status !== AppointmentStatus.PENDING) {
        //   return res.sendError("Invalid appointment status found.");
        // }

        if (
          appointmentData.meetingId &&
          appointmentData.meetingStatus == MeetingStatus.STARTED
        ) {
          // if (
          //   !appointmentData.meetingStatus ||
          //   appointmentData.meetingStatus != MeetingStatus.STARTED ||
          //   appointmentData.meetingId == ""
          // ) {
          //   return res.sendError("Invalid appointment status found.");
          // }
          console.log("XXXXXXXXXXXXXXXXXXX")
          const previousMeetingData = await Meeting.findOne({
            meetingId: appointmentData.meetingId,
          });
          if (!previousMeetingData) {
            AppLogger.error(`Initiate call | Previous meeting data not found. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
            return res.sendError("Invalid meeting found");
          }
          if (
            (previousMeetingData.callingStatus != CallingStatus.STARTED,
              !previousMeetingData.clientIdentifier ||
              !previousMeetingData.therapistIdentifier ||
              !previousMeetingData.password)
          ) {
            AppLogger.error(`Initiate call | Meeting id found in appointment but not in DB. appointmentId: ${appointmentId}, userId: ${req.user?._id}`);
            return res.sendError(
              "Meeting id found in appointment but not in DB"
            );
          }

          const dataForSend = {
            alreadyStarted: false,
            password: previousMeetingData.password,
            meetingId: previousMeetingData._id,
            sessionId: previousMeetingData.meetingId,
            clientIdentifier: previousMeetingData.clientIdentifier,
            therapistIdentifier: previousMeetingData.therapistIdentifier,
          };
          AppLogger.info(`Initiate call | Successfully initiate appointmentbased call. appointmentId: ${appointmentId}, userId: ${req.user?._id}`)
          return res.sendSuccess(dataForSend, "call initialization details");
        }
      }

      let newClientIdentifier;
      let newTherapistIdentifier;
      let newPassword;

      newClientIdentifier = await Util.getRandomInt(10000, 100000);
      newTherapistIdentifier = await Util.getRandomInt(10000, 100000);

      while (newClientIdentifier == newTherapistIdentifier) {
        newTherapistIdentifier = await Util.getRandomInt(10000, 100000);
      }

      newPassword = await Util.getRandomInt(10000, 100000);

      let meetingDetails: DMeeting;

      if (req.body.isAppointmentBased) {
        meetingDetails = {
          clientId: clientId,
          therapistId: therapistId,
          transcribeAllowed: req.body.isTranscribeAllowed,
          // transcribeAllowed: false,
          transcribingInProcess: false,
          accepted: false,
          noOfVideose: 0,
          transcribeCreated: false,
          meetingDuration: callDuration,
          isAppointmentBased: true,
          appointmentId: appointmentId,
          audioFiles: [],
          recordingAllowed:
            avatarDetailsOfRecieverWithMeetingData.callRecordingAllowed &&
              avatarDetailsOfOwnUser.callRecordingAllowed
              ? true
              : false,
          // recordingAllowed: false,
          callingStatus: CallingStatus.INITIALIZED,
          createdBy: req.user._id,
          recordingSharedWithClient: false,
          clientIdentifier: newClientIdentifier.toString(),
          therapistIdentifier: newTherapistIdentifier.toString(),
          password: newPassword.toString(),
          isAudioCall,
          participantCount: 1
        };

        if (req.user.role === UserRole.CLIENT) {
          meetingDetails.clientAllowedTranscribe =
            transcribeAllowedForLoggedUser;
        } else {
          meetingDetails.therapistAllowedTranscribe =
            transcribeAllowedForLoggedUser;
        }
      } else {
        meetingDetails = {
          clientId: clientId,
          therapistId: therapistId,
          transcribeAllowed: req.body.isTranscribeAllowed,
          // transcribeAllowed: false,
          transcribingInProcess: false,
          accepted: false,
          noOfVideose: 0,
          transcribeCreated: false,
          meetingDuration: callDuration,
          isAppointmentBased: false,
          audioFiles: [],
          recordingAllowed:
            avatarDetailsOfRecieverWithMeetingData.callRecordingAllowed &&
              avatarDetailsOfOwnUser.callRecordingAllowed
              ? true
              : false,
          // recordingAllowed: false,
          callingStatus: CallingStatus.INITIALIZED,
          createdBy: req.user._id,
          recordingSharedWithClient: false,
          clientIdentifier: newClientIdentifier.toString(),
          therapistIdentifier: newTherapistIdentifier.toString(),
          password: newPassword.toString(),
          isAudioCall,
          therapistAllowedTranscribe: transcribeAllowedForLoggedUser,
          participantCount: 1
        };
      }
      if (req.body.regularMeetingDate) {
        meetingDetails.regularMeetingDate = req.body.regularMeetingDate;
      }
      if (req.body.regularMeetingStartTime) {
        meetingDetails.regularMeetingStartTime =
          req.body.regularMeetingStartTime;
      }

      const createdMeeting = await VideoCallDao.createMeeting(meetingDetails);

      if (!createdMeeting) {
        AppLogger.error(`Initiate call | Meeting Creation Failed. clientId: ${clientId}, therapistId: ${therapistId}`);
        return res.sendError("Meeting Creation Failed.");
      }

      const dataForSend = {
        alreadyStarted: false,
        password: createdMeeting.password,
        meetingId: createdMeeting._id,
        clientIdentifier: createdMeeting.clientIdentifier,
        therapistIdentifier: createdMeeting.therapistIdentifier,
      };
      
      AppLogger.info(`Initiate call | Call initialization success. clientId: ${clientId}, therapistId: ${therapistId}`);
      return res.sendSuccess(dataForSend, "call initialization details");
    } catch (error) {
      AppLogger.error(`Initiate call | call initialization failed. userId: ${req.user?._id}`);
      return res.sendError("call initialization failed.");
    }
  }

  export function startCallValidation(): ValidationChain[] {
    return [
      check("meetingId")
        .notEmpty()
        .withMessage("meetingId is required")
        .isMongoId()
        .withMessage("Invalid appointmentId."),
      check("sessionName")
        .notEmpty()
        .withMessage("sessionName is required")
        .isString()
        .withMessage("Invalid sessionName."),
      check("recieverId")
        .notEmpty()
        .withMessage("recieverId is required")
        .isMongoId()
        .withMessage("Invalid recieverId."),
      // check("appointmentId")
      //   .optional()
      //   .isMongoId()
      //   .withMessage("Invalid appointmentId."),
      check("callDuration")
        .notEmpty()
        .withMessage("callDuration is required")
        .isInt({ min: 0, max: 60 })
        .withMessage("Your meeting duration is not within 0 and 60 minutes."),
      check("isTranscribeAllowed")
        .notEmpty()
        .withMessage("isTranscribeAllowed is required/..")
        .isBoolean()
        .withMessage("isTranscribeAllowed is not a boolean type."),
      check("isAppointmentBased")
        .notEmpty()
        .withMessage("isAppointmentBased is required")
        .isBoolean()
        .withMessage("isAppointmentBased is not a boolean type."),
    ];
  }

  export async function startVonageVideoCall(req: Request, res: Response, next: NextFunction) {
    try {

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      let appointmentId;
      let clientId;
      let therapistId;
      let appointmentData;
      let callDuration = parseInt(req.body.callDuration);
      // global
      // let hasValidMeetingId = false;
      let callIdFromVideoSDK;
      let preMeetingData;

      if (req.user.role === UserRole.CLIENT) {
        clientId = req.user._id;
        therapistId = req.body.recieverId;
      } else {
        therapistId = req.user._id;
        clientId = req.body.recieverId;
      }

      const alreadyCreatedMeetingId = req.body.meetingId;
      const vonageSessionName = req.body.sessionName;

      const alreadyCreatedMeetingData = await Meeting.findById(
        alreadyCreatedMeetingId
      );

      if (!alreadyCreatedMeetingData) {
        return res.sendError("invalid meeting id");
      }
      if (alreadyCreatedMeetingData.meetingId) {
        return res.sendError("invalid meeting id");
      }

      if (
        alreadyCreatedMeetingData.callingStatus != CallingStatus.INITIALIZED
      ) {
        return res.sendError("invalid meeting status");
      }

      const friendRequest = await FriendRequest.findOne({
        clientId: clientId,
        therapistId: therapistId,
        status: FriendRequestStatus.APPROVED,
      });

      if (friendRequest == null) {
        return res.sendError("You are no longer matched with this client.", 453);
      }

      if (alreadyCreatedMeetingData.isAppointmentBased) {
        if (!alreadyCreatedMeetingData.appointmentId) {
          return res.sendError("appointmentId id not found.");
        }
        appointmentId = alreadyCreatedMeetingData.appointmentId;

        appointmentData = await Appointment.findById(appointmentId);

        if (!appointmentData) {
          return res.sendError("Invalid appointmentId ");
        }

        if (
          appointmentData.meetingStatus &&
          appointmentData.meetingId &&
          appointmentData.meetingStatus == MeetingStatus.STARTED
        ) {
          return res.sendError("meeting already started");
        }
        if (
          appointmentData.meetingStatus &&
          appointmentData.meetingStatus != MeetingStatus.PENDING
        ) {
          return res.sendError("meeting already started");
        }

        if (appointmentData.typeOfMeeting !== "VIDEO") {
          return res.sendError("Not a type of video appointment.");
        }

        // if (appointmentData.approvedStatus !== ApprovalStatus.APPROVED) {
        //   return res.sendError("Appointment Not Approved yet.", 454);
        // }

        if (appointmentData.status === AppointmentStatus.COMPLETED) {
          return res.sendError("Already Completed.", 455);
        }
        if (appointmentData.status === AppointmentStatus.OVERDUE) {
          return res.sendError("Overdue Appointment.");
        }

        if (appointmentData.status === AppointmentStatus.REJECTED) {
          return res.sendError("Rejected Appointment.");
        }

        // if (appointmentData.status === AppointmentStatus.WAITING_FOR_APPROVAL) {
        //   return res.sendError("Appointment is not approved yet.");
        // }

        // if (appointmentData.status !== AppointmentStatus.PENDING) {
        //   return res.sendError("Invalid appointment status found.");
        // }
      }

      const avatarDetailsOfRecieverWithMeetingData =
        await VideoCallDao.getAvatarDetailsForStartCallWithMeetingDetals(
          req.body.recieverId,
          req.user.role,
          req.user._id,
          callDuration
        );
      if (!avatarDetailsOfRecieverWithMeetingData) {
        return res.sendError("invalid recieverId.");
      }
      if (
        avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus !==
        PremiumStatus.ACTIVE
      ) {
        if (
          avatarDetailsOfRecieverWithMeetingData.testSubscriptionStatus !==
          "active"
        ) {
          if (
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ==
            null ||
            avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId ===
            "" ||
            avatarDetailsOfRecieverWithMeetingData.clientSubStatus !==
            SubscriptionStatus.ACTIVE
          ) {
            return res.sendError("No Valid Subscription for client.", 456);
          }

          if (!avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained) {
            return res.sendError("Meeting time exceeded.", 457);
          }
        }
      }

      const avatarDetailsOfOwnUser =
        await VideoCallDao.getAvatarDetailsForStartCall(req.user._id);

      if (!avatarDetailsOfOwnUser) {
        return res.sendError("invalid user.");
      }

      const updatedMeetingData = await Meeting.findByIdAndUpdate(
        alreadyCreatedMeetingId,
        { vonageSessionName: vonageSessionName, callingStatus: CallingStatus.STARTED }
      );
      if (!updatedMeetingData) {
        return res.sendError("meeting details update failed.");
      }
      if (req.body.isAppointmentBased) {
        await Appointment.findByIdAndUpdate(appointmentId, {
          meetingStatus: MeetingStatus.STARTED,
          // meetingId: alreadyCreatedMeetingId,
          meetingStartedBy: req.user._id,
        });
      }

      if (req.body.isAppointmentBased) {
        await VideoCallDao.updateAppointmentCallingTries(
          appointmentId,
          req.user.role
        );
      }

      const recieversLatestDetails = await User.findById(req.body.recieverId);
      const recieverSocketId = recieversLatestDetails.socketId;
      const finalMeetingData = {
        recordingAllowed: updatedMeetingData.recordingAllowed,
        callIdFromVideoSDK: callIdFromVideoSDK,
        clientSubscriptionId:
          avatarDetailsOfRecieverWithMeetingData.clientSubscriptionId,
        clientSubStatus: avatarDetailsOfRecieverWithMeetingData.clientSubStatus,
        clientPremiumStatus:
          avatarDetailsOfRecieverWithMeetingData.clientPremiumStatus,
        isMeetingTimeRemained:
          avatarDetailsOfRecieverWithMeetingData.isMeetingTimeRemained,
        remainingMeetingTime:
          avatarDetailsOfRecieverWithMeetingData.remainingMeetingTime,
        isAudioCall: updatedMeetingData?.isAudioCall,
      };

      const finalRecieverData = {
        useDefaultAvatar:
          avatarDetailsOfRecieverWithMeetingData.useDefaultAvatar,
        avatarId: avatarDetailsOfRecieverWithMeetingData.avatarId,
        avatarBackgroundId:
          avatarDetailsOfRecieverWithMeetingData.avatarBackgroundId,
        incognito: avatarDetailsOfRecieverWithMeetingData.incognito,
        socketId: recieverSocketId,
        callerName: avatarDetailsOfRecieverWithMeetingData.callerName,
        callRecordingAllowed:
          avatarDetailsOfRecieverWithMeetingData.callRecordingAllowed,
      };

      const finalOwnUserData = {
        useDefaultAvatar: avatarDetailsOfOwnUser.useDefaultAvatar,
        avatarId: avatarDetailsOfOwnUser.avatarId,
        avatarBackgroundId: avatarDetailsOfOwnUser.avatarBackgroundId,
      };

      const finalData = {
        meetingData: finalMeetingData,
        recieverData: finalRecieverData,
        ownData: finalOwnUserData,
      };

      const userForSendEmails = await User.findById(req.body.recieverId).select('email primaryPhone');
      const ownDetails = await User.findById(req.user._id).select('firstname lastname');
      
      if (userForSendEmails && ownDetails) {
        await EmailService.sendEventMeetingLinkEmail(
          userForSendEmails,
          "New Meeting Started",
          "New Meeting Started by",
          // `Please login to ${process.env.APP_URL}/signin to join meeting.
          `\n\nJoin Meeting: ${process.env.APP_URL}/room/${vonageSessionName}`,
          ownDetails.firstname + " " + ownDetails.lastname
        );

        if (userForSendEmails.primaryPhone) {
          await SMSService.sendEventSMS(
            `New Meeting Started by ${ownDetails.firstname} ${ownDetails.lastname}
            \n\nJoin Meeting: ${process.env.APP_URL}/room/${vonageSessionName}`,
            userForSendEmails.primaryPhone,
            "Vonage-call-ep 01"
          );
        }
      }

      return res.sendSuccess(finalData, "Call Details");
    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Start Zoom Video Call Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export function updateSessionIdByRoomNameValidation(): ValidationChain[] {
    return [
      check("sessionId")
        .notEmpty()
        .withMessage("sessionId is required")
        .isString()
        .withMessage("Invalid sessionId."),
    ];
  }

  export async function updateSessionIdByRoomName(req: Request, res: Response, next: NextFunction) {
    try {
      const { roomName } = req.params;
      const sessionId = req.body.sessionId;
      const meeting = await Meeting.findOneAndUpdate(
        { vonageSessionName: roomName, meetingId: { $exists: false } },
        { meetingId: sessionId },
        { new: true }
      )
      if (!meeting) {
        return res.sendSuccess("Meeting already updated!")
      }
      if (meeting.isAppointmentBased) {
        const appoinmentId = meeting?.appointmentId;

        const appoinment = await Appointment.findByIdAndUpdate(
          appoinmentId,
          { meetingId: sessionId }
        )
      }
      return res.sendSuccess("SessionId updated successfully in Meeting table and Appoinment table!")
    } catch (error) {
      return res.sendError("Update session by room name failed!")
    }
  }

  export async function updateFirstSpeakerInMeeting(req: Request, res: Response) {
    try {
      const sessionName = req.body.sessionName;
      const firstSpeaker = req.body.firstSpeaker;

      AppLogger.info(`Attempting to update first speaker in session. sessionName: ${sessionName}, firstSpeaker: ${firstSpeaker}`);

      const meeting = await Meeting.findOneAndUpdate(
        { vonageSessionName: sessionName, firstSpeaker: { $exists: false } },
        { $set: { firstSpeaker: firstSpeaker } },
        { new: true }
      );

      if (!meeting) {
        AppLogger.info(`Session not found for update speaker in session. sessionName: ${sessionName}`);
        return res.sendError("Meeting not found!");
      }
      AppLogger.info(`Successfully updated first speaker in session. sessionName: ${sessionName}, firstSpeaker: ${firstSpeaker}`);
      return res.sendSuccess(meeting);
    } catch (error) {
      AppLogger.info(`Error updating fist speaker. sessionName: ${req.body.sessionName}, error: ${error}`);
      return res.sendError(error);
    }
  }

  export async function getMeetingDetailsBySessionName(req: Request, res: Response) {
    try {
      const { roomName } = req.params;
      console.log("roomName ", roomName)
      const meeting = await Meeting.findOne(
        { vonageSessionName: roomName }
      );
      console.log(meeting);

      if (!meeting) {
        return res.sendError("Meeting not found!");
      }
      return res.sendSuccess(meeting);
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getMeetingByMeetingIdField(req: Request, res: Response) {
    try {
      const { meetingId } = req.params;
      AppLogger.info(`Getting meeting by meetingId field: ${meetingId}`);
      
      if (!meetingId) {
        return res.sendError("Meeting ID is required!");
      }

      const meeting = await VideoCallDao.getMeetingByMeetingIdOnly(meetingId);
      
      if (!meeting) {
        return res.sendError("Meeting not found!");
      }
      
      return res.sendSuccess(meeting);
    } catch (error) {
      AppLogger.error(`Error getting meeting by meetingId field: ${error}`);
      return res.sendError(error);
    }
  }

  export async function createSessionForNetworkTest(req: Request, res: Response) {
    try {

      opentok.createSession({ mediaMode: 'routed' }, function(err: any, session: any) {
        if (err) return console.log(err);
        const sessionId = session.sessionId;

        // Generate a token with moderator role
        const tokenOptions = {
          role: 'moderator'
        };
        let token;
        try {
          token = opentok.generateToken(sessionId, tokenOptions);
        } catch (tokenError) {
          console.error(tokenError);
          return res.sendError(tokenError);
        }
        return res.sendSuccess({
          apiKey,
          sessionId,
          token
        })
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateParticipantCountInAllMeetings(req: Request, res: Response) {
    try {
      const updateMeetings = await VideoCallDao.updateParticipantCountInAllMeetings();
      return res.sendSuccess(updateMeetings)
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updatecallingStatusInAllMeetings(req: Request, res: Response) {
    try {
      const updateMeetings = await VideoCallDao.updatecallingStatusInAllMeetings();
      return res.sendSuccess(updateMeetings)
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function busyVonageCall( req: Request, res: Response, next: NextFunction ) {
    try {
      const {meetingId} = req.body;
      const preMeetingData: any = await Meeting.findById(meetingId)
      if (!preMeetingData) {
        return res.sendError("Meeting already completed or cancelled!")
      }
      let participantCount = preMeetingData.participantCount;
      if(!participantCount){
        participantCount = 1
      }
      let isBothUserJoined = false;
      if(participantCount > 1) {
        isBothUserJoined = true;
      } else {
        isBothUserJoined = false;
      }
        if (preMeetingData.isAppointmentBased) {
          if (
            preMeetingData.callingStatus === CallingStatus.ONGOING ||
            preMeetingData.callingStatus === CallingStatus.STARTED
          ) {
            await Meeting.findByIdAndUpdate(preMeetingData._id, {
              callingStatus:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? CallingStatus.COMPLETED
                  : CallingStatus.CANCELLED
            });
            await Appointment.findByIdAndUpdate(preMeetingData.appointmentId, {
              meetingStatus:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? MeetingStatus.COMPLETED
                  : MeetingStatus.PENDING,
              status:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? AppointmentStatus.COMPLETED
                  : AppointmentStatus.PENDING,
            });
          }
          const result = await ZoomVideoCallEP.createDiagnosisNote(
            preMeetingData._id
          );
        } else {
          if (
            preMeetingData.callingStatus === CallingStatus.ONGOING ||
            preMeetingData.callingStatus === CallingStatus.STARTED
          ) {

            await Meeting.findByIdAndUpdate(preMeetingData._id, {
              callingStatus:
                preMeetingData.callingStatus === CallingStatus.ONGOING && isBothUserJoined
                  ? CallingStatus.COMPLETED
                  : CallingStatus.CANCELLED
            });
          }
          const result = await ZoomVideoCallEP.createDiagnosisNote(preMeetingData._id);
        }
      return res.sendSuccess(preMeetingData);

    } catch (error) {
      await EmailService.sendAdminEmailWhenCallingExeptionEmail(
        "Call Cancelled Server Error Occured",
        error
      );
      return res.sendError("Server Error Occured. " + error);
    }
  }

  export async function checkClientHasCompletedSessionsOnThisWeek(req: Request, res: Response, next: NextFunction ): Promise<any> {
    try {
      console.log(req.body)
      const clientId = req.body.clientId;
      const therapistId = req.body.therapistId;
      const selectedDate = req.body?.selectedDate;
      let currentDate = new Date(selectedDate);
      let day = currentDate.getUTCDay();

      let diffToMonday = day === 0 ? 6 : day - 1;
      let monday = new Date(currentDate);
      monday.setDate(currentDate.getDate() - diffToMonday);
      let endOfSunday = new Date(monday);
      endOfSunday.setDate(monday.getDate() + 7);



      const meetingsCount = await VideoCallDao.checkClientHasCompletedSessionsOnThisWeek(Types.ObjectId(clientId), Types.ObjectId(therapistId), monday, endOfSunday);
      console.log(meetingsCount);

      let isCompletedThisWeekMeeting = false;

      if (meetingsCount > 0) {
        isCompletedThisWeekMeeting = true
      }
      return res.sendSuccess({isCompletedThisWeekMeeting})
    } catch (error) {
      return res.sendError(error)
    }
  }

  function getArchiveByArchiveId(archiveId: string): Promise<{ success: boolean; archive?: any; error?: any }> {
    return new Promise((resolve) => {
      try {
        opentok.getArchive(archiveId, function (err: any, archive: any) {
          if (err) {
            resolve({
              success: false,
              error: err
            });
          } else {
            resolve({
              success: true,
              archive
            });
          }
        });
      } catch (error) {
        resolve({
          success: false,
          error
        });
      }
    });
  }

  export async function saveVonageSessionFeedback(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { meetingId, rating, feedback, therapistComfortable, satisfied, nextSessionScheduled, needCall, createdBy } = req.body;
      
      if (!meetingId || !rating || !createdBy) {
        return res.sendError('Missing required fields');
      }

      if (!Types.ObjectId.isValid(createdBy)) {
        return res.sendError('Invalid createdBy ID');
      }
      
      AppLogger.info(`Save vonage session feedback for user ID ${createdBy}`);
      
      const data = {
        meetingId: meetingId,
        rate: rating, // Map 'rating' from request to 'rate' in the database
        feedback: feedback,
        therapistComfortable: therapistComfortable,
        satisfied: satisfied,
        nextSessionScheduled: nextSessionScheduled,
        needCall: needCall,
        createdBy: Types.ObjectId(createdBy)
      };
      
      const sessionFeedback = await FeedbackDao.createSessionFeedback(data);
      
      // Tìm thông tin chi tiết về meeting để đưa vào email
      // Tìm theo meetingId (string) thay vì _id (ObjectId)
      console.log('Finding meeting with meetingId:', meetingId);
      const meeting = await Meeting.findOne({ meetingId: meetingId }).populate('clientId therapistId');
      console.log('Meeting found:', meeting ? 'Yes' : 'No', 'with client and therapist populated:', meeting?.clientId ? 'Yes' : 'No', meeting?.therapistId ? 'Yes' : 'No');
      
      // Lấy thông tin client
      const clientDoc = meeting?.clientId as any; // The populated client document
      const clientName = clientDoc?.firstname ? `${clientDoc.firstname} ${clientDoc.lastname || ''}` : 'Client';
      console.log('Client Name:', clientName);
      
      // Lấy thông tin therapist
      const therapistDoc = meeting?.therapistId as any; // The populated therapist document
      const therapistName = therapistDoc?.firstname ? `${therapistDoc.firstname} ${therapistDoc.lastname || ''}` : 'Therapist';
      console.log('Therapist Name:', therapistName);
      const meetingDate = meeting?.regularMeetingDate ? new Date(meeting.regularMeetingDate).toLocaleDateString() : (meeting?.createdAt ? new Date(meeting.createdAt).toLocaleDateString() : 'Unknown date');
      
      // Gửi email khi satisfied = false
      console.log('satisfied', satisfied);
      console.log('satisfied==false', satisfied === false);
      if(needCall === true){
        const subject = 'Client Request for Clinical Director Contact';
        const body = `
          <h2>Client Request for Clinical Director Contact</h2>
          <p>We've received a post-session form from a client indicating they were ${satisfied === false ? 'not satisfied' : 'satisfied'} with their recent session and have requested to be contacted by the Clinical Director.</p>
          <p><strong>Client:</strong> ${clientName}</p>
          <p><strong>Therapist:</strong> ${therapistName}</p>
          <p><strong>Session Date:</strong> ${meetingDate}</p>
          <p><strong>Feedback:</strong> ${feedback || 'No feedback provided'}</p>
          <p>You can view the full details by logging in to the admin portal and navigating to the Session Feedback section.</p>
          <p>Please take a moment to review the information and follow up with the client at your earliest convenience. Let us know if you need anything from our side.</p>
        `;
        
        // Gửi email cho cả haris và crystal
        await EmailService.sendEmailToSelectedUsers('<EMAIL>', body, subject);
        await EmailService.sendEmailToSelectedUsers('<EMAIL>', body, subject);
        AppLogger.info(`Sent needCall email for meeting ID ${meetingId}`);
      } else if (satisfied === false) {
        const subject = 'Client Dissatisfaction Report';
        const body = `
          <h2>Client Dissatisfaction Report</h2>
          <p>A client has indicated they were not satisfied with their recent session.</p>
          <p><strong>Client:</strong> ${clientName}</p>
          <p><strong>Therapist:</strong> ${therapistName}</p>
          <p><strong>Session Date:</strong> ${meetingDate}</p>
          <p><strong>Feedback:</strong> ${feedback || 'No feedback provided'}</p>
          <p>You can view the full details by logging in to the admin portal and navigating to the Session Feedback section.</p>
        `;
        
        await EmailService.sendEmailToSelectedUsers('<EMAIL>', body, subject);
        AppLogger.info(`Sent dissatisfaction email for meeting ID ${meetingId}`);
      }
      
      return res.sendSuccess(sessionFeedback);
    } catch (error) {
      AppLogger.error(`Error occurred in save vonage session feedback for user ID ${req.body?.createdBy}`);
      console.log(error);
      return res.sendError('An error occurred while saving session feedback');
    }
  }
}