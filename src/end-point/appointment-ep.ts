import { NextFunction, Request, Response } from "express";
import {
  check,
  param,
  Val<PERSON><PERSON><PERSON>hain,
  validationResult,
} from "express-validator";
const moment = require('moment-timezone');
import { Types } from "mongoose";
import { AppLogger } from "../common/logging";
import { Util } from "../common/util";
import { AdminDao } from "../dao/admin-dao";
import { AppointmentDao } from "../dao/appointment-dao";
import { ClientDao } from "../dao/client-dao";
import { FriendRequestDao } from "../dao/friend-request-dao";
import { NotificationDao } from "../dao/notification-dao";
import { TherapistDao } from "../dao/therapist-dao";
import { UserDao } from "../dao/user-dao";
import { EmailService } from "../mail/config";
import {
  AppointmentStatus,
  ApprovalStatus,
  DAppointment,
  IAppointment,
  MeetingStatus,
  RepeatType,
} from "../models/appointment-model";
import {
  DNotification,
  NotificationEvent,
  NotificationVarient,
} from "../models/notification-model";
import { AppointmentSMSStatus, UserRole } from "../models/user-model";
import { SMSService } from "../sms/config";
import { AdminStatisticsDao } from "../dao/admin-statistics-dao";
import Appointment from "../schemas/appointment-schema";
import Meeting from "../schemas/meeting-schema";
import { CallingStatus } from "../models/meeting-model";
import Therapist from "../schemas/therapist-schema";
import { UserEp } from "./user-ep";

let mongoose = require("mongoose");

export namespace AppointmentEp {
  export function createAppointmentValidationRules() {
    return [
      check("therapistId")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Therapist Id is required."),
      check("clientId")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Client Id is required."),
      check("start")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Start time is required."),
      check("end")
        .isString()
        .not()
        .isEmpty()
        .withMessage("End time is required."),
      check("reminders").isArray().withMessage("Remider is required."),
      check("title").isString().withMessage("Title is required."),
      check("repeatInfo").isObject().withMessage("Repeat info is required."),
      check("color").isString().withMessage("Color is required."),
    ];
  }

  export function updateAppointmentValidationRules() {
    return [
      check("therapistId")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Therapist Id is required."),
      check("clientId")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Client Id is required."),
      check("appointmentId")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Appointment Id is required."),
      check("start")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Start time is required."),
      check("end")
        .isString()
        .not()
        .isEmpty()
        .withMessage("End time is required."),
      check("reminders").isArray().withMessage("Remider is required."),
      check("title").isString().withMessage("Title is required."),
      check("repeatInfo").isObject().withMessage("Repeat info is required."),
      check("color").isString().withMessage("Color is required."),
    ];
  }

  export function searchAppointmentValidationRules() {
    return [
      check("date")
        .not()
        .isEmpty()
        .withMessage("Date is required.")
        .isDate()
        .withMessage("Date is required."),
    ];
  }

  export function approvalStatusValidationRules() {
    return [
      check("appointmentId")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Appointment Id is required."),
      check("approvedStatus")
        .not()
        .isEmpty()
        .isString()
        .withMessage("Approved status is required."),
    ];
  }

  const getDatesDiff2 = (
    start_date: any,
    end_date: any,
    date_format = "YYYY-MM-DD"
  ) => {
    const diff = moment(end_date).diff(moment(start_date), "days") + 1;

    const dates = [];

    for (let i = 0; i < diff; i++) {
      const nextDate = moment(start_date).add(i, "day");
      const isWeekEndDay = nextDate.isoWeekday() > 7;

      if (!isWeekEndDay) dates.push(nextDate.format(date_format));
    }

    return dates;
  };

  function generateWeeklyAndBiWeeklyMessageForSent (appointmentDates: string[], type: string) {
    const formattedDatesString = appointmentDates
      .map(dateStr => moment.tz(dateStr, "America/New_York").format("MM/DD/YYYY"))
      .join(", ");

    const messageToSent = `Congratulations! New ${type} appointments have been scheduled on ${formattedDatesString} by`;
    return messageToSent;
  }

  export async function createAppointmentByClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.user._id;
    const appointment = req.body.appointment;
    const therapistId = appointment.therapistId;
    const clientId = appointment.clientId;

    const sessionStart = moment(appointment.start);
    const sessionEnd = moment(appointment.end);

    const sessionDuration = moment.duration(sessionEnd.diff(sessionStart)).asMinutes();

    if (!mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid therapist Id");
    }

    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid client Id");
    }

    try {
      if (role == UserRole.CLIENT) {
        let therapist = await TherapistDao.getUserById(therapistId);

        let client = await ClientDao.getUserById(clientId);

        if (!therapist || therapist == null) {
          return res.sendError(
            "No existing therapist for the provided therapist Id."
          );
        }

        if (!client || client == null) {
          return res.sendError(
            "No existing client for the provided client Id."
          );
        }

        let isFriend = await FriendRequestDao.checkIfUserIsFriend(
          client._id,
          therapist._id
        );

        if (!isFriend) {
          return res.sendError(
            "Sorry! You haven't connected with this Therapist yet."
          );
        }

        if (
          client.premiumStatus != "active" &&
          (client.subscriptionId == null ||
            client.subscriptionStatus != "active") &&
          client.testSubscriptionStatus != "active"
        ) {
          return res.sendError("Sorry! You don't have an active subscription.");
        }

        if (Util.invalidTimeCheck(appointment.start)) {
          return res.sendError("Sorry! You have selected invalid time.");
        }

        // if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
        //   return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
        // }

        const beginsAt = moment(appointment.start);
        const endsAt = moment(appointment.start);

        const startTime = moment(appointment.start).format("H:mm A");
        const endTime = moment(appointment.end).format("H:mm A");
        const appointmentStartTime = moment(appointment.start).format("YYYY-MM-DD HH:mm");
        const appointmentEndTime = moment(appointment.end).format("YYYY-MM-DD HH:mm");

        const mST = moment(startTime, "HH:mm").minute();

        if (mST != 0 && mST != 30) {
          return res.sendError("Please select valid start time.");
        }

        const datesOfWeek = Util.calculateWeekNumberAndDates(
          appointmentStartTime,
          client._id!,
          therapist._id!
        );

        const appointmentId = datesOfWeek.appointmentId;

        let sessionTimeOfWeek = 0;

        let appointmentsBySelectedWeek = await AppointmentDao.getAllAppointmentsBySelectedWeek(datesOfWeek);

        if (appointmentId) {
          appointmentsBySelectedWeek = appointmentsBySelectedWeek.filter(
            (appointment: IAppointment) =>
              appointmentId.toString() !== appointment._id.toString()
          );
        }

        if (appointmentsBySelectedWeek.length) {
          for (const session of appointmentsBySelectedWeek) {
            const startTime = moment(session.start, "HH:mm:ss a");
            const endTime = moment(session.end, "HH:mm:ss a");
            const duration = moment.duration(endTime.diff(startTime));
            const minutes = parseInt(duration.asMinutes().toString());

            sessionTimeOfWeek = sessionTimeOfWeek + minutes;
          }
        }

        const sessionDetails = {
          selectedDate: datesOfWeek.selectedDate,
          allSessionOfWeek: appointmentsBySelectedWeek,
          sessionTimeOfWeek: sessionTimeOfWeek,
          allSession: appointmentsBySelectedWeek.length,
        };

        if (
          appointmentsBySelectedWeek === undefined ||
          appointmentsBySelectedWeek == null
        ) {
          return res.sendError(
            "Server error occured. Please reach to support."
          );
        }

        if (Util.skipCheckingForPremiumUsers(client)) {
          const meetingDuration = Util.getMeetingTimeDuration(
            startTime,
            endTime
          );

          if (
            sessionDetails.sessionTimeOfWeek != 0 &&
            sessionDetails.sessionTimeOfWeek != 30 &&
            sessionDetails.sessionTimeOfWeek != 60
          ) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          }

          if (sessionDetails.sessionTimeOfWeek != 0) {
            if (
              sessionDetails.sessionTimeOfWeek == 30 &&
              meetingDuration == 60
            ) {
              return res.sendError(
                "Sorry! Client's weekly session time has exceeded."
              );
            } else if (sessionDetails.sessionTimeOfWeek == 60) {
              return res.sendError(
                "Sorry! Client's weekly session time has exceeded."
              );
            }
          }
        }

        if (!startTime) {
          return res.sendError("Please select valid start time.");
        }

        const workingDaysOfTherapist: string[] = [];

        therapist.workingHours?.map((session: any) => {
          if (!workingDaysOfTherapist.includes(session.day))
            workingDaysOfTherapist.push(session.day);
        });

        if (
          !moment(new Date()).isBefore(
            moment(
              new Date(appointmentStartTime).setHours(
                parseInt(startTime.split(":")[0]),
                parseInt(startTime.split(":")[1]),
                0,
                0
              )
            )
          )
        ) {
          return res.sendError(
            "Sorry! You can't create appointment in a past date!"
          );
        }

        if (!appointment.title) {
          return res.sendError("Please add title.");
        }

        const timeDifferenceInHours = moment.duration(endsAt.diff(beginsAt)).asHours();

        if (timeDifferenceInHours > 1) {
          return res.sendError("You can create one hour sessions only.");
        }

        if (appointment.repeatInfo?.repeatType == RepeatType.DOES_NOT_REPEAT) {
          let appointementsInTheCurrentSlots =
            await AppointmentDao.getAppointmentsOfTherapistByStartTime(
              new Date(appointment.start),
              new Date(appointment.end),
              sessionDuration,
              Types.ObjectId(appointment.therapistId)
            );

          if (appointementsInTheCurrentSlots.length > 0) {
            return res.sendError(
              role == "CLIENT"
                ? "Therapist has already scheduled an appointment during the selected time slot."
                : "You have already scheduled an appointment during the selected time slot."
            );
          }

          let appointementsInTheCurrentSlotsOfClient =
            await AppointmentDao.getAppointmentsOfClientByStartTime(
              new Date(appointment.start),
              new Date(appointment.end),
              sessionDuration,
              Types.ObjectId(appointment.clientId)
            );

          if (appointementsInTheCurrentSlotsOfClient.length > 0) {
            return res.sendError(
              role == "CLIENT"
                ? "You have already scheduled an appointment during the selected time slot."
                : "Client has already scheduled an appointment during the selected time slot."
            );
          }

          if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
            return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
          }

          const appointmentDetails: DAppointment = {
            therapistId: therapistId,
            clientId: clientId,
            start: new Date(appointment.start),
            end: new Date(appointment.end),
            title: appointment.title,
            reminders: appointment.reminders,
            typeOfMeeting: "VIDEO",
            createdBy: userId,
            color: appointment.color,
            groupId: appointment.groupId,
            status: AppointmentStatus.PENDING,
            approvedStatus: ApprovalStatus.APPROVED,
            repeatInfo: {
              repeatType: appointment.repeatInfo.repeatType,
              interval: appointment.repeatInfo.interval,
              repeatDays: {
                sunday: appointment.repeatInfo.repeatDays.sunday,
                monday: appointment.repeatInfo.repeatDays.monday,
                tuesday: appointment.repeatInfo.repeatDays.tuesday,
                wednesday: appointment.repeatInfo.repeatDays.wednesday,
                thursday: appointment.repeatInfo.repeatDays.thursday,
                friday: appointment.repeatInfo.repeatDays.friday,
                saturday: appointment.repeatInfo.repeatDays.saturday,
              },
              endingDate: appointment.repeatInfo.endingDate,
              endingAfter: appointment.repeatInfo.endingAfter,
              endingType: appointment.repeatInfo.endingType,
            },
          };

          const newAppointment = await AppointmentDao.createAppointment(
            appointmentDetails
          );

          const dataForCalendar = {
            start: appointment.start,
            end: appointment.end,
            title: appointment.title,
          }
          
          const calendarRes = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendar, therapistId);
          if (calendarRes){
            AppLogger.info(`Google calendar sync successfully by client, therapistId: ${therapistId}, start time: ${appointment.start}, end time: ${appointment.end}`);
          } else {
            AppLogger.error(`Google calendar sync failed by client, therapistId: ${therapistId}, start time: ${appointment.start}, end time: ${appointment.end}`);
          }

          const utcTime = moment.utc(appointment.start);

          const estTime = utcTime.tz('America/New_York');

          await EmailService.sendEventEmail(
            therapist,
            "New appointment is created!",
            `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by`,
            "Click here to connect with the client.",
            client.firstname + " " + client.lastname
          );

          if (therapist?.primaryPhone) {
            await SMSService.sendEventSMS(
              `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by ${client.firstname} ${client.lastname}`,
              therapist?.primaryPhone,
              "Appointment-ep 01"
            );
          }

          return res.sendSuccess(
            newAppointment,
            "Appointment Created Successfully."
          );
        } else if (appointment.repeatInfo?.repeatType == RepeatType.BI_WEEKLY) {
          const daysOf42 = Util.calculateDays(
            42,
            appointmentStartTime,
            client._id!,
            therapist._id!
          );

          let appointmentsInNext42Days = await AppointmentDao.getAllAppointmentsBySelectedWeek(daysOf42);

          if (appointmentsInNext42Days.length > 0) {
            if (
              client?.premiumStatus != "active" &&
              sessionDetails.sessionTimeOfWeek == 0
            ) {
              return res.sendError(
                "You have already scheduled sessions during next 8 weeks."
              );
            }
          }

          const dateAStartBW = moment(appointmentStartTime);
          const dateAEndBW = moment(appointmentEndTime);

          const dateBStartBW = moment(appointmentStartTime).add(14, "days");
          const dateBEndBW = moment(appointmentEndTime).add(14, "days");

          const dateCStartBW = moment(appointmentStartTime).add(28, "days");
          const dateCEndBW = moment(appointmentEndTime).add(28, "days");

          const dateDStartBW = moment(appointmentStartTime).add(42, "days");
          const dateDEndBW = moment(appointmentEndTime).add(42, "days");

          const therapistBlockedWeeks: any = [];
          const datesForCheck = [
            { start: dateAStartBW, week: "first" },
            { start: dateBStartBW, week: "third" },
            { start: dateCStartBW, week: "fifth" },
            { start: dateDStartBW, week: "seventh" }
          ];
  
          datesForCheck.forEach((date, index) => {
            if (Util.validateBlockedDates(date.start, therapist.blockedDates)) {
              therapistBlockedWeeks.push(date.week); 
            }
          });

          const scheduledTherapistWeeks: string[] = [];
          const timeSlotsForCheckAppointmentAlreadySceduledOrNot: any = [
            { start: dateAStartBW, end: dateAEndBW, week: "first" },
            { start: dateBStartBW, end: dateBEndBW, week: "third" },
            { start: dateCStartBW, end: dateCEndBW, week: "fifth" },
            { start: dateDStartBW, end: dateDEndBW, week: "seventh" }
          ];

          // Check for appointments and push scheduled weeks into the array
          for (const date of timeSlotsForCheckAppointmentAlreadySceduledOrNot) {
            const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
              date.start,
              date.end,
              sessionDuration,
              Types.ObjectId(appointment.therapistId)
            );

            if (appointmentsInCurrentSlot.length > 0) {
              scheduledTherapistWeeks.push(date.week); // Push the week if appointments are found
            }
          }

          const scheduledClientWeeks: string[] = [];

          // Check for client appointments and skip weeks with scheduled sessions
          for (const date of timeSlotsForCheckAppointmentAlreadySceduledOrNot) {
            const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfClientByStartTime(
              new Date(date.start),
              new Date(date.end),
              sessionDuration,
              Types.ObjectId(appointment.clientId)
            );

            if (appointmentsInCurrentSlot.length > 0) {
              scheduledClientWeeks.push(date.week); // Push the week if appointments are found
            }
          }

          if (scheduledClientWeeks.length > 0) {
            console.log("Already scheduled appointments in above weeks for client", scheduledClientWeeks)
          }

          const appointmentDetailsObj: DAppointment = {
            therapistId: therapistId,
            clientId: clientId,
            start: dateAStartBW,
            end: dateAEndBW,
            title: appointment.title,
            reminders: appointment.reminders,
            typeOfMeeting: "VIDEO",
            createdBy: userId,
            color: appointment.color,
            groupId: appointment.groupId,
            status: AppointmentStatus.PENDING,
            approvedStatus: ApprovalStatus.APPROVED,
            repeatInfo: {
              repeatType: appointment.repeatInfo.repeatType,
              interval: appointment.repeatInfo.interval,
              repeatDays: {
                sunday: appointment.repeatInfo.repeatDays.sunday,
                monday: appointment.repeatInfo.repeatDays.monday,
                tuesday: appointment.repeatInfo.repeatDays.tuesday,
                wednesday: appointment.repeatInfo.repeatDays.wednesday,
                thursday: appointment.repeatInfo.repeatDays.thursday,
                friday: appointment.repeatInfo.repeatDays.friday,
                saturday: appointment.repeatInfo.repeatDays.saturday,
              },
              endingDate: appointment.repeatInfo.endingDate,
              endingAfter: appointment.repeatInfo.endingAfter,
              endingType: appointment.repeatInfo.endingType,
            },
          };
          
          const appointmentScheduledDatesForSendBiWeeklyMessage = [];
          if( !therapistBlockedWeeks.includes('first') && !scheduledTherapistWeeks.includes('first') && !scheduledClientWeeks.includes('first')) {
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendBiWeeklyMessage.push(dateAStartBW);
            const dataForCalendarBiWeekly01 = {
              start: new Date(appointmentDetailsObj.start).toISOString(),
              end: new Date(appointmentDetailsObj.end).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForFirstBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly01, therapistId);
            if (calendarResForFirstBiWeeklyAppointment){
              AppLogger.info(`Bi-weekly appointment 01 synced successfully to Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly01.start}, end time: ${dataForCalendarBiWeekly01.end}`);
            } else {
              AppLogger.error(`Bi-weekly appointment 01 failed to sync with Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly01.start}, end time: ${dataForCalendarBiWeekly01.end}`);
            }
          }
          
          if( !therapistBlockedWeeks.includes('third') && !scheduledTherapistWeeks.includes('third') && !scheduledClientWeeks.includes('third')) {
            appointmentDetailsObj.start = dateBStartBW;
            appointmentDetailsObj.end = dateBEndBW;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendBiWeeklyMessage.push(dateBStartBW);
            const dataForCalendarBiWeekly02 = {
              start: new Date(dateBStartBW).toISOString(),
              end: new Date(dateBEndBW).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForSecondBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly02, therapistId);
            if (calendarResForSecondBiWeeklyAppointment){
              AppLogger.info(`Bi-weekly appointment 02 synced successfully to Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly02.start}, end time: ${dataForCalendarBiWeekly02.end}`);
            } else {
              AppLogger.error(`Bi-weekly appointment 02 failed to sync with Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly02.start}, end time: ${dataForCalendarBiWeekly02.end}`);
            }
          }
          
          if( !therapistBlockedWeeks.includes('fifth') && !scheduledTherapistWeeks.includes('fifth') && !scheduledClientWeeks.includes('fifth')) {
            appointmentDetailsObj.start = dateCStartBW;
            appointmentDetailsObj.end = dateCEndBW;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendBiWeeklyMessage.push(dateCStartBW);
            const dataForCalendarBiWeekly03 = {
              start: new Date(dateCStartBW).toISOString(),
              end: new Date(dateCEndBW).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForThirdBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly03, therapistId);
            if (calendarResForThirdBiWeeklyAppointment){
              AppLogger.info(`Bi-weekly appointment 03 synced successfully to Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly03.start}, end time: ${dataForCalendarBiWeekly03.end}`);
            } else {
              AppLogger.error(`Bi-weekly appointment 03 failed to sync with Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly03.start}, end time: ${dataForCalendarBiWeekly03.end}`);
            }
          }
          
          if( !therapistBlockedWeeks.includes('seventh') && !scheduledTherapistWeeks.includes('seventh') && !scheduledClientWeeks.includes('seventh')) {
            appointmentDetailsObj.start = dateDStartBW;
            appointmentDetailsObj.end = dateDEndBW;

            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendBiWeeklyMessage.push(dateDStartBW);

            const dataForCalendarBiWeek04 = {
              start: new Date(dateDStartBW).toISOString(),
              end: new Date(dateDEndBW).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForFourthBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeek04, therapistId);
            if (calendarResForFourthBiWeeklyAppointment){
              AppLogger.info(`Bi-weekly appointment 04 synced successfully to Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeek04.start}, end time: ${dataForCalendarBiWeek04.end}`);
            } else {
              AppLogger.error(`Bi-weekly appointment 04 failed to sync with Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeek04.start}, end time: ${dataForCalendarBiWeek04.end}`);
            }
          }
          
          if (appointmentScheduledDatesForSendBiWeeklyMessage.length > 0){
            const messageStringForWeeklyAppointments = generateWeeklyAndBiWeeklyMessageForSent(appointmentScheduledDatesForSendBiWeeklyMessage, "bi weekly");
            AppLogger.info(`createAppointmentByClient bi weekly schedule, ${messageStringForWeeklyAppointments} ${client?.firstname} ${client?.lastname}, therapistId: ${therapistId}, clientId: ${clientId}`);
            await EmailService.sendEventEmail(
              therapist,
              "New bi weekly appointments are created!",
              messageStringForWeeklyAppointments,
              "Click here to connect with the client.",
              client.firstname + " " + client.lastname
            );
  
            if (therapist?.primaryPhone) {
              await SMSService.sendEventSMS(
                `${messageStringForWeeklyAppointments} ${client?.firstname} ${client?.lastname}.`,
                therapist?.primaryPhone,
                "Appointment-ep 43"
              );
            }
          }  
          
          const combinedArray = [...therapistBlockedWeeks, ...scheduledTherapistWeeks, ...scheduledClientWeeks];
          const uniqueCombinedArray = [...new Set(combinedArray)];
          const uniqueToMainArray = datesForCheck.filter(item => uniqueCombinedArray.includes(item.week));
          const combinedStartString = uniqueToMainArray.length > 0 ? uniqueToMainArray.map(item => moment(item.start).tz(moment.tz.guess()).format('MMMM DD YYYY')).join(", "): '';

          if (uniqueToMainArray.length > 0){
            if (uniqueToMainArray.length == 4) {
              return res.sendError(`Your appointments have not been scheduled due to a time conflict`);
            }
            return res.sendError(`Your appointments have been successfully scheduled. ${combinedStartString !== '' ? `Except for ${combinedStartString} due to a time conflict. Your therapist will reach out to help schedule for that weeks.` : ''}`);
          }

          return res.sendSuccess(
            "Bi Weekly appointments are created successfully."
          );
        } else if(appointment.repeatInfo?.repeatType == RepeatType.WEEKLY) {
          const daysOf21 = Util.calculateDays(
            21,
            appointmentStartTime,
            client._id!,
            therapist._id!
          );

          let appointmentsInNext21Days =
            await AppointmentDao.getAllAppointmentsBySelectedWeek(daysOf21);

          if (appointmentsInNext21Days.length > 0) {
            if (
              client?.premiumStatus != "active" &&
              sessionDetails.sessionTimeOfWeek == 0
            ) {
              return res.sendError(
                "You have already scheduled sessions during next 4 weeks."
              );
            }
          }

          const dateAStart = moment(appointmentStartTime);
          const dateAEnd = moment(appointmentEndTime);

          const dateBStart = moment(appointmentStartTime).add(7, "days");
          const dateBEnd = moment(appointmentEndTime).add(7, "days");

          const dateCStart = moment(appointmentStartTime).add(14, "days");
          const dateCEnd = moment(appointmentEndTime).add(14, "days");

          const dateDStart = moment(appointmentStartTime).add(21, "days");
          const dateDEnd = moment(appointmentEndTime).add(21, "days");

          const blockedWeeks: any = [];
          const datesForCheck = [
            { start: dateAStart, week: "first" },
            { start: dateBStart, week: "second" },
            { start: dateCStart, week: "third" },
            { start: dateDStart, week: "fourth" }
          ];

          datesForCheck.forEach((date, index) => {
            if (Util.validateBlockedDates(date.start, therapist.blockedDates)) {
              blockedWeeks.push(date.week); 
            }
          });

          // if (blockedWeeks.length > 0) {
          //   const weeksMessage = blockedWeeks.join(", ").replace(/,([^,]*)$/, " and$1");
          //   const message = `Cannot schedule the weekly appointment. There is a blocked time for therapist in the ${weeksMessage} week${blockedWeeks.length > 1 ? "s" : ""} at this time.`;
          //   return res.sendError(message); 
          // }

          const scheduledTherapistWeeks: string[] = [];
          const timeSlotsForCheck: any = [
            { start: dateAStart, end: dateAEnd, week: "first" },
            { start: dateBStart, end: dateBEnd, week: "second" },
            { start: dateCStart, end: dateCEnd, week: "third" },
            { start: dateDStart, end: dateDEnd, week: "fourth" }
          ];

          for (const date of timeSlotsForCheck) {
            const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
              date.start,
              date.end,
              sessionDuration,
              Types.ObjectId(appointment.therapistId)
            );

            if (appointmentsInCurrentSlot.length > 0) {
              scheduledTherapistWeeks.push(date.week); // Push the week if appointments are found
            }
          }

          if (scheduledTherapistWeeks.length > 0) {
            console.log("Already scheduled appointments in above weeks for therapist", scheduledTherapistWeeks)
          }

          const scheduledClientWeeks: string[] = [];

          for (const date of timeSlotsForCheck) {
            const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfClientByStartTime(
              new Date(date.start),
              new Date(date.end),
              sessionDuration,
              Types.ObjectId(appointment.clientId)
            );

            if (appointmentsInCurrentSlot.length > 0) {
              scheduledClientWeeks.push(date.week); 
            }
          }

          if (scheduledClientWeeks.length > 0) {
            console.log("Already scheduled appointments in above weeks for client", scheduledClientWeeks)
          }

          const appointmentDetailsObj: DAppointment = {
            therapistId: therapistId,
            clientId: clientId,
            start: dateAStart,
            end: dateAEnd,
            title: appointment.title,
            reminders: appointment.reminders,
            typeOfMeeting: "VIDEO",
            createdBy: userId,
            color: appointment.color,
            groupId: appointment.groupId,
            status: AppointmentStatus.PENDING,
            approvedStatus: ApprovalStatus.APPROVED,
            repeatInfo: {
              repeatType: appointment.repeatInfo.repeatType,
              interval: appointment.repeatInfo.interval,
              repeatDays: {
                sunday: appointment.repeatInfo.repeatDays.sunday,
                monday: appointment.repeatInfo.repeatDays.monday,
                tuesday: appointment.repeatInfo.repeatDays.tuesday,
                wednesday: appointment.repeatInfo.repeatDays.wednesday,
                thursday: appointment.repeatInfo.repeatDays.thursday,
                friday: appointment.repeatInfo.repeatDays.friday,
                saturday: appointment.repeatInfo.repeatDays.saturday,
              },
              endingDate: appointment.repeatInfo.endingDate,
              endingAfter: appointment.repeatInfo.endingAfter,
              endingType: appointment.repeatInfo.endingType,
            },
          };
          const appointmentScheduledDatesForSendWeeklyMessage = [];
          if( !blockedWeeks.includes('first') && !scheduledTherapistWeeks.includes('first') && !scheduledClientWeeks.includes('first')) {
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendWeeklyMessage.push(dateAStart);
            const dataForCalendarWeekly01 = {
              start: new Date(dateAStart).toISOString(),
              end: new Date(dateAEnd).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForFirstWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly01, therapistId);
            if (calendarResForFirstWeeklyAppointment){
              AppLogger.info(`Weekly appointment 01 synced successfully to Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly01.start}, end time: ${dataForCalendarWeekly01.end}`);
            } else {
              AppLogger.error(`Weekly appointment 01 failed to sync with Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly01.start}, end time: ${dataForCalendarWeekly01.end}`);
            }
          }

          if( !blockedWeeks.includes('second') && !scheduledTherapistWeeks.includes('second') && !scheduledClientWeeks.includes('second')) {
            appointmentDetailsObj.start = dateBStart;
            appointmentDetailsObj.end = dateBEnd;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendWeeklyMessage.push(dateBStart);
            const dataForCalendarWeekly02 = {
              start: new Date(dateBStart).toISOString(),
              end: new Date(dateBEnd).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForSecondWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly02, therapistId);
            if (calendarResForSecondWeeklyAppointment){
              AppLogger.info(`Weekly appointment 02 synced successfully to Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly02.start}, end time: ${dataForCalendarWeekly02.end}`);
            } else {
              AppLogger.error(`Weekly appointment 02 failed to sync with Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly02.start}, end time: ${dataForCalendarWeekly02.end}`);
            }
          }
          
          if( !blockedWeeks.includes('third') && !scheduledTherapistWeeks.includes('third') && !scheduledClientWeeks.includes('third')) {
            appointmentDetailsObj.start = dateCStart;
            appointmentDetailsObj.end = dateCEnd;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendWeeklyMessage.push(dateCStart);
            const dataForCalendarWeekly03 = {
              start: new Date(dateCStart).toISOString(),
              end: new Date(dateCEnd).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForThirdWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly03, therapistId);
            if (calendarResForThirdWeeklyAppointment){
              AppLogger.info(`Weekly appointment 03 synced successfully to Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly03.start}, end time: ${dataForCalendarWeekly03.end}`);
            } else {
              AppLogger.error(`Weekly appointment 03 failed to sync with Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly03.start}, end time: ${dataForCalendarWeekly03.end}`);
            }
          }
          
          if( !blockedWeeks.includes('fourth') && !scheduledTherapistWeeks.includes('fourth') && !scheduledClientWeeks.includes('fourth')) {
            appointmentDetailsObj.start = dateDStart;
            appointmentDetailsObj.end = dateDEnd;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendWeeklyMessage.push(dateDStart);
            const dataForCalendarWeekly04 = {
              start: new Date(dateDStart).toISOString(),
              end: new Date(dateDEnd).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForFourthWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly04, therapistId);
            if (calendarResForFourthWeeklyAppointment){
              AppLogger.info(`Weekly appointment 04 synced successfully to Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly04.start}, end time: ${dataForCalendarWeekly04.end}`);
            } else {
              AppLogger.error(`Weekly appointment 04 failed to sync with Google Calendar by client, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly04.start}, end time: ${dataForCalendarWeekly04.end}`);
            }
          }

          if (appointmentScheduledDatesForSendWeeklyMessage.length > 0){
            const messageStringForWeeklyAppointments = generateWeeklyAndBiWeeklyMessageForSent(appointmentScheduledDatesForSendWeeklyMessage, "weekly");
            AppLogger.info(`createAppointmentByClient weekly schedule, ${messageStringForWeeklyAppointments} ${client.firstname} ${client.lastname}, therapistId: ${therapistId}, clientId: ${clientId}`);
            await EmailService.sendEventEmail(
              therapist,
              "New weekly appointments are created!",
              messageStringForWeeklyAppointments,
              "Click here to connect with the client.",
              client.firstname + " " + client.lastname
            );
  
            if (therapist?.primaryPhone) {
              await SMSService.sendEventSMS(
                `${messageStringForWeeklyAppointments} ${client?.firstname} ${client?.lastname}.`,
                therapist?.primaryPhone,
                "Appointment-ep 42"
              );
            }
          }  

          const combinedArray = [...blockedWeeks, ...scheduledTherapistWeeks, ...scheduledClientWeeks];
          const uniqueCombinedArray = [...new Set(combinedArray)];
          const uniqueToMainArray = datesForCheck.filter(item => uniqueCombinedArray.includes(item.week));
          const combinedStartString = uniqueToMainArray.length > 0 ? uniqueToMainArray.map(item => moment(item.start).tz(moment.tz.guess()).format('MMMM DD YYYY')).join(", "): '';

          if (uniqueToMainArray.length > 0){
            if (uniqueToMainArray.length == 4) {
              return res.sendError(`Your weekly appointments have not been scheduled due to a time conflict`);
            } else {
              return res.sendError(`Your weekly appointments have been successfully scheduled. ${combinedStartString !== '' ? `Except for ${combinedStartString} due to a time conflict. Your therapist will reach out to help schedule for that weeks.` : ''}`);
            }
          }
          return res.sendSuccess(
            "Weekly appointments are created successfully."
          );
        } else {
          return res.sendError('Incorrect repeat type when scheduling an appointment by the client.');
        }
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function createAppointmentByClientWithMatch(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.user._id;
    const appointment = req.body.appointment;
    const therapistId = appointment.therapistId;
    const clientId = appointment.clientId;

    const sessionStart = moment(appointment.start);
    const sessionEnd = moment(appointment.end);

    const sessionDuration = moment
      .duration(sessionEnd.diff(sessionStart))
      .asMinutes();

    if (!mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid therapist Id");
    }

    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid client Id");
    }

    try {
      if (role == UserRole.CLIENT) {
        let therapist = await TherapistDao.getUserById(therapistId);

        let client = await ClientDao.getUserById(clientId);

        if (!therapist || therapist == null) {
          return res.sendError(
            "No existing therapist for the provided therapist Id."
          );
        }

        if (!client || client == null) {
          return res.sendError(
            "No existing client for the provided client Id."
          );
        }

        if (
          client.premiumStatus != "active" &&
          (client.subscriptionId == null ||
            client.subscriptionStatus != "active") &&
          client.testSubscriptionStatus != "active"
        ) {
          return res.sendError("Sorry! You don't have an active subscription.");
        }

        if (Util.invalidTimeCheck(appointment.start)) {
          return res.sendError("Sorry! You have selected invalid time.");
        }

        // if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
        //   return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
        // }

        const beginsAt = moment(appointment.start);
        const endsAt = moment(appointment.start);

        const startTime = moment(appointment.start).format("H:mm A");
        const endTime = moment(appointment.end).format("H:mm A");
        const appointmentStartTime = moment(appointment.start).format("YYYY-MM-DD HH:mm");
        const appointmentEndTime = moment(appointment.end).format("YYYY-MM-DD HH:mm");

        const mST = moment(startTime, "HH:mm").minute();

        if (mST != 0 && mST != 30) {
          return res.sendError("Please select valid start time.");
        }

        const datesOfWeek = Util.calculateWeekNumberAndDates(
          appointmentStartTime,
          client._id!,
          therapist._id!
        );

        const appointmentId = datesOfWeek.appointmentId;
        let sessionTimeOfWeek = 0;

        let appointmentsBySelectedWeek =
          await AppointmentDao.getAllAppointmentsBySelectedWeek(datesOfWeek);

        if (appointmentId) {
          appointmentsBySelectedWeek = appointmentsBySelectedWeek.filter(
            (appointment: IAppointment) =>
              appointmentId.toString() !== appointment._id.toString()
          );
        }

        if (appointmentsBySelectedWeek.length) {
          for (const session of appointmentsBySelectedWeek) {
            const startTime = moment(session.start, "HH:mm:ss a");
            const endTime = moment(session.end, "HH:mm:ss a");
            const duration = moment.duration(endTime.diff(startTime));
            const minutes = parseInt(duration.asMinutes().toString());

            sessionTimeOfWeek = sessionTimeOfWeek + minutes;
          }
        }

        const sessionDetails = {
          selectedDate: datesOfWeek.selectedDate,
          allSessionOfWeek: appointmentsBySelectedWeek,
          sessionTimeOfWeek: sessionTimeOfWeek,
          allSession: appointmentsBySelectedWeek.length,
        };

        if (
          appointmentsBySelectedWeek === undefined ||
          appointmentsBySelectedWeek == null
        ) {
          return res.sendError(
            "Server error occured. Please reach to support."
          );
        }

        if (Util.skipCheckingForPremiumUsers(client)) {
          const meetingDuration = Util.getMeetingTimeDuration(
            startTime,
            endTime
          );

          if (
            sessionDetails.sessionTimeOfWeek != 0 &&
            sessionDetails.sessionTimeOfWeek != 30 &&
            sessionDetails.sessionTimeOfWeek != 60
          ) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          }

          if (sessionDetails.sessionTimeOfWeek != 0) {
            if (
              sessionDetails.sessionTimeOfWeek == 30 &&
              meetingDuration == 60
            ) {
              return res.sendError(
                "Sorry! Client's weekly session time has exceeded."
              );
            } else if (sessionDetails.sessionTimeOfWeek == 60) {
              return res.sendError(
                "Sorry! Client's weekly session time has exceeded."
              );
            }
          }
        }

        if (!startTime) {
          return res.sendError("Please select valid start time.");
        }

        const workingDaysOfTherapist: string[] = [];

        therapist.workingHours?.map((session: any) => {
          if (!workingDaysOfTherapist.includes(session.day))
            workingDaysOfTherapist.push(session.day);
        });

        if (
          !moment(new Date()).isBefore(
            moment(
              new Date(appointmentStartTime).setHours(
                parseInt(startTime.split(":")[0]),
                parseInt(startTime.split(":")[1]),
                0,
                0
              )
            )
          )
        ) {
          return res.sendError(
            "Sorry! You can't create appointment in a past date!"
          );
        }

        if (!appointment.title) {
          return res.sendError("Please add title.");
        }

        const timeDifferenceInHours = moment
          .duration(endsAt.diff(beginsAt))
          .asHours();

        if (timeDifferenceInHours > 1) {
          return res.sendError("You can create one hour sessions only.");
        }

        if (appointment.repeatInfo?.repeatType == RepeatType.DOES_NOT_REPEAT) {

          if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
            return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
          }

          let appointementsInTheCurrentSlots =
            await AppointmentDao.getAppointmentsOfTherapistByStartTime(
              new Date(appointment.start),
              new Date(appointment.end),
              sessionDuration,
              Types.ObjectId(appointment.therapistId)
            );

          if (appointementsInTheCurrentSlots.length > 0) {
            return res.sendError(
              role == "CLIENT"
                ? "Therapist has already scheduled an appointment during the selected time slot."
                : "You have already scheduled an appointment during the selected time slot."
            );
          }

          let appointementsInTheCurrentSlotsOfClient =
            await AppointmentDao.getAppointmentsOfClientByStartTime(
              new Date(appointment.start),
              new Date(appointment.end),
              sessionDuration,
              Types.ObjectId(appointment.clientId)
            );

          if (appointementsInTheCurrentSlotsOfClient.length > 0) {
            return res.sendError(
              role == "CLIENT"
                ? "You have already scheduled an appointment during the selected time slot."
                : "Client has already scheduled an appointment during the selected time slot."
            );
          }

          const appointmentDetails: DAppointment = {
            therapistId: therapistId,
            clientId: clientId,
            start: new Date(appointment.start),
            end: new Date(appointment.end),
            title: appointment.title,
            reminders: appointment.reminders,
            typeOfMeeting: "VIDEO",
            createdBy: userId,
            color: appointment.color,
            groupId: appointment.groupId,
            status: AppointmentStatus.PENDING,
            approvedStatus: ApprovalStatus.APPROVED,
            repeatInfo: {
              repeatType: appointment.repeatInfo.repeatType,
              interval: appointment.repeatInfo.interval,
              repeatDays: {
                sunday: appointment.repeatInfo.repeatDays.sunday,
                monday: appointment.repeatInfo.repeatDays.monday,
                tuesday: appointment.repeatInfo.repeatDays.tuesday,
                wednesday: appointment.repeatInfo.repeatDays.wednesday,
                thursday: appointment.repeatInfo.repeatDays.thursday,
                friday: appointment.repeatInfo.repeatDays.friday,
                saturday: appointment.repeatInfo.repeatDays.saturday,
              },
              endingDate: appointment.repeatInfo.endingDate,
              endingAfter: appointment.repeatInfo.endingAfter,
              endingType: appointment.repeatInfo.endingType,
            },
          };

          const newAppointment = await AppointmentDao.createAppointment(
            appointmentDetails
          );

          const utcTime = moment.utc(appointment.start);

          const estTime = utcTime.tz('America/New_York');

          await EmailService.sendEventEmail(
            therapist,
            "New appointment is created!",
            `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by`,
            "Click here to connect with the client.",
            client.firstname + " " + client.lastname
          );

          if (therapist?.primaryPhone) {
            await SMSService.sendEventSMS(
              `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by ${client.firstname} ${client.lastname}`,
              therapist?.primaryPhone,
              "Appointment-ep 02"
            );
          }

          const dataForCalendar = {
            start: appointment.start,
            end: appointment.end,
            title: appointment.title,
          }
          console.log(dataForCalendar);
          
          const calendarRes = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendar, therapistId);
          if (calendarRes){
            AppLogger.info(`Google calendar sync successfully by client, therapistId: ${therapistId}, start time: ${appointment.start}, end time: ${appointment.end}`);
          } else {
            AppLogger.error(`Google calendar sync failed by client, therapistId: ${therapistId}, start time: ${appointment.start}, end time: ${appointment.end}`);
          }

          return res.sendSuccess(
            newAppointment,
            "Appointment Created Successfully."
          );
        } else if (appointment.repeatInfo?.repeatType == RepeatType.BI_WEEKLY) {
          const daysOf42 = Util.calculateDays(
            42,
            appointmentStartTime,
            client._id!,
            therapist._id!
          );

          let appointmentsInNext42Days = await AppointmentDao.getAllAppointmentsBySelectedWeek(daysOf42);

          if (appointmentsInNext42Days.length > 0) {
            if (
              client?.premiumStatus != "active" &&
              sessionDetails.sessionTimeOfWeek == 0
            ) {
              return res.sendError(
                "You have already scheduled sessions during next 4 weeks."
              );
            }
          }

          const dateAStartBW = moment(appointmentStartTime);
          const dateAEndBW = moment(appointmentEndTime);

          const dateBStartBW = moment(appointmentStartTime).add(14, "days");
          const dateBEndBW = moment(appointmentEndTime).add(14, "days");

          const dateCStartBW = moment(appointmentStartTime).add(28, "days");
          const dateCEndBW = moment(appointmentEndTime).add(28, "days");

          const dateDStartBW = moment(appointmentStartTime).add(42, "days");
          const dateDEndBW = moment(appointmentEndTime).add(42, "days");

          const therapistBlockedWeeks: any = [];
          const datesForCheck = [
            { start: dateAStartBW, week: "first" },
            { start: dateBStartBW, week: "third" },
            { start: dateCStartBW, week: "fifth" },
            { start: dateDStartBW, week: "seventh" }
          ];
  
          datesForCheck.forEach((date, index) => {
            if (Util.validateBlockedDates(date.start, therapist.blockedDates)) {
              therapistBlockedWeeks.push(date.week); 
            }
          });

          const scheduledTherapistWeeks: string[] = [];
          const timeSlotsForCheckAppointmentAlreadySceduledOrNot: any = [
            { start: dateAStartBW, end: dateAEndBW, week: "first" },
            { start: dateBStartBW, end: dateBEndBW, week: "third" },
            { start: dateCStartBW, end: dateCEndBW, week: "fifth" },
            { start: dateDStartBW, end: dateDEndBW, week: "seventh" }
          ];

          // Check for appointments and push scheduled weeks into the array
          for (const date of timeSlotsForCheckAppointmentAlreadySceduledOrNot) {
            const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
              date.start,
              date.end,
              sessionDuration,
              Types.ObjectId(appointment.therapistId)
            );

            if (appointmentsInCurrentSlot.length > 0) {
              scheduledTherapistWeeks.push(date.week); // Push the week if appointments are found
            }
          }

          const scheduledClientWeeks: string[] = [];

          // Check for client appointments and skip weeks with scheduled sessions
          for (const date of timeSlotsForCheckAppointmentAlreadySceduledOrNot) {
            const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfClientByStartTime(
              new Date(date.start),
              new Date(date.end),
              sessionDuration,
              Types.ObjectId(appointment.clientId)
            );

            if (appointmentsInCurrentSlot.length > 0) {
              scheduledClientWeeks.push(date.week); // Push the week if appointments are found
            }
          }

          const appointmentDetailsObj: DAppointment = {
            therapistId: therapistId,
            clientId: clientId,
            start: dateAStartBW,
            end: dateAEndBW,
            title: appointment.title,
            reminders: appointment.reminders,
            typeOfMeeting: "VIDEO",
            createdBy: userId,
            color: appointment.color,
            groupId: appointment.groupId,
            status: AppointmentStatus.PENDING,
            approvedStatus: ApprovalStatus.APPROVED,
            repeatInfo: {
              repeatType: appointment.repeatInfo.repeatType,
              interval: appointment.repeatInfo.interval,
              repeatDays: {
                sunday: appointment.repeatInfo.repeatDays.sunday,
                monday: appointment.repeatInfo.repeatDays.monday,
                tuesday: appointment.repeatInfo.repeatDays.tuesday,
                wednesday: appointment.repeatInfo.repeatDays.wednesday,
                thursday: appointment.repeatInfo.repeatDays.thursday,
                friday: appointment.repeatInfo.repeatDays.friday,
                saturday: appointment.repeatInfo.repeatDays.saturday,
              },
              endingDate: appointment.repeatInfo.endingDate,
              endingAfter: appointment.repeatInfo.endingAfter,
              endingType: appointment.repeatInfo.endingType,
            },
          };
          const appointmentScheduledDatesForSendBiWeeklyMessage = [];
          if( !therapistBlockedWeeks.includes('first') && !scheduledTherapistWeeks.includes('first') && !scheduledClientWeeks.includes('first')) {
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendBiWeeklyMessage.push(dateAStartBW);
            const dataForCalendarBiWeekly01 = {
              start: new Date(appointmentDetailsObj.start).toISOString(),
              end: new Date(appointmentDetailsObj.end).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForFirstBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly01, therapistId);
            if (calendarResForFirstBiWeeklyAppointment){
              AppLogger.info(`Bi-weekly appointment 01 synced successfully to Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly01.start}, end time: ${dataForCalendarBiWeekly01.end}`);
            } else {
              AppLogger.error(`Bi-weekly appointment 01 failed to sync with Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly01.start}, end time: ${dataForCalendarBiWeekly01.end}`);
            }
          }
          
          if( !therapistBlockedWeeks.includes('third') && !scheduledTherapistWeeks.includes('third') && !scheduledClientWeeks.includes('third')) {
            appointmentDetailsObj.start = dateBStartBW;
            appointmentDetailsObj.end = dateBEndBW;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendBiWeeklyMessage.push(dateBStartBW);
            const dataForCalendarBiWeekly02 = {
              start: new Date(dateBStartBW).toISOString(),
              end: new Date(dateBEndBW).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForSecondBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly02, therapistId);
            if (calendarResForSecondBiWeeklyAppointment){
              AppLogger.info(`Bi-weekly appointment 02 synced successfully to Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly02.start}, end time: ${dataForCalendarBiWeekly02.end}`);
            } else {
              AppLogger.error(`Bi-weekly appointment 02 failed to sync with Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly02.start}, end time: ${dataForCalendarBiWeekly02.end}`);
            }
          }
          
          if( !therapistBlockedWeeks.includes('fifth') && !scheduledTherapistWeeks.includes('fifth') && !scheduledClientWeeks.includes('fifth')) {
            appointmentDetailsObj.start = dateCStartBW;
            appointmentDetailsObj.end = dateCEndBW;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendBiWeeklyMessage.push(dateCStartBW);
            const dataForCalendarBiWeekly03 = {
              start: new Date(dateCStartBW).toISOString(),
              end: new Date(dateCEndBW).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForThirdBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly03, therapistId);
            if (calendarResForThirdBiWeeklyAppointment){
              AppLogger.info(`Bi-weekly appointment 03 synced successfully to Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly03.start}, end time: ${dataForCalendarBiWeekly03.end}`);
            } else {
              AppLogger.error(`Bi-weekly appointment 03 failed to sync with Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly03.start}, end time: ${dataForCalendarBiWeekly03.end}`);
            }
          }
          
          if( !therapistBlockedWeeks.includes('seventh') && !scheduledTherapistWeeks.includes('seventh') && !scheduledClientWeeks.includes('seventh')) {
            appointmentDetailsObj.start = dateDStartBW;
            appointmentDetailsObj.end = dateDEndBW;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendBiWeeklyMessage.push(dateDStartBW);
            const dataForCalendarBiWeek04 = {
              start: new Date(dateDStartBW).toISOString(),
              end: new Date(dateDEndBW).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForFourthBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeek04, therapistId);
            if (calendarResForFourthBiWeeklyAppointment){
              AppLogger.info(`Bi-weekly appointment 04 synced successfully to Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeek04.start}, end time: ${dataForCalendarBiWeek04.end}`);
            } else {
              AppLogger.error(`Bi-weekly appointment 04 failed to sync with Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeek04.start}, end time: ${dataForCalendarBiWeek04.end}`);
            }
          }

          if (appointmentScheduledDatesForSendBiWeeklyMessage.length > 0){
            const messageStringForWeeklyAppointments = generateWeeklyAndBiWeeklyMessageForSent(appointmentScheduledDatesForSendBiWeeklyMessage, "bi weekly");
            AppLogger.info(`createAppointmentByClientWithMatch bi weekly schedule, ${messageStringForWeeklyAppointments} ${client?.firstname} ${client?.lastname}, therapistId: ${therapistId}, clientId: ${clientId}`);
            await EmailService.sendEventEmail(
              therapist,
              "New bi weekly appointments are created!",
              messageStringForWeeklyAppointments,
              "Click here to connect with the client.",
              client.firstname + " " + client.lastname
            );
  
            if (therapist?.primaryPhone) {
              await SMSService.sendEventSMS(
                `${messageStringForWeeklyAppointments} ${client?.firstname} ${client?.lastname}.`,
                therapist?.primaryPhone,
                "Appointment-ep 44"
              );
            }
          }  

          const combinedArray = [...therapistBlockedWeeks, ...scheduledTherapistWeeks, ...scheduledClientWeeks];
          const uniqueCombinedArray = [...new Set(combinedArray)];
          const uniqueToMainArray = datesForCheck.filter(item => uniqueCombinedArray.includes(item.week));
          const combinedStartString = uniqueToMainArray.length > 0 ? uniqueToMainArray.map(item => moment(item.start).tz(moment.tz.guess()).format('MMMM DD YYYY')).join(", "): '';

          if (uniqueToMainArray.length > 0){
            if (uniqueToMainArray.length == 4) {
              return res.sendError(`Your appointments have not been scheduled due to a time conflict`);
            }
            return res.sendError(`Your appointments have been successfully scheduled. ${combinedStartString !== '' ? `Except for ${combinedStartString} due to a time conflict. Your therapist will reach out to help schedule for that weeks.` : ''}`);
          }

          return res.sendSuccess(
            "Bi Weekly appointments are created successfully."
          );
        } else if(appointment.repeatInfo?.repeatType == RepeatType.WEEKLY) {
          const daysOf21 = Util.calculateDays(
            21,
            appointmentStartTime,
            client._id!,
            therapist._id!
          );

          let appointmentsInNext21Days =
            await AppointmentDao.getAllAppointmentsBySelectedWeek(daysOf21);

          if (appointmentsInNext21Days.length > 0) {
            if (
              client?.premiumStatus != "active" &&
              sessionDetails.sessionTimeOfWeek == 0
            ) {
              return res.sendError(
                "You have already scheduled sessions during next 4 weeks."
              );
            }
          }

          const dateAStart = moment(appointmentStartTime);
          const dateAEnd = moment(appointmentEndTime);

          const dateBStart = moment(appointmentStartTime).add(7, "days");
          const dateBEnd = moment(appointmentEndTime).add(7, "days");

          const dateCStart = moment(appointmentStartTime).add(14, "days");
          const dateCEnd = moment(appointmentEndTime).add(14, "days");

          const dateDStart = moment(appointmentStartTime).add(21, "days");
          const dateDEnd = moment(appointmentEndTime).add(21, "days");

          const blockedWeeks: any = [];
          const datesForCheck = [
            { start: dateAStart, week: "first" },
            { start: dateBStart, week: "second" },
            { start: dateCStart, week: "third" },
            { start: dateDStart, week: "fourth" }
          ];

          datesForCheck.forEach((date, index) => {
            if (Util.validateBlockedDates(date.start, therapist.blockedDates)) {
              blockedWeeks.push(date.week); 
            }
          });

          const scheduledTherapistWeeks: string[] = [];
          const timeSlotsForCheck: any = [
            { start: dateAStart, end: dateAEnd, week: "first" },
            { start: dateBStart, end: dateBEnd, week: "second" },
            { start: dateCStart, end: dateCEnd, week: "third" },
            { start: dateDStart, end: dateDEnd, week: "fourth" }
          ];

          for (const date of timeSlotsForCheck) {
            const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
              date.start,
              date.end,
              sessionDuration,
              Types.ObjectId(appointment.therapistId)
            );

            if (appointmentsInCurrentSlot.length > 0) {
              scheduledTherapistWeeks.push(date.week); 
            }
          }

          const scheduledClientWeeks: string[] = [];

          for (const date of timeSlotsForCheck) {
            const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfClientByStartTime(
              new Date(date.start),
              new Date(date.end),
              sessionDuration,
              Types.ObjectId(appointment.clientId)
            );

            if (appointmentsInCurrentSlot.length > 0) {
              scheduledClientWeeks.push(date.week); 
            }
          }

          const appointmentDetailsObj: DAppointment = {
            therapistId: therapistId,
            clientId: clientId,
            start: dateAStart,
            end: dateAEnd,
            title: appointment.title,
            reminders: appointment.reminders,
            typeOfMeeting: "VIDEO",
            createdBy: userId,
            color: appointment.color,
            groupId: appointment.groupId,
            status: AppointmentStatus.PENDING,
            approvedStatus: ApprovalStatus.APPROVED,
            repeatInfo: {
              repeatType: appointment.repeatInfo.repeatType,
              interval: appointment.repeatInfo.interval,
              repeatDays: {
                sunday: appointment.repeatInfo.repeatDays.sunday,
                monday: appointment.repeatInfo.repeatDays.monday,
                tuesday: appointment.repeatInfo.repeatDays.tuesday,
                wednesday: appointment.repeatInfo.repeatDays.wednesday,
                thursday: appointment.repeatInfo.repeatDays.thursday,
                friday: appointment.repeatInfo.repeatDays.friday,
                saturday: appointment.repeatInfo.repeatDays.saturday,
              },
              endingDate: appointment.repeatInfo.endingDate,
              endingAfter: appointment.repeatInfo.endingAfter,
              endingType: appointment.repeatInfo.endingType,
            },
          };
          const appointmentScheduledDatesForSendWeeklyMessage = [];
          if( !blockedWeeks.includes('first') && !scheduledTherapistWeeks.includes('first') && !scheduledClientWeeks.includes('first')) {
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendWeeklyMessage.push(dateAStart);
            const dataForCalendarWeekly01 = {
              start: new Date(dateAStart).toISOString(),
              end: new Date(dateAEnd).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForFirstWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly01, therapistId);
            if (calendarResForFirstWeeklyAppointment){
              AppLogger.info(`Weekly appointment 01 synced successfully to Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly01.start}, end time: ${dataForCalendarWeekly01.end}`);
            } else {
              AppLogger.error(`Weekly appointment 01 failed to sync with Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly01.start}, end time: ${dataForCalendarWeekly01.end}`);
            }
          }
          
          if( !blockedWeeks.includes('second') && !scheduledTherapistWeeks.includes('second') && !scheduledClientWeeks.includes('second')) {
            appointmentDetailsObj.start = dateBStart;
            appointmentDetailsObj.end = dateBEnd;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendWeeklyMessage.push(dateBStart);
            const dataForCalendarWeekly02 = {
              start: new Date(dateBStart).toISOString(),
              end: new Date(dateBEnd).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForSecondWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly02, therapistId);
            if (calendarResForSecondWeeklyAppointment){
              AppLogger.info(`Weekly appointment 02 synced successfully to Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly02.start}, end time: ${dataForCalendarWeekly02.end}`);
            } else {
              AppLogger.error(`Weekly appointment 02 failed to sync with Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly02.start}, end time: ${dataForCalendarWeekly02.end}`);
            }
          }
          
          if( !blockedWeeks.includes('third') && !scheduledTherapistWeeks.includes('third') && !scheduledClientWeeks.includes('third')) {
            appointmentDetailsObj.start = dateCStart;
            appointmentDetailsObj.end = dateCEnd;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendWeeklyMessage.push(dateCStart);
            const dataForCalendarWeekly03 = {
              start: new Date(dateCStart).toISOString(),
              end: new Date(dateCEnd).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForThirdWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly03, therapistId);
            if (calendarResForThirdWeeklyAppointment){
              AppLogger.info(`Weekly appointment 03 synced successfully to Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly03.start}, end time: ${dataForCalendarWeekly03.end}`);
            } else {
              AppLogger.error(`Weekly appointment 03 failed to sync with Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly03.start}, end time: ${dataForCalendarWeekly03.end}`);
            }
          }
          
          if( !blockedWeeks.includes('fourth') && !scheduledTherapistWeeks.includes('fourth') && !scheduledClientWeeks.includes('fourth')) {
            appointmentDetailsObj.start = dateDStart;
            appointmentDetailsObj.end = dateDEnd;
            await AppointmentDao.createAppointment(appointmentDetailsObj);
            appointmentScheduledDatesForSendWeeklyMessage.push(dateDStart);
            const dataForCalendarWeekly04 = {
              start: new Date(dateDStart).toISOString(),
              end: new Date(dateDEnd).toISOString(),
              title: appointmentDetailsObj.title,
            }
            const calendarResForFourthWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly04, therapistId);
            if (calendarResForFourthWeeklyAppointment){
              AppLogger.info(`Weekly appointment 04 synced successfully to Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly04.start}, end time: ${dataForCalendarWeekly04.end}`);
            } else {
              AppLogger.error(`Weekly appointment 04 failed to sync with Google Calendar by client with match, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly04.start}, end time: ${dataForCalendarWeekly04.end}`);
            }
          }

          if (appointmentScheduledDatesForSendWeeklyMessage.length > 0){
            const messageStringForWeeklyAppointments = generateWeeklyAndBiWeeklyMessageForSent(appointmentScheduledDatesForSendWeeklyMessage, "weekly");
            AppLogger.info(`createAppointmentByClientWithMatch weekly schedule, ${messageStringForWeeklyAppointments} ${client?.firstname} ${client?.lastname}, therapistId: ${therapistId}, clientId: ${clientId}`);
            await EmailService.sendEventEmail(
              therapist,
              "New weekly appointments are created!",
              messageStringForWeeklyAppointments,
              "Click here to connect with the client.",
              client.firstname + " " + client.lastname
            );
  
            if (therapist?.primaryPhone) {
              await SMSService.sendEventSMS(
                `${messageStringForWeeklyAppointments} ${client?.firstname} ${client?.lastname}.`,
                therapist?.primaryPhone,
                "Appointment-ep 45"
              );
            }
          }  

          const combinedArray = [...blockedWeeks, ...scheduledTherapistWeeks, ...scheduledClientWeeks];
          const uniqueCombinedArray = [...new Set(combinedArray)];
          const uniqueToMainArray = datesForCheck.filter(item => uniqueCombinedArray.includes(item.week));
          const combinedStartString = uniqueToMainArray.length > 0 ? uniqueToMainArray.map(item => moment(item.start).tz(moment.tz.guess()).format('MMMM DD YYYY')).join(", "): '';

          if (uniqueToMainArray.length > 0){
            if (uniqueToMainArray.length == 4) {
              return res.sendSuccess(`Your weekly appointments have not been scheduled due to a time conflict`);
            } else {
              return res.sendSuccess(`Your weekly appointments have been successfully scheduled. ${combinedStartString !== '' ? `Except for ${combinedStartString} due to a time conflict. Your therapist will reach out to help schedule for that weeks.` : ''}`);
            }
          }
          return res.sendSuccess(
            "Weekly appointments are created successfully."
          );
        } else {
          return res.sendError('Incorrect repeat type when scheduling an appointment by the clinet with match.');
        }
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function createAppointmentByClientPublic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const appointment = req.body.appointment;
    const therapistId = appointment.therapistId;
    const clientId = appointment.clientId;

    const sessionStart = moment(appointment.start);
    const sessionEnd = moment(appointment.end);

    const sessionDuration = moment
      .duration(sessionEnd.diff(sessionStart))
      .asMinutes();

    if (!mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid therapist Id");
    }

    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid client Id");
    }

    try {
      let therapist = await TherapistDao.getUserById(therapistId);

      let client = await ClientDao.getUserById(clientId);

      if (!therapist || therapist == null) {
        return res.sendError(
          "No existing therapist for the provided therapist Id."
        );
      }

      if (!client || client == null) {
        return res.sendError(
          "No existing client for the provided client Id."
        );
      }

      let isFriend = await FriendRequestDao.checkIfUserIsFriend(
        client._id,
        therapist._id
      );

      if (!isFriend) {
        return res.sendError(
          "Sorry! You haven't connected with this Therapist yet."
        );
      }

      if (Util.invalidTimeCheck(appointment.start)) {
        return res.sendError("Sorry! You have selected invalid time.");
      }

      if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
        return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
      }

      const beginsAt = moment(appointment.start);
      const endsAt = moment(appointment.start);

      const startTime = moment(appointment.start).format("H:mm A");
      const endTime = moment(appointment.end).format("H:mm A");
      const appointmentStartTime = moment(appointment.start).format("YYYY-MM-DD HH:mm");
      const appointmentEndTime = moment(appointment.end).format("YYYY-MM-DD HH:mm");

      const mST = moment(startTime, "HH:mm").minute();

      if (mST != 0 && mST != 30) {
        return res.sendError("Please select valid start time.");
      }

      const datesOfWeek = Util.calculateWeekNumberAndDates(
        appointmentStartTime,
        client._id!,
        therapist._id!
      );

      const appointmentId = datesOfWeek.appointmentId;
      let sessionTimeOfWeek = 0;

      let appointmentsBySelectedWeek =
        await AppointmentDao.getAllAppointmentsBySelectedWeek(datesOfWeek);

      if (appointmentId) {
        appointmentsBySelectedWeek = appointmentsBySelectedWeek.filter(
          (appointment: IAppointment) =>
            appointmentId.toString() !== appointment._id.toString()
        );
      }

      if (appointmentsBySelectedWeek.length) {
        for (const session of appointmentsBySelectedWeek) {
          const startTime = moment(session.start, "HH:mm:ss a");
          const endTime = moment(session.end, "HH:mm:ss a");
          const duration = moment.duration(endTime.diff(startTime));
          const minutes = parseInt(duration.asMinutes().toString());

          sessionTimeOfWeek = sessionTimeOfWeek + minutes;
        }
      }

      const sessionDetails = {
        selectedDate: datesOfWeek.selectedDate,
        allSessionOfWeek: appointmentsBySelectedWeek,
        sessionTimeOfWeek: sessionTimeOfWeek,
        allSession: appointmentsBySelectedWeek.length,
      };

      if (
        appointmentsBySelectedWeek === undefined ||
        appointmentsBySelectedWeek == null
      ) {
        return res.sendError(
          "Server error occured. Please reach to support."
        );
      }

      if (Util.skipCheckingForPremiumUsers(client)) {
        const meetingDuration = Util.getMeetingTimeDuration(
          startTime,
          endTime
        );

        if (
          sessionDetails.sessionTimeOfWeek != 0 &&
          sessionDetails.sessionTimeOfWeek != 30 &&
          sessionDetails.sessionTimeOfWeek != 60
        ) {
          // Bypassing weekly session time validation
          // return res.sendError(
          //   "Sorry! Client's weekly session time has exceeded."
          // );
        }

        if (sessionDetails.sessionTimeOfWeek != 0) {
          if (
            sessionDetails.sessionTimeOfWeek == 30 &&
            meetingDuration == 60
          ) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          } else if (sessionDetails.sessionTimeOfWeek == 60) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          }
        }
      }

      if (!startTime) {
        return res.sendError("Please select valid start time.");
      }

      const workingDaysOfTherapist: string[] = [];

      therapist.workingHours?.map((session: any) => {
        if (!workingDaysOfTherapist.includes(session.day))
          workingDaysOfTherapist.push(session.day);
      });

      if (
        !moment(new Date()).isBefore(
          moment(
            new Date(appointmentStartTime).setHours(
              parseInt(startTime.split(":")[0]),
              parseInt(startTime.split(":")[1]),
              0,
              0
            )
          )
        )
      ) {
        return res.sendError(
          "Sorry! You can't create appointment in a past date!"
        );
      }

      if (!appointment.title) {
        return res.sendError("Please add title.");
      }

      const timeDifferenceInHours = moment
        .duration(endsAt.diff(beginsAt))
        .asHours();

      if (timeDifferenceInHours > 1) {
        return res.sendError("You can create one hour sessions only.");
      }

      if (appointment.repeatInfo?.repeatType == RepeatType.DOES_NOT_REPEAT) {
        const appointmentDetails: DAppointment = {
          therapistId: therapistId,
          clientId: clientId,
          start: new Date(appointment.start),
          end: new Date(appointment.end),
          title: appointment.title,
          reminders: appointment.reminders,
          typeOfMeeting: "VIDEO",
          createdBy: clientId,
          color: appointment.color,
          groupId: appointment.groupId,
          status: AppointmentStatus.WAITING_FOR_APPROVAL,
          approvedStatus: ApprovalStatus.PENDING,
          repeatInfo: {
            repeatType: appointment.repeatInfo.repeatType,
            interval: appointment.repeatInfo.interval,
            repeatDays: {
              sunday: appointment.repeatInfo.repeatDays.sunday,
              monday: appointment.repeatInfo.repeatDays.monday,
              tuesday: appointment.repeatInfo.repeatDays.tuesday,
              wednesday: appointment.repeatInfo.repeatDays.wednesday,
              thursday: appointment.repeatInfo.repeatDays.thursday,
              friday: appointment.repeatInfo.repeatDays.friday,
              saturday: appointment.repeatInfo.repeatDays.saturday,
            },
            endingDate: appointment.repeatInfo.endingDate,
            endingAfter: appointment.repeatInfo.endingAfter,
            endingType: appointment.repeatInfo.endingType,
          },
        };

        const newAppointment = await AppointmentDao.createAppointment(
          appointmentDetails
        );

        const utcTime = moment.utc(appointment.start);

        const estTime = utcTime.tz('America/New_York');

        await EmailService.sendEventEmail(
          therapist,
          "New appointment is created!",
          `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by`,
          "Click here to connect with the client.",
          client.firstname + " " + client.lastname
        );

        if (therapist?.primaryPhone) {
          await SMSService.sendEventSMS(
            `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by ${client.firstname} ${client.lastname}`,
            therapist?.primaryPhone,
            "Appointment-ep 03"
          );
        }

        return res.sendSuccess(
          newAppointment,
          "Appointment Created Successfully."
        );
      } else if (appointment.repeatInfo?.repeatType == RepeatType.WEEKLY) {
        const daysOf21 = Util.calculateDays(
          21,
          appointmentStartTime,
          client._id!,
          therapist._id!
        );

        let appointmentsInNext21Days =
          await AppointmentDao.getAllAppointmentsBySelectedWeek(daysOf21);

        if (appointmentsInNext21Days.length > 0) {
          if (
            client?.premiumStatus != "active" &&
            sessionDetails.sessionTimeOfWeek == 0
          ) {
            return res.sendError(
              "You have already scheduled sessions during next 4 weeks."
            );
          }
        }

        const dateAStart = moment(appointmentStartTime);
        const dateAEnd = moment(appointmentEndTime);

        const dateBStart = moment(appointmentStartTime).add(7, "days");
        const dateBEnd = moment(appointmentEndTime).add(7, "days");

        const dateCStart = moment(appointmentStartTime).add(14, "days");
        const dateCEnd = moment(appointmentEndTime).add(14, "days");

        const dateDStart = moment(appointmentStartTime).add(21, "days");
        const dateDEnd = moment(appointmentEndTime).add(21, "days");

        let appointementsInTheCurrentSlotsA =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            dateAStart,
            dateAEnd,
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

        let appointementsInTheCurrentSlotsB =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            dateBStart,
            dateBEnd,
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

        let appointementsInTheCurrentSlotsC =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            dateCStart,
            dateCEnd,
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

        let appointementsInTheCurrentSlotsD =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            dateDStart,
            dateDEnd,
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

        if (
          appointementsInTheCurrentSlotsA.length > 0 ||
          appointementsInTheCurrentSlotsB.length > 0 ||
          appointementsInTheCurrentSlotsC.length > 0 ||
          appointementsInTheCurrentSlotsD.length > 0
        ) {
          return res.sendError(
            "Therapist has already scheduled an appointment during the selected time slot."
          );
        }

        let appointementsInTheCurrentSlotsOfClientA =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(dateAStart),
            new Date(dateAEnd),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

        let appointementsInTheCurrentSlotsOfClientB =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(dateBStart),
            new Date(dateBEnd),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

        let appointementsInTheCurrentSlotsOfClientC =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(dateCStart),
            new Date(dateCEnd),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

        let appointementsInTheCurrentSlotsOfClientD =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(dateDStart),
            new Date(dateDEnd),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

        if (
          appointementsInTheCurrentSlotsOfClientA.length > 0 ||
          appointementsInTheCurrentSlotsOfClientB.length > 0 ||
          appointementsInTheCurrentSlotsOfClientC.length > 0 ||
          appointementsInTheCurrentSlotsOfClientD.length > 0
        ) {
          return res.sendError(
            "You have already scheduled an appointment during the selected time slot."
          );
        }

        const appointmentDetailsObj: DAppointment = {
          therapistId: therapistId,
          clientId: clientId,
          start: dateAStart,
          end: dateAEnd,
          title: appointment.title,
          reminders: appointment.reminders,
          typeOfMeeting: "VIDEO",
          createdBy: clientId,
          color: appointment.color,
          groupId: appointment.groupId,
          status: AppointmentStatus.WAITING_FOR_APPROVAL,
          approvedStatus: ApprovalStatus.PENDING,
          repeatInfo: {
            repeatType: appointment.repeatInfo.repeatType,
            interval: appointment.repeatInfo.interval,
            repeatDays: {
              sunday: appointment.repeatInfo.repeatDays.sunday,
              monday: appointment.repeatInfo.repeatDays.monday,
              tuesday: appointment.repeatInfo.repeatDays.tuesday,
              wednesday: appointment.repeatInfo.repeatDays.wednesday,
              thursday: appointment.repeatInfo.repeatDays.thursday,
              friday: appointment.repeatInfo.repeatDays.friday,
              saturday: appointment.repeatInfo.repeatDays.saturday,
            },
            endingDate: appointment.repeatInfo.endingDate,
            endingAfter: appointment.repeatInfo.endingAfter,
            endingType: appointment.repeatInfo.endingType,
          },
        };

        await AppointmentDao.createAppointment(appointmentDetailsObj);

        appointmentDetailsObj.start = dateBStart;
        appointmentDetailsObj.end = dateBEnd;

        await AppointmentDao.createAppointment(appointmentDetailsObj);

        appointmentDetailsObj.start = dateCStart;
        appointmentDetailsObj.end = dateCEnd;

        await AppointmentDao.createAppointment(appointmentDetailsObj);

        appointmentDetailsObj.start = dateDStart;
        appointmentDetailsObj.end = dateDEnd;

        await AppointmentDao.createAppointment(appointmentDetailsObj);

        await EmailService.sendEventEmail(
          therapist,
          "New weekly appointments are created!",
          "New weekly appointments are scheduled by",
          "Click here to connect with the client.",
          client.firstname + " " + client.lastname
        );

        return res.sendSuccess(
          "Weekly appointments are created successfully."
        );
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function createAppointmentByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const appointment = req.body.appointment;
    const therapistId = appointment.therapistId;
    const clientId = appointment.clientId;
    const sessionStart = moment(appointment.start);
    const sessionEnd = moment(appointment.end);

    const sessionDuration = moment
      .duration(sessionEnd.diff(sessionStart))
      .asHours();

    if (!mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid therapist Id");
    }

    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid client Id");
    }

    try {
      let therapist = await TherapistDao.getUserById(therapistId);

      let client = await ClientDao.getUserById(clientId);

      if (!therapist || therapist == null) {
        return res.sendError(
          "No existing therapist for the provided therapist Id."
        );
      }

      if (!client || client == null) {
        return res.sendError("No existing client for the provided client Id.");
      }

      let isFriend = await FriendRequestDao.checkIfUserIsFriend(
        client._id,
        therapist._id
      );

      if (!isFriend) {
        return res.sendError(
          "Sorry! Client has not connected with this Therapist yet."
        );
      }

      if (
        client.premiumStatus != "active" &&
        (client.subscriptionId == null ||
          client.subscriptionStatus != "active") &&
        client.testSubscriptionStatus != "active"
      ) {
        return res.sendError(
          "Sorry! Client does not have an active subscription."
        );
      }

      if (Util.invalidTimeCheck(appointment.start)) {
        return res.sendError("Sorry! You have selected invalid time.");
      }

      // if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
      //   return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
      // }

      const beginsAt = moment(appointment.start);
      const endsAt = moment(appointment.start);

      const startTime = moment(appointment.start).format("H:mm A");
      const endTime = moment(appointment.end).format("H:mm A");
      const appointmentStartTime = moment(appointment.start).format("YYYY-MM-DD HH:mm");
      const appointmentEndTime = moment(appointment.end).format("YYYY-MM-DD HH:mm");

      const mST = moment(startTime, "HH:mm").minute();

      if (mST != 0 && mST != 30) {
        return res.sendError("Please select valid start time.");
      }

      const datesOfWeek = Util.calculateWeekNumberAndDates(
        appointmentStartTime,
        client._id!,
        therapist._id!
      );

      const appointmentId = datesOfWeek.appointmentId;
      let sessionTimeOfWeek = 0;

      let appointmentsBySelectedWeek =
        await AppointmentDao.getAllAppointmentsBySelectedWeek(datesOfWeek);

      if (appointmentId) {
        appointmentsBySelectedWeek = appointmentsBySelectedWeek.filter(
          (appointment: IAppointment) =>
            appointmentId.toString() !== appointment._id.toString()
        );
      }

      if (appointmentsBySelectedWeek.length) {
        for (const session of appointmentsBySelectedWeek) {
          const startTime = moment(session.start, "HH:mm:ss a");
          const endTime = moment(session.end, "HH:mm:ss a");
          const duration = moment.duration(endTime.diff(startTime));
          const minutes = parseInt(duration.asMinutes().toString());

          sessionTimeOfWeek = sessionTimeOfWeek + minutes;
        }
      }

      const sessionDetails = {
        selectedDate: datesOfWeek.selectedDate,
        allSessionOfWeek: appointmentsBySelectedWeek,
        sessionTimeOfWeek: sessionTimeOfWeek,
        allSession: appointmentsBySelectedWeek.length,
      };

      if (
        appointmentsBySelectedWeek === undefined ||
        appointmentsBySelectedWeek == null
      ) {
        return res.sendError("Server error occured. Please reach to support.");
      }

      if (Util.skipCheckingForPremiumUsers(client)) {
        const meetingDuration = Util.getMeetingTimeDuration(startTime, endTime);

        if (
          sessionDetails.sessionTimeOfWeek != 0 &&
          sessionDetails.sessionTimeOfWeek != 30 &&
          sessionDetails.sessionTimeOfWeek != 60
        ) {
          // Bypassing weekly session time validation
          // return res.sendError(
          //   "Sorry! Client's weekly session time has exceeded."
          // );
        }

        // Bypassing additional weekly session time validations
        /*
        if (sessionDetails.sessionTimeOfWeek != 0) {
          if (sessionDetails.sessionTimeOfWeek == 30 && meetingDuration == 60) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          } else if (sessionDetails.sessionTimeOfWeek == 60) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          }
        }
        */
      }

      if (!startTime) {
        return res.sendError("Please select valid start time.");
      }

      if (
        !moment(new Date()).isBefore(
          moment(
            new Date(appointmentStartTime).setHours(
              parseInt(startTime.split(":")[0]),
              parseInt(startTime.split(":")[1]),
              0,
              0
            )
          )
        )
      ) {
        return res.sendError(
          "Sorry! You can't create appointment in a past date!"
        );
      }

      if (!appointment.title) {
        return res.sendError("Please add title.");
      }

      const timeDifferenceInHours = moment
        .duration(endsAt.diff(beginsAt))
        .asHours();

      if (timeDifferenceInHours > 1) {
        return res.sendError("You can create one hour sessions only.");
      }

      const workingDaysOfTherapist: string[] = [];

      therapist.workingHours?.map((session: any) => {
        if (!workingDaysOfTherapist.includes(session.day))
          workingDaysOfTherapist.push(session.day);
      });

      if (appointment.repeatInfo?.repeatType == RepeatType.DOES_NOT_REPEAT) {
        let appointementsInTheCurrentSlots =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            new Date(appointment.start),
            new Date(appointment.end),
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

        if (appointementsInTheCurrentSlots.length > 0) {
          return res.sendError(
            "Therapist has already scheduled an appointment during the selected time slot."
          );
        }

        let appointementsInTheCurrentSlotsOfClient =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(appointment.start),
            new Date(appointment.end),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

        if (appointementsInTheCurrentSlotsOfClient.length > 0) {
          return res.sendError(
            "Client has already scheduled an appointment during the selected time slot."
          );
        }

        if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
          return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
        }

        const appointmentDetails: DAppointment = {
          therapistId: therapist._id,
          clientId: client._id,
          start: new Date(appointment.start),
          end: new Date(appointment.end),
          title: appointment.title,
          reminders: appointment.reminders,
          typeOfMeeting: "VIDEO",
          createdBy: client._id,
          color: appointment.color,
          groupId: appointment.groupId,
          status: AppointmentStatus.PENDING,
          approvedStatus: ApprovalStatus.APPROVED,
          repeatInfo: {
            repeatType: appointment.repeatInfo.repeatType,
            interval: appointment.repeatInfo.interval,
            repeatDays: {
              sunday: appointment.repeatInfo.repeatDays.sunday,
              monday: appointment.repeatInfo.repeatDays.monday,
              tuesday: appointment.repeatInfo.repeatDays.tuesday,
              wednesday: appointment.repeatInfo.repeatDays.wednesday,
              thursday: appointment.repeatInfo.repeatDays.thursday,
              friday: appointment.repeatInfo.repeatDays.friday,
              saturday: appointment.repeatInfo.repeatDays.saturday,
            },
            endingDate: appointment.repeatInfo.endingDate,
            endingAfter: appointment.repeatInfo.endingAfter,
            endingType: appointment.repeatInfo.endingType,
          },
        };

        const newAppointment = await AppointmentDao.createAppointment(
          appointmentDetails
        );

        const dataForCalendar = {
          start: appointment.start,
          end: appointment.end,
          title: appointment.title,
        }
        console.log("Does not repeate");
        console.log(dataForCalendar);
        
        const calendarRes = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendar, therapistId);
        if (calendarRes){
          AppLogger.info(`Google calendar sync successfully by admin, therapistId: ${therapistId}, start time: ${appointment.start}, end time: ${appointment.end}`);
        } else {
          AppLogger.error(`Google calendar sync failed by admin, therapistId: ${therapistId}, start time: ${appointment.start}, end time: ${appointment.end}`);
        }

        // await EmailService.sendEventEmail(
        //   therapist,
        //   "New appointment is created!",
        //   "New appointment is scheduled by",
        //   "Click here to connect with the client.",
        //   client.firstname + " " + client.lastname
        // );

        await EmailService.sendEventEmail(
          therapist,
          "New appointment is created!",
          "New appointment is scheduled by",
          "Click here to connect with the client.",
          "Lavni admin"
        );

        const utcTime = moment.utc(appointment.start);

        const estTime = utcTime.tz('America/New_York');

        await EmailService.sendEventReminderEmail(
          client,
          "New appointment is created!",
          `Hi ${client.firstname} ${client.lastname},<br><br>
          Friendly Reminder: Your therapy appointment is scheduled for ${estTime.format('YYYY-MM-DD hh:mm A')}.
          If you have any questions or need to reschedule, please reply to this message. We look forward to seeing you soon!
          <br><br>
          <br><br>
          Best,<br>
          lavni`,
        );

        if (therapist?.primaryPhone) {
          await SMSService.sendEventSMS(
            `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by Lavni Admin`,
            therapist?.primaryPhone,
            "Appointment-ep 04"
          );
        }

        if (client?.primaryPhone) {
          await SMSService.sendEventSMS(
            `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by Lavni Admin`,
            client?.primaryPhone
          );
        }

        return res.sendSuccess(
          newAppointment,
          "Appointment Created Successfully."
        );
      } else if (appointment.repeatInfo?.repeatType == RepeatType.BI_WEEKLY) {
        const daysOf42 = Util.calculateDays(
          42,
          appointmentStartTime,
          client._id!,
          therapist._id!
        );

        let appointmentsInNext42Days =
          await AppointmentDao.getAllAppointmentsBySelectedWeek(daysOf42);

        if (appointmentsInNext42Days.length > 0) {
          if (
            client?.premiumStatus != "active" &&
            sessionDetails.sessionTimeOfWeek == 0
          ) {
            return res.sendError(
              "You have already scheduled sessions during next 4 weeks."
            );
          }
        }

        const dateAStartBW = moment(appointmentStartTime);
        const dateAEndBW = moment(appointmentEndTime);

        const dateBStartBW = moment(appointmentStartTime).add(14, "days");
        const dateBEndBW = moment(appointmentEndTime).add(14, "days");

        const dateCStartBW = moment(appointmentStartTime).add(28, "days");
        const dateCEndBW = moment(appointmentEndTime).add(28, "days");

        const dateDStartBW = moment(appointmentStartTime).add(42, "days");
        const dateDEndBW = moment(appointmentEndTime).add(42, "days");

        const blockedWeeks: any = [];
        const datesForCheck = [
          { start: dateAStartBW, week: "first" },
          { start: dateBStartBW, week: "third" },
          { start: dateCStartBW, week: "fifth" },
          { start: dateDStartBW, week: "seventh" }
        ];
  
        datesForCheck.forEach((date, index) => {
          if (Util.validateBlockedDates(date.start, therapist.blockedDates)) {
            blockedWeeks.push(date.week); 
          }
        });

        const scheduledTherapistWeeks: string[] = [];
        const timeSlotsForCheckAppointmentAlreadySceduledOrNot: any = [
          { start: dateAStartBW, end: dateAEndBW, week: "first" },
          { start: dateBStartBW, end: dateBEndBW, week: "third" },
          { start: dateCStartBW, end: dateCEndBW, week: "fifth" },
          { start: dateDStartBW, end: dateDEndBW, week: "seventh" }
        ];

        for (const date of timeSlotsForCheckAppointmentAlreadySceduledOrNot) {
          const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            date.start,
            date.end,
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

          if (appointmentsInCurrentSlot.length > 0) {
            scheduledTherapistWeeks.push(date.week); 
          }
        }

        if (scheduledTherapistWeeks.length > 0) {
          console.log("Already scheduled appointments in above weeks for therapist", scheduledTherapistWeeks)
        }

        const scheduledClientWeeks: string[] = [];

        for (const date of timeSlotsForCheckAppointmentAlreadySceduledOrNot) {
          const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(date.start),
            new Date(date.end),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

          if (appointmentsInCurrentSlot.length > 0) {
            scheduledClientWeeks.push(date.week); 
          }
        }

        if (scheduledClientWeeks.length > 0) {
          console.log("Already scheduled appointments in above weeks for client", scheduledClientWeeks)
        }

        const appointmentDetailsObj: DAppointment = {
          therapistId: therapistId,
          clientId: clientId,
          start: dateAStartBW,
          end: dateAEndBW,
          title: appointment.title,
          reminders: appointment.reminders,
          typeOfMeeting: "VIDEO",
          createdBy: clientId,
          color: appointment.color,
          groupId: appointment.groupId,
          status: AppointmentStatus.PENDING,
          approvedStatus: ApprovalStatus.APPROVED,
          repeatInfo: {
            repeatType: appointment.repeatInfo.repeatType,
            interval: appointment.repeatInfo.interval,
            repeatDays: {
              sunday: appointment.repeatInfo.repeatDays.sunday,
              monday: appointment.repeatInfo.repeatDays.monday,
              tuesday: appointment.repeatInfo.repeatDays.tuesday,
              wednesday: appointment.repeatInfo.repeatDays.wednesday,
              thursday: appointment.repeatInfo.repeatDays.thursday,
              friday: appointment.repeatInfo.repeatDays.friday,
              saturday: appointment.repeatInfo.repeatDays.saturday,
            },
            endingDate: appointment.repeatInfo.endingDate,
            endingAfter: appointment.repeatInfo.endingAfter,
            endingType: appointment.repeatInfo.endingType,
          },
        };
        const appointmentScheduledDatesForSendBiWeeklyMessage = [];
        if( !blockedWeeks.includes('first') && !scheduledTherapistWeeks.includes('first') && !scheduledClientWeeks.includes('first')) {
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendBiWeeklyMessage.push(dateAStartBW);
          const dataForCalendarBiWeekly01 = {
            start: new Date(dateAStartBW).toISOString(),
            end: new Date(dateAEndBW).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForFirstBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly01, therapistId);
          if (calendarResForFirstBiWeeklyAppointment){
            AppLogger.info(`Bi-weekly appointment 01 synced successfully to Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly01.start}, end time: ${dataForCalendarBiWeekly01.end}`);
          } else {
            AppLogger.error(`Bi-weekly appointment 01 failed to sync with Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly01.start}, end time: ${dataForCalendarBiWeekly01.end}`);
          }
        }
        
        if( !blockedWeeks.includes('third') && !scheduledTherapistWeeks.includes('third') && !scheduledClientWeeks.includes('third')) {
          appointmentDetailsObj.start = dateBStartBW;
          appointmentDetailsObj.end = dateBEndBW;
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendBiWeeklyMessage.push(dateBStartBW);
          const dataForCalendarBiWeekly02 = {
            start: new Date(dateBStartBW).toISOString(),
            end: new Date(dateBEndBW).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForSecondBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly02, therapistId);
          if (calendarResForSecondBiWeeklyAppointment){
            AppLogger.info(`Bi-weekly appointment 02 synced successfully to Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly02.start}, end time: ${dataForCalendarBiWeekly02.end}`);
          } else {
            AppLogger.error(`Bi-weekly appointment 02 failed to sync with Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly02.start}, end time: ${dataForCalendarBiWeekly02.end}`);
          }
        }
        
        if( !blockedWeeks.includes('fifth') && !scheduledTherapistWeeks.includes('fifth') && !scheduledClientWeeks.includes('fifth')) {
          appointmentDetailsObj.start = dateCStartBW;
          appointmentDetailsObj.end = dateCEndBW;
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendBiWeeklyMessage.push(dateCStartBW);
          const dataForCalendarBiWeekly03 = {
            start: new Date(dateCStartBW).toISOString(),
            end: new Date(dateCEndBW).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForThirdBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly03, therapistId);
          if (calendarResForThirdBiWeeklyAppointment){
            AppLogger.info(`Bi-weekly appointment 03 synced successfully to Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly03.start}, end time: ${dataForCalendarBiWeekly03.end}`);
          } else {
            AppLogger.error(`Bi-weekly appointment 03 failed to sync with Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly03.start}, end time: ${dataForCalendarBiWeekly03.end}`);
          }
        }
        
        if( !blockedWeeks.includes('seventh') && !scheduledTherapistWeeks.includes('seventh') && !scheduledClientWeeks.includes('seventh')) {
          appointmentDetailsObj.start = dateDStartBW;
          appointmentDetailsObj.end = dateDEndBW;

          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendBiWeeklyMessage.push(dateDStartBW);
          const dataForCalendarBiWeek04 = {
            start: new Date(dateDStartBW).toISOString(),
            end: new Date(dateDEndBW).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForFourthBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeek04, therapistId);
          if (calendarResForFourthBiWeeklyAppointment){
            AppLogger.info(`Bi-weekly appointment 04 synced successfully to Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeek04.start}, end time: ${dataForCalendarBiWeek04.end}`);
          } else {
            AppLogger.error(`Bi-weekly appointment 04 failed to sync with Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeek04.start}, end time: ${dataForCalendarBiWeek04.end}`);
          }
        }
        
        if (appointmentScheduledDatesForSendBiWeeklyMessage.length > 0){
          const messageStringForWeeklyAppointments = generateWeeklyAndBiWeeklyMessageForSent(appointmentScheduledDatesForSendBiWeeklyMessage, "bi weekly");
          AppLogger.info(`createAppointmentByAdmin bi weekly schedule, ${messageStringForWeeklyAppointments} Lavni admin, therapistId: ${therapistId}, clientId: ${clientId}`);
          await EmailService.sendEventEmail(
            therapist,
            "New bi weekly appointments are created!",
            messageStringForWeeklyAppointments,
            "Click here to connect with the client.",
            "Lavni admin"
          );
  
          if (therapist?.primaryPhone) {
            await SMSService.sendEventSMS(
              `${messageStringForWeeklyAppointments} Lavni admin`,
              therapist?.primaryPhone,
              "Appointment-ep 05"
            );
          }
  
          await EmailService.sendEventEmail(
            client,
            "New bi weekly appointments are created!",
            messageStringForWeeklyAppointments,
            "Click here to connect with the therapist.",
            "Lavni admin"
          );
  
          if (client?.primaryPhone) {
            await SMSService.sendEventSMS(
              `${messageStringForWeeklyAppointments} lavni admin`,
              client?.primaryPhone,
              "Appointment-ep 46"
            );
          }
        }

        const combinedArray = [...blockedWeeks, ...scheduledTherapistWeeks, ...scheduledClientWeeks];
        const uniqueCombinedArray = [...new Set(combinedArray)];
        const uniqueToMainArray = datesForCheck.filter(item => uniqueCombinedArray.includes(item.week));
        const combinedStartString = uniqueToMainArray.length > 0 ? uniqueToMainArray.map(item => moment(item.start).tz(moment.tz.guess()).format('MMMM DD YYYY')).join(", "): '';

        if (uniqueToMainArray.length > 0){
          if (uniqueToMainArray.length == 4) {
            return res.sendError(`Bi-weekly appointments have not been scheduled due to a time conflict`);
          } else {
            return res.sendError(`Bi-weekly appointments have been successfully scheduled. ${combinedStartString !== '' ? `Except for ${combinedStartString} due to a time conflict.` : ''}`);
          }
        } 

        return res.sendSuccess(
          "Bi Weekly appointments are created successfully."
        );
      } else if (appointment.repeatInfo?.repeatType == RepeatType.WEEKLY) {
        const daysOf21 = Util.calculateDays(
          21,
          appointmentStartTime,
          client._id!,
          therapist._id!
        );

        let appointmentsInNext21Days =
          await AppointmentDao.getAllAppointmentsBySelectedWeek(daysOf21);

        if (appointmentsInNext21Days.length > 0) {
          if (
            client?.premiumStatus != "active" &&
            sessionDetails.sessionTimeOfWeek == 0
          ) {
            return res.sendError(
              "You have already scheduled sessions during next 4 weeks."
            );
          }
        }

        const dateAStart = moment(appointmentStartTime);
        const dateAEnd = moment(appointmentEndTime);

        const dateBStart = moment(appointmentStartTime).add(7, "days");
        const dateBEnd = moment(appointmentEndTime).add(7, "days");

        const dateCStart = moment(appointmentStartTime).add(14, "days");
        const dateCEnd = moment(appointmentEndTime).add(14, "days");

        const dateDStart = moment(appointmentStartTime).add(21, "days");
        const dateDEnd = moment(appointmentEndTime).add(21, "days");

        const blockedWeeks: any = [];
        const datesForCheck = [
          { start: dateAStart, week: "first" },
          { start: dateBStart, week: "second" },
          { start: dateCStart, week: "third" },
          { start: dateDStart, week: "fourth" }
        ];

        datesForCheck.forEach((date, index) => {
          if (Util.validateBlockedDates(date.start, therapist.blockedDates)) {
            blockedWeeks.push(date.week); 
          }
        });

        const scheduledTherapistWeeks: string[] = [];
        const timeSlotsForCheck: any = [
          { start: dateAStart, end: dateAEnd, week: "first" },
          { start: dateBStart, end: dateBEnd, week: "second" },
          { start: dateCStart, end: dateCEnd, week: "third" },
          { start: dateDStart, end: dateDEnd, week: "fourth" }
        ];

        for (const date of timeSlotsForCheck) {
          const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            date.start,
            date.end,
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

          if (appointmentsInCurrentSlot.length > 0) {
            scheduledTherapistWeeks.push(date.week); // Push the week if appointments are found
          }
        }

        if (scheduledTherapistWeeks.length > 0) {
          console.log("Already scheduled appointments in above weeks for therapist", scheduledTherapistWeeks)
        }

        const scheduledClientWeeks: string[] = [];

        for (const date of timeSlotsForCheck) {
          const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(date.start),
            new Date(date.end),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

          if (appointmentsInCurrentSlot.length > 0) {
            scheduledClientWeeks.push(date.week); 
          }
        }

        if (scheduledClientWeeks.length > 0) {
          console.log("Already scheduled appointments in above weeks for client", scheduledClientWeeks)
        }

        const appointmentDetailsObj: DAppointment = {
          therapistId: therapist._id,
          clientId: client._id,
          start: dateAStart,
          end: dateAEnd,
          title: appointment.title,
          reminders: appointment.reminders,
          typeOfMeeting: "VIDEO",
          createdBy: client._id,
          color: appointment.color,
          groupId: appointment.groupId,
          status: AppointmentStatus.PENDING,
          approvedStatus: ApprovalStatus.APPROVED,
          repeatInfo: {
            repeatType: appointment.repeatInfo.repeatType,
            interval: appointment.repeatInfo.interval,
            repeatDays: {
              sunday: appointment.repeatInfo.repeatDays.sunday,
              monday: appointment.repeatInfo.repeatDays.monday,
              tuesday: appointment.repeatInfo.repeatDays.tuesday,
              wednesday: appointment.repeatInfo.repeatDays.wednesday,
              thursday: appointment.repeatInfo.repeatDays.thursday,
              friday: appointment.repeatInfo.repeatDays.friday,
              saturday: appointment.repeatInfo.repeatDays.saturday,
            },
            endingDate: appointment.repeatInfo.endingDate,
            endingAfter: appointment.repeatInfo.endingAfter,
            endingType: appointment.repeatInfo.endingType,
          },
        };
        const appointmentScheduledDatesForSendWeeklyMessage = [];
        if( !blockedWeeks.includes('first') && !scheduledTherapistWeeks.includes('first') && !scheduledClientWeeks.includes('first')) {
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendWeeklyMessage.push(dateAStart);
          const dataForCalendarWeekly01 = {
            start: new Date(dateAStart).toISOString(),
            end: new Date(dateAEnd).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForFirstWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly01, therapistId);
          if (calendarResForFirstWeeklyAppointment){
            AppLogger.info(`Weekly appointment 01 synced successfully to Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly01.start}, end time: ${dataForCalendarWeekly01.end}`);
          } else {
            AppLogger.error(`Weekly appointment 01 failed to sync with Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly01.start}, end time: ${dataForCalendarWeekly01.end}`);
          }
        }
        
        if( !blockedWeeks.includes('second') && !scheduledTherapistWeeks.includes('second') && !scheduledClientWeeks.includes('second')) {
          appointmentDetailsObj.start = dateBStart;
          appointmentDetailsObj.end = dateBEnd;

          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendWeeklyMessage.push(dateBStart);
          const dataForCalendarWeekly02 = {
            start: new Date(dateBStart).toISOString(),
            end: new Date(dateBEnd).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForSecondWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly02, therapistId);
          if (calendarResForSecondWeeklyAppointment){
            AppLogger.info(`Weekly appointment 02 synced successfully to Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly02.start}, end time: ${dataForCalendarWeekly02.end}`);
          } else {
            AppLogger.error(`Weekly appointment 02 failed to sync with Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly02.start}, end time: ${dataForCalendarWeekly02.end}`);
          }
        }
        
        if( !blockedWeeks.includes('third') && !scheduledTherapistWeeks.includes('third') && !scheduledClientWeeks.includes('third')) {
          appointmentDetailsObj.start = dateCStart;
          appointmentDetailsObj.end = dateCEnd;
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendWeeklyMessage.push(dateCStart);
          const dataForCalendarWeekly03 = {
            start: new Date(dateCStart).toISOString(),
            end: new Date(dateCEnd).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForThirdWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly03, therapistId);
          if (calendarResForThirdWeeklyAppointment){
            AppLogger.info(`Weekly appointment 03 synced successfully to Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly03.start}, end time: ${dataForCalendarWeekly03.end}`);
          } else {
            AppLogger.error(`Weekly appointment 03 failed to sync with Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly03.start}, end time: ${dataForCalendarWeekly03.end}`);
          }
        }
        
        if( !blockedWeeks.includes('fourth') && !scheduledTherapistWeeks.includes('fourth') && !scheduledClientWeeks.includes('fourth')) {
          appointmentDetailsObj.start = dateDStart;
          appointmentDetailsObj.end = dateDEnd;
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendWeeklyMessage.push(dateDStart);
          const dataForCalendarWeekly04 = {
            start: new Date(dateDStart).toISOString(),
            end: new Date(dateDEnd).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForFourthWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly04, therapistId);
          if (calendarResForFourthWeeklyAppointment){
            AppLogger.info(`Weekly appointment 04 synced successfully to Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly04.start}, end time: ${dataForCalendarWeekly04.end}`);
          } else {
            AppLogger.error(`Weekly appointment 04 failed to sync with Google Calendar by admin, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly04.start}, end time: ${dataForCalendarWeekly04.end}`);
          }
        }
        
        if (appointmentScheduledDatesForSendWeeklyMessage.length > 0){
          const messageStringForWeeklyAppointments = generateWeeklyAndBiWeeklyMessageForSent(appointmentScheduledDatesForSendWeeklyMessage, "weekly");
          AppLogger.info(`createAppointmentByAdmin weekly schedule, ${messageStringForWeeklyAppointments} Lavni admin, therapistId: ${therapistId}, clientId: ${clientId}`);
          await EmailService.sendEventEmail(
            therapist,
            "New weekly appointments are created!",
            messageStringForWeeklyAppointments,
            "Click here to connect with the client.",
            "Lavni admin"
          );
  
          if (therapist?.primaryPhone) {
            await SMSService.sendEventSMS(
              `${messageStringForWeeklyAppointments} lavni admin`,
              therapist?.primaryPhone,
              "Appointment-ep 06"
            );
          }
  
          await EmailService.sendEventEmail(
            client,
            "New weekly appointments are created!",
            messageStringForWeeklyAppointments,
            "Click here to connect with the therapist.",
            "Lavni admin"
          );
  
          if (client?.primaryPhone) {
            await SMSService.sendEventSMS(
              `${messageStringForWeeklyAppointments} lavni admin`,
              client?.primaryPhone
            );
          }
        }
        const combinedArray = [...blockedWeeks, ...scheduledTherapistWeeks, ...scheduledClientWeeks];
        const uniqueCombinedArray = [...new Set(combinedArray)];
        const uniqueToMainArray = datesForCheck.filter(item => uniqueCombinedArray.includes(item.week));
        const combinedStartString = uniqueToMainArray.length > 0 ? uniqueToMainArray.map(item => moment(item.start).tz(moment.tz.guess()).format('MMMM DD YYYY')).join(", "): '';

        if (uniqueToMainArray.length > 0){
          if (uniqueToMainArray.length == 4) {
            return res.sendError(`Weekly appointments have not been scheduled due to a time conflict`);
          }
          return res.sendError(`Weekly appointments have been successfully scheduled. ${combinedStartString !== '' ? `Except for ${combinedStartString} due to a time conflict.` : ''}`);
        }

        return res.sendSuccess("Weekly appointments are created successfully.");
      } else {
        return res.sendError('Incorrect repeat type when scheduling an appointment by the admin.');
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function createAppointmentByTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.user._id;
    const appointment = req.body.appointment;
    const therapistId = appointment.therapistId;
    const clientId = appointment.clientId;
    const sessionStart = moment(appointment.start);
    const sessionEnd = moment(appointment.end);

    const sessionDuration = moment
      .duration(sessionEnd.diff(sessionStart))
      .asHours();

    if (!mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid therapist Id");
    }

    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid client Id");
    }

    try {
      let therapist = await TherapistDao.getUserById(therapistId);

      let client = await ClientDao.getUserById(clientId);

      if (!therapist || therapist == null) {
        return res.sendError(
          "No existing therapist for the provided therapist Id."
        );
      }

      if (!client || client == null) {
        return res.sendError("No existing client for the provided client Id.");
      }

      let isFriend = await FriendRequestDao.checkIfUserIsFriend(
        client._id,
        therapist._id
      );

      if (!isFriend) {
        return res.sendError(
          "Sorry! Selected client has not connected with this therapist yet."
        );
      }

      if (
        client.premiumStatus != "active" &&
        (client.subscriptionId == null ||
          client.subscriptionStatus != "active") &&
        client.testSubscriptionStatus != "active"
      ) {
        return res.sendError(
          "Sorry! This client doesn't have an active subscription."
        );
      }

      if (Util.invalidTimeCheck(appointment.start)) {
        return res.sendError("Sorry! You have selected invalid time.");
      }

      // if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
      //   return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
      // }

      const beginsAt = moment(appointment.start);
      const endsAt = moment(appointment.end);

      const startTime = moment(appointment.start).format("H:mm A");
      const endTime = moment(appointment.end).format("H:mm A");
      const appointmentStartTime = moment(appointment.start).format("YYYY-MM-DD HH:mm");
      const appointmentEndTime = moment(appointment.end).format("YYYY-MM-DD HH:mm");

      const mST = moment(startTime, "HH:mm").minute();

      if (mST != 0 && mST != 30) {
        return res.sendError("Please select valid start time.");
      }

      const datesOfWeek = Util.calculateWeekNumberAndDates(
        appointmentStartTime,
        client._id!,
        therapist._id!
      );

      const appointmentId = datesOfWeek.appointmentId;
      let sessionTimeOfWeek = 0;

      let appointmentsBySelectedWeek =
        await AppointmentDao.getAllAppointmentsBySelectedWeek(datesOfWeek);

      if (appointmentId) {
        appointmentsBySelectedWeek = appointmentsBySelectedWeek.filter(
          (appointment: IAppointment) =>
            appointmentId.toString() !== appointment._id.toString()
        );
      }

      if (appointmentsBySelectedWeek.length) {
        for (const session of appointmentsBySelectedWeek) {
          const startTime = moment(session.start, "HH:mm:ss a");
          const endTime = moment(session.end, "HH:mm:ss a");
          const duration = moment.duration(endTime.diff(startTime));
          const minutes = parseInt(duration.asMinutes().toString());

          sessionTimeOfWeek = sessionTimeOfWeek + minutes;
        }
      }

      const sessionDetails = {
        selectedDate: datesOfWeek.selectedDate,
        allSessionOfWeek: appointmentsBySelectedWeek,
        sessionTimeOfWeek: sessionTimeOfWeek,
        allSession: appointmentsBySelectedWeek.length,
      };

      if (
        appointmentsBySelectedWeek === undefined ||
        appointmentsBySelectedWeek == null
      ) {
        return res.sendError("Server error occured. Please reach to support.");
      }

      if (Util.skipCheckingForPremiumUsers(client)) {
        const meetingDuration = Util.getMeetingTimeDuration(startTime, endTime);

        if (
          sessionDetails.sessionTimeOfWeek != 0 &&
          sessionDetails.sessionTimeOfWeek != 30 &&
          sessionDetails.sessionTimeOfWeek != 60
        ) {
          // Bypassing weekly session time validation
          // return res.sendError(
          //   "Sorry! Client's weekly session time has exceeded."
          // );
        }

        // Bypassing additional weekly session time validations
        /*
        if (sessionDetails.sessionTimeOfWeek != 0) {
          if (sessionDetails.sessionTimeOfWeek == 30 && meetingDuration == 60) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          } else if (sessionDetails.sessionTimeOfWeek == 60) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          }
        }
        */
      }

      if (!startTime) {
        return res.sendError("Please select valid start time.");
      }

      const workingDaysOfTherapist: string[] = [];

      therapist.workingHours?.map((session: any) => {
        if (!workingDaysOfTherapist.includes(session.day))
          workingDaysOfTherapist.push(session.day);
      });

      if (
        !moment(new Date()).isBefore(
          moment(
            new Date(appointmentStartTime).setHours(
              parseInt(startTime.split(":")[0]),
              parseInt(startTime.split(":")[1]),
              0,
              0
            )
          )
        )
      ) {
        return res.sendError(
          "Sorry! You can't create appointment in a past date!"
        );
      }

      if (!appointment.title) {
        return res.sendError("Please add title.");
      }

      const timeDifferenceInHours = moment
        .duration(endsAt.diff(beginsAt))
        .asHours();

      if (timeDifferenceInHours > 1) {
        return res.sendError("You can create one hour sessions only.");
      }

      console.log("repeatType ", appointment.repeatInfo?.repeatType);
      

      if (appointment.repeatInfo?.repeatType == RepeatType.DOES_NOT_REPEAT) {
        let appointementsInTheCurrentSlots =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            new Date(appointment.start),
            new Date(appointment.end),
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

        if (appointementsInTheCurrentSlots.length > 0) {
          return res.sendError(
            "Therapist has already scheduled an appointment during the selected time slot."
          );
        }

        let appointementsInTheCurrentSlotsOfClient =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(appointment.start),
            new Date(appointment.end),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

        if (appointementsInTheCurrentSlotsOfClient.length > 0) {
          return res.sendError(
            "Client has already scheduled an appointment during the selected time slot."
          );
        }

        if (Util.validateBlockedDates(appointment.start, therapist.blockedDates)) {
          return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this time.");
        }

        const appointmentDetails: DAppointment = {
          therapistId: therapistId,
          clientId: clientId,
          start: appointment.start,
          end: appointment.end,
          title: appointment.title,
          reminders: appointment.reminders,
          typeOfMeeting: "VIDEO",
          createdBy: userId,
          color: appointment.color,
          groupId: appointment.groupId,
          status: AppointmentStatus.PENDING,
          approvedStatus: ApprovalStatus.APPROVED,
          repeatInfo: {
            repeatType: appointment.repeatInfo.repeatType,
            interval: appointment.repeatInfo.interval,
            repeatDays: {
              sunday: appointment.repeatInfo.repeatDays.sunday,
              monday: appointment.repeatInfo.repeatDays.monday,
              tuesday: appointment.repeatInfo.repeatDays.tuesday,
              wednesday: appointment.repeatInfo.repeatDays.wednesday,
              thursday: appointment.repeatInfo.repeatDays.thursday,
              friday: appointment.repeatInfo.repeatDays.friday,
              saturday: appointment.repeatInfo.repeatDays.saturday,
            },
            endingDate: appointment.repeatInfo.endingDate,
            endingAfter: appointment.repeatInfo.endingAfter,
            endingType: appointment.repeatInfo.endingType,
          },
        };

        const newAppointment = await AppointmentDao.createAppointment(
          appointmentDetails
        );

        const utcTime = moment.utc(appointment.start);

        const estTime = utcTime.tz('America/New_York');

        const dataForCalendar = {
          start: appointment.start,
          end: appointment.end,
          title: appointment.title,
        }
        
        const calendarRes = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendar, therapistId);
        if (calendarRes){
          AppLogger.info(`Google calendar sync successfully, therapistId: ${therapistId}, start time: ${appointment.start}, end time: ${appointment.end}`);
        } else {
          AppLogger.error(`Google calendar sync failed, therapistId: ${therapistId}, start time: ${appointment.start}, end time: ${appointment.end}`);
        }

        await EmailService.sendEventEmail(
          client,
          "New appointment is created!",
          `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by`,
          "Click here to connect with the therapist.",
          therapist.firstname + " " + therapist.lastname
        );

        if (client?.primaryPhone) {
          await SMSService.sendEventSMS(
            `New appointment is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST by ${therapist.firstname} ${therapist.lastname}`,
            client?.primaryPhone,
            "Appointment-ep 07"
          );
        }
        return res.sendSuccess(
          newAppointment,
          "Appointment Created Successfully."
        );
      } else if (appointment.repeatInfo?.repeatType == RepeatType.BI_WEEKLY) {
        const daysOf42 = Util.calculateDays(
          42,
          appointmentStartTime,
          client._id!,
          therapist._id!
        );

        let appointmentsInNext42Days = await AppointmentDao.getAllAppointmentsBySelectedWeek(daysOf42);

        if (appointmentsInNext42Days.length > 0) {
          if (
            client?.premiumStatus != "active" &&
            sessionDetails.sessionTimeOfWeek == 0
          ) {
            return res.sendError(
              "You have already scheduled sessions during next 4 weeks."
            );
          }
        }

        const dateAStartBW = moment(appointmentStartTime);
        const dateAEndBW = moment(appointmentEndTime);

        const dateBStartBW = moment(appointmentStartTime).add(14, "days");
        const dateBEndBW = moment(appointmentEndTime).add(14, "days");

        const dateCStartBW = moment(appointmentStartTime).add(28, "days");
        const dateCEndBW = moment(appointmentEndTime).add(28, "days");

        const dateDStartBW = moment(appointmentStartTime).add(42, "days");
        const dateDEndBW = moment(appointmentEndTime).add(42, "days");

        const blockedWeeks: any = [];
        const datesForCheckBlockedOrNot = [
          { start: dateAStartBW, week: "first" },
          { start: dateBStartBW, week: "third" },
          { start: dateCStartBW, week: "fifth" },
          { start: dateDStartBW, week: "seventh" }
        ];

        datesForCheckBlockedOrNot.forEach((date, index) => {
          if (Util.validateBlockedDates(date.start, therapist.blockedDates)) {
            blockedWeeks.push(date.week); 
          }
        });

        // if (blockedWeeks.length > 0) {
        //   const weeksMessage = blockedWeeks.join(", ").replace(/,([^,]*)$/, " and$1");
        //   const message = `Cannot schedule the Bi-weekly appointment. There is a blocked time in the ${weeksMessage} week${blockedWeeks.length > 1 ? "s" : ""} at this time.`;
        //   return res.sendError(message); 
        // }

        if (blockedWeeks.length > 0) {
          console.log("There is a blocked time in above weeks ", blockedWeeks)
        }

        const scheduledWeeks: string[] = [];
        const timeSlotsForCheckAppointmentAlreadySceduledOrNot: any = [
          { start: dateAStartBW, end: dateAEndBW, week: "first" },
          { start: dateBStartBW, end: dateBEndBW, week: "third" },
          { start: dateCStartBW, end: dateCEndBW, week: "fifth" },
          { start: dateDStartBW, end: dateDEndBW, week: "seventh" }
        ];

        // Check for appointments and push scheduled weeks into the array
        for (const date of timeSlotsForCheckAppointmentAlreadySceduledOrNot) {
          const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            date.start,
            date.end,
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

          if (appointmentsInCurrentSlot.length > 0) {
            scheduledWeeks.push(date.week); // Push the week if appointments are found
          }
        }

        if (scheduledWeeks.length > 0) {
          console.log("Already scheduled appointments in above weeks for therapist", scheduledWeeks)
        }

        const scheduledClientWeeks: string[] = [];
        const clientDatesForCheckAvailability = [
          { start: dateAStartBW, end: dateAEndBW, week: "first" },
          { start: dateBStartBW, end: dateBEndBW, week: "third" },
          { start: dateCStartBW, end: dateCEndBW, week: "fifth" },
          { start: dateDStartBW, end: dateDEndBW, week: "seventh" }
        ];

        // Check for client appointments and skip weeks with scheduled sessions
        for (const date of clientDatesForCheckAvailability) {
          const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(date.start),
            new Date(date.end),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

          if (appointmentsInCurrentSlot.length > 0) {
            scheduledClientWeeks.push(date.week); // Push the week if appointments are found
          }
        }

        if (scheduledClientWeeks.length > 0) {
          console.log("Already scheduled appointments in above weeks for client", scheduledClientWeeks)
        }

        const appointmentDetailsObj: DAppointment = {
          therapistId: therapistId,
          clientId: clientId,
          start: dateAStartBW,
          end: dateAEndBW,
          title: appointment.title,
          reminders: appointment.reminders,
          typeOfMeeting: "VIDEO",
          createdBy: userId,
          color: appointment.color,
          groupId: appointment.groupId,
          status: AppointmentStatus.PENDING,
          approvedStatus: ApprovalStatus.APPROVED,
          repeatInfo: {
            repeatType: appointment.repeatInfo.repeatType,
            interval: appointment.repeatInfo.interval,
            repeatDays: {
              sunday: appointment.repeatInfo.repeatDays.sunday,
              monday: appointment.repeatInfo.repeatDays.monday,
              tuesday: appointment.repeatInfo.repeatDays.tuesday,
              wednesday: appointment.repeatInfo.repeatDays.wednesday,
              thursday: appointment.repeatInfo.repeatDays.thursday,
              friday: appointment.repeatInfo.repeatDays.friday,
              saturday: appointment.repeatInfo.repeatDays.saturday,
            },
            endingDate: appointment.repeatInfo.endingDate,
            endingAfter: appointment.repeatInfo.endingAfter,
            endingType: appointment.repeatInfo.endingType,
          },
        };
        const appointmentScheduledDatesForSendBiWeeklyMessage = [];
        if( !blockedWeeks.includes('first') && !scheduledWeeks.includes('first') && !scheduledClientWeeks.includes('first')) {
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendBiWeeklyMessage.push(dateAStartBW);
          const dataForCalendarBiWeekly01 = {
            start: new Date(appointmentDetailsObj.start).toISOString(),
            end: new Date(appointmentDetailsObj.end).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForFirstBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly01, therapistId);
          if (calendarResForFirstBiWeeklyAppointment){
            AppLogger.info(`Bi-weekly appointment 01 synced successfully to Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly01.start}, end time: ${dataForCalendarBiWeekly01.end}`);
          } else {
            AppLogger.error(`Bi-weekly appointment 01 failed to sync with Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly01.start}, end time: ${dataForCalendarBiWeekly01.end}`);
          }
        }

        if( !blockedWeeks.includes('third') && !scheduledWeeks.includes('third') && !scheduledClientWeeks.includes('third')) {
          appointmentDetailsObj.start = dateBStartBW;
          appointmentDetailsObj.end = dateBEndBW;

          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendBiWeeklyMessage.push(dateBStartBW);
          const dataForCalendarBiWeekly02 = {
            start: new Date(dateBStartBW).toISOString(),
            end: new Date(dateBEndBW).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForSecondBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly02, therapistId);
          if (calendarResForSecondBiWeeklyAppointment){
            AppLogger.info(`Bi-weekly appointment 02 synced successfully to Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly02.start}, end time: ${dataForCalendarBiWeekly02.end}`);
          } else {
            AppLogger.error(`Bi-weekly appointment 02 failed to sync with Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly02.start}, end time: ${dataForCalendarBiWeekly02.end}`);
          }
        }

        if( !blockedWeeks.includes('fifth') && !scheduledWeeks.includes('fifth') && !scheduledClientWeeks.includes('fifth')) {
          appointmentDetailsObj.start = dateCStartBW;
          appointmentDetailsObj.end = dateCEndBW;

          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendBiWeeklyMessage.push(dateCStartBW);
          const dataForCalendarBiWeekly03 = {
            start: new Date(dateCStartBW).toISOString(),
            end: new Date(dateCEndBW).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForThirdBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeekly03, therapistId);
          if (calendarResForThirdBiWeeklyAppointment){
            AppLogger.info(`Bi-weekly appointment 03 synced successfully to Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly03.start}, end time: ${dataForCalendarBiWeekly03.end}`);
          } else {
            AppLogger.error(`Bi-weekly appointment 03 failed to sync with Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeekly03.start}, end time: ${dataForCalendarBiWeekly03.end}`);
          }
        }
        
        if( !blockedWeeks.includes('seventh') && !scheduledWeeks.includes('seventh') && !scheduledClientWeeks.includes('seventh')) {
          appointmentDetailsObj.start = dateDStartBW;
          appointmentDetailsObj.end = dateDEndBW;

          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendBiWeeklyMessage.push(dateDStartBW);
          const dataForCalendarBiWeek04 = {
            start: new Date(dateDStartBW).toISOString(),
            end: new Date(dateDEndBW).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForFourthBiWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarBiWeek04, therapistId);
          if (calendarResForFourthBiWeeklyAppointment){
            AppLogger.info(`Bi-weekly appointment 04 synced successfully to Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeek04.start}, end time: ${dataForCalendarBiWeek04.end}`);
          } else {
            AppLogger.error(`Bi-weekly appointment 04 failed to sync with Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarBiWeek04.start}, end time: ${dataForCalendarBiWeek04.end}`);
          }
        }

        if (appointmentScheduledDatesForSendBiWeeklyMessage.length > 0){
          const messageStringForWeeklyAppointments = generateWeeklyAndBiWeeklyMessageForSent(appointmentScheduledDatesForSendBiWeeklyMessage, "bi weekly");
          AppLogger.info(`createAppointmentByTherapist bi weekly schedule, ${messageStringForWeeklyAppointments} ${therapist?.firstname} ${therapist?.lastname}, therapistId: ${therapistId}, clientId: ${clientId}`);
          await EmailService.sendEventEmail(
            client,
            "New bi weekly appointments are created!",
            messageStringForWeeklyAppointments,
            "Click here to connect with the therapist.",
            therapist?.firstname + " " + therapist?.lastname
          );
  
          if (client?.primaryPhone) {
            await SMSService.sendEventSMS(
              `${messageStringForWeeklyAppointments} ${therapist?.firstname} ${therapist?.lastname}`,
              client?.primaryPhone,
              "Appointment-ep 08"
            );
          }  
        }
        
        const combinedArray = [...blockedWeeks, ...scheduledWeeks, ...scheduledClientWeeks];
        const uniqueCombinedArray = [...new Set(combinedArray)];
        const uniqueToMainArray = datesForCheckBlockedOrNot.filter(item => uniqueCombinedArray.includes(item.week));
        const combinedStartString = uniqueToMainArray.length > 0 ? uniqueToMainArray.map(item => moment(item.start).tz(moment.tz.guess()).format('MMMM DD YYYY')).join(", "): '';

        if (uniqueToMainArray.length > 0){
          if (uniqueToMainArray.length == 4) {
            return res.sendError(`Your bi-weekly appointments have not been scheduled due to a time conflict`);
          }
          return res.sendError(`Your bi-weekly appointments have been successfully scheduled. ${combinedStartString !== '' ? `Except for ${combinedStartString} due to a time conflict.` : ''}`);
        } 
        return res.sendSuccess(
          `Bi Weekly appointments are created successfully.`
        );
      } else if(appointment.repeatInfo?.repeatType == RepeatType.WEEKLY) {
        const daysOf21 = Util.calculateDays(
          21,
          appointmentStartTime,
          client._id!,
          therapist._id!
        );

        let appointmentsInNext21Days =
          await AppointmentDao.getAllAppointmentsBySelectedWeek(daysOf21);

        if (appointmentsInNext21Days.length > 0) {
          if (
            client?.premiumStatus != "active" &&
            sessionDetails.sessionTimeOfWeek == 0
          ) {
            return res.sendError(
              "You have already scheduled sessions during next 4 weeks."
            );
          }
        }
      
        const dateAStart = moment(appointmentStartTime)
        const dateAEnd = moment(appointmentEndTime);

        const dateBStart = moment(appointmentStartTime).add(7, "days");
        const dateBEnd = moment(appointmentEndTime).add(7, "days");

        const dateCStart = moment(appointmentStartTime).add(14, "days");
        const dateCEnd = moment(appointmentEndTime).add(14, "days");

        const dateDStart = moment(appointmentStartTime).add(21, "days");
        const dateDEnd = moment(appointmentEndTime).add(21, "days");

        const blockedWeeks: any = [];
        const datesForCheckBlockedOrNot = [
          { start: dateAStart, week: "first" },
          { start: dateBStart, week: "second" },
          { start: dateCStart, week: "third" },
          { start: dateDStart, week: "fourth" }
        ];

        datesForCheckBlockedOrNot.forEach((date, index) => {
          if (Util.validateBlockedDates(date.start, therapist.blockedDates)) {
            blockedWeeks.push(date.week); 
          }
        });

        // if (blockedWeeks.length > 0) {
        //   const weeksMessage = blockedWeeks.join(", ").replace(/,([^,]*)$/, " and$1");
        //   const message = `Cannot schedule the weekly appointment. There is a blocked time in the ${weeksMessage} week${blockedWeeks.length > 1 ? "s" : ""} at this time.`;
        //   return res.sendError(message); 
        // }

        const scheduledWeeks: string[] = [];
        const timeSlotsForCheck: any = [
          { start: dateAStart, end: dateAEnd, week: "first" },
          { start: dateBStart, end: dateBEnd, week: "second" },
          { start: dateCStart, end: dateCEnd, week: "third" },
          { start: dateDStart, end: dateDEnd, week: "fourth" }
        ];

        for (const date of timeSlotsForCheck) {
          const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            date.start,
            date.end,
            sessionDuration,
            Types.ObjectId(appointment.therapistId)
          );

          if (appointmentsInCurrentSlot.length > 0) {
            scheduledWeeks.push(date.week); // Push the week if appointments are found
          }
        }

        if (scheduledWeeks.length > 0) {
          console.log("Already scheduled appointments in above weeks for therapist", scheduledWeeks)
        }

        const scheduledClientWeeks: string[] = [];

        for (const date of timeSlotsForCheck) {
          const appointmentsInCurrentSlot = await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(date.start),
            new Date(date.end),
            sessionDuration,
            Types.ObjectId(appointment.clientId)
          );

          if (appointmentsInCurrentSlot.length > 0) {
            scheduledClientWeeks.push(date.week); 
          }
        }

        if (scheduledClientWeeks.length > 0) {
          console.log("Already scheduled appointments in above weeks for client", scheduledClientWeeks)
        }

        const appointmentDetailsObj: DAppointment = {
          therapistId: therapistId,
          clientId: clientId,
          start: dateAStart,
          end: dateAEnd,
          title: appointment.title,
          reminders: appointment.reminders,
          typeOfMeeting: "VIDEO",
          createdBy: userId,
          color: appointment.color,
          groupId: appointment.groupId,
          status: AppointmentStatus.PENDING,
          approvedStatus: ApprovalStatus.APPROVED,
          repeatInfo: {
            repeatType: appointment.repeatInfo.repeatType,
            interval: appointment.repeatInfo.interval,
            repeatDays: {
              sunday: appointment.repeatInfo.repeatDays.sunday,
              monday: appointment.repeatInfo.repeatDays.monday,
              tuesday: appointment.repeatInfo.repeatDays.tuesday,
              wednesday: appointment.repeatInfo.repeatDays.wednesday,
              thursday: appointment.repeatInfo.repeatDays.thursday,
              friday: appointment.repeatInfo.repeatDays.friday,
              saturday: appointment.repeatInfo.repeatDays.saturday,
            },
            endingDate: appointment.repeatInfo.endingDate,
            endingAfter: appointment.repeatInfo.endingAfter,
            endingType: appointment.repeatInfo.endingType,
          },
        };
        const appointmentScheduledDatesForSendWeeklyMessage = [];
        if( !blockedWeeks.includes('first') && !scheduledWeeks.includes('first') && !scheduledClientWeeks.includes('first')) {
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendWeeklyMessage.push(dateAStart);
          const dataForCalendarWeekly01 = {
            start: new Date(dateAStart).toISOString(),
            end: new Date(dateAEnd).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForFirstWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly01, therapistId);
          if (calendarResForFirstWeeklyAppointment){
            AppLogger.info(`Weekly appointment 01 synced successfully to Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly01.start}, end time: ${dataForCalendarWeekly01.end}`);
          } else {
            AppLogger.error(`Weekly appointment 01 failed to sync with Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly01.start}, end time: ${dataForCalendarWeekly01.end}`);
          }
        }
        
        if( !blockedWeeks.includes('second') && !scheduledWeeks.includes('second') && !scheduledClientWeeks.includes('second')) {
          appointmentDetailsObj.start = dateBStart;
          appointmentDetailsObj.end = dateBEnd;
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendWeeklyMessage.push(dateBStart);
          const dataForCalendarWeekly02 = {
            start: new Date(dateBStart).toISOString(),
            end: new Date(dateBEnd).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForSecondWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly02, therapistId);
          if (calendarResForSecondWeeklyAppointment){
            AppLogger.info(`Weekly appointment 02 synced successfully to Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly02.start}, end time: ${dataForCalendarWeekly02.end}`);
          } else {
            AppLogger.error(`Weekly appointment 02 failed to sync with Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly02.start}, end time: ${dataForCalendarWeekly02.end}`);
          }
        }
        
        if( !blockedWeeks.includes('third') && !scheduledWeeks.includes('third') && !scheduledClientWeeks.includes('third')) {
          appointmentDetailsObj.start = dateCStart;
          appointmentDetailsObj.end = dateCEnd;
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendWeeklyMessage.push(dateCStart);
          const dataForCalendarWeekly03 = {
            start: new Date(dateCStart).toISOString(),
            end: new Date(dateCEnd).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForThirdWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly03, therapistId);
          if (calendarResForThirdWeeklyAppointment){
            AppLogger.info(`Weekly appointment 03 synced successfully to Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly03.start}, end time: ${dataForCalendarWeekly03.end}`);
          } else {
            AppLogger.error(`Weekly appointment 03 failed to sync with Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly03.start}, end time: ${dataForCalendarWeekly03.end}`);
          }
        }

        if( !blockedWeeks.includes('fourth') && !scheduledWeeks.includes('fourth') && !scheduledClientWeeks.includes('fourth')) {
          appointmentDetailsObj.start = dateDStart;
          appointmentDetailsObj.end = dateDEnd;
          await AppointmentDao.createAppointment(appointmentDetailsObj);
          appointmentScheduledDatesForSendWeeklyMessage.push(dateDStart);
          const dataForCalendarWeekly04 = {
            start: new Date(dateDStart).toISOString(),
            end: new Date(dateDEnd).toISOString(),
            title: appointmentDetailsObj.title,
          }
          const calendarResForFourthWeeklyAppointment = await UserEp.createGoogleCalendarEventWhenCreateAppointment(dataForCalendarWeekly04, therapistId);
          if (calendarResForFourthWeeklyAppointment){
            AppLogger.info(`Weekly appointment 04 synced successfully to Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly04.start}, end time: ${dataForCalendarWeekly04.end}`);
          } else {
            AppLogger.error(`Weekly appointment 04 failed to sync with Google Calendar, therapistId: ${therapistId}, start time: ${dataForCalendarWeekly04.start}, end time: ${dataForCalendarWeekly04.end}`);
          }
        }

        if (appointmentScheduledDatesForSendWeeklyMessage.length > 0){
          const messageStringForWeeklyAppointments = generateWeeklyAndBiWeeklyMessageForSent(appointmentScheduledDatesForSendWeeklyMessage, "weekly");
          AppLogger.info(`createAppointmentByTherapist weekly schedule, ${messageStringForWeeklyAppointments} ${therapist?.firstname} ${therapist?.lastname}, therapistId: ${therapistId}, clientId: ${clientId}`);
          await EmailService.sendEventEmail(
            client,
            "New weekly appointments are created!",
            messageStringForWeeklyAppointments,
            "Click here to connect with the therapist.",
            therapist?.firstname + " " + therapist?.lastname
          );
          if (client?.primaryPhone) {
            await SMSService.sendEventSMS(
              `${messageStringForWeeklyAppointments} ${therapist?.firstname} ${therapist?.lastname}`,
              client?.primaryPhone,
              "Appointment-ep 09"
            );
          }
        }
        
        const combinedArray = [...blockedWeeks, ...scheduledWeeks, ...scheduledClientWeeks];
        const uniqueCombinedArray = [...new Set(combinedArray)];
        const uniqueToMainArray = datesForCheckBlockedOrNot.filter(item => uniqueCombinedArray.includes(item.week));
        const combinedStartString = uniqueToMainArray.length > 0 ? uniqueToMainArray.map(item => moment(item.start).tz(moment.tz.guess()).format('MMMM DD YYYY')).join(", "): '';

        if (uniqueToMainArray.length > 0){
          if (uniqueToMainArray.length == 4) {
            return res.sendError(`Your weekly appointments have not been scheduled due to a time conflict`);
          } else {
            return res.sendError(`Your weekly appointments have been successfully scheduled. ${combinedStartString !== '' ? `Except for ${combinedStartString} due to a time conflict.` : ''}`);
          }
        } 
        return res.sendSuccess("Weekly appointments are created successfully.");
      } else {
        return res.sendError('Incorrect repeat type when scheduling an appointment by the therapist.');
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateAppointment(req: Request, res: Response) {
    const role = req.user.role;
    const userId = req.user._id;
    const therapistId = req.body.therapistId;
    const clientId = req.body.clientId;
    const start = new Date(req.body.start);
    const end = new Date(req.body.end);
    const reminders = req.body.reminders;
    const title = req.body.title;
    const appointmentId = req.body.appointmentId;
    const color = req.body.color;
    const groupId = req.body.groupId;

    const sessionStart = moment(start);
    const sessionEnd = moment(end);

    const sessionDuration = moment
      .duration(sessionEnd.diff(sessionStart))
      .asHours();

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors);
    }

    if (!mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid therapist Id");
    }

    if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
      return res.sendError("Invalid appointment Id");
    }

    try {
      let therapist = await TherapistDao.getUserById(therapistId);

      if (!therapist || therapist == null) {
        return res.sendError(
          "No existing therapist for the provided therapist Id."
        );
      }

      let client = await ClientDao.getUserById(clientId);

      if (!client || client == null) {
        return res.sendError("No existing client for the provided client Id.");
      }

      let isAppointmentFound = await AppointmentDao.getAppointmentById(
        appointmentId
      );

      if (!isAppointmentFound || isAppointmentFound == null) {
        return res.sendError(
          "No existing appointment for the provided appointment Id."
        );
      }

      if (role != UserRole.SUPER_ADMIN && role != UserRole.SUB_ADMIN) {
        if (
          isAppointmentFound.createdBy.toString() !== userId.toString() &&
          isAppointmentFound.therapistId.toString() !== userId.toString() &&
          isAppointmentFound.clientId.toString() !== userId.toString()
        ) {
          return res.sendError("You are not allowed to edit this appointment.");
        }
      }

      if (isAppointmentFound.status == AppointmentStatus.COMPLETED) {
        return res.sendError("Not possible to edit completed appointments.");
      }

      if (isAppointmentFound.status == AppointmentStatus.REJECTED) {
        return res.sendError("Not possible to edit rejected appointments.");
      }

      let isFriend = await FriendRequestDao.checkIfUserIsFriend(
        client._id,
        therapist._id
      );

      if (!isFriend) {
        return res.sendError(
          "Sorry! You haven't connected with this Therapist yet."
        );
      }

      if (
        client.premiumStatus != "active" &&
        (client.subscriptionId == null ||
          client.subscriptionStatus != "active") &&
        client.testSubscriptionStatus != "active"
      ) {
        return res.sendError(
          "Sorry! This client doesn't have an active subscription."
        );
      }

      if (isAppointmentFound) {
        if (Util.invalidTimeCheck(start)) {
          return res.sendError("Sorry! You have selected invalid time.");
        }

        if (Util.validateBlockedDates(start, therapist.blockedDates)) {
          return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this date.");
        }

        const beginsAt = moment(start);
        const endsAt = moment(end);

        const appointmentStartTime = moment(start).format("YYYY-MM-DD HH:mm");
        const sessionStartTime = moment(start).format("H:mm A");
        const sessionEndTime = moment(end).format("H:mm A");

        const mST = moment(sessionStartTime, "HH:mm").minute();

        if (mST != 0 && mST != 30) {
          return res.sendError("Please select valid start time.");
        }

        const datesOfWeek = Util.calculateWeekNumberAndDates(
          appointmentStartTime,
          client._id!,
          therapist._id!
        );

        let sessionTimeOfWeek = 0;

        let appointmentsBySelectedWeek =
          await AppointmentDao.getAllAppointmentsBySelectedWeek(datesOfWeek);

        if (appointmentId) {
          appointmentsBySelectedWeek = appointmentsBySelectedWeek.filter(
            (appointment: IAppointment) =>
              appointmentId.toString() !== appointment._id.toString()
          );
        }

        if (appointmentsBySelectedWeek.length) {
          for (const session of appointmentsBySelectedWeek) {
            const startTime = moment(session.start, "HH:mm:ss a");
            const endTime = moment(session.end, "HH:mm:ss a");
            const duration = moment.duration(endTime.diff(startTime));
            const minutes = parseInt(duration.asMinutes().toString());

            sessionTimeOfWeek = sessionTimeOfWeek + minutes;
          }
        }

        const sessionDetails = {
          selectedDate: datesOfWeek.selectedDate,
          allSessionOfWeek: appointmentsBySelectedWeek,
          sessionTimeOfWeek: sessionTimeOfWeek,
          allSession: appointmentsBySelectedWeek.length,
        };

        if (
          appointmentsBySelectedWeek === undefined ||
          appointmentsBySelectedWeek == null
        ) {
          return res.sendError(
            "Server error occured. Please reach to support."
          );
        }

        if (Util.skipCheckingForPremiumUsers(client)) {
          const meetingDuration = Util.getMeetingTimeDuration(
            sessionStartTime,
            sessionEndTime
          );

          if (
            sessionDetails.sessionTimeOfWeek != 0 &&
            sessionDetails.sessionTimeOfWeek != 30 &&
            sessionDetails.sessionTimeOfWeek != 60
          ) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          }

          if (sessionDetails.sessionTimeOfWeek != 0) {
            if (
              sessionDetails.sessionTimeOfWeek == 30 &&
              meetingDuration == 60
            ) {
              return res.sendError(
                "Sorry! Client's weekly session time has exceeded."
              );
            } else if (sessionDetails.sessionTimeOfWeek == 60) {
              return res.sendError(
                "Sorry! Client's weekly session time has exceeded."
              );
            }
          }
        }

        if (!sessionStartTime) {
          return res.sendError("Please select valid start time.");
        }

        const workingDaysOfTherapist: string[] = [];

        therapist.workingHours?.map((session: any) => {
          if (!workingDaysOfTherapist.includes(session.day))
            workingDaysOfTherapist.push(session.day);
        });

        if (
          !moment(new Date()).isBefore(
            moment(
              new Date(appointmentStartTime).setHours(
                parseInt(sessionStartTime.split(":")[0]),
                parseInt(sessionStartTime.split(":")[1]),
                0,
                0
              )
            )
          )
        ) {
          return res.sendError(
            "Sorry! You can't create appointment in a past date!"
          );
        }

        if (!isAppointmentFound.title) {
          return res.sendError("Please add title.");
        }

        const timeDifferenceInHours = moment
          .duration(endsAt.diff(beginsAt))
          .asHours();

        if (timeDifferenceInHours < 0.5 && timeDifferenceInHours > 1) {
          return res.sendError(
            "You can only create one hour sessions or 30 minute sessions!"
          );
        }

        let appointementsInTheCurrentSlots =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            start,
            end,
            sessionDuration,
            Types.ObjectId(therapistId)
          );

        if (appointementsInTheCurrentSlots.length > 0) {
          let list = appointementsInTheCurrentSlots.filter(
            (a: IAppointment) =>
              a._id.toString() != isAppointmentFound._id.toString()
          );

          if (list.length > 0) {
            return res.sendError(
              role == UserRole.CLIENT || role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN
                ? "Therapist has already scheduled an appointment during the selected time slot."
                : "You have already scheduled an appointment during the selected time slot."
            );
          }
        }

        let appointementsInTheCurrentSlotsOfClient =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            start,
            end,
            sessionDuration,
            Types.ObjectId(clientId)
          );

        if (appointementsInTheCurrentSlotsOfClient.length > 0) {
          let list = appointementsInTheCurrentSlotsOfClient.filter(
            (a: IAppointment) =>
              a._id.toString() != isAppointmentFound._id.toString()
          );

          if (list.length > 0) {
            return res.sendError(
              role == UserRole.THERAPIST || role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN
                ? "Client has already scheduled an appointment during the selected time slot."
                : "You have already scheduled an appointment during the selected time slot."
            );
          }
        }

        const startTime = new Date(isAppointmentFound.start).getTime();
        const endTime = new Date(isAppointmentFound.end).getTime();

        if (
          role == UserRole.CLIENT ||
          role == UserRole.THERAPIST ||
          role == UserRole.SUPER_ADMIN ||
          role == UserRole.SUB_ADMIN
        ) {
          const appointmentDetails: DAppointment = {
            therapistId: therapistId,
            clientId: clientId,
            start: start,
            end: end,
            title: title,
            reminders: reminders,
            typeOfMeeting: "VIDEO",
            color: color,
            groupId: groupId,
            status: AppointmentStatus.PENDING,
            approvedStatus: ApprovalStatus.APPROVED,
            createdBy: userId,
            repeatInfo: isAppointmentFound.repeatInfo
          };

          let appointment = await AppointmentDao.updateAppointment(
            appointmentId,
            appointmentDetails
          );

          const convertOldStartToEasternTime = moment.utc(isAppointmentFound.start).tz('America/New_York').format('h:mm A ddd, MMM D');
          const convertNewStartToEasternTime = moment.utc(start).tz('America/New_York').format('h:mm A ddd, MMM D');
         
          if (userId.toString() == clientId.toString()) {
            if (
              !(
                startTime === new Date(start).getTime() &&
                endTime === new Date(end).getTime()
              )
            ) {
              AppLogger.info(`Appointment on ${convertOldStartToEasternTime} moved to ${convertNewStartToEasternTime} successfully by clientId: ${clientId}`);
              const msgToTherapist =  `Your ${convertOldStartToEasternTime} session with ${client?.firstname} ${client?.lastname} has been moved to ${convertNewStartToEasternTime}. If this time doesn't work for you, please contact the client directly.`;
              
              await EmailService.sendEventReminderEmail(
                therapist,
                "Client rescheduled appointment!",
                msgToTherapist
              );

              if (therapist?.primaryPhone) {
                await SMSService.sendEventSMS(
                  msgToTherapist,
                  therapist?.primaryPhone,
                  "Appointment-ep 10"
                );
              }

              const notificationDetails: DNotification = {
                senderId: userId,
                receiverId: therapistId,
                event: NotificationEvent.APPOINMENT_UPDATED,
                link: "/appointments",
                content:
                  "Appointment is rescheduled by " +
                  client?.firstname +
                  " " +
                  client?.lastname,
                variant: NotificationVarient.INFO,
                readStatus: false,
              };

              await NotificationDao.createNotification(notificationDetails);
            }
          }
          if (userId.toString() == therapistId.toString()) {
            if (
              !(
                startTime === new Date(start).getTime() &&
                endTime === new Date(end).getTime()
              )
            ) {
              AppLogger.info(`Appointment on ${convertOldStartToEasternTime} moved to ${convertNewStartToEasternTime} successfully by therapistId: ${therapistId}`);
              const msgToClient =  `Your ${convertOldStartToEasternTime} session with ${therapist?.firstname} ${therapist?.lastname} has been moved to ${convertNewStartToEasternTime}. If this time doesn't work for you, please contact the therapist directly.`;
              
              await EmailService.sendEventReminderEmail(
                client,
                "Your Therapist Rescheduled Appointment!",
                msgToClient
              );

              if (client?.primaryPhone) {
                await SMSService.sendEventSMS(
                  msgToClient,
                  client?.primaryPhone,
                  "Appointment-ep 12"
                );
              }

              const notificationDetails: DNotification = {
                senderId: therapistId,
                receiverId: clientId,
                event: NotificationEvent.APPOINMENT_UPDATED,
                link: "/appointments",
                content:
                  "Appointment is rescheduled by " +
                  therapist?.firstname +
                  " " +
                  therapist?.lastname,
                variant: NotificationVarient.INFO,
                readStatus: false,
              };

              await NotificationDao.createNotification(notificationDetails);
            }
          }
          AppLogger.info(`Appointment is updated successfully, appointmentId: ${appointmentId}`)
          return res.sendSuccess(
            appointment,
            "Appointment is updated successfully."
          );
        } else {
          AppLogger.error(`Invalid user role for update appointment, appointmentId: ${appointmentId}`);
          return res.sendError("Invalid user role.");
        }
      } else {
        AppLogger.error(`Appointment not found for update, appointmentId: ${appointmentId}`);
        return res.sendError(
          "Could not find an appointment for the provided appointment Id."
        );
      }
    } catch (error) {
      AppLogger.error(`Appointment update failed, appointmentId: ${appointmentId}, clientId: ${clientId}, therapistId: ${therapistId}, error: ${error}`);
      return res.sendError(error);
    }
  }

  export async function updateAppointmentStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const appointmentId = req.params.id;
    const status: any = req.params.status;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    let isFoundAppointment = await AppointmentDao.getAppointmentById(
      appointmentId
    );

    if (!isFoundAppointment || isFoundAppointment == null) {
      return res.sendError(
        "No existing appointment for the provided appointment Id."
      );
    }

    if (
      req.user.role == UserRole.CLIENT ||
      req.user.role == UserRole.THERAPIST
    ) {
      const appointmentDetails: DAppointment = {
        therapistId: isFoundAppointment.therapistId,
        start: isFoundAppointment.start,
        end: isFoundAppointment.end,
        title: isFoundAppointment.title,
        reminders: isFoundAppointment.reminders,
        typeOfMeeting: isFoundAppointment.typeOfMeeting,
        color: isFoundAppointment.color,
        createdBy: isFoundAppointment.createdBy,
        status: status,
        repeatInfo: {
          repeatType: isFoundAppointment.repeatInfo.repeatType,
          interval: isFoundAppointment.repeatInfo.interval,
          repeatDays: {
            sunday: isFoundAppointment.repeatInfo.repeatDays.sunday,
            monday: isFoundAppointment.repeatInfo.repeatDays.monday,
            tuesday: isFoundAppointment.repeatInfo.repeatDays.tuesday,
            wednesday: isFoundAppointment.repeatInfo.repeatDays.wednesday,
            thursday: isFoundAppointment.repeatInfo.repeatDays.thursday,
            friday: isFoundAppointment.repeatInfo.repeatDays.friday,
            saturday: isFoundAppointment.repeatInfo.repeatDays.saturday,
          },
          endingDate: isFoundAppointment.repeatInfo.endingDate,
          endingAfter: isFoundAppointment.repeatInfo.endingAfter,
          endingType: isFoundAppointment.repeatInfo.endingType,
        },
      };

      try {
        let appointment = await AppointmentDao.updateAppointment(
          Types.ObjectId(appointmentId),
          appointmentDetails
        );
        return res.sendSuccess(
          appointment,
          "Appointment Status Updated Successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function updateAppointmentApprovalStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    const role = req.user.role;
    const appointmentId = req.body.appointmentId;
    const approvedStatus = req.body.approvedStatus;
    const GoogleCalendarEventId = req.body.GoogleCalendarEventId;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
      return res.sendError("Invalid appointment Id");
    }

    try {
      let appointment = await AppointmentDao.getAppointmentById(appointmentId);

      if (!appointment) {
        return res.sendError("Cannot find an appointment for the provided Id.");
      }

      const user1 = await UserDao.getUserById(appointment.therapistId);
      const user2 = await UserDao.getUserById(appointment.clientId);
      const currentTimestamp = new Date();
      const sessionStart = moment(appointment.start);
      const sessionEnd = moment(appointment.end);

      const sessionDuration = moment
        .duration(sessionEnd.diff(sessionStart))
        .asHours();

      let appointementsInTheCurrentSlots =
        await AppointmentDao.getAppointmentsOfTherapistByStartTime(
          appointment.start,
          appointment.end,
          sessionDuration,
          appointment.therapistId
        );

      if (appointementsInTheCurrentSlots.length > 0) {
        let list = appointementsInTheCurrentSlots.filter(
          (a: IAppointment) => a._id.toString() != appointment._id.toString()
        );

        if (list.length > 0) {
          return res.sendError(
            role == "CLIENT"
              ? "Therapist has already scheduled an appointment during the selected time slot."
              : "You have already scheduled an appointment during the selected time slot."
          );
        }
      }

      let appointementsInTheCurrentSlotsOfClient =
        await AppointmentDao.getAppointmentsOfClientByStartTime(
          new Date(appointment.start),
          new Date(appointment.end),
          sessionDuration,
          appointment.clientId
        );

      if (appointementsInTheCurrentSlotsOfClient.length > 0) {
        let list = appointementsInTheCurrentSlotsOfClient.filter(
          (a: IAppointment) => a._id.toString() != appointment._id.toString()
        );

        if (list.length > 0) {
          return res.sendError(
            role == "CLIENT"
              ? "You have already scheduled an appointment during the selected time slot."
              : "Client has already scheduled an appointment during the selected time slot."
          );
        }
      }

      if (appointment.createdBy.toString() == appointment.clientId.toString()) {
        if (appointment.therapistId.toString() !== userId.toString()) {
          return res.sendError(
            "You are not allowed to approve this appointment."
          );
        } else {
          if (approvedStatus == ApprovalStatus.APPROVED) {
            const currentTimestamp = new Date();
            const appointmentUpdate: any = {
                status: AppointmentStatus.PENDING,
                approvedStatus: ApprovalStatus.APPROVED,
                GoogleCalendarEventId: GoogleCalendarEventId,
            };

            if (role === UserRole.THERAPIST) {
                appointmentUpdate.therapistChangeApprovedStatusAt = currentTimestamp;
            }
            await AppointmentDao.updateAppointment(appointmentId, appointmentUpdate);

            await EmailService.sendEventEmail(
              user2,
              "Appointment has been approved!",
              "Appointment has been approved by",
              "Click here to connect with the therapist.",
              user1.firstname + " " + user1.lastname
            );

            if (user2?.primaryPhone) {
              await SMSService.sendEventSMS(
                `Appointment has been approved by ${user1.firstname} ${user1.lastname}`,
                user2?.primaryPhone,
                "Appointment-ep 14"
              );
            }
          }

          if (approvedStatus == ApprovalStatus.REJECTED) {
            const appointmentUpdate: any = {
              status: AppointmentStatus.REJECTED,
              approvedStatus: ApprovalStatus.REJECTED,
            }

            if (role === UserRole.THERAPIST){
              appointmentUpdate.therapistChangeApprovedStatusAt = currentTimestamp
            }
            await AppointmentDao.updateAppointment(appointmentId, appointmentUpdate);

            await EmailService.sendEventEmail(
              user2,
              "Appointment has been rescheduled!",
              "Appointment has been rescheduled by",
              "Click here to connect with the therapist.",
              user1.firstname + " " + user1.lastname
            );

            if (user2?.primaryPhone) {
              await SMSService.sendEventSMS(
                `Appointment has been rescheduled by ${user1.firstname} ${user1.lastname}`,
                user2?.primaryPhone,
                "Appointment-ep 15"
              );
            }
          }
        }
      }

      if (
        appointment.createdBy.toString() == appointment.therapistId.toString()
      ) {
        if (appointment.clientId.toString() !== userId.toString()) {
          return res.sendError(
            "You are not allowed to approve this appointment."
          );
        } else {
          if (approvedStatus == ApprovalStatus.APPROVED) {
            const appointmentUpdate: any = {
              status: AppointmentStatus.PENDING,
              approvedStatus: ApprovalStatus.APPROVED,
            }

            if (role === UserRole.THERAPIST){
              appointmentUpdate.therapistChangeApprovedStatusAt = currentTimestamp
            }
            await AppointmentDao.updateAppointment(appointmentId, appointmentUpdate);

            await EmailService.sendEventEmail(
              user1,
              "Appointment has been approved!",
              "Appointment has been approved by",
              "Click here to connect with the therapist.",
              user2.firstname + " " + user2.lastname
            );

            await SMSService.sendEventSMS(
              `Appointment has been approved by ${user2.firstname} ${user2.lastname}`,
              user1.primaryPhone,
              "Appointment-ep 16"
            );
          }
          if (approvedStatus == ApprovalStatus.REJECTED) {
            const appointmentUpdate: any = {
              status: AppointmentStatus.REJECTED,
              approvedStatus: ApprovalStatus.REJECTED,
            }
            if (role === UserRole.THERAPIST){
              appointmentUpdate.therapistChangeApprovedStatusAt = currentTimestamp
            }
            await AppointmentDao.updateAppointment(appointmentId, appointmentUpdate);

            await EmailService.sendEventEmail(
              user1,
              "Appointment has been rescheduled!",
              "Appointment has been rescheduled by",
              "Click here to connect with the therapist.",
              user2.firstname + " " + user2.lastname
            );

            if (user1?.primaryPhone) {
              await SMSService.sendEventSMS(
                `Appointment has been rescheduled by ${user2.firstname} ${user2.lastname}`,
                user1?.primaryPhone,
                "Appointment-ep 17"
              );
            }
          }
        }
      }

      return res.sendSuccess("", "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateAppointmentApprovalStatusByClientForClientCreatedAppointment(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.user._id;
    const role = req.user.role;
    const appointmentId = req.body.appointmentId;
    const approvedStatus = req.body.approvedStatus;
    const GoogleCalendarEventId = req.body.GoogleCalendarEventId;
    const currentTimestamp = new Date();

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
      return res.sendError("Invalid appointment Id");
    }

    try {
      let appointment = await AppointmentDao.getAppointmentById(appointmentId);

      if (!appointment) {
        return res.sendError("Cannot find an appointment for the provided Id.");
      }

      const user1 = await UserDao.getUserById(appointment.therapistId);
      const user2 = await UserDao.getUserById(appointment.clientId);

      const sessionStart = moment(appointment.start);
      const sessionEnd = moment(appointment.end);

      const sessionDuration = moment
        .duration(sessionEnd.diff(sessionStart))
        .asHours();

      let appointementsInTheCurrentSlots =
        await AppointmentDao.getAppointmentsOfTherapistByStartTime(
          appointment.start,
          appointment.end,
          sessionDuration,
          appointment.therapistId
        );

      if (appointementsInTheCurrentSlots.length > 0) {
        let list = appointementsInTheCurrentSlots.filter(
          (a: IAppointment) => a._id.toString() != appointment._id.toString()
        );

        if (list.length > 0) {
          return res.sendError(
            role == "CLIENT"
              ? "Therapist has already scheduled an appointment during the selected time slot."
              : "You have already scheduled an appointment during the selected time slot."
          );
        }
      }

      let appointementsInTheCurrentSlotsOfClient =
        await AppointmentDao.getAppointmentsOfClientByStartTime(
          new Date(appointment.start),
          new Date(appointment.end),
          sessionDuration,
          appointment.clientId
        );

      if (appointementsInTheCurrentSlotsOfClient.length > 0) {
        let list = appointementsInTheCurrentSlotsOfClient.filter(
          (a: IAppointment) => a._id.toString() != appointment._id.toString()
        );

        if (list.length > 0) {
          return res.sendError(
            role == "CLIENT"
              ? "You have already scheduled an appointment during the selected time slot."
              : "Client has already scheduled an appointment during the selected time slot."
          );
        }
      }

      if (appointment.createdBy.toString() == appointment.clientId.toString()) {
        if (appointment.clientId.toString() !== userId.toString()) {
          return res.sendError(
            "You are not allowed to approve this appointment."
          );
        } else {
          if (approvedStatus == ApprovalStatus.APPROVED) {
            const appointmentUpdate: any = {
              status: AppointmentStatus.PENDING,
              approvedStatus: ApprovalStatus.APPROVED,
              GoogleCalendarEventId: GoogleCalendarEventId,
            }

            if (role === UserRole.THERAPIST){
              appointmentUpdate.therapistChangeApprovedStatusAt = currentTimestamp
            }
            await AppointmentDao.updateAppointment(appointmentId, appointmentUpdate);

            await EmailService.sendEventEmail(
              user2,
              "Appointment has been approved!",
              "Appointment has been approved by",
              "Click here to connect with the therapist.",
              user1.firstname + " " + user1.lastname
            );

            if (user2?.primaryPhone) {
              await SMSService.sendEventSMS(
                `Appointment has been approved by ${user1.firstname} ${user1.lastname}`,
                user2?.primaryPhone,
                "Appointment-ep 18"
              );
            }
          }

          if (approvedStatus == ApprovalStatus.REJECTED) {
            const appointmentUpdate: any = {
              status: AppointmentStatus.REJECTED,
              approvedStatus: ApprovalStatus.REJECTED,
            }
            if (role === UserRole.THERAPIST){
              appointmentUpdate.therapistChangeApprovedStatusAt = currentTimestamp
            }
            await AppointmentDao.updateAppointment(appointmentId, appointmentUpdate);

            await EmailService.sendEventEmail(
              user2,
              "Appointment has been rescheduled!",
              "Appointment has been rescheduled by",
              "Click here to connect with the therapist.",
              user1.firstname + " " + user1.lastname
            );

            if (user2?.primaryPhone) {
              await SMSService.sendEventSMS(
                `Appointment has been rescheduled by ${user1.firstname} ${user1.lastname}`,
                user2?.primaryPhone,
                "Appointment-ep 19"
              );
            }
          }
        }
      }

      if (
        appointment.createdBy.toString() == appointment.therapistId.toString()
      ) {
        if (appointment.therapistId.toString() !== userId.toString()) {
          return res.sendError(
            "You are not allowed to approve this appointment."
          );
        } else {
          if (approvedStatus == ApprovalStatus.APPROVED) {
            const appointmentUpdate: any = {
              status: AppointmentStatus.PENDING,
              approvedStatus: ApprovalStatus.APPROVED,
            }
            if (role === UserRole.THERAPIST){
              appointmentUpdate.therapistChangeApprovedStatusAt = currentTimestamp
            }
            await AppointmentDao.updateAppointment(appointmentId, appointmentUpdate);

            await EmailService.sendEventEmail(
              user1,
              "Appointemnt has been approved!",
              "Appointemnt has been approved by",
              "Click here to connect with the therapist.",
              user2.firstname + " " + user2.lastname
            );

            await SMSService.sendEventSMS(
              `Appointmnt has been approved by ${user2.firstname} ${user2.lastname}`,
              user1.primaryPhone,
              "Appointment-ep 20"
            );
          }
          if (approvedStatus == ApprovalStatus.REJECTED) {
            const appointmentUpdate: any = {
              status: AppointmentStatus.REJECTED,
              approvedStatus: ApprovalStatus.REJECTED,
            }
            if (role === UserRole.THERAPIST){
              appointmentUpdate.therapistChangeApprovedStatusAt = currentTimestamp
            }
            await AppointmentDao.updateAppointment(appointmentId, appointmentUpdate);

            await EmailService.sendEventEmail(
              user1,
              "Appointment has been rescheduled!",
              "Appointment has been rescheduled by",
              "Click here to connect with the therapist.",
              user2.firstname + " " + user2.lastname
            );

            if (user1?.primaryPhone) {
              await SMSService.sendEventSMS(
                `Appointment has been rescheduled by ${user2.firstname} ${user2.lastname}`,
                user1?.primaryPhone,
                "Appointment-ep 21"
              );
            }
          }
        }
      }

      return res.sendSuccess("", "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function viewSingleAppointment(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const appointmentId = req.params.id;

    if (role == UserRole.THERAPIST || role == UserRole.CLIENT) {
      if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
        return res.sendError("Invalid appointment Id.");
      }

      try {
        let appointment = await AppointmentDao.getAppointmentByAppointmentId(
          appointmentId
        );

        if (!appointment || appointment == null) {
          return res.sendError("Invalid appointment Id.");
        } else {
          return res.sendSuccess(
            appointment,
            "Apppintment Received Successfully."
          );
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function viewSingleAppointmentPublic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const appointmentId = req.params.id;

    if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
      return res.sendError("Invalid appointment Id.");
    }

    try {
      let appointment = await AppointmentDao.getAppointmentByAppointmentId(
        appointmentId
      );

      if (!appointment || appointment == null) {
        return res.sendError("Invalid appointment Id.");
      } else {
        return res.sendSuccess(
          appointment,
          "Apppintment Received Successfully."
        );
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function viewSingleAppointmentAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const appointmentId = req.params.id;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
        return res.sendError("Invalid appointment Id.");
      }

      try {
        let appointment = await AppointmentDao.getAppointmentByAppointmentId(
          appointmentId
        );

        if (!appointment || appointment == null) {
          return res.sendError("Invalid appointment Id.");
        } else {
          return res.sendSuccess(
            appointment,
            "Apppintment Received Successfully."
          );
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function searchAppointmentsByDate(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.user._id;
    const date = new Date(req.body.date);

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
      try {
        let appointmentList = await AppointmentDao.searchAppointmentByDate(
          date,
          userId
        );

        if (appointmentList.length === 0) {
          return res.sendError(
            `No appointments on ${moment(date).format("YYYY-MM-DD")}`
          );
        }

        return res.sendSuccess(
          appointmentList,
          "Apppintments Received Successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function viewAllAppointmentsByUser(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.user._id;
    const customActiveTab = req.params.customActiveTab;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
      try {
        let appointmentList: IAppointment[] = [];

        if (role == UserRole.CLIENT) {
          appointmentList = await AppointmentDao.getAllAppointmentsByClientIdNew(
            userId,
            customActiveTab,
            limit,
            offset
          );
        }

        if (role == UserRole.THERAPIST) {
          appointmentList = await AppointmentDao.getAllAppointmentsByTherapistIdNew(userId, customActiveTab, limit, offset);
        }

        return res.sendSuccess(
          appointmentList,
          "Apppintments Received Successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function viewAllUpcomingAppointmentStats(userRole: any, _id: any, userLimit: any) {
    const role = userRole;
    const userId = _id;
    const limit = Number(userLimit);

    if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
      try {
        let appointmentList: IAppointment[] = [];

        if (role == UserRole.CLIENT) {
          appointmentList =
            await AppointmentDao.getAllUpcomingAppointmentsByClientId(
              userId,
              limit
            );
        }

        if (role == UserRole.THERAPIST) {
          appointmentList =
            await AppointmentDao.getAllUpcomingAppointmentsByTherapistId(
              userId,
              limit
            );
        }
        return appointmentList;

      } catch (error) {
        return error;
      }
    } else {
      const error3 = "Invalid user role."
      return error3;
    }
  }

  export async function viewAllPendingAppointmentStats(userRole: any, _id: any, userLimit: any) {
    const role = userRole;
    const userId = _id;
    const limit = Number(userLimit);

    if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
      try {
        let appointmentList: IAppointment[] = [];

        if (role == UserRole.CLIENT) {
          appointmentList =
            await AppointmentDao.getAllPendingAppointmentsByClientId(
              userId,
              limit
            );
        }

        if (role == UserRole.THERAPIST) {
          appointmentList =
            await AppointmentDao.getAllPendingAppointmentsByTherapistId(
              userId,
              limit
            );
        }
        return appointmentList;

      } catch (error) {
        return error;
      }
    } else {
      const error3 = "Invalid user role."
      return error3;
    }
  }


  //  remmove this
  export async function viewAllUpcomingAppointments(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.user._id;
    const limit = Number(req.params.limit);


    if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
      try {
        let appointmentList: IAppointment[] = [];

        if (role == UserRole.CLIENT) {
          appointmentList =
            await AppointmentDao.getAllUpcomingAppointmentsByClientId(
              userId,
              limit
            );
        }

        if (role == UserRole.THERAPIST) {
          appointmentList =
            await AppointmentDao.getAllUpcomingAppointmentsByTherapistId(
              userId,
              limit
            );
        }

        return res.sendSuccess(
          appointmentList,
          "Apppintments Received Successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllUpcomingAppointmentById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.user._id;
    const limit = Number(req.params.limit);

    if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
      try {
        let appointmentList: IAppointment[] = [];
        if (role == UserRole.CLIENT) {
          appointmentList = await AppointmentDao.getAllAppointmentsByClientId(
            userId,
            limit
          );
        }

        if (role == UserRole.THERAPIST) {
          appointmentList =
            await AppointmentDao.getAllAppointmentsByTherapistId(userId, limit);
        }

        return res.sendSuccess(
          appointmentList,
          "Apppintments Received Successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function deleteAppointment(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const role = req.user.role;
      const appointmentId = req.body.appointmentId;

      if (
        role == UserRole.THERAPIST ||
        role == UserRole.CLIENT ||
        role == UserRole.SUPER_ADMIN ||
        role == UserRole.SUB_ADMIN
      ) {
        
        if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
          return res.sendError("Invalid appointment Id");
        }

        let appointment = await AppointmentDao.getAppointmentById(
          appointmentId
        );

        if (!appointment || appointment == null) {
          return res.sendError(
            "No appointment found for the provided appointmentId."
          );
        }

        if (appointment.status == AppointmentStatus.COMPLETED) {
          return res.sendError(
            "Not possible to delete completed appointments."
          );
        }

        if (role != UserRole.SUPER_ADMIN && role != UserRole.SUB_ADMIN) {
          if (
            appointment.createdBy.toString() !== userId.toString() &&
            appointment.therapistId.toString() !== userId.toString() &&
            appointment.clientId.toString() !== userId.toString()
          ) {
            return res.sendError(
              "You have no authorization delete this appointment."
            );
          }
        }

        const utcTime = moment.utc(appointment.start);
        const estTime = utcTime.tz('America/New_York');

        const client = await ClientDao.getUserById(appointment.clientId);

        if (role != UserRole.SUPER_ADMIN && role != UserRole.SUB_ADMIN) {
          if (
            client.premiumStatus != "active" &&
            (client.subscriptionId == null ||
              client.subscriptionStatus != "active") &&
            client.testSubscriptionStatus != "active"
          ) {
            return res.sendError(
              "Sorry! Unable to delete appointment because Client doesn't have an active subscription."
            );
          }
        }

        const therapist = await TherapistDao.getUserById(
          appointment.therapistId
        );

        let deletedAppointment: IAppointment = await AppointmentDao.deleteAppointment(
          appointmentId
        );

        if (userId.toString() == appointment.therapistId.toString()) {
          await EmailService.sendEventEmail(
            client,
            "Appointment is cancelled!",
            "Scheduled appointment at " + estTime.format('YYYY-MM-DD hh:mm A') + " is cancelled.",
            "Login to view more information.",
            ""
          );

          if (client?.primaryPhone) {
            await SMSService.sendEventSMS(
              "Scheduled appointment at " + estTime.format('YYYY-MM-DD hh:mm A') + " is cancelled.",
              client?.primaryPhone,
              "Appointment-ep 22"
            );
          }
        }

        if (userId.toString() == appointment.clientId.toString()) {
          await EmailService.sendEventEmail(
            therapist,
            "Appointment is cancelled!",
            "Scheduled appointment at " + estTime.format('YYYY-MM-DD hh:mm A') + " is cancelled.",
            "Login to view more information.",
            ""
          );

          if (therapist?.primaryPhone) {
            await SMSService.sendEventSMS(
              "Scheduled appointment at " + estTime.format('YYYY-MM-DD hh:mm A') + " is cancelled.",
              therapist?.primaryPhone,
              "Appointment-ep 23"
            );
          }
        }

        return res.sendSuccess(
          deletedAppointment,
          "Apppintment Deleted Successfully."
        );
        
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }


  export async function deleteAppointmentPublic(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const appointmentId = req.body.appointmentId;

    try {
      if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
        return res.sendError("Invalid appointment Id");
      }

      let appointment = await AppointmentDao.getAppointmentById(
        appointmentId
      );

      if (!appointment || appointment == null) {
        return res.sendError(
          "No appointment found for the provided appointmentId."
        );
      }

      if (appointment.status == AppointmentStatus.COMPLETED) {
        return res.sendError(
          "Not possible to delete completed appointments."
        );
      }

      const client = await ClientDao.getUserById(appointment.clientId);

      if (
        client.premiumStatus != "active" &&
        (client.subscriptionId == null ||
          client.subscriptionStatus != "active") &&
        client.testSubscriptionStatus != "active"
      ) {
        return res.sendError(
          "Sorry! Unable to delete appointment because Client doesn't have an active subscription."
        );
      }

      let deletedAppointment: IAppointment = await AppointmentDao.deleteAppointment(
        appointmentId
      );

      return res.sendSuccess(
        deletedAppointment,
        "Apppintment Deleted Successfully."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteAppointmentAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const appointmentId = req.params.id;

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
          return res.sendError("Invalid appointment Id");
        }

        let appointment = await AppointmentDao.getAppointmentById(
          appointmentId
        );

        if (!appointment || appointment == null) {
          return res.sendError(
            "No appointment found for the provided appointmentId."
          );
        }

        if (appointment.status == AppointmentStatus.COMPLETED) {
          return res.sendError(
            "Not possible to delete completed appointments."
          );
        }

        const utcTime = new Date(appointment.start);

        const easternTime = utcTime.toLocaleString('en-US', { timeZone: 'America/New_York' });

        let deletedAppointment = await AppointmentDao.deleteAppointment(
          appointmentId
        );

        const client = await ClientDao.getUserById(appointment.clientId);

        const therapist = await TherapistDao.getUserById(
          appointment.therapistId
        );

        await EmailService.sendEventEmail(
          client,
          "Appointment is cancelled!",
          "Scheduled appointment at " + easternTime + " is cancelled.",
          "Login to view more information.",
          ""
        );

        if (client?.primaryPhone) {
          await SMSService.sendEventSMS(
            "Scheduled appointment at " + easternTime + " is cancelled.",
            client?.primaryPhone,
            "Appointment-ep 24"
          );
        }

        await EmailService.sendEventEmail(
          therapist,
          "Appointment is cancelled!",
          "Scheduled appointment at " + easternTime + " is cancelled.",
          "Login to view more information.",
          ""
        );

        if (therapist?.primaryPhone) {
          await SMSService.sendEventSMS(
            "Scheduled appointment at " + easternTime + " is cancelled.",
            therapist?.primaryPhone,
            "Appointment-ep 25"
          );
        }

        return res.sendSuccess(
          deletedAppointment,
          "Apppintment Deleted Successfully."
        );
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }


  export async function viewAppointmentsByTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const therapistId = req.user._id;
    const limit = Number(req.params.limit);

    if (role == UserRole.THERAPIST) {
      try {
        let appointmentList =
          await AppointmentDao.getAllAppointmentsByTherapistId(
            therapistId,
            limit
          );

        return res.sendSuccess(appointmentList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function viewAppointmentsByTherapistAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const therapistId = Types.ObjectId(req.params.therapist);
    const limit = Number(req.params.limit);

    if (role == UserRole.SUPER_ADMIN || role == UserRole.SUB_ADMIN) {
      try {
        let appointmentList =
          await AppointmentDao.getAllAppointmentsByTherapistId(
            therapistId,
            limit
          );

        return res.sendSuccess(appointmentList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export function viewAppointmentsByUserIdParamValidation(): ValidationChain[] {
    return [
      param("limit").exists().withMessage("Limit is required").isInt({ gt: 0 }),
      param("offset")
        .exists()
        .withMessage("Offset is required")
        .isInt({ gt: 0 }),
    ];
  }

  export async function viewAppointmentsByUserId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      let limit = 0;
      let offset = 0;
      if (
        req.params.limit == null ||
        parseInt(req.params.limit) == null ||
        Number.isNaN(parseInt(req.params.limit)) ||
        parseInt(req.params.offset) == null ||
        Number.isNaN(parseInt(req.params.offset)) ||
        req.params.offset == null
      ) {
        return res.sendError("params should be send as numbers");
      }
      limit = parseInt(req.params.limit);
      offset = parseInt(req.params.offset);
      const role = req.user.role;
      const userId = req.user._id;

      let appointmentList = await AppointmentDao.getAllAppointmentsByUserId(
        userId,
        limit,
        offset,
        role
      );

      if (appointmentList === undefined || appointmentList == null) {
        return res.sendError("Server error occured. Please reach to support.");
      }

      return res.sendSuccess(
        appointmentList,
        "Appointments Received Successfully."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function viewAllAppointmentsByTherapistId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.params.id;

    try {
      let appointmentList = await AppointmentDao.viewAllAppointmentsByTherapistId(userId);

      return res.sendSuccess(
        appointmentList,
        "Appointments Received Successfully."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function viewAllAppointmentsByTherapistIdAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.params.id;
    try {
      let appointmentList =
        await AppointmentDao.viewAllAppointmentsByTherapistId(userId);
      return res.sendSuccess(
        appointmentList,
        "Appointments Received Successfully."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllStatisticsOfAppointmentStat(_id: any, userRole: any, filterValue: any) {
    const userId = _id;
    const role = userRole;
    if (
      role == UserRole.THERAPIST ||
      role == UserRole.CLIENT
    ) {
      try {
        const user = await UserDao.getUserById(userId);

        if (user) {
          const stat =
            await AppointmentDao.getAllPendingAndCompletedAppointmentsByUserId(
              user,
              filterValue
            );

          if (stat.length === 0) {
            const error3 = "Something went wrong! Could not load appointment statistics"
            return error3;
          }

          return stat;
        } else {
          const error3 = "User not found"
          return error3;
        }
      } catch (error) {
        return error;
      }
    } else {
      const error3 = "Invalid user role."
      return error3;
    }
  }

  export async function getAllStatisticsOfAppointmentStatSessions(req: Request, res: Response, next: NextFunction) {
    const role = req.user.role;
    const userId = req.user._id;
    const filterValue = req.body.filterValue;
    if (
      role == UserRole.THERAPIST ||
      role == UserRole.CLIENT
    ) {
      try {
        const user = await UserDao.getUserById(userId);

        if (user) {
          const stat =
            await AppointmentDao.getAllPendingAndCompletedAppointmentsByUserId(
              user,
              filterValue
            );

          return res.sendSuccess(
            stat,
            "Sessins stats."
          );
        } else {
          return res.sendError("Invalid user role.");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllStatisticsOfAppointments(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const userId = req.params.id;

    if (
      req.user.role == UserRole.THERAPIST ||
      req.user.role == UserRole.CLIENT
    ) {
      try {
        const user = await UserDao.getUserById(userId);

        if (user) {
          const stat =
            await AppointmentDao.getAllPendingAndCompletedAppointmentsByTherapistId(
              user
            );

          if (stat.length === 0) {
            return res.sendError(
              "Something went wrong! Could not load appointment statistics"
            );
          }

          return res.sendSuccess(stat, "Success");
        } else {
          return res.sendError("User not found");
        }
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getAllAppointmentsAndDetailsByUserId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]);
    }

    const datesOfWeek = req.body;
    const appointmentId = datesOfWeek.appointmentId;
    let sessionTimeOfWeek = 0;

    try {
      let appointmentsBySelectedWeek =
        await AppointmentDao.getAllAppointmentsBySelectedWeek2(datesOfWeek);

      if (appointmentId) {
        appointmentsBySelectedWeek = appointmentsBySelectedWeek.filter(
          (appointment: IAppointment) =>
            appointmentId.toString() !== appointment._id.toString()
        );

        if (appointmentsBySelectedWeek.length) {
          for (const session of appointmentsBySelectedWeek) {
            const startTime = moment(session.start, "HH:mm:ss a");
            const endTime = moment(session.end, "HH:mm:ss a");
            const duration = moment.duration(endTime.diff(startTime));
            const minutes = parseInt(duration.asMinutes().toString());

            sessionTimeOfWeek = sessionTimeOfWeek + minutes;
          }
        }
      }

      const sessionDetails = {
        selectedDate: datesOfWeek.selectedDate,
        allSessionOfWeek: appointmentsBySelectedWeek,
        sessionTimeOfWeek: sessionTimeOfWeek,
        allSession: appointmentsBySelectedWeek.length,
      };

      if (
        appointmentsBySelectedWeek === undefined ||
        appointmentsBySelectedWeek == null
      ) {
        return res.sendError("Server error occured. Please reach to support.");
      }

      return res.sendSuccess(
        sessionDetails,
        "Appointments Received Successfully."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllAppointmentsAndDetailsByUserIdAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]);
    }

    const datesOfWeek = req.body;
    const appointmentId = datesOfWeek.appointmentId;
    let sessionTimeOfWeek = 0;

    try {
      let appointmentsBySelectedWeek =
        await AppointmentDao.getAllAppointmentsBySelectedWeek(datesOfWeek);

      if (appointmentId) {
        appointmentsBySelectedWeek = appointmentsBySelectedWeek.filter(
          (appointment: IAppointment) =>
            appointmentId.toString() !== appointment._id.toString()
        );
      }

      if (appointmentsBySelectedWeek.length) {
        for (const session of appointmentsBySelectedWeek) {
          const startTime = moment(session.start, "HH:mm:ss a");
          const endTime = moment(session.end, "HH:mm:ss a");
          const duration = moment.duration(endTime.diff(startTime));
          const minutes = parseInt(duration.asMinutes().toString());

          sessionTimeOfWeek = sessionTimeOfWeek + minutes;
        }
      }

      const sessionDetails = {
        selectedDate: datesOfWeek.selectedDate,
        allSessionOfWeek: appointmentsBySelectedWeek,
        sessionTimeOfWeek: sessionTimeOfWeek,
        allSession: appointmentsBySelectedWeek.length,
      };

      if (
        appointmentsBySelectedWeek === undefined ||
        appointmentsBySelectedWeek == null
      ) {
        return res.sendError("Server error occured. Please reach to support.");
      }

      return res.sendSuccess(
        sessionDetails,
        "Appointments Received Successfully."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendReminderEmailsEvery15Minutes() {
    AppLogger.info(`.:: Send Reminder Email Every 15 Minutes ::.`);

    const systemAdmin = (await AdminDao.getAllAdmins())[0];
    let appointmentsIn15Minutes =
      await AppointmentDao.getAllAppointmentsInNext15Minutes();

    appointmentsIn15Minutes.map(async function (a: any) {
      if (a.clientId?.primaryPhone) {
        await SMSService.sendEventSMS(
          `This is a reminder that your therapy session is in 15 minutes. ${a.therapistId.firstname} ${a.therapistId.lastname}`,
          a.clientId?.primaryPhone,
          "Appointment-ep 26"
        );
      }

      let mail1Sent = await EmailService.send15MinuteAppointmentReminder(
        "You have a scheduled appointment in next 15 minutes.",
        a.clientId.email,
        a.clientId.firstname,
        a.clientId.firstname + " " + a.clientId.lastname
      );

      if (mail1Sent) {
        const notificationDetails: DNotification = {
          senderId: systemAdmin._id,
          receiverId: a.clientId._id,
          event: NotificationEvent.APPOINTMENT_REMINDER_IN_15_MINUTES,
          link: process.env.APP_URL + "/appointments",
          content: "You have a scheduled appointment in next 15 minutes.",
          variant: "info",
          readStatus: false,
        };

        await NotificationDao.createNotification(notificationDetails);

        AppLogger.info(
          `.:: Sent reminder email to ` + a.clientId.email + ` ::.`
        );
      }

      let mail2Sent = await EmailService.send15MinuteAppointmentReminder(
        "You have a scheduled appointment in next 15 minutes.",
        a.therapistId.email,
        a.therapistId.firstname,
        a.clientId.firstname + " " + a.clientId.lastname
      );

      if (mail2Sent) {
        const notificationDetails: DNotification = {
          senderId: systemAdmin._id,
          receiverId: a.therapistId._id,
          event: NotificationEvent.APPOINTMENT_REMINDER_IN_15_MINUTES,
          link: process.env.APP_URL + "/appointments",
          content: "You have a scheduled appointment in next 15 minutes.",
          variant: "info",
          readStatus: false,
        };

        await NotificationDao.createNotification(notificationDetails);

        AppLogger.info(
          `.:: Sent reminder email to ` + a.therapistId.email + ` ::.`,
          `.:: Sent reminder email to ` + a.clientId.email + ` ::.`
        );
      }
    });
  }

  export async function sendReminderEmailsEvery30Minutes() {
    AppLogger.info(`.:: Send Reminder Email Every 30 Minutes ::.`);
    // const userId = req.user._id;
    const systemAdmin = (await AdminDao.getAllAdmins())[0];
    let appointmentsIn30Minutes =
      await AppointmentDao.getAllAppointmentsInNext30Minutes();
    // const userData = await UserDao.getUser(userId);
    appointmentsIn30Minutes.map(async function (a: any) {
      if (
        a.therapistId?.primaryPhone &&
        a.therapistId.reminderType?.text !== false &&
        a.therapistId.reminderTime?.min30 == true
      ) {
        await SMSService.sendEventSMS(
          `You have a scheduled appointment in next 30 minutes. ${a.clientId.firstname} ${a.clientId.lastname}`,
          a.therapistId?.primaryPhone,
          "Appointment-ep 27"
        );
      }
      if (
        a.clientId.reminderType?.email == true &&
        a.clientId.reminderTime?.min30 == true
      ) {
        let mail1Sent = await EmailService.send30MinuteAppointmentReminder(
          "You have a scheduled appointment in next 30 minutes.",
          a.clientId.email,
          a.clientId.firstname,
          a.therapistId.firstname + " " + a.therapistId.lastname
        );

        if (mail1Sent) {
          const notificationDetails: DNotification = {
            senderId: systemAdmin._id,
            receiverId: a.clientId._id,
            event: NotificationEvent.APPOINTMENT_REMINDER_IN_30_MINUTES,
            link: process.env.APP_URL + "/appointments",
            content: "You have a scheduled appointment in next 30 minutes.",
            variant: "info",
            readStatus: false,
          };

          await NotificationDao.createNotification(notificationDetails);

          AppLogger.info(
            `.:: Sent reminder email to ` + a.createdBy.email + ` ::.`
          );
        }
      }

      if (
        a.clientId?.primaryPhone &&
        a.clientId.reminderType?.text !== false &&
        a.clientId.reminderTime?.min30 == true
      ) {
        await SMSService.sendEventSMS(
          `You have a scheduled appointment in next 30 minutes. ${a.therapistId.firstname} ${a.therapistId.lastname}`,
          a.clientId?.primaryPhone,
          "Appointment-ep 28"
        );
      }

      if (
        a.therapistId.reminderType?.email == true &&
        a.therapistId.reminderTime?.min30 == true
      ) {
        let mail2Sent = await EmailService.send30MinuteAppointmentReminder(
          "You have a scheduled appointment in next 30 minutes.",
          a.therapistId.email,
          a.therapistId.firstname,
          a.clientId.firstname + " " + a.clientId.lastname
        );

        if (mail2Sent) {
          const notificationDetails: DNotification = {
            senderId: systemAdmin._id,
            receiverId: a.therapistId._id,
            event: NotificationEvent.APPOINTMENT_REMINDER_IN_15_MINUTES,
            link: process.env.APP_URL + "/appointments",
            content: "You have a scheduled appointment in next 30 minutes.",
            variant: "info",
            readStatus: false,
          };

          await NotificationDao.createNotification(notificationDetails);

          AppLogger.info(
            `.:: Sent reminder email to ` + a.therapistId.email + ` ::.`
          );
        }
      }
    });
  }

  // coppy of 30 mins
  export async function notify30min(req: Request, res: Response, next: NextFunction) {
    AppLogger.info(`.:: Send Reminder Email Every 30 Minutes ::.`);
    // const userId = req.user._id;
    const systemAdmin = (await AdminDao.getAllAdmins())[0];
    let appointmentsIn30Minutes =
      await AppointmentDao.getAllAppointmentsInNext30Minutes();
    // const userData = await UserDao.getUser(userId);
    appointmentsIn30Minutes.map(async function (a: any) {
      if (
        a.therapistId?.primaryPhone &&
        a.therapistId?.reminderType?.text !== false &&
        a.therapistId?.reminderTime?.min30 == true
      ) {
        const bs = await SMSService.sendEventSMS(
          `You have a scheduled appointment in next 30 minutes. ${a.clientId.firstname} ${a.clientId.lastname}`,
          a.therapistId?.primaryPhone,
          "Appointment-ep 29"
        );
      }
      if (
        a.clientId.reminderType?.email == true &&
        a.clientId.reminderTime?.min30 == true
      ) {
        let mail1Sent = await EmailService.send30MinuteAppointmentReminder(
          "You have a scheduled appointment in next 30 minutes.",
          a.clientId.email,
          a.clientId.firstname,
          a.therapistId.firstname + " " + a.therapistId.lastname
        );

        if (mail1Sent) {
          const notificationDetails: DNotification = {
            senderId: systemAdmin._id,
            receiverId: a.clientId._id,
            event: NotificationEvent.APPOINTMENT_REMINDER_IN_30_MINUTES,
            link: process.env.APP_URL + "/appointments",
            content: "You have a scheduled appointment in next 30 minutes.",
            variant: "info",
            readStatus: false,
          };

          await NotificationDao.createNotification(notificationDetails);

          AppLogger.info(
            `.:: Sent reminder email to ` + a.createdBy.email + ` ::.`
          );
        }
      }

      if (
        a.clientId?.primaryPhone &&
        a.clientId.reminderType?.text !== false &&
        a.clientId.reminderTime?.min30 == true
      ) {
        const abc = await SMSService.sendEventSMS(
          `You have a scheduled appointment in next 30 minutes. ${a.therapistId.firstname} ${a.therapistId.lastname}`,
          a.clientId?.primaryPhone,
          "Appointment-ep 30"
        );
      }

      if (
        a.therapistId.reminderType?.email == true &&
        a.therapistId.reminderTime?.min30 == true
      ) {
        let mail2Sent = await EmailService.send30MinuteAppointmentReminder(
          "You have a scheduled appointment in next 30 minutes.",
          a.therapistId.email,
          a.therapistId.firstname,
          a.clientId.firstname + " " + a.clientId.lastname
        );

        if (mail2Sent) {
          const notificationDetails: DNotification = {
            senderId: systemAdmin._id,
            receiverId: a.therapistId._id,
            event: NotificationEvent.APPOINTMENT_REMINDER_IN_15_MINUTES,
            link: process.env.APP_URL + "/appointments",
            content: "You have a scheduled appointment in next 30 minutes.",
            variant: "info",
            readStatus: false,
          };

          await NotificationDao.createNotification(notificationDetails);

          AppLogger.info(
            `.:: Sent reminder email to ` + a.therapistId.email + ` ::.`
          );
        }
      }
    });
  }

  export async function sendReminderEmailsEveryHour() {
    AppLogger.info(`.:: Send Reminder Email Every Hour ::.`);

    const systemAdmin = (await AdminDao.getAllAdmins())[0];
    let appointmentsIn1Hour =
      await AppointmentDao.getAllAppointmentsInNextHour();
    appointmentsIn1Hour.map(async function (a: any) {
      if (
        a.therapistId?.primaryPhone &&
        a.therapistId.reminderType?.text !== false &&
        a.therapistId.reminderTime?.hour1 == true
      ) {
        await SMSService.sendEventSMS(
          `You have a scheduled appointment in the next hour. ${a.clientId.firstname} ${a.clientId.lastname}`,
          a.therapistId?.primaryPhone,
          "Appointment-ep 31"
        );
      }

      if (
        a.clientId.reminderType?.email == true &&
        a.clientId.reminderTime?.hour1 == true
      ) {
        let mail1Sent = await EmailService.sendNextHourAppointmentReminder(
          "You have a scheduled appointment in the next hour.",
          a.clientId.email,
          a.clientId.firstname,
          a.therapistId.firstname + " " + a.therapistId.lastname
        );

        if (mail1Sent) {
          const notificationDetails: DNotification = {
            senderId: systemAdmin._id,
            receiverId: a.clientId._id,
            event: NotificationEvent.APPOINTMENT_REMINDER_IN_1_HOUR,
            link: process.env.APP_URL + "/appointments",
            content: "You have a scheduled appointment in the next hour.",
            variant: "info",
            readStatus: false,
          };

          await NotificationDao.createNotification(notificationDetails);

          AppLogger.info(
            `.:: Sent reminder email to ` + a.createdBy.email + ` ::.`
          );
        }
      }

      if (
        a.clientId?.primaryPhone &&
        a.clientId.reminderType?.text !== false &&
        a.clientId.reminderTime?.hour1 == true
      ) {
        await SMSService.sendEventSMS(
          `You have a scheduled appointment in the next hour. ${a.therapistId.firstname} ${a.therapistId.lastname}`,
          a.clientId?.primaryPhone,
          "Appointment-ep 32"
        );
      }

      if (
        a.therapistId.reminderType?.email == true &&
        a.therapistId.reminderTime?.hour1 == true
      ) {
        let mail2Sent = await EmailService.sendNextHourAppointmentReminder(
          "You have a scheduled appointment in next the hour.",
          a.therapistId.email,
          a.therapistId.firstname,
          a.clientId.firstname + " " + a.clientId.lastname
        );

        if (mail2Sent) {
          const notificationDetails: DNotification = {
            senderId: systemAdmin._id,
            receiverId: a.therapistId._id,
            event: NotificationEvent.APPOINTMENT_REMINDER_IN_1_HOUR,
            link: process.env.APP_URL + "/appointments",
            content: "You have a scheduled appointment in the next hour.",
            variant: "info",
            readStatus: false,
          };

          await NotificationDao.createNotification(notificationDetails);

          AppLogger.info(
            `.:: Sent reminder email to ` + a.therapistId.email + ` ::.`
          );
        }
      }
    });
  }

  export async function sendReminderEmailsEveryDay() {
    AppLogger.info(`.:: Send Reminder Email Every Day ::.`);

    const systemAdmin = (await AdminDao.getAllAdmins())[0];
    let appointmentsInNextDay =
      await AppointmentDao.getAllAppointmentsInNextDay();

    let sentEmailClientArray = Array();
    let sentEmailTherapistArray = Array();
    appointmentsInNextDay.map(async function (a: any) {

      if (sentEmailClientArray.indexOf(a.clientId.email) <= -1) {
        sentEmailClientArray.push(a.clientId.email);
        const utcTime = moment.utc(a.start);
        const estTime = utcTime.tz('America/New_York');
        if (
          a.clientId?.primaryPhone &&
          a.clientId?.reminderType?.text !== false &&
          a.clientId?.reminderTime?.day == true &&
          a.clientId?.smsStop !== true
        ) {
          const newTime = moment(estTime, 'YYYY-MM-DD H:mm A')
            .subtract(12, 'hours')
            .format('h:mm A');
            const prefix = a.therapistId.gender === 'Male' ? 'Mr.' : 'Mrs.';
            const smsSend = await SMSService.sendEventSMS(
              `Hey ${a.clientId.firstname}, this is a friendly reminder about your therapy session with ${prefix} ${a.therapistId.lastname} tomorrow at ${estTime.format('h:mm A')}. Please reply with "1" if that time still works for you or "2" if you'd like to reschedule.`,
              a.clientId?.primaryPhone,
              "Appointment-ep 33"
            );
          if (smsSend == true) {
            let appointment = await AppointmentDao.updateAppointmentData(
              a._id,
              AppointmentSMSStatus.BEFORE24SMS
            );
          }
        }
        if (
          a.clientId.reminderType?.email == true &&
          a.clientId.reminderTime?.day == true
        ) {
          let mail1Sent = await EmailService.sendNextDayAppointmentsReminder(
            "You have upcoming appointments are scheduled on tomorrow.",
            a.clientId.email,
            a.clientId.firstname,
            a.therapistId.firstname + " " + a.therapistId.lastname
          );

          if (mail1Sent) {
            const notificationDetails: DNotification = {
              senderId: systemAdmin._id,
              receiverId: a.createdBy._id,
              event: NotificationEvent.APPOINTMENT_REMINDER_NEXT_DAY,
              link: process.env.APP_URL + "/appointments",
              content:
                "You have upcoming appointments are scheduled on tomorrow.",
              variant: "info",
              readStatus: false,
            };

            await NotificationDao.createNotification(notificationDetails);

            AppLogger.info(
              `.:: Sent reminder email to ` + a.createdBy.email + ` ::.`
            );
          }
        }
      }

      if (sentEmailTherapistArray.indexOf(a.therapistId.email) <= -1) {
        sentEmailTherapistArray.push(a.therapistId.email);
        if (
          a.therapistId?.primaryPhone &&
          a.therapistId.reminderType?.text !== false &&
          a.therapistId.reminderTime?.day == true
        ) {
          await SMSService.sendEventSMS(
            `You have upcoming appointments are scheduled on tomorrow. ${a.clientId.firstname} ${a.clientId.lastname}`,
            a.therapistId?.primaryPhone,
            "Appointment-ep 34"
          );
        }
        if (
          a.therapistId.reminderType?.email == true &&
          a.therapistId.reminderTime?.day == true
        ) {
          let mail2Sent = await EmailService.sendNextDayAppointmentsReminder(
            "You have upcoming appointments are scheduled on tomorrow.",
            a.therapistId.email,
            a.therapistId.firstname,
            a.clientId.firstname + " " + a.clientId.lastname
          );

          if (mail2Sent) {
            const notificationDetails: DNotification = {
              senderId: systemAdmin._id,
              receiverId: a.therapistId._id,
              event: NotificationEvent.APPOINTMENT_REMINDER_NEXT_DAY,
              link: process.env.APP_URL + "/appointments",
              content:
                "You have upcoming appointments are scheduled on tomorrow.",
              variant: "info",
              readStatus: false,
            };

            await NotificationDao.createNotification(notificationDetails);

            AppLogger.info(
              `.:: Sent reminder email to ` + a.therapistId.email + ` ::.`
            );
          }
        }
      }
    });
  }


  export async function sendReminderSMSsEveryNext18Hours() {
    let appointmentsInNext18Hours =
      await AppointmentDao.getAllAppointmentsInNext18Hours();

    let sentEmailClientArray = Array();

    if (appointmentsInNext18Hours) {
      await Promise.all(appointmentsInNext18Hours.map(async (a: any) => {
        if (sentEmailClientArray.indexOf(a.clientId.email) <= -1) {
          sentEmailClientArray.push(a.clientId.email);
  
          if (
            a.clientId?.primaryPhone &&
            a.clientId.reminderType?.text !== false &&
            a.clientId.reminderTime?.day == true &&
            a.clientId.smsStop !== true
          ) {
            const utcTime = moment.utc(a.start);
            const estTime = utcTime.tz('America/New_York');
            const newTime = moment(estTime, 'YYYY-MM-DD H:mm A')
            .subtract(12, 'hours')
            .format('h:mm A');
            // Sending SMS
            const prefix = a.therapistId.gender === 'Male' ? 'Mr.' : 'Mrs.';
            const smsSend = SMSService.sendEventSMS(
              `Hey ${a.clientId.firstname}, this is a friendly reminder about your therapy session with ${prefix} ${a.therapistId.lastname} tomorrow at ${estTime.format('h:mm A')}. Please reply with "1" if that time still works for you or "2" if you'd like to reschedule.`,
              a.clientId?.primaryPhone,
              "Appointment-ep 35"
            );
          }
        }
        AppLogger.info('.:: Send Reminder SMS Every 30MIN ::.' + a.clientId._id)
      }));
    }
  }


  export async function sendReminderSMSsEveryNext48Hours() {
    let pendingNotes =
      await AdminStatisticsDao.getPendingDiagnosisNoteNotesByAdmin();
    let sentEmailClientArray: string[] = [];
    if (pendingNotes) {
      await Promise.all(pendingNotes.map(async (a: any) => {
        try {
          if (sentEmailClientArray.indexOf(a.clientId.email) === -1) {
            console.log("a.meetingId", a.meeting)
            const utcTime = moment.utc(a.meeting.createdAt);
            const estTime = utcTime.tz('America/New_York');
            sentEmailClientArray.push(a.clientId.email);
            if (a.therapistId?.primaryPhone && a.therapistId.reminderType?.text !== false && a.clinicalReminderSMS !== true) {
              const smsSend = await SMSService.sendEventSMS(
                `Lavni Reminder: Your clinical note for ${a.clientId.firstname.charAt(0)}. ${a.clientId.lastname} is not complete.  
                Meeting started at: ${estTime.format('YYYY-MM-DD hh:mm A')}`,
                a.therapistId?.primaryPhone,
                "Appointment-ep 36"
              );
              if (smsSend) {
                let updateClinicalreminder = await AdminStatisticsDao.updateDiagnosisNoteNotesSMS(
                  a?._id, {
                  clinicalReminderSMS: true,
                }
                );
              }
            }
            if (a.therapistId.reminderType?.email == true && a.clinicalReminderEmail !== true) {
              const emailSend = await EmailService.sendEventEmailNotes(
                a.therapistId,
                "Lavni Reminder!",
                `Your clinical note for ${a.clientId.firstname.charAt(0)}. ${a.clientId.lastname} is not complete. Meeting started at: ${estTime.format('YYYY-MM-DD hh:mm A')}`
              );
              if (emailSend) {
                let updateClinicalreminder = await AdminStatisticsDao.updateDiagnosisNoteNotesSMS(
                  a?._id, {
                  clinicalReminderEmail: true,
                }
                );
              }
            }
          }
        } catch (error) {
          console.error("Error occurred while sending reminder:", error);
        }
        AppLogger.info('.:: Send Reminder SMS Every 15MIN  clinical note ::.' + a.clientId._id)
      }));
    }
  }

  export async function updateWeeklyAppointments(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const role = req.user.role;
    const userId = req.user._id;
    const therapistId = req.body.therapistId;
    const clientId = req.body.clientId;
    const start = new Date(req.body.start);
    const end = new Date(req.body.end);
    const reminders = req.body.reminders;
    const title = req.body.title;
    const appointmentId = req.body.appointmentId;
    const color = req.body.color;
    const groupId = req.body.groupId;
    const repeatInfo = req.body.repeatInfo;

    const sessionStart = moment(start);
    const sessionEnd = moment(end);

    const sessionDuration = moment
      .duration(sessionEnd.diff(sessionStart))
      .asHours();

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors);
    }

    if (!mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid therapist Id");
    }

    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.sendError("Invalid client Id");
    }

    if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
      return res.sendError("Invalid appointment Id");
    }

    try {
      let weeklyAppointments =
        await AppointmentDao.getWeeklyAppointmentsByGroupId(groupId);

      if (!weeklyAppointments) {
        return res.sendError(
          "Could not find a list appointments for the provided groupId."
        );
      }

      let therapist = await TherapistDao.getUserById(therapistId);

      if (!therapist || therapist == null) {
        return res.sendError(
          "No existing therapist for the provided therapist Id."
        );
      }

      let client = await ClientDao.getUserById(clientId);

      if (!client || client == null) {
        return res.sendError("No existing client for the provided client Id.");
      }

      for (let appointment of weeklyAppointments) {
        if (role != UserRole.SUPER_ADMIN && role != UserRole.SUB_ADMIN) {
          if (
            appointment.createdBy.toString() !== userId.toString() &&
            appointment.therapistId.toString() !== userId.toString() &&
            appointment.clientId.toString() !== userId.toString()
          ) {
            return res.sendError(
              "You are not allowed to edit this appointment."
            );
          }
        }
      }

      for (let appointment of weeklyAppointments) {
        if (
          appointment.status == AppointmentStatus.COMPLETED ||
          appointment.status == AppointmentStatus.REJECTED
        ) {
          return res.sendError(
            "Atleast one or more appointments are either rejected or completed. Please try editing one by one."
          );
        }
      }

      let isFriend = await FriendRequestDao.checkIfUserIsFriend(
        client._id,
        therapist._id
      );

      if (!isFriend) {
        return res.sendError(
          "Sorry! You haven't connected with this Therapist yet."
        );
      }

      if (
        client.premiumStatus != "active" &&
        (client.subscriptionId == null ||
          client.subscriptionStatus != "active") &&
        client.testSubscriptionStatus != "active"
      ) {
        return res.sendError(
          "Sorry! This client doesn't have an active subscription."
        );
      }

      if (weeklyAppointments?.length > 0) {
        const beginsAt = moment(start);
        const endsAt = moment(end);

        const appointmentStartTime = moment(start).format("YYYY-MM-DD HH:mm");
        const appointmentEndTime = moment(end).format("YYYY-MM-DD HH:mm");
        const startTime = moment(start).format("H:mm A");
        const endTime = moment(end).format("H:mm A");

        const mST = moment(startTime, "HH:mm").minute();

        if (mST != 0 && mST != 30) {
          return res.sendError("Please select valid start time.");
        }

        const datesOfWeek = Util.calculateWeekNumberAndDates(
          appointmentStartTime,
          client._id!,
          therapist._id!
        );
        let sessionTimeOfWeek = 0;

        let appointmentsBySelectedWeek =
          await AppointmentDao.getAllAppointmentsBySelectedWeek(datesOfWeek);

        if (groupId) {
          appointmentsBySelectedWeek = appointmentsBySelectedWeek.filter(
            (appointment: IAppointment) =>
              groupId.toString() !== appointment.groupId.toString()
          );
        }

        if (appointmentsBySelectedWeek.length) {
          for (const session of appointmentsBySelectedWeek) {
            const startTime = moment(session.start, "HH:mm:ss a");
            const endTime = moment(session.end, "HH:mm:ss a");
            const duration = moment.duration(endTime.diff(startTime));
            const minutes = parseInt(duration.asMinutes().toString());

            sessionTimeOfWeek = sessionTimeOfWeek + minutes;
          }
        }

        const sessionDetails = {
          selectedDate: datesOfWeek.selectedDate,
          allSessionOfWeek: appointmentsBySelectedWeek,
          sessionTimeOfWeek: sessionTimeOfWeek,
          allSession: appointmentsBySelectedWeek.length,
        };

        if (
          appointmentsBySelectedWeek === undefined ||
          appointmentsBySelectedWeek == null
        ) {
          return res.sendError(
            "Server error occured. Please reach to support."
          );
        }

        if (Util.skipCheckingForPremiumUsers(client)) {
          const meetingDuration = Util.getMeetingTimeDuration(
            startTime,
            endTime
          );

          if (
            sessionDetails.sessionTimeOfWeek != 0 &&
            sessionDetails.sessionTimeOfWeek != 30 &&
            sessionDetails.sessionTimeOfWeek != 60
          ) {
            return res.sendError(
              "Sorry! Client's weekly session time has exceeded."
            );
          }

          if (sessionDetails.sessionTimeOfWeek != 0) {
            if (
              sessionDetails.sessionTimeOfWeek == 30 &&
              meetingDuration == 60
            ) {
              return res.sendError(
                "Sorry! Client's weekly session time has exceeded."
              );
            } else if (sessionDetails.sessionTimeOfWeek == 60) {
              return res.sendError(
                "Sorry! Client's weekly session time has exceeded."
              );
            }
          }
        }

        if (!startTime) {
          return res.sendError("Please select valid start time.");
        }

        const workingDaysOfTherapist: string[] = [];

        therapist.workingHours?.map((session: any) => {
          if (!workingDaysOfTherapist.includes(session.day))
            workingDaysOfTherapist.push(session.day);
        });

        if (
          !moment(new Date()).isBefore(
            moment(
              new Date(appointmentStartTime).setHours(
                parseInt(startTime.split(":")[0]),
                parseInt(startTime.split(":")[1]),
                0,
                0
              )
            )
          )
        ) {
          return res.sendError(
            "Sorry! You can't create appointment in a past date!"
          );
        }

        const timeDifferenceInHours = moment
          .duration(endsAt.diff(beginsAt))
          .asHours();

        if (timeDifferenceInHours < 0.5 && timeDifferenceInHours > 1) {
          return res.sendError(
            "You can only create one hour sessions or 30 minute sessions!"
          );
        }

        const week01Start = moment(appointmentStartTime);
        const week01End = moment(appointmentEndTime);

        const week02Start = moment(appointmentStartTime).add(7, "days");
        const week02End = moment(appointmentEndTime).add(7, "days");

        const week03Start = moment(appointmentStartTime).add(14, "days");
        const week03End = moment(appointmentEndTime).add(14, "days");

        const week04Start = moment(appointmentStartTime).add(21, "days");
        const week04End = moment(appointmentEndTime).add(21, "days");

        let appointementsInTheCurrentSlotsWeek01 =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            week01Start,
            week01End,
            sessionDuration,
            Types.ObjectId(therapistId)
          );

        let appointementsInTheCurrentSlotsWeek02 =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            week02Start,
            week02End,
            sessionDuration,
            Types.ObjectId(therapistId)
          );

        let appointementsInTheCurrentSlotsWeek03 =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            week03Start,
            week03End,
            sessionDuration,
            Types.ObjectId(therapistId)
          );

        let appointementsInTheCurrentSlotsWeek04 =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            week04Start,
            week04End,
            sessionDuration,
            Types.ObjectId(therapistId)
          );

        if (
          appointementsInTheCurrentSlotsWeek01?.length > 0 ||
          appointementsInTheCurrentSlotsWeek02?.length > 0 ||
          appointementsInTheCurrentSlotsWeek03?.length > 0 ||
          appointementsInTheCurrentSlotsWeek04?.length > 0
        ) {
          let week01List = appointementsInTheCurrentSlotsWeek01.filter(
            (a: IAppointment) =>
              a.groupId.toString() != weeklyAppointments[0].groupId.toString()
          );
          let week02List = appointementsInTheCurrentSlotsWeek02.filter(
            (a: IAppointment) =>
              a.groupId.toString() != weeklyAppointments[0].groupId.toString()
          );
          let week03List = appointementsInTheCurrentSlotsWeek03.filter(
            (a: IAppointment) =>
              a.groupId.toString() != weeklyAppointments[0].groupId.toString()
          );
          let week04List = appointementsInTheCurrentSlotsWeek04.filter(
            (a: IAppointment) =>
              a.groupId.toString() != weeklyAppointments[0].groupId.toString()
          );

          let allAppointmentsWithinWeeks = week01List.concat(
            week02List,
            week03List,
            week04List
          );

          if (allAppointmentsWithinWeeks.length > 0) {
            return res.sendError(
              role == "CLIENT"
                ? "Therapist has already scheduled an appointment during one or more of the selected time slot/slots."
                : "You have already scheduled an appointment during one or more of selected time slot/slots."
            );
          }
        }

        let appointementsInTheCurrentSlotsOfClientWeek01 =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            week01Start,
            week01End,
            sessionDuration,
            Types.ObjectId(clientId)
          );
        let appointementsInTheCurrentSlotsOfClientWeek02 =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            week02Start,
            week02End,
            sessionDuration,
            Types.ObjectId(clientId)
          );
        let appointementsInTheCurrentSlotsOfClientWeek03 =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            week03Start,
            week03End,
            sessionDuration,
            Types.ObjectId(clientId)
          );
        let appointementsInTheCurrentSlotsOfClientWeek04 =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            week04Start,
            week04End,
            sessionDuration,
            Types.ObjectId(clientId)
          );

        if (
          appointementsInTheCurrentSlotsOfClientWeek01?.length > 0 ||
          appointementsInTheCurrentSlotsOfClientWeek02?.length > 0 ||
          appointementsInTheCurrentSlotsOfClientWeek03?.length > 0 ||
          appointementsInTheCurrentSlotsOfClientWeek04?.length > 0
        ) {
          let week01List = appointementsInTheCurrentSlotsOfClientWeek01.filter(
            (a: IAppointment) =>
              a.groupId.toString() != weeklyAppointments[0].groupId.toString()
          );
          let week02List = appointementsInTheCurrentSlotsOfClientWeek02.filter(
            (a: IAppointment) =>
              a.groupId.toString() != weeklyAppointments[0].groupId.toString()
          );
          let week03List = appointementsInTheCurrentSlotsOfClientWeek03.filter(
            (a: IAppointment) =>
              a.groupId.toString() != weeklyAppointments[0].groupId.toString()
          );
          let week04List = appointementsInTheCurrentSlotsOfClientWeek04.filter(
            (a: IAppointment) =>
              a.groupId.toString() != weeklyAppointments[0].groupId.toString()
          );

          let allAppointmentsWithinWeeks = week01List.concat(
            week02List,
            week03List,
            week04List
          );

          if (allAppointmentsWithinWeeks.length > 0) {
            return res.sendError(
              role == "CLIENT"
                ? "Therapist has already scheduled one or more appointments during one or more of the selected time slot/slots."
                : "You have already scheduled one or more appointments during one or more of the selected time slot/slots."
            );
          }
        }

        const sessionStartTime = new Date(
          weeklyAppointments[0].start
        ).getTime();
        const sessionEndTime = new Date(weeklyAppointments[0].end).getTime();

        if (role == UserRole.CLIENT || role == UserRole.THERAPIST) {
          const appointmentDetails: DAppointment = {
            therapistId: therapistId,
            clientId: clientId,
            start: start,
            end: end,
            title: title,
            reminders: reminders,
            typeOfMeeting: "VIDEO",
            color: color,
            groupId: groupId,
            status: AppointmentStatus.WAITING_FOR_APPROVAL,
            approvedStatus: ApprovalStatus.PENDING,
            createdBy: userId,
            repeatInfo: {
              repeatType: repeatInfo.repeatType,
              interval: repeatInfo.interval,
              repeatDays: {
                sunday: repeatInfo.repeatDays.sunday,
                monday: repeatInfo.repeatDays.monday,
                tuesday: repeatInfo.repeatDays.tuesday,
                wednesday: repeatInfo.repeatDays.wednesday,
                thursday: repeatInfo.repeatDays.thursday,
                friday: repeatInfo.repeatDays.friday,
                saturday: repeatInfo.repeatDays.saturday,
              },
              endingDate: repeatInfo.endingDate,
              endingAfter: repeatInfo.endingAfter,
              endingType: repeatInfo.endingType,
            },
          };

          let appointmentWeek01 = await AppointmentDao.updateAppointment(
            weeklyAppointments[0]._id,
            appointmentDetails
          );

          appointmentDetails.start = week02Start;
          appointmentDetails.end = week02End;

          let appointmentWeek02 = await AppointmentDao.updateAppointment(
            weeklyAppointments[1]._id,
            appointmentDetails
          );

          appointmentDetails.start = week03Start;
          appointmentDetails.end = week03End;

          let appointmentWeek03 = await AppointmentDao.updateAppointment(
            weeklyAppointments[2]._id,
            appointmentDetails
          );

          appointmentDetails.start = week04Start;
          appointmentDetails.end = week04End;

          let appointmentWeek04 = await AppointmentDao.updateAppointment(
            weeklyAppointments[3]._id,
            appointmentDetails
          );

          let appointmentList: IAppointment[] = [
            appointmentWeek01,
            appointmentWeek02,
            appointmentWeek03,
            appointmentWeek04,
          ];

          if (userId.toString == clientId.toString()) {
            if (
              !(
                sessionStartTime === new Date(start).getTime() &&
                sessionEndTime === new Date(end).getTime()
              )
            ) {
              await EmailService.sendEventEmail(
                therapist,
                "Client rescheduled appointment!",
                `Hey, your appointment has been rescheduled by`,
                "Please check your pending appointments!",
                client.firstname + " " + client.lastname
              );

              if (therapist?.primaryPhone) {
                await SMSService.sendEventSMS(
                  `Hey, your appointment has been rescheduled by ${client.firstname} ${client.lastname}`,
                  therapist?.primaryPhone,
                  "Appointment-ep 37"
                );
              }

              const notificationDetails: DNotification = {
                senderId: userId,
                receiverId: therapistId,
                event: NotificationEvent.APPOINMENT_UPDATED,
                link: "/appointments",
                content:
                  "Appointment is rescheduled by " +
                  client?.firstname +
                  " " +
                  client?.lastname,
                variant: NotificationVarient.INFO,
                readStatus: false,
              };

              await NotificationDao.createNotification(notificationDetails);
            } else {
              await EmailService.sendEventEmail(
                therapist,
                "Appointement has been updated!",
                "Appointement has been updated by",
                "Click here to connect with the client.",
                client.firstname + " " + client.lastname
              );

              if (therapist?.primaryPhone) {
                await SMSService.sendEventSMS(
                  `Appointement has been updated by ${client.firstname} ${client.lastname}`,
                  therapist?.primaryPhone,
                  "Appointment-ep 38"
                );
              }
            }
          }

          if (userId.toString == therapistId.toString()) {
            if (
              !(
                sessionStartTime === new Date(start).getTime() &&
                sessionEndTime === new Date(end).getTime()
              )
            ) {
              await EmailService.sendEventEmail(
                client,
                "Your Therapist Rescheduled Appointment!",
                `Hey, Rescheduled your appointment by`,
                "Please check your pending appointments!",
                therapist.firstname + " " + therapist.lastname
              );

              if (client?.primaryPhone) {
                await SMSService.sendEventSMS(
                  `Hey, Rescheduled your appointment by ${therapist.firstname} ${therapist.lastname}`,
                  client?.primaryPhone,
                  "Appointment-ep 39"
                );
              }

              const notificationDetails: DNotification = {
                senderId: therapistId,
                receiverId: clientId,
                event: NotificationEvent.APPOINMENT_UPDATED,
                link: "/appointments",
                content:
                  "Appointment is rescheduled by " +
                  therapist?.firstname +
                  " " +
                  therapist?.lastname,
                variant: NotificationVarient.INFO,
                readStatus: false,
              };

              await NotificationDao.createNotification(notificationDetails);
            } else {
              await EmailService.sendEventEmail(
                client,
                "Appointement has been updated!",
                "Appointement has been updated by",
                "Click here to connect with the therapist.",
                therapist.firstname + " " + therapist.lastname
              );

              if (client?.primaryPhone) {
                await SMSService.sendEventSMS(
                  `Appointement has been updated by ${therapist.firstname} ${therapist.lastname}`,
                  client?.primaryPhone,
                  "Appointment-ep 40"
                );
              }
            }
          }
          return res.sendSuccess(
            appointmentList,
            "Appointment is updated successfully."
          );
        }
      } else {
        return res.sendError(
          "Could not find weekly appointments for the provided group Id!"
        );
      }
    } catch (error) {
      return res.sendError;
    }
  }

  export async function viewAllAppointmentsByTherapistIdForMonth(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistId = req.body.therapistId;
      const selectedMonth = req.body.selectedMonth;

      if (!therapistId || !mongoose.Types.ObjectId.isValid(therapistId)) {
        return res.sendError("Invalid therapist id");
      }

      if (!selectedMonth || !moment(selectedMonth).isValid()) {
        return res.sendError("Selected Month is not a valid date.");
      }

      let appointmentList =
        await AppointmentDao.viewAllAppointmentsByTherapistIdForMonth(
          therapistId,
          selectedMonth
        );

      return res.sendSuccess(
        appointmentList,
        "Appointments Received Successfully."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendTomorrowAppointmentScheduleReminderForTherapist() {
    try {
      AppLogger.info(`.:: Send Tomorrow Appointment Schedule Reminder For Therapist ::.`);

      const tomorrow = moment().tz('America/New_York').add(1, 'days');
      const tomorrowStart = tomorrow.startOf('day').utc().toDate();
      const tomorrowEnd = tomorrow.endOf('day').utc().toDate();

      const therapistsWithTomorrowAppointments = await Appointment.aggregate([
        {
          $match: {
            start: { $gte: tomorrowStart, $lte: tomorrowEnd },
            $or: [
              { status: AppointmentStatus.PENDING },
              { status: AppointmentStatus.WAITING_FOR_APPROVAL }
            ]
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'clientId',
            foreignField: '_id',
            as: 'client'
          }
        },
        {
          $unwind: {
            path: '$client',
            preserveNullAndEmptyArrays: false
          }
        },
        {
          $group: {
            _id: '$therapistId',
            appointments: { $push: '$$ROOT' },
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'therapist'
          }
        },
        {
          $unwind: {
            path: '$therapist',
            preserveNullAndEmptyArrays: false
          }
        },
        {
          $project: {
            therapist: 1,
            appointments: 1,
          }
        },
      ]);

      if(therapistsWithTomorrowAppointments){
        await Promise.all(therapistsWithTomorrowAppointments.map(async data => {
          try {
            if (data?.therapist?._id && data?.therapist?.primaryPhone && data?.appointments?.length && data?.appointments?.length > 0) {
              
              // let scheduleText = "";

              // data?.appointments?.map((appointment: any, index: number) => {
              //   if(appointment?.start && appointment?._id){

              //     const utcAppointmentStartTime = moment.utc(appointment?.start);
              //     const estAppointmentStartTime = utcAppointmentStartTime.tz('America/New_York');
              //     const formattedTime = estAppointmentStartTime.format('hh:mm A');

              //     let firstNameOfClient = appointment?.client?.firstname;
              //     let lastNameOfClient = appointment?.client?.lastname;

              //     const firstNameInitial = firstNameOfClient && firstNameOfClient.trim() !== "" ? firstNameOfClient.charAt(0).toUpperCase() : "";
              //     const lastNameCapitalized = lastNameOfClient && lastNameOfClient.trim() !== ""  ? lastNameOfClient.charAt(0).toUpperCase() + lastNameOfClient.slice(1).toLowerCase() : "";
  
              //     const newText = `${index + 1}. ${formattedTime} - ${firstNameInitial ?? "Client"} ${lastNameCapitalized ?? ""}`;
  
              //     scheduleText = ` ${scheduleText}\n${newText}`;
              //   }

              // });

              // const maxCharactersLimit = 150;
              // let finalSmsText = "";

              // const messageWithFullList = 
              // `Hey ${data?.therapist?.firstname ?? "Therapist"},\n\nJust a reminder about your Lavni sessions tomorrow:\n\n${scheduleText}\n\nPlease review the client notes and let us know if you need any assistance!\n\nBest\n,Lavni`;

              // if(messageWithFullList.length > maxCharactersLimit){

              //   const linkText = `https://mylavni.com/tomorrow-schedule/${data?.therapist?._id}`

              //   const messageWithLink = 
              //   `Hey ${data?.therapist?.firstname ?? "Therapist"},\n\nJust a reminder about your Lavni sessions tomorrow:\n\n${scheduleText}\n\n(see more: ${linkText})\n\nPlease review the client notes and let us know if you need any assistance!\n\nBest\n,Lavni`;

              //   const charactersCountForRemove = messageWithLink.length - maxCharactersLimit;

              //   scheduleText = scheduleText.slice(0, -charactersCountForRemove);

              //   const indexOfLastNewLine = scheduleText.lastIndexOf('\n');

              //   if (indexOfLastNewLine != -1) {
              //     scheduleText = scheduleText.slice(0, indexOfLastNewLine);
              //   }

              //   finalSmsText = 
              //   `Hey ${data?.therapist?.firstname ?? "Therapist"},\n\nJust a reminder about your Lavni sessions tomorrow:\n\n${scheduleText}\n\n(see more: ${linkText})\n\nPlease review the client notes and let us know if you need any assistance!\n\nBest\n,Lavni`;

              // } else {
              //   finalSmsText = messageWithFullList;
              // }

              const linkText = `https://mylavni.com/tomorrow-schedule/${data?.therapist?._id}`;

              let finalSmsTextForSend = 
              `Hey ${data?.therapist?.firstname ?? "Therapist"},
               Check the link below to see your full Lavni schedule for tomorrow: ${linkText} .`;

              
              await SMSService.sendEventSMS(
                finalSmsTextForSend,
                data?.therapist?.primaryPhone,
                "Appointment-ep 41"
              );

            }
          } catch (error) {
            AppLogger.error(
              `.:: Send Tomorrow Appointment Schedule Reminder For Therapist: ${data?.therapist?._id ?? "No therapist presents"} failed with error ${error ?? "No error recieved"} ::.`
            );
          }
          
        }));
      }

    } catch (error) {
      AppLogger.error(
        `.:: Send Tomorrow Appointment Schedule Reminder For Therapist failed with error ${error ?? "No error recieved"} ::.`
      );
    }
  }

  export async function viewTomorrowAppointmentScheduleForTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistIdFrom = req.params.id;

      if (!therapistIdFrom || !mongoose.Types.ObjectId.isValid(therapistIdFrom)) {
        return res.sendError("Invalid id");
      }

      const therapistDetails = await Therapist.findById(therapistIdFrom);

      if(!therapistDetails || !therapistDetails?._id || !therapistDetails?.role || therapistDetails?.role != UserRole.THERAPIST) {
        return res.sendError("Invalid id");
      }

      const therapistId = therapistDetails?._id;
    
      const tomorrow = moment().tz('America/New_York').add(1, 'days');
      const tomorrowStart = moment(tomorrow).tz('America/New_York').startOf('day').utc().toDate();
      const tomorrowEnd = moment(tomorrow).tz('America/New_York').endOf('day').utc().toDate();

      const tomorrowAppointmentList = await Appointment.aggregate([
        {
          $match: {
            therapistId: therapistId,
            start: { $gte: tomorrowStart, $lte: tomorrowEnd },
            $or: [
              { status: AppointmentStatus.PENDING },
              { status: AppointmentStatus.WAITING_FOR_APPROVAL }
            ]
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'clientId',
            foreignField: '_id',
            as: 'clientDetails'
          }
        },
        {
          $set: {
            clientDetails: { $arrayElemAt: ["$clientDetails", 0] }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'therapistId',
            foreignField: '_id',
            as: 'therapistDetails'
          }
        },
        {
          $set: {
            therapistDetails: { $arrayElemAt: ["$therapistDetails", 0] }
          }
        },
        {
          $project: {
            _id: 0,
            start: 1,
            createdAt: 1,
            title: 1,
            approvedStatus:1,
            clientDetails: {
              firstname: 1,
              lastname: 1
            },
            therapistDetails: {
              firstname: 1,
              lastname: 1
            },
            repeatInfo: {
              repeatType: 1,
            },
          }
        },
        {
          $sort: { start: 1 } // Sort by 'start' field in ascending order (oldest to newest)
        }
      ]);

      if(!tomorrowAppointmentList){
        return res.sendError("Something went wrong!");
      }

      return res.sendSuccess(
        tomorrowAppointmentList,
        "Schedule Received Successfully."
      );
      
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function createPublicAppointmentValidationRules() {
    return [
      check("therapistId")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Therapist Id is required."),
      check("clientId")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Client Id is required."),
      check("start")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Start time is required."),
      check("end")
        .isString()
        .not()
        .isEmpty()
        .withMessage("End time is required."),
      check("title")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Title is required."),
    ];
  }

  export async function createPublicAppointment(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors);
      }
      const {therapistId, clientId, start, end, title } = req.body;

      let therapist = await TherapistDao.getUserById(therapistId);

      let client = await ClientDao.getUserById(clientId);

      if (!therapist || therapist == null) {
        return res.sendError(
          "No existing therapist for the provided therapist Id."
        );
      }

      if (!client || client == null) {
        return res.sendError("No existing client for the provided client Id.");
      }

      let isFriend = await FriendRequestDao.checkIfUserIsFriend(
        client._id,
        therapist._id
      );

      if (!isFriend) {
        return res.sendError(
          "Sorry! Selected client has not connected with this therapist yet."
        );
      }

      if (
        client.premiumStatus != "active" &&
        (client.subscriptionId == null ||
          client.subscriptionStatus != "active") &&
        client.testSubscriptionStatus != "active"
      ) {
        return res.sendError(
          "Sorry! This client doesn't have an active subscription."
        );
      }

      if (Util.invalidTimeCheck(start)) {
        return res.sendError("Sorry! You have selected invalid time.");
      }

      let appointementsInTheCurrentSlots =
          await AppointmentDao.getAppointmentsOfTherapistByStartTime(
            new Date(start),
            new Date(end),
            60,
            Types.ObjectId(therapistId)
          );

        if (appointementsInTheCurrentSlots.length > 0) {
          return res.sendError(
            "Therapist has already scheduled an appointment during the selected time slot."
          );
        }

        let appointementsInTheCurrentSlotsOfClient =
          await AppointmentDao.getAppointmentsOfClientByStartTime(
            new Date(start),
            new Date(end),
            60,
            Types.ObjectId(clientId)
          );

        if (appointementsInTheCurrentSlotsOfClient.length > 0) {
          return res.sendError(
            "Client has already scheduled an appointment during the selected time slot."
          );
        }

        if (Util.validateBlockedDates(start, therapist.blockedDates)) {
          return res.sendError("Sorry! Therapist `" + therapist.firstname + " " + therapist.lastname + "` has blocked this time.");
        }


      AppLogger.info(`Public appointment create started, therapistId: ${therapistId}, clientId: ${clientId}, appointment start: ${start}`)
      const appointmentDetails: DAppointment = {
        therapistId: therapistId,
        clientId: clientId,
        start: start,
        end: end,
        title: title,
        reminders: [30],
        typeOfMeeting: "VIDEO",
        createdBy: therapistId,
        color: "#FF6900",
        status: AppointmentStatus.PENDING,
        approvedStatus: ApprovalStatus.APPROVED,
        repeatInfo: {
          repeatType: RepeatType.DOES_NOT_REPEAT,
          endingDate: ""
        },
      };

      const newAppointment = await AppointmentDao.createAppointment(
        appointmentDetails
      );

      return res.sendSuccess({}, "Public appointment created successfully");
    } catch (error) {
      return res.sendError(`Public appointment create failed, error: ${error}`);
    }
  }

}
