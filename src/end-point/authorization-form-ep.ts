import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import { DAmeriHealthAuthForm } from "../models/ameri-health-auth-form-model";
import { AuthorizationFormDao } from "../dao/authorization-form-dao";
import * as mongoose from "mongoose";
import { DCarolinaCompleteHealthAuthForm } from "../models/carolina-complete-health-auth-form-model";
import { DUnitedHealthCareAuthForm } from "../models/united-health-care-auth-form-model";
import { DHealthyBlueAuthForm } from "../models/healthy-blue-auth-form-model";
import { AppLogger } from "../common/logging";
import { DWellCareAuthForm } from "../models/wellcare-auth-form-model";
import { DAmbetterAuthForm } from "../models/ambetter-auth-form-model";
import AuthorizationForm from "../schemas/authorization-form-schema";
import { UserDao } from "../dao/user-dao";
import { UserRole } from "../models/user-model";
import InsuranceDocApproval from "../schemas/insurance-doc-approval-schema";
import { AuthFormType } from "../models/authorization-form-model";

export namespace AuthorizationFormEp {
  export async function createAmeriHealthAuthForm(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistId = new mongoose.Types.ObjectId(req.user._id);
      const { user, ...restOfBody } = req.body;

      const ameriHealthData: DAmeriHealthAuthForm = {
        therapistId: therapistId,
        ...restOfBody,
      };

      const newForm = await AuthorizationFormDao.createAmeriHealthAuthForm(ameriHealthData);

      if (!newForm) {
        AppLogger.error(`Error while creating Ameri Health Form. TherapistId: ${therapistId}. ClientId: ${req.body.clientId}`);
        return res.sendError("Error while creating Ameri Health Form.");
      }

      return res.sendSuccess(newForm, "Ameri Health Form is successfully created.");
    } catch (error) {
      AppLogger.error(`Error while creating Ameri Health Form. TherapistId: ${req.user._id}. ClientId: ${req.body.clientId} Error: ${error}.`);
      return res.sendError(error);
    }
  }

  export async function createCarolinaCompleteHealthAuthForm(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistId = new mongoose.Types.ObjectId(req.user._id);
      const { user, ...restOfBody } = req.body;

      const carolinaCompleteHealthData: DCarolinaCompleteHealthAuthForm = {
        therapistId: therapistId,
        ...restOfBody,
      };

      const newForm = await AuthorizationFormDao.createCarolinaCompleteHealthAuthForm(carolinaCompleteHealthData);

      if (!newForm) {
        AppLogger.error(`Error while creating Carolina Complete Health Form. TherapistId: ${therapistId}. ClientId: ${req.body.clientId}`);
        return res.sendError("Error while creating Carolina Complete Health Form.");
      }

      return res.sendSuccess(newForm, "Carolina Complete Health Form is successfully created.");
    } catch (error) {
      AppLogger.error(`Error while creating Carolina Complete Health Form. TherapistId: ${req.user._id}. ClientId: ${req.body.clientId} Error: ${error}.`);
      return res.sendError(error);
    }
  }

  export async function createUnitedHealthCareAuthForm(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistId = new mongoose.Types.ObjectId(req.user._id);
      const { user, ...restOfBody } = req.body;

      const unitedHealthCareData: DUnitedHealthCareAuthForm = {
        therapistId: therapistId,
        ...restOfBody,
      };

      const newForm = await AuthorizationFormDao.createUnitedHealthCareAuthForm(unitedHealthCareData);

      if (!newForm) {
        AppLogger.error(`Error while creating United Health Care Form. TherapistId: ${therapistId}. ClientId: ${req.body.clientId}`);
        return res.sendError("Error while creating United Health Care Form.");
      }

      return res.sendSuccess(newForm, "United Health Care Form is successfully created.");
    } catch (error) {
      AppLogger.error(`Error while creating United Health Care Form. TherapistId: ${req.user._id}. ClientId: ${req.body.clientId} Error: ${error}.`);
      return res.sendError(error);
    }
  }

  export async function createHealthyBlueAuthForm(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistId = new mongoose.Types.ObjectId(req.user._id);
      const { user, ...restOfBody } = req.body;

      const healthyBlueData: DHealthyBlueAuthForm = {
        therapistId: therapistId,
        ...restOfBody,
      };

      const newForm = await AuthorizationFormDao.createHealthyBlueAuthForm(healthyBlueData);

      if (!newForm) {
        AppLogger.error(`Error while creating Health Blue Form. TherapistId: ${therapistId}. ClientId: ${req.body.clientId}`);
        return res.sendError("Error while creating Health Blue Form.");
      }

      return res.sendSuccess(newForm, "Health Blue Form is successfully created.");
    } catch (error) {
      AppLogger.error(`Error while creating Health Blue Form. TherapistId: ${req.user._id}. ClientId: ${req.body.clientId} Error: ${error}.`);
      return res.sendError(error);
    }
  }

  export async function createWellCareAuthForm(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistId = new mongoose.Types.ObjectId(req.user._id);
      const { user, ...restOfBody } = req.body;

      const wellCareData: DWellCareAuthForm = {
        therapistId: therapistId,
        ...restOfBody,
      };

      const newForm = await AuthorizationFormDao.createWellCareAuthForm(wellCareData);

      if (!newForm) {
        AppLogger.error(`Error while creating WellCare Form. TherapistId: ${therapistId}. ClientId: ${req.body.clientId}`);
        return res.sendError("Error while creating WellCare Form.");
      }

      return res.sendSuccess(newForm, "WellCare Form is successfully created.");
    } catch (error) {
      AppLogger.error(`Error while creating WellCare Form. TherapistId: ${req.user._id}. ClientId: ${req.body.clientId} Error: ${error}.`);
      return res.sendError(error);
    }
  }

  export async function createAmbetterAuthForm(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistId = new mongoose.Types.ObjectId(req.user._id);
      const { user, ...restOfBody } = req.body;

      const ambetterData: DAmbetterAuthForm = {
        therapistId: therapistId,
        ...restOfBody,
      };

      const newForm = await AuthorizationFormDao.createAmbetterAuthForm(ambetterData);

      if (!newForm) {
        AppLogger.error(`Error while creating Ambetter Form. TherapistId: ${therapistId}. ClientId: ${req.body.clientId}`);
        return res.sendError("Error while creating Ambetter Form.");
      }

      return res.sendSuccess(newForm, "Ambetter Form is successfully created.");
    } catch (error) {
      AppLogger.error(`Error while creating Ambetter Form. TherapistId: ${req.user._id}. ClientId: ${req.body.clientId} Error: ${error}.`);
      return res.sendError(error);
    }
  }

  export async function getAuthorizationFormDetailsByTherapist(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = Types.ObjectId(req.body.clientId);
    const insuranceCompanyId = Types.ObjectId(req.body.insuranceCompanyId);
    const therapistId = new mongoose.Types.ObjectId(req.user._id);
    try {
      let authorizationFormData = await AuthorizationFormDao.getAuthorizationFormData(clientId, insuranceCompanyId, therapistId);
      
      if (!authorizationFormData) {
        return res.sendError("There is no record matching the specified therapist, client, and insuranceCompanyId!");
      }

      return res.sendSuccess(authorizationFormData, "Authorization form data.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateAuthorizationForm(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { authFormType } = req.params;
      const _id = new mongoose.Types.ObjectId(req.body._id);
      // const therapistId = new mongoose.Types.ObjectId(req.user._id);
      const { user, ...restOfBody } = req.body;
      
      const formData = {
        // therapistId: therapistId,
        ...restOfBody,
      };
  
      const updatedForm = await AuthorizationFormDao.updateAuthorizationFormById(_id, formData);
  
      if (!updatedForm) {
        AppLogger.error(`Error while updating ${authFormType}. FormId: ${_id}.`);
        return res.sendError(`Error while updating ${authFormType}!`);
      }
  
      return res.sendSuccess(updatedForm, `${authFormType} is successfully updated.`);
    } catch (error) {
      AppLogger.error(`Error while updating form. FormId: ${req.body._id}. Error: ${error}.`);
      return res.sendError(error);
    }
  }

  export async function getAuthorizationFormDetailsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const authorizationFormId = req.body._id;

      if (!authorizationFormId || !mongoose.Types.ObjectId.isValid(authorizationFormId)) {
        return res.sendError("Invalid authorization form id");
      }

      let authorizationFormData = await AuthorizationForm.findById(authorizationFormId);

      if (!authorizationFormData) {
        return res.sendError("There is no record matching the specified authorization form id!");
      }
      return res.sendSuccess(authorizationFormData, "Authorization form data.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllInsuranceCompanyList(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN || req.user.role == UserRole.THERAPIST) {
      try {
        if(req.user.role == UserRole.SUB_ADMIN){
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if(ownUser.adminPermission.approvalQueue != true){
            return res.sendError(
              "You don't have permission for view or take an action for insurance documents approval section!"
            );
          }
        }

        const insuranceCompanyList = await AuthorizationFormDao.getAllInsuranceCompanyList();
        return res.sendSuccess(insuranceCompanyList, "List of all insurance companies.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getIndividualFaxInformation(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const insuranceDocApprovalId = Types.ObjectId(req.params.insuranceDocApprovalId);

      if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN || req.user.role == UserRole.THERAPIST) {
        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You don't have permission for view or take an action for insurance documents approval section!"
            );
          }
        }
        const faxData = await InsuranceDocApproval.findById(insuranceDocApprovalId)
        .select('messageId messageStatus fromPhoneNumber toPhoneNumber creationTime lastModifiedTime readStatus faxPageCount');

        if (faxData == null) {
          return res.sendError("No record found for the given Id.");
        }
        return res.sendSuccess(faxData, "Uploaded Insurance Document Fax information.");
      } else {
        return res.sendError("You don't have permission!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function authorizationFormSubmissionConfirmation(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistId = new mongoose.Types.ObjectId(req.user._id);
      const clientId = req.body.clientId;
      const insuranceCompanyId = req.body.insuranceCompanyId;
      const link = req.body.link;

      const authData: any = {
        therapistId: therapistId,
        clientId: clientId,
        insuranceCompanyId: insuranceCompanyId,
        authFormType: AuthFormType.NoAuth,
        link: link
      };

      const newForm = await AuthorizationFormDao.authorizationFormSubmissionConfirmationByTherapist(authData);

      if (!newForm) {
        AppLogger.error(`Error while authorization submission confirmed by therepist. TherapistId: ${therapistId}. ClientId: ${req.body.clientId}`);
        return res.sendError("An error occurred while confirming the prior authorization form submission. Please try again.");
      }

      return res.sendSuccess(newForm, "You have successfully confirmed the authorization form submission. You may now proceed with submitting it for admin review.");
    } catch (error) {
      AppLogger.error(`Error while authorization submission confirmed by therepist. TherapistId: ${req.user._id}. ClientId: ${req.body.clientId} Error: ${error}.`);
      return res.sendError(error);
    }
  }

}
