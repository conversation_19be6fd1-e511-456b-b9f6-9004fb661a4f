import { NextFunction, Request, Response } from "express";
import * as mongoose from "mongoose";
import User from "../schemas/user-schema";
import { UserRole } from "../models/user-model";
import { chatUserList, io } from "../server";
const { htmlToText } = require("html-to-text");
import moment = require("moment");
import { DNotification, NotificationEvent } from "../models/notification-model";
import Notification from "../schemas/notification-schema";

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const twilioAdminClientChatNumber =
  process.env.TWILIO_ADMIN_CLIENT_CHAT_PHONE_NUMBER;
const adminClientChatEncryptionKey =
  process.env.ADMIN_CLIENT_CHAT_ENCRYPTION_KEY;

const twilioClient = require("twilio")(accountSid, authToken);

const aes256 = require("aes256");

export namespace AdminClientSmsChatEp {
  export async function sendAdminClientChatMessage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const clientId = req.body.clientId;
      const messageText = req.body.messageText;
      const preMessageText = req.body.preMessageText;
      const userId = req.user._id;

      if (!clientId || !messageText || !userId) {
        return res.sendError("Please provide Client Id, Message Text.");
      }

      if (!mongoose.Types.ObjectId.isValid(clientId)) {
        return res.sendError("Invalid Client Id");
      }

      const clientDetails = await User.findOne({
        _id: clientId,
        role: UserRole.CLIENT,
      });
      const adminDetails = await User.findById(userId);

      if (!clientDetails || !adminDetails || !clientDetails.primaryPhone) {
        return res.sendError("Invalid client Id");
      }
      const clientPhoneNumber = clientDetails.primaryPhone;
      const decryptedMsgWithHtml = await doDecryptAdminClientMessage(
        messageText
      );
      const decryptedMsg = htmlToTextFunction(decryptedMsgWithHtml);
      let mentionMessageText;

      if (preMessageText) {
        const mentionMessageTextDecrypted = await doDecryptAdminClientMessage(
          preMessageText
        );
        mentionMessageText = htmlToTextFunction(mentionMessageTextDecrypted);
        if (mentionMessageText && mentionMessageText.length > 11) {
          mentionMessageText = mentionMessageText.substring(0, 10) + "...";
        }
      }

      let finalMessageString = `${
        mentionMessageText
          ? "reply to [ " + (mentionMessageText ?? "") + " ] "
          : ""
      } ${decryptedMsg ?? ""} `;

      const encryptedMessageText = await doEncryptAdminClientMessage(
        finalMessageString
      );

      const messageResponse = await twilioClient.messages.create({
        body: finalMessageString,
        from: twilioAdminClientChatNumber,
        to: clientPhoneNumber,
      });

      if (messageResponse) {
        const messageData = {
          _id: messageResponse.sid ?? "",
          firstname: adminDetails.firstname ?? "",
          lastname: adminDetails.lastname ?? "",
          messageText: encryptedMessageText,
          messageStatus: "sent",
          createdBy: "SUPER_ADMIN",
          createdAt: new Date(),
          clientId: clientDetails._id.toString(),
          adminId: userId,
        };

        sendAdminClientChatMessageSocketEvent(
          clientDetails._id.toString(),
          messageData
        );

        return res.sendSuccess(messageData, "Message is successfully sent!");
      } else {
        return res.sendError("Message sending failed");
      }
    } catch (err) {
      return res.sendError("Something went wrong" + err);
    }
  }

  export async function getTwilioMessageHistoryWithClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const clientId = req.body.clientId;
      // const startDate = req.body.startDate;
      // const endDate = req.body.endDate;
      let limit = req.body.limit;
      let offset = req.body.offset;
      const userId = req.user._id;
      let finalData: any[] = [];
      if (!clientId || !userId) {
        return res.sendError(
          "Please provide clientId, startDate, endDate , limit and offset."
        );
      }
      if (
        limit == null ||
        offset == null ||
        parseInt(limit) == null ||
        Number.isNaN(parseInt(limit)) ||
        parseInt(offset) == null ||
        Number.isNaN(parseInt(offset)) ||
        parseInt(limit) < 0 ||
        parseInt(offset) < 0
      ) {
        return res.sendError("invalid limit and offset");
      }
      // if (!moment(startDate).isValid()) {
      //   return res.sendError("Start Date is not a valid date.");
      // }
      // if (!moment(endDate).isValid()) {
      //   return res.sendError("End Date is not a valid date.");
      // }
      if (!mongoose.Types.ObjectId.isValid(clientId)) {
        return res.sendError("Invalid client Id");
      }

      const clientDetails = await User.findOne({
        _id: clientId,
        role: UserRole.CLIENT,
      });
      const adminDetails = await User.findById(userId);

      if (!clientDetails || !adminDetails || !clientDetails.primaryPhone) {
        return res.sendError("Invalid client Id");
      }

      await Notification.updateMany(
        {
          receiverId: userId,
          senderId: clientDetails._id,
          event: NotificationEvent.NEW_TWILIO_MESSAGE_FROM_CLIENT,
        },
        { $set: { readStatus: true, twilioMessageReadStatus: true } },
        { new: true }
      );

      const clientPhoneNumber = clientDetails.primaryPhone;
      // const finalStartDate = moment(startDate).format("YYYY-MM-DDTHH:mm:ss[Z]");
      // const finalEndDate = moment(endDate).format("YYYY-MM-DDTHH:mm:ss[Z]");
      const finalLimit = parseInt(limit);
      let finalOffset = parseInt(offset);

      // dateSentAfter: finalStartDate,
      // dateSentBefore: finalEndDate,

      const fromAdminSideResponse = await twilioClient.messages.list({
        from: twilioAdminClientChatNumber,
        to: clientPhoneNumber,
        limit: 1000,
      });

      if (!fromAdminSideResponse) {
        return res.sendError("Twilio error");
      }

      const fromClientSideResponse = await twilioClient.messages.list({
        from: clientPhoneNumber,
        to: twilioAdminClientChatNumber,
        limit: 1000,
      });

      if (!fromClientSideResponse) {
        return res.sendError("Twilio error");
      }

      await Promise.all(
        fromAdminSideResponse.map(async (message: any) => {
          let messageTextEncrypted = "";
          if (message.body) {
            messageTextEncrypted = await doEncryptAdminClientMessage(
              message.body
            );
          }

          const data = {
            _id: message.sid ?? "",
            firstname: adminDetails.firstname ?? "",
            lastname: adminDetails.lastname ?? "",
            messageText: messageTextEncrypted,
            messageStatus: message.status ?? "",
            createdBy: "SUPER_ADMIN",
            createdAt: message.dateSent ?? "",
          };
          finalData.push(data);
        })
      );

      await Promise.all(
        fromClientSideResponse.map(async (message: any) => {
          let messageTextEncrypted = "";
          if (message.body) {
            messageTextEncrypted = await doEncryptAdminClientMessage(
              message.body
            );
          }

          const data = {
            _id: message.sid ?? "",
            firstname: clientDetails.firstname ?? "",
            lastname: clientDetails.lastname ?? "",
            messageText: messageTextEncrypted,
            messageStatus: message.status ?? "",
            createdBy: clientDetails._id ?? "",
            createdAt: message.dateSent ?? "",
          };
          finalData.push(data);
        })
      );

      finalData.sort(
        (a: any, b: any) =>
          moment(b.createdAt).valueOf() - moment(a.createdAt).valueOf()
      );

      const finalSlicedData = finalData.slice(
        finalOffset,
        finalLimit + finalOffset
      );

      return res.sendSuccess(finalSlicedData, "Successfully !");
    } catch (err) {
      return res.sendError("Something went wrong");
    }
  }

  async function sendAdminClientChatMessageSocketEvent(
    clientId: string,
    dataForSend: any
  ) {
    try {
      const adminList = await User.find({
        $or: [
          { role: UserRole.SUPER_ADMIN },
          { role: UserRole.SUB_ADMIN }
        ]
      });

      if (adminList) {
        const userIdList = await Promise.all(
          adminList.map((user) => user._id.toString())
        );
        if (userIdList) {
          const socketIdList: any[] = [];
          await Promise.all(
            chatUserList.map((user: any) => {
              if (userIdList.includes(user.userId)) {
                socketIdList.push(user.socketId);
              }
            })
          );

          const finalSocketIdList = socketIdList.flat();

          io.to(finalSocketIdList).emit(
            `new-admin-client-chat-message-${clientId}`,
            dataForSend
          );
        }
      }

      return;
    } catch (error) {
      return;
    }
  }

  async function doEncryptAdminClientMessage(text: string) {
    try {
      if (text) {
        const encrypted = aes256.encrypt(adminClientChatEncryptionKey, text);
        return encrypted;
      }
      return "";
    } catch (error) {
      return "";
    }
  }

  async function doDecryptAdminClientMessage(cipher: string) {
    try {
      if (cipher && cipher?.length > 0) {
        const decrypted = aes256.decrypt(adminClientChatEncryptionKey, cipher);
        const regex = /<p>(.*?)<\/p>/;
        const withoutTags = decrypted.replace(regex, "$1");
        return withoutTags;
      }
      return "";
    } catch (error) {
      return "";
    }
  }

  function htmlToTextFunction(htmlTextMessage: string) {
    try {
      if (htmlTextMessage) {
        const finalText = htmlToText(htmlTextMessage, {
          wordwrap: 160,
        });
        return finalText;
      }
      return "";
    } catch (error) {
      return "";
    }
  }

  export async function sendAdminClientChatMessageSocketEventWhenIncomingMessage(
    req: Request
  ) {
    try {
      if (
        req &&
        req.body &&
        req.body.From &&
        req.body.Body &&
        req.body.SmsSid
      ) {
        const incomingPhoneNumber = req.body.From;
        const incomingMessageText = req.body.Body;
        const encryptedMessageText = await doEncryptAdminClientMessage(
          incomingMessageText
        );

        const incomingUserDetails = await User.findOne({
          primaryPhone: incomingPhoneNumber,
          role: UserRole.CLIENT,
        });
        if (incomingUserDetails) {
          await Notification.deleteMany({
            senderId: incomingUserDetails._id,
            event: NotificationEvent.NEW_TWILIO_MESSAGE_FROM_CLIENT,
          });

          const messageData = {
            _id: req.body.SmsSid ?? "",
            firstname: incomingUserDetails.firstname ?? "",
            lastname: incomingUserDetails.lastname ?? "",
            messageText: encryptedMessageText,
            messageStatus: "received",
            createdBy: incomingUserDetails._id ?? "",
            createdAt: new Date(),
            clientId: incomingUserDetails._id.toString(),
            adminId: "admin",
          };

          const adminList = await User.find({
            $or: [
              { role: UserRole.SUPER_ADMIN },
              { role: UserRole.SUB_ADMIN }
            ]
          });

          if (adminList && adminList?.length && adminList?.length > 0) {
            await Promise.all(
              adminList.map(async (admin) => {
                try {
                  if (admin?._id) {
                    const notificationDetails: DNotification = {
                      senderId: incomingUserDetails._id,
                      receiverId: admin?._id,
                      event: NotificationEvent.NEW_TWILIO_MESSAGE_FROM_CLIENT,
                      link: "/view-all-clients",
                      content: `New message received from ${
                        incomingUserDetails.firstname ?? "Client"
                      } ${incomingUserDetails.lastname ?? ""} (${
                        incomingUserDetails.primaryPhone ?? "Unknown"
                      })`,
                      variant: "info",
                      readStatus: false,
                      twilioMessageReadStatus: false,
                    };
      
                    const iNotification = new Notification(notificationDetails);
                    const newNotification = await iNotification.save();
                  }
                  
                } catch (error) {
                  
                }
                
              })
            );

            const userIdList = await Promise.all(
              adminList.map((user) => user._id.toString())
            );

            if (userIdList && userIdList?.length && userIdList?.length > 0) {
              const socketIdList: any[] = [];

              await Promise.all(
                chatUserList.map((user: any) => {
                  if (userIdList.includes(user.userId)) {
                    socketIdList.push(user.socketId);
                  }
                })
              );

              const finalSocketIdList = socketIdList.flat();

              io.to(finalSocketIdList).emit(
                `new-admin-client-chat-message-${incomingUserDetails._id.toString()}`,
                messageData
              );

              const notificationData = {
                notifyData: {
                  content: `New message received from ${
                    incomingUserDetails.firstname ?? "Client"
                  } ${incomingUserDetails.lastname ?? ""} (${
                    incomingUserDetails.primaryPhone ?? "Unknown"
                  })`,
                  variant: "info",
                  event: NotificationEvent.NEW_TWILIO_MESSAGE_FROM_CLIENT,
                  messageSenderId: incomingUserDetails._id.toString(),
                },
              };

              io.to(finalSocketIdList).emit("notification-received", {
                data: notificationData,
              });

            }
          }
        }
      }

      return;
    } catch (error) {
      return;
    }
  }
}
