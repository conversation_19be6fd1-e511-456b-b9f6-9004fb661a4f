import { NextFunction, Request, Response } from "express";
import { validationResult } from "express-validator";
import { Types } from "mongoose";
import { AdminDao } from "../dao/admin-dao";
import { FeedbackDao } from "../dao/feedback-dao";
import { ReportDao } from "../dao/report-user-dao";
import { UserDao } from "../dao/user-dao";
import { DFeedback } from "../models/feedback-model";
import { UserRole } from "../models/user-model";
import { AppLogger } from "../common/logging";
import Meeting from "../schemas/meeting-schema";
import { EmailService } from "../mail/config";

export namespace FeedbackEp {
  export async function adddFeedback(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      let user = await UserDao.getUserByUserId(req.user._id);

      if (!user) {
        return res.sendError("A user does not exist for the provided Id.");
      }

      if (user.role == UserRole.THERAPIST || user.role == UserRole.CLIENT) {
        const feedbackD: any = {
          feedback: req.body.feedback,
          isRead: false,
          userId: req.user._id,
        };

        try {
          let feedback = await FeedbackDao.addFeedback(feedbackD);

          if (!feedback) {
            return res.sendError(
              "Something went wrong. Please try again later."
            );
          }
          return res.sendSuccess(feedback, "Success");
        } catch (error) {
          return res.sendError(error);
        }
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllFeedbacks(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const user = req.user;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    if (user.role == UserRole.ADMIN || user.role == UserRole.SUPER_ADMIN || user.role ==  UserRole.SUB_ADMIN) {
      try {
        if(req.user.role == UserRole.SUB_ADMIN){
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if(ownUser.adminPermission.feedback != true){
            return res.sendError(
              "You don't have permission for Customer Feedback!"
            );
          }
        }

        let feedbacks = await FeedbackDao.getAllFeedbacks(limit, offset);
        let feedbacksCount = await FeedbackDao.getAllFeedbackCount();

        const feedbackList: any = {
          feedbackList: feedbacks,
          count: feedbacksCount,
        };

        return res.sendSuccess(feedbackList, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function updateFeedbackStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      try {
        if(req.user.role == UserRole.SUB_ADMIN){
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if(ownUser.adminPermission.feedback != true){
            return res.sendError(
              "You don't have permission for Customer Feedback!"
            );
          }
        }
        
        let updatedFeedback = await FeedbackDao.updateFeedback(
          Types.ObjectId(req.params.feedbackId),
          { isRead: true }
        );

        return res.sendSuccess(updatedFeedback, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role");
    }
  }

  export async function saveSessionFeedback(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
        const {meetingId, rate, feedback, therapistComfortable, satisfied, nextSessionScheduled, needCall, createdBy} = req.body;
        if (!meetingId || !rate || !createdBy) {
          return res.sendError('Missing required fields');
        }

        if (!Types.ObjectId.isValid(createdBy)) {
          return res.sendError('Invalid createdBy ID' );
        }
        AppLogger.info(`Save session feedback for user ID ${createdBy}`)
        const data = {
            meetingId: meetingId, 
            rate, 
            feedback, 
            therapistComfortable,
            satisfied,
            nextSessionScheduled,
            needCall,
            createdBy: Types.ObjectId(createdBy)
        };
        const sessionFeedback = await FeedbackDao.createSessionFeedback(data)
        
        
        
        return res.sendSuccess(sessionFeedback);
    } catch (error) {
        AppLogger.error(`Error occured in save session feedback for user ID ${req.body?.createdBy}`)
        console.log(error);
        return res.sendError('An error occurred while saving session feedback');
    }
  }

  export async function getAllSessionFeedbacks(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if(req.user.role == UserRole.SUB_ADMIN){
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if(ownUser.adminPermission.sessionFeedback != true){
          return res.sendError(
            "You don't have permission for Session Feedback!"
          );
        }
      }
      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);
      const searchableString = req.query.searchableString as string;
      const therapistId = req.query.therapistId as string;

      AppLogger.info(`Getting all session feedbacks. limit: ${limit}, offset: ${offset}, searchableString: ${searchableString}, therapistId: ${therapistId}`);

      let feedbacks = await FeedbackDao.getAllSessionFeedbacks(limit, offset, searchableString, therapistId);
      return res.sendSuccess(feedbacks);
    } catch (error) {
      AppLogger.error(`Error occured in getting session feedbacks!`)
      console.log(error);
    }
  }

  export async function getAllSessionFeedbacksNeedCall(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if(req.user.role == UserRole.SUB_ADMIN){
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if(ownUser.adminPermission.sessionFeedback != true){
          return res.sendError(
            "You don't have permission for Session Feedback!"
          );
        }
      }

      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);
      const searchableString = req.query.searchableString as string;
      const therapistId = req.query.therapistId as string;

      AppLogger.info(`Getting session feedbacks with needCall = true. limit: ${limit}, offset: ${offset}, searchableString: ${searchableString}, therapistId: ${therapistId}`);

      let feedbacks = await FeedbackDao.getAllSessionFeedbacksNeedCall(limit, offset, searchableString, therapistId);
      return res.sendSuccess(feedbacks);
    } catch (error) {
      AppLogger.error(`Error occurred in getting session feedbacks with needCall = true!`);
      console.log(error);
      return res.sendError('An error occurred while getting session feedbacks that need call');
    }
  }

  export async function updateSessionFeedbackIsRead(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if(req.user.role == UserRole.SUB_ADMIN){
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if(ownUser.adminPermission.sessionFeedback != true){
          return res.sendError(
            "You don't have permission for Session Feedback!"
          );
        }
      }
      
      const sessionFeedbackId = req.params.id;
      const { is_read } = req.body;
      
      if (!Types.ObjectId.isValid(sessionFeedbackId)) {
        return res.sendError('Invalid session feedback ID');
      }
      
      AppLogger.info(`Updating session feedback ${sessionFeedbackId} is_read to ${is_read}`);
      
      const updatedFeedback = await FeedbackDao.updateSessionFeedback(
        sessionFeedbackId,
        { is_read }
      );
      
      if (!updatedFeedback) {
        return res.sendError('Session feedback not found');
      }
      
      return res.sendSuccess(updatedFeedback);
    } catch (error) {
      AppLogger.error(`Error occurred while updating session feedback: ${error}`);
      console.log(error);
      return res.sendError('An error occurred while updating session feedback');
    }
  }

  export async function getSessionFeedbacksNeedCallCount(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if(req.user.role == UserRole.SUB_ADMIN){
        const ownUser = await UserDao.getUserByUserId(req.user._id);
        if(ownUser.adminPermission.sessionFeedback != true){
          return res.sendError(
            "You don't have permission for Session Feedback!"
          );
        }
      }
      
      AppLogger.info(`Getting count of session feedbacks with needCall = true`);
      
      const count = await FeedbackDao.getSessionFeedbacksNeedCallCount();
      return res.sendSuccess({ total_count: count });
    } catch (error) {
      AppLogger.error(`Error occurred in getting count of session feedbacks with needCall = true: ${error}`);
      console.log(error);
      return res.sendError('An error occurred while getting count of session feedbacks that need call');
    }
  }
}
