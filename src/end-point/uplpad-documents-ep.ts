import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import { UserDao } from "../dao/user-dao";
import multer = require("multer");
import * as path from "path";
import * as fs from "fs";
import { UploadDao } from "../dao/upload-dao";
import { DUploadDocument } from "../models/upload-documents-model";
import { UploadDocumentDao } from "../dao/upload-document-dao";
import { EmailService } from "../mail/config";
import { SMSService } from "../sms/config";
let mongoose = require("mongoose");

export namespace UploadDocumentEp {
  export enum UploadCategory {
    UPLOAD_DOCUMENTS = "UPLOAD_DOCUMENTS",
  }

  export async function uploadDocuments(req: Request, res: Response, next: NextFunction) {
    const userId = req.user._id;
    let uploadCategory = UploadCategory.UPLOAD_DOCUMENTS;

    const storage = multer.diskStorage({
      destination: async (req, FileRes, cb) => {
        await updateUploadDocumentsValidationRules(req, cb);
      },
    });

    async function updateUploadDocumentsValidationRules(req: any, cb: any) {
      let destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;

      try {
        fs.access(destination, (error: any) => {
          if (error) {
            return fs.mkdir(destination, (error: any) => cb(error, "destination"));
          } else {
            return cb(null, destination);
          }
        });
      } catch (error) {
        return cb(Error(error), null);
      }
    }

    const upload = multer({ storage: storage }).single("uploadDocumennt");

    try {
      upload(req, res, async function (error: any) {
        if (error) {
          return res.sendError(error + "");
        }
        if (req.file) {
          let clientDetails = JSON.parse(req.body.clientDetails);
          let uploadedDcoument = null;
          const upload: any = req.file;

          let signRequired: boolean = false;
          if (req.body.signRequired !== undefined) {
            signRequired = req.body.signRequired;
          }

          if (req.file) {
            const uploadDocumentFile = {
              userId: userId as unknown as Types.ObjectId,
              originalName: upload.originalname.replace(/ /g, ""),
              name: upload.filename,
              type: upload.mimetype,
              path: upload.path,
              fileSize: upload.size,
              extension: path.extname(upload.originalname) || req.body.extension,
              category: uploadCategory,
              signRequired: signRequired,
            };

            uploadedDcoument = await UploadDao.createUpload(uploadDocumentFile);

            if (!uploadedDcoument) {
              return res.sendError("Error while uploading document.");
            }
          }

          const doc: DUploadDocument = {
            clientId: clientDetails.clientId,
            therapistId: userId,
            uploadDocumentId: uploadedDcoument._id,
          };
          let updatedDoc = await UploadDocumentDao.CreateUploadDocument(doc);

          if (!updatedDoc) {
            return res.sendError("Failed to upload document.");
          }
          const user1 = await UserDao.getUserById(userId);
          const user2 = await UserDao.getUserById(clientDetails.clientId);
          // await EmailService.sendEventEmail(
          //   user2,
          //   "New Document file is uploaded!",
          //   "New Document file is uploaded by",
          //   "Login to view more information.",
          //   user1?.firstname + " " + user1?.lastname
          // );

          // await SMSService.sendEventSMS(`New document file is uploaded by ${user1.firstname} ${user1.lastname}`, user2.primaryPhone);

          return res.sendSuccess(updatedDoc, "Document has been uploaded successfully.");
        }
      });
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllUploadDocumentByTherapistId(req: Request, res: Response, next: NextFunction) {
    const clientId = req.params.userId;
    const therapistId = req.user._id;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    try {
      const docs = await UploadDocumentDao.viewUploadDocumentByClientId(Types.ObjectId(clientId), therapistId, limit, offset);

      return res.sendSuccess(docs, "All uploaded documents");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllUploadDocumentByClientId(req: Request, res: Response, next: NextFunction) {
    const therapistId = req.params.userId;
    const clientId = req.user._id;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);

    try {
      const docs = await UploadDocumentDao.viewUploadDocumentByClientId(clientId, Types.ObjectId(therapistId), limit, offset);
      return res.sendSuccess(docs, "All uploaded documents");
    } catch (error) {
      return res.sendError(error);
    }
  }
}
