import { validationResult } from "express-validator";
import { Request, Response } from "express";
import { TwilioCallDao } from "../dao/twilio-call-dao";

export namespace TwilioCallEp {
  export async function dispatchVoiceToken(req: Request, res: Response) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const { identity } = req.body;
      const token = await TwilioCallDao.dispatchVoiceToken(identity);

      return res.sendSuccess(token, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function twilioVoiceCall(req: Request, res: Response) {
    const { To } = req.body;
    console.log("voice calls");
    console.log(To);
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      console.log("error voice");
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      const twimlResponse = await TwilioCallDao.twilioVoiceCall(To);
      console.log(twimlResponse);
      res.set("Content-Type", "text/xml");
      return res.send(twimlResponse);
    } catch (error) {
      return res.sendError(error);
    }
  }
}
