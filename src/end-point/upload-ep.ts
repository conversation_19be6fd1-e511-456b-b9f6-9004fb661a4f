import { NextFunction, Request, Response } from "express";
import { ApplicationError } from "../common/application-error";
import { DUpload } from "../models/upload-model";
import { Util } from "../common/util";
import { UploadDao } from "../dao/upload-dao";
import * as fs from "fs";
import {
  check,
  param,
  Validation<PERSON>hain,
  validationResult,
} from "express-validator";
import { JwtToken } from "../middleware/jwt-token";
import { UserDao } from "../dao/user-dao";
import { RoleContext } from "twilio/lib/rest/conversations/v1/role";
import { UserRole } from "../models/user-model";
import { VideoCallDao } from "../dao/videocall-dao";
import { Types } from "mongoose";
import multer = require("multer");
const https = require("https");
import * as http from 'http';

var cron = require('node-cron');


export namespace UploadEp {
  export async function downloadDiagnosisNote(req: Request, res: Response) {
    try {
      if (fs.existsSync(process.env.DIAGONOSIS_NOTE_DOWNLOAD_PATH)) {
        return fs
          .createReadStream(process.env.DIAGONOSIS_NOTE_DOWNLOAD_PATH)
          .pipe(res);
      } else {
        return res.sendError("pdf download fail");
      }
    } catch (error) {
      return res.sendError("pdf download fail");
    }
  }
  export async function getImageFromId(req: Request, res: Response) {
    const imageId = req.params.imageId;

    if (!Util.isObjectId(imageId)) {
      return res.sendError("Invalid Image Id.");
    }

    const upload = await UploadDao.getUpload(imageId);

    if (fs.existsSync(upload?.path)) {
      return fs.createReadStream(upload?.path).pipe(res);
    } else {
      return fs.createReadStream("./uploads/logo.jpg").pipe(res);
    }
  }
  export async function getImageFromToken(req: Request, res: Response) {
    const token = req.params.token;
    try {
      const data: any = await JwtToken.getVerifiedDecodedToken(token);
      if (data.user_id) {
        const imageId = req.params.imageId;
        if (!Util.isObjectId(imageId)) {
          return res.sendError("Invalid Image Id.");
        }
        const upload = await UploadDao.getUpload(imageId);

        if (upload.type == "audio" || upload.category == "CALL_RECORDS") {
          return res.sendError("Invalid file type & category.");
        }
        if (fs.existsSync(upload.path)) {
          return fs.createReadStream(upload.path).pipe(res);
        } else {
          return fs.createReadStream("./uploads/logo.jpg").pipe(res);
        }
      } else {
        return res.sendError("Invalid Token.");
      }
    } catch (error) {
      return res.sendError("Invalid Token.");
    }
  }

  export async function getCallRecordFiles(req: Request, res: Response) {
    const token = req.params.token;

    try {
      const data: any = await JwtToken.getVerifiedDecodedToken(token);

      if (data.user_id) {
        const fileId = req.params.fileId;

        if (!Util.isObjectId(fileId)) {
          return res.sendError("Invalid audio Id.");
        }

        let user = await UserDao.getUserById(data.user_id);

        const upload = await UploadDao.getUpload(fileId);

        const meeting = await VideoCallDao.getMeetingByAudioId(
          Types.ObjectId(fileId)
        );

        if (upload.type != "audio" && upload.category != "CALL_RECORDS") {
          return res.sendError("Invalid file type & category.");
        }

        if (user?.role == UserRole.THERAPIST) {
          if (!upload.userId || upload.userId.toString() != data.user_id) {
            return res.sendError("You are not authorized to access this file.");
          }
        } else if (user?.role == UserRole.CLIENT) {
          if (
            meeting.recordingSharedWithClient == true &&
            meeting.clientId.toString() != data.user_id
          ) {
            return res.sendError("You are not authorized to access this file.");
          }
        }

        if (fs.existsSync(upload.path)) {
          let fileReadStream = fs.createReadStream(upload.path);

          const randomName = Math.floor((1 + Math.random()) * 0x100000000000000)
            .toString(16)
            .substring(1);

          res.setHeader(
            "Content-disposition",
            "attachment; filename=" + randomName + ".mp3"
          );

          fileReadStream.pipe(res);

          return fileReadStream;
        } else {
          return res.sendError("Invalid file path.");
        }
      } else {
        return res.sendError("Invalid Token.");
      }
    } catch (error) {
      return res.sendError("Invalid Token.");
    }
  }
  export async function getCallRecordFilesNew(req: Request, res: Response) {
    const token = req.params.token;

    try {
      const data: any = await JwtToken.getVerifiedDecodedToken(token);

      if (data.user_id) {
        const fileId = req.params.fileId;

        if (!Util.isObjectId(fileId)) {
          return res.sendError("Invalid audio Id.");
        }

        let user = await UserDao.getUserById(data.user_id);

        const upload = await UploadDao.getUpload(fileId);

        const meeting = await VideoCallDao.getMeetingByAudioId(
          Types.ObjectId(fileId)
        );

        if (upload.type != "audio" && upload.category != "CALL_RECORDS") {
          return res.sendError("Invalid file type & category.");
        }

        if (user?.role == UserRole.THERAPIST) {
          if (!upload.userId || upload.userId.toString() != data.user_id) {
            return res.sendError("You are not authorized to access this file.");
          }
        } else if (user?.role == UserRole.CLIENT) {
          if (
            meeting.recordingSharedWithClient == true &&
            meeting.clientId.toString() != data.user_id
          ) {
            return res.sendError("You are not authorized to access this file.");
          }
        }

        if (fs.existsSync(upload.path)) {
          // let fileReadStream = fs.createReadStream(upload.path);

          const randomName = Math.floor((1 + Math.random()) * 0x100000000000000)
            .toString(16)
            .substring(1);

          // res.setHeader(
          //   "Content-disposition",
          //   "attachment; filename=" + randomName + ".mp3"
          // );

          // fileReadStream.pipe(res);

          // return fileReadStream;
          var filePath = upload.path;
          var stat = fs.statSync(filePath);
          var total = stat.size;

          if (req.headers.range) {
            var range = req.headers.range;
            var parts = range.replace(/bytes=/, "").split("-");
            var partialstart = parts[0];
            var partialend = parts[1];

            var start = parseInt(partialstart, 10);
            var end = partialend ? parseInt(partialend, 10) : total - 1;
            var chunksize = end - start + 1;
            var readStream = fs.createReadStream(filePath, {
              start: start,
              end: end,
            });

            res.writeHead(206, {
              "Content-Range": "bytes " + start + "-" + end + "/" + total,
              "Accept-Ranges": "bytes",
              "Content-Length": chunksize,
              "Content-Type": "audio",
              "Content-disposition":
                "attachment; filename=" + randomName + ".mp3",
            });

            readStream.pipe(res);
            return readStream;
          } else {
            res.writeHead(200, {
              "Content-Length": total,
              "Content-Type": "audio",
              "Content-disposition":
                "attachment; filename=" + randomName + ".mp3",
            });
            var fileReadStream = fs.createReadStream(filePath);
            fileReadStream.pipe(res);
            return fileReadStream;
          }
        } else {
          return res.sendError("Invalid file path.");
        }
      } else {
        return res.sendError("Invalid Token.");
      }
    } catch (error) {
      return res.sendError("Invalid Token.");
    }
  }

  export async function uploadNewSessionRecordFile(req: Request, res: Response, next: NextFunction) {
    try {
      const uploadData = {
        path: req.body.uploadedFilePath,
        originalName: req.body.originalName,
        type: '.mp3',
        isUrl: false
      }

      let uploadResponse = await UploadDao.createUpload(uploadData);

      if (uploadResponse._id) {
        const newMeeting = await VideoCallDao.updateMeetingAudioArray(req.body.meetingId, uploadResponse._id)
        return res.sendSuccess(newMeeting, "New Session Added successfully");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getDisclosureStatementOfTherapist(req: Request, res: Response) {
    const therapistId = req.params.therapistId;

    if (!Util.isObjectId(therapistId)) {
      return res.sendError("Invalid Therapist Id.");
    }

    try {
      const upload = await UploadDao.getUploadByUserIdAndCategory(therapistId, "DISCLOSURE_STATEMENT");

      if (!upload) {
        return res.sendError("No disclosure statement found for this therapist.");
      }

      if (fs.existsSync(upload.path)) {
        return fs.createReadStream(upload.path).pipe(res);
      } else {
        return res.sendError("File not found on server.");
      }
    } catch (error) {
      return res.sendError("Error retrieving disclosure statement: " + error.message);
    }
  }
}
