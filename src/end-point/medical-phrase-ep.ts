var mongoose = require("mongoose");
import { NextFunction, Request, Response } from "express";
import { check, Validation<PERSON>hain, validationResult } from "express-validator";
import { MedicalPhraseDao } from "../dao/medical-phrase-dao";
import { DMedicalPhrase, Stages } from "../models/medical-phrases";

export namespace MedicalPhraseEp {
  export function createMedicalPhraseValidationRules(): ValidationChain[] {
    return [
      check("value")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Phrase is required"),
      check("tabType")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Tab type is required"),
      check("stage")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Stage is required"),
      check("lastItemId")
        .not()
        .isEmpty()
        .withMessage("Priority of the phrase is required"),
    ];
  }

  export function editMedicalPhraseValidationRules(): ValidationChain[] {
    return [
      check("phraseId")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Phrase Id is required"),
      check("newValue")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Tab type is required"),
    ];
  }

  export async function createMedicalPhrase(req: Request, res: Response) {
    const therapistId = req.user._id;
    const value = req.body.value;
    const tabType = req.body.tabType;
    const lastItemId = req.body.lastItemId;
    const stage = req.body.stage;

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    if (!mongoose.Types.ObjectId.isValid(therapistId)) {
      return res.sendError("Invalid phrase id");
    }

    if (!mongoose.Types.ObjectId.isValid(lastItemId)) {
      return res.sendError("Invalid phrase id");
    }

    try {
      const phraseFound = await MedicalPhraseDao.getMedicalPhrase({
        value: value,
      });

      if (phraseFound.length > 0) {
        return res.sendError("Phrase is already added!");
      }

      const lastAddedPhrase = await MedicalPhraseDao.getMedicalPhraseById(
        lastItemId
      );

      if (!lastAddedPhrase) {
        return res.sendError("Failed to find a phrase with the given Id");
      }

      const lastAddedPhrasePriority = lastAddedPhrase.priority;
      const priorityLetter = lastAddedPhrasePriority.split("_")[0];
      const priorityNumber = parseInt(lastAddedPhrasePriority.split("_")[1]);
      let newPriority = "";
      priorityNumber < 10
        ? (newPriority =
            priorityLetter + "_0" + (priorityNumber + 1).toString())
        : (newPriority =
            priorityLetter + "_" + (priorityNumber + 1).toString());

      const phrase: DMedicalPhrase = {
        value: value,
        priority: newPriority,
        tabType: tabType,
        stage: stage,
        systemGenerated: false,
        createdBy: therapistId,
      };

      const addedPhrase = await MedicalPhraseDao.createMedicalPhrase(phrase);

      return res.sendSuccess(addedPhrase, "Phrase added successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function editMedicalPhrase(req: Request, res: Response) {
    const phraseId = req.body.phraseId;
    const newValue = req.body.newValue;

    if (!mongoose.Types.ObjectId.isValid(phraseId)) {
      return res.sendError("Invalid phrase id");
    }

    try {
      const phraseIdFound = await MedicalPhraseDao.getMedicalPhraseById(
        phraseId
      );

      if (!phraseIdFound) {
        return res.sendError(
          "Failed to find a matching phrase for the provided Id"
        );
      }

      const phrase: Partial<DMedicalPhrase> = {
        value: newValue,
      };

      const phraseFound = await MedicalPhraseDao.getMedicalPhrase(phrase);

      if (phraseFound.length > 0) {
        return res.sendError("Phrase is already added!");
      }

      let updatedPhrase = await MedicalPhraseDao.updateMedicalPhrase(
        phraseId,
        phrase
      );

      return res.sendSuccess(updatedPhrase, "Phrase successfully updated!");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllMedicalPhrasesByStage(
    req: Request,
    res: Response
  ) {
    const stage = req.params.stage;
    const therapistId = req.user._id;

    try {
      let phrasesList = await MedicalPhraseDao.getAllMedicalPhrasesByStage(
        stage,
        therapistId.toString()
      );

      return res.sendSuccess(
        phrasesList,
        "Phrases list successfully retreived."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }
}
