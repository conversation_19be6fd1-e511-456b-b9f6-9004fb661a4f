import { NextFunction, Request, Response } from "express";
import * as mongoose from "mongoose";
import { Types } from "mongoose";
import SmsChatConversation from "../schemas/sms-chat-conversation-schema";
import User from "../schemas/user-schema";
import { ChatDao } from "../dao/chat-dao";
import { ChatData } from "../models/chat-model";
import { MessageData } from "../models/chat-message-model";
import Message from "../schemas/message-schema";
import Upload from "../schemas/upload-schema";
import { UserRole } from "../models/user-model";
import { DUpload } from "../models/upload-model";
import { UploadDao } from "../dao/upload-dao";
import * as path from "path";
import { chatUserList, io } from "../server";
import { IClient } from "../models/client-model";
import { ITherapist } from "../models/therapist-model";
import Therapist from "../schemas/therapist-schema";
import Client from "../schemas/client-schema";
import { DSmsChatConversation } from "../models/sms-chat-conversation-model";
import Appointment from "../schemas/appointment-schema";
import FriendRequest from "../schemas/friend-request-schema";
const { htmlToText } = require("html-to-text");

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const conversationServiceId = process.env.TWILIO_CONVERSATION_SERVICE_ID;
const twilio_number = process.env.TWILIO_CONVERSATION_PHONE_NUMBER;
const encryptionKey = process.env.MESSAGE_ENCRYPTION_KEY;

const twilioClient = require("twilio")(accountSid, authToken);

const aes256 = require("aes256");

export namespace SmsChatEp {
  export async function createMessageNewWithDirectMessage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const { chatId, senderId, text, unreadMessageObj, mentionedMessageId } =
      req.body;

    let tempUnreadMessageObj = null;

    try {
      if (!senderId || !text || !chatId) {
        return res.sendError("Please provide senderId, chatId and text.");
      }

      const checkCreatorIdExist = await ChatDao.getUserDetails(senderId);
      const checkMemberIdExist = await ChatDao.getUserDetails(chatId);

      if (!checkMemberIdExist) {
        return res.sendError("User not found");
      }

      if (!checkCreatorIdExist) {
        return res.sendError("Creator not found");
      }

      const members = [senderId, chatId];

      let checkChatExist = await ChatDao.findChatByMemberIds(members);

      if (!checkChatExist) {
        if (!members || members?.length < 2) {
          return res.sendError("Please add atleast two members");
        }

        if (senderId == chatId) {
          return res.sendError("Sender and reciever must be different users");
        }

        const chatData: ChatData = {
          isActive: true,
          unreadSMS: false,
          lastActiveTime: new Date(),
          members: members.map((id: string) => {
            return mongoose.Types.ObjectId(id);
          }),
          unreadMessage: null,
        };

        checkChatExist = await ChatDao.createChat(chatData);
      }

      let messageData: MessageData = {
        chatId: checkChatExist._id,
        senderId: mongoose.Types.ObjectId(senderId),
        text: text,
        AttachmentUploadId: null,
        mentionedMessage: mentionedMessageId || null,
        isActive: true,
        isMsgSeen: false,
      };

      const chats = await ChatDao.createMessage(messageData);

      const populateMentionedMessage = await Message.populate(chats, [
        { path: "mentionedMessage", model: "Message" },
      ]);

      const populateSenderId = await User.populate(populateMentionedMessage, [
        { path: "senderId", model: "User" },
        { path: "mentionedMessage.senderId", model: "User" },
      ]);

      const populateChats = await Upload.populate(populateSenderId, {
        path: "mentionedMessage.AttachmentUploadId",
      });

      if (unreadMessageObj) {
        tempUnreadMessageObj = {
          ...unreadMessageObj,
          unreadUserId: mongoose.Types.ObjectId(unreadMessageObj.unreadUserId),
          lastMessage: Types.ObjectId(chats._id),
          msgCount: unreadMessageObj?.msgCount + 1,
        };
      }
      const updateLastActiveTime = await ChatDao.updateLastActiveInChat(
        checkChatExist?._id,
        new Date(),
        tempUnreadMessageObj
      );
      //send message to client phone-----------------------
      sendMessageToClientPhone(
        req.user.role.toString(),
        req.user._id.toString(),
        chatId,
        mentionedMessageId || null,
        text || "",
        false,
        checkChatExist._id
      );

      //send message to client phone-------END---------------

      return res.sendSuccess(populateChats, "Message is successfully sent!");
    } catch (err) {
      return res.sendError("Something went wrong");
    }
  }

  export async function uploadAttachmentFileAndCreateMessageNewWithDirectMessage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.file) {
        const attachmentFileData: any = {
          attachFile: req.file,
        };

        if (attachmentFileData.attachFile) {
          const profileImageDetails: DUpload = {
            originalName: attachmentFileData.attachFile.originalname,
            name: attachmentFileData.attachFile.filename,
            type: attachmentFileData.attachFile.mimetype,
            path: attachmentFileData.attachFile.path,
            fileSize: attachmentFileData.attachFile.size,
            extension: path.extname(attachmentFileData.attachFile.originalname),
            category: "MESSAGE_ATTACHMENT",
          };

          let uploadAttachFile = await UploadDao.createUpload(
            profileImageDetails
          );

          if (!uploadAttachFile) {
            return res.sendError("Attachement Upload Failed");
          }

          if (!req.body.messageData) {
            return res.sendError("No message details provided");
          }

          const {
            chatId,
            senderId,
            text,
            unreadMessageObj,
            mentionedMessageId,
          } = JSON.parse(req.body.messageData);

          let tempUnreadMessageObj = null;

          const checkCreatorIdExist = await ChatDao.getUserDetails(senderId);
          const checkMemberIdExist = await ChatDao.getUserDetails(chatId);

          if (!checkMemberIdExist) {
            return res.sendError("User not found");
          }

          if (!checkCreatorIdExist) {
            return res.sendError("Creator not found");
          }

          const members = [senderId, chatId];

          let checkChatExist = await ChatDao.findChatByMemberIds(members);

          if (!checkChatExist) {
            if (!members || members?.length < 2) {
              return res.sendError("Please add atleast two members");
            }

            if (senderId == chatId) {
              return res.sendError(
                "Sender and reciever must be different users"
              );
            }

            const chatData: ChatData = {
              isActive: true,
              unreadSMS: false,
              lastActiveTime: new Date(),
              members: members.map((id: string) => {
                return mongoose.Types.ObjectId(id);
              }),
              unreadMessage: null,
            };

            checkChatExist = await ChatDao.createChat(chatData);
          }

          const messageData: MessageData = {
            chatId: checkChatExist._id,
            senderId: mongoose.Types.ObjectId(senderId),
            text: text,
            AttachmentUploadId: uploadAttachFile._id,
            mentionedMessage: mentionedMessageId || null,
            isActive: true,
            isMsgSeen: false,
          };

          if (!chatId || !senderId) {
            return res.sendError("Please provide senderId,chatId");
          }

          const chats = await ChatDao.createMessage(messageData);

          const populateMentionedMessage = await Message.populate(chats, [
            { path: "mentionedMessage", model: "Message" },
          ]);

          const populateChats = await Upload.populate(
            populateMentionedMessage,
            [
              {
                path: "AttachmentUploadId",
              },
              {
                path: "mentionedMessage.AttachmentUploadId",
              },
            ]
          );

          const populateSenderId = await User.populate(populateChats, [
            { path: "senderId", model: "User" },
            { path: "mentionedMessage.senderId", model: "User" },
          ]);

          if (unreadMessageObj) {
            tempUnreadMessageObj = {
              ...unreadMessageObj,
              lastMessage: chats._id,
              msgCount: unreadMessageObj?.msgCount + 1,
            };
          }

          const updateLastActiveTime = await ChatDao.updateLastActiveInChat(
            checkChatExist?._id,
            new Date(),
            tempUnreadMessageObj
          );
          //send message to client phone-----------------------
          sendMessageToClientPhone(
            req.user.role.toString(),
            req.user._id.toString(),
            chatId,
            mentionedMessageId || null,
            text || "",
            true,
            checkChatExist._id
          );
          //send message to client phone-------END---------------

          return res.sendSuccess(populateSenderId, "Success");
        }
      } else {
        return res.sendError("No file found");
      }
    } catch (err) {
      return res.sendError("Something went wrong - " + err);
    }
  }

  export async function sendMessageToClientPhone(
    userRole: string,
    therapistId: string,
    clientId: string,
    mentionedMessageId: string | null,
    messageText: string,
    isMediaMessage: boolean,
    existingChatId: ObjectId
  ) {
    try {
      if (userRole == UserRole.THERAPIST) {
        const decryptedMsgWithHtml = await doDecrypt(messageText);
        const decryptedMsg = htmlToTextFunction(decryptedMsgWithHtml);
        const therapistDetails: ITherapist = await Therapist.findById(
          therapistId
        );
        const clientDetails: IClient = await Client.findById(clientId);

        if (
          therapistDetails &&
          clientDetails &&
          clientDetails.primaryTherapist &&
          clientDetails.primaryPhone &&
          therapistDetails._id.toString() ==
            clientDetails.primaryTherapist.toString()
        ) {
          let finalConversationId;

          const existingConversation = await SmsChatConversation.findOne({
            clientId: clientDetails._id,
          });

          if (existingConversation) {
            await SmsChatConversation.findByIdAndUpdate(
              existingConversation._id,
              {
                therapistId: therapistDetails._id,
                chatId: existingChatId,
              }
            );
            //------------- update phone number if phoneNumber != clinetDetails.primaryNumber-------------
            if (
              clientDetails.primaryPhone != existingConversation.phoneNumber
            ) {
              if (
                existingConversation.twilioParticipantId &&
                existingConversation.twilioParticipantId != ""
              ) {
                //----Delete Participant----
                await twilioClient.conversations.v1
                  .services(conversationServiceId)
                  .conversations(existingConversation.conversationId)
                  .participants(existingConversation.twilioParticipantId)
                  .remove();

                await SmsChatConversation.findByIdAndUpdate(
                  existingConversation._id,
                  {
                    phoneNumber: "",
                    twilioParticipantId: "",
                  }
                );
              }

              //---Create new participant-----
              const newParticipant = await twilioClient.conversations.v1
                .services(conversationServiceId)
                .conversations(existingConversation.conversationId)
                .participants.create({
                  "messagingBinding.address": clientDetails.primaryPhone,
                  "messagingBinding.proxyAddress": twilio_number,
                });

              if (newParticipant && newParticipant.sid) {
                await SmsChatConversation.findByIdAndUpdate(
                  existingConversation._id,
                  {
                    phoneNumber: clientDetails.primaryPhone,
                    twilioParticipantId: newParticipant.sid,
                  }
                );
              }
            }

            finalConversationId = existingConversation.conversationId;

            //----------------------------------------------------------------------------------------------
          } else {
            const newConversation = await twilioClient.conversations.v1
              .services(conversationServiceId)
              .conversations.create({
                friendlyName: "Sms Chat Conversation",
              });
            if (newConversation && newConversation.sid) {
              const convaData: DSmsChatConversation = {
                clientId: clientDetails._id,
                therapistId: therapistDetails._id,
                conversationId: newConversation.sid,
                chatId: existingChatId,
                twilioParticipantId: "",
                phoneNumber: "",
              };
              const iconversation = new SmsChatConversation(convaData);
              const createdConversation = await iconversation.save();

              if (createdConversation) {
                const newParticipant = await twilioClient.conversations.v1
                  .services(conversationServiceId)
                  .conversations(newConversation.sid)
                  .participants.create({
                    "messagingBinding.address": clientDetails.primaryPhone,
                    "messagingBinding.proxyAddress": twilio_number,
                  });

                if (newParticipant && newParticipant.sid) {
                  // store new participant id
                  await SmsChatConversation.findByIdAndUpdate(
                    createdConversation._id,
                    {
                      phoneNumber: clientDetails.primaryPhone,
                      twilioParticipantId: newParticipant.sid,
                    }
                  );

                  finalConversationId = newConversation.sid;
                }
              }
            }
          }
          if (finalConversationId) {
            let mentionedMessage;
            let mentionMessageText;

            if (mentionedMessageId) {
              mentionedMessage = await Message.findById(mentionedMessageId);
              if (mentionedMessage && mentionedMessage.text) {
                const mentionMessageTextDecrypted = await doDecrypt(
                  mentionedMessage.text
                );
                mentionMessageText = htmlToTextFunction(
                  mentionMessageTextDecrypted
                );
                if (mentionMessageText && mentionMessageText.length > 11) {
                  mentionMessageText =
                    mentionMessageText.substring(0, 10) + "...";
                }
              }
            }

            let finalMessageString = `${therapistDetails.firstname ?? ""} ${
              therapistDetails.lastname ?? ""
            } - ${
              mentionedMessage
                ? "reply to [ " +
                  (mentionMessageText ?? "") +
                  (mentionedMessage.AttachmentUploadId
                    ? " < Sent Attachment >"
                    : "") +
                  " ] "
                : ""
            } ${
              (decryptedMsg ?? "") +
              (isMediaMessage
                ? " < Sent Attachment ( Please Login to View ) >"
                : "")
            } `;

            await twilioClient.conversations.v1
              .services(conversationServiceId)
              .conversations(finalConversationId)
              .messages.create({
                body: finalMessageString,
                author: "system",
              });
          }
        }
      }

      return;
    } catch (error) {
      return;
    }
  }

  export async function conversationWebhookFunction(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const eventType = req.body.EventType;
      if (eventType == "onMessageAdd") {
        const conversationSid = req.body.ConversationSid;
        const messageBody = req.body.Body;

        if (conversationSid && messageBody) {
          const existingConversation = await SmsChatConversation.findOne({
            conversationId: conversationSid,
          });

          if (existingConversation) {
            const encryptedMsg = await doEncrypt(messageBody);

            let messageData: MessageData = {
              chatId: existingConversation.chatId,
              senderId: existingConversation.clientId,
              text: encryptedMsg,
              AttachmentUploadId: null,
              mentionedMessage: null,
              isActive: true,
              isMsgSeen: false,
              isFromMobile: true,
            };

            const chatMessage = await ChatDao.createMessage(messageData);

            if (chatMessage) {
              const populatedData = await User.populate(chatMessage, [
                { path: "senderId", model: "User" },
              ]);

              const unreadMessageObj = {
                unreadUserId: existingConversation.therapistId,
                lastMessage: chatMessage._id,
                msgCount: 2,
              };

              await ChatDao.updateLastActiveInChat(
                existingConversation.chatId,
                new Date(),
                unreadMessageObj
              );

              sendChatMessageSocketEvent(
                existingConversation.clientId.toString(),
                populatedData,
                true
              );
              
              sendChatMessageSocketEvent(
                existingConversation.therapistId.toString(),
                populatedData,
                true
              );
            }
          }
        }
      }
      return;
    } catch (error) {
      return;
    }
  }

  function sendChatMessageSocketEvent(
    receiverId: string,
    dataForSend: any,
    sendNotification: boolean
  ) {
    try {
      const user = chatUserList.find((user: any) => user.userId === receiverId);

      io.to(user?.socketId).emit("receive-message", dataForSend);

      if (sendNotification) {
        io.to(user?.socketId).emit("refresh-unread-notification");
      }
      return;
    } catch (error) {
      return;
    }
  }

  async function doEncrypt(text: string) {
    try {
      if (text) {
        const encrypted = aes256.encrypt(encryptionKey, text);
        return encrypted;
      }
      return "";
    } catch (error) {
      return "";
    }
  }

  async function doDecrypt(cipher: string) {
    try {
      if (cipher && cipher?.length > 0) {
        const decrypted = aes256.decrypt(encryptionKey, cipher);
        const regex = /<p>(.*?)<\/p>/;
        const withoutTags = decrypted.replace(regex, "$1");
        return withoutTags;
      }
      return "";
    } catch (error) {
      return "";
    }
  }

  function htmlToTextFunction(htmlTextMessage: string) {
    try {
      if (htmlTextMessage) {
        const finalText = htmlToText(htmlTextMessage, {
          wordwrap: 160,
        });
        return finalText;
      }
      return "";
    } catch (error) {
      return "";
    }
  }

  export async function changePhoneNumberFromTwilio(
    newPhoneNumber: string,
    userRole: string,
    clientId: string
  ) {
    try {
      if (newPhoneNumber && userRole && clientId) {
        const existingConversation = await SmsChatConversation.findOne({
          clientId: clientId,
        });

        if (
          existingConversation &&
          newPhoneNumber != existingConversation.phoneNumber
        ) {
          if (
            existingConversation.twilioParticipantId &&
            existingConversation.twilioParticipantId != ""
          ) {
            //------------------Delete existing Participant-----------------
            await twilioClient.conversations.v1
              .services(conversationServiceId)
              .conversations(existingConversation.conversationId)
              .participants(existingConversation.twilioParticipantId)
              .remove();

            await SmsChatConversation.findByIdAndUpdate(
              existingConversation._id,
              {
                phoneNumber: "",
                twilioParticipantId: "",
              }
            );
          }

          //---Create new participant-----
          const newParticipant = await twilioClient.conversations.v1
            .services(conversationServiceId)
            .conversations(existingConversation.conversationId)
            .participants.create({
              "messagingBinding.address": newPhoneNumber,
              "messagingBinding.proxyAddress": twilio_number,
            });

          if (newParticipant && newParticipant.sid) {
            await SmsChatConversation.findByIdAndUpdate(
              existingConversation._id,
              {
                phoneNumber: newPhoneNumber,
                twilioParticipantId: newParticipant.sid,
              }
            );
          }
        }
      }

      return;
    } catch (error) {
      return;
    }
  }

  export async function changeTherapistFromConversation(
    newTherapistId: ObjectId,
    userRole: string,
    clientId: ObjectId
  ) {
    try {
      if (newTherapistId && userRole && clientId) {
        const existingConversation = await SmsChatConversation.findOne({
          clientId: clientId,
        });

        if (existingConversation) {
          const chatMembers = [clientId, newTherapistId];

          let existingChat = await ChatDao.findChatByMemberIds(chatMembers);

          if (!existingChat) {
            const chatData: ChatData = {
              isActive: true,
              unreadSMS: false,
              lastActiveTime: new Date(),
              members: chatMembers,
              unreadMessage: null,
            };

            existingChat = await ChatDao.createChat(chatData);
          }

          if (existingChat) {
            await SmsChatConversation.findByIdAndUpdate(
              existingConversation._id,
              {
                therapistId: newTherapistId,
                chatId: existingChat._id,
              }
            );
          }
        }
      }
      return;
    } catch (error) {
      return;
    }
  }

  export async function validatePhoneNumberFromTwilio(phoneNumber: string) {
    try {
      if (phoneNumber) {
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  // for testing

  export async function deleteConversationFromTwilio(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const response = await twilioClient.conversations.v1
        .services(conversationServiceId)
        .conversations(req.body.conversationId)
        .remove();

      return res.sendSuccess(response, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function setDefaultTherapistInExistingClientsTesting(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const allClients = await User.find({ role: UserRole.CLIENT });
      let usersWithoutAnyAppointment: any[] = [];

      if (allClients != null) {
        await Promise.all(
          allClients.map(async (client) => {
            const latestAppointmentsArray = await Appointment.find({
              clientId: client._id,
            })
              .sort({ createdAt: -1 })
              .limit(1);

            if (
              latestAppointmentsArray &&
              latestAppointmentsArray.length > 0 &&
              latestAppointmentsArray[0] &&
              latestAppointmentsArray[0].therapistId
            ) {
              await Client.findByIdAndUpdate(client._id, {
                primaryTherapist: latestAppointmentsArray[0].therapistId,
              });
            } else {
              const latestFriendRequestArray = await FriendRequest.find({
                clientId: client._id,
              })
                .sort({ createdAt: -1 })
                .limit(1);

              if (
                latestFriendRequestArray &&
                latestFriendRequestArray.length > 0 &&
                latestFriendRequestArray[0] &&
                latestFriendRequestArray[0].therapistId
              ) {
                await Client.findByIdAndUpdate(client._id, {
                  primaryTherapist: latestFriendRequestArray[0].therapistId,
                });
              } else {
                usersWithoutAnyAppointment.push(client._id);
              }
            }
          })
        );
      }

      return res.sendSuccess(usersWithoutAnyAppointment, "Success");
    } catch (error) {
      return res.sendError(error);
    }
  }
}
