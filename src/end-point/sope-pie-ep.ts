import { NextFunction, Request, Response } from "express";

import { TherapistDao } from "../dao/therapist-dao";

import {  UserRole } from "../models/user-model";
import { VideoCallDao } from "../dao/videocall-dao";

let mongoose = require("mongoose");
const moment = require("moment-timezone");

import Therapist from "../schemas/therapist-schema";
let PdfPrinter = require("pdfmake");
const { htmlToText } = require("html-to-text");

import * as fs from 'fs';
import * as path from 'path';
import  Meeting  from '../schemas/meeting-schema';
import Upload from '../schemas/upload-schema';
import DiagnosisNote from "../schemas/diagnosis-note-schema";
import DiagnosisNoteVersion from "../schemas/diagnosis-note-version-schema";
import { FormVersionDao } from "../dao/form-version-dao";
import { AppLogger } from "../common/logging";

export namespace SoapPIEEp {
  export async function generateSopeOrPIEWithOpenAI(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const openAIAPIKey = process.env.OPEN_AI_API_KEY;
      const mongoConnectString = process.env.MONGOOSE_URI;
      const mongoDBName = process.env.MONGO_DB_NAME;

      const meetingId = req?.body?.meetingId;
      const notes_type = req?.body?.noteType;

      const therapistId = req?.user?._id;

      if(!meetingId){
        return res.sendError("Invalid note meeting id");
      }

      if(!notes_type || (notes_type !== "SOAP" && notes_type !== "PIE")){
        return res.sendError("Invalid note type");
      }

      if(!therapistId){
        return res.sendError("Invalid user id");
      }

      const updatedTherapist = await Therapist.findOneAndUpdate(
        { _id: therapistId },
        { $set: { aiNotesType: notes_type } },
        { new: true }
      );

      if(!updatedTherapist){
        return res.sendError("Invalid therapist");
      }

      const meetingOriginData = await Meeting.findOne({ meetingId });

      if (!meetingOriginData) {
        return res.sendError("Invalid meeting id.");
      }

      const transcribeData = await VideoCallDao.getTranscribeByMeetingId(
        meetingOriginData?.meetingId
      );

      if (!transcribeData || transcribeData?.transcriptText.length == 0) {
        return res.sendError(
          "Unable to find transcribe data for this meeting.",
          404
        );
      }

      let dataExtracted: any;

      var spawn = require("child_process").spawn;

      let platform = process.platform === "win32" ? "python" : "python3";

      let process1 = spawn(
        platform,
        [
          "./src/python_script_new/main.py",
          openAIAPIKey,
          meetingOriginData._id,
          mongoConnectString,
          mongoDBName,
          notes_type,
        ],
        { shell: true }
      );

      process1.stdout.on("data", async function (data: any) {
        let processedText = data
          .toString()
          .replace(/(\r\n|\n|\r)/gm, "")
          .replace(/ :/g, ":")
          .replace(/\*/g, " ")
          .trim();

        await VideoCallDao.getTranscribeByMeetingIdAndUpdateAIResponse(
          meetingId,
          processedText
        );
  
        if (notes_type === "PIE") {

          let problem;
          var subArray1 = processedText.split("P (Problem):");
          subArray1.shift();
          var sub1 = subArray1.join("P (Problem):");
          problem = sub1.split("I (Intervention):").shift();

          if (problem == "" || problem == null) {
            var subArray1 = processedText.split("P");
            subArray1.shift();
            var sub1 = subArray1.join("P");
            problem = sub1.split("I ").shift();
          }

          let intervention;
          var subArray2 = processedText.split("I (Intervention):");
          subArray2.shift();
          var sub2 = subArray2.join("I (Intervention):");
          intervention = sub2.split("E (Evaluation):").shift();

          if (intervention == "" || intervention == null) {
            var subArray2 = processedText.split("I");
            subArray2.shift();
            var sub2 = subArray2.join("I");
            intervention = sub2.split("E ").shift();
          }

          let evaluation;
          var subArray3 = processedText.split("E (Evaluation):");
          subArray3.shift();
          evaluation = subArray3.join("E (Evaluation):");

          if (evaluation == "" || evaluation == null) {
            var subArray3 = processedText.split("E ");
            subArray3.shift();
            evaluation = subArray3.join("E ");
          }

          dataExtracted = {
            problem: problem.trim(),
            intervention: intervention.trim(),
            evaluation: evaluation.trim(),
          };
        } else if (notes_type === "SOAP") {

          let subjective;
          var subArray1 = processedText.split("S (Subjective):");
          subArray1.shift();
          var sub1 = subArray1.join("S (Subjective):");
          subjective = sub1.split("O (Objective):").shift();

          if (subjective == "" || subjective == null) {
            var subArray1 = processedText.split("S");
            subArray1.shift();
            var sub1 = subArray1.join("S");
            subjective = sub1.split("O ").shift();
          }

          let objective;
          var subArray2 = processedText.split("O (Objective):");
          subArray2.shift();
          var sub2 = subArray2.join("O (Objective):");
          objective = sub2.split("A (Assessment):").shift();

          if (objective == "" || objective == null) {
            var subArray2 = processedText.split("O");
            subArray2.shift();
            var sub2 = subArray2.join("O");
            objective = sub2.split("A ").shift();
          }

          let assessment;
          var subArray3 = processedText.split("A (Assessment):");
          subArray3.shift();
          var sub3 = subArray3.join("A (Assessment):");
          assessment = sub3.split("P (Plan):").shift();

          if (assessment == "" || assessment == null) {
            var subArray3 = processedText.split("A");
            subArray3.shift();
            var sub3 = subArray3.join("A");
            assessment = sub3.split("P ").shift();
          }

          let plan;
          var subArray4 = processedText.split("P (Plan):");
          subArray4.shift();
          plan = subArray4.join("P (Plan):");

          if (plan == "" || plan == null) {
            var subArray4 = processedText.split("P ");
            subArray4.shift();
            plan = subArray4.join("P ");
          }

          dataExtracted = {
            subjective: subjective.trim(),
            objective: objective.trim(),
            assessment: assessment.trim(),
            plan: plan.trim(),
          };

        }
      });

      process1.on("close", (code: any) => {
        if (code === 0) {
          if (dataExtracted) {
            dataExtracted.user = updatedTherapist
            if (notes_type === "PIE") {
              return res.sendSuccess(
                dataExtracted,
                "Successfully Generated PIE Notes."
              );
            } else if (notes_type === "SOAP") {

              return res.sendSuccess(
                dataExtracted,
                "Successfully Generated SOAP Notes."
              );
            }
          } else {
            return res.sendError(
              "Failed to generate note due to the unavailability of the AI response."
            );
          }
        } else {
          return res.sendError(
            `Failed to generate notes. Error code: ${code}`
          );
        }
      });

      process1.stderr.on("data", (data: any) => {
        AppLogger.error(`AI Generated Note Error: ${data}`);
        return res.sendError(
          `AI Generated Note Error: ${data}`
        );
      });

    } catch (error) {
      return res.sendError(
        `Failed to generate notes. Details: ${error}`
      );
    }
  }

  export async function generateSopeOrPIEWithOpenAIForAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const openAIAPIKey = process.env.OPEN_AI_API_KEY;
      const mongoConnectString = process.env.MONGOOSE_URI;
      const mongoDBName = process.env.MONGO_DB_NAME;

      const meetingId = req?.body?.meetingId;
      const notes_type = req?.body?.noteType;

      if(!meetingId){
        return res.sendError("Invalid note meeting id");
      }

      if(!notes_type || (notes_type !== "SOAP" && notes_type !== "PIE")){
        return res.sendError("Invalid note type");
      }

      const meetingOriginData = await Meeting.findOne({ meetingId });

      if (!meetingOriginData) {
        return res.sendError("Invalid meeting id.");
      }

      const transcribeData = await VideoCallDao.getTranscribeByMeetingId(
        meetingOriginData?.meetingId
      );

      if (!transcribeData || transcribeData?.transcriptText.length == 0) {
        return res.sendError(
          "Unable to find transcribe data for this meeting.",
          404
        );
      }

      let dataExtracted: any;

      var spawn = require("child_process").spawn;

      let platform = process.platform === "win32" ? "python" : "python3";
      // let platform = "/Users/<USER>/mylavni/lavni-api/venv/bin/python"


      let process1 = spawn(
        platform,
        [
          "./src/python_script_new/main.py",
          openAIAPIKey,
          meetingOriginData._id,
          mongoConnectString,
          mongoDBName,
          notes_type,
        ],
        { shell: true }
      );

      process1.stdout.on("data", async function (data: any) {
        let processedText = data
          .toString()
          .replace(/(\r\n|\n|\r)/gm, "")
          .replace(/ :/g, ":")
          .replace(/\*/g, " ")
          .trim();

        await VideoCallDao.getTranscribeByMeetingIdAndUpdateAIResponse(
          meetingId,
          processedText
        );
  
        if (notes_type === "PIE") {

          let problem;
          var subArray1 = processedText.split("P (Problem):");
          subArray1.shift();
          var sub1 = subArray1.join("P (Problem):");
          problem = sub1.split("I (Intervention):").shift();

          if (problem == "" || problem == null) {
            var subArray1 = processedText.split("P");
            subArray1.shift();
            var sub1 = subArray1.join("P");
            problem = sub1.split("I ").shift();
          }

          let intervention;
          var subArray2 = processedText.split("I (Intervention):");
          subArray2.shift();
          var sub2 = subArray2.join("I (Intervention):");
          intervention = sub2.split("E (Evaluation):").shift();

          if (intervention == "" || intervention == null) {
            var subArray2 = processedText.split("I");
            subArray2.shift();
            var sub2 = subArray2.join("I");
            intervention = sub2.split("E ").shift();
          }

          let evaluation;
          var subArray3 = processedText.split("E (Evaluation):");
          subArray3.shift();
          evaluation = subArray3.join("E (Evaluation):");

          if (evaluation == "" || evaluation == null) {
            var subArray3 = processedText.split("E ");
            subArray3.shift();
            evaluation = subArray3.join("E ");
          }

          dataExtracted = {
            problem: problem.trim(),
            intervention: intervention.trim(),
            evaluation: evaluation.trim(),
          };
        } else {
          let subjective;
          var subArray1 = processedText.split("S (Subjective):");
          subArray1.shift();
          var sub1 = subArray1.join("S (Subjective):");
          subjective = sub1.split("O (Objective):").shift();

          if (subjective == "" || subjective == null) {
            var subArray1 = processedText.split("S");
            subArray1.shift();
            var sub1 = subArray1.join("S");
            subjective = sub1.split("O ").shift();
          }

          let objective;
          var subArray2 = processedText.split("O (Objective):");
          subArray2.shift();
          var sub2 = subArray2.join("O (Objective):");
          objective = sub2.split("A (Assessment):").shift();

          if (objective == "" || objective == null) {
            var subArray2 = processedText.split("O");
            subArray2.shift();
            var sub2 = subArray2.join("O");
            objective = sub2.split("A ").shift();
          }

          let assessment;
          var subArray3 = processedText.split("A (Assessment):");
          subArray3.shift();
          var sub3 = subArray3.join("A (Assessment):");
          assessment = sub3.split("P (Plan):").shift();

          if (assessment == "" || assessment == null) {
            var subArray3 = processedText.split("A");
            subArray3.shift();
            var sub3 = subArray3.join("A");
            assessment = sub3.split("P ").shift();
          }

          let plan;
          var subArray4 = processedText.split("P (Plan):");
          subArray4.shift();
          plan = subArray4.join("P (Plan):");

          if (plan == "" || plan == null) {
            var subArray4 = processedText.split("P ");
            subArray4.shift();
            plan = subArray4.join("P ");
          }

          dataExtracted = {
            subjective: subjective.trim(),
            objective: objective.trim(),
            assessment: assessment.trim(),
            plan: plan.trim(),
          };
        }
      });

      process1.on("close", (code: any) => {
        if (code === 0) {
          if (dataExtracted) {
            if (notes_type === "PIE") {
              return res.sendSuccess(
                dataExtracted,
                "Successfully Generated PIE Notes by Admin."
              );
            } else if (notes_type === "SOAP") {
              return res.sendSuccess(
                dataExtracted,
                "Successfully Generated SOAP Notes by Admin."
              );
            }
          } else {
            return res.sendError(
              "Failed to generate note due to the unavailability of the AI response."
            );
          }
        } else {
          return res.sendError(
            `Failed to generate notes. Error code: ${code}`
          );
        }
      });

      process1.stderr.on("data", (data: any) => {
        AppLogger.error(`AI Generated Note Error by Admin: ${data}`);
        return res.sendError(
          `AI Generated Note Error: ${data}`
        );
      });

    } catch (error) {
      return res.sendError(
        `Failed to generate notes. Details: ${error}`
      );
    }
  }

  function htmlToTextFunction(htmlTextMessage: string) {
    try {
      if (htmlTextMessage) {
        const finalText = htmlToText(htmlTextMessage);
        return finalText;
      }
      return "";
    } catch (error) {
      return "";
    }
  }

  //start
    export async function downloadPIEDiagnosisNoteById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      let previousNote, populated;
      const noteId = req?.params?.noteId;
      const isVersion: boolean = req?.body?.isVersion ?? false;

      if (!noteId || !mongoose.Types.ObjectId.isValid(noteId)) {
        return res.sendError("Note id is required.");
      }

      // Conditionally use DiagnosisNote or DiagnosisNoteVersion based on isVersion
      if (!isVersion) {
        previousNote = await DiagnosisNote.findById(noteId);
        populated = await VideoCallDao.getDiagnosisNoteByIdFunction(noteId);
      } else {
        previousNote = await DiagnosisNoteVersion.findById(noteId);
        populated = await FormVersionDao.getDiagnosisNoteVersionByIdFunction(noteId);
      }

      // const previousNote = await DiagnosisNote.findById(noteId);

      if (!previousNote) {
        return res.sendError("Invalid note id.");
      }

      if (
        req?.user?.role == UserRole.THERAPIST &&
        previousNote?.therapistId.toString() != req?.user?._id.toString()
      ) {
        return res.sendError("You don't have access to download this note!");
      }

      if (!previousNote?.updatedByTherapist) {
        return res.sendError("This note is not updated yet!");
      }

      const therapistDetails = await Therapist.findById(previousNote?.therapistId);

      if(!therapistDetails){
        return res.sendError("Therapist for this note is not found!");
      }

      // const populated = await VideoCallDao.getDiagnosisNoteByIdFunction(
      //   noteId
      // );

      const dataForSend = {
        clientId: populated.clientId,
        therapistId: populated.therapistId,
        meetingId: populated.meetingId,
        updated: populated.updated,
        updatedByTherapist: populated.updatedByTherapist,
        patientID: populated.patientID,
        patientAcountNo: populated.patientAcountNo,
        encounterID: populated.encounterID,
        encounterDate: populated.encounterDate,
        encounterType: populated.encounterType,
        chiefComplaint: populated.chiefComplaint,
        historyOfPresentIllness: populated.historyOfPresentIllness,
        historyOfPresentIllnessAttachments:
          populated.historyOfPresentIllnessAttachments,
        diagnosisICDcodes: populated.diagnosisICDcodes,
        mentalBehavioralStatus: populated.mentalBehavioralStatus,
        mentalBehavioralStatusAttachments:
          populated.mentalBehavioralStatusAttachments,
        asssessments: populated.asssessments,
        cptCode: populated.cptCode,
        assessmentAttachments: populated.assessmentAttachments,
        procedureNotes: populated.procedureNotes,
        signature: (therapistDetails?.signature && req?.user?.role == UserRole.THERAPIST && therapistDetails?._id.toString() == req?.user?._id.toString()) ? therapistDetails?.signature : populated.signature,
        carePlan: populated.carePlan,
        carePlanAttachments: populated.carePlanAttachments,
        selectedGoals: populated.selectedGoals,
        intervention: populated.intervention,
        secondaryDiagnosisICDcodes: populated.secondaryDiagnosisICDcodes,
      };

      let obj = JSON.stringify(dataForSend);

      let pdfData = JSON.parse(obj);

      const cptCodeList = [
        {
          cptCode: "-1",
          cptCodeDescription: "Select",
        },
        {
          cptCode: "90791",
          cptCodeDescription: "Psychiatric diagnostic evaluation",
        },
        {
          cptCode: "90792",
          cptCodeDescription:
            "Psychiatric diagnostic evaluation with medical services",
        },
        {
          cptCode: "90832",
          cptCodeDescription: "Psychotherapy, 16-37 minutes",
        },
        {
          cptCode: "90833",
          cptCodeDescription:
            "Psychotherapy, 16-37 minutes with E/M service, listed seperately",
        },
        {
          cptCode: "90834",
          cptCodeDescription: "Psychotherapy, 38-52 minutes",
        },
        {
          cptCode: "90836",
          cptCodeDescription:
            "Psychotherapy, 38-52 minutes with E/M service, listed seperately",
        },
        {
          cptCode: "90837",
          cptCodeDescription: "Psychotherapy, 53+ minutes",
        },
        {
          cptCode: "90838",
          cptCodeDescription:
            "Psychotherapy, 53+ minutes with E/M service, listed seperately",
        },
        {
          cptCode: "90839",
          cptCodeDescription: "Psychotherapy for crisis, 30-74 minutes",
        },
        {
          cptCode: "90840",
          cptCodeDescription:
            "Psychotherapy for crisis, each additional 30 minutes beyond initial 74min, upto two add-ons per 90839",
        },
        {
          cptCode: "90846",
          cptCodeDescription: "Family psytx w/o patient 50 minutes",
        },
        {
          cptCode: "90847",
          cptCodeDescription: "Family psytx w/patient 50 minutes",
        },
        {
          cptCode: "90853",
          cptCodeDescription: "Group psychotherapy",
        },
        {
          cptCode: "00001",
          cptCodeDescription: "Non Billable",
        },
      ];

      let fonts = {
        Courier: {
          normal: "Courier",
          bold: "Courier-Bold",
          italics: "Courier-Oblique",
          bolditalics: "Courier-BoldOblique",
        },
        Helvetica: {
          normal: "Helvetica",
          bold: "Helvetica-Bold",
          italics: "Helvetica-Oblique",
          bolditalics: "Helvetica-BoldOblique",
        },
        Times: {
          normal: "Times-Roman",
          bold: "Times-Bold",
          italics: "Times-Italic",
          bolditalics: "Times-BoldItalic",
        },
        Symbol: {
          normal: "Symbol",
        },
        ZapfDingbats: {
          normal: "ZapfDingbats",
        },
      };

      let docDefinition = {
        content: [
          {
            image: "src/assets/images/logo.png",
            width: 150,
            alignment: "center",
            margin: [0, 0, 0, 20],
          },
          {
            text: [
              "Diagnosis Report ",
              moment(pdfData.meetingId.regularMeetingDate || pdfData.meetingId.createdAt || null).format(
                "YYYY-MM-DD"
              ),
              "\n\n",
            ],
            style: "header",
            alignment: "center",
            margin: [0, 0, 0, 20],
          },
          // {
          //   text: [
          //     "Duration ",
          //     ((pdfData?.meetingId?.spentDuration && !isNaN(pdfData?.meetingId?.spentDuration)) ? Math.floor(pdfData?.meetingId?.spentDuration) : "00"),
          //     " mins",
          //     "\n\n\n",
          //   ],
          //   style: "subheader",
          //   alignment: "center",
          //   margin: [0, 0, 0, 20],
          // },
          {
            columns: [
              {
                text: "Insurance Details \n\n",
              },
              {
                text: "Client Details \n\n",
              },
            ],
            alignment: "justify",
            style: "subheader",
          },
          {
            columns: [
              {
                text: [
                  {
                    text: "Receiver\n\n",
                    fontSize: 13,
                    bold: true,
                    margin: [0, 20],
                  },
                  { text: "Organization : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.insuranceCompanyId
                      ?.insuranceCompany,
                    fontSize: 11,
                  },
                  "\n\n\n",
                  { text: "Subscriber\n\n", fontSize: 13, bold: true },
                  { text: "Member Id : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber
                      ?.memberId,
                    fontSize: 11,
                  },
                  "\n\n",
                  {
                    text: "Payment Responsibility Level Code : ",
                    fontSize: 11,
                    bold: true,
                  },
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber
                      ?.paymentResponsibilityLevelCode,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "First Name : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber
                      ?.firstName,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Last Name : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber
                      ?.lastName,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Gender : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber?.gender,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Date of Birth : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber
                      ?.dateOfBirth,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Policy Number : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber
                      ?.policyNumber,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Address : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber?.address
                      ?.address1,
                    fontSize: 11,
                  },
                  pdfData?.clientId?.insuranceId?.subscriber?.address
                    ?.address1
                    ? " , "
                    : "",
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber?.address
                      ?.city,
                    fontSize: 11,
                  },
                  pdfData?.clientId?.insuranceId?.subscriber?.address?.city
                    ? " , "
                    : " ",
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber?.address
                      ?.state,
                    fontSize: 11,
                  },
                  pdfData?.clientId?.insuranceId?.subscriber?.address?.state
                    ? " , "
                    : " ",
                  {
                    text: pdfData?.clientId?.insuranceId?.subscriber?.address
                      ?.postalCode,
                    fontSize: 11,
                  },
                  "\n\n\n",
                  { text: "Dependent\n\n", fontSize: 13, bold: true },
                  { text: "Member Id : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent?.memberId,
                    fontSize: 11,
                  },
                  "\n\n",
                  {
                    text: "Payment Responsibility Level Code : ",
                    fontSize: 11,
                    bold: true,
                  },
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent
                      ?.paymentResponsibilityLevelCode,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "First Name : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent
                      ?.firstName,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Last Name : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent?.lastName,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Gender : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent?.gender,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Date of Birth : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent
                      ?.dateOfBirth,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Policy Number : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent
                      ?.policyNumber,
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Address : ", fontSize: 11, bold: true },
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent?.address
                      ?.address1,
                    fontSize: 11,
                  },
                  pdfData?.clientId?.insuranceId?.dependent?.address?.address1
                    ? ", "
                    : "",
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent?.address
                      ?.city,
                    fontSize: 11,
                  },
                  pdfData?.clientId?.insuranceId?.dependent?.address?.city
                    ? ", "
                    : " ",
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent?.address
                      ?.state,
                    fontSize: 11,
                  },
                  pdfData?.clientId?.insuranceId?.dependent?.address?.state
                    ? ", "
                    : " ",
                  {
                    text: pdfData?.clientId?.insuranceId?.dependent?.address
                      ?.postalCode,
                    fontSize: 11,
                  },
                  "\n\n\n",
                ],
              },
              {
                text: [
                  { text: "Name : ", fontSize: 11, bold: true },
                  { text: pdfData?.clientId?.firstname, fontSize: 11 },
                  " ",
                  { text: pdfData?.clientId?.lastname, fontSize: 11 },
                  "\n\n",
                  { text: "Patient Id : ", fontSize: 11, bold: true },
                  { text: pdfData?.patientID, fontSize: 11 },
                  "\n\n",
                  { text: "Date of Birth : ", fontSize: 11, bold: true },
                  {
                    text: moment(
                      pdfData?.clientId?.dateOfBirth || null
                    ).format("YYYY-MM-DD"),
                    fontSize: 11,
                  },
                  "\n\n",
                  { text: "Address : ", fontSize: 11, bold: true },
                  { text: pdfData?.clientId?.streetAddress, fontSize: 11 },
                  pdfData?.clientId?.streetAddress ? ", " : "",
                  { text: pdfData?.clientId?.city, fontSize: 11 },
                  pdfData?.clientId?.city ? ", " : "",
                  { text: pdfData?.clientId?.state, fontSize: 11 },
                  "\n\n",
                  { text: "Gender : ", fontSize: 11, bold: true },
                  { text: pdfData?.clientId?.gender, fontSize: 11 },
                  "\n\n",
                  { text: "Account Number : ", fontSize: 11, bold: true },
                  { text: pdfData?.clientId?.patientAcountNo, fontSize: 11 },
                  "\n\n",
                  { text: "Account Id : ", fontSize: 11, bold: true },
                  { text: pdfData?.clientId?.encounterType, fontSize: 11 },
                  "\n\n\n",
                ],
              },
            ],
            alignment: "justify",
          },
          {
            text: [
              { text: "PROBLEM ", fontSize: 15, bold: true },
              "\n\n\n",
              // { text: " / ", fontSize: 15, bold: true },
              // // { text: "What the client tells you \n\n\n", fontSize: 11 },
              // { text: "Chief Complaint : \n\n", fontSize: 12, bold: true },
              { text: htmlToTextFunction(pdfData?.chiefComplaint), fontSize: 12 },
              "\n\n\n",
              // {
              //   text: "History Of Present Illness : \n\n",
              //   fontSize: 12,
              //   bold: true,
              // },
              // { text: htmlToTextFunction(pdfData?.historyOfPresentIllness), fontSize: 12 },
              // "\n\n\n",
              { text: "INTERVENTION", fontSize: 15, bold: true },
                  "\n\n\n",
              // { text: " / ", fontSize: 15, bold: true },
              // { text: "Observable or measurable data \n\n\n", fontSize: 11 },
              // {
              //   text: "Mental / Behavioral Status : \n\n",
              //   fontSize: 12,
              //   bold: true,
              // },
              { text: htmlToTextFunction(pdfData?.mentalBehavioralStatus), fontSize: 12 },
              "\n\n\n",
              { text: "EVALUATION", fontSize: 15, bold: true },
              { text: "\n\n\n" },
              { text: "CPT Code : \n\n", fontSize: 12, bold: true },
              {
                text:
                  pdfData?.cptCode &&
                  cptCodeList.filter(
                    (code) => code.cptCode === pdfData.cptCode
                  )[0].cptCodeDescription,
                fontSize: 12,
              },
              "\n\n\n",
              {
                text: "Diagnosis ICD 10 Codes : \n\n",
                fontSize: 12,
                bold: true,
              },
              {
                text: pdfData?.diagnosisICDcodes.map(
                  (text: any) => text.label + "\n\n"
                ),
                fontSize: 12,
              },
              "\n\n",
              {
                text: "Secondary Diagnosis ICD 10 Codes : \n\n",
                fontSize: 12,
                bold: true,
              },
              {
                text: pdfData?.secondaryDiagnosisICDcodes.map(
                  (text: any) => text.label + "\n\n"
                ),
                fontSize: 12,
              },
              "\n\n",
              // { text: "Assessments : \n\n", fontSize: 12, bold: true },
              // { text: htmlToTextFunction(pdfData?.asssessments), fontSize: 12 },
              // "\n\n\n",
              // { text: "PLAN", fontSize: 15, bold: true },
              // { text: "\n\n\n" },
              // {
              //   text: "Procedure / Intervention Notes : \n\n",
              //   fontSize: 12,
              //   bold: true,
              // },
              // {
              //   text: htmlToTextFunction(pdfData?.procedureNotes),
              //   fontSize: 12,
              // },
              "\n\n\n",
              { text: "Care Plan : \n\n", fontSize: 12, bold: true },
              {
                text: htmlToTextFunction(pdfData?.carePlan),
                fontSize: 12,
              },
              "\n\n\n",
              { text: "Goals : \n\n", fontSize: 12, bold: true },
              {
                text:
                  pdfData?.selectedGoals.length > 0 &&
                  pdfData?.selectedGoals.map((text: any) => text + "\n\n"),
                fontSize: 12,
                pageBreak: "after",
              },
              {
                text: !previousNote?.selectedGoals
                  ? htmlToTextFunction(pdfData?.asssessments)
                  : " ",
                fontSize: 12,
              },
              { text: !previousNote?.selectedGoals ? "\n\n" : " " },
              "\n",
              { text: "Signature : \n\n", fontSize: 12, bold: true },
              "\n\n",
            ],
          },
          {
            image: pdfData?.signature && pdfData?.signature.length > 0 ? pdfData?.signature : "src/assets/images/Dotted-Line-PNG-File.jpg",
            width: 200,
          },
          "\n\n\n",
          {
            text: [
              { text: "PROVIDER", fontSize: 15, bold: true },
              { text: "\n\n\n" },
              { text: "Therapist Name : ", fontSize: 11, bold: true },
              { text: pdfData?.therapistId?.firstname, fontSize: 11 },
              " ",
              { text: pdfData?.therapistId?.lastname, fontSize: 11 },
              "\n\n",
              { text: "NPI 1 : ", fontSize: 11, bold: true },
              { text: pdfData?.therapistId?.nPI1, fontSize: 11 },
              "\n\n",
              { text: "NPI 2 : ", fontSize: 11, bold: true },
              { text: pdfData?.therapistId?.nPI2, fontSize: 11 },
              "\n\n",
              { text: "license : ", fontSize: 11, bold: true },
              { text: pdfData?.therapistId?.license, fontSize: 11 },
              "\n\n",
              { text: "Taxonomy Code : ", fontSize: 11, bold: true },
              { text: pdfData?.therapistId?.taxonomyCode, fontSize: 11 },
              "\n\n",
              { text: "Address : ", fontSize: 11, bold: true },
              { text: pdfData?.therapistId?.streetAddress, fontSize: 11 },
              pdfData?.therapistId?.streetAddress ? ", " : "",
              { text: pdfData?.therapistId?.city, fontSize: 11 },
              pdfData?.therapistId?.city ? ", " : "",
              { text: pdfData?.therapistId?.state, fontSize: 11 },
              "\n\n\n",
            ],
          },
        ],
        styles: {
          header: {
            fontSize: 18,
            bold: true,
          },
          subheader: {
            fontSize: 15,
            bold: true,
          },
          quote: {
            italics: true,
          },
          small: {
            fontSize: 8,
          },
          superMargin: {
            margin: [20, 0, 40, 0],
            fontSize: 15,
          },
        },
        defaultStyle: {
          font: "Helvetica",
        },
      };
      
      const printer = new PdfPrinter(fonts);

      const pdfDoc = printer.createPdfKitDocument(docDefinition);

      const filePath = process.env.DIAGONOSIS_NOTE_DOWNLOAD_PATH;

      const writeStream = fs.createWriteStream(filePath);

      pdfDoc.pipe(writeStream);
      pdfDoc.end();

      writeStream.on("finish", () => {
        return res.sendSuccess(dataForSend, "Success");
      });

      writeStream.on("error", (err:any) => {
        return res.sendError("Error saving pdf");
      });

      
    } catch (error) {
      return res.sendError(error);
    }
  }

  //end
}
