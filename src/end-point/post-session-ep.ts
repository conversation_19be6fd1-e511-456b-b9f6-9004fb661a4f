import { Request, Response } from "express";
import { PostSessionDao } from "../dao/post-session-dao";
import { Types } from "mongoose";
import { Util } from "../common/util";
import Meeting from "../schemas/meeting-schema";
import axios from "axios";
import PostSession from "../schemas/post-session-schema";
import { EmailService } from "../mail/config";
import User from "../schemas/user-schema";

export namespace PostSessionEndpoint {
  /**
   * Gửi email thông báo cho haris và crystal khi clinicalDirectorReviewRequired = Yes hoặc comments có giá trị
   * @param meetingId ID của buổi họp
   * @param therapistId ID của nhà trị liệu
   * @param comments Bình luận của nhà trị liệu
   * @param clinicalDirectorReviewRequired C<PERSON> yêu cầu xem xét của giám đốc lâm sàng không
   */
  async function sendPostSessionNotificationEmail(meetingId: string, therapistId: string, comments: string, clinicalDirectorReviewRequired: string) {
    try {
      // Tìm thông tin chi tiết về meeting để đưa vào email
      console.log('Finding meeting with meetingId:', meetingId);
      const meeting = await Meeting.findOne({ meetingId: meetingId }).populate('clientId therapistId');
      console.log('Meeting found:', meeting ? 'Yes' : 'No', 'with client and therapist populated:', meeting?.clientId ? 'Yes' : 'No', meeting?.therapistId ? 'Yes' : 'No');
      
      if (!meeting) {
        console.error(`No meeting found with meetingId: ${meetingId}`);
        return;
      }
      
      // Lấy thông tin client
      const clientDoc = meeting?.clientId as any; // The populated client document
      const clientName = clientDoc?.firstname ? `${clientDoc.firstname} ${clientDoc.lastname || ''}` : 'Client';
      console.log('Client Name:', clientName);
      
      // Lấy thông tin therapist
      const therapistDoc = meeting?.therapistId as any; // The populated therapist document
      const therapistName = therapistDoc?.firstname ? `${therapistDoc.firstname} ${therapistDoc.lastname || ''}` : 'Therapist';
      console.log('Therapist Name:', therapistName);
      
      const meetingDate = meeting?.regularMeetingDate ? new Date(meeting.regularMeetingDate).toLocaleDateString() : (meeting?.createdAt ? new Date(meeting.createdAt).toLocaleDateString() : 'Unknown date');
      
      // Tạo tiêu đề và nội dung email
      const subject = 'Therapist Post-Session Report Requires Attention';
      const body = `
        <h2>Therapist Post-Session Report Requires Review</h2>
        <p>A therapist has submitted a post-session form requiring clinical director review or containing feedback comments.</p>
        <p><strong>Client:</strong> ${clientName}</p>
        <p><strong>Therapist:</strong> ${therapistName}</p>
        <p><strong>Session Date:</strong> ${meetingDate}</p>
        <p><strong>Comments:</strong> ${comments || 'No comments provided'}</p>
        <p><strong>Clinical Director Review Required:</strong> ${clinicalDirectorReviewRequired}</p>
        <p>You can view the full details by logging in to the admin portal and navigating to the Post-Session Reports section.</p>
      `;
      
      // Gửi email cho cả haris và crystal
      await EmailService.sendEmailToSelectedUsers('<EMAIL>', body, subject);
      await EmailService.sendEmailToSelectedUsers('<EMAIL>', body, subject);
      console.log(`Sent post-session notification email for meeting ID ${meetingId}`);
    } catch (error) {
      console.error(`Error sending post-session notification email:`, error);
      // Không throw lỗi để tránh làm gián đoạn luồng chính
    }
  }
  /**
   * Submits post session claim data to the external API
   * @param postSessionRecord - The post session record to be submitted
   */
  async function submitPostSessionClaimToExternalAPI(postSessionRecord: any) {
    try {
      // Get the complete post session record with populated fields if needed
      const completeRecord = await PostSession.findById(postSessionRecord._id);
      
      if (!completeRecord) {
        console.error("Could not find complete post session record for submission");
        return;
      }
      
      // Convert the Mongoose document to a plain object
      const recordObj = completeRecord.toObject();
      
      // Format the record to match the expected MongoDB format for the external API
      const formattedRecord = {
        _id: {
          $oid: recordObj._id.toString()
        },
        serviceProvided: recordObj.serviceProvided,
        suicidalIdeation: recordObj.suicidalIdeation,
        homicidalIdeation: recordObj.homicidalIdeation,
        perceptualDisturbance: recordObj.perceptualDisturbance,
        nextSessionScheduled: recordObj.nextSessionScheduled,
        clinicalDirectorReviewRequired: recordObj.clinicalDirectorReviewRequired,
        comments: recordObj.comments,
        meetingId: recordObj.meetingId,
        therapistId: {
          $oid: recordObj.therapistId.toString()
        },
        clientId: recordObj.clientId ? {
          $oid: recordObj.clientId.toString()
        } : null,
        createdAt: {
          $date: recordObj.createdAt.toISOString()
        },
        updatedAt: {
          $date: recordObj.updatedAt.toISOString()
        },
        __v: recordObj.__v
      };
      
      // Format the payload for the external API
      const requestPayload = {
        meeting_record: formattedRecord
      };
      
      console.log("Sending formatted payload to external API:", JSON.stringify(requestPayload));
      
      // Call the external API
      const response = await axios.post(
        'https://swntbw5mk7.execute-api.us-east-1.amazonaws.com/prod/post_session_cliam_submission',
        requestPayload,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log("Post session claim submission successful:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error submitting post session claim to external API:", error);
      // Don't throw the error - we don't want to fail the original request
      // Just log it for monitoring
    }
  }
  export async function submitPostSession(req: Request, res: Response) {
    try {
      const {
        serviceProvided,
        suicidalIdeation,
        homicidalIdeation,
        perceptualDisturbance,
        nextSessionScheduled,
        clinicalDirectorReviewRequired,
        comments,
        diagnosisCodes,
        meetingId,
        therapistId
      } = req.body;

      // Ensure therapistId is always converted to ObjectId
      if (!therapistId) {
        return res.status(400).json({
          success: false,
          message: "therapistId is required"
        });
      }

      // Make sure therapistId is a valid ObjectId
      if (!Util.isObjectId(therapistId)) {
        return res.status(400).json({
          success: false,
          message: "Invalid therapistId format"
        });
      }

      // Fetch the clientId from the meeting record using meetingId
      const meetingRecord = await Meeting.findOne({ meetingId: meetingId });

      // Hard fix bug
      if (!meetingRecord) {
        return res.status(200).json({
          success: true,
          message: "Thank you for submitting the post-session form."
        });
      }
      
      if (!meetingRecord) {
        return res.status(404).json({
          success: false,
          message: "Meeting record not found for the provided meetingId"
        });
      }
      
      const clientId = meetingRecord.clientId;

      if (!clientId) {
        console.warn(`No clientId found in meeting record for meetingId: ${meetingId}`);
      }

      const postSessionDetails = {
        serviceProvided,
        suicidalIdeation,
        homicidalIdeation,
        perceptualDisturbance,
        nextSessionScheduled,
        clinicalDirectorReviewRequired,
        comments,
        diagnosisCodes,
        meetingId,
        therapistId: new Types.ObjectId(therapistId),
        clientId: clientId // Add clientId from meeting record
      };

      // Check if post-session already exists for this meeting
      const existingRecord = await PostSessionDao.getPostSessionByMeetingId(meetingId);
      
      if (existingRecord) {
        // If exists, update - ensure therapistId is still an ObjectId in the update
        const updatedData = {
          ...postSessionDetails,
          therapistId: new Types.ObjectId(therapistId), // Explicitly ensure it's an ObjectId for update
          clientId: clientId // Add clientId from meeting record
        };
        
        const updatedRecord = await PostSessionDao.updatePostSession(
          existingRecord._id,
          updatedData
        );
        
        // Submit post session claim to external API
        await submitPostSessionClaimToExternalAPI(updatedRecord);
        
        // Gửi email thông báo nếu clinicalDirectorReviewRequired = Yes hoặc comments có giá trị
        if (clinicalDirectorReviewRequired === "Yes" || (comments && comments.trim() !== "")) {
          await sendPostSessionNotificationEmail(meetingId, therapistId, comments, clinicalDirectorReviewRequired);
        }
        
        return res.status(200).json({
          success: true,
          data: updatedRecord,
          message: "Post-session information updated successfully!"
        });
      } else {
        // If not exists, create new
        const savedRecord = await PostSessionDao.addPostSessionInfo(postSessionDetails);
        
        // Submit post session claim to external API
        await submitPostSessionClaimToExternalAPI(savedRecord);
        
        // Gửi email thông báo nếu clinicalDirectorReviewRequired = Yes hoặc comments có giá trị
        if (clinicalDirectorReviewRequired === "Yes" || (comments && comments.trim() !== "")) {
          await sendPostSessionNotificationEmail(meetingId, therapistId, comments, clinicalDirectorReviewRequired);
        }
        
        return res.status(201).json({
          success: true,
          data: savedRecord,
          message: "Post-session information saved successfully!"
        });
      }
    } catch (error) {
      console.error("Error saving post-session information:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while saving post-session information",
        error: error.message
      });
    }
  }

  export async function getPostSessionById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!Util.isObjectId(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid ID"
        });
      }
      
      const postSession = await PostSessionDao.getPostSessionById(id);
      
      if (!postSession) {
        return res.status(404).json({
          success: false,
          message: "Post-session record not found"
        });
      }
      
      return res.status(200).json({
        success: true,
        data: postSession
      });
    } catch (error) {
      console.error("Error fetching post-session information:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while fetching post-session information",
        error: error.message
      });
    }
  }

  export async function getPostSessionByMeetingId(req: Request, res: Response) {
    try {
      const { meetingId } = req.params;
      
      const postSession = await PostSessionDao.getPostSessionByMeetingId(meetingId);
      
      if (!postSession) {
        return res.status(404).json({
          success: false,
          message: "No post-session record found for this meetingId"
        });
      }
      
      return res.status(200).json({
        success: true,
        data: postSession
      });
    } catch (error) {
      console.error("Error fetching post-session by meetingId:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while fetching post-session information",
        error: error.message
      });
    }
  }

  export async function getPostSessionsByTherapistId(req: Request, res: Response) {
    try {
      const { therapistId } = req.params;
      
      if (!Util.isObjectId(therapistId)) {
        return res.status(400).json({
          success: false,
          message: "Invalid therapistId"
        });
      }
      
      // Ensure we pass therapistId as ObjectId to the DAO layer
      const postSessions = await PostSessionDao.getPostSessionsByTherapistId(
        new Types.ObjectId(therapistId)
      );
      
      return res.status(200).json({
        success: true,
        data: postSessions
      });
    } catch (error) {
      console.error("Error fetching post-sessions by therapistId:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while fetching post-sessions information",
        error: error.message
      });
    }
  }



  // THERAPIST
  /**
   * Get all therapist post-sessions that need review (clinical director review or comments)
   * @param req - Express Request object containing limit and offset params
   * @param res - Express Response object
   */
  export async function getAllTherapistPostSessionsNeedCall(req: Request, res: Response) {
    try {
      // Kiểm tra quyền truy cập cho admin/superadmin
      if(req.user.role === 'SUB_ADMIN'){
        const ownUser = await User.findById(req.user._id);
        if(!ownUser || !ownUser.adminPermission || !ownUser.adminPermission.sessionFeedback){
          return res.status(403).json({
            success: false,
            message: "You don't have permission for Session Feedback!"
          });
        }
      }

      const limit = Number(req.params.limit);
      const offset = Number(req.params.offset);
      const searchableString = req.query.searchableString as string;
      const therapistId = req.query.therapistId as string;

      console.log(`Getting therapist post-sessions needing review. limit: ${limit}, offset: ${offset}, searchableString: ${searchableString}, therapistId: ${therapistId}`);

      let postSessions = await PostSessionDao.getAllTherapistPostSessionsNeedCall(limit, offset, searchableString, therapistId);

      return res.status(200).json({
        success: true,
        data: postSessions
      });
    } catch (error) {
      console.error("Error getting therapist post-sessions that need review:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while getting therapist post-sessions that need review",
        error: error.message
      });
    }
  }

  /**
   * Get count of therapist post-sessions that need review
   * @param req - Express Request object
   * @param res - Express Response object
   */
  export async function getTherapistPostSessionsNeedCallCount(req: Request, res: Response) {
    try {
      // Kiểm tra quyền truy cập cho admin/superadmin
      if(req.user.role === 'SUB_ADMIN'){
        const ownUser = await User.findById(req.user._id);
        if(!ownUser || !ownUser.adminPermission || !ownUser.adminPermission.sessionFeedback){
          return res.status(403).json({
            success: false,
            message: "You don't have permission for Session Feedback!"
          });
        }
      }
      
      const count = await PostSessionDao.getTherapistPostSessionsNeedCallCount();
      
      return res.status(200).json({
        success: true,
        data: count
      });
    } catch (error) {
      console.error("Error getting count of therapist post-sessions that need review:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while getting count of therapist post-sessions that need review",
        error: error.message
      });
    }
  }

  /**
   * Update post-session is_read status
   * @param req - Express Request object containing id param
   * @param res - Express Response object
   */
  export async function updateTherapistPostSessionIsRead(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { is_read } = req.body;
      
      if (!Util.isObjectId(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid ID"
        });
      }
      
      const updatedPostSession = await PostSessionDao.updateTherapistPostSessionIsRead(id, is_read);
      
      if (!updatedPostSession) {
        return res.status(404).json({
          success: false,
          message: "Post-session not found"
        });
      }
      
      return res.status(200).json({
        success: true,
        data: updatedPostSession,
        message: "Post-session marked as read successfully"
      });
    } catch (error) {
      console.error("Error updating post-session is_read status:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while updating post-session is_read status",
        error: error.message
      });
    }
  }

}
