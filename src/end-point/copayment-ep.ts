import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import { InsuranceDao } from "../dao/insurance-dao";
import { UserDao } from "../dao/user-dao";
import { ClientDao } from "../dao/client-dao";
import { Tradingpartner_ServiceId } from "../models/insurance-model";
import axios from "axios";
import { AppLogger } from "../common/logging";
import { FeatureFlags } from "../config/feature-flags";

export namespace CopaymentEp {
  
  // Function to format date from ISO string to YYYYMMDD
  function formatDateStringSimple(dateStr: string): string {
    return dateStr.substring(0, 10).replace(/-/g, '');
  }
  
  // Function to get current date in New York timezone with YYYYMMDD format
  function getCurrentDateInNewYork(): string {
    const today = new Date();
    const options = { timeZone: 'America/New_York' };
    const nyDateString = today.toLocaleDateString('en-US', options);
    const nyDate = new Date(nyDateString);
    
    const year = nyDate.getFullYear();
    const month = String(nyDate.getMonth() + 1).padStart(2, '0');
    const day = String(nyDate.getDate()).padStart(2, '0');
    
    return `${year}${month}${day}`;
  }
  
  export async function getCopaymentByClientId(req: Request, res: Response, next: NextFunction) {
    try {
      // Check feature flag, if true return mock data
      if (FeatureFlags.mock_respnse_api_get_copayment) {
        return res.sendSuccess({
          client_info: {
            name: "Ashley Teague",
            email: "<EMAIL>",
            insurance_id: "900756197T",
            insurance_company: "Partners"
          },
          insurance_info: {
            ins_name_f: "ASHLEY",
            ins_name_l: "TEAGUE",
            ins_city: "Hickory",
            ins_state: "NC",
            ins_zip: "28602",
            ins_dob: "19890326",
            ins_sex: "F",
            ins_addr_1: "173 15th St SE"
          },
          copayment: [
            {
              amount: "0",
              description: "Medical Care",
              Code: "1"
            },
            {
              amount: "0",
              description: "Chiropractic",
              Code: "33"
            },
            {
              amount: "0",
              description: "Dental Care",
              Code: "35"
            },
            {
              amount: "0",
              description: "Hospital",
              Code: "47"
            },
            {
              amount: "0",
              description: "Hospital - Inpatient",
              Code: "48"
            },
            {
              amount: "0",
              description: "Hospital - Room and Board",
              Code: "49"
            },
            {
              amount: "0",
              description: "Hospital - Outpatient",
              Code: "50"
            },
            {
              amount: "0",
              description: "Emergency Services",
              Code: "86"
            },
            {
              amount: "0",
              description: "Professional (Physician) Visit - Office",
              Code: "98"
            },
            {
              amount: "0",
              description: "Vision (Optometry)",
              Code: "AL"
            },
            {
              amount: "10",
              description: "Mental Health",
              Code: "MH"
            },
            {
              amount: "0",
              description: "Urgent Care",
              Code: "UC"
            },
            {
              amount: "0",
              description: "Pharmacy",
              Code: "88"
            }
          ]
        });
      }
      
      // Continue normal processing if feature flag is false
      // Check query client_id
      const clientId = req.query.client_id ? req.query.client_id.toString() : (req.user?._id || null);
      
      if (!clientId) {
        return res.sendError("Client ID not provided");
      }

      // Get user information
      const user = await UserDao.getUserByUserId(clientId);
      if (!user) {
        return res.sendError("User not found");
      }

      // Get client information
      const client = await ClientDao.getUserById(clientId);
      if (!client) {
        return res.sendError("Client information not found");
      }

      // Get insurance information
      const insurance = await InsuranceDao.getInsuranceByClientId(clientId);
      if (!insurance) {
        return res.sendError("Insurance information not found");
      }

      // Get insurance company
      const insuranceCompany = insurance.insuranceCompanyId;
      if (!insuranceCompany) {
        return res.sendError("Insurance company information not found");
      }

      // Get payerId from tradingPartnerServiceId of insurance company
      const insuranceCompanyName = insuranceCompany.insuranceCompany;
      AppLogger.info("Insurance company name:", insuranceCompanyName);

      if (insuranceCompany.isMedicaid === true) {
        // Return formatted result with fixed insurance_info and copayment for Medicaid
        AppLogger.info('Medicaid detected for client:', {
          clientId,
          insuranceCompanyId: insurance.insuranceCompanyId,
          insuranceCompanyName,
          isMedicaid: insuranceCompany.isMedicaid
        });
        
        return res.sendSuccess({
          client_info: {
            name: `${user.firstname} ${user.lastname}`,
            email: user.email,
            insurance_id: insurance.subscriber?.memberId || "Unknown",
            insurance_company: insuranceCompanyName
          },
          insurance_info: {
            ins_name_f: "Unknown",
            ins_name_l: "Unknown",
            ins_city: "Unknown",
            ins_state: "Unknown",
            ins_zip: "Unknown",
            ins_dob: "Unknown",
            ins_sex: "Unknown",
            ins_addr_1: "Unknown"
          },
          copayment: []
        });
      }
      
      let payerId = '';
      if (Tradingpartner_ServiceId[insuranceCompanyName]) {
        payerId = Tradingpartner_ServiceId[insuranceCompanyName];
      }
      if (!payerId) {
        return res.sendError("payerId not found");
      }

      AppLogger.info('Client and insurance info:', {
        name: `${user.firstname} ${user.lastname}`,
        email: user.email,
        insurance_id: insurance.subscriber.memberId,
        insurance_company: insuranceCompanyName,
        payerId: payerId
      });

      // Check insured person's first and last name
      const firstName = insurance.subscriber.firstName || user.firstname;
      const lastName = insurance.subscriber.lastName || user.lastname;
      
      if (!firstName) {
        return res.sendError("Insured person's first name not found");
      }

      if (!lastName) {
        return res.sendError("Insured person's last name not found");
      }

      // Process case of lastName in uppercase for requirement
      const lastNameFormatted = lastName.toUpperCase();

      // Get date of birth from database
      let dobRaw = null;
      
      // Prefer to get from insurance.subscriber.dateOfBirth
      if (insurance.subscriber.dateOfBirth) {
        dobRaw = insurance.subscriber.dateOfBirth;
      } else if (user.dateOfBirth) {
        // If not available, get from user.dateOfBirth
        dobRaw = user.dateOfBirth;
      }
      
      if (!dobRaw) {
        return res.sendError("Insured person's date of birth not found");
      }
      
      AppLogger.info("Raw DOB from database:", dobRaw);
      
      // Format date to YYYYMMDD
      let dobFormatted;
      
      if (dobRaw instanceof Date) {
        // If it's a Date object, convert to ISO string before
        dobFormatted = formatDateStringSimple(dobRaw.toISOString());
      } else if (typeof dobRaw === 'string') {
        // If it's a string, use formatDateStringSimple function
        dobFormatted = formatDateStringSimple(dobRaw);
      } 

      if(!dobFormatted){
        return res.sendError("DOB not found");
      }
      
      AppLogger.info("Formatted DOB for API:", dobFormatted);
      
      if (!insurance.subscriber.memberId) {
        return res.sendError("Insurance ID not found");
      }
      
      // Prepare data for eligibility check request
      const data = {
        'AccountKey': '18420_X07Qzhib2EmpXA42arGNCR6e',
        'ins_name_f': firstName,
        'ins_name_l': lastNameFormatted,
        'payerid': payerId,
        'ins_dob': dobFormatted,
        'pat_rel': '18',
        'fdos': getCurrentDateInNewYork(), // Current date in New York timezone
        'ins_sex': insurance.subscriber.gender && insurance.subscriber.gender.toLowerCase() === 'male' ? 'M' : 'F',
        'prov_npi': '**********', // Fixed NPI
        'prov_taxid': '*********', // Fixed Tax ID
        'ins_number': insurance.subscriber.memberId
      };

      // Log request data
      AppLogger.info('Eligibility request data:', data);

      // Send eligibility check request
      const result = await submitEligibility(data);
      AppLogger.info('Eligibility response data:', result);
      
      if (!result) {
        return res.sendError("Could not retrieve copayment information");
      }

      // Process result to get copayment
      const eligibilityInfo = extractInsuranceInfo(result.data);
      const copayments = processEligibilityResponse(result);

      return res.sendSuccess({
        client_info: {
          name: `${user.firstname} ${user.lastname}`,
          email: user.email,
          insurance_id: insurance.subscriber.memberId,
          insurance_company: insuranceCompanyName
        },
        insurance_info: eligibilityInfo,
        copayment: copayments
      });
    } catch (error) {
      AppLogger.error('getCopaymentByClientId error:', error);
      return res.sendError(`Error retrieving copayment information: ${error.message}`);
    }
  }

  async function submitEligibility(data) {
    const url = "https://svc.claim.md/services/eligdata/";
    const headers = {
      "Content-Type": "application/x-www-form-urlencoded",
      "Accept": "application/json"
    };

    try {
      const response = await axios.post(url, new URLSearchParams(data), { headers });
      AppLogger.info('Eligibility API response status:', response.status);
      
      // Log raw response from claim.md
      AppLogger.info('====================== RAW CLAIM.MD RESPONSE ======================');
      AppLogger.info(JSON.stringify(response.data, null, 2));
      AppLogger.info('==================================================================');
      
      return response;
    } catch (error) {
      AppLogger.error('submitEligibility error:', error);
      throw error;
    }
  }

  function processEligibilityResponse(response) {
    if (response.status === 200) {
      try {
        AppLogger.info('Raw eligibility response:', response.data);
        
        let responseText = typeof response.data === 'string' ? response.data : JSON.stringify(response.data);
        const data = JSON.parse(responseText);
        
        AppLogger.info('Processing eligibility response data structure');
        
        if (!data.elig || !data.elig.benefit) {
          AppLogger.warn('No benefit data found in response');
          return [];
        }
        
        const benefits = Array.isArray(data.elig.benefit) ? data.elig.benefit : [data.elig.benefit];
        
        // Filter only to get copayments
        const copayments = benefits.filter(b => b.benefit_coverage_description === 'Co-Payment')
          .map(b => ({
            amount: b.benefit_amount || '0',
            description: b.benefit_description || '',
            Code: b.benefit_code || ''
          }));
        
        AppLogger.info(`Found ${copayments.length} copayment records:`, copayments);
        return copayments;
      } catch (error) {
        AppLogger.error('processEligibilityResponse error:', error);
        return [];
      }
    }
    return [];
  }

  function extractInsuranceInfo(jsonData) {
    try {
      let responseText = typeof jsonData === 'string' ? jsonData : JSON.stringify(jsonData);
      const data = JSON.parse(responseText);
      const eligibilityInfo = data.elig || {};

      if (!eligibilityInfo || typeof eligibilityInfo !== 'object') {
        AppLogger.warn('Invalid eligibility info structure');
        return {};
      }

      AppLogger.info('Extracted insurance basic info');
      
      return {
        ins_name_f: eligibilityInfo.ins_name_f || 'Unknown',
        ins_name_l: eligibilityInfo.ins_name_l || 'Unknown',
        ins_city: eligibilityInfo.ins_city || 'Unknown',
        ins_state: eligibilityInfo.ins_state || 'Unknown',
        ins_zip: eligibilityInfo.ins_zip || 'Unknown',
        ins_dob: eligibilityInfo.ins_dob || 'Unknown',
        ins_sex: eligibilityInfo.ins_sex || 'Unknown',
        ins_addr_1: eligibilityInfo.ins_addr_1 || 'Unknown',
      };
    } catch (error) {
      AppLogger.error('extractInsuranceInfo error:', error);
      return {};
    }
  }
} 