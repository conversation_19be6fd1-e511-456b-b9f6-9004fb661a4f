import { NextFunction, Request, Response } from "express";
import { check, validationResult } from "express-validator";
import ChatGroup from "../schemas/chat-group-schema";
const moment = require('moment-timezone');
import {
  ChatGroupCallCallingStatus,
  DChatGroupCall,
} from "../models/chat-group-call-model";
import { ChatGroupCallDao } from "../dao/chat-group-call-dao";
import ChatGroupCall from "../schemas/chat-group-call-schema";
import ChatGroupMember from "../schemas/chat-group-member-schema";
import { EmailService } from "../mail/config";
import { SMSService } from "../sms/config";
import * as mongoose from "mongoose";
import Appointment from "../schemas/appointment-schema";
import { AppointmentStatus } from "../models/appointment-model";
import { AppointmentDao } from "../dao/appointment-dao";
import { Types } from "mongoose";

export namespace ChatGroupCallEp {
  export function createChatGroupCallValidationRules() {
    return [
      check("groupId")
        .notEmpty()
        .withMessage("Group Id is required.")
        .isMongoId()
        .withMessage("Invalid Group Id."),
      check("start")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Start time is required."),
    ];
  }
  export async function createChatGroupCall(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      const userId = req.user._id;
      const groupId = req.body.groupId;
      const start = new Date(req.body.start);
      const startTime = moment(start).format("H:mm A");
      const groupCallStartTime = moment(start).format("YYYY-MM-DD HH:mm");

      if (!startTime || !groupCallStartTime) {
        return res.sendError("Please select valid start time.");
      }

      if (
        !moment(new Date()).isBefore(
          moment(
            new Date(groupCallStartTime).setHours(
              parseInt(startTime.split(":")[0]),
              parseInt(startTime.split(":")[1]),
              0,
              0
            )
          )
        ) ||
        (parseInt(startTime.split(":")[1]) !== 0 &&
          parseInt(startTime.split(":")[1]) !== 30)
      ) {
        return res.sendError("Invalid start date!");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
        createdBy: userId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      // Calculate end time (default add 1 hour)
      const end = new Date(start);
      end.setHours(end.getHours() + 1);

      // Check if therapist already has any other group chat session at the same time
      const overlappingChatGroupCalls = await ChatGroupCall.aggregate([
        {
          $lookup: {
            from: "chatgroups",
            localField: "groupId",
            foreignField: "_id",
            as: "groupDetails"
          }
        },
        {
          $match: {
            createdBy: userId,
            callingStatus: { $in: [ChatGroupCallCallingStatus.PENDING, ChatGroupCallCallingStatus.ONGOING] },
            $or: [
              // Start within the new session timeframe
              {
                start: { $gte: start, $lt: end }
              },
              // End within the new session timeframe (assuming each session lasts 1 hour)
              {
                $expr: {
                  $and: [
                    { $lt: ["$start", end] },
                    { $gt: [{ $add: ["$start", 3600000] }, start] } // 3600000 milliseconds = 1 hour
                  ]
                }
              }
            ]
          }
        }
      ]);

      if (overlappingChatGroupCalls.length > 0) {
        const conflictSession = overlappingChatGroupCalls[0];
        const groupName = conflictSession.groupDetails && conflictSession.groupDetails.length > 0 
          ? conflictSession.groupDetails[0].title 
          : "another group";
        
        return res.sendError(`You already have a scheduled group session in "${groupName}" during this time slot.`);
      }

      // Check if therapist already has any individual appointment at the same time
      const overlappingAppointments = await AppointmentDao.getAppointmentsOfTherapistByStartTime(
        start, 
        end, 
        1, // Duration in hours
        userId instanceof Types.ObjectId ? userId : new Types.ObjectId(userId.toString())
      );

      if (overlappingAppointments.length > 0) {
        return res.sendError("You already have a scheduled individual appointment during this time slot.");
      }

      const chatGroupCallDetails: DChatGroupCall = {
        groupId: chatGroup._id,
        createdBy: userId,
        start: start,
        callingStatus: ChatGroupCallCallingStatus.PENDING,
      };

      const createdChatGroupCall = await ChatGroupCallDao.createChatGroupCall(
        chatGroupCallDetails
      );

      if (createdChatGroupCall == null) {
        return res.sendError("Session creation failed.");
      }

      const allChatMembers = await ChatGroupMember.aggregate([
        {
          $match: {
            groupId: chatGroup._id,
            userId: { $ne: userId },
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "userDetails",
          },
        },
        {
          $unwind: "$userDetails",
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ["$userDetails", "$$ROOT"] },
          },
        },
        {
          $project: {
            _id: "$userDetails._id",
            email: 1,
            firstname: 1,
            lastname: 1,
            primaryPhone: 1,
          },
        },
      ]);

      const utcTime = moment.utc(createdChatGroupCall.start);

      const estTime = utcTime.tz('America/New_York');


      if (allChatMembers != null) {
        await Promise.all(
          allChatMembers.map(async (member) => {
            if (member.email) {

              await EmailService.sendGroupChatEventEmail(
                member,
                member.firstname ?? "user",
                `New session is scheduled in ${chatGroup.title} chat group`,
                `New session is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST in ${chatGroup.title} ${chatGroup.type == "PUBLIC" ? "Peer Support" : "private"
                } chat group.`
              );
            }

            if (member.primaryPhone) {
              await SMSService.sendGroupChatEventSMS(
                `Hi ${member.firstname ?? "user"
                }, New session is scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST in ${chatGroup.title} ${chatGroup.type == "PUBLIC" ? "Peer Support" : "private"
                } chat group.`,
                member.primaryPhone
              );
            }
          })
        );
      }

      return res.sendSuccess(
        createdChatGroupCall,
        "Session created successfully."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }
  export function updateChatGroupCallValidationRules() {
    return [
      check("sessionId")
        .notEmpty()
        .withMessage("Session Id is required.")
        .isMongoId()
        .withMessage("Invalid Session Id."),
      check("start")
        .isString()
        .not()
        .isEmpty()
        .withMessage("Start time is required."),
    ];
  }
  export async function updateChatGroupCall(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      const userId = req.user._id;
      const sessionId = req.body.sessionId;
      const start = new Date(req.body.start);
      const startTime = moment(start).format("H:mm A");
      const groupCallStartTime = moment(start).format("YYYY-MM-DD HH:mm");

      if (!startTime || !groupCallStartTime) {
        return res.sendError("Please select valid start time.");
      }

      if (
        !moment(new Date()).isBefore(
          moment(
            new Date(groupCallStartTime).setHours(
              parseInt(startTime.split(":")[0]),
              parseInt(startTime.split(":")[1]),
              0,
              0
            )
          )
        ) ||
        (parseInt(startTime.split(":")[1]) !== 0 &&
          parseInt(startTime.split(":")[1]) !== 30)
      ) {
        return res.sendError("Invalid start date!");
      }

      const prevSession = await ChatGroupCall.findOne({
        _id: sessionId,
        createdBy: userId,
        callingStatus: { $in: [ChatGroupCallCallingStatus.PENDING] },
      });

      if (prevSession == null) {
        return res.sendError("Invalid session.");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: prevSession.groupId,
        createdBy: userId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const updatedChatGroupCallDetails = {
        start: start,
      };
      const updatedChatGroupCall = await ChatGroupCall.findOneAndUpdate(
        { _id: prevSession._id },
        {
          $set: updatedChatGroupCallDetails,
        },
        { new: true }
      );

      if (updatedChatGroupCall == null) {
        return res.sendError("Session update failed.");
      }

      const allChatMembers = await ChatGroupMember.aggregate([
        {
          $match: {
            groupId: chatGroup._id,
            userId: { $ne: userId },
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "userDetails",
          },
        },
        {
          $unwind: "$userDetails",
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ["$userDetails", "$$ROOT"] },
          },
        },
        {
          $project: {
            _id: "$userDetails._id",
            email: 1,
            firstname: 1,
            lastname: 1,
            primaryPhone: 1,
          },
        },
      ]);

      const utcTime = moment.utc(prevSession.start);

      const estTime = utcTime.tz('America/New_York');

      const utcTimeUpdatedChatGroupCall = moment.utc(updatedChatGroupCall.start);

      const estTimeUpdatedChatGroupCall = utcTimeUpdatedChatGroupCall.tz('America/New_York');

      if (allChatMembers != null) {
        await Promise.all(
          allChatMembers.map(async (member) => {
            if (member.email) {
              await EmailService.sendGroupChatEventEmail(
                member,
                member.firstname ?? "user",
                `Scheduled session updated in ${chatGroup.title} chat group`,
                `Session that scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST was updated to ${estTimeUpdatedChatGroupCall.format('YYYY-MM-DD hh:mm A')} EST in ${chatGroup.title} ${chatGroup.type == "PUBLIC" ? "Peer Support" : "private"
                } chat group.`
              );
            }

            if (member.primaryPhone) {
              await SMSService.sendGroupChatEventSMS(
                `Hi ${member.firstname ?? "user"
                }, Session that scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST on was updated to ${estTimeUpdatedChatGroupCall.format('YYYY-MM-DD hh:mm A')} EST in ${chatGroup.title} ${chatGroup.type == "PUBLIC" ? "Peer Support" : "private"
                } chat group.`,
                member.primaryPhone
              );
            }
          })
        );
      }

      return res.sendSuccess(
        updatedChatGroupCall,
        "Session updated successfully."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function deleteChatGroupCallValidationRules() {
    return [
      check("sessionId")
        .notEmpty()
        .withMessage("Session Id is required.")
        .isMongoId()
        .withMessage("Invalid Session Id."),
    ];
  }
  export async function deleteChatGroupCall(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      const userId = req.user._id;
      const sessionId = req.body.sessionId;

      const prevSession = await ChatGroupCall.findOne({
        _id: sessionId,
        createdBy: userId,
        callingStatus: { $in: [ChatGroupCallCallingStatus.PENDING] },
      });

      if (prevSession == null) {
        return res.sendError("Invalid session.");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: prevSession.groupId,
        createdBy: userId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const deletedSession = await ChatGroupCall.findOneAndDelete({
        _id: prevSession._id,
      });

      if (deletedSession == null) {
        return res.sendError("Session delete failed.");
      }

      const allChatMembers = await ChatGroupMember.aggregate([
        {
          $match: {
            groupId: chatGroup._id,
            userId: { $ne: userId },
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "userDetails",
          },
        },
        {
          $unwind: "$userDetails",
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ["$userDetails", "$$ROOT"] },
          },
        },
        {
          $project: {
            _id: "$userDetails._id",
            email: 1,
            firstname: 1,
            lastname: 1,
            primaryPhone: 1,
          },
        },
      ]);

      const utcTime = moment.utc(prevSession.start);

      const estTime = utcTime.tz('America/New_York');

      if (allChatMembers != null) {
        await Promise.all(
          allChatMembers.map(async (member) => {
            if (member.email) {
              await EmailService.sendGroupChatEventEmail(
                member,
                member.firstname ?? "user",
                `Session is removed in ${chatGroup.title} chat group`,
                `Session that scheduled at ${estTime.format('YYYY-MM-DD hh:mm A')} EST was removed in ${chatGroup.title} ${chatGroup.type == "PUBLIC" ? "Peer Support" : "private"
                } chat group.`
              );
            }

            if (member.primaryPhone) {
              await SMSService.sendGroupChatEventSMS(
                `Hi ${member.firstname ?? "user"
                }, Session that scheduled at ${moment(
                  prevSession.start,
                  "YYYY-MM-DD H:mm A"
                ).format("h:mm A")} EST on ${moment(prevSession.start).format(
                  "MM/DD/YYYY"
                )} was removed in ${chatGroup.title} ${chatGroup.type == "PUBLIC" ? "public" : "private"
                } chat group.`,
                member.primaryPhone
              );
            }
          })
        );
      }

      return res.sendSuccess("Session deleted successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }
}
