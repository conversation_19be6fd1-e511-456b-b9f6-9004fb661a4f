import { NextFunction, Request, Response } from "express";
import { UserRole } from "../models/user-model";
import { UserDao } from "../dao/user-dao";
import { ClientRewardDao } from "../dao/client-reward-dao";
import { AppLogger } from "../common/logging";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import ClientReward from "../schemas/client-reward-schema";
import { TransactionType } from "../models/transaction-model";
import ClientReferral from "../schemas/client-referral-schema";
import { RewardType } from "../models/referral-earning-model";
import Client from "../schemas/client-schema";
import { StringOrObjectId } from "../common/util";
import { validationResult } from "express-validator";

export namespace ClientRewardEp {

    export async function getClientRewards(req: Request, res: Response, next: NextFunction) {
      try {
        const limit = Number(req.params.limit);
        const offset = Number(req.params.offset);
        if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN || req.user.role == UserRole.ADMIN) {
          if (req.user.role == UserRole.SUB_ADMIN) {
            const ownUser = await UserDao.getUserByUserId(req.user._id);
            if (ownUser.adminPermission.clientRewards != true) {
              return res.sendError(
                "You don't have permission for view client rewards!"
              );
            }
          }
          const clientRewards = await ClientRewardDao.getClientRewardsWithoutFilter(limit, offset);
          let unpaidClientRewardsCount = await ClientRewardDao.getAllUnpaidClientRewardsCount();

          const rewards: any = {
            clientRewardsList: clientRewards,
            count: unpaidClientRewardsCount
          };

          return res.sendSuccess(rewards, "Client reward details.");
        } else {
          return res.sendError("Invalid user role!");
        }
      } catch (error) {
        return res.sendError(error);
      }
    }

    export async function markedAsPaidClientRewardRecord(req: Request, res: Response, next: NextFunction) {
      try {
        const rewardId = req.body.rewardId;
        const paidStatus = req.body.paidStatus;
        if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
          if (req.user.role == UserRole.SUB_ADMIN) {
            const ownUser = await UserDao.getUserByUserId(req.user._id);
            if (ownUser.adminPermission.clientRewards != true) {
              return res.sendError(
                "You don't have permission for modify client reward data!"
              );
            }
          }
          let isReward: any = await ClientRewardDao.getClientRewardById(rewardId);

          if (isReward == null) {
            return res.sendError("No reward record found!");
          }

          try {
            const data = {
              paidStatus: paidStatus
            }
            const reward = await ClientRewardDao.updateClientRewardById(rewardId, data);
            return res.sendSuccess(reward, `Reward record marked as a ${paidStatus}!`);
          } catch (error) {
            return res.sendError(error);
          }
        } else {
          return res.sendError("Invalid user role!");
        }

      } catch (error) {
        return res.sendError(error);
      }
    }

    export async function deleteClientRewardRecord(req: Request, res: Response, next: NextFunction) {

      try {
        const rewardId = req.params.rewardId;
        if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

          if(req.user.role == UserRole.SUB_ADMIN){
            const ownUser = await UserDao.getUserByUserId(req.user._id);
            if(ownUser.adminPermission.clientRewards != true){
              return res.sendError(
                "You don't have permission for delete client reward data!"
              );
            }
          }
          let reward: any = await ClientRewardDao.getClientRewardById(rewardId);

          if (reward == null) {
            return res.sendError("No reward record found!");
          }

          try {
            let deletedReward = await ClientRewardDao.deleteClientRewardById(rewardId);

            return res.sendSuccess(deletedReward, "Reward record DELETED!");

          } catch (error) {
            return res.sendError(error);
          }
        } else {
          return res.sendError("Invalid user role!");
        }
      } catch (error) {
        return res.sendError(error);
      }

    }

    export async function getClientRewardsByClientId(req: Request, res: Response, next: NextFunction) {
      const clientId = req.user._id;
      const role = req.user.role;
  
      const errors = validationResult(req);
  
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }
  
      if (role == UserRole.CLIENT) {
        try {
          let recentRewards = await ClientRewardDao.getClientRewardDetailsByClientId(clientId);
  
          if (!recentRewards) {
            return res.sendError("Unable to get recent rewards.");
          }
  
          return res.sendSuccess(recentRewards, "Recent rewards of Client.");
        } catch (error) {
          return res.sendError(error);
        }
      } else {
        return res.sendError("Invalid user role!");
      }
    }

    export async function addRewardAmountToClients() {
      try {
        const bonusAmount = "10";
    
        const clientsForFirst = await TreatmentHistory.aggregate([
          {
            $lookup: {
                from: 'clientreferrals',
                localField: 'clientId',
                foreignField: 'referredUserId',
                as: 'referralDetails'
            }
          },
          {
            $unwind: {
                path: '$referralDetails',
                preserveNullAndEmptyArrays: true
            }
          },
          {
            $match: {
                referralDetails: { $ne: null },
                'referralDetails.referrerUserId': { $ne: null }
            }
          },
          {
            $lookup: {
                from: 'users',
                localField: 'clientId',
                foreignField: '_id',
                as: 'clientDetails'
            }
          },
          {
            $unwind: {
                path: '$clientDetails',
                preserveNullAndEmptyArrays: true
            }
          },
          {
            $match: {
                $or: [
                {
                    $and: [
                    { claimStatus: { $exists: true } },
                    { claimStatus: 'PAID' }
                    ]
                },
                {
                    $and: [
                    { clientDetails: { $ne: null } },
                    { 'clientDetails.subscriptionStatus': { $ne: null } },
                    { 'clientDetails.subscriptionStatus': { $eq: 'active' } }
                    ]
                }
                ]
            }
          },
          {
            $group: {
                _id: '$clientId',
                meetingCount: { $sum: 1 }
            }
          },
          {
            $lookup: {
                from: 'users',
                localField: '_id',
                foreignField: '_id',
                as: 'client'
            }
          },
          {
            $unwind: {
                path: '$client',
                preserveNullAndEmptyArrays: false
            }
          },
          {
            $project: {
                _id: 0,
                userId: '$client._id',
                firstSessionBonus: '$client.firstSessionBonus',
                meetingCount: 1
            }
          },
          { 
            $match: {
                $or: [
                { firstSessionBonus: { $exists: false } },
                { firstSessionBonus: null }
                ],
                meetingCount: { $gte: 4 }
            }
          },
        ]);
    
        if (clientsForFirst) {
          await Promise.all(clientsForFirst.map(async client => {
            if (client && client.userId && client.meetingCount && client.meetingCount >= 4) {
              if (!client.firstSessionBonus) {
                const existingRewards = await ClientReward.findOne({
                    clientId: client.userId,
                    type: TransactionType.REFERRAL,
                    rewardType: RewardType.OWNFIRST
                });
    
                if (!existingRewards) {
                  let rewards = await ClientRewardDao.createClientRewards(
                  client.userId,
                  bonusAmount,
                  TransactionType.REFERRAL,
                  RewardType.OWNFIRST,
                  null
                  );
  
                  if (rewards && rewards._id) {
                    await Client.findByIdAndUpdate(
                      client.userId,
                      { $set: { firstSessionBonus: rewards._id } },
                      { useFindAndModify: false }
                    );
                  }
                }
              }
                
              const referralRecord = await ClientReferral.findOne({
                referredUserId: client.userId,
                $or: [
                  { firstSessionCompletionBonusForReferrer: { $exists: false } },
                  { firstSessionCompletionBonusForReferrer: null }
                ]
              });
  
              if (referralRecord && referralRecord._id && referralRecord.referrerUserId && !(referralRecord.firstSessionCompletionBonusForReferrer)) {
              
                const clientId: StringOrObjectId = referralRecord.referrerUserId.toString();
  
                const existingRewards = await ClientReward.findOne({
                    clientId: clientId,
                    type: TransactionType.REFERRAL,
                    rewardType: RewardType.REFERFIRST,
                    rewardReferralId: referralRecord._id
                });
    
                if (!existingRewards) {
                  let rewards = await ClientRewardDao.createClientRewards(
                    referralRecord.referrerUserId,
                    bonusAmount,
                    TransactionType.REFERRAL,
                    RewardType.REFERFIRST,
                    referralRecord._id
                  );
  
                  if (rewards && rewards._id) {
                    await ClientReferral.findByIdAndUpdate(
                        referralRecord._id,
                        { $set: { firstSessionCompletionBonusForReferrer: rewards._id } },
                        { useFindAndModify: false }
                    );
                  }
                }
              }
            }
          }));
        }
        AppLogger.info(`Clients rewards allocation process has been successfully completed.`);
      } catch (error) {
          AppLogger.error(`An error occurred while allocating rewards to Clients. Error: ${error}.`);
      }
    }

    // export async function rewardAPIClient(req: Request, res: Response, next: NextFunction) {
    //     try {
    //       const bonusAmount = "10";
    
    //       const clientsForFirst = await TreatmentHistory.aggregate([
    //         {
    //           $lookup: {
    //             from: 'clientreferrals',
    //             localField: 'clientId',
    //             foreignField: 'referredUserId',
    //             as: 'referralDetails'
    //           }
    //         },
    //         {
    //           $unwind: {
    //             path: '$referralDetails',
    //             preserveNullAndEmptyArrays: true
    //           }
    //         },
    //         {
    //           $match: {
    //             referralDetails: { $ne: null },
    //             'referralDetails.referrerUserId': { $ne: null }
    //           }
    //         },
    //         {
    //           $lookup: {
    //             from: 'users',
    //             localField: 'clientId',
    //             foreignField: '_id',
    //             as: 'clientDetails'
    //           }
    //         },
    //         {
    //           $unwind: {
    //             path: '$clientDetails',
    //             preserveNullAndEmptyArrays: true
    //           }
    //         },
    //         {
    //           $match: {
    //             $or: [
    //               {
    //                 $and: [
    //                   { claimStatus: { $exists: true } },
    //                   { claimStatus: 'PAID' }
    //                 ]
    //               },
    //               {
    //                 $and: [
    //                   { clientDetails: { $ne: null } },
    //                   { 'clientDetails.subscriptionStatus': { $ne: null } },
    //                   { 'clientDetails.subscriptionStatus': { $eq: 'active' } }
    //                 ]
    //               }
    //             ]
    //           }
    //         },
    //         {
    //           $group: {
    //             _id: '$clientId',
    //             meetingCount: { $sum: 1 }
    //           }
    //         },
    //         {
    //           $lookup: {
    //             from: 'users',
    //             localField: '_id',
    //             foreignField: '_id',
    //             as: 'client'
    //           }
    //         },
    //         {
    //           $unwind: {
    //             path: '$client',
    //             preserveNullAndEmptyArrays: false
    //           }
    //         },
    //         {
    //           $project: {
    //             _id: 0,
    //             userId: '$client._id',
    //             firstSessionBonus: '$client.firstSessionBonus',
    //             meetingCount: 1
    //           }
    //         },
    //         { $match: {
    //             $or: [
    //               { firstSessionBonus: { $exists: false } },
    //               { firstSessionBonus: null }
    //             ],
    //             meetingCount: { $gte: 4 }
    //           }
    //         },
    //       ]);
    
    //       if (clientsForFirst) {
    //         await Promise.all(clientsForFirst.map(async client => {
    //           if (client && client.userId && client.meetingCount && client.meetingCount >= 4) {
                
    //             if (!client.firstSessionBonus) {
    //               const existingRewards = await ClientReward.findOne({
    //                 clientId: client.userId,
    //                 type: TransactionType.REFERRAL,
    //                 rewardType: RewardType.OWNFIRST
    //               });
    
    //               if (!existingRewards) {
    //                 let rewards = await ClientRewardDao.createClientRewards(
    //                   client.userId,
    //                   bonusAmount,
    //                   TransactionType.REFERRAL,
    //                   RewardType.OWNFIRST,
    //                   null
    //                 );
    
    //                 if (rewards && rewards._id) {
    //                   await Client.findByIdAndUpdate(
    //                     client.userId,
    //                     { $set: { firstSessionBonus: rewards._id } },
    //                     { useFindAndModify: false }
    //                   );
    //                 }
    //               }
    //             }
                
    //             const referralRecord = await ClientReferral.findOne({
    //               referredUserId: client.userId,
    //               $or: [
    //                 { firstSessionCompletionBonusForReferrer: { $exists: false } },
    //                 { firstSessionCompletionBonusForReferrer: null }
    //               ]
    //             });
    
    //             if (referralRecord && referralRecord._id && referralRecord.referrerUserId && !(referralRecord.firstSessionCompletionBonusForReferrer)) {
                  
    //               const clientId: StringOrObjectId = referralRecord.referrerUserId.toString();
    
    //               const existingRewards = await ClientReward.findOne({
    //                 clientId: clientId,
    //                 type: TransactionType.REFERRAL,
    //                 rewardType: RewardType.REFERFIRST,
    //                 rewardReferralId: referralRecord._id
    //               });
    
    //               if (!existingRewards) {
    //                 let rewards = await ClientRewardDao.createClientRewards(
    //                   referralRecord.referrerUserId,
    //                   bonusAmount,
    //                   TransactionType.REFERRAL,
    //                   RewardType.REFERFIRST,
    //                   referralRecord._id
    //                 );
    
    //                 if (rewards && rewards._id) {
    //                   await ClientReferral.findByIdAndUpdate(
    //                     referralRecord._id,
    //                     { $set: { firstSessionCompletionBonusForReferrer: rewards._id } },
    //                     { useFindAndModify: false }
    //                   );
    //                 }
    //               }
    //             }
    
    //           }
    //         }));
    //       }
    
    //       AppLogger.info(`Clients rewards allocation process has been successfully completed - (Test API Route).`);
    //       return res.sendSuccess({}, `Clients rewards allocation process has been successfully completed. Date: `);
    
    //     } catch (error) {
    //       AppLogger.error(`An error occurred while allocating rewards to clients - (Test API Route). Error ${error}.`);
    //       return res.sendError(error);
    //     }
    // }
}