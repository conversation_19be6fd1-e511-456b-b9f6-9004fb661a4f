import { NextFunction, Request, Response } from "express";
import { validationResult } from "express-validator";
import { UserDao } from "../dao/user-dao";
import { UserRole } from "../models/user-model";
import { DGoal } from "../models/goal-model";
import { GoalDao } from "../dao/goal-dao";
import { EmailService } from "../mail/config";
import { Types } from "mongoose";
import { SMSService } from "../sms/config";
let mongoose = require("mongoose");

export namespace GoalEp {
  export async function createGoal(req: Request, res: Response, next: NextFunction) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    try {
      let user = UserDao.getUserByUserId(req.user._id);

      if (!user) {
        return res.sendError("Not Existing User!");
      }

      if ((await user).role == UserRole.THERAPIST || (await user).role == UserRole.CLIENT) {
        if (!req.body.title) {
          return res.sendError("Title is required.");
        }

        if (!req.body.description) {
          return res.sendError("Description is required.");
        }

        if (!req.body.dueDate) {
          return res.sendError("Due Date is required.");
        }

        if (!req.body.createdBy) {
          return res.sendError("Client Id is required.");
        }

        if (!mongoose.Types.ObjectId.isValid(req.body.createdBy)) {
          return res.sendError("Client Id is invalid.");
        }

        const goalDetails: DGoal = {
          title: req.body.title,
          description: req.body.description,
          prograss: req.body.prograss || 0,
          dueDate: req.body.dueDate,
          isComplete: false,
          createdBy: req.body.createdBy,
          assignedFor: req.body.assignedFor,
          completedDates: req.body.completedDates,
        };

        try {
          let goal = await GoalDao.createGoal(goalDetails);

          if (!goal) {
            return res.sendError("Goal could not be created. Please try again later.");
          }

          const user1 = await UserDao.getUserById(req.body.createdBy);
          const user2 = await UserDao.getUserById(req.body.assignedFor);

          if (req.user.role == UserRole.THERAPIST) {
            if (user2.reminderType && user2.reminderType.email == true) {
              await EmailService.sendEventEmail(
                user2,
                "New goal has been created!",
                "New goal " + goal.title + " has been created by",
                "Login to view more information.",
                user1?.firstname + " " + user1?.lastname
              );
            }
            if (user2.reminderType && user2.reminderType.text == true) {
              await SMSService.sendEventSMS(
                `New goal ${goal.title} has been created by ${user1.firstname} ${user1.lastname}`,
                user2.primaryPhone
              );
            }
          }

          if (req.user.role == UserRole.CLIENT) {
            if (user1.reminderType && user1.reminderType.email == true) {
              await EmailService.sendEventEmail(
                user1,
                "New goal has been created!",
                "New goal " + goal.title + " has been created by",
                "Login to view more information.",
                user2?.firstname + " " + user2?.lastname
              );
            }

            if (user1.reminderType && user1.reminderType.text == true) {
              await SMSService.sendEventSMS(
                `New goal ${goal.title} has been created by ${user2.firstname} ${user2.lastname}`,
                user1.primaryPhone
              );
            }
          }

          return res.sendSuccess(goal, "Success");
        } catch (error) {
          return res.sendError(error);
        }
      } else {
        return res.sendError("You havn't permission to create goal!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateGoal(req: Request, res: Response, next: NextFunction) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.sendError(errors.array()[0]["msg"]);
    }

    const goalId = req.params.goalId;
    let user = UserDao.getUserByUserId(req.user._id);

    if (!user) {
      return res.sendError("Not Existing User!");
    }

    if ((await user).role == UserRole.THERAPIST || (await user).role == UserRole.CLIENT) {
      if (!req.body.title) {
        return res.sendError("Title is required.");
      }

      if (!req.body.description) {
        return res.sendError("Description is required.");
      }

      if (!req.body.dueDate) {
        return res.sendError("Due Date is required.");
      }

      if (!req.body.assignedFor._id) {
        return res.sendError("AssignedById is required.");
      }

      if (!mongoose.Types.ObjectId.isValid(req.body.assignedFor._id)) {
        return res.sendError("AssignedById Id is invalid.");
      }

      if (!req.body.createdBy._id) {
        return res.sendError("Client Id is required.");
      }

      if (!mongoose.Types.ObjectId.isValid(req.body.createdBy._id)) {
        return res.sendError("Client Id is invalid.");
      }

      const goalDetails: DGoal = {
        title: req.body.title,
        description: req.body.description,
        prograss: req.body.prograss || 0,
        dueDate: req.body.dueDate,
        isComplete: req.body.isComplete,
        createdBy: req.body.createdBy._id,
        assignedFor: req.body.assignedFor._id,
        completedDates: req.body.completedDates,
      };

      try {
        let goal = await GoalDao.updateGoal(goalId, goalDetails);

        if (!goal) {
          return res.sendError("Goal could not be created. Please try again later.");
        }

        const user1 = await UserDao.getUserById(req.body.createdBy);
        const user2 = await UserDao.getUserById(req.body.assignedFor);

        if (req.user.role == UserRole.THERAPIST) {
          if (user2.reminderType && user2.reminderType.email == true) {
            await EmailService.sendEventEmail(
              user2,
              "Goal has been updated!",
              "Goal " + goal.title + " has been updated by",
              "Login to view more information.",
              user1?.firstname + " " + user1?.lastname
            );
          }

          if (user2.reminderType && user2.reminderType.text == true) {
            await SMSService.sendEventSMS(
              `New goal ${goal.title} has been updated by ${user1.firstname} ${user1.lastname}`,
              user2.primaryPhone
            );
          }
        }

        if (req.user.role == UserRole.CLIENT && user1.role == UserRole.THERAPIST && goal.isComplete == true) {
          if (user1.reminderType && user1.reminderType.email == true) {
            await EmailService.sendEventEmail(
              user1,
              "Goal has been completed!",
              "Goal " + goal.title + " has been completed by",
              "Login to view more information.",
              user2?.firstname + " " + user2?.lastname
            );
          }

          if (user1.reminderType && user1.reminderType.email == true) {
            await SMSService.sendEventSMS(
              `New goal ${goal.title} has been completed by ${user2.firstname} ${user2.lastname}`,
              user1.primaryPhone
            );
          }
        } else {
          if (user1.reminderType && user1.reminderType.email == true) {
            await EmailService.sendEventEmail(
              user1,
              "Goal has been updated!",
              "Goal " + goal.title + " has been updated by",
              "Login to view more information.",
              user2?.firstname + " " + user2?.lastname
            );
          }

          if (user1.reminderType && user1.reminderType.text == true) {
            await SMSService.sendEventSMS(
              `New goal ${goal.title} has been updated by ${user2.firstname} ${user2.lastname}`,
              user1.primaryPhone
            );
          }
        }
        return res.sendSuccess(goal, "Success");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("You havn't permission to create goal!");
    }
  }

  export async function getAllGoalsByUserIds(req: Request, res: Response, next: NextFunction) {
    const createdId = req.params.createdId;
    const assignedId = req.params.assignedId;
    const limit = Number(req.params.limit);
    const offset = Number(req.params.offset);
    try {
      const goal = await GoalDao.getGoalsByIds(Types.ObjectId(createdId), Types.ObjectId(assignedId), limit, offset);
      return res.sendSuccess(goal, "All Goals");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getGoalByGoalId(req: Request, res: Response, next: NextFunction) {
    const goalId = req.params.goalId;

    try {
      const goal = await GoalDao.getGoalById(goalId);
      return res.sendSuccess(goal, "Selected Goal");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function deleteGoalById(req: Request, res: Response, next: NextFunction) {
    const goalId = req.params.goalId;

    try {
      let user = UserDao.getUserByUserId(req.user._id);
      const deletededGoal = await GoalDao.deleteGoalById(goalId);

      const createdBy = await UserDao.getUserById(deletededGoal.createdBy);
      const assignedFor = await UserDao.getUserById(deletededGoal.assignedFor);

      if (req.user.role == UserRole.THERAPIST) {
        if (assignedFor.reminderType && assignedFor.reminderType.email == true) {
          await EmailService.sendEventEmail(
            assignedFor,
            "Goal is deleted!",
            "Goal " + deletededGoal.title + " has been deleted by",
            "Login to view more information.",
            createdBy?.firstname + " " + createdBy?.lastname
          );
        }

        if(assignedFor.reminderType && assignedFor.reminderType.text == true) {
          await SMSService.sendEventSMS(
            `Goal ${deletededGoal.title} has been deleted by ${createdBy.firstname} ${createdBy.lastname}`,
            assignedFor.primaryPhone
          );
        }
      }

      if (req.user.role == UserRole.CLIENT && createdBy.role == UserRole.THERAPIST) {
        if(createdBy.reminderType && createdBy.reminderType.email == true){
        await EmailService.sendEventEmail(
          createdBy,
          "Goal is deleted!",
          "Goal " + deletededGoal.title + " has been deleted by",
          "Login to view more information.",
          assignedFor?.firstname + " " + assignedFor?.lastname
        );
      }
      
      if(createdBy.reminderType && createdBy.reminderType.text == true)
        await SMSService.sendEventSMS(
          `Goal ${deletededGoal.title} has been deleted by ${assignedFor.firstname} ${assignedFor.lastname}`,
          createdBy.primaryPhone
        );
      }

      return res.sendSuccess(deletededGoal, "Deleted Goal!");
    } catch (error) {
      return res.sendError(error);
    }
  }
}
