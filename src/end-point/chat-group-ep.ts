import { NextFunction, Request, Response } from "express";
import { check, validationR<PERSON>ult } from "express-validator";
import { Types } from "mongoose";
let mongoose = require("mongoose");
import { ChatGroupType, DChatGroup } from "../models/chat-group-model";
import { ChatGroupDao } from "../dao/chat-group-dao";
import {
  ChatGroupMemberType,
  DChatGroupMember,
} from "../models/chat-group-member-model";
import User from "../schemas/user-schema";
import FriendRequest from "../schemas/friend-request-schema";
import { FriendRequestStatus } from "../models/friend-request-model";
import ChatGroup from "../schemas/chat-group-schema";
import ChatGroupMember from "../schemas/chat-group-member-schema";
import moment = require("moment");
import {
  ChatGroupCallCallingStatus,
  DChatGroupCall,
} from "../models/chat-group-call-model";
import { User<PERSON><PERSON> } from "../models/user-model";
import ChatGroup<PERSON>all from "../schemas/chat-group-call-schema";
import { UploadCategory } from "./user-ep";
import multer = require("multer");
import { UploadDao } from "../dao/upload-dao";
import { DUpload } from "../models/upload-model";
var fs = require("fs");
import * as path from "path";
import Upload from "../schemas/upload-schema";
import PublicChatGroupRequest from "../schemas/public-chat-group-request-schema";
import {
  DPublicChatGroupRequest,
  PublicChatGroupRequestStatusType,
} from "../models/public-chat-group-request-model";
import { EmailService } from "../mail/config";
import { SMSService } from "../sms/config";
import {
  ChatGroupMessageStatus,
  DChatGroupMessage,
} from "../models/chat-group-message-model";
import ChatGroupMessage from "../schemas/chat-group-message-schema";

export namespace ChatGroupEp {
  // export function createChatGroupValidationRules() {
  //   return [
  //     check("title")
  //       .notEmpty()
  //       .withMessage("Title is required.")
  //       .isString()
  //       .withMessage("Invalid Title."),
  //     check("description")
  //       .notEmpty()
  //       .withMessage("Description is required.")
  //       .isString()
  //       .withMessage("Invalid Description."),
  //     check("type")
  //       .notEmpty()
  //       .withMessage("Type is required.")
  //       .isString()
  //       .withMessage("Invalid Chat Group Type.")
  //       .isIn([ChatGroupType.PUBLIC, ChatGroupType.PRIVATE])
  //       .withMessage("Invalid Chat Group Type"),
  //   ];
  // }
  export async function createChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const uploadCategory = UploadCategory.GROUP_ICON;

      const storage = multer.diskStorage({
        destination: async (req, FileRes, cb) => {
          await groupChatValidationRules(req, cb);
        },
      });

      async function groupChatValidationRules(req: any, cb: any) {
        try {
          const destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;
          let groupDetails = JSON.parse(req.body.groupDetails);

          if (
            !groupDetails.groupTitle ||
            typeof groupDetails.groupTitle !== "string" ||
            groupDetails.groupTitle.trim() === ""
          ) {
            return cb(Error("Group title is required."), null);
          }

          if (
            !groupDetails.groupDescription ||
            typeof groupDetails.groupDescription !== "string" ||
            groupDetails.groupDescription.trim() === ""
          ) {
            return cb(Error("Group description is required."), null);
          }

          if (
            !groupDetails.groupType ||
            typeof groupDetails.groupType !== "string" ||
            groupDetails.groupType.trim() === "" ||
            (groupDetails.groupType !== ChatGroupType.PUBLIC &&
              groupDetails.groupType !== ChatGroupType.PRIVATE)
          ) {
            return cb(Error("Group type is required."), null);
          }

          fs.access(destination, (error: any) => {
            if (error) {
              return fs.mkdir(destination, (error: any) =>
                cb(error, "destination")
              );
            } else {
              return cb(null, destination);
            }
          });
        } catch (error) {
          return cb(Error(error), null);
        }
      }

      const upload = multer({ storage: storage }).single("groupIcon");
      try {
        upload(req, res, async function (error: any) {
          try {
            if (error) {
              return res.sendError(error + " ");
            } else {
              let groupDetails = JSON.parse(req.body.groupDetails);
              let isValid = true;
              if (!req.file) {
                if (
                  !groupDetails.groupTitle ||
                  typeof groupDetails.groupTitle !== "string" ||
                  groupDetails.groupTitle.trim() === ""
                ) {
                  isValid = false;
                  return res.sendError("Group title is required.");
                }

                if (
                  !groupDetails.groupDescription ||
                  typeof groupDetails.groupDescription !== "string" ||
                  groupDetails.groupDescription.trim() === ""
                ) {
                  isValid = false;
                  return res.sendError("Group description is required.");
                }

                if (
                  !groupDetails.groupType ||
                  typeof groupDetails.groupType !== "string" ||
                  groupDetails.groupType.trim() === "" ||
                  (groupDetails.groupType !== ChatGroupType.PUBLIC &&
                    groupDetails.groupType !== ChatGroupType.PRIVATE)
                ) {
                  isValid = false;
                  return res.sendError("Group type is required.");
                }
              }
              if (isValid) {
                const groupIcon: any = req.file;
                if (groupIcon) {
                  let signRequired: boolean = false;

                  if (req.body.signRequired !== undefined) {
                    signRequired = req.body.signRequired;
                  }

                  const data: DUpload = {
                    userId: userId as unknown as Types.ObjectId,
                    originalName: groupIcon.originalname.replace(/ /g, ""),
                    name: groupIcon.filename,
                    type: groupIcon.mimetype,
                    path: groupIcon.path,
                    fileSize: groupIcon.size,
                    extension:
                      path.extname(groupIcon.originalname) ||
                      req.body.extension,
                    category: uploadCategory,
                    signRequired: signRequired,
                  };

                  let uploadedgroupIcon = await UploadDao.createUpload(data);
                  if (
                    uploadedgroupIcon != null &&
                    uploadedgroupIcon._id != null
                  ) {
                    const chatGroupDetails: DChatGroup = {
                      title: groupDetails.groupTitle,
                      description: groupDetails.groupDescription,
                      type: groupDetails.groupType,
                      createdBy: userId,
                      groupIcon: uploadedgroupIcon._id,
                    };

                    const createdChatGroup = await ChatGroupDao.createChatGroup(
                      chatGroupDetails
                    );

                    if (createdChatGroup == null) {
                      return res.sendError("Chat group creation failed.");
                    }

                    const createdChatGroupPopulated = await ChatGroup.findOne({
                      _id: createdChatGroup._id,
                    }).populate({
                      path: "groupIcon",
                      select: "_id",
                    });

                    const todayNow: Date = new Date();

                    let chatGroupMemberDetails: DChatGroupMember = {
                      groupId: createdChatGroup._id,
                      userId: userId,
                      role: ChatGroupMemberType.SUPER_ADMIN,
                      lastActive: todayNow,
                    };

                    const createdChatGroupMember =
                      await ChatGroupDao.createChatGroupMember(
                        chatGroupMemberDetails
                      );
                    return res.sendSuccess(
                      createdChatGroupPopulated,
                      "Chat group created successfully."
                    );
                  } else {
                    return res.sendError("File upload error occured.");
                  }
                } else {
                  const chatGroupDetails: DChatGroup = {
                    title: groupDetails.groupTitle,
                    description: groupDetails.groupDescription,
                    type: groupDetails.groupType,
                    createdBy: userId,
                  };

                  const createdChatGroup = await ChatGroupDao.createChatGroup(
                    chatGroupDetails
                  );

                  if (createdChatGroup == null) {
                    return res.sendError("Chat group creation failed.");
                  }

                  const createdChatGroupPopulated = await ChatGroup.findOne({
                    _id: createdChatGroup._id,
                  }).populate({
                    path: "groupIcon",
                    select: "_id",
                  });

                  const todayNow: Date = new Date();

                  let chatGroupMemberDetails: DChatGroupMember = {
                    groupId: createdChatGroup._id,
                    userId: userId,
                    role: ChatGroupMemberType.SUPER_ADMIN,
                    lastActive: todayNow,
                  };

                  const createdChatGroupMember =
                    await ChatGroupDao.createChatGroupMember(
                      chatGroupMemberDetails
                    );
                  return res.sendSuccess(
                    createdChatGroupPopulated,
                    "Chat group created successfully."
                  );
                }
              } else {
                return res.sendError("Invalid json data.");
              }
            }
          } catch (error) {
            return res.sendError(error + " ");
          }
        });
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }
  export async function updateChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const uploadCategory = UploadCategory.GROUP_ICON;

      const storage = multer.diskStorage({
        destination: async (req, FileRes, cb) => {
          await groupChatValidationRules(req, cb);
        },
      });

      async function groupChatValidationRules(req: any, cb: any) {
        try {
          const destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;
          let groupDetails = JSON.parse(req.body.groupDetails);

          if (
            !groupDetails.groupId ||
            typeof groupDetails.groupId !== "string"
          ) {
            return cb(Error("Group id is required."), null);
          }

          if (
            groupDetails.groupId == null ||
            !mongoose.Types.ObjectId.isValid(groupDetails.groupId)
          ) {
            return cb(Error("Invalid group id."), null);
          }

          const prevChatGroup = await ChatGroup.findOne({
            _id: groupDetails.groupId,
            createdBy: userId,
          });

          if (prevChatGroup == null) {
            return cb(Error("Invalid group."), null);
          }

          if (
            groupDetails.removeAlreadyUploadedIcon == null ||
            typeof groupDetails.removeAlreadyUploadedIcon !== "boolean"
          ) {
            return cb(Error("Remove Already Uploaded Icon is required."), null);
          }

          if (
            !groupDetails.groupTitle ||
            typeof groupDetails.groupTitle !== "string" ||
            groupDetails.groupTitle.trim() === ""
          ) {
            return cb(Error("Group title is required."), null);
          }

          if (
            !groupDetails.groupDescription ||
            typeof groupDetails.groupDescription !== "string" ||
            groupDetails.groupDescription.trim() === ""
          ) {
            return cb(Error("Group description is required."), null);
          }

          if (
            !groupDetails.groupType ||
            typeof groupDetails.groupType !== "string" ||
            groupDetails.groupType.trim() === "" ||
            (groupDetails.groupType !== ChatGroupType.PUBLIC &&
              groupDetails.groupType !== ChatGroupType.PRIVATE)
          ) {
            return cb(Error("Group type is required."), null);
          }

          fs.access(destination, (error: any) => {
            if (error) {
              return fs.mkdir(destination, (error: any) =>
                cb(error, "destination")
              );
            } else {
              return cb(null, destination);
            }
          });
        } catch (error) {
          return cb(Error(error), null);
        }
      }

      const upload = multer({ storage: storage }).single("groupIcon");
      try {
        upload(req, res, async function (error: any) {
          try {
            if (error) {
              return res.sendError(error + " ");
            } else {
              let groupDetails = JSON.parse(req.body.groupDetails);
              let isValid = true;
              let prevChatGroup;
              if (!req.file) {
                if (
                  !groupDetails.groupId ||
                  typeof groupDetails.groupId !== "string"
                ) {
                  isValid = false;
                  return res.sendError("Group id is required.");
                }

                if (
                  groupDetails.groupId == null ||
                  !mongoose.Types.ObjectId.isValid(groupDetails.groupId)
                ) {
                  isValid = false;
                  return res.sendError("Invalid group id");
                }

                prevChatGroup = await ChatGroup.findOne({
                  _id: groupDetails.groupId,
                  createdBy: userId,
                });

                if (prevChatGroup == null) {
                  isValid = false;
                  return res.sendError("Invalid group.");
                }
                console.log(groupDetails.removeAlreadyUploadedIcon);
                console.log(typeof groupDetails.removeAlreadyUploadedIcon);
                if (
                  groupDetails.removeAlreadyUploadedIcon == null ||
                  typeof groupDetails.removeAlreadyUploadedIcon !== "boolean"
                ) {
                  isValid = false;
                  return res.sendError(
                    "Remove Already Uploaded Icon is required."
                  );
                }

                if (
                  !groupDetails.groupTitle ||
                  typeof groupDetails.groupTitle !== "string" ||
                  groupDetails.groupTitle.trim() === ""
                ) {
                  isValid = false;
                  return res.sendError("Group title is required.");
                }

                if (
                  !groupDetails.groupDescription ||
                  typeof groupDetails.groupDescription !== "string" ||
                  groupDetails.groupDescription.trim() === ""
                ) {
                  isValid = false;
                  return res.sendError("Group description is required.");
                }

                if (
                  !groupDetails.groupType ||
                  typeof groupDetails.groupType !== "string" ||
                  groupDetails.groupType.trim() === "" ||
                  (groupDetails.groupType !== ChatGroupType.PUBLIC &&
                    groupDetails.groupType !== ChatGroupType.PRIVATE)
                ) {
                  isValid = false;
                  return res.sendError("Group type is required.");
                }
              } else {
                prevChatGroup = await ChatGroup.findOne({
                  _id: groupDetails.groupId,
                  createdBy: userId,
                });

                if (prevChatGroup == null) {
                  isValid = false;
                  return res.sendError("Invalid group.");
                }
              }
              if (isValid) {
                const groupIcon: any = req.file;
                if (groupIcon) {
                  let signRequired: boolean = false;

                  if (req.body.signRequired !== undefined) {
                    signRequired = req.body.signRequired;
                  }

                  const data: DUpload = {
                    userId: userId as unknown as Types.ObjectId,
                    originalName: groupIcon.originalname.replace(/ /g, ""),
                    name: groupIcon.filename,
                    type: groupIcon.mimetype,
                    path: groupIcon.path,
                    fileSize: groupIcon.size,
                    extension:
                      path.extname(groupIcon.originalname) ||
                      req.body.extension,
                    category: uploadCategory,
                    signRequired: signRequired,
                  };

                  let uploadedgroupIcon = await UploadDao.createUpload(data);
                  if (
                    uploadedgroupIcon != null &&
                    uploadedgroupIcon._id != null
                  ) {
                    const updatedChatGroupDetails = {
                      title: groupDetails.groupTitle,
                      description: groupDetails.groupDescription,
                      groupIcon: uploadedgroupIcon._id,
                    };

                    const updatedChatGroup = await ChatGroup.findOneAndUpdate(
                      { _id: prevChatGroup._id },
                      {
                        $set: updatedChatGroupDetails,
                      },
                      { new: true }
                    );

                    if (updatedChatGroup == null) {
                      return res.sendError("Chat group update failed.");
                    }

                    const updatedChatGroupPopulated = await ChatGroup.findOne({
                      _id: updatedChatGroup._id,
                    }).populate({
                      path: "groupIcon",
                      select: "_id",
                    });

                    // remove previous icon
                    try {
                      if (prevChatGroup.groupIcon != null) {
                        const prevUpload = await Upload.findOne({
                          _id: prevChatGroup.groupIcon,
                        });

                        if (
                          prevUpload != null &&
                          prevUpload._id != null &&
                          prevUpload.name != null
                        ) {
                          const prevFilePath = `${process.env.UPLOAD_PATH}/${uploadCategory}/${prevUpload.name}`;

                          if (fs.existsSync(prevFilePath)) {
                            fs.unlinkSync(prevFilePath);
                            await Upload.findOneAndDelete({
                              _id: prevUpload._id,
                            });
                          }
                        }
                      }
                    } catch (error) {}

                    return res.sendSuccess(
                      updatedChatGroupPopulated,
                      "Chat group updated successfully."
                    );
                  } else {
                    return res.sendError("File upload error occured.");
                  }
                } else {
                  const updatedChatGroupDetails = {
                    title: groupDetails.groupTitle,
                    description: groupDetails.groupDescription,
                  };
                  let updatedChatGroup;
                  if (
                    groupDetails.removeAlreadyUploadedIcon != null &&
                    groupDetails.removeAlreadyUploadedIcon == true &&
                    prevChatGroup.groupIcon != null
                  ) {
                    updatedChatGroup = await ChatGroup.findOneAndUpdate(
                      { _id: prevChatGroup._id },
                      {
                        $set: updatedChatGroupDetails,
                        $unset: { groupIcon: "" },
                      },
                      { new: true }
                    );
                  } else {
                    updatedChatGroup = await ChatGroup.findOneAndUpdate(
                      { _id: prevChatGroup._id },
                      {
                        $set: updatedChatGroupDetails,
                      },
                      { new: true }
                    );
                  }

                  if (updatedChatGroup == null) {
                    return res.sendError("Chat group update failed.");
                  }

                  const updatedChatGroupPopulated = await ChatGroup.findOne({
                    _id: updatedChatGroup._id,
                  }).populate({
                    path: "groupIcon",
                    select: "_id",
                  });
                  // remove previous icon
                  try {
                    if (
                      groupDetails.removeAlreadyUploadedIcon != null &&
                      groupDetails.removeAlreadyUploadedIcon == true &&
                      prevChatGroup.groupIcon != null
                    ) {
                      const prevUpload = await Upload.findOne({
                        _id: prevChatGroup.groupIcon,
                      });

                      if (
                        prevUpload != null &&
                        prevUpload._id != null &&
                        prevUpload.name != null
                      ) {
                        const prevFilePath = `${process.env.UPLOAD_PATH}/${uploadCategory}/${prevUpload.name}`;

                        if (fs.existsSync(prevFilePath)) {
                          fs.unlinkSync(prevFilePath);
                          await Upload.findOneAndDelete({
                            _id: prevUpload._id,
                          });
                        }
                      }
                    }
                  } catch (error) {}

                  return res.sendSuccess(
                    updatedChatGroupPopulated,
                    "Chat group updated successfully."
                  );
                }
              } else {
                return res.sendError("Invalid json data.");
              }
            }
          } catch (error) {
            return res.sendError(error + " ");
          }
        });
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }
  export function addMemberToChatGroupValidationRules() {
    return [
      check("groupId")
        .notEmpty()
        .withMessage("Group Id is required.")
        .isMongoId()
        .withMessage("Invalid Group Id."),
      check("memberId")
        .notEmpty()
        .withMessage("Member Id is required.")
        .isMongoId()
        .withMessage("Invalid Member Id."),
    ];
  }
  export async function addMemberToChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      const userId = req.user._id;
      const groupId = req.body.groupId;
      const memberId = req.body.memberId;

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
        createdBy: userId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const friendRequest = await FriendRequest.findOne({
        clientId: memberId,
        therapistId: userId,
        status: FriendRequestStatus.APPROVED,
      });

      if (friendRequest == null) {
        return res.sendError("You can't add unmatched clients to the group.");
      }

      const member = await User.findOne({
        _id: memberId,
        lavniTestAccount: { $ne: true },
        $or: [{ blockedByAdmin: false }, { blockedByAdmin: undefined }],
      });

      if (member == null) {
        return res.sendError("Invalid member.");
      }

      if (member.role != UserRole.CLIENT) {
        return res.sendError("Only clients can be add as members");
      }

      const alreadyExistMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: member._id,
      });

      if (alreadyExistMember != null) {
        return res.sendError("Member already exists.");
      }

      // Check if there's any main member in the group
      const existingMainMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        is_main: true
      });

      const todayNow: Date = new Date();

      const memberDetails: DChatGroupMember = {
        groupId: chatGroup._id,
        userId: member._id,
        role: ChatGroupMemberType.MEMBER,
        lastActive: todayNow,
        is_main: existingMainMember ? false : true // Set is_main = true if no main member exists
      };

      const createdChatMember = await ChatGroupDao.createChatGroupMember(
        memberDetails
      );

      if (createdChatMember == null) {
        return res.sendError("Add member to group failed.");
      }

      const newMember = {
        _id: member._id,
        firstname: member.firstname,
        lastname: member.lastname,
        role: createdChatMember.role,
        photoId: member.photoId,
        is_main: createdChatMember.is_main
      };

      if (member.email) {
        await EmailService.sendGroupChatEventEmail(
          member,
          member.firstname ?? "user",
          `You're Added to ${chatGroup.title} chat group`,
          `You're Added to ${chatGroup.title} ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group.`
        );
      }

      if (member.primaryPhone) {
        await SMSService.sendGroupChatEventSMS(
          `Hi ${member.firstname ?? "user"}, you're Added to ${
            chatGroup.title
          } ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group.`,
          member.primaryPhone
        );
      }

      return res.sendSuccess(newMember, "Member added successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }
  export function removeMemberFromChatGroupValidationRules() {
    return [
      check("groupId")
        .notEmpty()
        .withMessage("Group Id is required.")
        .isMongoId()
        .withMessage("Invalid Group Id."),
      check("memberId")
        .notEmpty()
        .withMessage("Member Id is required.")
        .isMongoId()
        .withMessage("Invalid Member Id."),
    ];
  }
  export async function removeMemberFromChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      const userId = req.user._id;
      const groupId = req.body.groupId;
      const memberId = req.body.memberId;

      if (memberId == userId) {
        return res.sendError("Can't delete own entry.");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
        createdBy: userId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const member = await User.findOne({
        _id: memberId,
      });

      if (member == null) {
        return res.sendError("Invalid member.");
      }

      const alreadyExistMember = await ChatGroupMember.findOneAndDelete({
        groupId: chatGroup._id,
        userId: memberId,
      });

      if (alreadyExistMember == null) {
        return res.sendError("Remove member from group failed.");
      }

      if (member.email) {
        await EmailService.sendGroupChatEventEmail(
          member,
          member.firstname ?? "user",
          `You're Removed from ${chatGroup.title} chat group`,
          `You're Removed from ${chatGroup.title} ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group.`
        );
      }

      if (member.primaryPhone) {
        await SMSService.sendGroupChatEventSMS(
          `Hi ${member.firstname ?? "user"}, you're Removed from ${
            chatGroup.title
          } ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group.`,
          member.primaryPhone
        );
      }

      return res.sendSuccess("Member removed successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllChatGroupsForUser(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const prevGroupId = req.body.prevGroupId;
      const groupTypeFromReq = req.body.type;
      const limit = Number(req.body.limit);
      const offset = Number(req.body.offset);

      let prevSelectedGroupId;

      let groupTypeFor: ChatGroupType = ChatGroupType.PRIVATE;

      if (
        groupTypeFromReq != null &&
        (groupTypeFromReq === ChatGroupType.PRIVATE || ChatGroupType.PUBLIC)
      ) {
        groupTypeFor = groupTypeFromReq;
      }

      if (prevGroupId != null) {
        if (mongoose.Types.ObjectId.isValid(prevGroupId)) {
          const alreadyExistMember = await ChatGroupMember.findOne({
            groupId: prevGroupId,
            userId: userId,
          });

          if (alreadyExistMember != null && alreadyExistMember._id != null) {
            const chatGroup = await ChatGroup.findOne({
              _id: alreadyExistMember.groupId,
            });

            if (
              chatGroup != null &&
              chatGroup._id != null &&
              chatGroup.type != null
            ) {
              groupTypeFor = chatGroup.type;
              prevSelectedGroupId = chatGroup._id;
            }
          }
        }
      }

      const chatGroups = await ChatGroupMember.aggregate([
        {
          $match: {
            userId: userId,
          },
        },
        {
          $lookup: {
            from: "chatgroups",
            localField: "groupId",
            foreignField: "_id",
            as: "chatGroups",
          },
        },
        {
          $unwind: "$chatGroups",
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: [
                "$chatGroups",
                {
                  lastActive: {
                    $ifNull: ["$$ROOT.lastActive", "$$ROOT.createdAt"],
                  },
                  memberAddTime: "$$ROOT.createdAt",
                },
              ],
            },
          },
        },
        {
          $match: {
            type: groupTypeFor,
          },
        },
        {
          $lookup: {
            from: "chatgroupmessages",
            let: {
              groupId: "$_id",
              lastActive: "$lastActive",
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$groupId", "$$groupId"] },
                      { $gt: ["$createdAt", "$$lastActive"] },
                    ],
                  },
                },
              },
            ],
            as: "filteredGroupMessages",
          },
        },
        {
          $lookup: {
            from: "chatgroupmessages",
            localField: "_id",
            foreignField: "groupId",
            as: "groupMessages",
          },
        },
        {
          $addFields: {
            unreadMessageCount: { $size: "$filteredGroupMessages" },
            latestMessagePrev: { $arrayElemAt: ["$groupMessages", -1] },
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "latestMessagePrev.createdBy",
            foreignField: "_id",
            as: "userDetails",
          },
        },
        {
          $addFields: {
            latestMessage: {
              _id: { $ifNull: ["$latestMessagePrev._id", ""] },
              firstname: {
                $ifNull: [{ $arrayElemAt: ["$userDetails.firstname", 0] }, ""],
              },
              lastname: {
                $ifNull: [{ $arrayElemAt: ["$userDetails.lastname", 0] }, ""],
              },
              messageText: {
                $cond: {
                  if: {
                    $or: [
                      { $eq: ["$latestMessagePrev.messageStatus", null] },
                      {
                        $ne: [
                          "$latestMessagePrev.messageStatus",
                          ChatGroupMessageStatus.DELETED,
                        ],
                      },
                    ],
                  },
                  then: { $ifNull: ["$latestMessagePrev.messageText", ""] },
                  else: "",
                },
              },
              messageStatus: {
                $ifNull: ["$latestMessagePrev.messageStatus", null],
              },
              createdBy: { $ifNull: ["$latestMessagePrev.createdBy", ""] },
              createdAt: { $ifNull: ["$latestMessagePrev.createdAt", ""] },
              mediaFileId: {
                $cond: {
                  if: {
                    $or: [
                      { $eq: ["$latestMessagePrev.messageStatus", null] },
                      {
                        $ne: [
                          "$latestMessagePrev.messageStatus",
                          ChatGroupMessageStatus.DELETED,
                        ],
                      },
                    ],
                  },
                  then: { $ifNull: ["$latestMessagePrev.mediaFileId", null] },
                  else: null,
                },
              },
            },
          },
        },
        {
          $addFields: {
            latestActiveTime: {
              $ifNull: ["$latestMessagePrev.createdAt", "$memberAddTime"],
            },
          },
        },
        {
          $sort: {
            latestActiveTime: -1,
          },
        },
        {
          $project: {
            filteredGroupMessages: 0,
            groupMessages: 0,
            latestMessagePrev: 0,
            userDetails: 0,
            chatGroups: 0,
            latestActiveTime: 0,
            memberAddTime: 0,
          },
        },
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ]);

      if (chatGroups == null) {
        return res.sendError("Get all chat groups for user failed.");
      }

      let populated = await Upload.populate(chatGroups, [
        {
          path: "groupIcon",
          select: {
            _id: 1,
          },
        },
      ]);

      if (populated == null) {
        return res.sendError("Gell all chat groups for user failed.");
      }

      return res.sendSuccess(populated, "Chat groups for user.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function getAllUsersInChatGroupValidationRules() {
    return [
      check("groupId")
        .notEmpty()
        .withMessage("Group Id is required.")
        .isMongoId()
        .withMessage("Invalid Group Id."),
      check("searchableString")
        .notEmpty({ ignore_whitespace: true })
        .withMessage("Title is required.")
        .isString()
        .withMessage("Invalid Title."),
      check("limit")
        .notEmpty()
        .withMessage("Limit is required.")
        .isInt({ min: 1 })
        .withMessage("Limit must be a positive integer."),
      check("offset")
        .notEmpty()
        .withMessage("Offset is required.")
        .isInt({ min: 0 })
        .withMessage("Offset must be a non-negative integer."),
    ];
  }
  export async function getAllUsersInChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const groupId = req.body.groupId;
      const limit = Number(req.body.limit);
      const offset = Number(req.body.offset);

      if (groupId == null || !mongoose.Types.ObjectId.isValid(groupId)) {
        return res.sendError("Invalid group id");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const alreadyExistMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: userId,
      });

      if (alreadyExistMember == null) {
        return res.sendError("You are not a member.");
      }

      const groupIdFrom = chatGroup._id;

      let searchedName = null;
      if (
        req.body.searchableString != null &&
        req.body.searchableString != ""
      ) {
        let searchableString = req.body.searchableString;
        let seacrhItem = searchableString.replace(/\s/g, "");
        searchedName =
          searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
      }

      const nameQuery =
        searchedName != null && searchedName
          ? {
              $and: [
                {
                  $or: [
                    { firstname: searchedName },
                    { lastname: searchedName },
                  ],
                },
                { _id: { $ne: userId } },
              ],
            }
          : {};

      const allChatMembers = await ChatGroupMember.aggregate([
        {
          $match: {
            groupId: groupIdFrom,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "userDetails",
          },
        },
        {
          $unwind: "$userDetails",
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: [
                "$userDetails",
                {
                  role: "$role",
                  is_main: {
                    $ifNull: ["$is_main", false]
                  }
                }
              ]
            },
          },
        },
        {
          $project: {
            _id: "$_id",
            firstname: 1,
            lastname: 1,
            role: 1,
            photoId: 1,
            is_main: 1
          },
        },
        {
          $match: nameQuery,
        },
        {
          $addFields: {
            memberTypeOrder: {
              $switch: {
                branches: [
                  { case: { $eq: ["$_id", userId] }, then: 1 },
                  { case: { $eq: ["$role", "SUPER_ADMIN"] }, then: 2 },
                  { case: { $eq: ["$role", "ADMIN"] }, then: 3 },
                  { case: { $eq: ["$role", "MEMBER"] }, then: 4 },
                ],
                default: 5,
              },
            },
          },
        },
        {
          $sort: {
            memberTypeOrder: 1,
          },
        },
        {
          $project: {
            memberTypeOrder: 0,
          },
        },
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ]);

      const allChatMembersCount = await ChatGroupMember.aggregate([
        {
          $match: {
            groupId: groupIdFrom,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "userDetails",
          },
        },
        {
          $unwind: "$userDetails",
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ["$userDetails", "$$ROOT"] },
          },
        },
        {
          $project: {
            _id: "$userDetails._id",
            firstname: 1,
            lastname: 1,
            role: "$role",
            photoId: 1,
          },
        },
        {
          $match: nameQuery,
        },
      ]);

      if (
        allChatMembers == null ||
        allChatMembersCount == null ||
        allChatMembersCount.length == null
      ) {
        return res.sendError("Get all members for chat group failed.");
      }

      let allChatMembersPopulated = await Upload.populate(allChatMembers, [
        {
          path: "photoId",
          select: {
            _id: 1,
          },
        },
      ]);

      if (allChatMembersPopulated == null) {
        return res.sendError("Get all members for chat group failed.");
      }

      let finalResult = {
        members: allChatMembersPopulated,
        count: allChatMembersCount.length,
      };

      return res.sendSuccess(finalResult, "All group members.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function getAllSessionsInChatGroupValidationRules() {
    return [
      check("groupId")
        .notEmpty()
        .withMessage("Group Id is required.")
        .isMongoId()
        .withMessage("Invalid Group Id."),
      check("limit")
        .notEmpty()
        .withMessage("Limit is required.")
        .isInt({ min: 1 })
        .withMessage("Limit must be a positive integer."),
      check("offset")
        .notEmpty()
        .withMessage("Offset is required.")
        .isInt({ min: 0 })
        .withMessage("Offset must be a non-negative integer."),
    ];
  }

  export async function getAllSessionsInChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const groupId = req.body.groupId;
      const limit = Number(req.body.limit);
      const offset = Number(req.body.offset);

      if (groupId == null || !mongoose.Types.ObjectId.isValid(groupId)) {
        return res.sendError("Invalid group id");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const alreadyExistMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: userId,
      });

      if (alreadyExistMember == null) {
        return res.sendError("You are not a member.");
      }

      const groupIdFrom = chatGroup._id;

      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);

      const allChatGroupSessions = await ChatGroupCall.aggregate([
        {
          $match: {
            groupId: groupIdFrom,
          },
        },
        {
          $addFields: {
            daysDiff: {
              $abs: {
                $subtract: ["$start", currentDate],
              },
            },
            statusOrder: {
              $switch: {
                branches: [
                  { case: { $eq: ["$callingStatus", "ONGOING"] }, then: 1 },
                  { case: { $eq: ["$callingStatus", "PENDING"] }, then: 2 },
                ],
                default: 3,
              },
            },
            pastDate: {
              $lt: ["$start", currentDate],
            },
          },
        },
        {
          $sort: {
            statusOrder: 1,
            pastDate: 1,
            daysDiff: 1,
          },
        },
        {
          $project: {
            _id: 1,
            groupId: 1,
            callingStatus: 1,
            start: 1,
            createdBy: 1,
            createdAt: 1,
          },
        },
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ]);

      const allChatGroupSessionsCount = await ChatGroupCall.aggregate([
        {
          $match: {
            groupId: groupIdFrom,
          },
        },
        {
          $addFields: {
            daysDiff: {
              $abs: {
                $subtract: ["$start", currentDate],
              },
            },
            statusOrder: {
              $switch: {
                branches: [
                  { case: { $eq: ["$callingStatus", "ONGOING"] }, then: 1 },
                  { case: { $eq: ["$callingStatus", "PENDING"] }, then: 2 },
                ],
                default: 3,
              },
            },
            pastDate: {
              $lt: ["$start", currentDate],
            },
          },
        },
        {
          $sort: {
            statusOrder: 1,
            pastDate: 1,
            daysDiff: 1,
          },
        },
        {
          $project: {
            _id: 1,
            groupId: 1,
            callingStatus: 1,
            start: 1,
            createdBy: 1,
            createdAt: 1,
          },
        },
      ]);

      if (
        allChatGroupSessions == null ||
        allChatGroupSessionsCount == null ||
        allChatGroupSessionsCount.length == null
      ) {
        return res.sendError("Get all sessions for chat group failed.");
      }

      let finalResult = {
        sessions: allChatGroupSessions,
        count: allChatGroupSessionsCount.length,
      };

      return res.sendSuccess(finalResult, "All group sessions.");
    } catch (error) {
      return res.sendError(error);
    }
  }


  export function getAllMatchedClientsForChatGroupValidationRules() {
    return [
      check("groupId")
        .notEmpty()
        .withMessage("Group Id is required.")
        .isMongoId()
        .withMessage("Invalid Group Id."),
      check("searchableString")
        .notEmpty({ ignore_whitespace: true })
        .withMessage("Title is required.")
        .isString()
        .withMessage("Invalid Title."),
      check("limit")
        .notEmpty()
        .withMessage("Limit is required.")
        .isInt({ min: 1 })
        .withMessage("Limit must be a positive integer."),
      check("offset")
        .notEmpty()
        .withMessage("Offset is required.")
        .isInt({ min: 0 })
        .withMessage("Offset must be a non-negative integer."),
    ];
  }

  export async function getAllMatchedClientsForChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const groupId = req.body.groupId;
      const limit = Number(req.body.limit);
      const offset = Number(req.body.offset);

      if (groupId == null || !mongoose.Types.ObjectId.isValid(groupId)) {
        return res.sendError("Invalid group id");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
        createdBy: userId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      // const queryConditions: any = {
      //   "clientDetails.lavniTestAccount": { $ne: true },
      // };
      const queryConditions: any = {};

      const groupIdFrom = chatGroup._id;
      let searchedName = null;
      if (
        req.body.searchableString != null &&
        req.body.searchableString != ""
      ) {
        let searchableString = req.body.searchableString;
        let seacrhItem = searchableString.replace(/\s/g, "");
        searchedName =
          searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
      }

      const nameQuery =
        searchedName != null && searchedName
          ? {
              $and: [
                {
                  $or: [
                    { firstname: searchedName },
                    { lastname: searchedName },
                  ],
                },
                { _id: { $ne: userId } },
              ],
            }
          : {};

      const allMatchedClients = await FriendRequest.aggregate([
        {
          $match: {
            $and: [
              { therapistId: userId },
              { status: FriendRequestStatus.APPROVED },
            ],
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            as: "clientDetails",
          },
        },
        {
          $unwind: {
            path: "$clientDetails",
          },
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ["$clientDetails", "$$ROOT"] },
          },
        },
        {
          $match: {
            $and: [queryConditions],
            $or: [
              { "clientDetails.blockedByAdmin": false },
              { "clientDetails.blockedByAdmin": undefined },
            ],
          },
        },
        {
          $project: {
            _id: "$clientDetails._id",
            firstname: 1,
            lastname: 1,
            photoId: 1,
          },
        },
        {
          $lookup: {
            from: "chatgroupmembers",
            localField: "_id",
            foreignField: "userId",
            as: "groups",
          },
        },
        {
          $match: {
            "groups.groupId": {
              $ne: groupIdFrom,
            },
          },
        },
        {
          $project: {
            _id: 1,
            firstname: 1,
            lastname: 1,
            photoId: 1,
          },
        },
        {
          $match: nameQuery,
        },
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ]);

      const allMatchedClientsCount = await FriendRequest.aggregate([
        {
          $match: {
            $and: [
              { therapistId: userId },
              { status: FriendRequestStatus.APPROVED },
            ],
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            as: "clientDetails",
          },
        },
        {
          $unwind: {
            path: "$clientDetails",
          },
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ["$clientDetails", "$$ROOT"] },
          },
        },
        {
          $match: {
            $and: [queryConditions],
            $or: [
              { "clientDetails.blockedByAdmin": false },
              { "clientDetails.blockedByAdmin": undefined },
            ],
          },
        },
        {
          $project: {
            _id: "$clientDetails._id",
            firstname: 1,
            lastname: 1,
            photoId: 1,
          },
        },
        {
          $lookup: {
            from: "chatgroupmembers",
            localField: "_id",
            foreignField: "userId",
            as: "groups",
          },
        },
        {
          $match: {
            "groups.groupId": {
              $ne: groupIdFrom,
            },
          },
        },
        {
          $project: {
            _id: 1,
            firstname: 1,
            lastname: 1,
            photoId: 1,
          },
        },
        {
          $match: nameQuery,
        },
      ]);

      if (
        allMatchedClients == null ||
        allMatchedClientsCount == null ||
        allMatchedClientsCount.length == null
      ) {
        return res.sendError("Gell all matched clients for chat group failed.");
      }

      let allMatchedClientsPopulated = await Upload.populate(
        allMatchedClients,
        [
          {
            path: "photoId",
            select: {
              _id: 1,
            },
          },
        ]
      );

      if (allMatchedClientsPopulated == null) {
        return res.sendError("Gell all matched clients for chat group failed.");
      }

      let finalResult = {
        members: allMatchedClientsPopulated,
        count: allMatchedClientsCount.length,
      };

      return res.sendSuccess(
        finalResult,
        "All matched clients for chat group."
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function getAllPublicChatGroupsForClientValidationRules() {
    return [
      check("limit")
        .notEmpty()
        .withMessage("Limit is required.")
        .isInt({ min: 1 })
        .withMessage("Limit must be a positive integer."),
      check("offset")
        .notEmpty()
        .withMessage("Offset is required.")
        .isInt({ min: 0 })
        .withMessage("Offset must be a non-negative integer."),
    ];
  }

  export async function getAllPublicChatGroupsForClient(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      const userId = req.user._id;
      const limit = Number(req.body.limit);
      const offset = Number(req.body.offset);

      const publicChatGroups = await ChatGroup.aggregate([
        {
          $match: {
            type: ChatGroupType.PUBLIC,
          },
        },
        {
          $lookup: {
            from: "chatgroupmembers",
            localField: "_id",
            foreignField: "groupId",
            as: "members",
          },
        },
        {
          $addFields: {
            memberCount: { $size: "$members" },
          },
        },
        {
          $match: {
            members: { $not: { $elemMatch: { userId: userId } } },
          },
        },
        {
          $lookup: {
            from: "publicchatgrouprequests",
            let: { groupId: "$_id", userId: userId },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$groupId", "$$groupId"] },
                      { $eq: ["$userId", "$$userId"] },
                    ],
                  },
                },
              },
              { $limit: 1 },
              {
                $project: {
                  _id: 0,
                  status: 1,
                },
              },
            ],
            as: "requests",
          },
        },
        {
          $addFields: {
            requestStatus: {
              $ifNull: [{ $arrayElemAt: ["$requests.status", 0] }, "NONE"],
            },
          },
        },
        {
          $project: {
            members: 0,
            requests: 0,
          },
        },
        {
          $sort: {
            createdAt: -1,
          },
        },
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ]);

      if (publicChatGroups == null) {
        return res.sendError("Get all public chat groups for client failed.");
      }

      let populated = await Upload.populate(publicChatGroups, [
        {
          path: "groupIcon",
          select: {
            _id: 1,
          },
        },
      ]);

      if (populated == null) {
        return res.sendError("Gell all public chat groups for client failed.");
      }

      return res.sendSuccess(populated, "Public chat groups for client.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendJoinRequestForPublicGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const groupId = req.body.groupId;

      if (groupId == null || !mongoose.Types.ObjectId.isValid(groupId)) {
        return res.sendError("Invalid group id");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const alreadyExistMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: userId,
      });

      if (alreadyExistMember != null) {
        return res.sendError("You already exists as member.");
      }

      const alreadyExistRequest = await PublicChatGroupRequest.findOne({
        groupId: chatGroup._id,
        userId: userId,
      });

      if (alreadyExistRequest != null) {
        const result = await PublicChatGroupRequest.findOneAndUpdate(
          { _id: alreadyExistRequest._id },
          { $set: { status: PublicChatGroupRequestStatusType.PENDING } },
          { new: true }
        );

        if (result == null) {
          return res.sendError("Join request to group failed.");
        }
      } else {
        const requestDetails: DPublicChatGroupRequest = {
          groupId: chatGroup._id,
          userId: userId,
          status: PublicChatGroupRequestStatusType.PENDING,
        };

        const createdRequest = await ChatGroupDao.createPublicChatGroupRequest(
          requestDetails
        );

        if (createdRequest == null) {
          return res.sendError("Join request to group failed.");
        }
      }

      const clientUser = await User.findOne({
        _id: userId,
      });

      const therapistUser = await User.findOne({
        _id: chatGroup.createdBy,
      });

      if (therapistUser && clientUser && therapistUser.email) {
        await EmailService.sendGroupChatEventEmail(
          therapistUser,
          therapistUser.firstname ?? "user",
          `New join request to ${chatGroup.title} chat group`,
          `New join request to ${chatGroup.title} ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group recieved from ${clientUser.firstname ?? "user"}.`
        );

        if (therapistUser.primaryPhone) {
          await SMSService.sendGroupChatEventSMS(
            `Hi ${therapistUser.firstname ?? "user"}, new join request to ${
              chatGroup.title
            } ${
              chatGroup.type == "PUBLIC" ? "peer support" : "private"
            } chat group recieved from ${clientUser.firstname ?? "user"}.`,
            therapistUser.primaryPhone
          );
        }
      }

      return res.sendSuccess("Request added successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function cancellJoinRequestForPublicGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const groupId = req.body.groupId;

      if (groupId == null || !mongoose.Types.ObjectId.isValid(groupId)) {
        return res.sendError("Invalid group id");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const alreadyExistMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: userId,
      });

      if (alreadyExistMember != null) {
        return res.sendError("You already exists as member.");
      }

      const alreadyExistRequest = await PublicChatGroupRequest.findOne({
        groupId: chatGroup._id,
        userId: userId,
      });

      if (alreadyExistRequest == null) {
        return res.sendError("No request found.");
      }

      const result = await PublicChatGroupRequest.findOneAndUpdate(
        { _id: alreadyExistRequest._id },
        { $set: { status: PublicChatGroupRequestStatusType.CANCELLED } },
        { new: true }
      );

      if (result == null) {
        return res.sendError("Cancelling request failed.");
      }

      const clientUser = await User.findOne({
        _id: userId,
      });

      const therapistUser = await User.findOne({
        _id: chatGroup.createdBy,
      });

      if (therapistUser && clientUser && therapistUser.email) {
        await EmailService.sendGroupChatEventEmail(
          therapistUser,
          therapistUser.firstname ?? "user",
          `Canceled join request to ${chatGroup.title} chat group`,
          `Join request to ${chatGroup.title} ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group canceled by ${clientUser.firstname ?? "user"}.`
        );

        if (therapistUser.primaryPhone) {
          await SMSService.sendGroupChatEventSMS(
            `Hi ${therapistUser.firstname ?? "user"}, join request to ${
              chatGroup.title
            } ${
              chatGroup.type == "PUBLIC" ? "peer support" : "private"
            } chat group canceled by ${clientUser.firstname ?? "user"}.`,
            therapistUser.primaryPhone
          );
        }
      }

      return res.sendSuccess("Request cancelled successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getAllJoinRequestForPublicChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const groupId = req.body.groupId;
      const limit = Number(req.body.limit);
      const offset = Number(req.body.offset);

      if (groupId == null || !mongoose.Types.ObjectId.isValid(groupId)) {
        return res.sendError("Invalid group id");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
        createdBy: userId,
        type: ChatGroupType.PUBLIC,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const groupIdFrom = chatGroup._id;
      let searchedName = null;
      if (
        req.body.searchableString != null &&
        req.body.searchableString != ""
      ) {
        let searchableString = req.body.searchableString;
        let seacrhItem = searchableString.replace(/\s/g, "");
        searchedName =
          searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
      }

      const nameQuery =
        searchedName != null && searchedName
          ? {
              $and: [
                {
                  $or: [
                    { firstname: searchedName },
                    { lastname: searchedName },
                  ],
                },
                { _id: { $ne: userId } },
              ],
            }
          : {};

      const allRequests = await PublicChatGroupRequest.aggregate([
        {
          $match: {
            groupId: groupIdFrom,
            status: { $in: [PublicChatGroupRequestStatusType.PENDING] },
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "clientDetails",
          },
        },
        {
          $unwind: {
            path: "$clientDetails",
          },
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ["$clientDetails", "$$ROOT"] },
          },
        },
        {
          $match: {
            "clientDetails.lavniTestAccount": { $ne: true },
            $or: [
              { "clientDetails.blockedByAdmin": false },
              { "clientDetails.blockedByAdmin": undefined },
            ],
          },
        },
        {
          $project: {
            _id: 1,
            firstname: 1,
            lastname: 1,
            photoId: 1,
            status: 1,
            userId: "$clientDetails._id",
          },
        },
        {
          $match: nameQuery,
        },
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ]);

      const allRequestsCount = await PublicChatGroupRequest.aggregate([
        {
          $match: {
            groupId: groupIdFrom,
            status: { $in: [PublicChatGroupRequestStatusType.PENDING] },
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "clientDetails",
          },
        },
        {
          $unwind: {
            path: "$clientDetails",
          },
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ["$clientDetails", "$$ROOT"] },
          },
        },
        {
          $match: {
            "clientDetails.lavniTestAccount": { $ne: true },
            $or: [
              { "clientDetails.blockedByAdmin": false },
              { "clientDetails.blockedByAdmin": undefined },
            ],
          },
        },
        {
          $project: {
            _id: 1,
            firstname: 1,
            lastname: 1,
            photoId: 1,
            status: 1,
            userId: "$clientDetails._id",
          },
        },
        {
          $match: nameQuery,
        },
      ]);

      if (
        allRequests == null ||
        allRequestsCount == null ||
        allRequestsCount.length == null
      ) {
        return res.sendError("Gell all requests for chat group failed.");
      }

      let allRequestsPopulated = await Upload.populate(allRequests, [
        {
          path: "photoId",
          select: {
            _id: 1,
          },
        },
      ]);

      if (allRequestsPopulated == null) {
        return res.sendError("Gell all requests for chat group failed.");
      }

      let finalResult = {
        requests: allRequestsPopulated,
        count: allRequestsCount.length,
      };

      return res.sendSuccess(finalResult, "All requests for chat group.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function acceptJoinRequestForPublicGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistUserId = req.user._id;
      const requestId = req.body.requestId;

      if (requestId == null || !mongoose.Types.ObjectId.isValid(requestId)) {
        return res.sendError("Invalid request id");
      }

      const existsJoinRequest = await PublicChatGroupRequest.findOne({
        _id: requestId,
        status: PublicChatGroupRequestStatusType.PENDING,
      });

      if (existsJoinRequest == null) {
        return res.sendError("Invalid join request.");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: existsJoinRequest.groupId,
        createdBy: therapistUserId,
        type: ChatGroupType.PUBLIC,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const client = await User.findOne({
        _id: existsJoinRequest.userId,
        lavniTestAccount: { $ne: true },
        $or: [{ blockedByAdmin: false }, { blockedByAdmin: undefined }],
      });

      if (client == null) {
        return res.sendError("Invalid member.");
      }

      if (client.role != UserRole.CLIENT) {
        return res.sendError("Only clients can be add as members");
      }

      const alreadyExistMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: client._id,
      });

      if (alreadyExistMember != null) {
        return res.sendError("Already exists as member.");
      }

      const todayNow: Date = new Date();

      const memberDetails: DChatGroupMember = {
        groupId: chatGroup._id,
        userId: client._id,
        role: ChatGroupMemberType.MEMBER,
        lastActive: todayNow,
      };

      const createdChatMember = await ChatGroupDao.createChatGroupMember(
        memberDetails
      );

      if (createdChatMember == null) {
        return res.sendError("Add member to group failed.");
      }

      const updatedRequest = await PublicChatGroupRequest.findOneAndUpdate(
        { _id: existsJoinRequest._id },
        { $set: { status: PublicChatGroupRequestStatusType.ACCEPTED } },
        { new: true }
      );

      if (client.email) {
        await EmailService.sendGroupChatEventEmail(
          client,
          client.firstname ?? "user",
          `Join request to ${chatGroup.title} chat group accepted`,
          `Join request to ${chatGroup.title} ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group accepted.`
        );
      }

      if (client.primaryPhone) {
        await SMSService.sendGroupChatEventSMS(
          `Hi ${client.firstname ?? "user"}, join request to ${
            chatGroup.title
          } ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group accepted.`,
          client.primaryPhone
        );
      }

      return res.sendSuccess("Request accepted successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function rejectJoinRequestForPublicGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapistUserId = req.user._id;
      const requestId = req.body.requestId;

      if (requestId == null || !mongoose.Types.ObjectId.isValid(requestId)) {
        return res.sendError("Invalid request id");
      }

      const existsJoinRequest = await PublicChatGroupRequest.findOne({
        _id: requestId,
        status: PublicChatGroupRequestStatusType.PENDING,
      });

      if (existsJoinRequest == null) {
        return res.sendError("Invalid join request.");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: existsJoinRequest.groupId,
        createdBy: therapistUserId,
        type: ChatGroupType.PUBLIC,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const client = await User.findOne({
        _id: existsJoinRequest.userId,
      });

      if (client == null) {
        return res.sendError("Invalid member.");
      }

      if (client.role != UserRole.CLIENT) {
        return res.sendError("Only clients can be add as members");
      }

      const alreadyExistMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: client._id,
      });

      if (alreadyExistMember != null) {
        return res.sendError("Already exists as member.");
      }

      const updatedRequest = await PublicChatGroupRequest.findOneAndUpdate(
        { _id: existsJoinRequest._id },
        { $set: { status: PublicChatGroupRequestStatusType.REJECTED } },
        { new: true }
      );

      if (updatedRequest == null) {
        return res.sendError("Reject join request failed.");
      }

      if (client.email) {
        await EmailService.sendGroupChatEventEmail(
          client,
          client.firstname ?? "user",
          `Join request to ${chatGroup.title} chat group rejected`,
          `Join request to ${chatGroup.title} ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group rejected.`
        );
      }

      if (client.primaryPhone) {
        await SMSService.sendGroupChatEventSMS(
          `Hi ${client.firstname ?? "user"}, join request to ${
            chatGroup.title
          } ${
            chatGroup.type == "PUBLIC" ? "peer support" : "private"
          } chat group rejected.`,
          client.primaryPhone
        );
      }

      return res.sendSuccess("Request rejected successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  // chat api's
  // export function sendTextMessageValidationRules() {
  //   return [
  //     check("groupId")
  //       .notEmpty()
  //       .withMessage("Group Id is required.")
  //       .isMongoId()
  //       .withMessage("Invalid Group Id."),
  //     check("messageText")
  //       .notEmpty()
  //       .withMessage("Message Text is required.")
  //       .isString()
  //       .withMessage("Invalid Message Text."),
  //   ];
  // }

  // export async function sendTextMessage(
  //   req: Request,
  //   res: Response,
  //   next: NextFunction
  // ) {
  //   try {
  //     const errors = validationResult(req);
  //     if (!errors.isEmpty()) {
  //       return res.sendError(errors.array()[0]);
  //     }

  //     const userId = req.user._id;
  //     const groupId = req.body.groupId;
  //     const messageText = req.body.messageText;

  //     const preMessageId = req.body.preMessageId;

  //     let preChatMessage;

  //     if (preMessageId) {
  //       if (!mongoose.Types.ObjectId.isValid(preMessageId)) {
  //         return res.sendError("Invalid Pre Message Id.");
  //       }
  //       preChatMessage = await ChatGroupMessage.findOne({
  //         _id: preMessageId,
  //       });

  //       if (preChatMessage == null) {
  //         return res.sendError("Invalid Pre Message Id.");
  //       }
  //     }

  //     const chatGroup = await ChatGroup.findOne({
  //       _id: groupId,
  //     });

  //     if (chatGroup == null) {
  //       return res.sendError("Invalid group.");
  //     }

  //     const alreadyExistMember = await ChatGroupMember.findOne({
  //       groupId: chatGroup._id,
  //       userId: userId,
  //     });

  //     if (alreadyExistMember == null) {
  //       return res.sendError("You are not a member.");
  //     }

  //     let messageDetails: DChatGroupMessage;

  //     if (preChatMessage != null && preChatMessage._id != null) {
  //       messageDetails = {
  //         groupId: chatGroup._id,
  //         messageText: messageText,
  //         createdBy: userId,
  //         preMessageId: preChatMessage._id,
  //       };
  //     } else {
  //       messageDetails = {
  //         groupId: chatGroup._id,
  //         messageText: messageText,
  //         createdBy: userId,
  //       };
  //     }

  //     const createdChatMessage = await ChatGroupDao.createChatGroupMessage(
  //       messageDetails
  //     );

  //     if (createdChatMessage == null) {
  //       return res.sendError("Send text message failed.");
  //     }

  //     const todayNow: Date = new Date();

  //     const updatedMember = await ChatGroupMember.findOneAndUpdate(
  //       {
  //         groupId: chatGroup._id,
  //         userId: userId,
  //       },
  //       {
  //         $set: { lastActive: todayNow },
  //       },
  //       { new: true }
  //     );

  //     const userDetails = await User.findOne({
  //       _id: userId,
  //     });

  //     let finalResult;
  //     if (preChatMessage != null && preChatMessage._id != null) {
  //       const preChatMessageUserDetails = await User.findOne({
  //         _id: preChatMessage.createdBy,
  //       });

  //       let preMessageDetails = {
  //         _id: preChatMessage._id,
  //         firstname:
  //           preChatMessageUserDetails != null &&
  //           preChatMessageUserDetails.firstname != null
  //             ? preChatMessageUserDetails.firstname
  //             : "",
  //         lastname:
  //           preChatMessageUserDetails != null &&
  //           preChatMessageUserDetails.lastname != null
  //             ? preChatMessageUserDetails.lastname
  //             : "",
  //         messageText: preChatMessage.messageText,
  //         createdBy: preChatMessage.createdBy,
  //         createdAt: preChatMessage.createdAt,
  //       };

  //       finalResult = {
  //         _id: createdChatMessage._id,
  //         firstname: userDetails.firstname,
  //         lastname: userDetails.lastname,
  //         messageText: createdChatMessage.messageText,
  //         createdBy: createdChatMessage.createdBy,
  //         createdAt: createdChatMessage.createdAt,
  //         preMessageId: preMessageDetails,
  //       };
  //     } else {
  //       finalResult = {
  //         _id: createdChatMessage._id,
  //         firstname: userDetails.firstname,
  //         lastname: userDetails.lastname,
  //         messageText: createdChatMessage.messageText,
  //         createdBy: createdChatMessage.createdBy,
  //         createdAt: createdChatMessage.createdAt,
  //       };
  //     }

  //     return res.sendSuccess(finalResult, "Message sent successfully.");
  //   } catch (error) {
  //     return res.sendError(error);
  //   }
  // }

  export function getAllMessagesInChatGroupValidationRules() {
    return [
      check("groupId")
        .notEmpty()
        .withMessage("Group Id is required.")
        .isMongoId()
        .withMessage("Invalid Group Id."),
      check("limit")
        .notEmpty()
        .withMessage("Limit is required.")
        .isInt({ min: 1 })
        .withMessage("Limit must be a positive integer."),
      check("offset")
        .notEmpty()
        .withMessage("Offset is required.")
        .isInt({ min: 0 })
        .withMessage("Offset must be a non-negative integer."),
    ];
  }
  export async function getAllMessagesInChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const groupId = req.body.groupId;
      const limit = Number(req.body.limit);
      const offset = Number(req.body.offset);

      if (groupId == null || !mongoose.Types.ObjectId.isValid(groupId)) {
        return res.sendError("Invalid group id");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const alreadyExistMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: userId,
      });

      if (alreadyExistMember == null) {
        return res.sendError("You are not a member.");
      }

      const groupIdFrom = chatGroup._id;

      const allChatMessages = await ChatGroupMessage.aggregate([
        {
          $match: {
            groupId: groupIdFrom,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            as: "userDetails",
          },
        },
        {
          $unwind: "$userDetails",
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ["$userDetails", "$$ROOT"] },
          },
        },
        {
          $project: {
            _id: 1,
            firstname: 1,
            lastname: 1,
            messageText: {
              $cond: {
                if: {
                  $or: [
                    { $eq: ["$messageStatus", null] },
                    {
                      $ne: ["$messageStatus", ChatGroupMessageStatus.DELETED],
                    },
                  ],
                },
                then: "$messageText",
                else: "",
              },
            },
            messageStatus: 1,
            createdBy: 1,
            createdAt: 1,
            preMessageId: {
              $cond: {
                if: {
                  $or: [
                    { $eq: ["$messageStatus", null] },
                    {
                      $ne: ["$messageStatus", ChatGroupMessageStatus.DELETED],
                    },
                  ],
                },
                then: { $ifNull: ["$preMessageId", null] },
                else: null,
              },
            },
            mediaFileId: {
              $cond: {
                if: {
                  $or: [
                    { $eq: ["$messageStatus", null] },
                    {
                      $ne: ["$messageStatus", ChatGroupMessageStatus.DELETED],
                    },
                  ],
                },
                then: { $ifNull: ["$mediaFileId", null] },
                else: null,
              },
            },
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "mediaFileId",
            foreignField: "_id",
            as: "mediaDetails",
          },
        },
        {
          $addFields: {
            mediaDetails: {
              $ifNull: [{ $arrayElemAt: ["$mediaDetails", 0] }, null],
            },
          },
        },
        {
          $lookup: {
            from: "chatgroupmessages",
            localField: "preMessageId",
            foreignField: "_id",
            as: "preMessageDetails",
          },
        },
        {
          $addFields: {
            preMessageDetails: {
              $ifNull: [{ $arrayElemAt: ["$preMessageDetails", 0] }, null],
            },
          },
        },
        {
          $lookup: {
            from: "uploads",
            let: { mediaFileId: "$preMessageDetails.mediaFileId" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$_id", { $ifNull: ["$$mediaFileId", null] }],
                  },
                },
              },
            ],
            as: "preMediaDetails",
          },
        },
        {
          $unwind: {
            path: "$preMediaDetails",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            let: { createdBy: "$preMessageDetails.createdBy" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$_id", { $ifNull: ["$$createdBy", null] }],
                  },
                },
              },
            ],
            as: "preUserDetails",
          },
        },
        {
          $unwind: {
            path: "$preUserDetails",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            preMessageId: {
              $cond: {
                if: {
                  $and: [
                    { $ne: ["$preMessageDetails", null] },
                    { $ne: ["$preMessageDetails._id", null] },
                  ],
                },
                then: {
                  _id: "$preMessageDetails._id",
                  firstname: {
                    $ifNull: ["$preUserDetails.firstname", ""],
                  },
                  lastname: {
                    $ifNull: ["$preUserDetails.lastName", ""],
                  },
                  messageText: {
                    $cond: {
                      if: {
                        $or: [
                          { $eq: ["$preMessageDetails.messageStatus", null] },
                          {
                            $ne: [
                              "$preMessageDetails.messageStatus",
                              ChatGroupMessageStatus.DELETED,
                            ],
                          },
                        ],
                      },
                      then: "$preMessageDetails.messageText",
                      else: "",
                    },
                  },
                  messageStatus: "$preMessageDetails.messageStatus",
                  createdBy: "$preMessageDetails.createdBy",
                  createdAt: "$preMessageDetails.createdAt",
                  mediaFileId: {
                    $cond: {
                      if: {
                        $and: [
                          {
                            $or: [
                              {
                                $eq: ["$preMessageDetails.messageStatus", null],
                              },
                              {
                                $ne: [
                                  "$preMessageDetails.messageStatus",
                                  ChatGroupMessageStatus.DELETED,
                                ],
                              },
                            ],
                          },
                          { $ne: ["$preMediaDetails", null] },
                          { $ne: ["$preMediaDetails._id", null] },
                          { $ne: ["$preMediaDetails.extension", null] },
                          { $ne: [{ $type: "$preMediaDetails" }, "missing"] },
                          {
                            $ne: [{ $type: "$preMediaDetails._id" }, "missing"],
                          },
                          {
                            $ne: [
                              { $type: "$preMediaDetails.extension" },
                              "missing",
                            ],
                          },
                        ],
                      },
                      then: {
                        _id: { $ifNull: ["$preMediaDetails._id", null] },
                        extension: {
                          $ifNull: ["$preMediaDetails.extension", null],
                        },
                      },
                      else: null,
                    },
                  },
                },
                else: null,
              },
            },
            mediaFileId: {
              $cond: {
                if: {
                  $and: [
                    { $ne: ["$mediaDetails", null] },
                    { $ne: ["$mediaDetails._id", null] },
                    { $ne: ["$mediaDetails.extension", null] },
                    { $ne: [{ $type: "$mediaDetails" }, "missing"] },
                    {
                      $ne: [{ $type: "$mediaDetails._id" }, "missing"],
                    },
                    {
                      $ne: [{ $type: "$mediaDetails.extension" }, "missing"],
                    },
                  ],
                },
                then: {
                  _id: { $ifNull: ["$mediaDetails._id", null] },
                  extension: {
                    $ifNull: ["$mediaDetails.extension", null],
                  },
                },
                else: null,
              },
            },
          },
        },
        {
          $project: {
            preMessageDetails: 0,
            preUserDetails: 0,
            mediaDetails: 0,
            preMediaDetails: 0,
          },
        },
        {
          $sort: {
            createdAt: -1,
          },
        },
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ]);

      if (allChatMessages == null) {
        return res.sendError("Get all messages for chat group failed.");
      }

      return res.sendSuccess(allChatMessages, "All group messages.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function updateLastActiveOfMemberValidationRules() {
    return [
      check("groupId")
        .notEmpty()
        .withMessage("Group Id is required.")
        .isMongoId()
        .withMessage("Invalid Group Id."),
    ];
  }

  export async function updateLastActiveOfMember(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      const userId = req.user._id;
      const groupId = req.body.groupId;

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      const todayNow: Date = new Date();

      const updatedMember = await ChatGroupMember.findOneAndUpdate(
        {
          groupId: chatGroup._id,
          userId: userId,
        },
        {
          $set: { lastActive: todayNow },
        },
        { new: true }
      );

      if (updatedMember == null) {
        return res.sendError("Failed to update last active.");
      }

      return res.sendSuccess("Last active updated successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function deleteMessageValidationRules() {
    return [
      check("messageId")
        .notEmpty()
        .withMessage("Message Id is required.")
        .isMongoId()
        .withMessage("Invalid Message Id."),
    ];
  }

  export async function deleteMessage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }

      const userId = req.user._id;
      const messageId = req.body.messageId;

      const chatMessage = await ChatGroupMessage.findOne({
        _id: messageId,
        createdBy: userId,
      });

      if (chatMessage == null) {
        return res.sendError("Invalid Chat Message.");
      }

      const messageGroupIdFrom = chatMessage.groupId;

      const alreadyExistMember = await ChatGroupMember.findOne({
        groupId: messageGroupIdFrom,
        userId: userId,
      });

      if (alreadyExistMember == null) {
        return res.sendError("You are not a member.");
      }

      const removedMessage = await ChatGroupMessage.findOneAndUpdate(
        {
          _id: messageId,
          createdBy: userId,
        },
        {
          $set: { messageStatus: ChatGroupMessageStatus.DELETED },
        },
        { new: true }
      );

      if (removedMessage == null) {
        return res.sendError("Delete message failed.");
      }

      return res.sendSuccess("Message deleted successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function sendMessage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const uploadCategory = UploadCategory.GROUP_CHAT_MEDIA;

      const storage = multer.diskStorage({
        destination: async (req, FileRes, cb) => {
          await groupChatMessageValidationRules(req, cb);
        },
      });

      async function groupChatMessageValidationRules(req: any, cb: any) {
        try {
          const destination = `${process.env.UPLOAD_PATH}/${uploadCategory}`;
          let messageDetails = JSON.parse(req.body.messageDetails);

          if (
            !messageDetails.groupId ||
            typeof messageDetails.groupId !== "string"
          ) {
            return cb(Error("Group id is required."), null);
          }

          if (
            messageDetails.groupId == null ||
            !mongoose.Types.ObjectId.isValid(messageDetails.groupId)
          ) {
            return cb(Error("Invalid group id."), null);
          }

          const prevChatGroup = await ChatGroup.findOne({
            _id: messageDetails.groupId,
          });

          if (prevChatGroup == null) {
            return cb(Error("Invalid group."), null);
          }

          const alreadyExistMember = await ChatGroupMember.findOne({
            groupId: prevChatGroup._id,
            userId: userId,
          });

          if (alreadyExistMember == null) {
            return cb(Error("You are not a member."), null);
          }

          if (messageDetails.preMessageId != null) {
            if (
              !messageDetails.preMessageId ||
              typeof messageDetails.preMessageId !== "string" ||
              messageDetails.preMessageId.trim() === "" ||
              !mongoose.Types.ObjectId.isValid(messageDetails.preMessageId)
            ) {
              return cb(Error("Invalid Pre Message Id."), null);
            }

            const preChatMessage = await ChatGroupMessage.findOne({
              _id: messageDetails.preMessageId,
            });

            if (preChatMessage == null) {
              return cb(Error("Invalid Pre Message Id."), null);
            }
          }

          fs.access(destination, (error: any) => {
            if (error) {
              return fs.mkdir(destination, (error: any) =>
                cb(error, "destination")
              );
            } else {
              return cb(null, destination);
            }
          });
        } catch (error) {
          return cb(Error(error), null);
        }
      }

      const upload = multer({ storage: storage }).single("messageMedia");
      try {
        upload(req, res, async function (error: any) {
          try {
            if (error) {
              return res.sendError(error + " ");
            } else {
              let messageDetails = JSON.parse(req.body.messageDetails);
              let isValid = true;
              if (!req.file) {
                if (
                  !messageDetails.groupId ||
                  typeof messageDetails.groupId !== "string"
                ) {
                  isValid = false;
                  return res.sendError("Group id is required.");
                }

                if (
                  !messageDetails.messageText ||
                  typeof messageDetails.messageText !== "string" ||
                  messageDetails.messageText.trim() === ""
                ) {
                  isValid = false;
                  return res.sendError("Message Text is required.");
                }

                if (
                  messageDetails.groupId == null ||
                  !mongoose.Types.ObjectId.isValid(messageDetails.groupId)
                ) {
                  isValid = false;
                  return res.sendError("Invalid group id.");
                }

                const prevChatGroup = await ChatGroup.findOne({
                  _id: messageDetails.groupId,
                });

                if (prevChatGroup == null) {
                  isValid = false;
                  return res.sendError("Invalid group.");
                }

                const alreadyExistMember = await ChatGroupMember.findOne({
                  groupId: prevChatGroup._id,
                  userId: userId,
                });

                if (alreadyExistMember == null) {
                  isValid = false;
                  return res.sendError("You are not a member.");
                }

                if (messageDetails.preMessageId != null) {
                  if (
                    !messageDetails.preMessageId ||
                    typeof messageDetails.preMessageId !== "string" ||
                    messageDetails.preMessageId.trim() === "" ||
                    !mongoose.Types.ObjectId.isValid(
                      messageDetails.preMessageId
                    )
                  ) {
                    isValid = false;
                    return res.sendError("Invalid Pre Message Id.");
                  }

                  const preChatMessage = await ChatGroupMessage.findOne({
                    _id: messageDetails.preMessageId,
                  });

                  if (preChatMessage == null) {
                    isValid = false;
                    return res.sendError("Invalid Pre Message Id.");
                  }
                }
              }
              if (isValid) {
                const messageMediaFiles: any = req.file;
                if (messageMediaFiles) {
                  let signRequired: boolean = false;

                  if (req.body.signRequired !== undefined) {
                    signRequired = req.body.signRequired;
                  }

                  let preChatMessage;

                  if (messageDetails.preMessageId != null) {
                    preChatMessage = await ChatGroupMessage.findOne({
                      _id: messageDetails.preMessageId,
                    }).populate({
                      path: "mediaFileId",
                      select: "_id extension",
                    });
                  }

                  const dataToUpload: DUpload = {
                    userId: userId as unknown as Types.ObjectId,
                    originalName: messageMediaFiles.originalname.replace(
                      / /g,
                      ""
                    ),
                    name: messageMediaFiles.filename,
                    type: messageMediaFiles.mimetype,
                    path: messageMediaFiles.path,
                    fileSize: messageMediaFiles.size,
                    extension:
                      path.extname(messageMediaFiles.originalname) ||
                      req.body.extension,
                    category: uploadCategory,
                    signRequired: signRequired,
                  };

                  let uploadedMessageMedia = await UploadDao.createUpload(
                    dataToUpload
                  );
                  if (
                    uploadedMessageMedia != null &&
                    uploadedMessageMedia._id != null
                  ) {
                    let messageDetailsTo: DChatGroupMessage;

                    const prevChatGroupIn = await ChatGroup.findOne({
                      _id: messageDetails.groupId,
                    });

                    if (preChatMessage != null && preChatMessage._id != null) {
                      messageDetailsTo = {
                        groupId: prevChatGroupIn._id,
                        messageText:
                          !messageDetails.messageText ||
                          typeof messageDetails.messageText !== "string" ||
                          messageDetails.messageText.trim() === ""
                            ? ""
                            : messageDetails.messageText,
                        createdBy: userId,
                        preMessageId: preChatMessage._id,
                        mediaFileId: uploadedMessageMedia._id,
                      };
                    } else {
                      messageDetailsTo = {
                        groupId: prevChatGroupIn._id,
                        messageText:
                          !messageDetails.messageText ||
                          typeof messageDetails.messageText !== "string" ||
                          messageDetails.messageText.trim() === ""
                            ? ""
                            : messageDetails.messageText,
                        createdBy: userId,
                        mediaFileId: uploadedMessageMedia._id,
                      };
                    }

                    const createdChatMessageInitial =
                      await ChatGroupDao.createChatGroupMessage(
                        messageDetailsTo
                      );

                    if (createdChatMessageInitial == null) {
                      return res.sendError("Send message failed.");
                    }

                    const createdChatMessage = await ChatGroupMessage.findOne({
                      _id: createdChatMessageInitial._id,
                    }).populate({
                      path: "mediaFileId",
                      select: "_id extension",
                    });

                    if (createdChatMessage == null) {
                      return res.sendError("Send message failed.");
                    }

                    const todayNow: Date = new Date();

                    const updatedMember =
                      await ChatGroupMember.findOneAndUpdate(
                        {
                          groupId: prevChatGroupIn._id,
                          userId: userId,
                        },
                        {
                          $set: { lastActive: todayNow },
                        },
                        { new: true }
                      );

                    const userDetails = await User.findOne({
                      _id: userId,
                    });

                    let finalResult;
                    if (preChatMessage != null && preChatMessage._id != null) {
                      const preChatMessageUserDetails = await User.findOne({
                        _id: preChatMessage.createdBy,
                      });

                      let preMessageDetails = {
                        _id: preChatMessage._id,
                        firstname:
                          preChatMessageUserDetails != null &&
                          preChatMessageUserDetails.firstname != null
                            ? preChatMessageUserDetails.firstname
                            : "",
                        lastname:
                          preChatMessageUserDetails != null &&
                          preChatMessageUserDetails.lastname != null
                            ? preChatMessageUserDetails.lastname
                            : "",
                        messageText: preChatMessage.messageText,
                        createdBy: preChatMessage.createdBy,
                        createdAt: preChatMessage.createdAt,
                        mediaFileId: preChatMessage.mediaFileId,
                      };

                      finalResult = {
                        _id: createdChatMessage._id,
                        firstname: userDetails.firstname,
                        lastname: userDetails.lastname,
                        messageText: createdChatMessage.messageText,
                        createdBy: createdChatMessage.createdBy,
                        createdAt: createdChatMessage.createdAt,
                        preMessageId: preMessageDetails,
                        mediaFileId: createdChatMessage.mediaFileId,
                      };
                    } else {
                      finalResult = {
                        _id: createdChatMessage._id,
                        firstname: userDetails.firstname,
                        lastname: userDetails.lastname,
                        messageText: createdChatMessage.messageText,
                        createdBy: createdChatMessage.createdBy,
                        createdAt: createdChatMessage.createdAt,
                        mediaFileId: createdChatMessage.mediaFileId,
                      };
                    }

                    return res.sendSuccess(
                      finalResult,
                      "Message sent successfully."
                    );
                  } else {
                    return res.sendError("File upload error occured.");
                  }
                } else {
                  let preChatMessage;

                  if (messageDetails.preMessageId != null) {
                    preChatMessage = await ChatGroupMessage.findOne({
                      _id: messageDetails.preMessageId,
                    }).populate({
                      path: "mediaFileId",
                      select: "_id extension",
                    });
                  }

                  let messageDetailsTo: DChatGroupMessage;

                  const prevChatGroupIn = await ChatGroup.findOne({
                    _id: messageDetails.groupId,
                  });

                  if (preChatMessage != null && preChatMessage._id != null) {
                    messageDetailsTo = {
                      groupId: prevChatGroupIn._id,
                      messageText: messageDetails.messageText,
                      createdBy: userId,
                      preMessageId: preChatMessage._id,
                    };
                  } else {
                    messageDetailsTo = {
                      groupId: prevChatGroupIn._id,
                      messageText: messageDetails.messageText,
                      createdBy: userId,
                    };
                  }

                  const createdChatMessage =
                    await ChatGroupDao.createChatGroupMessage(messageDetailsTo);

                  if (createdChatMessage == null) {
                    return res.sendError("Send message failed.");
                  }

                  const todayNow: Date = new Date();

                  const updatedMember = await ChatGroupMember.findOneAndUpdate(
                    {
                      groupId: prevChatGroupIn._id,
                      userId: userId,
                    },
                    {
                      $set: { lastActive: todayNow },
                    },
                    { new: true }
                  );

                  const userDetails = await User.findOne({
                    _id: userId,
                  });

                  let finalResult;
                  if (preChatMessage != null && preChatMessage._id != null) {
                    const preChatMessageUserDetails = await User.findOne({
                      _id: preChatMessage.createdBy,
                    });

                    let preMessageDetails = {
                      _id: preChatMessage._id,
                      firstname:
                        preChatMessageUserDetails != null &&
                        preChatMessageUserDetails.firstname != null
                          ? preChatMessageUserDetails.firstname
                          : "",
                      lastname:
                        preChatMessageUserDetails != null &&
                        preChatMessageUserDetails.lastname != null
                          ? preChatMessageUserDetails.lastname
                          : "",
                      messageText: preChatMessage.messageText,
                      createdBy: preChatMessage.createdBy,
                      createdAt: preChatMessage.createdAt,
                      mediaFileId: preChatMessage.mediaFileId,
                    };

                    finalResult = {
                      _id: createdChatMessage._id,
                      firstname: userDetails.firstname,
                      lastname: userDetails.lastname,
                      messageText: createdChatMessage.messageText,
                      createdBy: createdChatMessage.createdBy,
                      createdAt: createdChatMessage.createdAt,
                      preMessageId: preMessageDetails,
                    };
                  } else {
                    finalResult = {
                      _id: createdChatMessage._id,
                      firstname: userDetails.firstname,
                      lastname: userDetails.lastname,
                      messageText: createdChatMessage.messageText,
                      createdBy: createdChatMessage.createdBy,
                      createdAt: createdChatMessage.createdAt,
                    };
                  }

                  return res.sendSuccess(
                    finalResult,
                    "Message sent successfully."
                  );
                }
              } else {
                return res.sendError("Invalid json data.");
              }
            }
          } catch (error) {
            return res.sendError(error + " ");
          }
        });
      } catch (error) {
        return res.sendError(error);
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateMainMemberInChatGroup(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user._id;
      const groupId = req.body.groupId;
      const memberId = req.body.memberId;
      const isMain = req.body.isMain;

      if (groupId == null || !mongoose.Types.ObjectId.isValid(groupId)) {
        return res.sendError("Invalid group id");
      }

      if (memberId == null || !mongoose.Types.ObjectId.isValid(memberId)) {
        return res.sendError("Invalid member id");
      }

      const chatGroup = await ChatGroup.findOne({
        _id: groupId,
      });

      if (chatGroup == null) {
        return res.sendError("Invalid group.");
      }

      // Check if requester is member of group
      const requesterMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: userId,
      });

      if (requesterMember == null) {
        return res.sendError("You are not a member of this group.");
      }

      // Only SUPER_ADMIN and ADMIN can update main member
      if (requesterMember.role !== ChatGroupMemberType.SUPER_ADMIN && 
          requesterMember.role !== ChatGroupMemberType.ADMIN) {
        return res.sendError("You don't have permission to update main member.");
      }

      // Check if target member exists
      const targetMember = await ChatGroupMember.findOne({
        groupId: chatGroup._id,
        userId: memberId,
      });

      if (targetMember == null) {
        return res.sendError("Target member not found in group.");
      }

      if (isMain) {
        // If setting as main, remove main flag from other members first
        await ChatGroupMember.updateMany(
          { 
            groupId: chatGroup._id,
            is_main: true 
          },
          { 
            $set: { is_main: false } 
          }
        );
      }

      // Update target member
      const updatedMember = await ChatGroupMember.findOneAndUpdate(
        {
          groupId: chatGroup._id,
          userId: memberId,
        },
        {
          $set: { is_main: isMain }
        },
        { new: true }
      );

      if (updatedMember == null) {
        return res.sendError("Update main member failed.");
      }

      return res.sendSuccess(updatedMember, "Main member updated successfully.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export function getAllChatGroupCallsByTherapistIdValidationRules() {
    return [
      check("therapistId")
        .notEmpty()
        .withMessage("Therapist Id is required.")
        .isMongoId()
        .withMessage("Invalid Therapist Id."),
      check("limit")
        .notEmpty()
        .withMessage("Limit is required.")
        .isInt({ min: 1 })
        .withMessage("Limit must be a positive integer."),
      check("offset")
        .notEmpty()
        .withMessage("Offset is required.")
        .isInt({ min: 0 })
        .withMessage("Offset must be a non-negative integer."),
    ];
  }

  export async function getAllChatGroupCallsByTherapistId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]);
      }
      
      const therapistId = req.body.therapistId;
      const limit = Number(req.body.limit);
      const offset = Number(req.body.offset);

      if (therapistId == null || !mongoose.Types.ObjectId.isValid(therapistId)) {
        return res.sendError("Invalid therapist id");
      }

      // Get all groups created by the therapist
      const chatGroups = await ChatGroup.find({
        createdBy: therapistId
      });

      if (!chatGroups || chatGroups.length === 0) {
        return res.sendSuccess({
          sessions: [],
          count: 0
        }, "No chat groups found for this therapist.");
      }

      // Get all groupId from chat groups
      const groupIds = chatGroups.map(group => group._id);

      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);
      
      // Calculate 60 days before and 60 days after
      const sixtyDaysBefore = new Date(currentDate);
      sixtyDaysBefore.setDate(currentDate.getDate() - 60);
      
      const sixtyDaysAfter = new Date(currentDate);
      sixtyDaysAfter.setDate(currentDate.getDate() + 60);

      // Get all chat sessions based on the groupIds list
      const allChatGroupSessions = await ChatGroupCall.aggregate([
        {
          $match: {
            groupId: { $in: groupIds },
            start: { 
              $gte: sixtyDaysBefore, 
              $lte: sixtyDaysAfter 
            }
          },
        },
        {
          $addFields: {
            daysDiff: {
              $abs: {
                $subtract: ["$start", currentDate],
              },
            },
            statusOrder: {
              $switch: {
                branches: [
                  { case: { $eq: ["$callingStatus", "ONGOING"] }, then: 1 },
                  { case: { $eq: ["$callingStatus", "PENDING"] }, then: 2 },
                ],
                default: 3,
              },
            },
            pastDate: {
              $lt: ["$start", currentDate],
            },
          },
        },
        {
          $sort: {
            statusOrder: 1,
            pastDate: 1,
            daysDiff: 1,
          },
        },
        {
          $lookup: {
            from: "chatgroups",
            localField: "groupId",
            foreignField: "_id",
            as: "groupDetails"
          }
        },
        {
          $unwind: "$groupDetails"
        },
        {
          $project: {
            _id: 1,
            groupId: 1,
            callingStatus: 1,
            start: 1,
            createdBy: 1,
            createdAt: 1,
            "groupDetails.title": 1,
            "groupDetails.description": 1,
            "groupDetails.type": 1
          },
        },
        {
          $skip: offset,
        },
        {
          $limit: limit,
        },
      ]);

      const allChatGroupSessionsCount = await ChatGroupCall.aggregate([
        {
          $match: {
            groupId: { $in: groupIds },
            start: { 
              $gte: sixtyDaysBefore, 
              $lte: sixtyDaysAfter 
            }
          },
        },
        {
          $addFields: {
            daysDiff: {
              $abs: {
                $subtract: ["$start", currentDate],
              },
            },
            statusOrder: {
              $switch: {
                branches: [
                  { case: { $eq: ["$callingStatus", "ONGOING"] }, then: 1 },
                  { case: { $eq: ["$callingStatus", "PENDING"] }, then: 2 },
                ],
                default: 3,
              },
            },
            pastDate: {
              $lt: ["$start", currentDate],
            },
          },
        },
        {
          $sort: {
            statusOrder: 1,
            pastDate: 1,
            daysDiff: 1,
          },
        },
        {
          $project: {
            _id: 1,
            groupId: 1,
            callingStatus: 1,
            start: 1,
            createdBy: 1,
            createdAt: 1,
          },
        },
      ]);

      if (
        allChatGroupSessions == null ||
        allChatGroupSessionsCount == null ||
        allChatGroupSessionsCount.length == null
      ) {
        return res.sendError("Get all sessions for chat group failed.");
      }

      let finalResult = {
        sessions: allChatGroupSessions,
        count: allChatGroupSessionsCount.length,
      };

      return res.sendSuccess(finalResult, "All group sessions for therapist.");
    } catch (error) {
      return res.sendError(error);
    }
  }
}
