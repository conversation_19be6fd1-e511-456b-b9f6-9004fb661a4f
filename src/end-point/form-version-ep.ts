import { NextFunction, Request, Response } from "express";
import { Types } from "mongoose";
import { UserRole } from "../models/user-model";
import { FormVersionDao } from "../dao/form-version-dao";
import { DTherapyPlanVersion } from "../models/therapy-plan-version-model";
import { AppLogger } from "../common/logging";
import { UserDao } from "../dao/user-dao";
import { DClinicalAssesmentVersion } from "../models/clinicalAssesment-version-model";
import { DDigitalAssessmentVersion } from "../models/digital-assessment-version-model";
import { TherapistDao } from "../dao/therapist-dao";

export namespace FormVersionEp {
  export async function getCurrentTherapyPlanById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You dont't have permission for insurance documents approval section!"
            );
          }
        }

        const formId = Types.ObjectId(req.params.id);
        let currentTherapyPlan = await FormVersionDao.getCurrentTherapyPlanById(formId);

        if (!currentTherapyPlan) {
          return res.sendError("Something went wrong! Could not load therapy plan.");
        }

        return res.sendSuccess(currentTherapyPlan, "Current therapy plan data.");
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function createTherapyPlanVersion(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You dont't have permission for insurance documents approval section!"
            );
          }
        }

        const { user, _id, ...restOfBody } = req.body;
        const therapyPlanVersionData: DTherapyPlanVersion = {
          ...restOfBody,
          versionCreatedBy: req.user._id,
          versionCreatedAt: new Date(),
        };

        const newVersion = await FormVersionDao.createTherapyPlanVersion(therapyPlanVersionData);

        if (!newVersion) {
          return res.sendError("Error while creating version");
        }

        AppLogger.info(`Created version for original therapy plan Id: ${newVersion.therapyPlanId} by user Id ${newVersion.versionCreatedBy}`);
        return res.sendSuccess(newVersion, "A new version is successfully created.");
    } else {
      return res.sendError("Invalid user role.");
    }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getTherapyPlansWithAllVersions(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = Types.ObjectId(req.params.clientId);
    const therapistId = req.user._id;

    if (req.user.role == UserRole.THERAPIST) {
      try {
        let tpWithVersion = await FormVersionDao.getTherapyPlansWithAllVersions(clientId, therapistId);

        if (!tpWithVersion) {
          return res.sendError("Encountered an error while retrieving the therapy plan data with versions!");
        }

        return res.sendSuccess(tpWithVersion, "Therapy plan data with versions for client.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getTherapyPlanVersion(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        const tpVersionId = Types.ObjectId(req.params.id);
        let therapyPlanVersion = await FormVersionDao.getTherapyPlanVersion(tpVersionId);

        if (!therapyPlanVersion) {
          return res.sendError("Encountered an error while retrieving the therapy plan version!");
        }

        return res.sendSuccess(therapyPlanVersion, "Therapy plan version details.");
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getDiagnosisNoteVersionById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const noteId = req.params.noteId;
      const previousNote = await FormVersionDao.getDiagnosisNoteVersionByIdWithOutPopulateFunction(noteId);

      if (!previousNote) {
        return res.sendError("Invalid note id.");
      }

      if (previousNote.updated) {
        const populated = await FormVersionDao.getDiagnosisNoteVersionByIdFunction(noteId);

        const dataForSend = {
          clientId: populated.clientId,
          therapistId: populated.therapistId,
          meetingId: populated.meetingId,

          updated: populated.updated,
          updatedByTherapist: populated.updatedByTherapist,
          patientID: populated.patientID,
          patientAcountNo: populated.patientAcountNo,
          encounterID: populated.encounterID,
          encounterDate: populated.encounterDate,
          encounterType: populated.encounterType,
          chiefComplaint: populated.chiefComplaint,
          historyOfPresentIllness: populated.historyOfPresentIllness,
          historyOfPresentIllnessAttachments:
            populated.historyOfPresentIllnessAttachments,
          diagnosisICDcodes: populated.diagnosisICDcodes,
          mentalBehavioralStatus: populated.mentalBehavioralStatus,
          mentalBehavioralStatusAttachments:
            populated.mentalBehavioralStatusAttachments,
          asssessments: populated.asssessments,
          cptCode: populated.cptCode,
          assessmentAttachments: populated.assessmentAttachments,
          procedureNotes: populated.procedureNotes,
          signature: populated.signature,
          carePlan: populated.carePlan,
          carePlanAttachments: populated.carePlanAttachments,
          selectedGoals: populated.selectedGoals,
          intervention: populated.intervention,
          secondaryDiagnosisICDcodes: populated.secondaryDiagnosisICDcodes,
        };
        return res.sendSuccess(dataForSend, "Diagnosis note version details.");
      } else {
        return res.sendError("Diagnosis note is not yet updated by therapist!");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getCurrentClinicalAssessmentById(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You dont't have permission for insurance documents approval section!"
            );
          }
        }

        const formId = Types.ObjectId(req.params.id);
        let currentClinicalAssessment = await FormVersionDao.getCurrentClinicalAssessmentDataById(formId);

        if (!currentClinicalAssessment) {
          return res.sendError("Something went wrong! Could not load clinical assessment.");
        }

        return res.sendSuccess(currentClinicalAssessment, "Current clinical assessment data.");
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function createClinicalAssessmentVersion(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You dont't have permission for insurance documents approval section!"
            );
          }
        }

        const { user, _id, ...restOfBody } = req.body;
        const clinicalAssesmentVersionData: DClinicalAssesmentVersion = {
          ...restOfBody,
          versionCreatedBy: req.user._id,
          versionCreatedAt: new Date(),
        };

        const newVersion = await FormVersionDao.createNewClinicalAssessmentVersion(clinicalAssesmentVersionData);

        if (!newVersion) {
          return res.sendError("Error while creating version");
        }

        AppLogger.info(`Created version for original clinical assessment Id: ${newVersion.clinicalAssesmentId} by user Id ${newVersion.versionCreatedBy}`);
        return res.sendSuccess(newVersion, "A new version is successfully created.");
    } else {
      return res.sendError("Invalid user role.");
    }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getClinicalAssessmentsWithAllVersionsOld(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = Types.ObjectId(req.params.clientId);
    const therapistId = req.user._id;

    if (req.user.role == UserRole.THERAPIST) {
      try {
        let caWithVersion = await FormVersionDao.getTheClinicalAssessmentsWithAllVersions(clientId, therapistId);

        if (!caWithVersion) {
          return res.sendError("Encountered an error while retrieving the clinical assessment data with versions!");
        }

        return res.sendSuccess(caWithVersion, "Clinical assessment data with versions for client.");
      } catch (error) {
        return res.sendError(error);
      }
    } else {
      return res.sendError("Invalid user role.");
    }
  }

  export async function getClinicalAssessmentsWithAllVersions(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const clientId = Types.ObjectId(req.params.clientId);
    const therapistId = req.user._id;

    try {
      const caWithVersion = await FormVersionDao.getTheClinicalAssessmentsWithAllVersions(clientId, therapistId);

      if (caWithVersion && caWithVersion.length > 0) {
        const caData = {
          relatedCAFormType: "OLD_CA",
          caWithVersion,
        };
        return res.sendSuccess(caData, "Clinical Assessment data with versions.");
      }

      const daWithVersion = await FormVersionDao.getTheNewTypeOfClinicalAssessmentWithAllVersions(clientId, therapistId);

      if (daWithVersion) {
        const daData: {
          relatedCAFormType: string;
          daWithVersion: any;
          meetingData?: any;
        } = {
          relatedCAFormType: "NEW_CA",
          daWithVersion,
        };

        // if (daWithVersion.length === 0) {
        //   const meetingData = await TherapistDao.getFirstMeetingDataByClientIdAndTherapistId(clientId, therapistId);
        //   daData.meetingData = meetingData;
        // }

        const meetingData =
          await TherapistDao.getFirstMeetingDataByClientIdAndTherapistId(
            clientId,
            therapistId
          );
        daData.meetingData = meetingData;

        return res.sendSuccess(
          daData,
          "Digital Assessment data with versions."
        );
      }

      return res.sendError("Encountered an error while retrieving the assessment data with versions!");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getClinicalAssessmentVersion(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        const caVersionId = Types.ObjectId(req.params.id);
        let clinicalAssesmentVersion = await FormVersionDao.getTheClinicalAssessmentVersion(caVersionId);

        if (!clinicalAssesmentVersion) {
          return res.sendError("Encountered an error while retrieving the clinical assessment version!");
        }

        return res.sendSuccess(clinicalAssesmentVersion, "Clinical assessment version details.");
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getCurrentNewTypeOfClinicalAssessment(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You dont't have permission for insurance documents approval section!"
            );
          }
        }

        const formId = Types.ObjectId(req.params.id);
        let currentDigitalAssessment = await FormVersionDao.getTheCurrentNewTypeOfClinicalAssessment(formId);

        if (!currentDigitalAssessment) {
          return res.sendError("Something went wrong! Could not load clinical assessment.");
        }

        return res.sendSuccess(currentDigitalAssessment, "Current digital assessment data.");
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function createNewTypeOfClinicalAssessmentVersion(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        if (req.user.role == UserRole.SUB_ADMIN) {
          const ownUser = await UserDao.getUserByUserId(req.user._id);
          if (ownUser.adminPermission.approvalQueue != true) {
            return res.sendError(
              "You dont't have permission for insurance documents approval section!"
            );
          }
        }

        const { user, _id, ...restOfBody } = req.body;
        const digitalAssesmentVersionData: DDigitalAssessmentVersion = {
          ...restOfBody,
          versionCreatedBy: req.user._id,
          versionCreatedAt: new Date(),
        };

        const newVersion = await FormVersionDao.createTheNewTypeOfClinicalAssessmentVersion(digitalAssesmentVersionData);

        if (!newVersion) {
          return res.sendError("Error while creating version");
        }

        AppLogger.info(`Created version for original digital assessment Id: ${newVersion.digitalAssessmentId} by user Id ${newVersion.versionCreatedBy}`);
        return res.sendSuccess(newVersion, "A new version is successfully created.");
    } else {
      return res.sendError("Invalid user role.");
    }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getNewTypeOfClinicalAssessmentDetailsForDownload(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        const assesmentId = Types.ObjectId(req.body.assesmentId);
        let digitalAssesment = await FormVersionDao.getTheNewTypeOfClinicalAssessmentDetailsForDownload(assesmentId);

        if (!digitalAssesment) {
          return res.sendError("Encountered an error while retrieving the clinical assessment!");
        }

        return res.sendSuccess(digitalAssesment, "Digital assessment version details for download.");
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getNewTypeOfClinicalAssessmentVersionForDownload(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {

        const caVersionId = Types.ObjectId(req.params.id);
        let digitalAssesmentVersion = await FormVersionDao.getTheNewTypeOfClinicalAssessmentVersionForDownload(caVersionId);

        if (!digitalAssesmentVersion) {
          return res.sendError("Encountered an error while retrieving the clinical assessment version!");
        }

        return res.sendSuccess(digitalAssesmentVersion, "Digital assessment version details.");
      } else {
        return res.sendError("Invalid user role.");
      }
    } catch (error) {
      return res.sendError(error);
    }
  }
}
