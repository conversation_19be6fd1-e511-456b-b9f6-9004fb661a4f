import { NextFunction, Request, Response } from "express";
let mongoose = require("mongoose");
import { validationResult } from "express-validator";
import { DTherapyPlan } from "../models/therapy-plan-model";
import { TherapyPlanDao } from "../dao/therapy-plan-dao";
import { ClientDao } from "../dao/client-dao";
import { TherapistDao } from "../dao/therapist-dao";
import { EmailService } from "../mail/config";
import { StringOrObjectId } from "../common/util";
import TherapyPlan from "../schemas/therapy-plan-schema";
import Meeting from "../schemas/meeting-schema";
import User from "../schemas/user-schema";
import Client from "../schemas/client-schema";
import { SMSService } from "../sms/config";
import Therapist from "../schemas/therapist-schema";
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const moment = require("moment-timezone");
import { AdminDao } from "../dao/admin-dao";
import FriendRequest from "../schemas/friend-request-schema";
import { UserRole } from "../models/user-model";

export namespace TherapyPlanEp {
  export async function addTherapyPlanDetails(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }

      const therapistId = req.user._id;
      const clientId = req.body.clientId;
      const planCreationDate = req.body.planCreationDate;
      const creationDate = req.body.creationDate;
      const diagnosis = req.body.diagnosis;
      const goalDate = req.body.goalDate;
      const goalInformation = req.body.goalInformation;
      const treatmentSession = req.body.treatmentSession;
      const clientSignatureDetails = req.body.clientSignatureDetails;
      const clientSignature = req.body.clientSignature;
      const lrpSignatureDetails = req.body.lrpSignatureDetails;
      const lrpSignature = req.body.lrpSignature;
      const clinicianSignatureDetails = req.body.clinicianSignatureDetails;
      const clinicianTwoSignatureDetails = req.body.clinicianTwoSignatureDetails;
      const clinicianSignature = req.body.clinicianSignature;
      const clinicianTwoSignature = req.body.clinicianTwoSignature;
      const isSignature = req.body.isSignature;
      const isSendSignatureEmail = req.body.isSendSignatureEmail;
      const goalDate2 = req.body.goalDate2;
      const goalInformation2 = req.body.goalInformation2;
      const treatmentSession2 = req.body.treatmentSession2;
      const goalDate3 = req.body.goalDate3;
      const goalInformation3 = req.body.goalInformation3;
      const treatmentSession3 = req.body.treatmentSession3;

      const TherapyPlanDetails: DTherapyPlan = {
        therapistId: therapistId,
        clientId: clientId,
        planCreationDate: planCreationDate,
        creationDate: creationDate,
        diagnosis: diagnosis,
        goalDate: goalDate,
        goalInformation: goalInformation,
        treatmentSession: treatmentSession,
        clientSignatureDetails: clientSignatureDetails,
        clientSignature: clientSignature,
        lrpSignatureDetails: lrpSignatureDetails,
        lrpSignature: lrpSignature,
        clinicianSignatureDetails: clinicianSignatureDetails,
        clinicianTwoSignatureDetails: clinicianTwoSignatureDetails,
        clinicianSignature: clinicianSignature,
        clinicianTwoSignature: clinicianTwoSignature,
        isSignature: isSignature,
        goalDate2: goalDate2,
        goalInformation2: goalInformation2,
        treatmentSession2: treatmentSession2,
        goalDate3: goalDate3,
        goalInformation3: goalInformation3,
        treatmentSession3: treatmentSession3,
      };


      let TherapyPlanData = await TherapyPlanDao.addDetails(TherapyPlanDetails);

      if (!TherapyPlanData) {
        return res.sendError(
          "Details could not be submitted. Please try again later."
        );
      }

      const therapyPlanId: StringOrObjectId = TherapyPlanData._id.toString();

      if (isSignature == false && isSendSignatureEmail == true) {
        let client = await ClientDao.getUserById(clientId);
        let therapist = await TherapistDao.getUserById(therapistId);

        if (client) {
          await EmailService.sendTherapyPlanSignatureEmail(
            client,
            therapist,
            "Therapy Plan Client Signature Request",
            therapyPlanId
          );
        } else {
          return res.sendError("client cannot find");
        }
      }

      return res.sendSuccess(TherapyPlanData, "Details submitted.");

    } catch (error) {
      if (error instanceof mongoose.Error.ValidationError) {
        const errorMessages = Object.values(error.errors).map(
          (val: any) => val.message
        );
        return res.sendError(errorMessages.join(", "));
      } else {
        return res.sendError(error);
      }
    }

  }

  export async function updateTherapyPlanDetails(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.sendError(errors.array()[0]["msg"]);
      }
      const stepCount = req.body.stepCount;
      const _id = req.body._id;
      const therapistId = req.user._id;
      const clientId = req.body.clientId;
      const planCreationDate = req.body.planCreationDate;
      const creationDate = req.body.creationDate;
      const diagnosis = req.body.diagnosis;
      const goalDate = req.body.goalDate;
      const goalInformation = req.body.goalInformation;
      const treatmentSession = req.body.treatmentSession;
      const clientSignatureDetails = req.body.clientSignatureDetails;
      const clientSignature = req.body.clientSignature;
      const lrpSignatureDetails = req.body.lrpSignatureDetails;
      const lrpSignature = req.body.lrpSignature;
      const clinicianTwoSignatureDetails = req.body.clinicianTwoSignatureDetails;
      const clinicianSignatureDetails = req.body.clinicianSignatureDetails;
      const clinicianTwoSignature = req.body.clinicianTwoSignature;
      const clinicianSignature = req.body.clinicianSignature;
      const isSignature = req.body.isSignature;
      const isSendSignatureEmail = req.body.isSendSignatureEmail;
      const goalDate2 = req.body.goalDate2;
      const goalInformation2 = req.body.goalInformation2;
      const treatmentSession2 = req.body.treatmentSession2;
      const goalDate3 = req.body.goalDate3;
      const goalInformation3 = req.body.goalInformation3;
      const treatmentSession3 = req.body.treatmentSession3;

      const step1Update: DTherapyPlan = {
        planCreationDate: planCreationDate,
        creationDate: creationDate,
        diagnosis: diagnosis,
        goalDate: goalDate,
        goalInformation: goalInformation,
        treatmentSession: treatmentSession,
        clientSignatureDetails: clientSignatureDetails,
        clientSignature: clientSignature,
        lrpSignatureDetails: lrpSignatureDetails,
        lrpSignature: lrpSignature,
        clinicianTwoSignatureDetails: clinicianTwoSignatureDetails,
        clinicianSignatureDetails: clinicianSignatureDetails,
        clinicianTwoSignature: clinicianTwoSignature,
        clinicianSignature: clinicianSignature,
        isSignature: isSignature,
        goalDate2: goalDate2,
        goalInformation2: goalInformation2,
        treatmentSession2: treatmentSession2,
        goalDate3: goalDate3,
        goalInformation3: goalInformation3,
        treatmentSession3: treatmentSession3,
      };

      if (stepCount === 1) {

        if (isSignature == false && isSendSignatureEmail == true) {
          let client = await ClientDao.getUserById(clientId);
          let therapist = await TherapistDao.getUserById(therapistId);

          if (client) {
            await EmailService.sendTherapyPlanSignatureEmail(
              client,
              therapist,
              "Therapy Plan Client Signature Request",
              _id
            );
          } else {
            return res.sendError("client cannot find");
          }
        }

        let TherapyPlanData = await TherapyPlanDao.updateDetails(
          _id,
          step1Update
        );

        if (!TherapyPlanData) {
          return res.sendError(
            "Details could not be updated. Please try again later."
          );
        }

        return res.sendSuccess(TherapyPlanData, "Details Updated.");
      } else if (stepCount === 2) {
        /*let TherapyPlanData = await TherapyPlanDao.updateDetails(
          _id,
          step2Update
        );
        if (!TherapyPlanData) {
          return res.sendError(
            "Details could not be updated. Please try again later."
          );
        }
        return res.sendSuccess(TherapyPlanData, "Details Updated.");*/
      }

    } catch (error) {
      return res.sendError(error);
    }

  }

  export async function getTherapyPlanDetails(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapyPlanId = req.body.therapyPlanId;

    try {
      let TherapyPlanData = await TherapyPlanDao.getTherapyPlanByClientId(
        therapyPlanId
      );

      if (!TherapyPlanData) {
        return res.sendError(
          "There is no record matching the specified therapist and client!"
        );
      }

      // Create a response object from the mongoose document
      let responseData = TherapyPlanData.toObject ? TherapyPlanData.toObject() : {...TherapyPlanData};

      // Check if both dates exist
      if (responseData.planCreationDate && responseData.creationDate) {
        // Create Date objects to compare only the date part (not time)
        const planDate = new Date(responseData.planCreationDate);
        const creationDate = new Date(responseData.creationDate);

        // Set both times to midnight to compare only the date parts
        const planDateString = planDate.toISOString().split('T')[0];
        const creationDateString = creationDate.toISOString().split('T')[0];

        // If the dates match (comparing only year-month-day), add 1 year to creationDate
        if (planDateString === creationDateString) {
          const newCreationDate = new Date(creationDate);
          newCreationDate.setFullYear(newCreationDate.getFullYear() + 1);
          responseData.creationDate = newCreationDate;
        }
      }

      return res.sendSuccess(responseData, "Therapy plan data.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function updateClientTherapyPlanSignature(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const signature = req.body.signature;
      const isLRP = req.body.isLRP;
      const _id = req.body._id;
      const isSignature = true;

      // Get current date as string in format YYYY-MM-DD
      const currentDate = new Date().toISOString().split('T')[0];

      // Get name from request body or try to find client/therapist name
      let name = req.body.name;

      // Get relationship from request body
      const relationship = req.body.relationship || "";

      // If name is not provided in the request, try to get it from the database
      if (!name || name === "") {
        try {
          const therapyPlan = await TherapyPlan.findById(_id);
          if (therapyPlan) {
            if (!isLRP) {
              // Get client name for client signature
              const client = await Client.findById(therapyPlan.clientId);
              if (client) {
                name = `${client.firstname} ${client.lastname}`;
              }
            } else {
              // Get therapist name for LRP signature
              const therapist = await Therapist.findById(therapyPlan.therapistId);
              if (therapist) {
                name = `${therapist.firstname} ${therapist.lastname}`;
              }
            }
          }
        } catch (error) {
          console.error("Error fetching name:", error);
        }
      }

      // Default to empty string if still no name
      name = name || "";

      const data = {
        _id: _id,
        isSignature: isSignature,
        ...(isLRP
          ? {
              lrpSignature: signature,
              lrpSignatureDetails: {
                name: name,
                date: currentDate,
                relationship: relationship
              }
            }
          : {
              clientSignature: signature,
              clientSignatureDetails: {
                name: name,
                date: currentDate,
                relationship: relationship
              }
            }),
      };

      const removeData: any = {
        ...(isLRP
          ? { removeClientSignature: true }
          : { removeLrpSignature: true }),
      };

      let updatedSignature =
        await TherapyPlanDao.updateClientTherapyPlanSignature(
          _id,
          data,
          removeData
        );

      if (!updatedSignature) {
        return res.sendError("Cannot update signature!");
      }
      return res.sendSuccess(
        updatedSignature,
        "Signature successfuly updated!"
      );
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getTherapyPlanDetailsByAdmin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const therapyPlanId = req.body.therapyPlanId;

    try {
      let TherapyPlanData = await TherapyPlanDao.getTherapyPlanByClientId(
        therapyPlanId
      );

      if (!TherapyPlanData) {
        return res.sendError(
          "There is no record matching the specified therapy plan id!"
        );
      }

      // Create a response object from the mongoose document
      let responseData = TherapyPlanData.toObject ? TherapyPlanData.toObject() : {...TherapyPlanData};

      // Check if both dates exist
      if (responseData.planCreationDate && responseData.creationDate) {
        // Create Date objects to compare only the date part (not time)
        const planDate = new Date(responseData.planCreationDate);
        const creationDate = new Date(responseData.creationDate);

        // Set both times to midnight to compare only the date parts
        const planDateString = planDate.toISOString().split('T')[0];
        const creationDateString = creationDate.toISOString().split('T')[0];

        // If the dates match (comparing only year-month-day), add 1 year to creationDate
        if (planDateString === creationDateString) {
          const newCreationDate = new Date(creationDate);
          newCreationDate.setFullYear(newCreationDate.getFullYear() + 1);
          responseData.creationDate = newCreationDate;
        }
      }

      return res.sendSuccess(responseData, "Therapy plan data.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getClientUsingTherapyPlanId(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const therapyPlanId = req.body.therapyPlanId;

      if (!therapyPlanId || !mongoose.Types.ObjectId.isValid(therapyPlanId)) {
        return res.sendError("Invalid assesment Id");
      }

      let therapyPlan = await TherapyPlan.findById(therapyPlanId).select(
        "clientId"
      );

      if (!therapyPlan) {
        return res.sendError(
          "There is no record matching the specified therapy plan id!"
        );
      }
      const client = await AdminDao.getClientById(therapyPlan?.clientId);
      if (!client) {
        return res.sendError(
          "There is no record matching the specified client id!"
        );
      }
      return res.sendSuccess(client, "Client data.");
    } catch (error) {
      return res.sendError(error);
    }
  }

  export async function getClientNameAndTherapistName(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { clientId, therapistId } = req.body;

      if (!clientId || !mongoose.Types.ObjectId.isValid(clientId)) {
        return res.sendError("Invalid client ID.");
      }

      // Validate therapistId if provided
      if (therapistId && !mongoose.Types.ObjectId.isValid(therapistId)) {
        return res.sendError("Invalid therapist ID.");
      }

      // If therapistId is provided, use direct lookup
      if (therapistId) {
        // Find client by ID
        const client = await User.findById(clientId).select("firstname lastname").exec();
        if (!client) {
          return res.sendError("Client not found.");
        }

        // Find therapist by ID and verify role
        const therapist = await Therapist.findOne({
          _id: therapistId
        }).select("firstname lastname therapistCategorizationByType").exec();

        if (!therapist) {
          return res.sendError("Therapist not found or invalid role.");
        }

        return res.sendSuccess(
          {
            clientName: `${client.firstname} ${client.lastname}`,
            therapistName: `${therapist.firstname} ${therapist.lastname}`,
            therapistCategorizationByType: therapist.therapistCategorizationByType || "",
          },
          "Client and therapist names retrieved successfully."
        );
      }

      // Original logic when therapistId is not provided
      const friendRequest = await FriendRequest.findOne({ clientId })
        .populate("clientId", "firstname lastname")
        .populate(
          "therapistId",
          "firstname lastname therapistCategorizationByType"
        )
        .exec();

      if (!friendRequest) {
        const therapyPlan = await TherapyPlan.findOne({ _id: clientId })
          .populate("clientId", "firstname lastname")
          .populate(
            "therapistId",
            "firstname lastname therapistCategorizationByType"
          )
          .exec();

        if (!therapyPlan) {
          return res.sendError("Identifier Id not found.");
        }

        if (!therapyPlan.clientId || !therapyPlan.therapistId) {
          return res.sendError("Client or therapist information is missing.");
        }
        const client = therapyPlan.clientId as unknown as {
          firstname: string;
          lastname: string;
        };

        const therapist = therapyPlan.therapistId as unknown as {
          firstname: string;
          lastname: string;
          therapistCategorizationByType: string;
        };

        return res.sendSuccess(
          {
            clientName: `${client.firstname} ${client.lastname}`,
            therapistName: `${therapist.firstname} ${therapist.lastname}`,
            therapistCategorizationByType: `${therapist.therapistCategorizationByType}`||"",
          },
          "Client and therapist names retrieved successfully."
        );
      }

      const client = friendRequest.clientId as unknown as {
        firstname: string;
        lastname: string;
      };
      const therapist = friendRequest.therapistId as unknown as {
        firstname: string;
        lastname: string;
        therapistCategorizationByType: string;
      };

      return res.sendSuccess(
        {
          clientName: `${client.firstname} ${client.lastname}`,
          therapistName: `${therapist.firstname} ${therapist.lastname}`,
          therapistCategorizationByType:
            `${therapist.therapistCategorizationByType}` || "",
        },
        "Client and therapist names retrieved successfully."
      );
    } catch (error) {
      console.error("Error fetching client and therapist names:", error);
      return res.sendError("An error occurred while fetching names.");
    }
  }
  export async function getTherapistCategorizationByType(
      req: Request,
      res: Response,
      next: NextFunction
  ) {
    try {
      const therapistId = req.body.user;


      if (!therapistId || !mongoose.Types.ObjectId.isValid(therapistId)) {
        return res.sendError("Invalid user ID.");
      }

        const therapist = await Therapist.findOne({ _id: therapistId });

        if (!therapist) {
          return res.sendError("Therapist not found.");
        }

        return res.sendSuccess(therapist, "Therapist categorization retrieved successfully.");

    } catch (e) {
      console.error(e);
      return res.sendError("An error occurred while finding therapist categorization.");
    }
  }
   export async function updateTherapistCategorizationByType(
      req: Request,
      res: Response,
      next: NextFunction
  ) {
    try {
      const therapistId = req.body.user;
      const therapistCategorizationByType = req.body.therapistCategorizationByType;

      if (!therapistId || !mongoose.Types.ObjectId.isValid(therapistId)) {
        return res.sendError("Invalid user ID.");
      }

      if (therapistCategorizationByType) {
        const therapist = await Therapist.findOneAndUpdate(
            { _id: therapistId },
            { therapistCategorizationByType: therapistCategorizationByType },
            { new: true, upsert: true }
        );

        if (!therapist) {
          return res.sendError("Therapist not found.");
        }

        return res.sendSuccess(therapist, "Therapist categorization updated successfully.");
      } else {
        return res.sendError("Therapist categorization type is required.");
      }
    } catch (e) {
      console.error(e);
      return res.sendError("An error occurred while updating therapist categorization.");
    }
  }
}
