import openai
import json
import re
import spacy
from presidio_analyzer import AnalyzerEngine
from presidio_anonymizer import AnonymizerEngine
from collections import Counter
from Get_transcript import get_conversation_from_transcript
import tiktoken
from Get_transcript import *
# from get_diagnosis import *
from summary import *
from assessments import *
from sentize_anonymise import *
import os
import sys

openAIAPIKey = sys.argv[1]
openai.api_key = openAIAPIKey

# Load spaCy model and initialize Presidio outside the function for performance
nlp = spacy.load("en_core_web_lg")
analyzer = AnalyzerEngine()
anonymizer = AnonymizerEngine()

# Dictionary containing field keys mapped to prompts
prompts_dict = {
    # Symptom Checklists (Various Symptoms)
    ".symptomChecklists.depressiveSymptoms.comment": "Acting as an expert licensed therapist, summarize the patient's description of any depressive symptoms such as sadness, fatigue, or loss of interest from the transcript {convo}.",
    # "symptom_checklists.depressive_symptoms.recurrent_thoughts_of_death_suicidal_ideation": "Based on the session transcript {convo}, extract any mentions of recurrent thoughts of death or suicidal ideation expressed by the patient.",
    ".symptomChicklist.manicSymptoms.symtoms.comment": "From the transcript {convo}, summarize any manic symptoms such as elevated mood, irritability, or increased energy that the patient described.",
    ".symptomChicklist.conductLegalProblem.comment": "Acting as an expert therapist, identify and summarize any problematic behaviors (e.g., lying, stealing, or aggressiveness) the patient mentioned in the session transcript {convo}.",
    "symptomChecklists.psychosis.comment": "From the transcript {convo}, summarize any psychotic symptoms described by the patient, including hallucinations, delusions, or paranoia.",
    "symptomChecklists.anxietySymptoms.comment": "Using your DSM-5 knowledge, summarize the patient's description of anxiety symptoms from the transcript {convo}, including worry, avoidance, or panic attacks.",
    "symptomChecklists.attentionSymptoms.comment": "Based on the transcript {convo}, identify and summarize any attention-related symptoms such as difficulty focusing, distractibility, or impulsivity.",

    # Symptom Checklists - Other fields
    # ".symptomChicklist.depressiveSymptoms.comment": "From the transcript {convo}, describe any other depressive symptoms the patient mentioned that don't fit into predefined categories.",
    # ".symptomChicklist.manicSymptoms.none": "Identify and describe any other manic symptoms not already covered, based on the session transcript {convo}.",
    # "symptom_checklists.conduct_problems.other_description": "Based on the transcript {convo}, describe any other conduct problems not mentioned in the predefined categories.",
    # ".symptomChicklist.depressiveSymptoms.symtoms.none": "From the transcript {convo}, describe any other psychotic symptoms not fitting the predefined categories.",
    # ".symptomChicklist.anxietySymptoms.symtoms.none": "Using the session transcript {convo}, describe any other anxiety-related symptoms that don’t fit the usual categories.",
    # "symptom_checklists.attention_symptoms.other_description": "Summarize any attention-related symptoms mentioned in {convo} that don't fit predefined categories.",

    # Biological Functions
    ".biologicalFunction.sleep.comment": "From the transcript {convo}, summarize any issues with sleep the client reported, such as insomnia, excessive sleep, or early waking.",
    "biologicalFunction.nutritionalStatus.comment": "Based on {convo}, summarize any changes in the client's appetite, eating habits, or mention of food allergies.",
    ".biologicalFunction.otherBiologicalFunction.other": "Using the transcript {convo}, describe any other biological functions that may affect the client, such as significant weight changes or libido issues.",
    ".biologicalFunction.sexualActivity.atRiskBehavior.describe": "From {convo}, summarize any concerns the client has regarding sexual activity risks, including STI risks or protective measures.",

    # Substance Use History
    # "substance_use_history.substances[].problems_with_use.other_description": "From the session transcript {convo}, summarize any substance use problems that don’t fit into the predefined categories.",

    # Trauma History
    "traumaHistory.badAccidentDescribe": "Based on {convo}, describe any bad accidents the client mentioned, including the circumstances and emotional impact.",
    "traumaHistory.seriousAttackDescribe": "From {convo}, summarize any physical abuse the client discussed, including the nature and frequency of the abuse.",
    "traumaHistory.describeSexualCoercion": "Using the transcript {convo}, summarize any sexual abuse the client disclosed, including details and emotional impact.",
    "traumaHistory.describeTraumaticExperience": "From the transcript {convo}, describe any other traumatic experiences the client mentioned that don't fit the predefined categories.",
    # "traumaHistory.intrusiveThoughts": "From {convo}, determine whether the client has experienced unwanted thoughts about traumatic events over the last month.",
    # "traumaHistory.avoidanceBehavior": "From the transcript {convo}, identify whether the client has been avoiding thoughts or reminders of traumatic events in the last month.",
    # "traumaHistory.hypervigilance": "Using {convo}, determine if the client described being constantly on guard or easily startled over the past month.",
    # "traumaHistory.emotionalDetachment": "Based on {convo}, summarize if the client expressed feeling numb or detached from people or surroundings in the last month.",
    # "traumaHistory.selfBlame": "Summarize whether the client expressed feelings of guilt or inability to stop blaming themselves or others for traumatic events, based on {convo}.",

    # Risk Assessment
    # "risk_assessment.current_risk_to_self.self_injured_past_month_description": "From {convo}, summarize if the patient mentioned self-injury in the past month, including methods and frequency.",
    # "risk_assessment.current_risk_to_self.suicidal_ideation_or_threats_description": "From the session transcript {convo}, summarize any suicidal ideation or threats made by the patient, including plans or intent.",
    # "risk_assessment.current_risk_to_self.ideation_involves_command_hallucinations_description": "Using the transcript {convo}, summarize any command hallucinations the patient described, including what they were instructed to do.",

    # Presenting Problem
    "presentingProblem.description": "Summarize the client's presenting problem based on the session transcript {convo}, including any primary issues they are seeking help for.",
    "historyOfProblem": "Based on {convo}, summarize the history of the presenting problem, including when it started, any triggers, and how it has evolved.",

    # Medical and Legal History
    "releventMedicalInformation.comment": "From the transcript {convo}, summarize any medical conditions or physical health issues the client discussed.",
    # "medical_history.medications[].medication_name": "Extract the names of any medications the patient mentioned taking, based on the session transcript {convo}.",
    # ".symptomChicklist.conductLegalProblem.comment": "Summarize any legal involvement the client disclosed, including current or past legal issues, based on the session transcript {convo}.",

    # Diagnosis and Recommendations
    # "diagnoses.codes[].code": "Based on the session transcript {convo} and the DSM-5 criteria, suggest a diagnosis code that aligns with the patient's symptoms.",
    # "diagnoses.codes[].diagnosis": "Using your DSM-5 knowledge, provide a diagnosis description for the patient based on their symptoms in the transcript {convo}.",
    "recommendations.level_of_care": "From {convo}, recommend the appropriate level of care based on the client's symptoms and risk factors, such as outpatient, inpatient, or intensive therapy.",

    # Mental Status
    # "mental_status.physical_stature": "Summarize the therapist's observations of the patient's physical appearance, including build and grooming, based on the session transcript {convo}.",
    "mentalStatus.speech": "From {convo}, summarize observations of the client's speech, including tone, speed, and coherence.",
    "mentalStatus.mood": "Based on {convo}, summarize the client's mood as described during the session.",
    "mentalStatus.affect": "Summarize the therapist's observations of the client's emotional expression (affect) during the session, based on {convo}.",
    "mentalStatus.thoughtForm": "Summarize the therapist's observations of the client's thought process (e.g., logical, tangential) based on the transcript {convo}.",
    "mentalStatus.thoughtContent": "Using {convo}, summarize the content of the client's thoughts, including any delusions or paranoia that were discussed."
}

# Load spaCy model and initialize Presidio outside the function for performance

# Function to summarize text with token limit
def chunk_text_by_turns(text, max_chunk_size=3000):
    """
    Splits the text into chunks based on dialogue turns. Ensures that each chunk contains full conversational turns
    between therapist and client.
    """
    turns = text.split('\n')

    chunks = []
    current_chunk = ""

    for turn in turns:
        if len(current_chunk) + len(turn) <= max_chunk_size:
            current_chunk += turn + '\n'
        else:
            chunks.append(current_chunk.strip())
            current_chunk = turn + '\n'

    if current_chunk:
        chunks.append(current_chunk.strip())
    # print(f"chunch  -> {chunks}" )
    return chunks

def summarize_text_with_limit(text, max_output_length=5000, max_chunk_size=3000, max_summary_tokens=500):
    """
    Summarizes large conversational text while ensuring the final output is not more than the specified length.
    """
    # Step 1: Split the text into conversational chunks
    chunks = chunk_text_by_turns(text, max_chunk_size=max_chunk_size)

    # Step 2: Summarize each chunk
    summaries = []
    for i, chunk in enumerate(chunks):
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4-mini",  # or "gpt-3.5-turbo"
                messages=[
                    {"role": "system", "content": "You are an assistant who helps summarize conversations."},
                    {"role": "user",
                     "content": f"Summarize the following conversation while preserving context and the flow of the dialogue:\n\n{chunk}"}
                ],
                max_tokens=4000,
                temperature=0.5
            )
            summary = response['choices'][0]['message']['content']
            summaries.append(summary)
        except Exception as e:
            print(f"Error summarizing chunk {i}: {e}")
            summaries.append(chunk)  # If summarization fails, retain the original chunk

    # Step 3: Combine all summaries
    combined_summary = "\n".join(summaries)

    # Step 4: Ensure the combined summary is not more than max_output_length
    encoding = tiktoken.encoding_for_model("gpt-4o-mini")
    combined_summary_tokens = len(encoding.encode(combined_summary))

    if combined_summary_tokens > max_output_length:
        # If the combined summary exceeds the limit, summarize it further
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4o-mini",  # or "gpt-3.5-turbo"
                messages=[
                    {"role": "system", "content": "You are an assistant who helps condense text."},
                    {"role": "user",
                     "content": f"Please condense the following text while retaining the most important details:\n\n{combined_summary}"}
                ],
                max_tokens=max_summary_tokens,
                temperature=0.5
            )
            final_summary = response['choices'][0]['message']['content']
            return final_summary
        except Exception as e:
            print(f"Error condensing the summary: {e}")
            return combined_summary  # As a fallback, return the combined summary
    else:

        return combined_summary

# Rest of your code remains the same...

def get_sanitized_conversation(meeting_id, max_tokens_limit):
    # Retrieve the transcript data using the provided meeting ID
    transcript = get_conversation_from_transcript(meeting_id)['convo']

    # Parse the transcript data
    dialogues = []
    pattern = re.compile(r'^(THERAPIST|CLIENT)\s*:\s*(.*)$')

    for line in transcript:
        match = pattern.match(line.strip())
        if match:
            speaker_label = match.group(1)
            text = match.group(2)
            dialogues.append({'speaker': speaker_label, 'text': text})
        else:
            print(f"Line skipped (no match): {line}")

    # Function to anonymize text
    def anonymize_text(text):
        analyzer_results = analyzer.analyze(text=text, language='en')
        anonymized_result = anonymizer.anonymize(text=text, analyzer_results=analyzer_results)
        return anonymized_result.text

    # Anonymize each dialogue and reconstruct the sanitized transcript
    sanitized_transcript = []
    for dialogue in dialogues:
        speaker = dialogue['speaker']
        original_text = dialogue['text']
        sanitized_text = anonymize_text(original_text)
        sanitized_line = f"{speaker}: {sanitized_text}"
        sanitized_transcript.append(sanitized_line)

    # Join the sanitized transcript into a single string
    sanitized_convo_text = '\n'.join(sanitized_transcript)

    # Check the token size of the sanitized conversation
    encoding = tiktoken.encoding_for_model("gpt-4o-mini")
    convo_tokens = len(encoding.encode(sanitized_convo_text))

    # If the conversation exceeds 70% of max_tokens_limit, summarize it
    token_threshold = int(0.7 * max_tokens_limit)
    if convo_tokens > token_threshold:
        print("Sanitized conversation exceeds token threshold. Summarizing...")
        summarized_convo = summarize_text_with_limit(
            sanitized_convo_text,
            max_output_length=token_threshold,
            max_chunk_size=3000,
            max_summary_tokens=500
        )
        # Split the summarized conversation back into lines
        summarized_convo_lines = summarized_convo.split('\n')
        return summarized_convo_lines
    else:
        # Return the sanitized transcript as a list of lines
        return sanitized_transcript
    print(f"santized data -> {sanitized_transcript}")

def calculate_transcript_tokens(convo_lines, model="gpt-4o-mini"):
    encoding = tiktoken.encoding_for_model(model)
    convo_text = '\n'.join(convo_lines)  # Convert list to string
    return len(encoding.encode(convo_text))

def estimate_prompt_tokens(prompt, convo_lines, model="gpt-4o-mini"):
    encoding = tiktoken.encoding_for_model(model)
    prompt_tokens = len(encoding.encode(prompt))
    convo_tokens = calculate_transcript_tokens(convo_lines, model=model)
    # print(f"{prompt_tokens} + {convo_tokens}")
    return prompt_tokens + convo_tokens

def group_prompts(prompts, convo_lines, max_tokens, model="gpt-4o-mini"):
    """Group prompts together to minimize API calls while staying under max token limit."""
    print(f"group prompts -> {convo_lines}")
    groups = []
    current_group = []
    current_token_count = 0
    for field, prompt in prompts.items():
        # print(field, prompt)
        # Estimate the number of tokens for the current prompt + convo
        estimated_tokens = estimate_prompt_tokens(prompt, convo_lines, model=model)

        # Handle case where estimated tokens exceed max tokens
        if estimated_tokens > max_tokens:
            print(f"Warning: The prompt for field '{field}' exceeds the maximum token limit and will be skipped.")
            continue  # Skip this prompt or handle as needed

        # If adding this prompt exceeds the max tokens, start a new group
        if current_token_count + estimated_tokens > max_tokens:
            if current_group:
                groups.append(current_group)  # Save the current group
            current_group = [(field, prompt)]  # Start a new group with the current prompt
            current_token_count = estimated_tokens  # Reset the token count to the current prompt's tokens
        else:
            # Add the current prompt to the group
            current_group.append((field, prompt))
            current_token_count += estimated_tokens

    # Add the last group if it contains any prompts
    if current_group:
        groups.append(current_group)

    return groups


import time
from openai.error import RateLimitError

def generate_grouped_content(group, convo_lines, max_tokens):
    """
    Generates content for a group of fields using a single prompt and transcript, with rate limit handling.
    """
    # Join the conversation lines into a single string
    convo = '\n'.join(convo_lines)

    field_prompts = []
    field_mapping = []

    # Create individual prompts for each field
    for field, prompt in group:
        formatted_prompt = prompt.format(convo=convo)
        field_prompts.append(f"Field: {field}\nInstruction: {formatted_prompt}")
        field_mapping.append(field)

    # Combine all field prompts into one message
    combined_prompt = "\n\n".join(field_prompts)

    # Prepare the messages for ChatCompletion
    messages = [
        {
            "role": "system",
            "content": (
                "You are an expert licensed therapist assistant that helps to analyze therapy session transcripts and "
                "extract relevant information based on instructions. Provide your responses in the same order as the "
                "fields, starting each response with 'Field: [field name]'."
            )
        },
        {"role": "user", "content": combined_prompt}
    ]

    # Calculate tokens for messages
    encoding = tiktoken.encoding_for_model("gpt-4o-mini")
    input_tokens = sum(len(encoding.encode(message['content'])) for message in messages)

    # Set a reasonable max_tokens for the response
    max_response_tokens = 1000  # Adjust this value based on expected response length

    # Ensure total tokens do not exceed the model's context length
    max_context_length = 8000  # GPT-4 standard context length
    if input_tokens + max_response_tokens > max_context_length:
        max_response_tokens = max_context_length - input_tokens - 1  # Subtract 1 for safety

    # Retry logic for rate limit errors
    retry_delay = 10  # Initial delay in seconds
    max_retries = 5

    for attempt in range(max_retries):
        try:
            # Send the combined messages to GPT-4 using OpenAI API
            response = openai.ChatCompletion.create(
                model="gpt-4o-mini",
                messages=messages,
                max_tokens=max_response_tokens,
                temperature=0.7
            )
            break  # Exit the retry loop if successful
        except RateLimitError as e:
            if attempt < max_retries - 1:
                # Wait for the suggested time or use exponential backoff
                wait_time = int(e.headers.get("Retry-After", retry_delay))
                print(f"Rate limit exceeded. Waiting for {wait_time} seconds before retrying...")
                time.sleep(wait_time)
                retry_delay *= 2  # Exponential backoff
            else:
                print("Maximum retries reached. Aborting.")
                raise e

    # Extract and clean up the assistant's reply
    assistant_reply = response['choices'][0]['message']['content'].strip()

    # Split the assistant's reply into responses for each field
    responses = {}
    pattern = re.compile(r'Field:\s*(.*?)\n(.*?)(?=Field:|$)', re.DOTALL)
    matches = pattern.findall(assistant_reply)

    for field_name, content in matches:
        field_name = field_name.strip()
        content = content.strip()
        responses[field_name] = {"response": content}

    return responses


def generate_all_groups(prompts, convo_lines, max_tokens, model="gpt-4o-mini"):
    """
    Generates responses for all grouped fields based on the conversation transcript and prompts.

    Parameters:
    - prompts: Dictionary mapping fields to prompts
    - convo_lines: List of sanitized conversation lines
    - max_tokens: Maximum number of tokens allowed in the prompt + response
    - model: The model to use for token estimation

    Returns:
    - A dictionary mapping fields to responses
    """
    # Group the prompts based on the token limits
    grouped_prompts = group_prompts(prompts, convo_lines, max_tokens, model=model)

    results = {}

    # Loop through all the groups and generate responses
    for group in grouped_prompts:
        group_responses = generate_grouped_content(group, convo_lines, max_tokens)
        results.update(group_responses)

    return results



# Example usage
# if __name__ == "__main__":
#     # Sample meeting ID
#     meeting_id = "65f301f43d99896904f34b60"  # Replace with your actual meeting ID
#
#     # Set the model and max tokens
#     model = "gpt-4o-mini"
#     max_tokens_limit = 3000  # Adjust according to the model's limits
#
#     # Get the sanitized conversation as a list of strings
#     sanitized_convo = get_sanitized_conversation(meeting_id, max_tokens_limit)
#
#     # Generate all groups and convert the result to JSON format
#     generated_groups = generate_all_groups(prompts_dict, sanitized_convo, max_tokens_limit, model=model)
#
#     # Output the result as a JSON string
#     json_output = json.dumps(generated_groups, indent=4)
#     print(json_output)