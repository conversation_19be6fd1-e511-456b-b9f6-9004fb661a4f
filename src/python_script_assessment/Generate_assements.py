
from assessments import *  # Import the function to generate assessment groups
from Get_transcript import get_conversation_from_transcript  # Adjust the import based on your actual function
# from get_diagnosis import get_diagnosis_codes
from sentize_anonymise import get_sanitized_conversation  # Assuming this is the function for sanitization
from motor.motor_asyncio import AsyncIOMotorClient
import asyncio
import sys

openAIAPIKey = sys.argv[1]
meetingId = sys.argv[2]
mongoConnectString = sys.argv[3]
dbName = sys.argv[4]

client = AsyncIOMotorClient(mongoConnectString)
db = client[dbName]

async def create_indexes():
    await db['diagnosisnotes'].create_index([('meetingId', 1)])
    await db['diagnosisnotes'].create_index([('clientId', 1)])
async def process_assessments(meeting_id):
    # Step 1: Get the conversation transcript
    transcript_data = await get_conversation_from_transcript(meeting_id)
    conversation_text = transcript_data.get('convo', '')

    # If conversation_text is a list, join it into a single string

    if isinstance(conversation_text, list):
        conversation_text = "\n".join(conversation_text)

    # Step 2: Sanitize the conversation
    sanitized_text =  get_sanitized_conversation(conversation_text)  # Pass the conversation text to sanitize

    # Step 3: Retrieve diagnosis codes
    # diagnosis = await get_diagnosis_codes(meeting_id)
    # diagnosis_codes = ', '.join(f"{key}: {value}" for key, value in diagnosis.items())

    # Step 4: Generate assessment groups
    max_tokens_limit = 8000  # Adjust according to the model's limits
    generated_groups = generate_all_groups(prompts_dict, sanitized_text,  max_tokens_limit)

    # Step 5: Output the result as a JSON string
    print("Generated Assessment Groups:\n", generated_groups)

if __name__ == "__main__":
    meeting_id = meetingId  # Replace with the actual meeting ID
    asyncio.run(process_assessments(meeting_id))  # Run the async process_assessments function
