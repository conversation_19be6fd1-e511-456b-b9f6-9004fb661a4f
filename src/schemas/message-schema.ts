import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IChatData } from "../models/chat-model";
import { IMessageData } from "../models/chat-message-model";
import Chat from "./chat-schema";
import Upload from "./upload-schema";

import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const MessageSchema = new mongoose.Schema(
  {
    chatId: {
      type: Schema.Types.ObjectId,
      ref: Chat.modelName,
      require: true,
    },
    senderId: {
      type: Schema.Types.ObjectId,
      ref: User.modelName,
      require: true,
    },
    AttachmentUploadId: {
      type: Schema.Types.ObjectId,
      ref: Upload.modelName,
      require: true,
    },
    mentionedMessage: {
      type: Schema.Types.ObjectId,
      ref: "Message",
      require: true,
    },
    text: { type: Schema.Types.String, require: true },
    isActive: { type: Schema.Types.Boolean, require: true, default: true },
    isMsgSeen:{ type: Schema.Types.Boolean, require: true, default: false },
    isFromMobile:{ type: Schema.Types.Boolean, require: false, default: false }
  },

  schemaOptions
);

const Message = mongoose.model<IMessageData>("Message", MessageSchema);
export default Message;
