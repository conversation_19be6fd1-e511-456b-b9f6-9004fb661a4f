import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ITechTicket } from "../models/tech-ticket-model";
import Upload from "./upload-schema";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const TechTicketSchema = new mongoose.Schema(
  {
    title: {
      type: Schema.Types.String,
      require: true,
    },
    message: {
      type: Schema.Types.String,
      require: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    uploadId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: Upload.modelName,
    },
    isRead: {
      type: Schema.Types.Boolean,
      require: true
    }
  },
  schemaOptions
);

const TechTicket = mongoose.model<ITechTicket>("TechTicket", TechTicketSchema);

export default TechTicket;
