import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import { ChatGroupType, IChatGroup } from "../models/chat-group-model";
import Upload from "./upload-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const ChatGroupSchema = new mongoose.Schema(
  {
    title: {
      type: Schema.Types.String,
      require: true,
    },
    description: {
      type: Schema.Types.String,
      require: true,
    },
    type: {
      type: Schema.Types.String,
      enum: ChatGroupType,
      require: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    groupIcon: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Upload.modelName,
    },
  },

  schemaOptions
);

const ChatGroup = mongoose.model<IChatGroup>("ChatGroup", ChatGroupSchema);
export default ChatGroup;
