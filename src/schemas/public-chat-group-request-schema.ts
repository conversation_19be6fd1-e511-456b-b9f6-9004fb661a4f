import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import ChatGroup from "./chat-group-schema";
import {
  IPublicChatGroupRequest,
  PublicChatGroupRequestStatusType,
} from "../models/public-chat-group-request-model";
const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};
export const PublicChatGroupRequestSchema = new mongoose.Schema(
  {
    groupId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: ChatGroup.modelName,
    },
    userId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    status: {
      type: Schema.Types.String,
      enum: PublicChatGroupRequestStatusType,
      require: true,
    },
  },
  schemaOptions
);
const PublicChatGroupRequest = mongoose.model<IPublicChatGroupRequest>(
  "PublicChatGroupRequest",
  PublicChatGroupRequestSchema
);
export default PublicChatGroupRequest;