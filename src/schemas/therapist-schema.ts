import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import {
  ITherapist,
  RegistrationApprovalStatus,
  TherapistCategorizationByType,
} from "../models/therapist-model";
import { UserRole } from "../models/user-model";
import Education from "./education-schema";
import ExperienceTag from "./experience-tag-schema";
import License from "./license-schema";
import Profession from "./profession-schema";
import { PaymentSchema } from "./sub-schemas/payment-schema";
import { ReviewSchema } from "./sub-schemas/review-schema";
import { WorkingHourSchema } from "./sub-schemas/working-hour";
import Upload from "./upload-schema";
import User from "./user-schema";
import ProfessionLicense from "./profession-license-schema";
import InsuranceCompany from "./Insurance-company-schema";
import { PayRateTypeSchema } from "./sub-schemas/pay-rate-type-schema";
import { BlockDateSchema } from "./sub-schemas/block-date.schema";
import Transaction from "./transaction-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const TherapistSchema = new mongoose.Schema(
  {
    experiencedIn: [
      {
        type: Schema.Types.ObjectId,
        required: true,
        ref: ExperienceTag.modelName,
      },
    ],
    insuranceCompanies: [
      {
        type: Schema.Types.ObjectId,
        required: true,
        ref: InsuranceCompany.modelName,
      },
    ],
    licenseId: [
      {
        type: Schema.Types.ObjectId,
        required: true,
        ref: License.modelName,
      },
    ],
    qualifications: [
      {
        type: Schema.Types.ObjectId,
        required: true,
        ref: Education.modelName,
      },
    ],
    profession: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Profession.modelName,
    },
    professionLicense: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: ProfessionLicense.modelName,
    },
    loginVerification: {
      type: Schema.Types.String,
      required: false,
    },
    dislikedClients: [
      {
        type: Schema.Types.ObjectId,
        require: false,
      },
    ],
    workingHours: [
      {
        type: WorkingHourSchema,
      },
    ],
    isAvailable: {
      type: Schema.Types.Boolean,
      required: false,
    },
    yearsOfExperience: {
      type: Schema.Types.Number,
      required: false,
    },
    reviews: [
      {
        type: ReviewSchema,
      },
    ],
    payRateType: {
      type: PayRateTypeSchema,
    },
    payment: [
      {
        type: PaymentSchema,
      },
    ],
    socialSecurity: {
      type: Schema.Types.String,
      required: false,
    },
    cAQH: {
      type: Schema.Types.String,
      required: false,
    },
    nPI1: {
      type: Schema.Types.String,
      required: false,
    },
    taxIdentification: {
      type: Schema.Types.String,
      required: false,
    },
    license: {
      type: Schema.Types.String,
      required: false,
    },
    issueDate: {
      type: Schema.Types.String,
      required: false,
    },
    expirationDate: {
      type: Schema.Types.String,
      required: false,
    },
    schoolName: {
      type: Schema.Types.String,
      required: false,
    },
    dateOfGraduation: {
      type: Schema.Types.String,
      required: false,
    },
    schoolStreetAddress: {
      type: Schema.Types.String,
      required: false,
    },
    schoolCity: {
      type: Schema.Types.String,
      required: false,
    },
    schoolState: {
      type: Schema.Types.String,
      required: false,
    },
    schoolZipCode: {
      type: Schema.Types.String,
      required: false,
    },
    taxonomyCode: {
      type: Schema.Types.String,
      required: false,
    },
    malpracticePolicy: {
      type: Schema.Types.String,
      required: false,
    },
    malpracticeExpirationDate: {
      type: Schema.Types.String,
      required: false,
    },
    videoId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Upload.modelName,
    },
    vimeoId: {
      type: Schema.Types.String,
      required: false,
    },
    disclosureStatementId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Upload.modelName,
    },
    stripeConnectedAccountId: {
      type: Schema.Types.String,
      required: false,
    },
    stripeConnectedLoginLink: {
      type: Schema.Types.String,
      required: false,
    },
    stripeChargesEnabled: {
      type: Schema.Types.Boolean,
      required: false,
    },
    stripeDetailsSubmitted: {
      type: Schema.Types.Boolean,
      required: false,
    },
    payRate: {
      type: Schema.Types.Number,
      required: false,
    },
    timeZone: {
      type: Schema.Types.String,
      required: false,
    },
    aiGenerateCount: {
      type: Schema.Types.Number,
      required: false,
    },
    aiReviewSubmitted: {
      type: Schema.Types.Boolean,
      required: false,
    },
    caqhUserName: {
      type: Schema.Types.String,
      required: false,
    },
    caqhPassword: {
      type: Schema.Types.String,
      required: false,
    },
    medicaidUsername: {
      type: Schema.Types.String,
      required: false,
    },
    MedicaidPassword: {
      type: Schema.Types.String,
      required: false,
    },
    psychologyTodayUsername: {
      type: Schema.Types.String,
      required: false,
    },
    psychologyTodayPassword: {
      type: Schema.Types.String,
      required: false,
    },
    blockedDates: [
      {
        type: BlockDateSchema,
        required: false,
      },
    ],
    therapyState: [
      {
        type: Schema.Types.String,
        required: false,
        default: [],
      },
    ],
    claimOpen: {
      type: Schema.Types.Boolean,
      required: false,
    },
    signature: {
      type: Schema.Types.String,
      require: false,
    },
    firstSessionBonus: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Transaction.modelName,
    },
    twentySessionsBonus: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Transaction.modelName,
    },
    grantedAccessFileFolderPermission: {
      type: Schema.Types.Boolean,
      require: false,
      // default: false,
    },
    registrationApprovalStatus: {
      type: Schema.Types.String,
      require: false,
      default: RegistrationApprovalStatus.PENDING,
    },
    aiNotesType: {
      type: Schema.Types.String,
      require: false,
    },
    medicaidId: {
      type: Schema.Types.String,
      require: false,
    },
    isNewDashboard: {
      type: Schema.Types.Boolean,
      require: false,
    },
    therapistCategorizationByType:{
      type: Schema.Types.String,
      require: false,
      default: TherapistCategorizationByType.NONE,
    },
  },
  schemaOptions
);

const Therapist = User.discriminator<ITherapist>(
  "Therapist",
  TherapistSchema,
  UserRole.THERAPIST
);

TherapistSchema.virtual("recentTransaction", {
  ref: "Transaction",
  localField: "_id",
  foreignField: "therapistId",
  justOne: true,
  options: { sort: { createdAt: -1 } },
});

export default Therapist;
