import * as mongoose from "mongoose";
import { AmeriGeneralInformationSchema } from "./sub-schemas/AmeriHealthAuthFormSubSchemas/ameri-general-information-schema";
import { AmeriServiceInformationSchema } from "./sub-schemas/AmeriHealthAuthFormSubSchemas/ameri-service-information-schema";
import { AmeriProviderSchema } from "./sub-schemas/AmeriHealthAuthFormSubSchemas/ameri-provider-schema";
import { AmeriPrescribingProviderSchema } from "./sub-schemas/AmeriHealthAuthFormSubSchemas/ameri-prescribing-provider-schema";
import { AuthFormType, IAuthorizationForm } from "../models/authorization-form-model";
import AuthorizationForm from "./authorization-form-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const AmeriHealthAuthFormSchema = new mongoose.Schema(
  {
    generalInformation: {
      type: AmeriGeneralInformationSchema,
      required: true,
    },
    serviceInformation: {
      type: AmeriServiceInformationSchema,
      required: true,
    },
    providerInformation: {
      type: AmeriProviderSchema,
      required: true,
    },
    prescibingProviderInformation: {
      type: AmeriPrescribingProviderSchema,
      required: true,
    },
  },
  schemaOptions
);

const AmeriHealthAuthForm = AuthorizationForm.discriminator<IAuthorizationForm>(
  "AmeriHealthAuthForm",
  AmeriHealthAuthFormSchema,
  AuthFormType.AmeriHealthAuthForm
);

export default AmeriHealthAuthForm;
