import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import { IClientReward } from "../models/client-reward-model";
import TherapistReferral from "./therapist-referral-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const ClientRewardSchema = new mongoose.Schema(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    },
    type: {
      type: Schema.Types.String,
      require: true
    },
    transactionAmount: {
      type: Schema.Types.Number,
      require: true,
      default: 0
    },
    accumulatedBalance: {
      type: Schema.Types.Number,
      require: true,
      default: 0
    },
    accumulatedTotalEarnings: {
      type: Schema.Types.Number,
      require: true,
      default: 0
    },
    accumulatedWithdrawals: {
      type: Schema.Types.Number,
      require: true,
      default: 0
    },
    rewardType: {
      type: Schema.Types.String,
      require: false
    },
    rewardReferralId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: TherapistReferral.modelName
    },
  },
  schemaOptions
);

const ClientReward = mongoose.model<IClientReward>("ClientReward", ClientRewardSchema);

export default ClientReward;
