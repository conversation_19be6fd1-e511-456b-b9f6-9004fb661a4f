import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IGroupTherapy } from "../models/group-therapy-model";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const GroupTherapySchema = new mongoose.Schema(
  {
    groupTitle: {
      type: Schema.Types.String,
      require: true,
    },
    groupMembers: [
      {
        type: Schema.Types.String,
        require: true,
      },
    ],
    createdBy: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    uploadId: {
      type: Schema.Types.ObjectId,
      require: true,
    },
  },
  schemaOptions
);

const GroupTherapy = mongoose.model<IGroupTherapy>("GroupTherapy", GroupTherapySchema);

export default GroupTherapy;