import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import Client from "./client-schema";
import Meeting from "./meeting-schema";
import { ICDCodeSchema } from "./sub-schemas/ICDCode-schema";
import { ProcedureCodeSchema } from "./sub-schemas/procedure-code-schema";
import Therapist from "./therapist-schema";
import Upload from "./upload-schema";
import { IDiagnosisNoteVersion } from "../models/diagnosis-note-version-model";
import DiagnosisNote from "./diagnosis-note-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const DiagnosisNoteVersionSchema = new mongoose.Schema(
  {
    diagnosisNoteId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: DiagnosisNote.modelName
    },
    updated: {
      type: Schema.Types.Boolean,
      require: true,
      default: false,
    },
    updatedByTherapist: {
      type: Schema.Types.Boolean,
      require: true,
      default: false,
    },
    isVonageTranscribe: {
      type: Schema.Types.Boolean,
      require: true,
      default: false,
    },
    clinicalReminderEmail: {
      type: Schema.Types.Boolean,
      require: false,
    },
    clinicalReminderSMS: {
      type: Schema.Types.Boolean,
      require: false,
    },
    clientId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: Client.modelName,
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: Therapist.modelName,
    },
    meetingId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: Meeting.modelName,
    },
    // gg
    patientID: {
      type: Schema.Types.String,
      require: false,
    },
    patientAcountNo: {
      type: Schema.Types.String,
      require: false,
    },
    encounterID: {
      type: Schema.Types.String,
      require: false,
    },
    encounterDate: {
      type: Schema.Types.Date,
      require: false,
    },
    encounterType: {
      type: Schema.Types.String,
      require: false,
    },
    chiefComplaint: {
      type: Schema.Types.String,
      require: false,
    },
    historyOfPresentIllness: {
      type: Schema.Types.String,
      require: false,
    },
    historyOfPresentIllnessAttachments: [
      {
        type: Schema.Types.ObjectId,
        require: false,
        ref: Upload.modelName,
      },
    ],
    carePlan: {
      type: Schema.Types.String,
      require: false,
    },
    carePlanAttachments: [
      {
        type: Schema.Types.ObjectId,
        require: false,
        ref: Upload.modelName,
      },
    ],
    procedureNotes: {
      type: Schema.Types.String,
      require: false,
    },
    signature: {
      type: Schema.Types.String,
      required: false,
    },
    diagnosisICDcodes: [
      {
        type: ICDCodeSchema,
        require: false,
      },
    ],
    secondaryDiagnosisICDcodes: [
      {
        type: ICDCodeSchema,
        require: false,
      },
    ],
    mentalBehavioralStatus: {
      type: Schema.Types.String,
      require: false,
    },
    mentalBehavioralStatusAttachments: [
      {
        type: Schema.Types.ObjectId,
        require: false,
        ref: Upload.modelName,
      },
    ],
    asssessments: {
      type: Schema.Types.String,
      require: false,
    },
    cptCode: {
      type: Schema.Types.String,
      require: false,
    },
    assessmentAttachments: [
      {
        type: Schema.Types.ObjectId,
        require: false,
        ref: Upload.modelName,
      },
    ],
    selectedGoals: [
      {
        type: Schema.Types.String,
        require: false,
      },
    ],
    intervention: {
      type: Schema.Types.String,
      require: false,
    },
    procedureCodes: [
      {
        type: ProcedureCodeSchema,
        required: false,
      },
    ],
    noteType: {
      type: Schema.Types.String,
      require: false,
    },
    reasonForEdit: {
      type: Schema.Types.String,
      required: true,
    },
    versionCreatedAt: {
      type: Schema.Types.Date,
      required: true,
    },
  },
  schemaOptions
);

const DiagnosisNoteVersion = mongoose.model<IDiagnosisNoteVersion>(
  "DiagnosisNoteVersion",
  DiagnosisNoteVersionSchema
);

export default DiagnosisNoteVersion;
