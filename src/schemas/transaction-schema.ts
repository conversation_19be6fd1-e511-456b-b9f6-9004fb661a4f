import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import { ITransaction } from "../models/transaction-model";
import Meeting from "./meeting-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const TransactionSchema = new mongoose.Schema(
  {
    therapistId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    },
    meetingId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: Meeting.modelName
    },
    type: {
      type: Schema.Types.String,
      require: true
    },
    insuranceCompany: {
      type: Schema.Types.String,
      require: false
    },
    transactionAmount: {
      type: Schema.Types.Number,
      require: true,
      default: 0
    },
    accumulatedBalance: {
      type: Schema.Types.Number,
      require: true,
      default: 0
    },
    accumulatedTotalEarnings: {
      type: Schema.Types.Number,
      require: true,
      default: 0
    },
    accumulatedWithdrawals: {
      type: Schema.Types.Number,
      require: true,
      default: 0
    },
    paidStatus: {
      type: Schema.Types.String,
      require: false
    },
    eligibleForPayment: {
      type: Schema.Types.Boolean,
      require: false,
      default: true
    },
    paymentReleaseDate: {
      type: Schema.Types.Date,
      require: false,
    },
  },
  schemaOptions
);

const Transaction = mongoose.model<ITransaction>("Transaction", TransactionSchema);

export default Transaction;
