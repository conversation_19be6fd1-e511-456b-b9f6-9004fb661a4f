import * as mongoose from "mongoose";
import { AmbetterBasicInformationSchema } from "./sub-schemas/AmbetterAuthFormSubSchemas/ambetter-basic-information-schema";
import { AmbetterAuthorizationRequestSchema } from "./sub-schemas/AmbetterAuthFormSubSchemas/ambetter-authorization-request-schema";
import { AmbetterFacilityInformationSchema } from "./sub-schemas/AmbetterAuthFormSubSchemas/ambetter-facility-information-schema";
import { AmbetterMemberInformationSchema } from "./sub-schemas/AmbetterAuthFormSubSchemas/ambetter-member-information-schema";
import { AmbetterProviderSchema } from "./sub-schemas/AmbetterAuthFormSubSchemas/ambetter-provider-schema";
import { AuthFormType, IAuthorizationForm } from "../models/authorization-form-model";
import AuthorizationForm from "./authorization-form-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
      getters: true,
      virtuals: true,
    },
  };

  export const AmbetterAuthFormSchema = new mongoose.Schema(
    {
      basicInformation: {
          type: AmbetterBasicInformationSchema,
          required: true,
      },
      memberInformation: {
          type: AmbetterMemberInformationSchema,
          required: true,
      },
      providerInformation: {
          type: AmbetterProviderSchema,
          required: true,
      },
      facilityInformation: {
          type: AmbetterFacilityInformationSchema,
          required: true,
      },
      authorizationRequest: {
          type: AmbetterAuthorizationRequestSchema,
          required: true,
      },
    },
    schemaOptions
  );

const AmbetterAuthForm = AuthorizationForm.discriminator<IAuthorizationForm>(
    "AmbetterAuthForm",
    AmbetterAuthFormSchema,
    AuthFormType.AmbetterAuthForm
  );

export default AmbetterAuthForm;