import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import Insurance from "./insurance-schema";
import { IAuthorizationForm } from "../models/authorization-form-model";

export const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    discriminatorKey: 'authFormType',
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const AuthorizationFormSchema = new mongoose.Schema({
    therapistId: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: User.modelName
    },
    clientId: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: User.modelName
    },
    insuranceCompanyId: {
        type: Schema.Types.ObjectId,
        require: true,
        ref: Insurance.modelName,
      },
    authFormType: {
        type: Schema.Types.String,
        required: true
    },
}, schemaOptions);

const AuthorizationForm = mongoose.model<IAuthorizationForm>('AuthorizationForm', AuthorizationFormSchema);
export default AuthorizationForm;