import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import { ITranscribe } from "../models/transcribe-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const TranscribeSchema = new mongoose.Schema(
  {
    meetingId: {
      type: Schema.Types.String,
      require: true
    },
    clientId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: User.modelName,
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: User.modelName,
    },
    videoUrl: {
      type: Schema.Types.String,
      require: true,
    },

    transcriptText: [
      {
        type: Schema.Types.String,
        require: true,
        default: [],
      },
    ],
    transCribeInProcess: {
      type: Schema.Types.Boolean,
      require: true,
    },
    speakersDetected: {
      type: Schema.Types.Boolean,
      require: false,
    },
    speakersArray: [
      {
        type: Schema.Types.ObjectId,
        require: false,
        ref: User.modelName,
        default: [],
      },
    ],
    aiResponse: {
      type: Schema.Types.String,
      require: false
    },
  },
  schemaOptions
);

const Transcribe = mongoose.model<ITranscribe>("Transcribe", TranscribeSchema);

export default Transcribe;
