import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IProfession } from "../models/profession-model";
import { IProfessionLicense } from "../models/profession-license-model";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const ProfessionLicenseSchema = new mongoose.Schema(
    {
        professionId: {
            type: Schema.Types.ObjectId,
            require: true,
        },
        name: {
            type: Schema.Types.String,
            require: true,
        },
    },
    schemaOptions
);

const ProfessionLicense = mongoose.model<IProfessionLicense>("ProfessionLicense", ProfessionLicenseSchema);

export default ProfessionLicense;
