import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { AssesmentHeaderSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/assesment-header-schema";
import { generalInforSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/general-Info-schema";
import { chiefComplaintSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/chiefComplaint-schema";
import { currentServicesSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/currentServices-schema";
import { leisureActivitySchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/leisureActivity-Schema";
import { houseHoldMemberSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/houseHoldMember-schema";
import { IClinicalAssesment, currentLivingSituation, mortivationEngageInServices } from "../models/clinicalAssesment-model";
import { HistoryofAbuseSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/historyofAbuse-schema";
import { PSAServiceHistorySchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/PSAServiceHistory-schema";
import { historyofPsychiatricDiagnosesSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/historyofPsychiatricDiagnoses-schema";
import { historyofSymptomsSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/historyofSymptoms-schema";
import { CurrentMedicationsSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/currentMedications-schema";
import { MedicalHistorySchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/medicalHistory-schema";
import { HealthNotesSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/healthNote-schema";
import { LegalHistorySchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/legalHistory-schema";
import { ReligiousCulturalLanguagePrefSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/religious-schema";
import { AsamDimensionsSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/asamDimension-schema";
import { EmploymentVocationalSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/employmentVocational-schema";
import { independentLivingSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/independentLiving-schema";
import { MentalStatusExamSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/mentalHealthAssessment-schema";
import { SubstanceInfoSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/substanceInfo-schema";
import { SuicideHomicideRiskPotentialSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/suicideRiskPotential-schema";
import { SummaryofNeedSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/summaryofNeeds-schema";
import { RecoveryHistoryandEnvironmentSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/RecoveryHistory-schema";
import { currentImpairmentsSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/currentImpairments-schema";
import { psychiatricSymptomSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/psychiatricSymptom-schema";
import { SummeryOfFindingSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/summeryOfFinding-schema";
import { ClientSaftyPlanSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/clientSaftyPlan-schema";
import { signatureDetailsSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/signaturePage-schema";
import { GAD7AnxietySchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/gad7Anxeity-schema";
import { patientHealthQuestionaireSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/patientHealthQuestionaire-schema";
import { RecoveryAssesmentScaleSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/recoveryAssesmentScale-schema";
import Client from "./client-schema";
import { AceScoreSchema } from "./sub-schemas/ClinicalAssesmentSubSchemas/aceScore-schema";
import User from "./user-schema";
import { IClinicalAssesmentVersion } from "../models/clinicalAssesment-version-model";
import ClinicalAssesment from "./clinical-assesment-schema";


const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const ClinicalAssesmentVersionSchema = new mongoose.Schema(
    {
        clinicalAssesmentId: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: ClinicalAssesment.modelName
        },
        therapistId: {
            type: Schema.Types.ObjectId,
            required: true
        },
        clientId: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: Client.modelName
        },
        assesmentHeader: {
            type: AssesmentHeaderSchema,
            required: false,
        },
        lengthOfAssestment: {
            type: Schema.Types.Number,
            required: false,
        },
        dateOfAssesment: {
            type: Schema.Types.Date,
            required: false,
        },
        comprehensiveAssessment: {
            type: Schema.Types.Boolean,
            required: false,
        },
        other: {
            type: Schema.Types.String,
            required: false,
        },
        generalInfor: {
            type: generalInforSchema,
            required: false,
        },
        chiefComplaint: {
            type: chiefComplaintSchema,
            required: false,
        },
        currentServices: {
            type: currentServicesSchema,
            required: false,
        },
        religiousCulturalLanguagePref: {
            type: ReligiousCulturalLanguagePrefSchema,
            required: false,
        },
        leisureActivity: [{
            type: leisureActivitySchema,
            required: false,
        }],
        identifiedStrengths: {
            type: Schema.Types.String,
            required: false,
        },
        identifiedNeeds: {
            type: Schema.Types.String,
            required: false,
        },
        currentlivingSituation: {
            type: Schema.Types.String,
            enum: currentLivingSituation,
            required: false
        },
        currentAddressDuration: {
            type: Schema.Types.String,
            required: false
        },
        frequentMoves: {
            type: Schema.Types.Boolean,
            required: false
        },
        strengths: {
            type: Schema.Types.String,
            required: false
        },
        needs: {
            type: Schema.Types.String,
            required: false
        },
        houseHoldMember: [{
            type: houseHoldMemberSchema,
            required: false,
        }],
        // page-03 rest of houseHold
        immediateFamilyOutside: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false
            },
            explanation: {
                type: Schema.Types.String,
                required: false
            }
        },
        feelingUnsafe: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false
            },
            explanation: {
                type: Schema.Types.String,
                required: false
            }
        },
        incomeSource: {
            employed: {
                type: Schema.Types.Boolean,
                required: false
            },
            where: {
                type: Schema.Types.String,
                required: false
            },
            SSDI: {
                type: Schema.Types.Boolean,
                required: false
            },
            unemployed: {
                type: Schema.Types.Boolean,
                required: false
            },
            yes: {
                type: Schema.Types.Boolean,
                required: false
            },
            explanation: {
                type: Schema.Types.String,
                required: false
            },
        },
        transportSource: {
            hasaCar: {
                type: Schema.Types.Boolean,
                required: false
            },
            publicTransport: {
                type: Schema.Types.Boolean,
                required: false
            },
        },
        // Developmental Milestones page-03
        adultDevelopmentalAbnormalities: {
            type: Schema.Types.String,
            required: false
        },
        wnl: {
            type: Schema.Types.Boolean,
            required: false
        },
        noReportHistoryOfAbuse: {
            type: Schema.Types.Boolean,
            required: false
        },
        historyofAbuseSchema: {
            type: HistoryofAbuseSchema,
            required: false,
        },

        pSAServiceHistory: [
            {
                type: PSAServiceHistorySchema,
                required: false,
            },
        ],
        noPSASserviceHistoryReported:
        {
            type: Schema.Types.Boolean,
            required: false,
        },

        // page 04
        historyofPsychiatricDiagnoses: [
            {
                type: historyofPsychiatricDiagnosesSchema,
                required: false,
            },
        ],
        noHistrotypsyDiagnosesReported:
        {
            type: Schema.Types.Boolean,
            required: false,
        },

        historyofSymptoms: {
            type: historyofSymptomsSchema,
            required: false,
        },

        currentModications: [
            {
                type: CurrentMedicationsSchema,
                required: false,
            },
        ],
        medicalHistory: {
            type: MedicalHistorySchema,
            required: false,
        },
        healthNotes: {
            type: HealthNotesSchema,
            required: false,
        },

        // page 05
        legalHistory: {
            type: LegalHistorySchema,
            required: false,
        },
        substanceAbuse: {
            type: SubstanceInfoSchema,
            required: false,
        },

        // page 06
        asamDimensions: {
            type: AsamDimensionsSchema,
            required: false
        },

        // page 07
        schoolName: {
            type: Schema.Types.String,
            required: false
        },
        highestEducation: {
            type: Schema.Types.String,
            required: false
        },
        employmentVocational: {
            type: EmploymentVocationalSchema,
            required: false
        },
        militaryInfo: {
            checkIfNA: {
                type: Schema.Types.Boolean,
                required: false
            }
        },
        rank: {
            type: Schema.Types.String,
            required: false
        },
        yearsServed: {
            type: Schema.Types.String,
            required: false
        },
        reasonforDischarge: {
            type: Schema.Types.String,
            required: false
        },
        serviceConnectedDisability: {
            yesOrNo: {
                type: Schema.Types.Boolean,
                required: false
            },
            explaination: {
                type: Schema.Types.String,
                required: false
            },
        },
        independentLiving: {
            type: independentLivingSchema,
            required: false
        },
        // page 08
        mentalStatusExam: {
            type: MentalStatusExamSchema,
            required: false
        },
        suicideRiskPotential: {
            type: SuicideHomicideRiskPotentialSchema,
            required: false
        },
        // page 09
        summaryofNeeds: {
            type: SummaryofNeedSchema,
            required: false
        },
        recoveryHistoryandEnvironment: {
            type: RecoveryHistoryandEnvironmentSchema,
            required: false
        },
        mortivationEngageInServices: {
            type: Schema.Types.String,
            enum: mortivationEngageInServices,
            required: false
        },
        currentImpairments: {
            type: currentImpairmentsSchema,
            required: false
        },
        // page 10
        psychiatricSymptom: {
            type: psychiatricSymptomSchema,
            required: false
        },
        summeryOfFindings: {
            type: SummeryOfFindingSchema,
            required: false
        },
        eligibilityRecommendations: {
            type: Schema.Types.String,
            required: false
        },
        treatmentRecommendations: {
            type: Schema.Types.String,
            required: false
        },
        recommendationNotes: {
            type: Schema.Types.String,
            required: false
        },
        saftyPlanNotNeeded: {
            type: Schema.Types.Boolean,
            required: false
        },
        clientSaftyPlanCompleted: {
            type: Schema.Types.Boolean,
            required: false
        },
        clientSaftyPlan: {
            type: ClientSaftyPlanSchema,
            required: false
        },
        signatureDetails: {
            type: signatureDetailsSchema,
            required: false
        },
        therapistSignature: {
            type: Schema.Types.String,
            required: false,
        },
        clientSignature: {
            type: Schema.Types.String,
            required: false,
        },
        gad7anxiety: {
            type: GAD7AnxietySchema,
            required: false
        },
        AceScore:{
            type:AceScoreSchema,
            required:false
        },
        pationHealthQuestionaire: {
            type: patientHealthQuestionaireSchema,
            required: false
        },
        findingAceScore: {
            type: Schema.Types.Number,
            required: false
        },
        recoveryAssesmentScale: {
            type: RecoveryAssesmentScaleSchema,
            required: false
        },
        isSignature: {
            type: Schema.Types.Boolean,
            require: false
        },
        reasonForEdit: {
            type: Schema.Types.String,
            required: false,
        },
        versionCreatedBy: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: User.modelName,
        },
        versionCreatedAt: {
            type: Schema.Types.Date,
            required: true,
        },
    }, schemaOptions

);
const ClinicalAssesmentVersion = mongoose.model<IClinicalAssesmentVersion>("ClinicalAssesmentVersion", ClinicalAssesmentVersionSchema);

export default ClinicalAssesmentVersion;
