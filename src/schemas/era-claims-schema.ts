import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IERAClaimMdSchema } from "../models/era-claim-model";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const ERAClaimMdSchema = new mongoose.Schema(
    {
        prov_taxid: {
            type: Schema.Types.Number,
            required: false,
        },
        payer_name: {
            type: Schema.Types.String,
            required: false,
        },
        received_time: {
            type: Schema.Types.String,
            required: false,
        },
        check_number: {
            type: Schema.Types.String,
            required: false,
        },
        eraid: {
            type: Schema.Types.Number,
            required: false,
        },
        paid_date: {
            type: Schema.Types.String,
            required: false,
        },
        paid_amount: {
            type: Schema.Types.String,
            required: false,
        },
        payerid: {
            type: Schema.Types.String,
            required: false,
        },
        prov_name: {
            type: Schema.Types.String,
            required: false,
        },
        claimmd_prov_name: {
            type: Schema.Types.String,
            required: false,
        },
        prov_npi: {
            type: Schema.Types.String,
            required: false,
        },
        status: {
            type: Schema.Types.String,
            required: false,
        },
    },
    schemaOptions
);

const ERAClaimMd = mongoose.model<IERAClaimMdSchema>("ERAClaim", ERAClaimMdSchema);

export default ERAClaimMd;
