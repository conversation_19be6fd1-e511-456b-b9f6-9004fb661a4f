import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IDeletedFriendRequest } from "../models/deleted-friend-request-model";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const DeletedFriendRequestSchema = new mongoose.Schema(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    },
    unfriendedBy: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    }
  },
  schemaOptions
);

const DeletedFriendRequest = mongoose.model<IDeletedFriendRequest>("DeletedFriendRequest", DeletedFriendRequestSchema);

export default DeletedFriendRequest;
