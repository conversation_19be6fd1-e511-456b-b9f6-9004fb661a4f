import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ISaasTherapistTask } from "../models/saas-therapist-task-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const SaasTherapistTaskSchema = new mongoose.Schema(
  {
    therapistId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    taskTitle: {
      type: Schema.Types.String,
      required: true,
    },
    taskDetail: {
      type: Schema.Types.String,
      required: false,
    },
    taskStatus: {
      type: Schema.Types.String,
      required: false,
    },
  },
  schemaOptions
);

const SaasTherapistTask = mongoose.model<ISaasTherapistTask>("SaasTherapistTask", SaasTherapistTaskSchema);

export default SaasTherapistTask;
