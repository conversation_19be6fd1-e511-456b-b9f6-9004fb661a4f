import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { CarolinaCompleteHealthPrescribingProviderSchema } from "./sub-schemas/CarolinaCompleteHealthSubSchemas/carolina-complete-health-prescribing-provider-schema";
import { CarolinaCompleteGeneralInformationSchema } from "./sub-schemas/CarolinaCompleteHealthSubSchemas/carolina-complete-health-general-information-schema";
import { CarolinaCompleteHealthServiceInformationSchema } from "./sub-schemas/CarolinaCompleteHealthSubSchemas/caroline-complete-health-service-information-schema";
import { CarolinaCompleteHealthProviderSchema } from "./sub-schemas/CarolinaCompleteHealthSubSchemas/carolina-complete-health-provider-schema";
import AuthorizationForm from "./authorization-form-schema";
import { AuthFormType, IAuthorizationForm } from "../models/authorization-form-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const CarolinaCompleteHealthAuthFormSchema = new mongoose.Schema(
  {
    generalInformation: {
      type: CarolinaCompleteGeneralInformationSchema,
      required: true,
    },
    serviceInformation: {
      type: CarolinaCompleteHealthServiceInformationSchema,
      required: true,
    },
    providerInformation: {
      type: CarolinaCompleteHealthProviderSchema,
      required: true,
    },
    prescibingProviderInformation: {
      type: CarolinaCompleteHealthPrescribingProviderSchema,
      required: true,
    },
    denialReason: {
      type: Schema.Types.String,
      required: false,
    },
    date: {
      type: Schema.Types.Date,
      required: false,
    },
    signature: {
      type: Schema.Types.Buffer,
      required: false,
    },
  },
  schemaOptions
);

const CarolinaCompleteHealthAuthForm = AuthorizationForm.discriminator<IAuthorizationForm>(
  "CarolinaCompleteHealthAuthForm",
  CarolinaCompleteHealthAuthFormSchema,
  AuthFormType.CarolinaCompleteHealthAuthForm
);

export default CarolinaCompleteHealthAuthForm;
