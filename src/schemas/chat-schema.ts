import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IChatData } from "../models/chat-model";
import Message from "./message-schema";
import Upload from "./upload-schema";

import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

const UnreadMessageSchema = new mongoose.Schema({
  unreadUserId: { type: Schema.Types.ObjectId, ref: User.modelName, },
  msgCount: { type: Schema.Types.Number },
  lastMessage: { type: Schema.Types.ObjectId, ref: "Message", }
});

export const ChatSchema = new mongoose.Schema(
  {
    members: [
      {
        type: Schema.Types.ObjectId,
        ref: User.modelName,
      },
    ],
    lastActiveTime: {
      type: Schema.Types.Date,
    },
    isActive: { type: Schema.Types.Boolean, default: true },
    unreadSMS: { type: Schema.Types.Boolean, default: false },
    unreadMessage: {
      type: UnreadMessageSchema,
    },
  },

  schemaOptions
);

const Chat = mongoose.model<IChatData>("Chat", ChatSchema);
export default Chat;
