import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IInsuranceCompany } from "../models/Insurance-company-model";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};
export const InsuranceCompanySchema = new mongoose.Schema({
    insuranceCompany: {
        type: Schema.Types.String,
        require: true,

    },
    contractPrice: {
        type: Schema.Types.Number,
        require: false
    },
    coPayment: {
        type: Schema.Types.Number,
        require: false
    },
    states: [
        {
            type: Schema.Types.String,
            required: false,
            default: []
        }
    ],
    fax: {
        type: Schema.Types.String,
        require: false
    },
    tradingPartnerServiceId: {
        type: Schema.Types.String,
        require: false,
    },
    organizationName: {
        type: Schema.Types.String,
        require: false,
    },
    payerName: {
        type: Schema.Types.String,
        require: false,
    },
    link: {
        type: Schema.Types.String,
        require: false,
    },
    isCommercialInsurance: {
        type: Schema.Types.Boolean,
        default: false,
        require: false,
    },
    isMedicaid: {
        type: Schema.Types.Boolean,
        default: false,
        require: false,
    },
}, schemaOptions);

const InsuranceCompany = mongoose.model<IInsuranceCompany>('InsuranceCompany', InsuranceCompanySchema);

export default InsuranceCompany;