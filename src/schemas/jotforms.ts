import * as mongoose from "mongoose";

export interface IJotform extends mongoose.Document {
  title_form: string;
  status: string;
  form_id: string;
}

const JotformSchema = new mongoose.Schema(
  {
    title_form: { type: String, required: true },
    status: { type: String, required: true },
    form_id: { type: String, required: true, unique: true }
  },
  {
    timestamps: true,
  }
);

export default mongoose.model<IJotform>("Jotform", JotformSchema);
