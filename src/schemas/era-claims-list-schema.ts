import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IERAClaimMdListSchema } from "../models/era-claim-list-model";
import { ERACHargeList } from "./sub-schemas/claim-era";
import ERAClaimMd from "./era-claims-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const IERAClaimMdListsSchema = new mongoose.Schema(
    {
        pcn: {
            type: Schema.Types.String,
            required: false,
        },
        pat_name_f: {
            type: Schema.Types.String,
            required: false,
        },
        pat_name_l: {
            type: Schema.Types.String,
            required: false,
        },
        from_dos: {
            type: Schema.Types.String,
            required: false,
        },
        claim_received_date: {
            type: Schema.Types.String,
            required: false,
        },
        total_paid: {
            type: Schema.Types.String,
            required: false,
        },
        paid_amount: {
            type: Schema.Types.String,
            required: false,
        },
        status_code: {
            type: Schema.Types.String,
            required: false,
        },
        ins_number: {
            type: Schema.Types.String,
            required: false,
        },
        total_charge: {
            type: Schema.Types.String,
            required: false,
        },
        charge: [
            {
              type: ERACHargeList,
            },
          ],
        status: {
            type: Schema.Types.String,
            required: false,
        },
        eraClaimId: {
            type: Schema.Types.ObjectId,
            required: false,
            ref: ERAClaimMd.modelName,
        },
    },
    schemaOptions
);

const ERAClaimMdList = mongoose.model<IERAClaimMdListSchema>("ERAClaimList", IERAClaimMdListsSchema);

export default ERAClaimMdList;
