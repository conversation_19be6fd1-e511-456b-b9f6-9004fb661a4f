import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import { IFeedback } from '../models/feedback-model';

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
      getters: true,
      virtuals: true,
    },
};

export const FeedbackSchema = new mongoose.Schema({
    userId: {
        type: Schema.Types.ObjectId,
        require: true,
        ref: User.modelName,
    },
    feedback: {
        type: Schema.Types.String,
        require: true,
    },
    isRead: {
        type: Schema.Types.Boolean,
        require: true,
    }
}, schemaOptions);

const Feedback = mongoose.model<IFeedback>("Feedback", FeedbackSchema);

export default Feedback;