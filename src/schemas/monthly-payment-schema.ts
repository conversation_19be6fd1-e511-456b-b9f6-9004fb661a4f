import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { TherapistTransaction } from "./sub-schemas/therapist-transaction-schema";
import { IMonthlyPayment } from "../models/monthly-payment-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const MonthlyPaymentSchema = new mongoose.Schema(
  {
    therapistTransaction: [
      {
        type: TherapistTransaction,
      },
    ],
    verifiedStatus: {
      type: Schema.Types.String,
      required: false
    },
    createdAt: {
      type: Schema.Types.Date,
      required: false,
    },
    crownJobType: {
      type: Schema.Types.String,
      required: false
    },
    timePeriodStartAt: {
      type: Schema.Types.Date,
      required: false,
    },
    timePeriodEndAt: {
      type: Schema.Types.Date,
      required: false,
    },
    
  },
  schemaOptions
);

const MonthlyPayment = mongoose.model<IMonthlyPayment>("MonthlyPayment", MonthlyPaymentSchema);

export default MonthlyPayment;
