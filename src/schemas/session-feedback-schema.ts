import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import Meeting from "./meeting-schema";
import { ISessionFeedBack } from "../models/session-feedback-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const SessionFeedbackSchema = new mongoose.Schema(
  {
    meetingId: {
      type: Schema.Types.String,
      require: true,
      ref: Meeting.modelName
    },
    rate: {
      type: Schema.Types.Number,
      require: true
    },
    feedback: {
      type: Schema.Types.String,
      require: false
    },
    therapistComfortable: {
      type: Schema.Types.Boolean,
      require: false
    },
    satisfied: {
      type: Schema.Types.Boolean,
      require: false
    },
    nextSessionScheduled: {
      type: Schema.Types.Boolean,
      require: false
    },
    needCall: {
      type: Schema.Types.Boolean,
      require: false
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    },
  },
  schemaOptions
);

const sessionFeedBack = mongoose.model<ISessionFeedBack>("SessionFeedback", SessionFeedbackSchema);

export default sessionFeedBack;