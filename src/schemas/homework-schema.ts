import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import Upload from "./upload-schema";
import { IHomework } from '../models/homework-model';
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
      getters: true,
      virtuals: true,
    }
};

export const HomeworkSchema = new mongoose.Schema({
    title: {
        type: Schema.Types.String,
        require: true,
    },
    description: {
        type: Schema.Types.String,
        require: true,
    },
    dueDate: {
        type: Schema.Types.String,
        require: true,
    },
    isComplete: {
        type: Schema.Types.Boolean,
        require: true,
        default: false
    },
    uploads: [
        {
          type: Schema.Types.ObjectId,
          require: true,
          ref: Upload.modelName,
        },
    ],
    createdBy: {
        type: Schema.Types.ObjectId,
        require: true,
        ref: User.modelName,
    },
    assignedFor: {
        type: Schema.Types.ObjectId,
        require: true,
        ref: User.modelName,
    },
    status: {
        type: Schema.Types.String,
        require: true,
    }
}, schemaOptions);

const Homework = mongoose.model<IHomework>('HomeWork', HomeworkSchema);

export default Homework;