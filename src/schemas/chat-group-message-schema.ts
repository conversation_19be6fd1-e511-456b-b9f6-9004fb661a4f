import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import ChatGroup from "./chat-group-schema";
import {
  ChatGroupMessageStatus,
  IChatGroupMessage,
} from "../models/chat-group-message-model";
import Upload from "./upload-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const ChatGroupMessageSchema = new mongoose.Schema(
  {
    groupId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: ChatGroup.modelName,
    },
    messageText: {
      type: Schema.Types.String,
      require: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    messageStatus: {
      type: Schema.Types.String,
      enum: ChatGroupMessageStatus,
      require: false,
    },
    preMessageId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: "ChatGroupMessage",
    },
    mediaFileId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Upload.modelName,
    },
  },

  schemaOptions
);

const ChatGroupMessage = mongoose.model<IChatGroupMessage>(
  "ChatGroupMessage",
  ChatGroupMessageSchema
);
export default ChatGroupMessage;
