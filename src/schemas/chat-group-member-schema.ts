import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import ChatGroup from "./chat-group-schema";
import {
  ChatGroupMemberType,
  IChatGroupMember,
} from "../models/chat-group-member-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const ChatGroupMemberSchema = new mongoose.Schema(
  {
    groupId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: ChatGroup.modelName,
    },
    userId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    role: {
      type: Schema.Types.String,
      enum: ChatGroupMemberType,
      require: true,
    },
    lastActive: {
      type: Schema.Types.Date,
      require: true,
    },
    is_main: {
      type: Schema.Types.Boolean,
      default: false,
    },
  },

  schemaOptions
);

const ChatGroupMember = mongoose.model<IChatGroupMember>(
  "ChatGroupMember",
  ChatGroupMemberSchema
);
export default ChatGroupMember;
