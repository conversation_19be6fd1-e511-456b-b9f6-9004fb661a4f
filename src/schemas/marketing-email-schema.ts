import * as mongoose from "mongoose";
import { IMarketingEmail } from "../models/marketing-email-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const MarketingEmailSchema = new mongoose.Schema(
  {
  },
  schemaOptions
);

const MarketingEmail = mongoose.model<IMarketingEmail>("MarketingEmail", MarketingEmailSchema);

export default MarketingEmail;
