import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { diagnosisSchema } from "./sub-schemas/TherapyPlanSubSchemas/diagnosis-schema";
import { GoalInformationSchema } from "./sub-schemas/TherapyPlanSubSchemas/goal-information-schema";
import { TreatmentSessionSchema } from "./sub-schemas/TherapyPlanSubSchemas/treatment-session-schema";
import { SignatureDetailsSchema } from "./sub-schemas/TherapyPlanSubSchemas/signature-details-schema";
import { ITherapyPlan } from "../models/therapy-plan-model";
import Client from "./client-schema";
import Therapist from "./therapist-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const TherapyPlanSchema = new mongoose.Schema(
    {
        therapistId: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: Therapist.modelName
        },
        clientId: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: Client.modelName
        },
        planCreationDate: {
            type: Schema.Types.Date,
            required: false,
        },
        creationDate: {
            type: Schema.Types.Date,
            required: false,
        },
        diagnosis: [{
            type: diagnosisSchema,
            required: false,
        }],
        goalDate: {
            type: Schema.Types.Date,
            required: false,
        },
        goalInformation: {
            type: GoalInformationSchema,
            required: false,
        },
        treatmentSession: [{
            type: TreatmentSessionSchema,
            required: false,
        }],
        clientSignatureDetails: {
            type: SignatureDetailsSchema,
            required: false
        },
        clientSignature: {
            type: Schema.Types.String,
            required: false,
        },
        lrpSignatureDetails: {
            type: SignatureDetailsSchema,
            required: false
        },
        lrpSignature: {
            type: Schema.Types.String,
            required: false,
        },
        clinicianSignatureDetails: {
            type: SignatureDetailsSchema,
            required: false
        },
        clinicianSignature: {
            type: Schema.Types.String,
            required: false,
        },
        goalDate2: {
            type: Schema.Types.Date,
            required: false,
        },
        goalInformation2: {
            type: GoalInformationSchema,
            required: false,
        },
        treatmentSession2: [{
            type: TreatmentSessionSchema,
            required: false,
        }],
        goalDate3: {
            type: Schema.Types.Date,
            required: false,
        },
        goalInformation3: {
            type: GoalInformationSchema,
            required: false,
        },
        treatmentSession3: [{
            type: TreatmentSessionSchema,
            required: false,
        }],
        isSignature: {
            type: Schema.Types.Boolean,
            require: false
        }
    }, schemaOptions

);
const TherapyPlan = mongoose.model<ITherapyPlan>("TherapyPlan", TherapyPlanSchema);

export default TherapyPlan;
