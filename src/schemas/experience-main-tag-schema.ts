import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import { IExperienceMainTags } from "../models/experience-main-tag-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const ExperienceMainTagSchema = new mongoose.Schema(
  {
    mainTagName: {
        type: Schema.Types.String,
        require: true,
    }
  },
  schemaOptions
);

const ExperienceMainTags = mongoose.model<IExperienceMainTags>("ExperienceMainTags", ExperienceMainTagSchema);

export default ExperienceMainTags;
