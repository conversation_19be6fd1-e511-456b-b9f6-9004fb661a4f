import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export interface IJotFormInvitation {
    _id?: mongoose.Types.ObjectId;
    clientId: mongoose.Types.ObjectId;
    therapistId: mongoose.Types.ObjectId;
    formLink: string;
    emailSent: boolean;
    sentAt: Date;
    status: string;
    invitationType?: string; // To track if invitation sent by therapist or admin
    createdAt?: Date;
    updatedAt?: Date;
}

const JotFormInvitationSchema = new mongoose.Schema(
    {
        clientId: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        therapistId: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        formLink: {
            type: Schema.Types.String,
            required: true
        },
        emailSent: {
            type: Schema.Types.Boolean,
            default: false
        },
        sentAt: {
            type: Schema.Types.Date,
            default: Date.now
        },
        status: {
            type: Schema.Types.String,
            enum: ['ACTIVE', 'INACTIVE'],
            default: 'ACTIVE'
        },
        invitationType: {
            type: Schema.Types.String,
            enum: ['THERAPIST', 'ADMIN'],
            default: 'THERAPIST'
        }
    },
    {
        timestamps: true
    }
);

const JotFormInvitation = mongoose.model<IJotFormInvitation>("JotFormInvitation", JotFormInvitationSchema, "jotforminvitation");
export default JotFormInvitation;
