import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IState } from "../models/state-model";
import Upload from "./upload-schema";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const StateSchema = new mongoose.Schema(
  {
    stateName: {
      type: Schema.Types.String,
      require: true,
    },
  },
  schemaOptions
);

const State = mongoose.model<IState>("State", StateSchema);

export default State;
