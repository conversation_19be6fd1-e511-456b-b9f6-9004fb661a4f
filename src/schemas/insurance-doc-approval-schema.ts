import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import Insurance from "./insurance-schema";
import { IInsuranceDocApproval } from "../models/insurance-doc-approval-model";
import Upload from "./upload-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const InsuranceDocApprovalSchema = new mongoose.Schema(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    },
    insuranceCompanyId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: Insurance.modelName,
    },
    therapistApprovalStatus: {
      type: Schema.Types.String,
      require: true,
    },
    adminApprovalStatus: {
      type: Schema.Types.String,
      require: false,
    },
    clinicalAssessmentUploadId: {
      type: Schema.Types.ObjectId,
      ref: Upload.modelName,
    },
    therapyPlanUploadId: {
      type: Schema.Types.ObjectId,
      ref: Upload.modelName,
    },
    authorizationFormUploadId: {
      type: Schema.Types.ObjectId,
      ref: Upload.modelName,
    },
    messageId: {
      type: Schema.Types.String,
      require: false,
    },
    messageStatus: {
      type: Schema.Types.String,
      require: false,
    },
    recipientId: {
      type: Schema.Types.String,
      require: false,
    },
    toPhoneNumber: {
      type: Schema.Types.String,
      require: false,
    },
    toMessageStatus: {
      type: Schema.Types.String,
      require: false,
    },
    fromPhoneNumber: {
      type: Schema.Types.String,
      require: false,
    },
    readStatus: {
      type: Schema.Types.String,
      require: false,
    },
    faxPageCount: {
      type: Schema.Types.Number,
      require: false,
    },
    creationTime: {
      type: Schema.Types.Date,
      require: false,
    },
    lastModifiedTime: {
      type: Schema.Types.Date,
      require: false,
    },
  },
  schemaOptions
);

const InsuranceDocApproval = mongoose.model<IInsuranceDocApproval>("InsuranceDocApproval", InsuranceDocApprovalSchema);

export default InsuranceDocApproval;
