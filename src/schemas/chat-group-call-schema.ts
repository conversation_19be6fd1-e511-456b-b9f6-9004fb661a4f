import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import ChatGroup from "./chat-group-schema";
import {
  ChatGroupCallCallingStatus,
  IChatGroupCall,
} from "../models/chat-group-call-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const ChatGroupCallSchema = new mongoose.Schema(
  {
    groupId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: ChatGroup.modelName,
    },
    start: {
      type: Schema.Types.Date,
      require: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    callingStatus: {
      type: Schema.Types.String,
      enum: ChatGroupCallCallingStatus,
      require: true,
    },
  },

  schemaOptions
);

const ChatGroupCall = mongoose.model<IChatGroupCall>(
  "ChatGroupCall",
  ChatGroupCallSchema
);
export default ChatGroupCall;
