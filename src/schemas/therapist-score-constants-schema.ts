import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import { ITherapistScoreConstants } from "../models/therapist-score-constants-model";
import Meeting from "./meeting-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const TherapistScoreConstantsSchema = new mongoose.Schema(
  {
    responseTimeWeight: {
      type: Schema.Types.Number,
      required: false
    },
    availabilityWeight: {
      type: Schema.Types.Number,
      required: false
    },
    followUpAppointmentsWeight: {
      type: Schema.Types.Number,
      required: false
    },
    totalSessionsWeight: {
      type: Schema.Types.Number,
      required: false
    },
    missedAppointmentsWeight: {
      type: Schema.Types.Number,
      required: false
    },
    loyalityYearsWeight: {
      type: Schema.Types.Number,
      required: false
    },
    scheduledAppointmentsWeight: {
      type: Schema.Types.Number,
      required: false
    },
    noOfMatchesWeight: {
      type: Schema.Types.Number,
      required: false
    }
  },
  schemaOptions
);

const TherapistScoreConstants = mongoose.model<ITherapistScoreConstants>("TherapistScoreConstants", TherapistScoreConstantsSchema);

export default TherapistScoreConstants;
