import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import Upload from "./upload-schema";
import User from "./user-schema";
import { IInsurance } from "../models/insurance-model";
import { ReceiverSchema } from "./sub-schemas/receiver-schema";
import { SubscriberSchema } from "./sub-schemas/subceiber-schema";
import { DependentSchema } from "./sub-schemas/dependent-schema";
import InsuranceCompany from "./Insurance-company-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const InsuranceSchema = new mongoose.Schema(
  {
    subscriber: {
      type: SubscriberSchema,
    },
    dependent: {
      type: DependentSchema,
    },
    insuranceCardId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Upload.modelName,
    },
    insuranceCardBackId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Upload.modelName,
    },
    clientId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: User.modelName,
    },
    insuranceCompanyId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: InsuranceCompany.modelName,
    },
    insuranceAccessToken: {
      type: Schema.Types.String,
      required: false,
    },
    controlNumber: {
      type: Schema.Types.String,
      required: false,
    },
  },
  schemaOptions
);

const Insurance = mongoose.model<IInsurance>("Insurance", InsuranceSchema);

export default Insurance;
