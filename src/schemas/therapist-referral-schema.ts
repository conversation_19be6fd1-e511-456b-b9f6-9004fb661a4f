import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ITherapistReferral } from "../models/therapist-referral-model";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
      getters: true,
      virtuals: true,
    },
};

export const TherapistReferralSchema = new mongoose.Schema(
    {
        referrerUserId: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: User.modelName
        },
        referredUserId: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: User.modelName
        },
        firstSessionCompletionBonusForReferrer: {
            type: Schema.Types.ObjectId,
            required: false,
        },
        twentySessionsCompletionBonusForReferrer: {
            type: Schema.Types.ObjectId,
            required: false,
        },
        createdAt: {
            type: Schema.Types.Date,
            required: false
        },
        updatedAt: {
            type: Schema.Types.Date,
            required: false
        },
    },
    schemaOptions
);

const TherapistReferral = mongoose.model<ITherapistReferral>("TherapistReferral", TherapistReferralSchema);

export default TherapistReferral;
