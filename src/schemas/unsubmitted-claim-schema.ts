import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IUnsubmittedClaimMd } from "../models/unsubmitted-claim-model";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const UnsubmittedClaimMdSchema = new mongoose.Schema(
    {
        claim_form: {
            type: Schema.Types.Number,
            required: false,
        },
        payer_name: {
            type: Schema.Types.String,
            required: false,
        },
        accept_assign: {
            type: Schema.Types.String,
            required: false,
        },
        employment_related: {
            type: Schema.Types.String,
            required: false,
        },
        ins_name_f: {
            type: Schema.Types.String,
            required: false,
        },
        ins_name_l: {
            type: Schema.Types.String,
            required: false,
        },
        ins_addr_1: {
            type: Schema.Types.String,
            required: false,
        },
        ins_city: {
            type: Schema.Types.String,
            required: false,
        },
        ins_dob: {
            type: Schema.Types.String,
            required: false,
        },
        ins_sex: {
            type: Schema.Types.String,
            required: false,
        },
        ins_state: {
            type: Schema.Types.String,
            required: false,
        },
        ins_zip: {
            type: Schema.Types.String,
            required: false,
        },
        ins_number: {
            type: Schema.Types.String,
            required: false,
        },
        bill_taxonomy: {
            type: Schema.Types.String,
            required: false,
        },
        place_of_service_1: {
            type: Schema.Types.String,
            required: false,
        },
        prov_name_l: {
            type: Schema.Types.String,
            required: false,
        },
        prov_name_f: {
            type: Schema.Types.String,
            required: false,
        },
        prov_npi: {
            type: Schema.Types.String,
            required: false,
        },
        prov_taxonomy: {
            type: Schema.Types.String,
            required: false,
        },
        pcn: {
            type: Schema.Types.String,
            required: false,
        },
        charge_1: {
            type: Schema.Types.Number,
            required: false,
        },
        pat_rel: {
            type: Schema.Types.String,
            required: false,
        },
        payerid: {
            type: Schema.Types.String,
            required: false,
        },
        total_charge: {
            type: Schema.Types.Number,
            required: false,
        },
        claimNumber: {
            type: Schema.Types.String,
            required: false,
        },
        proc_code_1: {
            type: Schema.Types.String,
            required: false,
        },
        mod1_1: {
            type: Schema.Types.String,
            required: false,
        },
        diag_1: {
            type: Schema.Types.String,
            required: false,
        },
        diag_2: {
            type: Schema.Types.String,
            required: false,
        },
        from_date_1: {
            type: Schema.Types.String,
            required: false,
        },
        bill_name: {
            type: Schema.Types.String,
            required: false,
        },
        bill_addr_1: {
            type: Schema.Types.String,
            required: false,
        },
        bill_addr_2: {
            type: Schema.Types.String,
            required: false,
        },
        bill_city: {
            type: Schema.Types.String,
            required: false,
        },
        bill_state: {
            type: Schema.Types.String,
            required: false,
        },
        bill_zip: {
            type: Schema.Types.String,
            required: false,
        },
        bill_npi: {
            type: Schema.Types.String,
            required: false,
        },
        bill_id: {
            type: Schema.Types.String,
            required: false,
        },
        bill_phone: {
            type: Schema.Types.String,
            required: false,
        },
        bill_taxid: {
            type: Schema.Types.String,
            required: false,
        },
        bill_taxid_type: {
            type: Schema.Types.String,
            required: false,
        },
        diag_ref_1: {
            type: Schema.Types.String,
            required: false,
        },
        units_1: {
            type: Schema.Types.Number,
            required: false,
        },
        pat_name_l: {
            type: Schema.Types.String,
            required: false,
        },
        pat_name_f: {
            type: Schema.Types.String,
            required: false,
        },
        pat_addr_1: {
            type: Schema.Types.String,
            required: false,
        },
        pat_city: {
            type: Schema.Types.String,
            required: false,
        },
        pat_state: {
            type: Schema.Types.String,
            required: false,
        },
        pat_zip: {
            type: Schema.Types.String,
            required: false,
        },
        pat_dob: {
            type: Schema.Types.String,
            required: false,
        },
        pat_sex: {
            type: Schema.Types.String,
            required: false,
        },
        status: {
            type: Schema.Types.String,
            required: false,
        },
        treatmentHistoryId: {
            type: Schema.Types.ObjectId,
            required: false,
        },
    },
    schemaOptions
);

const UnsubmittedClaimMd = mongoose.model<IUnsubmittedClaimMd>("UnsubmittedClaimMd", UnsubmittedClaimMdSchema);

export default UnsubmittedClaimMd;
