import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IMedicalPhrase } from "../models/medical-phrases";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const MedicalPhraseSchema = new mongoose.Schema(
  {
    value: {
      type: Schema.Types.String,
      required: true,
    },
    priority: {
      type: Schema.Types.String,
      required: true,
    },
    stage: {
      type: Schema.Types.String,
      required: true,
    },
    tabType: {
      type: Schema.Types.String,
      required: true,
    },
    systemGenerated: {
      type: Schema.Types.Boolean,
      required: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: false,
    },
  },
  schemaOptions
);

const MedicalPhrase = mongoose.model<IMedicalPhrase>(
  "MedicalPhrase",
  MedicalPhraseSchema
);

export default MedicalPhrase;
