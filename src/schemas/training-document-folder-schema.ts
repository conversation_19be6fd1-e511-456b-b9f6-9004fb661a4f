import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ITrainingDocumentFolder, DTrainingDocumentFolder} from "../models/training-document-folder-model";
import Upload from "./upload-schema";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
      getters: true,
      virtuals: true,
    }
};


export const TrainingDocumentFolderSchema = new mongoose.Schema({
    folderName: {
        type: Schema.Types.String,
        required: true,
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: User.modelName,
    },
    parentFolderId: {
        type: Schema.Types.ObjectId,
        default: null,
        ref: 'TrainingDocumentsFolder',
    }
}, schemaOptions);

const TrainigDocFolder = mongoose.model<ITrainingDocumentFolder>('TrainingDocumentsFolder', TrainingDocumentFolderSchema);

export default TrainigDocFolder;