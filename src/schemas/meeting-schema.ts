import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import { IMeeting } from "../models/meeting-model";
import Transcribe from "./transcribe-schema";
import Upload from "./upload-schema";
import { copaymentSchema } from "./sub-schemas/copayment-schema";
import { insuranceClaimSchema } from "./sub-schemas/insuranceClaim-schema";
import ChatGroupCall from "./chat-group-call-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const MeetingSchema = new mongoose.Schema(
  {
    meetingId: {
      type: Schema.Types.String,
      require: true,
    },
    meetingType: {
      type: Schema.Types.String,
      require: true,
    },
    meetingDuration: {
      type: Schema.Types.Number,
      require: false,
    },
    spentDuration: {
      type: Schema.Types.Number,
      require: false,
    },
    clientId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: User.modelName,
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: User.modelName,
    },
    transcribeId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: Transcribe.modelName,
    },
    appointmentId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: Transcribe.modelName,
    },
    transcribeAllowed: {
      type: Schema.Types.Boolean,
      require: true,
      default: false,
    },
    transcribingInProcess: {
      type: Schema.Types.Boolean,
      require: true,
      default: true,
    },
    isAppointmentBased: {
      type: Schema.Types.Boolean,
      require: true,
      default: false,
    },
    transcribeCreated: {
      type: Schema.Types.Boolean,
      require: true,
      default: false,
    },
    accepted: {
      type: Schema.Types.Boolean,
      require: true,
      default: true,
    },
    noOfVideose: {
      type: Schema.Types.Number,
      require: true,
      default: 0,
    },
    videoUrls: [
      {
        type: Schema.Types.String,
        require: true,
        default: [],
      },
    ],
    speakerHistory: [
      {
        type: Schema.Types.String,
        require: true,
        default: [],
      },
    ],
    audioFiles: [
      {
        type: Schema.Types.ObjectId,
        require: true,
        default: [],
        ref: Upload.modelName,
      },
    ],
    recordingAllowed: {
      type: Schema.Types.Boolean,
      require: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: User.modelName,
    },
    callingStatus: {
      type: Schema.Types.String,
      require: true,
    },
    recordingSharedWithClient: {
      type: Schema.Types.Boolean,
      require: true,
    },
    transcriptTextInProcess: {
      type: Schema.Types.Boolean,
      require: false,
      default: false,
    },
    transcriptTextCreated: {
      type: Schema.Types.Boolean,
      require: false,
      default: false,
    },
    isInvalidMeetingTime: {
      type: Schema.Types.Boolean,
      require: true,
    },
    invalidDuration: {
      type: Schema.Types.Number,
      require: false,
    },
    clientIdentifier: {
      type: Schema.Types.String,
      require: true,
    },
    therapistIdentifier: {
      type: Schema.Types.String,
      require: true,
    },
    password: {
      type: Schema.Types.String,
      require: true,
    },
    bothJoinedAt: {
      type: Schema.Types.Date,
      require: false,
    },
    isAudioCall: {
      type: Schema.Types.Boolean,
      require: false,
    },
    sessionAmount: {
      type: Schema.Types.Number,
      required: false,
    },
    copayment: {
      type: copaymentSchema,
    },
    insuranceClaim: {
      type: insuranceClaimSchema,
    },
    hasTranscribes: {
      type: Schema.Types.Boolean,
      require: false,
      default: false,
    },
    therapistAllowedTranscribe: {
      type: Schema.Types.Boolean,
      require: false,
      default: false,
    },
    clientAllowedTranscribe: {
      type: Schema.Types.Boolean,
      require: false,
      default: false,
    },
    firstSpeaker: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: User.modelName,
    },
    vonageSessionName: {
      type: Schema.Types.String,
      require: false,
    },
    vonageArchiveId: {
      type: Schema.Types.String,
      require: false,
    },
    isGroupCall: {
      type: Schema.Types.Boolean,
      require: false,
    },
    chatGroupCallId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: ChatGroupCall.modelName,
    },
    participantCount: {
      type: Schema.Types.Number,
      require: false,
    },
    regularMeetingStartTime: {
      type: Schema.Types.String,
      require: false,
    },
    regularMeetingDate: {
      type: Schema.Types.Date,
      require: false,
    },
    s3AudioPath: {
      type: Schema.Types.String,
      require: false,
    },
    isVonageNativeSDKCall: {
      type: Schema.Types.Boolean,
      require: false,
    }
  },
  schemaOptions
);

const Meeting = mongoose.model<IMeeting>("Meeting", MeetingSchema);

export default Meeting;
