import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IAdminStatistics } from "../models/admin-statistics-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const AdminStatisticsSchema = new mongoose.Schema(
  {
    yearlyRecurringRevenueCount: {
      type: Schema.Types.String,
      require: true
    },
    monthlyRecurringRevenueCount: {
      type: Schema.Types.String,
      require: true
    },
    lifeTimeSales: {
      type: Schema.Types.String,
      require: true
    },
    averageCustomerLifetimeValue: {
      type: Schema.Types.String,
      require: true
    }
  },
  schemaOptions
);

const AdminStatistics = mongoose.model<IAdminStatistics>("AdminStatistics", AdminStatisticsSchema);

export default AdminStatistics;