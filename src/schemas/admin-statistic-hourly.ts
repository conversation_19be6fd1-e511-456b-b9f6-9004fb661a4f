import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IAdminStatisticsHourly } from "../models/admin-statistics-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const AdminStatisticsHourlySchema = new mongoose.Schema(
  {
    type: {
      type: Schema.Types.String,
      require: false
    },
    pastMissedAppointmentsCount: {
      type: Schema.Types.Number,
      require: false,
        default: 0
    },
    scheduledAppointmentsCount: {
      type: Schema.Types.Number,
      require: false,
        default: 0
    },
    completedSessions: {
      type: Schema.Types.Number,
      require: false,
        default: 0
    },
    yearlyRecurringRevenueCount: {
      type: Schema.Types.String,
      require: false,
        default: 0
    },
    monthlyRecurringRevenueCount: {
      type: Schema.Types.String,
      require: false,
        default: 0
    },
    lifeTimeSales: {
      type: Schema.Types.String,
      require: false,
        default: 0
    },
    averageCustomerLifetimeValue: {
      type: Schema.Types.String,
      require: false,
        default: 0
    }
  },
  schemaOptions
);

const AdminStatisticsHourly = mongoose.model<IAdminStatisticsHourly>("AdminStatisticsHourly", AdminStatisticsHourlySchema);

export default AdminStatisticsHourly;