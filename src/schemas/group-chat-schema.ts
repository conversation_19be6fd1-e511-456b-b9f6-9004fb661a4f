import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IGroupChat } from "../models/group-chat-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const GroupChatSchema = new mongoose.Schema(
  {
    title: {
      type: Schema.Types.String,
      require: true,
    },
    groupIconId: {
      type: Schema.Types.ObjectId,
      require: false,
    },
    // groupType: {
    //   type: Schema.Types.String,
    //   require: true,
    // },
  },
  schemaOptions
);

const GroupChat = mongoose.model<IGroupChat>("GroupChat", GroupChatSchema);

export default GroupChat;
