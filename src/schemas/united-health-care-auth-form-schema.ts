import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { UnitedHealthCareMemberInformationSchema } from "./sub-schemas/UnitedHealthCareAuthFormSubschemas/united-health-care-member-information-schema";
import { UnitedHealthCarePrescriberInformationSchema } from "./sub-schemas/UnitedHealthCareAuthFormSubschemas/united-health-care-prescriber-information-schema";
import { UnitedHealthCareMedicationInformationSchema } from "./sub-schemas/UnitedHealthCareAuthFormSubschemas/united-health-care-medication-information-schema";
import { UnitedHealthCareClinicalInformationSchema } from "./sub-schemas/UnitedHealthCareAuthFormSubschemas/united-health-care-clinical-information-schema";
import AuthorizationForm from "./authorization-form-schema";
import { AuthFormType, IAuthorizationForm } from "../models/authorization-form-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const UnitedHealthCareAuthFormSchema = new mongoose.Schema(
  {
    memberInformation: {
      type: UnitedHealthCareMemberInformationSchema,
      required: true,
    },
    prescriberInformation: {
      type: UnitedHealthCarePrescriberInformationSchema,
      required: true,
    },
    medicationInformation: {
      type: UnitedHealthCareMedicationInformationSchema,
      required: true,
    },
    clinicalInformation: {
      type: UnitedHealthCareClinicalInformationSchema,
      required: true,
    },
    additionalInformation: {
      type: Schema.Types.String,
      required: false,
    },
    date: {
      type: Schema.Types.Date,
      required: false,
    },
    isSignature: {
      type: Schema.Types.Boolean,
      required: false,
    },
  },
  schemaOptions
);

const UnitedHealthCareAuthForm = AuthorizationForm.discriminator<IAuthorizationForm>(
  "UnitedHealthCareAuthForm",
  UnitedHealthCareAuthFormSchema,
  AuthFormType.UnitedHealthCareAuthForm
);

export default UnitedHealthCareAuthForm;
