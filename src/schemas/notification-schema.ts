import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { INotification } from "../models/notification-model";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    }
};

export const NotificationSchema = new mongoose.Schema({
    senderId: {
        type: Schema.Types.ObjectId,
        require: true,
        ref: User.modelName
    },
    receiverId: {
        type: Schema.Types.ObjectId,
        require: true,
        ref: User.modelName
    },
    event: {
        type: Schema.Types.String,
        require: true
    },
    link: {
        type: Schema.Types.String,
        require: true
    },
    content: {
        type: Schema.Types.String,
        require: true
    },
    variant: {
        type: Schema.Types.String,
        require: false
    },
    readStatus: {
        type: Schema.Types.Boolean,
        require: true,
        default: false
    },
    twilioMessageReadStatus: {
        type: Schema.Types.Boolean,
        require: false
    }
}, schemaOptions);

const Notification = mongoose.model<INotification>("Notification", NotificationSchema);

export default Notification;