import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IHashtag } from "../models/hashtag-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  }
};

export const HashtagSchema = new mongoose.Schema(
  {
    name: {
      type: Schema.Types.String,
      require: true,
    },
  },
  schemaOptions
);

const Hashtag = mongoose.model<IHashtag>("Hashtag", HashtagSchema);

export default Hashtag;