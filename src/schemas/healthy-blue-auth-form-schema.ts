import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { HealthyBlueGeneralInformationSchema } from "./sub-schemas/HealthyBlueAuthFormSubSchemas/healthy-blue-general-information-schema";
import { HealthyBlueServiceInformationSchema } from "./sub-schemas/HealthyBlueAuthFormSubSchemas/healthy-blue-service-information-schema";
import { HealthyBlueProviderSchema } from "./sub-schemas/HealthyBlueAuthFormSubSchemas/healthy-blue-provider-schema";
import { HealthyBluePractitionerSchema } from "./sub-schemas/HealthyBlueAuthFormSubSchemas/healthy-blue-practitioner-schema";
import { HealthyBlueAuthorizationSchema } from "./sub-schemas/HealthyBlueAuthFormSubSchemas/healthy-blue-authorization-schema";
import AuthorizationForm from "./authorization-form-schema";
import { AuthFormType, IAuthorizationForm } from "../models/authorization-form-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const HealthyBlueAuthFormSchema = new mongoose.Schema(
  {
    generalInformation: {
      type: HealthyBlueGeneralInformationSchema,
      required: true,
    },
    serviceInformation: {
      type: HealthyBlueServiceInformationSchema,
      required: true,
    },
    providerInformation: {
      type: HealthyBlueProviderSchema,
      required: true,
    },
    practitionerInformation: {
      type: HealthyBluePractitionerSchema,
      required: true,
    },
    authorization: {
      type: HealthyBlueAuthorizationSchema,
      required: true,
    },
    referenceNumber: {
      type: Schema.Types.String,
      required: false,
    },
    signature: {
      type: Schema.Types.String,
      required: false,
    },
  },
  schemaOptions
);

const HealthyBlueAuthForm = AuthorizationForm.discriminator<IAuthorizationForm>(
  "HealthyBlueAuthForm",
  HealthyBlueAuthFormSchema,
  AuthFormType.HealthyBlueAuthForm
);

export default HealthyBlueAuthForm;
