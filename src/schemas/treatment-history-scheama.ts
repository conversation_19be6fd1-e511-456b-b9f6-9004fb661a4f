import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ITreatmentHistory } from "../models/treatment-history-model";
import Client from "./client-schema";
import DiagnosisNote from "./diagnosis-note-schema";
import Meeting from "./meeting-schema";
import Therapist from "./therapist-schema";
import Transcribe from "./transcribe-schema";


const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const TreatmentHistorySchema = new mongoose.Schema(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: Client.modelName,
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: Therapist.modelName,
    },
    note: {
      type: Schema.Types.String,
      required: true,
    },
    claimStatus: {
      type: Schema.Types.String,
      required: false,
    },
    meetingStartedTime: {
      type: Schema.Types.Date,
      required: false,
    },
    isMeetingTranscribe: {
      type: Schema.Types.Boolean,
      required: false,
    },
    meetingId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Meeting.modelName,
    },
    mergedMeetings: [{
      type: Schema.Types.ObjectId,
      required: false,
      ref: Meeting.modelName
    }],
    diagnosisNoteId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: DiagnosisNote.modelName,
    },
    transcribeId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Transcribe.modelName,
    },
    errorMsg: {
      type: Schema.Types.String,
      required: false,
    },
    deleteTreatmentHistory: {
      type: Schema.Types.Boolean,
      required: false
    },
    cronjobCount: {
      type: Schema.Types.Number,
      required: false,
    },
    flag: {
      type: Schema.Types.Boolean,
      required: false
    },
    claimSubmittedTime: {
      type: Schema.Types.Date,
      required: false,
    },
    subTherapistId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: Therapist.modelName,
    },
    claimSubmittedUser: {
      type: Schema.Types.String,
      required: false,
    },
    pcn: {
      type: Schema.Types.String,
      required: false,
    },
    paidAmount: {
      type: Schema.Types.String,
      required: false,
    },
  },
  schemaOptions
);

const TreatmentHistory = mongoose.model<ITreatmentHistory>(
  "TreatmentHistory",
  TreatmentHistorySchema
);

export default TreatmentHistory;
