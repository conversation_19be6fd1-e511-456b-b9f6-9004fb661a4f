import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ILavniReview } from "../models/lavni-review-model";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const LavniReviewSchema = new mongoose.Schema({
    name: {
        type: Schema.Types.String,
        require : true,
    },
    email: {
        type: Schema.Types.String,
        require : true,
    },
    reviewMessage: {
        type: Schema.Types.String,
        require : true
    },
    ratingValue: {
        type: Schema.Types.String,
        require : true
    },
    status: {
        type: Schema.Types.String,
        require: true
    },
    clientId: {
        type: Schema.Types.ObjectId,
        require: false
    },
    therapistId: {
        type: Schema.Types.ObjectId,
        require: false
    },
    adminId: {
        type: Schema.Types.ObjectId,
        require: false
    }
}, schemaOptions);

const LavniReview = mongoose.model<ILavniReview>('LavniReview', LavniReviewSchema);

export default LavniReview;