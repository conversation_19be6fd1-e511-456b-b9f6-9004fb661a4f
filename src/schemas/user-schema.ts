import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import * as bcrypt from 'bcryptjs';
import { IUser, Permission } from "../models/user-model";
import { checkPermission } from "../middleware/verify-permission";
import Upload from "./upload-schema";
import Ethnicity from "./ethnicity-schema";
import { PaymentSchema } from "./sub-schemas/payment-schema";
import { reminderTypeSchema } from "./sub-schemas/reminderType-schema";
import { reminderTimeSchema } from "./sub-schemas/reminderTIme-schema";
import { adminPermissionSchema } from "./sub-schemas/admin-permission-schema";
import FriendRequest from "./friend-request-schema";

const jwt = require('jsonwebtoken');

export const UserSchemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    discriminatorKey: 'role',
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const UserSchema = new mongoose.Schema({
    firstname: {
        type: Schema.Types.String,
        require: true
    },
    lastname: {
        type: Schema.Types.String,
        require: true
    },
    middleInitials: {
        type: Schema.Types.String,
        require: true
    },
    gender: {
        type: Schema.Types.String,
        require: true
    },
    dateOfBirth: {
        type: Schema.Types.Date,
        require: true
    },
    ethnicityId: {
        type: Schema.Types.ObjectId,
        required: false,
        ref: Ethnicity.modelName
    },
    email: {
        type: Schema.Types.String,
        unique: true,
        required: false
    },
    secondaryEmail: {
        type: Schema.Types.String,
        required: false
    },
    facebookId: {
        type: Schema.Types.String,
        //unique: true,
        required: false
    },
    googleId: {
        type: Schema.Types.String,
        //unique: true,
        required: false
    },
    password: {
        type: Schema.Types.String,
        required: false
    },
    role: {
        type: Schema.Types.String,
        required: true
    },
    medium: {
        type: Schema.Types.String,
        required: true
    },
    verifiedStatus: {
        type: Schema.Types.String,
        required: false
    },
    lastLogin: {
        type: Schema.Types.Date,
        required: false
    },
    verificationCode: {
        type: Schema.Types.String,
        required: false
    },
    photoId: {
        type: Schema.Types.ObjectId,
        required: false,
        ref: Upload.modelName
    },
    coverPhotoId: {
        type: Schema.Types.ObjectId,
        required: false,
        ref: Upload.modelName
    },
    description: {
        type: Schema.Types.String,
        required: false
    },
    blockedUser: [{
        type: Schema.Types.ObjectId,
        required: false
    }],
    paymentDetails: [{
        type: PaymentSchema
    }],
    friendRequests: [{
        type: Schema.Types.ObjectId,
        required: false
    }],
    socketId: {
        type: Schema.Types.String,
        required: false
    },
    adminApproved: {
        type: Schema.Types.Boolean,
        required: false
    },
    username: {
        type: Schema.Types.String,
        trim: true,
        index: {
            partialFilterExpression: { username: { $type: "string" } }
        }
    },
    guideComplete: {
        type: Schema.Types.Boolean,
        required: false,
        default: false
    },
    defaultAvatarId: {
        type: Schema.Types.String,
        required: false
    },
    avatarId: {
        type: Schema.Types.ObjectId,
        required: false,
        ref: Upload.modelName
    },
    avatarBackgroundId: {
        type: Schema.Types.String,
        required: false
    },
    useDefaultAvatar: {
        type: Schema.Types.Boolean,
        required: false
    },
    blockedByAdmin: {
        type: Schema.Types.Boolean,
        required: false
    },
    streetAddress: {
        type: Schema.Types.String,
        required: false
    },
    zipCode: {
        type: Schema.Types.String,
        required: false
    },
    state: {
        type: Schema.Types.String,
        required: false
    },
    primaryPhone: {
        type: Schema.Types.String,
        required: false
    },
    lavniTestAccount: {
        type: Schema.Types.Boolean,
        required: false
    },
    callRecordingAllowed: {
        type: Schema.Types.Boolean,
        required: false,
    },
    hideCallTimer: {
        type: Schema.Types.Boolean,
        required: false,
    },
    reminderType: {
        type: reminderTypeSchema
    },
    reminderTime: {
        type: reminderTimeSchema
    },
    smsStop: {
        type: Schema.Types.Boolean,
        required: false
    },
    avatarImage: {
        type: Schema.Types.String,
        required: false
    },
    message: {
        type: Schema.Types.String,
        required: false
    },
    pinNumber: {
        type: Schema.Types.String,
        required: false
    },
    reasonToBlock: {
        type: Schema.Types.String,
        required: false
    },
    priorityNumber: {
        type: Schema.Types.Number,
        required: false
    },
    blackTherapyPriorityNumber: {
        type: Schema.Types.Number,
        required: false
    },
    relationshipTherapyPriorityNumber: {
        type: Schema.Types.Number,
        required: false
    },
    mensMentalTherapyPriorityNumber: {
        type: Schema.Types.Number,
        required: false
    },
    adminCreated: {
        type: Schema.Types.Boolean,
        required: false
    },
    reminderSMS: {
        type: Schema.Types.String,
        required: false
    },
    missed1stAppointment: {
        type: Schema.Types.Boolean,
        required: false
    },
    conversationId: {
        type: Schema.Types.String,
        required: false
    },
    adminPermission: {
        type: adminPermissionSchema,
        required: false
    },
    isLoginFirstTime: {
        type: Schema.Types.Boolean,
        required: false
    },
    FCMToken: {
        type: Schema.Types.String,
        required: false
    },
    googleCalendarAccess: {
        type: Schema.Types.Boolean,
        required: false,
    },
    googleCalendarRefreshToken: {
        type: Schema.Types.String,
        required: false
    },
    appointmentAuthToken: {
        type: Schema.Types.String,
        required: false,
    },
    appointmentAuthTokenExpiredAt: {
        type: Schema.Types.Date,
        required: false
    },
}, UserSchemaOptions);

UserSchema.pre('save', function (next) {
    const user: any = this;

    // only hash the password if it has been modified (or is new)
    if (!user.isModified('password')) return next();

    // generate a salt
    // noinspection JSIgnoredPromiseFromCall
    bcrypt.genSalt(10, function (err, salt) {
        if (err) return next(err);

        // hash the password using our new salt
        // noinspection JSIgnoredPromiseFromCall
        bcrypt.hash(user.password, salt, function (err, hash) {
            if (err) return next(err);

            user.password = hash;
            next();
        });
    });
});

// @ts-ignore
// UserSchema.methods.createAccessToken = function (this: IUser, expiresIn?:string) {
//     if(expiresIn){
//         return jwt.sign({user_id: this._id}, process.env.JWT_SECRET, {expiresIn});
//     } else {
//         return jwt.sign({user_id: this._id}, process.env.JWT_SECRET);
//     }
// };

UserSchema.methods.createAccessToken = function (
    expiresIn: string = "48h"
) {
    return jwt.sign({ user_id: this._id }, process.env.JWT_SECRET, {
        expiresIn: expiresIn,
    });
};

UserSchema.methods.comparePassword = function (password: any): Promise<boolean> {
    return new Promise((resolve, reject) => {
        // noinspection JSIgnoredPromiseFromCall
        // @ts-ignore
        bcrypt.compare(password, this.password, function (err, isMatch) {
            if (err) {
                return reject(err);
            }
            return resolve(isMatch);
        });
    });
};

UserSchema.methods.compareVerificationCode = function (verificationCode: any): Promise<boolean> {
    return new Promise((resolve, reject) => {
        // noinspection JSIgnoredPromiseFromCall
        // @ts-ignore
        bcrypt.compare(verificationCode, this.verificationCode, function (err, isMatch) {
            if (err) {
                return reject(err);
            }

            return resolve(isMatch);
        });
    });
};

UserSchema.methods.hasPermission = function (...permissions: Permission[]): boolean {
    // @ts-ignore
    const [success] = checkPermission(this, permissions);
    return success;
};

const User = mongoose.model<IUser>('User', UserSchema);
export default User;
