import * as mongoose from "mongoose";
import { Schema } from "mongoose";

import { FormHeaderSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/formHeader-schema";
import { SymptomChicklistSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/symptomChicklist-schema";
import { BiologicalFunctionsSchemas } from "./sub-schemas/DigitalAssessmentFormSubSchemas/biologicalFunction-schema";
import { TobaccoUseSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/tobaccoUse-schema";
import { ReleventMedicalInformationSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/releventMedicalInformation-schema";
import { InPatienttreatementHistorySchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/inPatienttreatementHistoryDetails-schema";
import { BioPsychosocialDevelopemntHistorySchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/bioPsychosocialDevelopemntHistory-schema";
import { BioPsychosocialEducationSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/bioPsychosocialEducation-schema";
import { TraumaHistorySchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/traumaHistory-schema";
import { MentalStatusSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/mentalStatus-schema";
import { ReleventDDSInformationSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/releventDDSInformation-schema";
import { DiagnoseRecommendationDetailsSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/diagnoseRecommendationDetails-schema";
import { IDigitalAssessment } from "./../models/digital-assessment-model";
import { OutPatientTreatementHistorySchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/outPatientTreatementHistory-schema";
import { GeneralInformationSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/generalInformation-schema";
import { NameOfSubstanceSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/nameOfSubstance-schema";
import { RiskToSelfAndOtherSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/riskToselfAndOther-schema";
import { ReleventLegalInforMationSchema } from "./sub-schemas/DigitalAssessmentFormSubSchemas/releventLegalInformation-schema";
import DigitalAssessment from "./digital-assessment-form-schema";
import { IDigitalAssessmentVersion } from "../models/digital-assessment-version-model";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const DigitalAssessmentFormsVersionSchema = new mongoose.Schema(
  {
    digitalAssessmentId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: DigitalAssessment.modelName
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    clientId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    formHeader: {
      type: FormHeaderSchema,
      required: false,
    },
    generalInformation: {
      type: GeneralInformationSchema,
      required: false,
    },
    presentingProblem: {
      description: {
        type: Schema.Types.String,
        required: false,
      },
      historyOfProblem: {
        type: Schema.Types.String,
        required: false,
      },
    },
    historyOfProblem: {
      type: Schema.Types.String,
      required: false,
    },
    symptomChicklist: {
      type: SymptomChicklistSchema,
      required: false,
    },
    biologicalFunction: {
      type: BiologicalFunctionsSchemas,
      required: false,
    },
    alcoholAndDrugUseHistory: {
      historyStatus: {
        type: Schema.Types.String,
        required: false,
      },
    },
    nameOfSubstance: [
      {
        type: NameOfSubstanceSchema,
        required: false,
      },
    ],
    tobaccoUseTypeDetails: {
      type: TobaccoUseSchema,
      required: false,
    },
    releventMedicalInformation: {
      type: ReleventMedicalInformationSchema,
      required: false,
    },
    outPatientTreatementHistory: {
      type: OutPatientTreatementHistorySchema,
      required: false,
    },
    inpatientTreatementHistory: {
      type: InPatienttreatementHistorySchema,
      required: false,
    },
    bioPsychosocialDevelopemntHistory: {
      type: BioPsychosocialDevelopemntHistorySchema,
      required: false,
    },
    bioPsychosocialEducation: {
      type: BioPsychosocialEducationSchema,
      required: false,
    },
    traumaHistory: {
      type: TraumaHistorySchema,
      required: false,
    },
    riskToSelfAndOthers: {
      type: RiskToSelfAndOtherSchema,
      required: false,
    },
    mentalStatus: {
      type: MentalStatusSchema,
      required: false,
    },
    releventDDSInformation: {
      type: ReleventDDSInformationSchema,
      required: false,
    },
    relevantLegalInformation: {
      type: ReleventLegalInforMationSchema,
      required: false,
    },
    diagnoseRecommendationDetails: {
      type: DiagnoseRecommendationDetailsSchema,
      required: false,
    },
    reasonForEdit: {
      type: Schema.Types.String,
      required: true,
    },
    versionCreatedBy: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: User.modelName,
    },
    versionCreatedAt: {
      type: Schema.Types.Date,
      required: true,
    },
  },
  schemaOptions
);

const DigitalAssessmentVersion = mongoose.model<IDigitalAssessmentVersion>(
  "DigitalAssessmentVersion",
  DigitalAssessmentFormsVersionSchema
);
export default DigitalAssessmentVersion;
