import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IReminderSMS } from "../models/reminder-sms-model";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const ReminderSMSSchema = new mongoose.Schema({
    clientId: {
        type: Schema.Types.ObjectId,
        require: false,
        ref: 'User',
    },
    reply: {
        type: Schema.Types.Number,
        require: false,
    },
    sentSMS: {
        type: Schema.Types.String,
        require: true,
    },
    smsType: {
        type: Schema.Types.String,
        require: false,
    },
    replySMS: [
        {
            type: Schema.Types.String,
            required: false,
            default: []
        }
    ],
}, schemaOptions);

const ReminderSMS = mongoose.model<IReminderSMS>('ReminderSMS', ReminderSMSSchema);

export default ReminderSMS;