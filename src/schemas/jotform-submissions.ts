import * as mongoose from "mongoose";

export interface IJotformSubmission extends mongoose.Document {
  formName: string;
  formId: string;
  resultLink: string;
  submissionDate: Date;
  therapistId: mongoose.Types.ObjectId;
  clientId: mongoose.Types.ObjectId;
  submissionId: string;
  status: string;
  sharedTherapist: mongoose.Types.ObjectId[];
  score_PHQ_9?: number;
  score_GAD_7?: number;
  score_DAST?: number;
  score_AUDIT?: number;
}

const JotformSubmissionSchema = new mongoose.Schema(
  {
    formName: { type: String, required: true },
    formId: { type: String, required: true },
    resultLink: { type: String, required: true },
    submissionDate: { type: Date, required: true },
    therapistId: { type: mongoose.Schema.Types.ObjectId, ref: "Therapist", required: true },
    clientId: { type: mongoose.Schema.Types.ObjectId, ref: "Client", required: true },
    submissionId: { type: String, required: true },
    status: { type: String, required: true, default: 'ACTIVE' },
    sharedTherapist: [{ type: mongoose.Schema.Types.ObjectId, ref: "Therapist" }],
    score_PHQ_9: { type: Number, required: false },
    score_GAD_7: { type: Number, required: false },
    score_DAST: { type: Number, required: false },
    score_AUDIT: { type: Number, required: false }
  },
  {
    timestamps: true,
  }
);

// Create compound index to prevent duplicate submissions
JotformSubmissionSchema.index({ formId: 1, submissionId: 1 }, { unique: true });

export default mongoose.model<IJotformSubmission>("JotformSubmission", JotformSubmissionSchema);
