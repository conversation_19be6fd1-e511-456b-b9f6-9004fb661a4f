import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import Upload from "./upload-schema";
import { DueDateSchema } from "./sub-schemas/due-date-schema";
import { IGoal } from '../models/goal-model';
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
      getters: true,
      virtuals: true
    },
};

export const GoalSchema = new mongoose.Schema({
    title: {
        type: Schema.Types.String,
        require: true
    },
    description: {
        type: Schema.Types.String,
        require: true
    },
    prograss: {
        type: Schema.Types.Number,
        require: true,
        default: 0
    },
    dueDate: {
        type: Schema.Types.String,
        require: true
    },
    completedDates: [{
        type: DueDateSchema
    }],
    isComplete: {
        type: Schema.Types.Boolean,
        require: true,
        default: false
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        require: true,
        ref: User.modelName
    },
    assignedFor: {
        type: Schema.Types.ObjectId,
        require: false,
        ref: User.modelName
    }
}, schemaOptions);

const Goal = mongoose.model<IGoal>('Goal', GoalSchema);

export default Goal;