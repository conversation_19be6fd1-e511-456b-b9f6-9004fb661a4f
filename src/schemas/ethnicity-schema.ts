import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IEthnicity } from "../models/ethnicity-model";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};

export const EthnicitySchema = new mongoose.Schema({
    ethnicity: {
        type: Schema.Types.String,
        require : true,
    },    
}, schemaOptions);

const Ethnicity = mongoose.model<IEthnicity>('Ethnicity', EthnicitySchema);

export default Ethnicity;