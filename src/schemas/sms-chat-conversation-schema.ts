import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ISmsChatConversation } from "../models/sms-chat-conversation-model";
import User from "./user-schema";
import Message from "./message-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};
export const SmsChatConversationSchema = new mongoose.Schema(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    conversationId: {
      type: Schema.Types.String,
      require: true,
    },
    chatId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: Message.modelName,
    },
    twilioParticipantId: {
      type: Schema.Types.String,
      require: false,
    },
    phoneNumber: {
      type: Schema.Types.String,
      required: false
    },
  },
  schemaOptions
);
const SmsChatConversation = mongoose.model<ISmsChatConversation>(
  "SmsChatConversation",
  SmsChatConversationSchema
);
export default SmsChatConversation;
