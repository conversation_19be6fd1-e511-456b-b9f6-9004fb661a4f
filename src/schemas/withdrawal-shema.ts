import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IWithdrawal } from "../models/withdrawal-model";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const WithdrawalSchema = new mongoose.Schema(
  {
    therapistId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    },
    withdrawnAmount: {
      type: Schema.Types.Number,
      require: true
    }
  },
  schemaOptions
);

const Withdrawal = mongoose.model<IWithdrawal>("Withdrawal", WithdrawalSchema);

export default Withdrawal;