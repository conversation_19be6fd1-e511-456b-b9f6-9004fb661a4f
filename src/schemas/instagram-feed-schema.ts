import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IInstagramFeed } from "../models/instagram-feed-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
    transform: (doc: any, ret: any) => {
      delete ret.path;
      delete ret.isUrl;
    },
  },
};

const instagramFeedSchema = new mongoose.Schema(
  {
    instagramId: {
      type: Schema.Types.String,
      required: false,
    },
    path: {
      type: Schema.Types.String,
      required: false,
    },
    mediaUrl: {
      type: Schema.Types.String,
      required: false,
    },
    caption: {
        type: Schema.Types.String,
        required: false,
      }
  },
  schemaOptions
);


instagramFeedSchema.set("toObject", { virtuals: true });
instagramFeedSchema.set("toJSON", { virtuals: true });

const InstagramFeed = mongoose.model<IInstagramFeed>("InstagramFeed", instagramFeedSchema);
export default InstagramFeed;
