import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IUploadDocument } from "../models/upload-documents-model";
import Client from "./client-schema";
import Therapist from "./therapist-schema";
import Upload from "./upload-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const UploadDocumentSchema = new mongoose.Schema(
  {

    clientId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: Client.modelName,
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: Therapist.modelName,
    },
    uploadDocumentId: {
        type: Schema.Types.ObjectId,
        required: false,
        ref: Upload.modelName,
      },
  },
  schemaOptions
);

const UploadDocument = mongoose.model<IUploadDocument>(
  "UploadDocument",
  UploadDocumentSchema
);

export default UploadDocument;
