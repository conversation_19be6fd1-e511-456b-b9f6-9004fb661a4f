import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import {
  ITrainingDocumentFolderFile,
  DTrainingDocumentFolderFile,
} from "../models/training-document-folder-file-model";
import User from "./user-schema";
import TrainingDocumentsFolder from "./training-document-folder-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const TrainingDocumentFolderFileSchema = new mongoose.Schema(
  {
    originalFileName: {
      type: Schema.Types.String,
      required: true,
    },
    fileNameInAwsBucket: {
      type: Schema.Types.String,
      required: true,
    },
    type: {
      type: Schema.Types.String,
      required: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: User.modelName,
    },
    parentFolderId: {
      type: Schema.Types.ObjectId,
      default: null,
      ref: TrainingDocumentsFolder.modelName,
    },
    title: {
      type: Schema.Types.String,
      required: true,
    },
    description: {
      type: Schema.Types.String,
      required: true,
    },
  },
  schemaOptions
);

const TrainigDocFolderFile = mongoose.model<ITrainingDocumentFolderFile>(
  "TrainingDocumentsFolderFile",
  TrainingDocumentFolderFileSchema
);

export default TrainigDocFolderFile;
