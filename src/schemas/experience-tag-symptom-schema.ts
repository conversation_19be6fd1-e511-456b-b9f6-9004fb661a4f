import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IExperienceTagSymptom } from "../models/experience-tag-symptom-model";
import ExperienceMainTag from './experience-main-tag-schema'

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
        getters: true,
        virtuals: true,
    },
};
export const ExperienceTagSymptomSchema = new mongoose.Schema({
    symptomName: {
        type: Schema.Types.String,
        require : true,
    },    
    experienceMainTags: [
        {
            type: Schema.Types.ObjectId,
            required: false,
            ref: ExperienceMainTag.modelName
        }
    ],
}, schemaOptions);

const ExperienceTag = mongoose.model<IExperienceTagSymptom>('ExperienceTagSymptom', ExperienceTagSymptomSchema);

export default ExperienceTag;