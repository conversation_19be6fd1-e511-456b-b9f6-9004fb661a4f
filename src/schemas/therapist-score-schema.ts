import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";
import { ITherapistScore } from "../models/therapist-score-model";
import Meeting from "./meeting-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const TherapistScoreSchema = new mongoose.Schema(
  {
    therapistId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName
    },
    responseTime: {
      type: Schema.Types.Number,
      require: false
    },
    availability: {
      type: Schema.Types.Number,
      require: false
    },
    followUpAppointments: {
      type: Schema.Types.Number,
      require: false
    },
    totalSessions: {
      type: Schema.Types.Number,
      require: false
    },
    missedAppointments: {
      type: Schema.Types.Number,
      require: false
    },
    loyaltyYears: {
      type: Schema.Types.Number,
      require: false
    },
    scheduledAppointments: {
      type: Schema.Types.Number,
      require: false
    },
    noOfMatches: {
      type: Schema.Types.Number,
      require: false
    },
    score: {
      type: Schema.Types.Number,
      require: false
    }
  },
  schemaOptions
);

const TherapistScore = mongoose.model<ITherapistScore>("TherapistScore", TherapistScoreSchema);

export default TherapistScore;
