import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "../user-schema";

export const PayRateTypeSchema = new mongoose.Schema(
  {
    type: {
      type: Schema.Types.String,
      required: false,
      ref: User.modelName,
    },
    percentage: {
      type: Schema.Types.Number,
      required: false,
    },
    flatValue: {
      type: Schema.Types.Number,
      required: false,
    },
  },

  { _id: false }
);
