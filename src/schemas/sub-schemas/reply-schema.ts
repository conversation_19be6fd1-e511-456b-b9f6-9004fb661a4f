import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { Reply } from "../../models/sub-models/reply-model";

export const ReplySchema = new mongoose.Schema({    
  userId: {
    type: Schema.Types.ObjectId,
    required: true,
  },
  parentId: {
    type: Schema.Types.ObjectId,
    required: true,
  },
  reply: {
    type: Schema.Types.String,
    required: true,
  },
//   replies: [
//     new mongoose.Schema({
//       type: Schema.Types.Mixed,
//       required: false,
//     }),
//   ],
  date: {
    type: Schema.Types.Date,
    required: true,
  },
});

const Reply = mongoose.model<Reply>("Reply", ReplySchema);
export default Reply;