import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const CarolinaCompleteHealthProviderSchema = new mongoose.Schema(
    {
        providerName: {
            type: Schema.Types.String,
            required: false
        },
        providerAddress: {
            type: Schema.Types.String,
            required: false
        },
        npi: {
            type: Schema.Types.String,
            required: false
        },
        taxId: {
            type: Schema.Types.String,
            required: false
        },
        faxNumber: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false }  
);
