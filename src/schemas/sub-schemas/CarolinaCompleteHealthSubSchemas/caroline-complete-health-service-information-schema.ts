import * as mongoose from "mongoose";
import { Schema } from "mongoose";

const CarolinaCompleteHealthServiceInformationArraySchema = new mongoose.Schema({
    referenceNumber: {
        type: Schema.Types.String,
        required: false
    },
    procedureCode: {
        type: Schema.Types.String,
        required: false
    },
    startDate: {
        type: Schema.Types.Date,
        required: false
    },
    endDate: {
        type: Schema.Types.Date,
        required: false
    },
    serviceDescription: {
        type: Schema.Types.String,
        required: false
    },
    itemQuantity: {
        type: Schema.Types.String,
        required: false
    },
    approved: {
        type: Schema.Types.Boolean,
        required: false
    },
    denied: {
        type: Schema.Types.Boolean,
        required: false
    },
    allowedAmount: {
        type: Schema.Types.String,
        required: false
    },
}, { _id: false }); 

export const CarolinaCompleteHealthServiceInformationSchema = new mongoose.Schema({
    serviceInformationArray: {
        type: [CarolinaCompleteHealthServiceInformationArraySchema], 
        required: false
    },
    necessityExplanation: {
        type: Schema.Types.String,
        required: false
    }
});