import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const CarolinaCompleteGeneralInformationSchema = new mongoose.Schema(
    {
        memberName: {
            type: Schema.Types.String,
            required: false
        },
        dateOfBirth: {
            type: Schema.Types.Date,
            required: false
        },
        medicaidID: {
            type: Schema.Types.String,
            required: false
        },
        memberAddress: {
            type: Schema.Types.String,
            required: false
        },
        diagnosisCode: {
            type: Schema.Types.String,
            required: false
        },
        diagnosisDescription: {
            type: Schema.Types.String,
            required: false
        },
        facilityName: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false }  
);
