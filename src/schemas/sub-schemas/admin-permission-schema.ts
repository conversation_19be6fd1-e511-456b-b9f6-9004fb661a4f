import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const adminPermissionSchema = new mongoose.Schema(
    {
        statistics: {
            type: Schema.Types.Boolean,
            required: true,
        },
        adminDashboard: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewEthnicity: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewExperienceTags: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewInsuranceCompanies: {
            type: Schema.Types.Boolean,
            required: true,
        },
        createAppointmentAdmin: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewProfessions: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewHashTags: {
            type: Schema.Types.Boolean,
            required: true,
        },
        documents: {
            type: Schema.Types.Boolean,
            required: true,
        },
        accessManagement: {
            type: Schema.Types.<PERSON>olean,
            required: true,
        },
        viewThemeImage: {
            type: Schema.Types.Boolean,
            required: true,
        },
        reportReviews: {
            type: Schema.Types.Boolean,
            required: true,
        },
        reviews: {
            type: Schema.Types.Boolean,
            required: true,
        },
        contactUs: {
            type: Schema.Types.Boolean,
            required: true,
        },
        articles: {
            type: Schema.Types.Boolean,
            required: true,
        },
        feedback: {
            type: Schema.Types.Boolean,
            required: true,
        },
        newsLetterEmails: {
            type: Schema.Types.Boolean,
            required: true,
        },
        marketingEmails: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewMeetingsAndRecordings: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewAllClients: {
            type: Schema.Types.Boolean,
            required: true,
        },
        manageClients: {
            type: Schema.Types.Boolean,
            required: true,
        },
        premiumClients: {
            type: Schema.Types.Boolean,
            required: true,
        },
        reminderSms: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewAllTherapists: {
            type: Schema.Types.Boolean,
            required: true,
        },
        manageTherapists: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewTherapistReviews: {
            type: Schema.Types.Boolean,
            required: true,
        },
        viewTherapistsSoapReviews: {
            type: Schema.Types.Boolean,
            required: true,
        },
        educationalDetails: {
            type: Schema.Types.Boolean,
            required: true,
        },
        licenseDetails: {
            type: Schema.Types.Boolean,
            required: true,
        },
        therapistRequests: {
            type: Schema.Types.Boolean,
            required: true,
        },
        availableBalances: {
            type: Schema.Types.Boolean,
            required: true,
        },
        adminApprovePayment: {
            type: Schema.Types.Boolean,
            required: true,
        },
        referralEarnings: {
            type: Schema.Types.Boolean,
            required: true,
        },
        clientRewards: {
            type: Schema.Types.Boolean,
            required: true,
        },
        notifications: {
            type: Schema.Types.Boolean,
            required: true,
        },
        sessionFeedback: {
            type: Schema.Types.Boolean,
            required: true,
        },
        approvalQueue: {
            type: Schema.Types.Boolean,
            required: true,
        },
        techTickets: {
            type: Schema.Types.Boolean,
            required: true,
        },
        profile: {
            type: Schema.Types.Boolean,
            required: true,
            default: true
        }
    },

    { _id: false }
);
