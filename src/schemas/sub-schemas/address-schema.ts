import * as mongoose from "mongoose";
import { Schema } from "mongoose";
export const AddressSchema = new mongoose.Schema(
    {
        address1: {
            type: Schema.Types.String,
            required: false,
        },

        city: {
            type: Schema.Types.String,
            required: false,
        },

        state: {
            type: Schema.Types.String,
            required: false,
        },
        postalCode: {
            type: Schema.Types.String,
            required: false,
        }
    },
    { _id: false }
);
