import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const chiefComplaintSchema = new mongoose.Schema(
    {
        precipitatingProblem: {
            type: Schema.Types.String,
            required: false,
        },
        HistoryofIllness: {
            type: Schema.Types.String,
            required: false,
        },
        familySocialHistory: {
            type: Schema.Types.String,
            required: false,
        },
        shortTermGoal: {
            type: Schema.Types.String,
            required: false,
        },
        longTermGoal: {
            type: Schema.Types.String,
            required: false,
        },
    },
    { _id: false }
)