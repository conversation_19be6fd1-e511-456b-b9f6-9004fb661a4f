import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const scoring = new mongoose.Schema(
    {
        notAtAll: {
            type: Schema.Types.Boolean,
            required: false,
        },
        severalDays: {
            type: Schema.Types.Boolean,
            required: false,
        },
        moreThanHalfTheDays: {
            type: Schema.Types.Boolean,
            required: false,
        },
        NearlyEveryDay: {
            type: Schema.Types.Boolean,
            required: false,
        },

    },
    { _id: false }
);

export const Diflevel = new mongoose.Schema(
    {
        notDifficult:{
            type:Schema.Types.Boolean,
            required:false,
        },
        someWhatDifficult:{
            type:Schema.Types.Boolean,
            required:false,
        },
        veryDifficult:{
            type:Schema.Types.Boolean,
            required:false,
        },
        extremlyDifficult:{
            type:Schema.Types.Boolean,
            required:false,
        },
    }, {_id:false}
)

export const ColumnScore = new mongoose.Schema(
    {
        column1:{
            type:Schema.Types.Number,
            required:false,
        },
        column2:{
            type:Schema.Types.Number,
            required:false,
        },
        column3:{
            type:Schema.Types.Number,
            required:false,
        },
        column4:{
            type:Schema.Types.Number,
            required:false,
        },
        total:{
            type:Schema.Types.Number,
            required:false,
        },
    }, {_id:false}
)

export const GAD7AnxietySchema = new mongoose.Schema(
    {
        feelingNervous: {
            type: scoring,
            required: false,
        },
        cantControllWorrying: {
            type: scoring,
            required: false,
        },
        worryingMuch: {
            type: scoring,
            required: false,
        },
        troubleRelaxing: {
            type: scoring,
            required: false,
        },
        beingRestless: {
            type: scoring,
            required: false,
        },
        easilyAnnoyed: {
            type: scoring,
            required: false,
        },
        feelingAfraid: {
            type: scoring,
            required: false,
        },
        difficultLevel: {
            type: Diflevel,
            required: false
        },
        columnCal: {
            type: ColumnScore,
            required: false
        }
    },
    { _id: false }
);