import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { lethalityAssessment } from "../../../models/sub-models/ClinicalAssesmentSubModels/suicideRiskPotential-model";


export const SummaryofNeedSchema = new mongoose.Schema(
    {


        financial: {
            type: Schema.Types.Boolean,
            required: false
        },
        housing: {
            type: Schema.Types.Boolean,
            required: false
        },
        spiritual: {
            type: Schema.Types.Boolean,
            required: false
        },
        sexuality: {
            type: Schema.Types.Boolean,
            required: false
        },
        safetyCrisis: {
            type: Schema.Types.Boolean,
            required: false
        },
        transportation: {
            type: Schema.Types.Boolean,
            required: false
        },
        employment: {
            type: Schema.Types.Boolean,
            required: false
        },
        medicalHealth: {
            type: Schema.Types.Boolean,
            required: false
        },
        substanceUse: {
            type: Schema.Types.Boolean,
            required: false
        },
        parentingSkills: {
            type: Schema.Types.Boolean,
            required: false
        },
        physicalNeeds: {
            type: Schema.Types.Boolean,
            required: false
        },
        adaptive: {
            type: Schema.Types.Boolean,
            required: false
        },
        educationalVocational: {
            type: Schema.Types.Boolean,
            required: false
        },
        emotionalPsychological: {
            type: Schema.Types.Boolean,
            required: false
        },
        legalCourtInvolvement: {
            type: Schema.Types.Boolean,
            required: false
        },
        familyAttachment: {
            type: Schema.Types.Boolean,
            required: false
        },
        culturalImmigration: {
            type: Schema.Types.Boolean,
            required: false
        },
        socialSkillsRecreational: {
            type: Schema.Types.Boolean,
            required: false
        },
        communicationLanguage: {
            type: Schema.Types.Boolean,
            required: false
        },
        behaviorManagement: {
            type: Schema.Types.Boolean,
            required: false
        },
        problemSolvingSkills: {
            type: Schema.Types.Boolean,
            required: false
        },
        developmentalHistory: {
            type: Schema.Types.Boolean,
            required: false
        },
    },
    { _id: false }
)