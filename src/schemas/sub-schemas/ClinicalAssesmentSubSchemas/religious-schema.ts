import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const ReligiousCulturalLanguagePrefSchema = new mongoose.Schema(
    {
        christian: {
            type: Schema.Types.Boolean,
            required: false
        },
        doesntKnow: {
            type: Schema.Types.Boolean,
            required: false
        },
        otherReligion: {
            isOtherReligion: {
                type: Schema.Types.Boolean,
                required: false
            },
            otherReligionName: {
                type: Schema.Types.String,
                required: false
            }
        },
        english: {
            type: Schema.Types.Boolean,
            required: false
        },
        spanish: {
            type: Schema.Types.Boolean,
            required: false
        },
        otherlanguage: {
            isOtherLanguage: {
                type: Schema.Types.Boolean,
                required: false
            },
            otherLanguageName: {
                type: Schema.Types.String,
                required: false
            }
        },

    }
);