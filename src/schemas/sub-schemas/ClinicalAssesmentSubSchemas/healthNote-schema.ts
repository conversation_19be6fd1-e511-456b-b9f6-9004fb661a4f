import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const HealthNotesSchema = new mongoose.Schema(
    {
        notes: {
            type: String,
            required: false
        },
        allergies: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        enuresisOrEncopresis: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        visionComplications: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        hearingComplications: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        dentalComplications: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        dietRestrictions: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        seizures: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        somaticComplaints: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        heartBloodPressure: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        diabetes: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        hxofEatingDisorder: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        problemsSleeping: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            },
            goingtoSleep: {
                type: Schema.Types.Boolean,
                default: false
            },
            stayingAsleep: {
                type: Schema.Types.Boolean,
                default: false
            },
            Nightmares: {
                type: Schema.Types.Boolean,
                default: false
            }
        },
        historyofHeadInjury: {
            isReported: {
                type: Schema.Types.Boolean,
                default: false
            },
            explanation: {
                type: Schema.Types.String,
                default: ""
            }
        },
        dateofLastPhysical: {
            type: Schema.Types.String,
            required: false
        },
    },
    { _id: false }
);