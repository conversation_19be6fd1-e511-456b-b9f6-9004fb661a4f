import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { generalInforEthnicity, genralInfoGender } from "../../../models/sub-models/ClinicalAssesmentSubModels/generalInfor-model";

export const generalInforSchema = new mongoose.Schema(
    {
        pcpReferal: {
            type: Schema.Types.String,
            required: false,
        },
        npi: {
            type: Schema.Types.String,
            required: false,
        },
        gender: {
            type: Schema.Types.String,
            enum: genralInfoGender,
            required: false,
        },
        age: {
            type: Schema.Types.Number,
            required: false,
        },
        consumerDob: {
            type: Schema.Types.String,
            required: false,
        },
        consumerphone: {
            type: Schema.Types.String,
            required: false,
        },
        ethnicity: {
            type: Schema.Types.String,
            enum: generalInforEthnicity,
            required: false,
        },
        other: {
            type: Schema.Types.String,
            required: false,
        },
        emergancyContactName: {
            type: Schema.Types.String,
            required: false,
        },
        phone: {
            type: Schema.Types.String,
            required: false,
        },
        individualParticipants: {
            type: Schema.Types.String,
            required: false,
        },
        insuranceBesideBCBS: {
            noOtherInusrnace: {
                type: Schema.Types.Boolean,
                required: false,
            },
            nameOfInsurance: {
                type: Schema.Types.String,
                required: false,
            },
        },

        otherTherapyService: {
            noService: {
                type: Schema.Types.Boolean,
                required: false,
            },
            nameOfService: {
                type: Schema.Types.String,
                required: false,
            },

        },

    },
    { _id: false }
)