const mongoose = require('mongoose');
const Schema = mongoose.Schema;

export const MentalStatusExamSchema = new mongoose.Schema(
    {
        appearanceDress: {
            neat: {
                type: Schema.Types.Boolean
            },
            relaxed: {
                type: Schema.Types.Boolean
            },
            disheveled: {
                type: Schema.Types.Boolean
            },
            eccentric: {
                type: Schema.Types.Boolean
            },
            ageAppropriate: {
                type: Schema.Types.Boolean
            },
            hygiene: {
                normal: {
                    type: Schema.Types.Boolean
                },
                bodyOdor: {
                    type: Schema.Types.Boolean
                },
                badBreath: {
                    type: Schema.Types.Boolean
                },
            },
            other: {
                type: Schema.Types.String
            },
        },
        behaviour: {
            passive: {
                type: Schema.Types.Boolean
            },
            defensive: {
                type: Schema.Types.Boolean
            },
            guarded: {
                type: Schema.Types.Boolean
            },
            hostile: {
                type: Schema.Types.Boolean
            },
            attentive: {
                type: Schema.Types.Boolean
            },
            cooperative: {
                type: Schema.Types.Boolean
            },
            demanding: {
                type: Schema.Types.Boolean
            },
            oppositional: {
                type: Schema.Types.Boolean
            },
            selfDestructive: {
                type: Schema.Types.Boolean
            },
            preoccupied: {
                type: Schema.Types.Boolean
            },
            eyeContact: {
                intense: {
                    type: Schema.Types.Boolean
                },
                appropriate: {
                    type: Schema.Types.Boolean
                },
                sporadic: {
                    type: Schema.Types.Boolean
                },
                avoidant: {
                    type: Schema.Types.Boolean
                },
            }

        },
        motor: {
            relaxed: {
                type: Schema.Types.Boolean
            },
            catatonic: {
                type: Schema.Types.Boolean
            },
            posturing: {
                type: Schema.Types.Boolean
            },
            restless: {
                type: Schema.Types.Boolean
            },
            tremors: {
                type: Schema.Types.Boolean
            },
            other: {
                type: Schema.Types.String
            },
            pacing: {
                type: Schema.Types.Boolean
            },
            tics: {
                type: Schema.Types.Boolean
            },
            threatening: {
                type: Schema.Types.Boolean
            },
            mannerisms: {
                type: Schema.Types.Boolean
            },
            appearsSedated: {
                type: Schema.Types.Boolean
            },
            psychomotorRet: {
                type: Schema.Types.Boolean
            },
        },
        language: {
            rate: {
                normal: {
                    type: Schema.Types.Boolean
                },
                pressured: {
                    type: Schema.Types.Boolean
                },
                slow: {
                    type: Schema.Types.Boolean
                },
            },
            quality: {
                normal: {
                    type: Schema.Types.Boolean
                },
                apraxic: {
                    type: Schema.Types.Boolean
                },
                dysarthric: {
                    type: Schema.Types.Boolean
                },
                paraphasia: {
                    type: Schema.Types.Boolean
                },
                clanging: {
                    type: Schema.Types.Boolean
                },
                echolalia: {
                    type: Schema.Types.Boolean
                },
                incoherent: {
                    type: Schema.Types.Boolean
                },
                neologisms: {
                    type: Schema.Types.Boolean
                }
            },
            volume: {
                normal: {
                    type: Schema.Types.Boolean
                },
                loud: {
                    type: Schema.Types.Boolean
                },
                soft: {
                    type: Schema.Types.Boolean
                }

            }
        },
        mood: {
            normal: {
                type: Schema.Types.Boolean
            },
            euphoric: {
                type: Schema.Types.Boolean
            },
            elevated: {
                type: Schema.Types.Boolean
            },
            angry: {
                type: Schema.Types.Boolean
            },
            depress: {
                type: Schema.Types.Boolean
            },
            anxious: {
                type: Schema.Types.Boolean
            },
            irritable: {
                type: Schema.Types.Boolean
            },
            other: {
                type: Schema.Types.String
            },

        },
        affect: {
            broad: {
                type: Schema.Types.Boolean
            },
            blunted: {
                type: Schema.Types.Boolean
            },
            reactive: {
                type: Schema.Types.Boolean
            },
            flat: {
                type: Schema.Types.Boolean
            },
            congruent: {
                type: Schema.Types.Boolean
            },
            labile: {
                type: Schema.Types.Boolean
            },
            restricted: {
                type: Schema.Types.Boolean
            },
            other: {
                type: Schema.Types.String
            },
            inappropriate: {
                type: Schema.Types.Boolean
            },
        },
        thoughtFormation: {
            logical: {
                type: Schema.Types.Boolean
            },
            concrete: {
                type: Schema.Types.Boolean
            },
            obsessive: {
                type: Schema.Types.Boolean
            },
            unexplainedDizziness: {
                type: Schema.Types.Boolean
            },
            illogical: {
                type: Schema.Types.Boolean
            },
            relevant: {
                type: Schema.Types.Boolean
            },
            blocking: {
                type: Schema.Types.Boolean
            },
            sequential: {
                type: Schema.Types.Boolean
            },
            irrelevant: {
                type: Schema.Types.Boolean
            },
            circumstantial: {
                type: Schema.Types.Boolean
            },
            indecisive: {
                type: Schema.Types.Boolean
            },
            distractible: {
                type: Schema.Types.Boolean
            },
            tangential: {
                type: Schema.Types.Boolean
            },
            goalDirected: {
                type: Schema.Types.Boolean
            },
            other: {
                type: Schema.Types.String
            },
            flightofIdeas: {
                type: Schema.Types.Boolean
            },
            looseAssociations: {
                type: Schema.Types.Boolean
            },

        },
        thoughtContent: {
            delusions: {
                type: Schema.Types.Boolean
            },
            somatic: {
                type: Schema.Types.Boolean
            },
            suicidal: {
                type: Schema.Types.Boolean
            },
            separationLossDwelling: {
                type: Schema.Types.Boolean
            },
            guilt: {
                type: Schema.Types.Boolean
            },
            homicidal: {
                type: Schema.Types.Boolean
            },
            phobias: {
                type: Schema.Types.Boolean
            },
            obsessions: {
                type: Schema.Types.Boolean
            },
            grandiose: {
                type: Schema.Types.Boolean
            },
            suspicious: {
                type: Schema.Types.Boolean
            },
            hopelessness: {
                type: Schema.Types.Boolean
            },
            futureOriented: {
                type: Schema.Types.Boolean
            },
            hallucinations: {
                type: Schema.Types.Boolean
            },
        },
        orientation: {
            person: {
                type: Schema.Types.Boolean
            },
            place: {
                type: Schema.Types.Boolean
            },
            time: {
                type: Schema.Types.Boolean
            },
            situation: {
                type: Schema.Types.Boolean
            },
        },
        estimateIntellect: {
            average: {
                type: Schema.Types.Boolean
            },
            upAverage: {
                type: Schema.Types.Boolean
            },
            downAverage: {
                type: Schema.Types.Boolean
            },
            borderline: {
                type: Schema.Types.Boolean
            },
            mentalRetardation: {
                type: Schema.Types.Boolean
            },
            knownDisability: {
                type: Schema.Types.String
            },
        },
        memory: {
            adequate: {
                type: Schema.Types.Boolean
            },
            impaired: {
                type: Schema.Types.Boolean
            },
            recent: {
                type: Schema.Types.Boolean
            },
            remote: {
                type: Schema.Types.Boolean
            },
            selective: {
                type: Schema.Types.Boolean
            },
        },
        perception: {
            auditoryHallucinations: {
                type: Schema.Types.Boolean
            },
            visualHallucinations: {
                type: Schema.Types.Boolean
            },
            depersonalization: {
                type: Schema.Types.Boolean
            },
            traumaticFlashbacks: {
                type: Schema.Types.Boolean
            },
            ideasofReference: {
                type: Schema.Types.Boolean
            },
        },
        judgment: {
            good: {
                type: Schema.Types.Boolean
            },
            fair: {
                type: Schema.Types.Boolean
            },
            impaired: {
                type: Schema.Types.String
            },
            poor: {
                type: Schema.Types.Boolean
            }

        },
        impulseControl: {
            fair: {
                type: Schema.Types.Boolean
            },
            poor: {
                type: Schema.Types.Boolean
            },
            severe: {
                type: Schema.Types.Boolean
            },
            adequate: {
                type: Schema.Types.Boolean
            },
            wNL: {
                type: Schema.Types.Boolean
            },
            mildlydistractible: {
                type: Schema.Types.Boolean
            },
            moderately: {
                type: Schema.Types.Boolean
            },
        }

    }
);