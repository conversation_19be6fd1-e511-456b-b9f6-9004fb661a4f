import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const AssesmentHeaderSchema = new mongoose.Schema(
    {
        clientName: {
            type: Schema.Types.String,
            required: false,
        },
        dateOfBirth: {
            type: Schema.Types.Date,
            required: false,
        },
        insuranceNumber: {
            type: Schema.Types.String,
            required: false,
        },
        dateOfSession: {
            type: Schema.Types.String,
            required: false,
        },
        TherapistName: {
            type: Schema.Types.String,
            required: false,
        },
    },
    { _id: false }
);