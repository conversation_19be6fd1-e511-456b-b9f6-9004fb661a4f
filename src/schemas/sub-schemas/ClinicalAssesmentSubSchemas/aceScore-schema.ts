import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { houseHoldMemberSchema } from "./houseHoldMember-schema";

export const AceScoreSchema = new mongoose.Schema(
    {
        humiliateOrPhysicallyHurt: {
            type: Schema.Types.Boolean,
            required: false,
        },
        pushGrabSlap: {
            type: Schema.Types.Boolean,
            required: false,
        },
        intercourse: {
            type: Schema.Types.Boolean,
            required: false,
        },
        familySupport: {
            type: Schema.Types.Boolean,
            required: false,
        },
        noOneToProtectYou: {
            type: Schema.Types.Boolean,
            required: false,
        },
        parentSeperateDivorce: {
            type: Schema.Types.Boolean,
            required: false,
        },
        hitWithSomthingHard: {
            type: Schema.Types.Boolean,
            required: false,
        },
        threatenedWithGunOrKnife: {
            type: Schema.Types.Boolean,
            required: false,
        },
        liveWithDrugUser: {
            type: Schema.Types.Boolean,
            required: false,
        },
        isHouseHoldMemberattemptSuicide: {
            type: Schema.Types.Boolean,
            required: false,
        },
        isHouseHOldMemberGoToPrison: {
            type: Schema.Types.Boolean,
            required: false,
        },
        aceScore: {
            type: Schema.Types.Number,
            required: false,
        },


    },
    { _id: false }
);