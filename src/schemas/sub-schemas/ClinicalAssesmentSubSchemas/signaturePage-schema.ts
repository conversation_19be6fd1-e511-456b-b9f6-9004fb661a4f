import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const signatureDetailsSchema = new mongoose.Schema(
    {
        comprehensiveCliniCalAssesment: {
            
            nameOfClinician: {
                type: Schema.Types.String,
                required: false
            },
            date: {
                type: Schema.Types.Date,
                required: false
            },
        },
        clientAcknowledgingParticipation: {
            
            date: {
                type: Schema.Types.Date,
                required: false
            }
        }


    },
    { _id: false }
);