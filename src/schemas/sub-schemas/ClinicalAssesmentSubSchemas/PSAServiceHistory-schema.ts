import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const PSAServiceHistorySchema = new mongoose.Schema(
    {
        type: {
            type: Schema.Types.String,
            required: false,
        },
        providerName: {
            type: Schema.Types.String,
            required: false,
        },
        datesofService: {
            type: Schema.Types.Number,
            required: false,
        },
        outcome: {
            type: Schema.Types.String,
            required: false,
        },

    },
    { _id: false }
);