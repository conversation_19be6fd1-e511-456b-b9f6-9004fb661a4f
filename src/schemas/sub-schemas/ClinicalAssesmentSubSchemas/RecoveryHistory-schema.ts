import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { recoveryEnvironment } from "../../../models/sub-models/ClinicalAssesmentSubModels/RecoveryHistory-model";


export const RecoveryHistoryandEnvironmentSchema = new mongoose.Schema(
    {
       
        isPreviousMentalHealthTreatment:{
            type:Schema.Types.Boolean,
            required:false
        },
        isPreviousSubstanceAbuseTreatment:{
            type:Schema.Types.Boolean,
            required:false
        },    
        recoveryEnvironment:{
            type:Schema.Types.String,
            enum:recoveryEnvironment,
            required:false
        }
        
    },
    { _id: false }
)