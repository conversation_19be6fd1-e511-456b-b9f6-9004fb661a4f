import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { likertScale } from "../../../models/sub-models/ClinicalAssesmentSubModels/RecoveryAssesmentScale-model";


export const RecoveryAssesmentScaleSchema = new mongoose.Schema(
    {
        desireToSucceed: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        ownPlanForWellness: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        lifeGoals: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        beliefInMeetingGoals: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        senseOfPurpose: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        othersCaringmySelf: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        fearlessLiving: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        handleLifeSituations: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        selfLove: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        peopleLikeMe: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        ideaWhoWantToBe: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        somethingGoodHappen: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        hopefulness: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        continueInterests: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        mentalIllnessCoping: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        symptomInterference: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        symptomDuration: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        helpSeekingAwareness: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        willingnessToAskForHelp: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        askingHelpWhenNeed: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        stressHandling: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        peaopleCanCounton: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        otherPeopleBelive: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
        importanceOfFriendship: {
            type: Schema.Types.String,
            enum: likertScale,
            required: false
        },
    },{_id:false}
);