import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const PeopleSchema = new mongoose.Schema(
    {
        name1: {
            name: {
                type: Schema.Types.String,
                required: false,
            },
            phone: {
                type: Schema.Types.String,
                required: false,
            },
        },
        name2: {
            name: {
                type: Schema.Types.String,
                required: false,
            },
            phone: {
                type: Schema.Types.String,
                required: false,
            },
        },
        name3: {
            name: {
                type: Schema.Types.String,
                required: false,
            },
            phone: {
                type: Schema.Types.String,
                required: false,
            },
        },

    }, { _id: false }
)

export const ClientSaftyPlanSchema = new mongoose.Schema(
    {

        people: {
            type: PeopleSchema,
            required: false
        },
        professionals: {
            clinicianName: {
                type: Schema.Types.String,
                required: false
            },
            clinicianPhone: {
                type: Schema.Types.String,
                required: false
            },
            docotorName: {
                type: Schema.Types.String,
                required: false
            },
            docotorPhone: {
                type: Schema.Types.String,
                required: false
            },
            careServiceAddress: {
                type: Schema.Types.String,
                required: false
            },
            enviornmentSafe: {
                1: {
                    type: Schema.Types.String,
                    required: false
                },
                2: {
                    type: Schema.Types.String,
                    required: false
                },
            },
            agreement: {
                agree: {
                    type: Schema.Types.String,
                    required: false
                },
                clientSignature: {
                    type: Schema.Types.String,
                    required: false
                },
                clientDate: {
                    type: Schema.Types.String,
                    required: false
                },
                therapistSignature: {
                    type: Schema.Types.String,
                    required: false
                },
                therapistDate: {
                    type: Schema.Types.String,
                    required: false
                },
            }
        }
    }, { _id: false }
)