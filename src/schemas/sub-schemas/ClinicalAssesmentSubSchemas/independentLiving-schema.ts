const mongoose = require('mongoose');
const Schema = mongoose.Schema;

export const independentLivingSchema = new mongoose.Schema(
    {
        assistanceWithSkills: {
            feedingSelf: {
                type: Schema.Types.Boolean
            },
            financialAssistance: {
                type: Schema.Types.Boolean
            },
            applyingForBenefits: {
                type: Schema.Types.Boolean
            },
            mealPreparation: {
                type: Schema.Types.Boolean
            },
            legalAssistance: {
                type: Schema.Types.Boolean
            },
            academicEnrollment: {
                type: Schema.Types.Boolean
            },
            groceryShopping: {
                type: Schema.Types.Boolean
            },
            attendingCourtMandatedEvents: {
                type: Schema.Types.Boolean
            },
            vocationalAssistance: {
                type: Schema.Types.Boolean
            },
            nutrition: {
                type: Schema.Types.Boolean
            },
            accessingSpecializedServices: {
                type: Schema.Types.Boolean
            },
            basicHygiene: {
                type: Schema.Types.Boolean
            },
            accessingSupportSystems: {
                type: Schema.Types.Boolean
            },
            toiletTraining: {
                type: Schema.Types.Boolean
            },
            transportation: {
                type: Schema.Types.Boolean
            },
            otherSkills: {
                type: Schema.Types.String
            },

        }

    }
);