import * as mongoose from "mongoose";
import { Schema } from "mongoose";


export const SubstanceSchema = new mongoose.Schema({
    ageAtFirstUse: {
        type:Schema.Types.String,
        required: false
    },
    ageAtRegularUse: {
        type:Schema.Types.String,
        required: false
    },
    currentFrequencyAverageUse: {
        type:Schema.Types.String,
        required: false
    },
    methodOfAdministration: {
        type:Schema.Types.String,
        required: false
    },
    lastDateUsed: {
        type:Schema.Types.String,
        required: false
    },
    primarySecondaryOrTertiary: {
        type:Schema.Types.String,
        required: false
    },
    
    
}, { _id: false });