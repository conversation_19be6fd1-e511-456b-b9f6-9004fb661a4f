import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const HistoryofAbuseSchema = new mongoose.Schema(
    {
        physicalAbuse: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,
            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            },
        },
        sexualAbuse: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,
            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            },
        },
        emotionalAbuse: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,
            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            },
        },
        historyofNeglect: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,
            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            },
        },
        otherTrauma: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,
            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            },
        },
        currentInvolvementCPSDSSAPS: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,
            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            },
        },
        historyInvolvementCPSDSSAPS: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,
            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            },
        },
    },
    { _id: false }
);