import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { relationDescriotion } from "../../../models/sub-models/ClinicalAssesmentSubModels/houseHoldMember-model";

export const houseHoldMemberSchema = new mongoose.Schema(
    {
        firstName: {
            type: Schema.Types.String,
            required: false,
        },
        lastName: {
            type: Schema.Types.String,
            required: false,
        },
        age: {
            type: Schema.Types.Number,
            required: false,
        },
        gender: {
            type: Schema.Types.String,
            required: false,
        },
        consumerRelation: {
            type: Schema.Types.String,
            required: false,
        },
        relationDescription: {
            type: Schema.Types.String,
            enum: relationDescriotion,
            required: false,
        },

    }, { _id: false }
)