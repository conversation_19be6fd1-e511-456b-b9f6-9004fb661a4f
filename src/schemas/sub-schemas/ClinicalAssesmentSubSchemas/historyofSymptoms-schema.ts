import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const historyofSymptomsSchema = new mongoose.Schema(
    {
        impulsiveness: {
            type: Schema.Types.String,
            required: false,
        },
        irritability: {
            type: Schema.Types.String,
            required: false,
        },
        changeinHealthStatus: {
            type: Schema.Types.String,
            required: false,
        },
        InvoluntaryCommitment: {
            type: Schema.Types.String,
            required: false,
        },
        angerManagement: {
            type: Schema.Types.String,
            required: false,
        },
        legalInvolvement: {
            type: Schema.Types.String,
            required: false,
        },
        appetiteDisturbance: {
            type: Schema.Types.String,
            required: false,
        },
        impairedJudgment: {
            type: Schema.Types.String,
            required: false,
        },
        substanceAbuse: {
            type: Schema.Types.String,
            required: false,
        },
        truancy: {
            type: Schema.Types.String,
            required: false,
        },
        sleepDisturbance: {
            type: Schema.Types.String,
            required: false,
        },
        traumaticBrainInjury: {
            type: Schema.Types.String,
            required: false,
        },
        behaviorProblems: {
            type: Schema.Types.String,
            required: false,
        },
        missedWork: {
            type: Schema.Types.String,
            required: false,
        },
        delusions: {
            type: Schema.Types.String,
            required: false,
        },
        memoryLoss: {
            type: Schema.Types.String,
            required: false,
        },
        moodSwings: {
            type: Schema.Types.String,
            required: false,
        },
        otherHC: {
            type: Schema.Types.String,
            required: false,
        },
        other: {
            type: Schema.Types.String,
            required: false,
        },

    },
    { _id: false }
);