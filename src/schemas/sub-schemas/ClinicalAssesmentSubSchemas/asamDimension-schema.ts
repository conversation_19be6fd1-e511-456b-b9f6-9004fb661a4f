import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { level } from "../../../models/sub-models/ClinicalAssesmentSubModels/asamDimensions-model";

export const AsamDimensionsSchema = new mongoose.Schema(
    {
        intoxicationWithdrawal: {
            type: String,
            enum: level,
            required: false
        },
        medicalConditions: {
            type: String,
            enum: level,
            required: false
        },
        psychiatricCoMorbidities: {
            type: String,
            enum: level,
            required: false
        },
        mortivationforTreatment: {
            type: String,
            enum: level,
            required: false
        },
        relapsePrevention: {
            type: String,
            enum: level,
            required: false
        },
        recoveryEviornment: {
            type: String,
            enum: level,
            required: false
        }
    }
);