import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { SeverityScore } from "../../../models/sub-models/ClinicalAssesmentSubModels/statementBehaviour-model";



export const psychiatricSymptomSchema = new mongoose.Schema(
    {
        anxiety: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        tension: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        depressiveMood: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        helplessnessHopelessness: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        guiltFeelings: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        somaticConcern: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        hostility: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        suspiciousness: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        uncooperativeness: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        distractibility: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        elatedMood: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        motorHyperactivity: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        disorientation: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        disorganizedSpeech: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        grandioseStatements: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        unusualIdeas: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        hallucinatoryStatements: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        hallucinatoryBehavior: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        socialWithdrawal: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        bluntedAffect: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        motorRetardation: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        mannerismsPosturing: {
            type: String,
            enum: SeverityScore,
            required: true
        },
        lossOfFunction: {
            type: String,
            enum: SeverityScore,
            required: true
        }
    }, { _id: false }
);



