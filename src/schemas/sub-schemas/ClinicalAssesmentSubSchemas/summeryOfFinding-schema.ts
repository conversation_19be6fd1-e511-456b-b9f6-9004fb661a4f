import * as mongoose from "mongoose";
import { Schema } from "mongoose";


export const FindingsSchema = new mongoose.Schema(
    {
        code: {
            type: Schema.Types.String,
            required: false,
        },
        description: {
            type: Schema.Types.String,
            required: false,
        }
    }, { _id: false }
);

export const nonReportedOrDescribe = new mongoose.Schema(
    {
        nonReported: {
            type: Schema.Types.Boolean,
            required: false,
        },
        describePlan: {
            type: Schema.Types.String,
            required: false,
        }
    }, { _id: false }
);

export const SummeryOfFindingSchema = new mongoose.Schema(
    {
        summeryOfFindingNarrative: {
            type: Schema.Types.String,
            required: false,
        },
        I1: {
            type: FindingsSchema,
            required: false,
        },
        // I2: {
        //     type: FindingsSchema,
        //     required: false,
        // },
        II1: {
            type: FindingsSchema,
            required: false,
        },
        // II2: {
        //     type: FindingsSchema,
        //     required: false,
        // },
        III: {
            type: FindingsSchema,
            required: false,
        },
        IV: {
            type: FindingsSchema,
            required: false,
        },
        V: {
            type: FindingsSchema,
            required: false,
        },
        advanceDirectives: {
            type: nonReportedOrDescribe,
            required: false,
        }

    },
    { _id: false }
);