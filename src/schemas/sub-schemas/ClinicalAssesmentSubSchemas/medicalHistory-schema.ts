import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { MedicalHistoryTableSchema } from "./medicalHistoryTable-schema";

export const MedicalHistorySchema = new mongoose.Schema(
    {
        medicalHistoryTableSchema: [
            {
                type: MedicalHistoryTableSchema,
                required: false
            }
        ],
        baselineMammogram: {
            type: Schema.Types.String,
            required: false
        },
        strengths: {
            type: Schema.Types.String,
            required: false
        },
        needs: {
            type: Schema.Types.String,
            required: false
        },
        checkIfNone: {
            type: Schema.Types.Boolean,
            required: false
        }
    },
    { _id: false }
);