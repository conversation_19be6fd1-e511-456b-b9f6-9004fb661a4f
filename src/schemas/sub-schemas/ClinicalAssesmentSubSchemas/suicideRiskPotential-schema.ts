import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { lethalityAssessment } from "../../../models/sub-models/ClinicalAssesmentSubModels/suicideRiskPotential-model";


export const SuicideHomicideRiskPotentialSchema = new mongoose.Schema(
    {
        noRiskfactorsIdentified: {
            type: Schema.Types.Boolean,
            required: false
        },
        suicideAttempts: {
            actionMethod: {
                type: Schema.Types.String,
                required: false
            },
            Ideation: {
                type: Schema.Types.String,
                required: false
            },
            plan: {
                type: Schema.Types.String,
                required: false
            },
        },
        homicideAttempts: {
            actionMethod: {
                type: Schema.Types.String,
                required: false
            },
            Ideation: {
                type: Schema.Types.String,
                required: false
            },
            plan: {
                type: Schema.Types.String,
                required: false
            },
        },
        historyOfImpulsiveBehaviors: {
            type: Schema.Types.<PERSON>olean,
            required: false
        },
        safeEnviornment: {
            type: Schema.Types.<PERSON><PERSON><PERSON>,
            required: false
        },
        crueltytoAnimalsHistory: {
            type: Schema.Types.Boolean,
            required: false
        },
        fireSetting: {
            type: Schema.Types.Boolean,
            required: false
        },
        otherRiskFactors: {
            type: Schema.Types.String,
            required: false
        },
        lethalityAssessment: {
            type: Schema.Types.String,
            enum: lethalityAssessment,
            required: false
        },
        addtionalComments: {
            type: Schema.Types.String,
            required: false
        },
    },
    { _id: false }
)