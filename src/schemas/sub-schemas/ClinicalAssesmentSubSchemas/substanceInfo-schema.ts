import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { SubstanceSchema } from "./substance-Schema";




export const SubstanceInfoSchema = new mongoose.Schema(
    {

        substanceAbuseHistoryReported: {
            type: Schema.Types.Boolean,
            default: false
        },
        
        noSAHistoryReported: {
            type: Schema.Types.Boolean,
            required: false
        },

        nicotine: {
            type: SubstanceSchema,
            required: false
        },

        alcohol: {
            type: SubstanceSchema,
            required: false
        },

        marijuana: {
            type: SubstanceSchema,
            required: false
        },

        cocaineCrack: {
            type: SubstanceSchema,
            required: false
        },

        amphetamines: {
            type: SubstanceSchema,
            required: false
        },

        hallucinogens: {
            type: SubstanceSchema,
            required: false
        },

        ecstasyOther: {
            type: SubstanceSchema,
            required: false
        },

        inhalants: {
            type: SubstanceSchema,
            required: false
        },

        heroin: {
            type: SubstanceSchema,
            required: false
        },

        barbiturates: {
            type: SubstanceSchema,
            required: false
        },

        other: {
            type: SubstanceSchema,
            required: false
        },

        substanceMeans: {
            type: Schema.Types.String,
            required: false
        },
        mortivationForUse: {
            type: Schema.Types.String,
            required: false
        },
        useReductionInterest: {
            type: Schema.Types.String,
            required: false
        },
        pastAbstinence: {
            type: Schema.Types.String,
            required: false
        },
        problemUsageCaused: {
            type: Schema.Types.String,
            required: false
        },
        additionalHistory: {
            type: Schema.Types.String,
            enum: ["DWI", "Blackouts", "Absenteeism", "Seizures", "JobLoss", "IVDrugUse"],
            required: false
        },
        chargesHistory: {
            type: Schema.Types.String,
            required: false
        },
        sARelatedWithdrawalSymptoms: {
            past: {
                type: Schema.Types.String,
                required: false
            },
            present: {
                type: Schema.Types.String,
                required: false
            }
        },
        signsOfTolerance: {
            type: Schema.Types.String,
            required: false
        },
        isRecommended: {
            type: Schema.Types.Boolean,
            required: false
        },
        recommend: {
            type: Schema.Types.String,
            required: false
        },
    },
    { _id: false }
);