const mongoose = require('mongoose');
const Schema = mongoose.Schema;

export const EmploymentVocationalSchema = new mongoose.Schema(
    {
        checkIfNA: {
            type: Schema.Types.Boolean,
            required: false,
        },
        currentlyEmployed: {
            type: Schema.Types.Boolean,
            required: false,
        },
        currentPosition: {
            type: Schema.Types.String,
            required: false,
        },
        employedSince: {
            type: Schema.Types.String,
            required: false,
        },
        numJobsLastFiveYears: {
            type: Schema.Types.Number,
            required: false,
        },
        ssdiClaimStatus: {
            type: Schema.Types.String,
            required: false,
        },
        ssdiEligible: {
            type: Schema.Types.Boolean,
            required: false,
        },
        employSatisfaction: {
            type: Schema.Types.String,
            required: false,
        },
        employmentHistory: {
            type: Schema.Types.String,
            required: false,
        },
        barriersChallenges: {
            type: Schema.Types.String,
            required: false,
        },
        identifiedStrengths: {
            type: Schema.Types.String,
            required: false,
        },
        identifiedNeeds: {
            type: Schema.Types.String,
            required: false,
        },
    }
);