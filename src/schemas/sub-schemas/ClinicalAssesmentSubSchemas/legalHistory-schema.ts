import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { LegalImpairment } from "../../../models/sub-models/ClinicalAssesmentSubModels/LegalHistory-model";

export const LegalHistorySchema = new mongoose.Schema(
    {
        ifNoCurrent: {
            type: Schema.Types.Boolean,
            required: false,
            default: false
        },
        currentlegalCharges: {
            explanation: {
                type: Schema.Types.String,
                default: false
            },

        },
        probationOfficer: {
            name: {
                type: Schema.Types.String,
                default: false
            },

        },
        currentlyonProbation: {
            name: {
                type: Schema.Types.String,
                default: false
            },

        },
        historyofIncarcerations: {
            name: {
                type: Schema.Types.String,
                default: false
            },
            additionalInformation: {
                type: Schema.Types.Boolean,
                default: false
            }

        },
        historyoflegalInvolvement: {
            name: {
                type: Schema.Types.String,
                default: false
            },

        },
        familylegalInvolvement: {
            name: {
                type: Schema.Types.String,
                default: false
            },

        },
        specificTermsofProbation: {
            name: {
                type: Schema.Types.String,
                default: false
            },

        },
        legalImpairment: {
            name: {
                type: Schema.Types.String,
                enum: LegalImpairment,
                required:false
            },

        },


    },
    { _id: false }
);