import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ImpairmentLevel } from "../../../models/sub-models/ClinicalAssesmentSubModels/currentImpairments-model";



export const currentImpairmentsSchema = new Schema(
    {
        moodDisturbance: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        anxiety: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        psychosis: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        thinkingCognitionMemory: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        impulsiveRecklessAggressive: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        activitiesOfDailyLiving: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        weightLossAssocWithEatingDO: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        medicalPhysicalConditions: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        substanceAbuseDependence: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        schoolJobPerformance: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        socialMaritalFamilyProblems: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        },
        legal: {
            type: Schema.Types.String,
            enum: ImpairmentLevel,
            required: true,
            default: "N/A"
        }
    }, { _id: false }
);