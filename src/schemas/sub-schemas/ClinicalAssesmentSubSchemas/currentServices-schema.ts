import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const currentServicesSchema = new mongoose.Schema(
    {
        mentalHealth: {
            
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,

            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            }

        },
        substanceAbuse: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,

            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            }
        },
        medical: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,

            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            }
        },
        vocational: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,

            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            }
        },
        developmental: {
            nonReported: {
                type: Schema.Types.Boolean,
                required: false,

            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            }
        },
        other: {
            nonReported: {
                type: Schema.Types.<PERSON><PERSON><PERSON>,
                required: false,

            },
            explanation: {
                type: Schema.Types.String,
                required: false,
            }
        },
        treatmentAttitudeOverTime: {
            type: Schema.Types.String,
            required: false,
        },
        previousRecoveryFactors: {
            type: Schema.Types.String,
            required: false,
        },
        notes: {
            type: Schema.Types.String,
            required: false,
        },
    }, { _id: false }
);