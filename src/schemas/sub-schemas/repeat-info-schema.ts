import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { WorkingDaysSchema } from "./working-day-schema";
// import { WorkingDaysSchema } from "./working-days-schema";

export const RepeatInfoSchema = new mongoose.Schema(
  {
    repeatType: {
      type: Schema.Types.String,
      required: true,
    },
    interval: {
      type: Schema.Types.String,
      required: false,
    },
    repeatDays: {
      type: WorkingDaysSchema,
      required: false,
    },
    endingType: {
      type: Schema.Types.String,
      required: false,
    },
    endingDate: {
      type: Schema.Types.Date,
      required: false,
    },
    endingAfter: {
      type: Schema.Types.Number,
      required: false,
    },
  },
  { _id: false }
);
