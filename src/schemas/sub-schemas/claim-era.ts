import { CodeB<PERSON> } from "aws-sdk";
import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const ErrorCodeList = new mongoose.Schema(
  {
    code: {
      type: Schema.Types.String,
    },
    group: {
      type: Schema.Types.String,
    },
    amount: {
      type: Schema.Types.String,
    },
  },

  { _id: false }
);

export const ERACHargeList = new mongoose.Schema(
  {
    adjustment: [
      {
        type: ErrorCodeList,
      },
    ],
    chgid: {
      type: Schema.Types.String,
    },
    paid: {
      type: Schema.Types.String,
    },
    allowed: {
      type: Schema.Types.String,
    },
    proc_code: {
      type: Schema.Types.String,
    },
    charge: {
      type: Schema.Types.String,
    },
    thru_dos: {
      type: Schema.Types.String,
    },
    units: {
      type: Schema.Types.String,
    },
  },

  { _id: false }
);