import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { Client } from "socket.io/dist/client";
import { ClientSchema } from "../client-schema";
import ExperienceTag from "../experience-tag-schema";
import User from "../user-schema";

export const PreferencesSchema = new mongoose.Schema(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: User.modelName,
    },
    gender: {
      type: Schema.Types.String,
      required: false,
    },
    ethnicityId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    professionId: {
        type: Schema.Types.ObjectId,
        required: false,
      },
      experiencedIn: [
      {
        type: Schema.Types.ObjectId,
        required: false,
        ref: ExperienceTag.modelName,
       },
    ],
    yearsOfExperienceMin: {
        type: Schema.Types.Number,
        required: false,
    },
    yearsOfExperienceMax: {
        type: Schema.Types.Number,
        required: false,
    },
  },

  { _id: false }
);
