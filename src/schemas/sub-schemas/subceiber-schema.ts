import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { AddressSchema } from "./address-schema";
export const SubscriberSchema = new mongoose.Schema(
    {
        memberId: {
            type: Schema.Types.String,
            required: false,
        },
        paymentResponsibilityLevelCode: {
            type: Schema.Types.String,
            required: false,
        },
        firstName: {
            type: Schema.Types.String,
            required: false,
        },
        lastName: {
            type: Schema.Types.String,
            required: false,
        },
        gender: {
            type: Schema.Types.String,
            required: false,
        },
        dateOfBirth: {
            type: Schema.Types.String,
            required: false,
        },
        policyNumber: {
            type: Schema.Types.String,
            required: false,
        },
        address: {
            type: AddressSchema
        },
    },

    { _id: false }
);
