import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const AmeriGeneralInformationSchema = new mongoose.Schema(
    {
        memberName: {
            type: Schema.Types.String,
            required: false
        },
        dateOfBirth: {
            type: Schema.Types.Date,
            required: false
        },
        memberAddress: {
            type: Schema.Types.String,
            required: false
        },
        medicaidID: {
            type: Schema.Types.String,
            required: false
        },
        diagnosisCode: {
            type: Schema.Types.String,
            required: false
        },
        diagnosisDescription: {
            type: Schema.Types.String,
            required: false
        },
        facilityName: {
            type: Schema.Types.String,
            required: false
        },
        treatmentType: {
            inpatient: {
                type: Schema.Types.Boolean,
                required: false
            },
            outpatient: {
                type: Schema.Types.Boolean,
                required: false
            }
        }
    },
    { _id: false }
);
