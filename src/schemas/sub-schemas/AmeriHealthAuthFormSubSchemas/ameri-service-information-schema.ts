import * as mongoose from "mongoose";
import { Schema } from "mongoose";

const AmeriServiceInformationArraySchema = new mongoose.Schema({
    referenceNumber: {
        type: Schema.Types.String,
        required: false
    },
    procedureCode: {
        type: Schema.Types.String,
        required: false
    },
    startDate: {
        type: Schema.Types.Date,
        required: false
    },
    endDate: {
        type: Schema.Types.Date,
        required: false
    },
    serviceDescription: {
        type: Schema.Types.String,
        required: false
    },
    itemQuantity: {
        type: Schema.Types.String,
        required: false
    }
}, { _id: false }); 

export const AmeriServiceInformationSchema = new mongoose.Schema({
    serviceInformationArray: {
        type: [AmeriServiceInformationArraySchema], 
        required: false
    },
    necessityExplanation: {
        type: Schema.Types.String,
        required: false
    }
});

