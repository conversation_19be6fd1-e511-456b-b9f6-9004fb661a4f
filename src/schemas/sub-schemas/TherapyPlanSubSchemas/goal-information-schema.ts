import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const GoalInformationSchema = new mongoose.Schema(
    {
        goalObjective: {
            type: Schema.Types.String,
            required: false
        },
        progressIdentifier:{
            type: Schema.Types.String,
            required: false
        },
        timeframe:{
            type: Schema.Types.String,
            required: false
        },
        serviceInterventionFrequency: {
            type: Schema.Types.String,
            required: false
        },
        responsibleParties: {
            clientName: {
                type: Schema.Types.String,
                required: false
            },
            therapistNameCredentials: {
                type: Schema.Types.String,
                required: false
            },
        },
    },
    { _id: false }
);