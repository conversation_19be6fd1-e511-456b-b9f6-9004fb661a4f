import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const SignatureDetailsSchema = new mongoose.Schema(
    {
        name: {
            type: Schema.Types.String,
            required: false
        },
        date: {
            type: Schema.Types.Date,
            required: false
        },
        relationship: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false }
);