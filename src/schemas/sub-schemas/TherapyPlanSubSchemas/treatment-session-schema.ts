import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { treatmentGoalStatusCode } from "../../../models/sub-models/TherapyPlanSubModels/treatment-goals-status-model";

export const TreatmentSessionSchema = new mongoose.Schema(
    {
        targetDate: {
            type: Schema.Types.String,
            required: false
        },
        reviewDate: {
            type: Schema.Types.String,
            required: false
        },
        statusCode: {
            type: Schema.Types.String,
            enum: treatmentGoalStatusCode,
            required: false,
        },
        notes: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false }
);