import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const UnitedHealthCarePrescriberInformationSchema = new mongoose.Schema(
    {
        providerName: {
            type: Schema.Types.String,
            required: false
        },
        npi: {
            type: Schema.Types.String,
            required: false
        },
        specialty: {
            type: Schema.Types.String,
            required: false
        },
        officePhone: {
            type: Schema.Types.String,
            required: false
        },
        officeFax: {
            type: Schema.Types.String,
            required: false
        },
        officeAddress: {
            type: Schema.Types.String,
            required: false
        },
        city: {
            type: Schema.Types.String,
            required: false
        },
        state: {
            type: Schema.Types.String,
            required: false
        },
        zipCode: {
            type: Schema.Types.String,
            required: false
        },
        providerAddress: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false }  
);
