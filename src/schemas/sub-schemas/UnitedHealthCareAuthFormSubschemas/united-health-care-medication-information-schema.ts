import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const UnitedHealthCareMedicationInformationSchema = new mongoose.Schema(
    {
        medication: {
            type: Schema.Types.String,
            required: false
        },
        strength: {
            type: Schema.Types.String,
            required: false
        },
        useDirections: {
            type: Schema.Types.String,
            required: false
        },
        quantity: {
            type: Schema.Types.String,
            required: false
        },
        medicationAdministered: {
            selfAdministered: {
                type: Schema.Types.Boolean,
                required: false
            },
            physicianOffice: {
                type: Schema.Types.Boolean,
                required: false
            },
            other: {
                type: Schema.Types.Boolean,
                required: false
            },
            otherDescription: {
                type: Schema.Types.String,
                required: false
            }
        }
    },
    { _id: false }  
);
