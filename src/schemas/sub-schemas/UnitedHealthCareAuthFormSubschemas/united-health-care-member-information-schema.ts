import * as mongoose from "mongoose";
import { Schema } from "mongoose";


export const UnitedHealthCareMemberInformationSchema = new mongoose.Schema(
    {
        memberName: {
            type: Schema.Types.String,
            required: false
        },
        medicaidID: {
            type: Schema.Types.String,
            required: false
        },
        dateOfBirth: {
            type: Schema.Types.Date,
            required: false
        },
        streetAddress: {
            type: Schema.Types.String,
            required: false
        },
        city: {
            type: Schema.Types.String,
            required: false
        },
        state: {
            type: Schema.Types.String,
            required: false
        },
        zipCode: {
            type: Schema.Types.String,
            required: false
        },
        phone: {
            type: Schema.Types.String,
            required: false
        },
        allergies: {
            type: Schema.Types.String,
            required: false
        },
        isRequestedMedication: {
            new: {
                type: Schema.Types.Boolean,
                required: false
            },
            continuationDate: {
                type: Schema.Types.Date,
                required: false
            }
        },
        isHospitalized: {
            yes: {
                type: Schema.Types.Boolean,
                required: false
            },
            dischargeDate: {
                type: Schema.Types.Date,
                required: false
            }
        },
        isPregnant: {
            yes: {
                type: Schema.Types.Boolean,
                required: false
            },
            dueDate: {
                type: Schema.Types.Date,
                required: false
            }
        },
    },
    { _id: false }  
);

