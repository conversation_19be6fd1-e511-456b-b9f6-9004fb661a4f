import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const UnitedHealthCareClinicalInformationSchema = new mongoose.Schema(
    {
        diagnosisForMedication: {
            type: Schema.Types.String,
            required: false
        },
        icdCode: {
            type: Schema.Types.String,
            required: false
        },
        medicationHistoryOfFailure: {
            type: Schema.Types.String,
            required: false
        },
        medicationContraindication: {
            type: Schema.Types.String,
            required: false
        },
        labResults: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false } 
);
