import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const AmbetterProviderSchema = new mongoose.Schema(
    {
        requestingNpi: {
            type: Schema.Types.String,
            required: true
        },
        requestingTin: {
            type: Schema.Types.String,
            required: true
        },
        requestingProviderName: {
            type: Schema.Types.String,
            required: false
        },
        requestingProviderContactName: {
            type: Schema.Types.String,
            required: false
        },
        phone: {
            type: Schema.Types.String,
            required: false
        },
        faxNumber: {
            type: Schema.Types.String,
            required: true
        }
    },
    { _id: false }  
);
