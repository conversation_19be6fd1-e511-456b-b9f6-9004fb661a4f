import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const AmbetterFacilityInformationSchema = new mongoose.Schema(
    {
        servicingNpi: {
            type: Schema.Types.String,
            required: true
        },
        servicingTin: {
            type: Schema.Types.String,
            required: true
        },
        servicingFacilityName: {
            type: Schema.Types.String,
            required: false
        },
        servicingProviderContactName: {
            type: Schema.Types.String,
            required: false
        },
        phone: {
            type: Schema.Types.String,
            required: false
        },
        faxNumber: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false }  
);
