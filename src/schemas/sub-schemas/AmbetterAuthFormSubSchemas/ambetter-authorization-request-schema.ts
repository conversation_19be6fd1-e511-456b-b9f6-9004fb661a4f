import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const AmbetterAuthorizationRequestSchema = new mongoose.Schema(
    {
        primaryProcedureCode: {
            type: Schema.Types.String,
            required: true
        },
        primaryProcedureCodeModifier: {
            type: Schema.Types.String,
            required: true
        },
        additionalProcedureCode: {
            type: Schema.Types.String,
            required: true
        },
        additionalProcedureCodeModifier: {
            type: Schema.Types.String,
            required: true
        },
        startDate: {
            type: Schema.Types.Date,
            required: false
        },
        endDate: {
            type: Schema.Types.Date,
            required: false
        },
        diagnosisCode: {
            type: Schema.Types.String,
            required: false
        },
        totalUnit: {
            type: Schema.Types.String,
            required: true
        },
        serviceTypeNumber: {
            type: Schema.Types.String,
            required: false
        },
        purchasePrice: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false }  
);
