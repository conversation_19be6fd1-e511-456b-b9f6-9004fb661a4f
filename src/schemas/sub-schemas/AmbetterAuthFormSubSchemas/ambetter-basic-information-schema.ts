import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const AmbetterBasicInformationSchema = new mongoose.Schema(
    {
        additionalRequest: {
            type: Schema.Types.Boolean,
            required: false
        },
        existingAuthorization: {
            type: Schema.Types.String,
            required: false
        },
        unit: {
            type: Schema.Types.String,
            required: false
        },
        standardRequest: {
            type: Schema.Types.Boolean,
            required: false
        },
        urgentRequest: {
            type: Schema.Types.Boolean,
            required: false
        },
        signature: {
            type: Schema.Types.Buffer,
            required: false
        }
    },
    { _id: false }  
);
