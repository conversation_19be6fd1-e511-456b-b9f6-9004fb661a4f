import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { RequestType } from "../../../models/sub-models/WellcareAuthFormSubModels/wellcare-authorization-model";

export const WellCareAuthorizationSchema = new mongoose.Schema(
    {
        date: {
            type: Schema.Types.Date,
            required: false
        },
        requestingProvider: {
            type: Schema.Types.String,
            required: false
        },
        npi: {
            type: Schema.Types.String,
            required: false
        },
        tin: {
            type: Schema.Types.String,
            required: false
        },
        contactName: {
            type: Schema.Types.String,
            required: false
        },
        phoneNumber: {
            type: Schema.Types.String,
            required: false
        },
        faxNumber: {
            type: Schema.Types.String,
            required: false
        },
        typeOfRequest: {
            type: Schema.Types.String,
            enum: RequestType, 
            required: false
        }
    },
    { _id: false }  
);
