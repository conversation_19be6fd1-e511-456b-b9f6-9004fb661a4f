import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const WellCarePlaceTypeSchema = new mongoose.Schema(
    {
        office: {
            type: Schema.Types.Boolean,
            required: false
        },
        home: {
            type: Schema.Types.Boolean,
            required: false
        },
        inpatient: {
            type: Schema.Types.Boolean,
            required: false
        },
        outpatient: {
            type: Schema.Types.Boolean,
            required: false
        },
        ambulatorySurgery: {
            type: Schema.Types.Boolean,
            required: false
        },
        landAmbulance: {
            type: Schema.Types.Boolean,
            required: false
        },
        airAmbulance: {
            type: Schema.Types.Boolean,
            required: false
        },
        inpatientPsychiatric: {
            type: Schema.Types.Boolean,
            required: false
        },
        communityMentalHealth: {
            type: Schema.Types.Boolean,
            required: false
        },
        other: {
            type: Schema.Types.Boolean,
            required: false
        },
        detailsForOther: {
            type: Schema.Types.String,
            required: false
        },
    },
    { _id: false }  
);
