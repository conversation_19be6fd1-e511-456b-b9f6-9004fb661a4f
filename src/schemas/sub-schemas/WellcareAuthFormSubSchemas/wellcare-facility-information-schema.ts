import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const WellCareFacilityInformationSchema = new mongoose.Schema(
    {
        facilityName: {
            type: Schema.Types.String,
            required: false
        },
        wellcareId: {
            type: Schema.Types.String,
            required: false
        },
        npi: {
            type: Schema.Types.String,
            required: false
        },
        taxId: {
            type: Schema.Types.String,
            required: false
        },
        address: {
            type: Schema.Types.String,
            required: false
        },
        city: {
            type: Schema.Types.String,
            required: false
        },
        state: {
            type: Schema.Types.String,
            required: false
        },
        zipCode: {
            type: Schema.Types.String,
            required: false
        },
        phoneNumber: {
            type: Schema.Types.String,
            required: false
        },
        faxNumber: {
            type: Schema.Types.String,
            required: false
        },
        isDocumentsIncluded: {
            type: Schema.Types.Boolean,
            required: false
        },
        documentsAmount: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false }  
);
