import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const WellCareMemberInformationSchema = new mongoose.Schema(
    {
        memberName: {
            type: Schema.Types.String,
            required: false
        },
        medicaidId: {
            type: Schema.Types.String,
            required: false
        },
        wellcareId: {
            type: Schema.Types.String,
            required: false
        },
        memberAddress: {
            type: Schema.Types.String,
            required: false
        },
        dateOfBirth: {
            type: Schema.Types.Date,
            required: false
        },
        isPregnant: {
            type: Schema.Types.Boolean,
            required: false
        },
        memberPcp: {
            type: Schema.Types.String,
            required: false
        },
        phoneNumber: {
            type: Schema.Types.String,
            required: false
        },
        npi: {
            type: Schema.Types.String,
            required: false
        }
    },
    { _id: false } 
);
