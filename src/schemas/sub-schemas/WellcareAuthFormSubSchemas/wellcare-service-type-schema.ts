import * as mongoose from "mongoose";
import { Schema } from "mongoose";

const BehaviourServiceSchema = new mongoose.Schema({
    intensiveOutpatient: { type: Schema.Types.Boolean, required: false },
    csu: { type: Schema.Types.Boolean, required: false },
    inpatient: { type: Schema.Types.Boolean, required: false },
    subAcute: { type: Schema.Types.Boolean, required: false },
    detox: { type: Schema.Types.Boolean, required: false },
    routineOutpatient: { type: Schema.Types.Boolean, required: false },
    caseManagement: { type: Schema.Types.Boolean, required: false },
    residential: { type: Schema.Types.Boolean, required: false },
    rehabilitation: { type: Schema.Types.Boolean, required: false },
    ect: { type: Schema.Types.Boolean, required: false },
    other: { type: Schema.Types.Boolean, required: false },
    detailsForOther: { type: Schema.Types.String, required: false },
}, { _id: false });

const MedicalServiceSchema = new mongoose.Schema({
    dmePurchase: { type: Schema.Types.Boolean, required: false },
    dmeRental: { type: Schema.Types.Boolean, required: false },
    homeHealth: { type: Schema.Types.Boolean, required: false },
    inpatientAdmission: { type: Schema.Types.Boolean, required: false },
    inpatientRehab: { type: Schema.Types.Boolean, required: false },
    ltach: { type: Schema.Types.Boolean, required: false },
    skilledTherapy: { type: Schema.Types.Boolean, required: false },
    snf: { type: Schema.Types.Boolean, required: false },
    surgeryOutpatient: { type: Schema.Types.Boolean, required: false },
    surgeryInpatient: { type: Schema.Types.Boolean, required: false },
    other: { type: Schema.Types.Boolean, required: false },
    detailsForOther: { type: Schema.Types.String, required: false },

}, { _id: false });

const TransportationSchema = new mongoose.Schema({
    air: { type: Schema.Types.Boolean, required: false },
    land: { type: Schema.Types.Boolean, required: false },
    mileage: { type: Schema.Types.String, required: false },
    trip: { type: Schema.Types.String, required: false },
    isO2Needed: { type: Schema.Types.Boolean, required: false },
    pickUpAddress: { type: Schema.Types.String, required: false },
    dropOffAddress: { type: Schema.Types.String, required: false }
}, { _id: false });

export const WellCareServiceTypeSchema = new mongoose.Schema({
    behaviourService: { type: BehaviourServiceSchema, required: false },
    medicalService: { type: MedicalServiceSchema, required: false },
    transpotation: { type: TransportationSchema, required: false }
}, { _id: false });
