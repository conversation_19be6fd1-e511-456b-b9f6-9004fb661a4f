import * as mongoose from "mongoose";
import { Schema } from "mongoose";


const WellCareClinicalInformationArraySchema = new mongoose.Schema({
    startDate: {
        type: Schema.Types.Date,
        required: false
    },
    endDate: {
        type: Schema.Types.Date,
        required: false
    },
    procedureCode: {
        type: Schema.Types.String,
        required: false
    },
    description: {
        type: Schema.Types.String,
        required: false
    },
    requestedUnit: {
        type: Schema.Types.String,
        required: false
    }
}, { _id: false }); 

export const WellCareClinicalInformationSchema = new mongoose.Schema({
    clinicalInformationArray: {
        type: [WellCareClinicalInformationArraySchema], 
        required: false
    },
    icdCode1: {
        type: Schema.Types.String,
        required: false
    },
    icdCode2: {
        type: Schema.Types.String,
        required: false
    },
    icdCode3: {
        type: Schema.Types.String,
        required: false
    },
    icdCode4: {
        type: Schema.Types.String,
        required: false
    }
});