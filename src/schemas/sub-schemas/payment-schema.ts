import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const PaymentSchema = new mongoose.Schema(
  {
    cardNo: {
      type: Schema.Types.String,
      required: true,
    },
    expYear: {
      type: Schema.Types.String,
      required: true,
    },
    expMonth: {
      type: Schema.Types.String,
      required: true,
    },
    cvv: {
      type: Schema.Types.String,
      required: true,
    },
    isDefault: {
      type: Schema.Types.Boolean,
      required: true,
    },
  },

  { _id: false }
);
