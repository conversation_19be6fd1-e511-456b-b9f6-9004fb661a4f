import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const BlockDateSchema = new mongoose.Schema(
  {
    title: {
      type: Schema.Types.String,
      required: true,
    },
    start: {
      type: Schema.Types.Date,
      require: true
    },
    end: {
      type: Schema.Types.Date,
      require: true
    },
    display: {
      type: Schema.Types.String,
      required: true,
    },
    className: {
      type: Schema.Types.String,
      required: true,
    }
  },

  { _id: false }
);