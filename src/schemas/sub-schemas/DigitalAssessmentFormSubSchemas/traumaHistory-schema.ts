import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const TraumaHistorySchema = new mongoose.Schema(
  {
    isBadAccident: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    badAccidentDescribe: {
      type: Schema.Types.String,
      required: false,
    },
    isNaturalDisaster: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    naturalDisasterdDescribe: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    isMilitaryCombat: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    militoryCombatDescribe: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    isSeriousAttack: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    seriousAttackDescribe: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    isSexualCoercion: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    ageOfSexualCoercion: {
      type: Schema.Types.Number,
      required: false,
      default: null,
    },
    describeSexualCoercion: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    isTraumaticExperience: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    describeTraumaticExperience: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    intrusiveThoughts: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    avoidanceBehavior: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    hypervigilance: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    emotionalDetachment: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    selfBlame: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
  },
  { _id: false }
);
