import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const PsychosisSchema = new mongoose.Schema(
  {
    delusions: {
      type: Schema.Types.String,
      required: false,
    },
    paranoia: {
      type: Schema.Types.String,
      required: false,
    },
    sensoryHallucinations: {
      type: Schema.Types.String,
      required: false,
    },
    auditoryHallucinations: {
      type: Schema.Types.String,
      required: false,
    },
    visualHallucinations: {
      type: Schema.Types.String,
      required: false,
    },
    none: {
      type: Schema.Types.String,
      required: false,
    },
    ideasOfReference: {
      type: Schema.Types.String,
      required: false,
    },
    disorganizedSpeech: {
      type: Schema.Types.String,
      required: false,
    },
    other: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
