import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const MentalStatusSchema = new mongoose.Schema(
  {
    physicalStature: {
      small: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      average: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      tall: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    weight: {
      thin: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      average: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      overweight: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      obese: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    grooming: {
      wellGroomed: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      normal: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      neglected: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      bizarre: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    clothing: {
      neatClean: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      inappropriate: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      dirty: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      seductive: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      disheveled: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      bizarre: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    posture: {
      normal: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      tense: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      stooped: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      rigid: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      slumped: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      bizarre: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    attitude: {
      cooperative: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      passive: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      guarded: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      irritable: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      manipulative: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      seductive: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      suspicious: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      defensive: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      dramatic: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      silly: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      hostile: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      critical: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      resistant: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      sarcastic: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      uninterested: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      argumentative: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    motor: {
      nonremarkable: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      tremor: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      slowed: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      tics: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      restless: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      agitated: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    speech: {
      normal: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      rapid: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      slurred: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      loud: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      paucity: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      pressured: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      mute: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    affect: {
      appropriate: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      inappropriate: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      flat: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      restricted: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      blunted: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      labile: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    mood: {
      euthymic: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      confused: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      pessimistic: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      depressed: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      anxious: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      euphoric: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      apathetic: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      angry: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    thoughtForm: {
      goaldirected: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      appropriate: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      logical: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      tangentialthinking: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      circumstantial: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      looseassociations: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      confused: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      incoherent: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      perseverations: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      flightofidea: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      slownessofthought: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    thoughtContent: {
      appropriate: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      paranoid: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      suspicions: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      persecutions: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      paucity: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      delusions: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      bizarre: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      hypochondriac: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      ideasofreference: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    preoccupations: {
      phobias: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      guilt: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      other: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      somatic: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      religion: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      suicide: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      homicidal: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    hallucinations: {
      auditory: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      other: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      visual: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      sensory: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    orientation: {
      person: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      place: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      time: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      situation: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    levelOfIntellectualFunctioning: {
      belowAverage: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      average: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      aboveAverage: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    fundofKnowledge: {
      belowAverage: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      average: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      aboveAverage: {
        type: Schema.Types.Boolean,
        required: false,
      },
    },
    judgment: {
      belowAverage: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      average: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      aboveAverage: {
        type: Schema.Types.Boolean,
        required: false,
      },
    },
    insightIntoProblems: {
      belowAverage: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      average: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      aboveAverage: {
        type: Schema.Types.Boolean,
        required: false,
      },
    },
    clinicalImpressionSummary: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
