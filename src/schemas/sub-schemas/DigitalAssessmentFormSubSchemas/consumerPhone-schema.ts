import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const ConsumerPhoneSchema = new mongoose.Schema(
  {
    consumerPhone: {
      home: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      work: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      cellPhone: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      other: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    otherConsumerPhoneType: {
      type: Schema.Types.String,
      required: false,
    },
    otherConsumerPhoneNumber: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
