import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const NutritionalStatusSchema = new mongoose.Schema(
  {
    increasedAppetite: {
      type: Schema.Types.String,
      required: false,
    },
    weightloss: {
      type: Schema.Types.String,
      required: false,
    },
    bingeing: {
      type: Schema.Types.String,
      required: false,
    },
    dentalProblems: {
      type: Schema.Types.String,
      required: false,
    },
    decreasedAppetite: {
      type: Schema.Types.String,
      required: false,
    },
    eatingdisorder: {
      type: Schema.Types.String,
      required: false,
    },
    foodAllergies: {
      type: Schema.Types.String,
      required: false,
    },
    weightgain: {
      type: Schema.Types.String,
      required: false,
    },
    noDifficulties: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
