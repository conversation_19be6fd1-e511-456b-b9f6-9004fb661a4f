import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const BioPsychosocialEducationSchema = new mongoose.Schema(
  {
    isApplicable: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    schoolGradeName: {
      type: Schema.Types.String,
      required: false,
    },
    lastIep: {
      type: Schema.Types.String,
      required: false,
    },
    teacher: {
      type: Schema.Types.String,
      required: false,
    },
    learningAbility: {
      type: Schema.Types.String,
      required: false,
    },
    disabilityArea: {
      type: Schema.Types.String,
      required: false,
    },
    educationalProblems: {
      type: Schema.Types.String,
      required: false,
    },
    behaviorProblem: {
      type: Schema.Types.String,
      required: false,
    },
    repetedGrades: {
      type: Schema.Types.String,
      required: false,
    },
    socialInteraction: {
      type: Schema.Types.String,
      required: false,
    },
    suspension: {
      type: Schema.Types.String,
      required: false,
    },
    expulsion: {
      type: Schema.Types.String,
      required: false,
    },
    isPsychologicalTestingCompleted: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    date: {
      type: Schema.Types.Date,
      required: false,
    },
    expulsionTestResults: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
