import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const ProblemWithUseSchema = new mongoose.Schema(
  {
    tolerance: {
      type: Schema.Types.String,
      required: false,
    },
    withdrawal: {
      type: Schema.Types.String,
      required: false,
    },
    social: {
      type: Schema.Types.String,
      required: false,
    },
    legal: {
      type: Schema.Types.String,
      required: false,
    },
    financial: {
      type: Schema.Types.String,
      required: false,
    },
    increasedAmount: {
      type: Schema.Types.String,
      required: false,
    },
    desireToCutDown: {
      type: Schema.Types.String,
      required: false,
    },
    usewhilehazardous: {
      type: Schema.Types.String,
      required: false,
    },
    other: {
      type: Schema.Types.String,
      required: false,
    },
    occupational: {
      type: Schema.Types.String,
      required: false,
    },
    timeDevoted: {
      type: Schema.Types.String,
      required: false,
    },
    physicalAndOrPsychological: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
