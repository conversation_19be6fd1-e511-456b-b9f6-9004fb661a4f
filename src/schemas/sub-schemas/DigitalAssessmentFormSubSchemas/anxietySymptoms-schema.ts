import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const AnxietySymptomsSchema = new mongoose.Schema(
  {
    anxietyWorry: {
      type: Schema.Types.String,
      required: false,
    },
    avoidance: {
      type: Schema.Types.String,
      required: false,
    },
    panicAttacks: {
      type: Schema.Types.String,
      required: false,
    },
    obsessions: {
      type: Schema.Types.String,
      required: false,
    },
    compulsions: {
      type: Schema.Types.String,
      required: false,
    },
    markedlyDiminished: {
      type: Schema.Types.String,
      required: false,
    },
    intensePsychological: {
      type: Schema.Types.String,
      required: false,
    },
    feelingOFDetachment: {
      type: Schema.Types.String,
      required: false,
    },
    somaticComplaints: {
      type: Schema.Types.String,
      required: false,
    },
    dissociativeEpisodes: {
      type: Schema.Types.String,
      required: false,
    },
    restrictedAffect: {
      type: Schema.Types.String,
      required: false,
    },
    intrusivereOccurringThoughts: {
      type: Schema.Types.String,
      required: false,
    },
    difficultyConcentrating: {
      type: Schema.Types.String,
      required: false,
    },
    hypervigilance: {
      type: Schema.Types.String,
      required: false,
    },
    exaggeratedStartleResponse: {
      type: Schema.Types.String,
      required: false,
    },
    nightmaresReoccurringDreams: {
      type: Schema.Types.String,
      required: false,
    },
    irritabilityOrOutburstsOfAnger: {
      type: Schema.Types.String,
      required: false,
    },
    senseOfForeshortenedFuture: {
      type: Schema.Types.String,
      required: false,
    },
    avoidanceOfActivities: {
      type: Schema.Types.String,
      required: false,
    },
    unwantedMemories: {
      type: Schema.Types.String,
      required: false,
    },
    none: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
