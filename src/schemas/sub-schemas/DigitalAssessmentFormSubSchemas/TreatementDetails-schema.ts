import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const TreatementDetailsSchema = new mongoose.Schema(
  {
    medication: {
      type: Schema.Types.String,
      required: false,
    },
    does: {
      type: Schema.Types.String,
      required: false,
    },
    frequency: {
      type: Schema.Types.String,
      required: false,
    },
    lastUse: {
      type: Schema.Types.Date,
      required: false,
    },
  },
  { _id: false }
);
