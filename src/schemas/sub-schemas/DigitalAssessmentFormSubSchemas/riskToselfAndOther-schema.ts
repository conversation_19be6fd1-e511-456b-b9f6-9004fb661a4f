import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { CurrentRiskToSelfSchema } from "./currentRiskToSelf-schema";
import { CurrentRiskToOtherSchema } from "./currentRiskToOthers-schema";
import { HistoryOfSuicidalBehaviorSchema } from "./historyOfSuicidalBehavior-schema";
import { EvaluationOfRiskSchema } from "./evaluationOfRisk-schema";

export const RiskToSelfAndOtherSchema = new mongoose.Schema(
  {
    currentRiskToSelf: {
      type: CurrentRiskToSelfSchema,
      required: false,
    },
    currentRiskToOthers: {
      type: CurrentRiskToOtherSchema,
      required: false,
    },
    historyOfSuicidalBehavior: {
      type: HistoryOfSuicidalBehaviorSchema,
      required: false,
    },
    evaluationOfRisk: {
      type: EvaluationOfRiskSchema,
      required: false,
    },
  },
  { _id: false }
);
