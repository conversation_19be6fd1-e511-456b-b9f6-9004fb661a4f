import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const DepressiveSymptomsSchema = new mongoose.Schema(
  {
    sadnessFeelsEmpty: {
      type: Schema.Types.String,
      required: false,
    },
    insomniaHypersomnia: {
      type: Schema.Types.String,
      required: false,
    },
    fatigueOrLossOfEnergy: {
      type: Schema.Types.String,
      required: false,
    },
    crying: {
      type: Schema.Types.String,
      required: false,
    },
    psychomotorAgitationOrRetardation: {
      type: Schema.Types.String,
      required: false,
    },
    diminishedAbilityToThinkOrConcentrate: {
      type: Schema.Types.String,
      required: false,
    },
    diminishedInterest: {
      type: Schema.Types.String,
      required: false,
    },
    significantWeightLossorGainORDecrease: {
      type: Schema.Types.String,
      required: false,
    },
    recurrentThoughtsOfDeath: {
      type: Schema.Types.String,
      required: false,
    },
    feelingsOfWorthlessness: {
      type: Schema.Types.String,
      required: false,
    },
    none: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
