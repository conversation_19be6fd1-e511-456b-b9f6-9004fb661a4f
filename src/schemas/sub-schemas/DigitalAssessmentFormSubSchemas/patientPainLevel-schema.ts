import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { TreatementDetailsSchema } from "./TreatementDetails-schema";
import { DisabilityStatusSchema } from "./disabilityStatus-schema";

export const PatientPainLevelSchema = new mongoose.Schema(
  {
    currentLevelOfPhysicalPain: {
      type: Schema.Types.Number,
      required: false,
    },
    isCurrentReceivingTreatementPain: {
      type: Schema.Types.String,
      required: false,
    },
    isReceivingTreatement: {
      type: Schema.Types.String,
      required: false,
    },
    treatementDetails: [
      {
        type: TreatementDetailsSchema,
        required: false,
      },
    ],
    disablityStatus: {
      type: DisabilityStatusSchema,
      required: false,
    },
    adjustDisability: {
      type: Schema.Types.String,
      required: false,
    },
    requireEquipmentOrServices: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
