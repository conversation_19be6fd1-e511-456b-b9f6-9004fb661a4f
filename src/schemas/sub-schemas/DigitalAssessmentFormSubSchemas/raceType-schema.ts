import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const RaceTypeSchema = new mongoose.Schema(
  {
    race: {
      blackAfricanAmerican: {
        type: Schema.Types.Boolean,
        required: false,
      },
      alaskaNative: {
        type: Schema.Types.Boolean,
        required: false,
      },
      whiteCaucasian: {
        type: Schema.Types.Boolean,
        required: false,
      },
      asian: {
        type: Schema.Types.Boolean,
        required: false,
      },
      americanIndianNativeAmerican: {
        type: Schema.Types.Boolean,
        required: false,
      },
      pacificIslander: {
        type: Schema.Types.Boolean,
        required: false,
      },
      multiracial: {
        type: Schema.Types.Boolean,
        required: false,
      },
      other: {
        type: Schema.Types.Boolean,
        required: false,
      },
    },
    otherRace: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
