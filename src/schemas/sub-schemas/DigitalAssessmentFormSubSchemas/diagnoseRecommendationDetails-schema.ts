import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { DiagnoseSchema } from "./diagnose-schema";
import { RecommendationsSchema } from "./recommendations-schema";

export const DiagnoseRecommendationDetailsSchema = new mongoose.Schema(
  {
    diagnosis: [
      {
        type: DiagnoseSchema,
        required: false,
      },
    ],

    recommendation: {
      type: RecommendationsSchema,
      required: false,
    },
    printedName: {
      type: Schema.Types.String,
      required: false,
    },
    signature: {
      type: Schema.Types.String,
      required: false,
    },
    clientSignature: {
      type: Schema.Types.String,
      required: false,
    },
    date: {
      type: Schema.Types.Date,
      required: false,
    },
    dateOfClinicianSignature: {
      type: Schema.Types.Date,
      required: false,
    },
  },
  { _id: false }
);
