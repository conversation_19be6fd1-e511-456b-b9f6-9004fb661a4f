import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const DisabilityStatusSchema = new mongoose.Schema(
  {
    hearingImpairment: {
      type: Schema.Types.String,
      required: false,
    },
    sightImpairment: {
      type: Schema.Types.String,
      required: false,
    },
    intellectualDevelopmentalDisability: {
      type: Schema.Types.String,
      required: false,
    },
    other: {
      type: Schema.Types.String,
      required: false,
    },
    none: {
      type: Schema.Types.String,
      required: false,
    },
    comment: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
