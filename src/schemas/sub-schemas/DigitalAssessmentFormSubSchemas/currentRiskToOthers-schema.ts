import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const CurrentRiskToOtherSchema = new mongoose.Schema(
  {
    isHomicidalThreats: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    isPersistentIdeation: {
      type: Schema.Types.Boolean,
      required: false,
    },
    isSpecificPlan: {
      type: Schema.Types.Boolean,
      required: false,
    },
    isLethalIntent: {
      type: Schema.Types.Boolean,
      required: false,
    },
    isHallucinationCommands: {
      type: Schema.Types.Boolean,
      required: false,
    },
    describeHallucination: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
