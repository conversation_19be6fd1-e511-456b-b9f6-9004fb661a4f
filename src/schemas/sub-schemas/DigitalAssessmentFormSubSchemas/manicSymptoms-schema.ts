import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const ManicSymptomsSchema = new mongoose.Schema(
  {
    irrationalAnger: {
      type: Schema.Types.String,
      required: false,
    },
    decreasedSleep: {
      type: Schema.Types.String,
      required: false,
    },
    irritability: {
      type: Schema.Types.String,
      required: false,
    },
    distractibility: {
      type: Schema.Types.String,
      required: false,
    },
    moreTalkative: {
      type: Schema.Types.String,
      required: false,
    },
    flightOfIdeas: {
      type: Schema.Types.String,
      required: false,
    },
    inflatedselfEsteem: {
      type: Schema.Types.String,
      required: false,
    },
    increaseInGoalDirectedActivity: {
      type: Schema.Types.String,
      required: false,
    },
    excessiveInvolvement: {
      type: Schema.Types.String,
      required: false,
    },
    none: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
