import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ConsumerPhoneSchema } from "./consumerPhone-schema";
import { LegalGuardianshipTypeSchema } from "./legalGuarianship-schema";
import { RaceTypeSchema } from "./raceType-schema";
import { LivingArrangementSchema } from "./livingArrangement-schema";

export const GeneralInformationSchema = new mongoose.Schema(
  {
    name: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    dob: {
      type: Schema.Types.Date,
      required: false,
      default: null,
    },
    knownAs: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    age: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    primaryLanguage: {
      primaryLanguageReadablityStatus: {
        type: Schema.Types.Boolean,
        default: null,
      },
      english: {
        type: Schema.Types.Boolean,
        default: null,
      },
      spanish: {
        type: Schema.Types.Boolean,
        default: null,
      },
      other: {
        type: Schema.Types.Boolean,
        default: null,
      },
      otherLanguage: {
        type: Schema.Types.String,
        required: false,
      },
    },
    consumerPhone: {
      type: ConsumerPhoneSchema,
      required: false,
      default: {},
    },
    legalGuarianship: {
      type: LegalGuardianshipTypeSchema,
      required: false,
      default: {},
    },
    guardianPhone: {
      sameasAbove: { type: Schema.Types.Boolean, default: null },
      home: { type: Schema.Types.Boolean, default: null },
      work: { type: Schema.Types.Boolean, default: null },
      cell: { type: Schema.Types.Boolean, default: null },
      guardianPhoneNumber: { type: Schema.Types.String, required: false },
    },
    primaryInformant: {
      primaryInformant: {
        self: {
          type: Schema.Types.Boolean,
          required: false,
          default: null,
        },
        other: {
          type: Schema.Types.Boolean,
          required: false,
          default: null,
        },
      },
      other: {
        type: Schema.Types.String,
        required: false,
        default: null,
      },
    },
    informantPhone: {
      sameasAbove: { type: Schema.Types.Boolean, default: null },
      home: { type: Schema.Types.Boolean, default: null },
      work: { type: Schema.Types.Boolean, default: null },
      cell: { type: Schema.Types.Boolean, default: null },
    },
    genderAtBirth: {
      male: { type: Schema.Types.Boolean, default: null },
      female: { type: Schema.Types.Boolean, default: null },
      femalePregnant: { type: Schema.Types.Boolean, default: null },
    },
    genderIdentity: {
      male: { type: Schema.Types.Boolean, default: null },
      female: { type: Schema.Types.Boolean, default: null },
      nonBinary: { type: Schema.Types.Boolean, default: null },
    },
    sexualOrientation: {
      sexualOrientations: {
        type: Schema.Types.String,
        required: false,
      },
      isInterestingLGBTService: {
        type: Schema.Types.Boolean,
        default: null,
      },
    },
    ethnicity: {
      notHispanicOrigin: { type: Schema.Types.Boolean, default: null },
      hispanicCuban: { type: Schema.Types.Boolean, default: null },
      hispanicMexicanAmerican: { type: Schema.Types.Boolean, default: null },
      hispanicPuertoRican: { type: Schema.Types.Boolean, default: null },
      hispanicOther: { type: Schema.Types.Boolean, default: null },
    },
    raceType: {
      type: RaceTypeSchema,
      required: false,
      default: {},
    },
    livingArrangement: {
      type: LivingArrangementSchema,
      required: false,
      default: {},
    },
    maritalStatus: {
      married: { type: Schema.Types.Boolean, default: null },
      widowed: { type: Schema.Types.Boolean, default: null },
      divorced: { type: Schema.Types.Boolean, default: null },
      singleNeverMarried: { type: Schema.Types.Boolean, default: null },
      separated: { type: Schema.Types.Boolean, default: null },
      domesticPartnership: { type: Schema.Types.Boolean, default: null },
    },
    familySize: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    currentEmploymentStatus: {
      unemployed: { type: Schema.Types.Boolean, default: null },
      notavailableforwork: { type: Schema.Types.Boolean, default: null },
      employedFullTime: { type: Schema.Types.Boolean, default: null },
      homemaker: { type: Schema.Types.Boolean, default: null },
      armedForcesNationalGuard: { type: Schema.Types.Boolean, default: null },
      employedPartTime: { type: Schema.Types.Boolean, default: null },
      retired: { type: Schema.Types.Boolean, default: null },
      student: { type: Schema.Types.Boolean, default: null },
      disabilityIncome: { type: Schema.Types.Boolean, default: null },
    },
    employmentHistory: {
      type: Schema.Types.String,
      required: false,
      default: null,
    },
    education: {
      highestGradeCompleted: { type: Schema.Types.Boolean, default: null },
      highSchoolgraduate: { type: Schema.Types.Boolean, default: null },
      ged: { type: Schema.Types.Boolean, default: null },
      someCollege: { type: Schema.Types.Boolean, default: null },
      associateDegree: { type: Schema.Types.Boolean, default: null },
      bachelordegree: { type: Schema.Types.Boolean, default: null },
      graduateSchool: { type: Schema.Types.Boolean, default: null },
      specialEdClasses: { type: Schema.Types.Boolean, default: null },
      technicalTradeSchool: { type: Schema.Types.Boolean, default: null },
    },
  },
  { _id: false }
);
