import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const SleepSchema = new mongoose.Schema(
  {
    increased: {
      type: Schema.Types.String,
      required: false,
    },
    decreased: {
      type: Schema.Types.String,
      required: false,
    },
    restless: {
      type: Schema.Types.String,
      required: false,
    },
    difficultyFallingaSleep: {
      type: Schema.Types.String,
      required: false,
    },
    difficultyRemainingAsleep: {
      type: Schema.Types.String,
      required: false,
    },
    earlyMorningAwakening: {
      type: Schema.Types.String,
      required: false,
    },
    noDifficulties: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
