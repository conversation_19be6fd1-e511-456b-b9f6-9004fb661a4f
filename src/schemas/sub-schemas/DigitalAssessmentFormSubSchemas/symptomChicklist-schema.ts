import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { DepressiveSymptomsSchema } from "./depressiveSymptoms-schema";
import { ManicSymptomsSchema } from "./manicSymptoms-schema";
import { ConductLegalProblemSchema } from "./conductLegalProblem-schema";
import { PsychosisSchema } from "./psychosis-schema";
import { AnxietySymptomsSchema } from "./anxietySymptoms-schema";
import { AttentionSymptomsSchema } from "./attentionSymptoms-schema";

export const SymptomChicklistSchema = new mongoose.Schema(
  {
    depressiveSymptoms: {
      symtoms: {
        type: DepressiveSymptomsSchema,
        required: false,
      },
      comment: {
        type: Schema.Types.String,
        required: false,
      },
    },
    manicSymptoms: {
      symtoms: {
        type: ManicSymptomsSchema,
        required: false,
      },
      comment: {
        type: Schema.Types.String,
        required: false,
      },
    },
    conductLegalProblem: {
      symtoms: {
        type: ConductLegalProblemSchema,
        required: false,
      },
      comment: {
        type: Schema.Types.String,
        required: false,
      },
    },
    psychosis: {
      symtoms: {
        type: PsychosisSchema,
        required: false,
      },
      comment: {
        type: Schema.Types.String,
        required: false,
      },
    },
    anxietySymptoms: {
      symtoms: {
        type: AnxietySymptomsSchema,
        required: false,
      },
      comment: {
        type: Schema.Types.String,
        required: false,
      },
    },
    attentionSymptoms: {
      symtoms: {
        type: AttentionSymptomsSchema,
        required: false,
      },
      comment: {
        type: Schema.Types.String,
        required: false,
      },
    },
  },
  { _id: false }
);
