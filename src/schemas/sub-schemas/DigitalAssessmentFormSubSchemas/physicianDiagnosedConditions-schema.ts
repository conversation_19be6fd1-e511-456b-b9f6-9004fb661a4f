import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const PhysicianDiagnosedConditionsSchema = new mongoose.Schema(
  {
    allergies: {
      type: Schema.Types.String,
      required: false,
    },
    gynecological: {
      type: Schema.Types.String,
      required: false,
    },
    pancreatitis: {
      type: Schema.Types.String,
      required: false,
    },
    anemia: {
      type: Schema.Types.String,
      required: false,
    },
    headInjury: {
      type: Schema.Types.String,
      required: false,
    },
    respiratory: {
      type: Schema.Types.String,
      required: false,
    },
    arthritis: {
      type: Schema.Types.String,
      required: false,
    },
    heartDisease: {
      type: Schema.Types.String,
      required: false,
    },
    seizureDisorder: {
      type: Schema.Types.String,
      required: false,
    },
    asthma: {
      type: Schema.Types.String,
      required: false,
    },
    hepatitis: {
      type: Schema.Types.String,
      required: false,
    },
    std: { type: Schema.Types.String, required: false },
    brainDisorder: { type: Schema.Types.String, required: false },
    highBloodPressure: { type: Schema.Types.String, required: false },
    stroke: { type: Schema.Types.String, required: false },
    cancer: { type: Schema.Types.String, required: false },
    lowBloodPressure: { type: Schema.Types.String, required: false },
    thyroidDisease: { type: Schema.Types.String, required: false },
    chronicPain: { type: Schema.Types.String, required: false },
    cirrhosisoftheliver: { type: Schema.Types.String, required: false },
    immuneDisease: { type: Schema.Types.String, required: false },
    tuberculosis: { type: Schema.Types.String, required: false },
    diabetes: { type: Schema.Types.String, required: false },
    kidneyDisease: { type: Schema.Types.String, required: false },
    ulcer: { type: Schema.Types.String, required: false },
    eatingDisorder: { type: Schema.Types.String, required: false },
    muscleDisorder: { type: Schema.Types.String, required: false },
    mensHealthProblems: { type: Schema.Types.String, required: false },
    hivAids: { type: Schema.Types.String, required: false },
    none: { type: Schema.Types.String, required: false },
    other: { type: Schema.Types.String, required: false },
  },
  { _id: false }
);
