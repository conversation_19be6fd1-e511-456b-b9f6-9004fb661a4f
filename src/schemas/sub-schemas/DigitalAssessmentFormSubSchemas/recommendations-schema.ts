import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const RecommendationsSchema = new mongoose.Schema(
  {
    notApplicable: {
      type: Schema.Types.String,
      required: false,
    },
    levelSAIOP: {
      type: Schema.Types.String,
      required: false,
    },
    levelResidential: {
      type: Schema.Types.String,
      required: false,
    },
    levelMedicalDetox: {
      type: Schema.Types.String,
      required: false,
    },
    levelOutpatienttreatment: {
      type: Schema.Types.String,
      required: false,
    },
    levelComprehensiveoutpatient: {
      type: Schema.Types.String,
      required: false,
    },
    levelNonhospital: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
