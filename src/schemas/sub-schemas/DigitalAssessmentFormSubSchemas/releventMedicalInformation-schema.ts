import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { PhysicianDiagnosedConditionsSchema } from "./physicianDiagnosedConditions-schema";
import { PatientPainLevelSchema } from "./patientPainLevel-schema";

export const ReleventMedicalInformationSchema = new mongoose.Schema(
  {
    physicianDiagnosedConditions: {
      type: PhysicianDiagnosedConditionsSchema,
      required: false,
    },
    cancerType: {
      type: Schema.Types.String,
      required: false,
    },
    otherType: {
      type: Schema.Types.String,
      required: false,
    },
    lastVisitPrimaryCarePhysician: {
      type: Schema.Types.String,
      required: false,
    },
    comment: {
      type: Schema.Types.String,
      required: false,
    },
    anyAllergies: {
      type: Schema.Types.String,
      required: false,
    },
    hypersensitivities: {
      type: Schema.Types.String,
      required: false,
    },
    patientPainLevel: {
      type: PatientPainLevelSchema,
    },
  },
  { _id: false }
);
