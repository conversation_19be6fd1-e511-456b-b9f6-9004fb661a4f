import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const SexualActivitySchema = new mongoose.Schema(
  {
    sexuallyActive: {
      sexuallyActiveness: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      activeWith: {
        type: Schema.Types.String,
        required: false,
      },
    },
    protectionAgainstHepatitisHiv: {
      protection: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      how: {
        type: Schema.Types.String,
        required: false,
      },
    },
    protectionAgainstPregnancy: {
      protection: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      how: {
        type: Schema.Types.String,
        required: false,
      },
    },
    atRiskBehavior: {
      risk: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      describe: {
        type: Schema.Types.String,
        required: false,
      },
    },
    otherSymtoms: {
      type: Schema.Types.String,
      required: false,
    },
    comments: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
