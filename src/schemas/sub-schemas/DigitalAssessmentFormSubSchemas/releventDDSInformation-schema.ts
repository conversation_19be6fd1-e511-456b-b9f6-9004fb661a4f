import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const ReleventDDSInformationSchema = new mongoose.Schema(
  {
    ddsInvolvement: {
      type: Schema.Types.Boolean,
      required: false,
    },
    ddsCare: {
      type: Schema.Types.String,
      required: false,
    },
    ddsCareDescription: {
      type: Schema.Types.String,
      required: false,
    },
    ddsSocialWorker: {
      type: Schema.Types.String,
      required: false,
    },
    phone: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
