import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const BioPsychosocialDevelopemntHistorySchema = new mongoose.Schema(
  {
    developmentHistory: {
      type: Schema.Types.String,
      required: false,
    },
    childhoodHistory: {
      type: Schema.Types.String,
      required: false,
    },
    fosterCare: {
      type: Schema.Types.String,
      required: false,
    },
    siblings: {
      type: Schema.Types.String,
      required: false,
    },
    familyHistoryOfMHOrSAIssues: {
      type: Schema.Types.String,
      required: false,
    },
    currentSpouse: {
      type: Schema.Types.String,
      required: false,
    },
    childrenStepChildren: {
      type: Schema.Types.String,
      required: false,
    },
    relationsIssues: {
      type: Schema.Types.String,
      required: false,
    },
    otherSupports: {
      family: {
        type: Schema.Types.String,
        required: false,
      },
      church: {
        type: Schema.Types.String,
        required: false,
      },
      employer: {
        type: Schema.Types.String,
        required: false,
      },
      friends: {
        type: Schema.Types.String,
        required: false,
      },
      other: {
        type: Schema.Types.String,
        required: false,
      },
      otherSupport: {
        type: Schema.Types.String,
        required: false,
      },
    },
  },
  { _id: false }
);
