import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const FormHeaderSchema = new mongoose.Schema(
  {
    date: {
      type: Schema.Types.Date,
      required: false,
      default: null,
    },
    insuranceId: {
      type: Schema.Types.String,
      required: false,
    },
    lastName: {
      type: Schema.Types.String,
      required: false,
    },
    firstname: {
      type: Schema.Types.String,
      required: false,
    },
    mi: {
      type: Schema.Types.String,
      required: false,
    },
    currentAddress: {
        streetAddress: {
            type: Schema.Types.String,
            required: false,
        },
        unit: {
            type: Schema.Types.String,
            required: false,
        },
        city: {
            type: Schema.Types.String,
            required: false,
        },
        state: {
            type: Schema.Types.String,
            required: false,
        },
        zipCode: {
            type: Schema.Types.String,
            required: false,
        },
    },
  },
  { _id: false }
);
