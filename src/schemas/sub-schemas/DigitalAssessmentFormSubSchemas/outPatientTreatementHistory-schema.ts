import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const OutPatientTreatementHistorySchema = new mongoose.Schema(
  {
    applicableState: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    outPatienttreatementHistoryDetails: [
      {
        agencyOrService: {
          type: Schema.Types.String,
          required: false,
        },
        dateOfTreatement: {
          type: Schema.Types.String,
          required: false,
        },
        diagnosis: {
          type: Schema.Types.String,
          required: false,
        },
        medication: {
          type: Schema.Types.String,
          required: false,
        },
      },
    ],
  },
  { _id: false }
);
