import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const AttentionSymptomsSchema = new mongoose.Schema(
  {
    failsToFinishTasks: {
      type: Schema.Types.String,
      required: false,
    },
    inattentive: {
      type: Schema.Types.String,
      required: false,
    },
    fidgety: {
      type: Schema.Types.String,
      required: false,
    },
    forgetful: {
      type: Schema.Types.String,
      required: false,
    },
    difficultyFollowingDirections: {
      type: Schema.Types.String,
      required: false,
    },
    difficultyOrganizingThoughts: {
      type: Schema.Types.String,
      required: false,
    },
    difficultyAwaitingTurn: {
      type: Schema.Types.String,
      required: false,
    },
    poorAttentionSpan: {
      type: Schema.Types.String,
      required: false,
    },
    talksExcessively: {
      type: Schema.Types.String,
      required: false,
    },
    interruptsOrIntrudesOnOthers: {
      type: Schema.Types.String,
      required: false,
    },
    feelsAlways: {
      type: Schema.Types.String,
      required: false,
    },
    failsToGiveAttention: {
      type: Schema.Types.String,
      required: false,
    },
    avoidsDislikesTasks: {
      type: Schema.Types.String,
      required: false,
    },
    easilyDistracted: {
      type: Schema.Types.String,
      required: false,
    },
    blurtsOutAnswersBeforeQuestions: {
      type: Schema.Types.String,
      required: false,
    },
    none: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
