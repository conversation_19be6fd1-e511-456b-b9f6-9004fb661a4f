import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ProblemWithUseSchema } from "./problemWithUse-schema";

export const NameOfSubstanceSchema = new mongoose.Schema(
  {
    ageOfFirstUse: {
      type: Schema.Types.String,
      required: false,
    },
    regularUse: {
      type: Schema.Types.String,
      required: false,
    },
    dateLastUse: {
      type: Schema.Types.String,
      required: false,
    },
    route: {
      type: Schema.Types.String,
      required: false,
    },
    amount: {
      type: Schema.Types.Number,
      required: false,
    },
    frequency: {
      type: Schema.Types.Number,
      required: false,
    },
    problemWithUse: {
      type: ProblemWithUseSchema,
      required: false,
    },
    comment: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
