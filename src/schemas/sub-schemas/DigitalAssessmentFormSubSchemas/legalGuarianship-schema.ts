import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const LegalGuardianshipTypeSchema = new mongoose.Schema(
  {
    legalGuarianship: {
      self: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      other: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    otherLegalGuarianship: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
