import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const DiagnoseSchema = new mongoose.Schema(
  {
    code: {
      type: Schema.Types.String,
      required: false,
    },
    diagnosis: [
      {
        value: {
          type: Schema.Types.String,
          required: false,
        },
        label: {
          type: Schema.Types.String,
          required: false,
        },
      },
    ],
  },
  { _id: false }
);
