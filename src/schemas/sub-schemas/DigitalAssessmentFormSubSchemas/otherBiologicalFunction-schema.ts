import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const OtherBiologicalFunctionSchema = new mongoose.Schema(
  {
    amenorrhea: {
      type: Schema.Types.String,
      required: false,
    },
    encopresis: {
      type: Schema.Types.String,
      required: false,
    },
    increased: {
      type: Schema.Types.String,
      required: false,
    },
    decreased: {
      type: Schema.Types.String,
      required: false,
    },
    other: {
      type: Schema.Types.String,
      required: false,
    },
    enuresis: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
