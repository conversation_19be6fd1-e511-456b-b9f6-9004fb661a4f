import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const ReleventLegalInforMationSchema = new mongoose.Schema(
  {
    legalInvolvement: {
      type: Schema.Types.Boolean,
      required: false,
    },
    countOfArrestet: {
      type: Schema.Types.Number,
      required: false,
    },
    currentCharges: {
      type: Schema.Types.String,
      required: false,
    },
    attorneyName: {
      type: Schema.Types.String,
      required: false,
    },
    currentProbationRequirement: {
      type: Schema.Types.String,
      required: false,
    },
    probationOffice: {
      type: Schema.Types.String,
      required: false,
    },
    previousChargesJailOrPrison: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
