import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const ConductLegalProblemSchema = new mongoose.Schema(
  {
    fireSetting: {
      type: Schema.Types.String,
      required: false,
    },
    lying: {
      type: Schema.Types.String,
      required: false,
    },
    stealing: {
      type: Schema.Types.String,
      required: false,
    },
    fighting: {
      type: Schema.Types.String,
      required: false,
    },
    substanceAbuse: {
      type: Schema.Types.String,
      required: false,
    },
    none: {
      type: Schema.Types.String,
      required: false,
    },
    oppositionalDefiant: {
      type: Schema.Types.String,
      required: false,
    },
    gangInvolvement: {
      type: Schema.Types.String,
      required: false,
    },
    arrestsConviction: {
      type: Schema.Types.String,
      required: false,
    },
    impulsivity: {
      type: Schema.Types.String,
      required: false,
    },
    familyDesertion: {
      type: Schema.Types.String,
      required: false,
    },
    exhibitionism: {
      type: Schema.Types.String,
      required: false,
    },
    sexualActingOut: {
      type: Schema.Types.String,
      required: false,
    },
    consistentIrresponsibility: {
      type: Schema.Types.String,
      required: false,
    },
    propertyDestruction: {
      type: Schema.Types.String,
      required: false,
    },
    other: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
