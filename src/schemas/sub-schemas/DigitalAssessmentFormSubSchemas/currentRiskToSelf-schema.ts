import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const CurrentRiskToSelfSchema = new mongoose.Schema(
  {
    isSelfInjured: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    repetitiveIdeation: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    isSuicidePlan: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    isLethalIntent: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    isCommandHallucinations: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    isSuicidalThreats: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    describeSelfInjured: {
      type: Schema.Types.String,
      required: false,
    },
    describeSuicidalThreats: {
      type: Schema.Types.String,
      required: false,
    },
    describeCommandHallucinations: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
