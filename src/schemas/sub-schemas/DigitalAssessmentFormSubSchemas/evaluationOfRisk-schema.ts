import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const EvaluationOfRiskSchema = new mongoose.Schema(
  {
    selfHarming: {
      lowToNoRisk: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      moderateRisk: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      highRisk: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      imminentRisk: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    assaultive: {
      lowToNoRisk: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      moderateRisk: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      highRisk: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      imminentRisk: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    actionEvaluation: {
      type: Schema.Types.String,
      required: false,
    },
    beliefSystem: {
      type: Schema.Types.String,
      required: false,
    },
    roleOfBeliefinlife: {
      type: Schema.Types.String,
      required: false,
    },
    roleOfBeliefRecovery: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
