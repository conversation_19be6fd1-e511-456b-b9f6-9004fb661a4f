import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const LivingArrangementSchema = new mongoose.Schema(
  {
    livingArrangement: {
      privateResidence: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      institution: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      ownResidence: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      other: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      roomHouseDorm: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      adultCareHome: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      alternativeFamilyLiving: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      nursingHome: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      communityICFMR: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      correctionalFacility: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      homeless: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
      residentialFacility: {
        type: Schema.Types.Boolean,
        required: false,
        default: null,
      },
    },
    other: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
