import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const ExperienceSymptomsSchema = new mongoose.Schema(
  {
    agitationRestlessness: {
      type: Schema.Types.String,
      required: false,
    },
    confusion: {
      type: Schema.Types.String,
      required: false,
    },
    nauseaVomiting: {
      type: Schema.Types.String,
      required: false,
    },
    muscleTwitching: {
      type: Schema.Types.String,
      required: false,
    },
    chills: {
      type: Schema.Types.String,
      required: false,
    },
    deliriumTremens: {
      type: Schema.Types.String,
      required: false,
    },
    tremorsShaking: {
      type: Schema.Types.String,
      required: false,
    },
    anxiety: {
      type: Schema.Types.String,
      required: false,
    },
    racingPulse: {
      type: Schema.Types.String,
      required: false,
    },
    rapidBreathing: {
      type: Schema.Types.String,
      required: false,
    },
    sweats: {
      type: Schema.Types.String,
      required: false,
    },
    hallucinations: {
      type: Schema.Types.String,
      required: false,
    },
    backJointPain: {
      type: Schema.Types.String,
      required: false,
    },
    other: {
      type: Schema.Types.String,
      required: false,
    },
    difficultySleeping: {
      type: Schema.Types.String,
      required: false,
    },
    runnyNose: {
      type: Schema.Types.String,
      required: false,
    },
    tearyEyes: {
      type: Schema.Types.String,
      required: false,
    },
    craving: {
      type: Schema.Types.String,
      required: false,
    },
    cramps: {
      type: Schema.Types.String,
      required: false,
    },
    seizures: {
      type: Schema.Types.String,
      required: false,
    },
    none: {
      type: Schema.Types.String,
      required: false,
    },
  },
  { _id: false }
);
