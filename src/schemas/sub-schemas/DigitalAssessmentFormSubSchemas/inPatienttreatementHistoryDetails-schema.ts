import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const InPatienttreatementHistorySchema = new mongoose.Schema(
  {
    applicableState: {
      type: Schema.Types.String,
      required: false,
    },
    pastTreatement: {
      type: Schema.Types.String,
      required: false,
    },
    additionalMedicalInformation: {
      type: Schema.Types.String,
      required: false,
    },
    inPatientTreatementHistory: [
      {
        treatementProgram: {
          type: Schema.Types.String,
          required: false,
        },
        dateOfTreatements: {
          type: Schema.Types.Date,
          required: false,
        },
        reasonForTreatement: {
          type: Schema.Types.String,
          required: false,
        },
        diagnosis: {
          type: Schema.Types.String,
          required: false,
        },
        medication: {
          type: Schema.Types.String,
          required: false,
        },
      },
    ],
  },
  { _id: false }
);
