import * as mongoose from "mongoose";
import { Schema } from "mongoose";

export const HistoryOfSuicidalBehaviorSchema = new mongoose.Schema(
  {
    describeHomicidalBehavior: {
      type: Schema.Types.String,
      required: false,
    },
    describeSeriousPhysicalHarm: {
      type: Schema.Types.String,
      required: false,
    },
    describeAggression: {
      type: Schema.Types.String,
      required: false,
    },
    describeHistoryOfSuicidalBehavior: {
      type: Schema.Types.String,
      required: false,
    },
    isHistoryOfSuicidalBehavior: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    isSeriousHarmSelfOthers: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    isHomicidalBehavior: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    isAggressionViolenceOthers: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
  },
  { _id: false }
);
