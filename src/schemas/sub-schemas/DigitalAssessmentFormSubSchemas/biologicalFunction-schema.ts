import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { SleepSchema } from "./sleep-schema";
import { NutritionalStatusSchema } from "./nutritionalStatus-schema";
import { OtherBiologicalFunctionSchema } from "./otherBiologicalFunction-schema";
import { SexualActivitySchema } from "./sexualActivity-schema";

export const BiologicalFunctionsSchemas = new mongoose.Schema(
  {
    sleep: {
      sleepStatus: {
        type: SleepSchema,
        required: false,
      },
      comment: {
        type: Schema.Types.String,
        required: false,
      },
    },
    nutritionalStatus: {
      nutritionalStatus: {
        type: NutritionalStatusSchema,
        required: false,
      },
      comment: {
        type: Schema.Types.String,
        required: false,
      },
    },

    otherBiologicalFunction: {
      type: OtherBiologicalFunctionSchema,
      required: false,
    },
    sexualActivity: {
      type: SexualActivitySchema,
      required: false,
    },
  },
  { _id: false }
);
