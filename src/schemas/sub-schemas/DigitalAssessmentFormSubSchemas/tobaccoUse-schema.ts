import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { ExperienceSymptomsSchema } from "./experienceSymptoms-schema";

export const TobaccoUseSchema = new mongoose.Schema(
  {
    tobaccoUse: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    tobaccoType: {
      type: Schema.Types.String,
      required: false,
    },
    howOften: {
      type: Schema.Types.String,
      required: false,
    },
    howMuch: {
      type: Schema.Types.String,
      required: false,
    },
    howLong: {
      type: Schema.Types.String,
      required: false,
    },
    interestEndUse: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    interestReducingUse: {
      type: Schema.Types.Boolean,
      required: false,
      default: null,
    },
    endingUse: {
      type: Schema.Types.String,
      required: false,
    },
    longestAbstinence: {
      type: Schema.Types.String,
      required: false,
    },
    whenWas: {
      type: Schema.Types.String,
      required: false,
    },
    relapse: {
      type: Schema.Types.String,
      required: false,
    },
    comment: {
      type: Schema.Types.String,
      required: false,
    },
    experiencedSymptoms: {
      type: ExperienceSymptomsSchema,
      required: false,
    },
  },
  { _id: false }
);
