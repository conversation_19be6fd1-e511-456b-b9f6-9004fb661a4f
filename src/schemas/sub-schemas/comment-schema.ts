import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { Comment } from "../../models/sub-models/comment-model";
import User from "../user-schema";
import { ReplySchema } from "./reply-schema";
//import { IComment } from "../../models/sub-models/comment-model";

export const CommentSchemaOptions: mongoose.SchemaOptions = {
  _id: false,
  id: false,
  timestamps: false,
  skipVersioning: true,
  strict: false,
  discriminatorKey: "role",
  toJSON: {
    getters: true,
    virtuals: true,
  }
};

export const CommentSchema = new mongoose.Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref:User.modelName
    },
    comment: {
      type: Schema.Types.String,
      required: true,
    },
    replies: [
      {
        type: ReplySchema,
        required: false,
        blackbox: true,
      },
    ],
    // replies: [
    //   new mongoose.Schema({
    //     type: Schema.Types.Mixed,
    //     required: false,
    //   }),
    // ],
    date: {
      type: Schema.Types.Date,
      required: true,
    },
  },
  CommentSchemaOptions
);

const Comment = mongoose.model<Comment>("Comment", CommentSchema);
export default Comment;
