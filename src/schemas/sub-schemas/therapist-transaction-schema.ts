import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "../user-schema";
import Meeting from "../meeting-schema";

const TransactionSchema = new Schema({
    therapistId: {
        type: Schema.Types.ObjectId,
        require: true,
        ref: User.modelName
    },
    meetingId: {
        type: Schema.Types.ObjectId,
        require: false,
        ref: Meeting.modelName
    },
    type: {
        type: Schema.Types.String,
        require: true
    },
    transactionAmount: {
        type: Schema.Types.Number,
        require: true,
        default: 0
    },
    accumulatedBalance: {
        type: Schema.Types.Number,
        require: true,
        default: 0
    },
    accumulatedTotalEarnings: {
        type: Schema.Types.Number,
        require: true,
        default: 0
    },
    accumulatedWithdrawals: {
        type: Schema.Types.Number,
        require: true,
        default: 0
    },
    insuranceCompany: {
        type: Schema.Types.String,
        require: true
    },
    isFlag: {
        type: Schema.Types.Boolean,
        require: false
    },
});

export const TherapistTransaction = new mongoose.Schema(
    {
        _id: {
            type: Schema.Types.String,
            required: true
        },
        role: {
            type: Schema.Types.String,
            required: false
        },
        email: {
            type: Schema.Types.String,
            required: false
        },
        firstname: {
            type: Schema.Types.String,
            required: false
        },
        primaryPhone: {
            type: Schema.Types.String,
            required: false
        },
        lastname: {
            type: Schema.Types.String,
            required: false
        },
        stripeConnectedAccountId: {
            type: Schema.Types.String,
            required: false
        },
        recentTransaction: [
            {
                type: TransactionSchema,
            },
        ],
        paidStatus: {
            type: Schema.Types.String,
            required: false
        },

    },
);
