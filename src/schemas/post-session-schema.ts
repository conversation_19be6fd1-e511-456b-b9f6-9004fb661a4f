import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const PostSessionSchema = new mongoose.Schema(
  {
    serviceProvided: {
      type: Schema.Types.String,
      required: false,
    },
    suicidalIdeation: {
      type: Schema.Types.String,
      required: false,
    },
    homicidalIdeation: {
      type: Schema.Types.String,
      required: false,
    },
    perceptualDisturbance: {
      type: Schema.Types.String,
      required: false,
    },
    nextSessionScheduled: {
      type: Schema.Types.String,
      required: false,
    },
    clinicalDirectorReviewRequired: {
      type: Schema.Types.String,
      required: false,
    },
    comments: {
      type: Schema.Types.String,
      required: false,
    },
    diagnosisCodes: {
      type: [Schema.Types.String],
      required: false,
    },
    is_read: {
      type: Schema.Types.Boolean,
      default: false,
    },
    meetingId: {
      type: Schema.Types.String,
      required: true,
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: User.modelName,
    },
    clientId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: User.modelName,
    },
  },
  schemaOptions
);

const PostSession = mongoose.model("PostSession", PostSessionSchema);

export default PostSession;
