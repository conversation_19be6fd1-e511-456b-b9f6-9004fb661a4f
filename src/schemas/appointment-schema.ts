import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IAppointment, AppointmentStatus } from "../models/appointment-model";
import { RepeatInfoSchema } from "./sub-schemas/repeat-info-schema";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const AppointmentSchema = new mongoose.Schema(
  {
    therapistId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    clientId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    title: {
      type: Schema.Types.String,
      require: true,
    },
    start: {
      type: Schema.Types.Date,
      require: true,
    },
    end: {
      type: Schema.Types.Date,
      require: true,
    },
    typeOfMeeting: {
      type: Schema.Types.String,
      require: true,
    },
    reminders: [
      {
        type: Schema.Types.Number,
        require: true,
      },
    ],
    repeatInfo: {
      type: RepeatInfoSchema,
    },
    color: {
      type: Schema.Types.String,
      require: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    status: {
      type: Schema.Types.String,
      require: false,
      default: AppointmentStatus.PENDING,
    },
    approvedStatus: {
      type: Schema.Types.String,
      require: false,
      default: AppointmentStatus.PENDING,
    },
    groupId: {
      type: Schema.Types.String,
      require: false,
    },
    noOfCallingTriesByTherapist: {
      type: Schema.Types.Number,
      require: false,
    },
    noOfCallingTriesByClient: {
      type: Schema.Types.Number,
      require: false,
    },
    meetingStatus: {
      type: Schema.Types.String,
      require: false,
    },
    meetingId: {
      type: Schema.Types.String,
      require: false,
    },
    meetingStartedBy: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    GoogleCalendarEventId: {
      type: Schema.Types.String,
      require: false,
    },
    smsStatus: {
      type: Schema.Types.String,
      require: false,
    },
    therapistChangeApprovedStatusAt: {
      type: Schema.Types.Date,
      require: false,
    },
  },
  schemaOptions
);

const Appointment = mongoose.model<IAppointment>("Appointment", AppointmentSchema);

export default Appointment;
