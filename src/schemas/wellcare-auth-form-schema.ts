import * as mongoose from "mongoose";
import { WellCareAuthorizationSchema } from "./sub-schemas/WellcareAuthFormSubSchemas/wellcare-authorization-schema";
import { WellCareMemberInformationSchema } from "./sub-schemas/WellcareAuthFormSubSchemas/wellcare-member-information-schema";
import { WellCareFacilityInformationSchema } from "./sub-schemas/WellcareAuthFormSubSchemas/wellcare-facility-information-schema";
import { WellCareClinicalInformationSchema } from "./sub-schemas/WellcareAuthFormSubSchemas/wellcare-clinical-information-schema";
import { WellCareServiceTypeSchema } from "./sub-schemas/WellcareAuthFormSubSchemas/wellcare-service-type-schema";
import { WellCarePlaceTypeSchema } from "./sub-schemas/WellcareAuthFormSubSchemas/wellcare-place-type-schema";
import { WellCareProviderSchema } from "./sub-schemas/WellcareAuthFormSubSchemas/wellcare-provider-schema";
import AuthorizationForm from "./authorization-form-schema";
import { AuthFormType, IAuthorizationForm } from "../models/authorization-form-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const WellCareAuthFormSchema = new mongoose.Schema(
  {
    authorizationInformation: {
      type: WellCareAuthorizationSchema,
      required: true,
    },
    memberInformation: {
      type: WellCareMemberInformationSchema,
      required: true,
    },
    providerInformation: {
      type: WellCareProviderSchema,
      required: true,
    },
    facilityInformation: {
      type: WellCareFacilityInformationSchema,
      required: true,
    },
    serviceTypeInformation: {
      type: WellCareServiceTypeSchema,
      required: true,
    },
    placeTypeInformation: {
      type: WellCarePlaceTypeSchema,
      required: true,
    },
    clinicalInformation: {
      type: WellCareClinicalInformationSchema,
      required: true,
    },
  },
  schemaOptions
);

const WellCareAuthForm = AuthorizationForm.discriminator<IAuthorizationForm>(
  "WellCareAuthForm",
  WellCareAuthFormSchema,
  AuthFormType.WellcareAuthForm
);

export default WellCareAuthForm;
