import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IInvoice } from "../models/invoice-model";
import Insurance from "./insurance-schema";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const InvoiceSchema = new mongoose.Schema(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    insuranceId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: Insurance.modelName,
    },
    paidAmountByInsurance: {
      type: Schema.Types.String,
      require: true,
    },
    dueAmount: {
      type: Schema.Types.Number,
      require: true,
    },
    paymentStatus: {
      type: Schema.Types.String,
      require: true,
    },
    paymentMonth: {
      type: Schema.Types.String,
      require: true,
    }
  },
  schemaOptions
);

const Invoice = mongoose.model<IInvoice>("Invoice", InvoiceSchema);

export default Invoice;
