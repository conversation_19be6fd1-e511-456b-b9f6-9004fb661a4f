import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { IClient } from "../models/client-model";
import { UserRole } from "../models/user-model";
import Insurance from "./insurance-schema";
import Invoice, { InvoiceSchema } from "./invoice-shema";
import { PreferencesSchema } from "./sub-schemas/preferences-schema";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const ClientSchema = new mongoose.Schema(
  {
    preference: {
      type: PreferencesSchema,
      required: false,
    },
    incognito: {
      type: Schema.Types.Boolean,
      required: false,
    },
    incognitoPopupShow: {
      type: Schema.Types.Boolean,
      required: false,
    },
    skip: {
      type: Schema.Types.Boolean,
      required: false,
      default: false
    },
    PersonalizeMatchData: {
      type: Schema.Types.Mixed,
      required: false
    },
    dislikedTherapists: [
      {
        type: Schema.Types.ObjectId,
        require: false,
      },
    ],
    maritalStatus: {
      type: Schema.Types.String,
      required: false,
    },
    unit: {
      type: Schema.Types.String,
      required: false,
    },
    homePhone: {
      type: Schema.Types.String,
      required: false,
    },
    workPhone: {
      type: Schema.Types.String,
      required: false,
    },
    voiceMail: {
      type: Schema.Types.String,
      required: false,
    },
    stripeCustomerId: {
      type: Schema.Types.String,
      required: false,
    },
    subscriptionId: {
      type: Schema.Types.String,
      required: false,
    },
    subscriptionStatus: {
      type: Schema.Types.String,
      required: false,
    },
    testSubscriptionStatus: {
      type: Schema.Types.String,
      required: false,
    },
    insuranceId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: Insurance.modelName,
    },
    secondaryInsuranceId: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: Insurance.modelName,
    },
    premiumStatus: {
      type: Schema.Types.String,
      required: false,
    },
    premiumMembershipStartedDate: {
      type: Schema.Types.Date,
      required: false,
    },
    premiumMembershipRevokedDate: {
      type: Schema.Types.Date,
      required: false,
    },
    chatWordCount: {
      type: Schema.Types.Number,
      required: false,
    },
    freeUser: {
      type: Schema.Types.Boolean,
      required: false,
    },
    clientActiveStatus: {
      type: Schema.Types.Boolean,
      required: false,
    },
    copaymentAmount: {
      type: Schema.Types.Number,
      require: false
    },
    claimEligibilityDetails: {
      type: Schema.Types.Mixed,
      required: false,
    },
    claimEligibilityMdDetails: {
      type: Schema.Types.Mixed,
      required: false,
    },
    primaryTherapist: {
      type: Schema.Types.ObjectId,
      require: false,
      ref: User.modelName,
    },
    claimEligibilityMdErrorDetails:{
      type: Schema.Types.String,
      required: false
    },
    claimEliMdInactiveDetails: {
      type: Schema.Types.Mixed,
      required: false,
    },
    last_pay_copayment_time: {
      type: Schema.Types.Date,
      required: false,
    },
    last_pay_copayment_amount: {
      type: Schema.Types.Number,
      required: false,
    },
  },
  schemaOptions
);

const Client = User.discriminator<IClient>(
  "Client",
  ClientSchema,
  UserRole.CLIENT
);

export default Client;
