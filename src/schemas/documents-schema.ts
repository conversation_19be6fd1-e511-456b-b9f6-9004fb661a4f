import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import { AudienceType, IDocument } from "../models/document-model";
import Upload from "./upload-schema";
import User from "./user-schema";

const schemaOptions: mongoose.SchemaOptions = {
    _id: true,
    id: false,
    timestamps: true,
    skipVersioning: true,
    strict: false,
    toJSON: {
      getters: true,
      virtuals: true,
    }
};


export const TrainingDocumentSchema = new mongoose.Schema({
    documentTitle: {
        type: Schema.Types.String,
        require: true,
    },
    documentDescription: {
        type: Schema.Types.String,
        require: true,
    },
    uploads: [
        {
          type: Schema.Types.ObjectId,
          require: false,
          ref: Upload.modelName,
        },
    ],
    vimoIds: [
        {
          type: Schema.Types.String,
          require: false,
        },
    ],
    createdBy: {
        type: Schema.Types.ObjectId,
        require: true,
        ref: User.modelName,
    },
    editedBy: {
        type: Schema.Types.ObjectId,
        require: false,
        ref: User.modelName,
    },
    audience: {
        type: AudienceType,
        require: true,
        default: AudienceType.PUBLIC
    }
}, schemaOptions);

const TrainigDoc = mongoose.model<IDocument>('TrainingDocuments', TrainingDocumentSchema);

export default TrainigDoc;