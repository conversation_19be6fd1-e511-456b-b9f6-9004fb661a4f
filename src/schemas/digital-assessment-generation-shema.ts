import * as mongoose from "mongoose";
import { Schema } from "mongoose";
import Meeting from "./meeting-schema";
import User from "./user-schema";
import { IDigitalAssessmentGeneration } from "../models/digital-assessment-generation-model";

const schemaOptions: mongoose.SchemaOptions = {
  _id: true,
  id: false,
  timestamps: true,
  skipVersioning: true,
  strict: false,
  toJSON: {
    getters: true,
    virtuals: true,
  },
};

export const DigitalAssessmentGenerationSchema = new mongoose.Schema(
  {
    meetingId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: Meeting.modelName,
    },
    clientId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      require: true,
      ref: User.modelName,
    },
    aiResponse: {
      type: Schema.Types.String,
      require: false,
    }
  },
  schemaOptions
);

const DigitalAssessmentGeneration = mongoose.model<IDigitalAssessmentGeneration>("DigitalAssessmentGeneration", DigitalAssessmentGenerationSchema);

export default DigitalAssessmentGeneration;
