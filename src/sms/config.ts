import { AppLogger } from "../common/logging";
import { notiToSlack } from "../mail/slack-notifier";
import User from "../schemas/user-schema";

require("dotenv").config();
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const client = require("twilio")(accountSid, authToken);
export namespace SMSService {
  export async function sendEventSMS(subject: string, contactNumber: string, fileName: string = 'N/A') {
    const CalledFunction = `SMS Event: sendEventSMS, End Point: ${fileName}`;
    return await sendSMS(subject, contactNumber, CalledFunction);
  }
  
  export async function sendUnpaidCOPAYSMS(
    firstname: string,
    date: any,
    time: any,
    coPaymentValue: number,
    contactNumber: string,
    paymentLink: string,
    fileName: string = 'N/A'
  ) {
    AppLogger.info(`SendUnpaidCOPAYSMS test log: ${firstname} ${date} ${time} ${fileName}`);
    const body = `Hi ${firstname},\nFriendly reminder: Your therapy session on  on ${date} at ${time} has a $${coPaymentValue} copayment due. Please click the link below to make the payment and ensure uninterrupted sessions.\n${paymentLink}  \n\nBest,\nLavni`;
    const CalledFunction = `SMS Event: sendUnpaidCOPAYSMS, End Point: ${fileName}`;
    return await sendSMS(body, contactNumber, CalledFunction);
  }

  export async function sendPaidCOPAYSMS(
    firstname: string,
    createdAt: any,
    contactNumber: string,
    fileName: string = 'N/A'
  ) {
    const body = `Dear ${firstname},\nYour copayment for your session on ${createdAt} was processed successfully.\n
      Thank you for trusting us with your care. \n\nBest,\nLavni`;
    const CalledFunction = `SMS Event: sendPaidCOPAYSMS, End Point: ${fileName}`;
    return await sendSMS(body, contactNumber, CalledFunction);
  }

  export async function sendInactiveClientSMS(
    subject: string,
    contactNumber: string,
    fileName: string = 'N/A'
  ) {
    const CalledFunction = `SMS Event: sendInactiveClientSMS, End Point: ${fileName}`;
    return await sendSMS(subject, contactNumber, CalledFunction);
  }

  export async function sendGroupChatEventSMS(
    subject: string,
    contactNumber: string,
    fileName: string = 'N/A'
  ) {
    const CalledFunction = `SMS Event: sendGroupChatEventSMS, End Point: ${fileName}`;
    return await sendSMS(subject, contactNumber, CalledFunction);
  }
  export async function sendReminderSMS(subject: string, contactNumber: string, fileName: string = 'N/A') {
    const CalledFunction = `SMS Event: sendReminderSMS, End Point: ${fileName}`;
    return await sendSMS(subject, contactNumber, CalledFunction);
  }

  function maskContactNumber(contactNumber: string): string {
    if (contactNumber.length <= 6) return contactNumber;
    const totalLength = contactNumber.length;
    const midStart = Math.floor((totalLength - 4) / 2);
    const midEnd = totalLength - 5;
    return (
      contactNumber.slice(0, midStart) +
      '****' +
      contactNumber.slice(midEnd)
    );
  }

  export async function sendSMS(body: string, to: string, fileName: string = 'N/A') {
    const logFileName = fileName;
    const logContactNumber = maskContactNumber(to);
    const logMessage = body.replace(/\n/g, '').replace(/\s{2,}/g, ' ').slice(0, 15) + '****';
    
    try {
      // Tìm user dựa trên số điện thoại
      const user = await User.findOne({ primaryPhone: to });
      
      // Nếu có user và số lần gửi thất bại > 10, không gửi tin nhắn
      if (user && user.failedSmsCount && user.failedSmsCount > 3) {
        AppLogger.info(
          `SMS not sent due to failure threshold exceeded, ${logFileName}, contact number: ${logContactNumber}, failed count: ${user.failedSmsCount}`
        );
        return false;
      }
      
      if (process.env.NODE_ENV == "production") {
        AppLogger.info(
          `Send SMS Function Called, ${logFileName}, contact number: ${logContactNumber}, message: ${logMessage}`
        );
  
        const message = await client.messages.create({
          body,
          from: process.env.TWILIO_PHONE_NUMBER,
          to,
        });
      }
      if (process.env.NODE_ENV == "development" || process.env.NODE_ENV == "staging") {
        const content = `Send SMS Function Called, ${logFileName}, contact number: ${logContactNumber}, message: ${logMessage}`;
        notiToSlack(content);
      }

      return true;
    } catch (error) {
      AppLogger.error(
        `Send SMS Error Occurred, ${logFileName}, contact number: ${logContactNumber}, message: ${logMessage}, error: ${error}`
      );
      
      return false;
    }
  }
  
}
