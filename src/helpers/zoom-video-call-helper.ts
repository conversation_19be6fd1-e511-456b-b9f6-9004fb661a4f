import { ApplicationError } from "../common/application-error";

const KJUR = require("jsrsasign");
const jwt = require("jsonwebtoken");

const apiKey = process.env.ZOOM_API_KEY;
const apiSecret = process.env.ZOOM_API_SECRET;

const sdkKey = process.env.ZOOM_SDK_KEY;
const sdkSecret = process.env.ZOOM_SDK_SECRET;

const apiKeyExpiresIn = process.env.ZOOM_API_KEY_EXPIRATION_TIME;
const sdkKeyExpiresIn = process.env.ZOOM_SDK_KEY_EXPIRATION_TIME;

export namespace ZoomVideoCallHelper {
  export function genarateZoomApiToken(): Promise<string> {
    try {
      const expiresInSecs = parseInt(apiKeyExpiresIn);
      const currentTimeInSecs = Math.round(new Date().getTime() / 1000);
      const expirationTimeInSecs = currentTimeInSecs + expiresInSecs;

      const payload = {
        iss: apiKey,
        exp: expirationTimeInSecs,
      };

      const jwtToken = jwt.sign(payload, apiSecret);

      if (!jwtToken) {
        throw new ApplicationError("zoom api token genarating error occured");
      }
      return jwtToken;
    } catch (error) {
      throw new ApplicationError("zoom api token genarating error occured");
    }
  }

  export function genarateZoomSDKToken(
    topic: string,
    roleType: number,
    userId: string,
    sessionId: string,
    password: string
  ): Promise<string> {
    try {
      const expiresInSecs = parseInt(sdkKeyExpiresIn);
      const iat = Math.round(new Date().getTime() / 1000) - 30;
      const expirationTime = iat + expiresInSecs;

      const headers = {
        alg: "HS256",
        typ: "JWT",
      };

      const payload = {
        app_key: sdkKey,
        tpc: topic,
        role_type: roleType,
        user_identity: userId,
        session_key: sessionId,
        version: 1,
        iat: iat,
        exp: expirationTime,
        pwd: password,
      };

      const sHeader = JSON.stringify(headers);
      const sPayload = JSON.stringify(payload);

      const jwtToken = KJUR.jws.JWS.sign("HS256", sHeader, sPayload, sdkSecret);

      if (!jwtToken) {
        throw new ApplicationError("zoom sdk token genarating error occured");
      }

      return jwtToken;
    } catch (error) {
      throw new ApplicationError("zoom sdk token genarating error occured");
    }
  }
  export function doubleEncodeString(stringForEncode: string): string {
    try {
      if (
        stringForEncode.indexOf("/") == 0 ||
        stringForEncode.indexOf("//") > -1
      ) {
        const encodedString = encodeURIComponent(
          encodeURIComponent(stringForEncode)
        );

        if (!encodedString) {
          throw new ApplicationError("double encoding error occured");
        }

        return encodedString;
      }
      return stringForEncode;
    } catch (error) {
      throw new ApplicationError("double encoding error occured");
    }
  }
}
