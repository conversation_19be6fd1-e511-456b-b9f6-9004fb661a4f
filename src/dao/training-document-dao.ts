import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";
import { AudienceType, DDocument, IDocument } from "../models/document-model";
import TrainigDoc from "../schemas/documents-schema";
import { UserDao } from "./user-dao";
import { IUser, UserRole } from "../models/user-model";
import {
  DTrainingDocumentFolder,
  ITrainingDocumentFolder,
} from "../models/training-document-folder-model";
import TrainingDocFolder from "../schemas/training-document-folder-schema";
import {
  DTrainingDocumentFolderFile,
  ITrainingDocumentFolderFile,
} from "../models/training-document-folder-file-model";
import TrainingDocFolderFile from "../schemas/training-document-folder-file-schema";
import { ITherapist } from "../models/therapist-model";
import Therapist from "../schemas/therapist-schema";

export namespace TrainigDocumentDao {
  export async function createTrainigDocument(
    doc: DDocument
  ): Promise<IDocument> {
    const iDoc = new TrainigDoc(doc);
    let res = await iDoc
      .save()
      .then((iDoc) =>
        iDoc
          .populate([{ path: "uploads" }, { path: "createdBy" }])
          .execPopulate()
      );
    return res;
  }

  export async function updateTrainingDocument(
    trainingDocId: Types.ObjectId,
    doc: DDocument
  ): Promise<IDocument> {
    let res = await TrainigDoc.findByIdAndUpdate(trainingDocId, doc, {
      new: true,
    }).populate([{ path: "uploads" }, { path: "createdBy" }]);
    return res;
  }
  export async function updateTrainingDocumentAdmin(
    trainingDocId: Types.ObjectId,
    doc: DDocument
  ): Promise<IDocument> {
    let res = await TrainigDoc.findByIdAndUpdate(trainingDocId, doc, {
      new: true,
    }).populate([{ path: "uploads" }, { path: "createdBy" }]);
    return res;
  }
  export async function getAllDocuments(
    subType: string,
    userId: StringOrObjectId
  ): Promise<IDocument[]> {
    const response = await TrainigDoc.aggregate([
      {
        $match: {
          $or: [
            { audience: AudienceType.PUBLIC },
            { audience: subType },
            { createdBy: userId },
          ],
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          as: "createdBy",
        },
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "uploads",
          foreignField: "_id",
          as: "uploads",
        },
      },
    ]).sort({ createdAt: -1 });

    return response;
  }

  export async function getTrainingDocumentById(
    docId: StringOrObjectId
  ): Promise<IDocument> {
    const res = await TrainigDoc.findById(docId);
    return res;
  }

  export async function deleteTrainingDocumentById(
    id: StringOrObjectId
  ): Promise<IDocument> {
    const res = await TrainigDoc.findByIdAndDelete(id);
    return res;
  }

  export async function searchDocuments(
    userId: Types.ObjectId,
    searchableText: string,
    subType: string,
    limit: number,
    offset: number
  ): Promise<IDocument[]> {
    // need to develop more
    let searchedName = null;
    const user: IUser = await UserDao.getUserById(userId);
    let userBase = null;

    if (searchableText) {
      let seacrhItem = searchableText.replace(/\s/g, "");
      searchedName =
        searchableText != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const documentTitleQuery =
      searchedName != null && searchedName
        ? { documentTitle: searchedName }
        : { documentTitle: { $exists: true } };

    if (user.role == UserRole.THERAPIST) {
      userBase =
        {
          $or: [
            { createdBy: Types.ObjectId(userId.toString()) },
            { audience: subType },
            { audience: AudienceType.PUBLIC },
          ],
        } || {};
    } else if (user.role == UserRole.CLIENT) {
      userBase =
        { $or: [{ audience: subType }, { audience: AudienceType.PUBLIC }] } ||
        {};
    } else if (
      user.role == UserRole.SUPER_ADMIN ||
      user.role == UserRole.SUB_ADMIN
    ) {
      userBase =
        {
          $or: [
            { createdBy: Types.ObjectId(userId.toString()) },
            { audience: subType },
            { audience: AudienceType.PUBLIC },
            { audience: AudienceType.CLIENTS },
            { audience: AudienceType.THERAPISTS },
          ],
        } || {};
    }

    const response = await TrainigDoc.aggregate([
      {
        $project: {
          documentTitle: 1,
          documentDescription: 1,
          uploads: 1,
          videoUrl: 1,
          vimoIds: 1,
          createdBy: 1,
          editedBy: 1,
          createdAt: 1,
          audience: 1,
          _id: 1,
        },
      },
      {
        $match: {
          $and: [documentTitleQuery, userBase],
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          as: "createdBy",
        },
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "uploads",
          foreignField: "_id",
          as: "uploads",
        },
      },
      {
        $skip: offset,
      },
      {
        $limit: limit,
      },
    ]).sort({ createdAt: -1 });

    return response;
  }

  export async function createTrainingDocumentFolder(
    data: DTrainingDocumentFolder
  ): Promise<ITrainingDocumentFolder> {
    const iDocumentFolderUpload: ITrainingDocumentFolder =
      new TrainingDocFolder(data);
    return await iDocumentFolderUpload.save();
  }

  export async function createTrainingDocumentFolderFile(
    data: DTrainingDocumentFolderFile
  ): Promise<ITrainingDocumentFolderFile> {
    const iDocumentFolderFileUpload: ITrainingDocumentFolderFile =
      new TrainingDocFolderFile(data);
    return await iDocumentFolderFileUpload.save();
  }

  export async function checkTrainingDocumentFolderAlreadyExist(
    parentFolderId: Types.ObjectId | null,
    folderName: string
  ): Promise<any> {
    const trainingDocumentFolder = await TrainingDocFolder.find({
      parentFolderId,
      folderName,
    });
    return trainingDocumentFolder;
  }

  export async function checkTrainingDocumentFolderFileAlreadyExist(
    parentFolderId: Types.ObjectId | null,
    fileName: string
  ): Promise<any> {
    const trainingDocumentFolder = await TrainingDocFolderFile.find({
      parentFolderId,
      originalFileName: fileName,
    });
    return trainingDocumentFolder;
  }

  export async function getTrainingDocumentFoldersByParentFolderId(
    parentFolderId: Types.ObjectId | null,
    nameOrder?: string | null
  ): Promise<any> {
    const query =
      parentFolderId === null
        ? { parentFolderId: null }
        : { parentFolderId: parentFolderId };
    console.log("query ", query);
    
    // Add sorting based on nameOrder parameter
    let sortOption = {};
    if (nameOrder === 'asc') {
      sortOption = { folderName: 1 }; // Ascending order (A->Z)
    } else if (nameOrder === 'desc') {
      sortOption = { folderName: -1 }; // Descending order (Z->A)
    }
    
    const trainingDocumentFolders = await TrainingDocFolder.find(query)
      .sort(sortOption)
      .exec();
    return trainingDocumentFolders;
  }

  export async function getTrainingDocumentFolderFilesByParentFolderId(
    parentFolderId: Types.ObjectId | null,
    nameOrder?: string | null
  ): Promise<any> {
    const query =
      parentFolderId === null
        ? { parentFolderId: null }
        : { parentFolderId: parentFolderId };
    console.log("query ", query);
    
    // Add sorting based on nameOrder parameter
    let sortOption = {};
    if (nameOrder === 'asc') {
      sortOption = { originalFileName: 1 }; // Ascending order (A->Z)
    } else if (nameOrder === 'desc') {
      sortOption = { originalFileName: -1 }; // Descending order (Z->A)
    }
    
    const trainingDocumentFolderFiles = await TrainingDocFolderFile.find(query)
      .sort(sortOption)
      .exec();
    return trainingDocumentFolderFiles;
  }

  export async function getTrainingDocumentFolderBylderId(
    folderId: Types.ObjectId
  ): Promise<any> {
    const trainingDocumentFolder = await TrainingDocFolder.findById(
      folderId
    ).exec();
    return trainingDocumentFolder;
  }

  export async function getTrainingDocumentByDocId(
    docId: Types.ObjectId
  ): Promise<any> {
    const file = await TrainingDocFolderFile.findById(docId);

    return file;
  }

  export async function deleteTrainingDocumentByDocId(docId: Types.ObjectId) {
    await TrainingDocFolderFile.findByIdAndDelete(docId);

    return "file delete success";
  }

  export async function deleteTrainingDocumentFolderByFolderId(
    folderId: Types.ObjectId[]
  ) {
    await TrainingDocFolder.deleteMany({
      _id: { $in: folderId },
    });

    return "success";
  }

  export async function getFolderFilesIdsByParentFoldersId(folderId: string) {
    // Aggregate to get all subfolder IDs and the folder itself
    const objectId = new Types.ObjectId(folderId);

    // Find all nested folders
    const folders = await TrainingDocFolder.aggregate([
      {
        $match: { _id: objectId },
      },
      {
        $graphLookup: {
          from: "trainingdocumentsfolders",
          startWith: "$_id",
          connectFromField: "_id",
          connectToField: "parentFolderId",
          as: "subfolders",
        },
      },
      {
        $project: {
          folderIds: {
            $concatArrays: [["$_id"], "$subfolders._id"],
          },
        },
      },
    ]);

    if (folders.length === 0) {
      throw new Error("Folder not found");
    }

    const folderIds = folders[0].folderIds;

    // Find all files in the found folders
    const files = await TrainingDocFolderFile.aggregate([
      {
        $match: { parentFolderId: { $in: folderIds } },
      },
      {
        $group: {
          _id: null,
          files: {
            $push: {
              id: "$_id",
              fileNameInAwsBucket: "$fileNameInAwsBucket",
            },
          },
        },
      },
    ]);

    return {
      folderIds,
      files: files.length > 0 ? files[0].files : [],
    };
  }

  export async function renameFile(
    fileId: string | Types.ObjectId,
    data: Partial<DTrainingDocumentFolderFile>
  ) {
    const result = await TrainingDocFolderFile.findByIdAndUpdate(fileId, data, {
      new: true,
    });

    return result;
  }

  export async function renameFolder(
    folderId: string | Types.ObjectId,
    data: Partial<DTrainingDocumentFolder>
  ): Promise<ITrainingDocumentFolder> {
    const result = await TrainingDocFolder.findByIdAndUpdate(folderId, data, {
      new: true,
    });

    return result;
  }

  export async function searchFoldersFiles(
    searchString: string,
    parentFolderId: null | Types.ObjectId
  ): Promise<any> {

    const pipeline = [
      {
        $match: {
          parentFolderId: parentFolderId,
          folderName: { $regex: searchString, $options: "i" },
        },
      },
    ];

    const pipeline2 = [
      {
        $match: {
          parentFolderId: parentFolderId,
          $or: [
            { originalFileName: { $regex: searchString, $options: "i" } },
            { title: { $regex: searchString, $options: "i" } },
          ],
        },
      },
    ];

    const folders = await TrainingDocFolder.aggregate(pipeline).exec();

    const files = await TrainingDocFolderFile.aggregate(pipeline2).exec();
    return {
      folders,
      files,
    };
  }

  export async function filterTherapistByDocumentAccess(
    searchableString: string,
    limit: number,
    offset: number,

    status?: string,
    grantedStatus?: string
  ): Promise<any[]> {
    const statusQuery =
      status != null && status ? { verifiedStatus: status } : {};

    const grantedStatusQuery =
      grantedStatus === "Granted"
        ? { grantedAccessFileFolderPermission: true }
        : grantedStatus === "Not Granted"
        ? {
            $or: [
              { grantedAccessFileFolderPermission: false },
              { grantedAccessFileFolderPermission: { $exists: false } },
            ],
          }
        : {};

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
            $and: [
              {
                $or: [
                  { firstname: searchedName },
                  { lastname: searchedName },
                  { email: searchedName },
                  { fullName: searchedName },
                ],
              },
            ],
          }
        : {};

    let searchResult: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          // createdAt: 1,
          // photoId: 1,
          role: 1,
          gender: 1,
          // zipCode: 1,
          // state: 1,
          verifiedStatus: 1,
          // blockedByAdmin: 1,
          // profession: 1,
          // lavniTestAccount: 1,
          // primaryPhone: 1,
          // payRateType: 1,
          _id: 1,
          priorityNumber: 1,
          adminApproved: 1,
          // blackTherapyPriorityNumber: 1,
          // relationshipTherapyPriorityNumber: 1,
          // mensMentalTherapyPriorityNumber: 1,
          // claimOpen: 1,
          grantedAccessFileFolderPermission: 1,
          createdAt: 1
        },
      },
      {
        $match: {
          $and: [grantedStatusQuery, statusQuery, clientNameQuery],
        },
      },
      // {
      //   $lookup: {
      //     from: "professions",
      //     localField: "profession",
      //     foreignField: "_id",
      //     pipeline: [
      //       {
      //         $project: { name: 1 },
      //       },
      //     ],
      //     as: "profession",
      //   },
      // },
      // {
      //   $lookup: {
      //     from: "uploads",
      //     localField: "photoId",
      //     foreignField: "_id",
      //     pipeline: [
      //       {
      //         $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
      //       },
      //     ],
      //     as: "photoId",
      //   },
      // },

      // {
      //   $unwind: {
      //     path: "$profession",
      //     preserveNullAndEmptyArrays: true,
      //   },
      // },
      // {
      //   $unwind: {
      //     path: "$photoId",
      //     preserveNullAndEmptyArrays: true,
      //   },
      // },
    ])
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return searchResult;
  }
}
