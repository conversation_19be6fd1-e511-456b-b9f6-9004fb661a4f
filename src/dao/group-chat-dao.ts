import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";
import { DGroupChat, IGroupChat } from "../models/group-chat-model";
import GroupChat from "../schemas/group-chat-schema";

export namespace GroupChatDao {
    export async function createGroupChat(groupChat:DGroupChat):Promise<IGroupChat>{
        const iGroupChat = new GroupChat(groupChat);
        let response = await iGroupChat.save();
        return response;
    }
}
