import { Types } from "mongoose";
import { DArticle, IArticle } from "../models/article-model";
import Article from "../schemas/article-schema";
import { StringOrObjectId } from "../common/util";
import { AppLogger } from "../common/logging";
import Upload from "../schemas/upload-schema";
import { UserDao } from "./user-dao";
import { Reply } from "../models/sub-models/reply-model";
import { Comment } from "../models/sub-models/comment-model";
import { ArticleEp } from "../end-point/article-ep";

export namespace ArticleDao {
  const populateOptions = [
    {
      path: "uploadId",
    },
    {
      path: "createdBy",
      populate: [{ path: "photoId" }],
      select: {
        firstname: 1,
        lastname: 1,
        photoId: 1,
        url: 1,
        blockedByAdmin: 1,
      },
    },
    {
      model: "User",
      path: "comments.userId",
      select: { firstname: 1, lastname: 1, photoId: 1, url: 1 },
      populate: [{ path: "photoId", model: "Upload", select: { url: 1 } }],
    },
    {
      path: "articleTags",
    },
    {
      model: "User",
      path: "comments.replies.userId",
      select: { firstname: 1, lastname: 1, photoId: 1, url: 1 },
      populate: [{ path: "photoId", model: "Upload", select: { url: 1 } }],
    },
  ];

  export async function getArticleById(id: Types.ObjectId): Promise<IArticle> {
    let article: IArticle = await Article.findById(id);
    return article;
  }
  export async function getAllArticle() {
    let articleCount = await Article.count();
    return articleCount;
  }
  export async function getArticleByArticleId(
    id: Types.ObjectId,
    userId: Types.ObjectId
  ): Promise<IArticle> {
    let blockList: Types.ObjectId[] = [];
    let user = await UserDao.getUserByUserId(userId);
    blockList = user.blockedUser;
    let article: IArticle = await Article.findById(id)
      .where("createdBy")
      .nin(blockList)
      .populate(populateOptions);
    // if (article == null) {
    //   AppLogger.info(`Article not found: ${article}`);
    // } else {
    //   AppLogger.info(`Article found, articleId: ${article}`);
    // }
    return article;
  }

  export async function getArticleByArticleIdPublic(
    id: Types.ObjectId
  ): Promise<IArticle> {
    let article: IArticle = await Article.findById(id).populate(
      populateOptions
    );

    return article;
  }

  export async function deleteArticleById(
    id: StringOrObjectId
  ): Promise<IArticle> {
    let deletedArticle: IArticle = await Article.findByIdAndDelete(id);
    AppLogger.info(`Deleted article, articleId: ${deletedArticle._id}`);
    if (deletedArticle.uploadId) {
      let articleImage = await Upload.findByIdAndDelete(
        deletedArticle.uploadId
      );
      AppLogger.info(`Deleted upload, uploadId: ${deletedArticle.uploadId}`);
    }
    return deletedArticle;
  }

  export async function addArticle(
    articleDetails: DArticle
  ): Promise<IArticle> {
    const iArticle = new Article(articleDetails);
    let response = await iArticle.save();

    return response;
  }

  export async function updateArticle(
    articleId: StringOrObjectId,
    articleDetails: DArticle
  ): Promise<IArticle> {
    let response = await Article.findByIdAndUpdate(articleId, articleDetails, {
      new: true,
    });
    return response;
  }
  
  export async function getAllArticlesPublic(limit: number, ids: string[],offset: number): Promise<IArticle[]> {
    const articles = await Article.find({ _id: { $in: ids } })
      .populate(populateOptions)
      .skip(limit * (offset - 1))
      .limit(limit) 

    return articles;
  }
  export async function getAllArticlesStats(
    userId: Types.ObjectId,
    limit: number,
    offset: number,
    sortItem: string,
    sortOrder: number
  ): Promise<any> {
    const response = await Article.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          pipeline: [
            {
              $project: { blockedByAdmin: 1, _id: 1, },
            },
          ],
          as: "createdBy",
        },
      },
      {
        $match: {
          $or: [
            { "createdBy.blockedByAdmin": false },
            { "createdBy.blockedByAdmin": undefined },
          ],
        },
      },
      { $skip: limit * (offset - 1) },
      { $limit: limit },
    ]).sort({ [sortItem]: sortOrder });
    const articleList = await Article.populate(response, populateOptions);
    return articleList;
  }

  export async function getAllArticles(
    userId: Types.ObjectId,
    limit: number,
    offset: number,
    sortItem: string,
    sortOrder: number
  ): Promise<any> {
    const response = await Article.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          as: "createdBy",
        },
      },
      {
        $match: {
          $or: [
            { "createdBy.blockedByAdmin": false },
            { "createdBy.blockedByAdmin": undefined },
          ],
        },
      },
      { $skip: limit * (offset - 1) },
      { $limit: limit },
    ]).sort({ [sortItem]: sortOrder });
    const articleList = await Article.populate(response, populateOptions);
    return articleList;
  }
  export async function getAllPublicArticles(
    limit: number,
    offset: number,
    sortItem: string,
    sortOrder: number
  ): Promise<any> {
    const response = await Article.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          as: "createdBy",
        },
      },
      {
        $match: {
          $or: [
            { "createdBy.blockedByAdmin": false },
            { "createdBy.blockedByAdmin": undefined },
          ],
        },
      },
      { $skip: limit * (offset - 1) },
      { $limit: limit },
    ]).sort({ [sortItem]: sortOrder });
    const articleList = await Article.populate(response, populateOptions);
    return articleList;
  }

  export async function getAllArticlesCount(
    userId: Types.ObjectId
  ): Promise<any> {
    const response = await Article.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          as: "createdBy",
        },
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $or: [
            { "createdBy.blockedByAdmin": false },
            { "createdBy.blockedByAdmin": undefined },
          ],
        },
      },
      {
        $count: "articleCount",
      },
    ]);

    return response[0].articleCount;
  }
  export async function getAllPublicArticlesCount(): Promise<any> {
    const response = await Article.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          as: "createdBy",
        },
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $or: [
            { "createdBy.blockedByAdmin": false },
            { "createdBy.blockedByAdmin": undefined },
          ],
        },
      },
      {
        $count: "articleCount",
      },
    ]);

    return response[0].articleCount;
  }

  export async function updateArticleField(
    articleId: Types.ObjectId,
    articleDetails: any
  ): Promise<IArticle> {
    const response = await Article.findByIdAndUpdate(
      articleId,
      { likedBy: articleDetails },
      { new: true }
    ).populate(populateOptions);
    return response;
  }

  export async function addComment(
    articleId: Types.ObjectId,
    commentDetails: any
  ): Promise<IArticle> {
    const response = await Article.findByIdAndUpdate(
      articleId,
      { $push: { comments: commentDetails } },
      { new: true }
    ).populate(populateOptions);
    return response;
  }

  export async function addReply(
    articleId: StringOrObjectId,
    commentId: StringOrObjectId,
    reply: string,
    userId: Types.ObjectId
  ): Promise<any> {
    const article = await Article.findById(articleId);

    const replyDetails: Reply = {
      userId: userId,
      reply: reply,
      date: new Date(),
    };

    let updatedCommentList = article.comments.map((item) => {
      if (item._id.toString() === commentId.toString()) {
        item.replies.push(replyDetails);
      }
      return item;
    });

    const updatedList = await Article.findByIdAndUpdate(
      articleId,
      { comments: updatedCommentList },
      { new: true }
    ).populate(populateOptions);

    return updatedList;
  }

  export async function searchArticlesByTags(
    searchTags: Types.ObjectId[],
    type: string,
    userId: Types.ObjectId,
    limit: number,
    offset: number
  ): Promise<IArticle[]> {
    let query = null;
    if (type === "me") {
      if (searchTags.length > 0) {
        query = {
          articleTags: { $in: searchTags },
          createdBy: userId,
        };
      } else {
        query = {
          createdBy: userId,
        };
      }
    }
    if (type === "all") {
      if (searchTags.length > 0) {
        query = { articleTags: { $in: searchTags } };
      } else {
        query = {};
      }
    }
    const response = await Article.find(query)
      .populate(populateOptions)
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return response;
  }

  export async function searchArticlesByTagsCount(
    searchTags: Types.ObjectId[],
    type: string,
    userId: Types.ObjectId
  ): Promise<number | any> {
    let query = null;
    if (type === "me") {
      if (searchTags.length > 0) {
        query = {
          articleTags: { $in: searchTags },
          createdBy: userId,
        };
      } else {
        query = {
          createdBy: userId,
        };
      }
    }
    if (type === "all") {
      if (searchTags.length > 0) {
        query = { articleTags: { $in: searchTags } };
      } else {
        query = {};
      }
    }
    const response = await Article.find(query).countDocuments();
    return response;
  }

  export async function searchArticlesByTagsAndHashTags(
    userId: Types.ObjectId,
    experienceTags: Types.ObjectId[],
    hashTags: string[],
    limit: number,
    offset: number
  ): Promise<IArticle[]> {
    let query = {};
    let blockList: Types.ObjectId[] = [];
    if (experienceTags.length > 0 || hashTags.length > 0) {
      query = {
        $or: [
          {
            articleTags: {
              $in: experienceTags.length > 0 ? experienceTags : [],
            },
          },
          {
            hashTags: { $in: hashTags.length > 0 ? hashTags : [] },
          },
        ],
      };
    }
    const user = await UserDao.getUserByUserId(userId);
    blockList = user.blockedUser;
    const response = await Article.find(query)
      .where("createdBy")
      .nin(blockList)
      .populate(populateOptions)
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return response;
  }
  export async function searchPublicArticlesByTagsAndHashTags(
    experienceTags: Types.ObjectId[],
    hashTags: string[],
    limit: number,
    offset: number
  ): Promise<IArticle[]> {
    let query = {};
    let blockList: Types.ObjectId[] = [];
    if (experienceTags.length > 0 || hashTags.length > 0) {
      query = {
        $or: [
          {
            articleTags: {
              $in: experienceTags.length > 0 ? experienceTags : [],
            },
          },
          {
            hashTags: { $in: hashTags.length > 0 ? hashTags : [] },
          },
        ],
      };
    }

    const response = await Article.find(query)
      .where("createdBy")

      .populate(populateOptions)
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return response;
  }

  export async function searchArticlesByTagsAndHashTagsCount(
    userId: Types.ObjectId,
    experienceTags: Types.ObjectId[],
    hashTags: string[]
  ): Promise<any> {
    let query = {};
    let blockList: Types.ObjectId[] = [];
    if (experienceTags.length > 0 || hashTags.length > 0) {
      query = {
        $or: [
          {
            articleTags: {
              $in: experienceTags.length > 0 ? experienceTags : [],
            },
          },
          {
            hashTags: { $in: hashTags.length > 0 ? hashTags : [] },
          },
        ],
      };
    }
    const user = await UserDao.getUserByUserId(userId);
    blockList = user.blockedUser;
    const response = await Article.find(query)
      .where("createdBy")
      .nin(blockList)
      .countDocuments();
    return response;
  }
  export async function searchPublicArticlesByTagsAndHashTagsCount(
    experienceTags: Types.ObjectId[],
    hashTags: string[]
  ): Promise<any> {
    let query = {};

    if (experienceTags.length > 0 || hashTags.length > 0) {
      query = {
        $or: [
          {
            articleTags: {
              $in: experienceTags.length > 0 ? experienceTags : [],
            },
          },
          {
            hashTags: { $in: hashTags.length > 0 ? hashTags : [] },
          },
        ],
      };
    }

    const response = await Article.find(query)
      .where("createdBy")

      .countDocuments();
    return response;
  }

  export async function getPopularPosts(): Promise<IArticle[]> {
    const response = await Article.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          as: "createdBy",
        },
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $or: [
            { "createdBy.blockedByAdmin": false },
            { "createdBy.blockedByAdmin": undefined },
          ],
        },
      },
      {
        $unwind: "$likedBy",
      },
      {
        $group: {
          _id: {
            _id: "$_id",
            articleTitle: "$articleTitle",
            uploadId: "$uploadId",
            createdAt: "$createdAt",
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
      {
        $limit: 5,
      },
      {
        $project: { articleTitle: 1, createdAt: 1, uploadId: 1 },
      },
    ]);

    let filteredArticles = await Article.populate(response, [
      { path: "_id.uploadId", model: "Upload" },
    ]);
    return filteredArticles;
  }

  export async function updateComment(
    articleId: StringOrObjectId,
    data: Comment[]
  ): Promise<IArticle | IArticle[]> {
    let updatedList = await Article.findByIdAndUpdate(
      articleId,
      { $set: { comments: data } },
      { new: true }
    ).populate(populateOptions);
    return updatedList;
  }

  export async function deleteCommentsByUserId(
    userId: StringOrObjectId
  ): Promise<number> {
    let response = await Article.updateMany({
      $pull: { comments: { userId: userId } },
    });
    return response.ok;
  }

  export async function deleteRepliesByUserId(
    userId: StringOrObjectId
  ): Promise<number> {
    let response = await Article.updateMany({
      $pull: { "comments.$[].replies": { userId: userId } },
    });
    return response.ok;
  }

  export async function deleteLikesByUserId(
    userId: StringOrObjectId
  ): Promise<number> {
    let response = await Article.updateMany({ $pull: { likedBy: userId } });
    return response.ok;
  }

  export async function deleteArticlesByTherapistId(
    therapistId: StringOrObjectId,
    deleteUploads: (value: StringOrObjectId) => Promise<boolean>
  ): Promise<number> {
    const articleList = await Article.find({ createdBy: therapistId });

    if (articleList.length > 0) {
      await Promise.all(
        articleList.map(async (article: any) => {
          if (!article.vimeoId) {
            deleteUploads(article.uploadId._id);
          }
        })
      );
    }

    const response = await Article.deleteMany({ createdBy: therapistId });

    return response.ok;
  }
}
