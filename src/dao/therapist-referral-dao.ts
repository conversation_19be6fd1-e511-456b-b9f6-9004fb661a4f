import { ITherapistReferral } from "../models/therapist-referral-model";
import TherapistReferral from "../schemas/therapist-referral-schema";

export namespace TherapistReferralDao {

  export async function addDetails(
    referrerUserId: ObjectId,
    referredUserId: ObjectId,
  ): Promise<ITherapistReferral> {

    const referralPlan = new TherapistReferral({
      referrerUserId: referrerUserId,
      referredUserId: referredUserId,
      firstSessionCompletionBonusForReferrer: null,
      twentySessionsCompletionBonusForReferrer: null
    });

    const therapistReferral = await referralPlan.save();

    return therapistReferral;
  }

}

