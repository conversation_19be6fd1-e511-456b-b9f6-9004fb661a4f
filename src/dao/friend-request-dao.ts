import {Types} from "mongoose";
import {StringOrObjectId} from "../common/util";
import {DFriendRequest, FriendRequestStatus, IFriendRequest,} from "../models/friend-request-model";
import {UserRole} from "../models/user-model";
import FriendRequest from "../schemas/friend-request-schema";
import Client from "../schemas/client-schema";
import {IClient} from "../models/client-model";
import {DDeletedFriendRequest} from "../models/deleted-friend-request-model";
import DeletedFriendRequest from "../schemas/deleted-friend-request-schema";

export namespace FriendRequestDao {
    const populateOptions = [
        {
            path: "clientId",
            select: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                email: 1,
                gender: 1,
                ethnicityId: 1,
                primaryPhone: 1,
                subscriptionId: 1,
                subscriptionStatus: 1,
                premiumStatus: 1,
                clientActiveStatus: 1,
            },
            populate: [
                {path: "photoId", model: "Upload"}
            ],
        },
        {
            path: "therapistId",
            select: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                experiencedIn: 1,
                email: 1,
                primaryPhone: 1,
            },
            populate: [
                {path: "photoId", model: "Upload"}
            ],
        },
    ];

    export async function getRequestById(
        id: StringOrObjectId
    ): Promise<IFriendRequest> {
        let request: IFriendRequest = await FriendRequest.findById(id).populate(
            populateOptions
        );

        return request;
    }

    export async function updateRequest(
        id: StringOrObjectId,
        data: Partial<DFriendRequest>
    ): Promise<IFriendRequest> {
        let request = await FriendRequest.findByIdAndUpdate(
            {_id: id},
            {$set: data},
            {new: true}
        ).populate(populateOptions);

        return request;
    }

    export async function createRequestByClient(
        requestDetails: DFriendRequest
    ): Promise<IFriendRequest> {
        const iRequest = new FriendRequest(requestDetails);

        let request = await iRequest.save();

        let response = FriendRequestDao.getRequestById(request._id);

        return response;
    }

    export async function getAllFriendsByTherapistIdAdmin(
        therapistId: Types.ObjectId
    ): Promise<IFriendRequest[]> {
        const response = await FriendRequest.find({
            therapistId: therapistId,
            status: FriendRequestStatus.APPROVED,
        }).populate(populateOptions);
        return response;
    }

    export async function getAllRequestsByTherapistIdStats(
        therapistId: Types.ObjectId,
        limit: number,
        offset: number
    ): Promise<IFriendRequest[]> {
        const queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
        };
        const res = await FriendRequest.aggregate([
            {
                $match: {
                    $and: [
                        {therapistId: therapistId},
                        {status: {$ne: FriendRequestStatus.REJECTED}},
                    ],
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "clientId",
                },
            },
            {
                $unwind: {
                    path: "$clientId",
                },
            },
            {
                $match: {
                    $and: [queryConditions],
                    $or: [
                        {"clientId.blockedByAdmin": false},
                        {"clientId.blockedByAdmin": undefined},
                    ],
                },
            },
        ])
            .sort({createdAt: -1})
            .skip(limit * (offset - 1))
            .limit(limit);

        const response = FriendRequest.populate(res, populateOptions);

        return response;
    }

    export async function getTherapistMeetingStatsById(
        therapistId: Types.ObjectId
    ): Promise<IFriendRequest[]> {

        const uncompletedFormsCount = await FriendRequest.aggregate([
            {
                $match: {
                    therapistId: therapistId,
                    status: FriendRequestStatus.APPROVED
                }
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "clientInfo"
                }
            },
            {
                $unwind: "$clientInfo"
            },
            {
                $match: {
                    $or: [
                        {"clientInfo.blockedByAdmin": false},
                        {"clientInfo.blockedByAdmin": {$exists: false}}
                    ]
                }
            },
            {
                $lookup: {
                    from: "meetings",
                    let: {clientId: "$clientId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", therapistId]},
                                        {$eq: ["$callingStatus", "COMPLETED"]}
                                    ]
                                }
                            }
                        },
                        {
                            $count: "meetingCount"
                        }
                    ],
                    as: "meetingsInfo"
                }
            },
            {
                $unwind: "$meetingsInfo"
            },
            {
                $lookup: {
                    from: "clinicalassesments",
                    let: {clientId: "$clientId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", therapistId]}
                                    ]
                                }
                            }
                        },
                        {
                            $limit: 1
                        }
                    ],
                    as: "clinicalAssessments"
                }
            },
            {
                $lookup: {
                    from: "digitalassessments",
                    let: {clientId: "$clientId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", therapistId]}
                                    ]
                                }
                            }
                        },
                        {
                            $limit: 1
                        }
                    ],
                    as: "digitalAssessments"
                }
            },
            {
                $lookup: {
                    from: "therapyplans",
                    let: {clientId: "$clientId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", therapistId]}
                                    ]
                                }
                            }
                        },
                        {
                            $limit: 1
                        }
                    ],
                    as: "therapyPlans"
                }
            },
            {
                $project: {
                    clientId: 1,
                    meetingCount: "$meetingsInfo.meetingCount",
                    hasClinicalAssessment: {$size: "$clinicalAssessments"},
                    hasDigitalAssessment: {$size: "$digitalAssessments"},
                    hasAssessment: {$add: ["$hasClinicalAssessment", "$hasDigitalAssessment"]},
                    hasTherapyPlan: {$size: "$therapyPlans"}
                }
            },
            {
                $group: {
                    _id: null,
                    allMeetingCount: {$sum: "$meetingCount"},
                    uncompletedAssessmentsCount: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {$gte: ["$meetingCount", 1]},
                                        {$eq: ["$hasAssessment", 0]}
                                    ]
                                },
                                1,
                                0
                            ]
                        }
                    },
                    uncompletedTherapyPlanCount: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {$gte: ["$meetingCount", 2]},
                                        {$eq: ["$hasTherapyPlan", 0]}
                                    ]
                                },
                                1,
                                0
                            ]
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    allMeetingCount: 1,
                    uncompletedAssessmentsCount: 1,
                    uncompletedTherapyPlanCount: 1
                }
            }
        ]);

        const uncompletedFormCountOne = uncompletedFormsCount[0] || {
            uncompletedAssessmentsCount: 0,
            uncompletedTherapyPlanCount: 0
        };

        const uncompletedAuthFormsCount = await FriendRequest.aggregate([
            {
                $match: {
                    therapistId: therapistId,
                    status: FriendRequestStatus.APPROVED
                }
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "clientInfo"
                }
            },
            {
                $unwind: "$clientInfo"
            },
            {
                $match: {
                    $or: [
                        {"clientInfo.blockedByAdmin": false},
                        {"clientInfo.blockedByAdmin": {$exists: false}}
                    ]
                }
            },
            {
                $lookup: {
                    from: "meetings",
                    let: {clientId: "$clientId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", therapistId]},
                                        {$eq: ["$callingStatus", "COMPLETED"]}
                                    ]
                                }
                            }
                        },
                        {
                            $count: "meetingCount"
                        }
                    ],
                    as: "meetingsInfo"
                }
            },
            {
                $unwind: "$meetingsInfo"
            },
            {
                $match: {
                    "meetingsInfo.meetingCount": {$gte: 2}
                }
            },
            {
                $lookup: {
                    from: "insurances",
                    localField: "clientId",
                    foreignField: "clientId",
                    as: "insuranceDetails"
                }
            },
            {
                $unwind: "$insuranceDetails"
            },
            {
                $lookup: {
                    from: "insurancecompanies",
                    localField: "insuranceDetails.insuranceCompanyId",
                    foreignField: "_id",
                    as: "insuranceCompanyDetails"
                }
            },
            {
                $unwind: "$insuranceCompanyDetails"
            },
            {
                $match: {
                    "insuranceCompanyDetails.fax": {$exists: true, $ne: null},
                    "insuranceCompanyDetails.authorizationFormAvailability": true
                }
            },
            {
                $lookup: {
                    from: "authorizationforms",
                    let: {clientId: "$clientId", insuranceCompanyId: "$insuranceDetails.insuranceCompanyId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", therapistId]},
                                        {$eq: ["$insuranceCompanyId", "$$insuranceCompanyId"]}
                                    ]
                                }
                            }
                        },
                        {
                            $limit: 1
                        }
                    ],
                    as: "authorizationForms"
                }
            },
            {
                $group: {
                    _id: null,
                    totalRequiredAuthForms: {$sum: 1},
                    totalCompletedAuthForms: {
                        $sum: {
                            $cond: [
                                {$gt: [{$size: "$authorizationForms"}, 0]},
                                1,
                                0
                            ]
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    uncompletedAuthorizationFormsCount: {
                        $max: [
                            {$subtract: ["$totalRequiredAuthForms", "$totalCompletedAuthForms"]},
                            0
                        ]
                    }
                }
            }
        ]);

        const uncompletedFormCountTwo = uncompletedAuthFormsCount[0] || {
            uncompletedAuthorizationFormsCount: 0
        };

        const result = {
            ...uncompletedFormCountOne,
            ...uncompletedFormCountTwo
        };

        return result;
    }

    export async function getAllRequestsByTherapistId(
        therapistId: Types.ObjectId,
        gender: string,
        ethnicity: string,
        searchClientName: string,
        sortAlphabetically: string,
        limit: number,
        offset: number
    ): Promise<IFriendRequest[]> {
        const genderQuery =
            gender != null && gender ? {"clientId.gender": gender} : {};
        const ethnicityQuery =
            ethnicity != null && ethnicity
                ? {"clientId.ethnicityId": Types.ObjectId(ethnicity)}
                : {};

        let searchedName = null;

        if (searchClientName) {
            let seacrhItem = searchClientName.replace(/\s/g, "");
            searchedName =
                searchClientName != null ? new RegExp(`^${seacrhItem}`, "i") : null;
        }

        const clientNameQuery =
            searchedName != null && searchedName
                ? {
                    $or: [
                        {"clientId.firstname": searchedName},
                        {"clientId.lastname": searchedName},
                        {"clientId.fullName": searchedName},
                    ],
                }
                : {};
        const queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
        };

        const dataSortingMethod = sortAlphabetically === "alphabetically"
            ? {
                "$sort": {
                    "clientId.firstnameLowerCase": 1,
                    "clientId.lastnameLowerCase": 1
                }
            }
            : {"$sort": {createdAt: -1}};

        const res = await FriendRequest.aggregate([
            {
                $match: {
                    $and: [
                        {therapistId: therapistId},
                        {status: {$ne: FriendRequestStatus.REJECTED}},
                    ],
                    lavniTestAccount: {$ne: true},
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "clientId",
                },
            },
            {
                $unwind: {
                    path: "$clientId",
                },
            },
            {
                $match: {
                    $and: [genderQuery, ethnicityQuery, clientNameQuery, queryConditions],
                    $or: [
                        {"clientId.blockedByAdmin": false},
                        {"clientId.blockedByAdmin": undefined},
                    ],
                },
            },
            {
                $addFields: {
                    "clientId.firstnameLowerCase": {$toLower: "$clientId.firstname"},
                    "clientId.lastnameLowerCase": {$toLower: "$clientId.lastname"}
                }
            },
            dataSortingMethod,
        ])
            .skip(limit * (offset - 1))
            .limit(limit);

        const response = FriendRequest.populate(res, populateOptions);

        return response;
    }

    export async function getAllRequestsByClient(
        clientId: Types.ObjectId,
        limit?: number,
        offset?: number
    ): Promise<IFriendRequest[]> {
        const aggregateQuery = [
            {
                $match: {
                    $and: [
                        {clientId: clientId},
                        {status: {$ne: FriendRequestStatus.REJECTED}},
                    ],
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "therapistId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                experiencedIn: 1,
                                photoId: 1,
                                coverPhotoId: 1,
                                adminApproved: 1,
                                blockedByAdmin: 1,
                                firstname: 1,
                                lastname: 1,
                                role: 1,
                                vimeoId: 1,
                            },
                        },
                    ],
                    as: "therapistId",
                },
            },
            {
                $unwind: {
                    path: "$therapistId",
                },
            },
            {
                $match: {
                    $or: [
                        {"therapistId.blockedByAdmin": false},
                        {"therapistId.blockedByAdmin": undefined},
                    ],
                },
            },
            {
                $lookup: {
                    from: "experiencetags",
                    localField: "therapistId.experiencedIn",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {experienceTag: 1},
                        },
                    ],
                    as: "therapistId.experiencedIn",
                },
            },
            {
                $lookup: {
                    from: "uploads",
                    localField: "therapistId.photoId",
                    foreignField: "_id",
                    as: "therapistId.photoId",
                },
            },
            {
                $lookup: {
                    from: "uploads",
                    localField: "therapistId.coverPhotoId",
                    foreignField: "_id",
                    as: "therapistId.coverPhotoId",
                },
            },
            {
                $unwind: {
                    path: "$therapistId.photoId",
                    preserveNullAndEmptyArrays: true,
                },
            }
        ];

        if (limit) {
            const res = await FriendRequest.aggregate(aggregateQuery).skip(limit * (offset - 1)).limit(limit);

            return res;
        } else {
            const res = await FriendRequest.aggregate(aggregateQuery);

            return res;
        }
    }

    export async function checkIfUserIsFriend(
        clientId: Types.ObjectId,
        therapistId: Types.ObjectId
    ): Promise<IFriendRequest> {
        const response = await FriendRequest.findOne({
            clientId: clientId,
            therapistId: therapistId,
            $or: [{status: FriendRequestStatus.APPROVED,}, {status: FriendRequestStatus.PENDING}]
        }).populate(populateOptions);

        return response;
    }

    export async function getAllRequestsByClientId(
        clientId: StringOrObjectId
    ): Promise<IFriendRequest[]> {
        const response = await FriendRequest.find({
            clientId: clientId,
        })
            .where("status")
            .ne(FriendRequestStatus.REJECTED)
            .populate(populateOptions);

        return response;
    }

    export async function deleteRequestById(
        id: StringOrObjectId
    ): Promise<IFriendRequest> {
        const response = await FriendRequest.findByIdAndDelete(id).populate(
            populateOptions
        );
        return response;
    }

    export async function getRequestByIdAndUserId(
        requestId: StringOrObjectId,
        userId: StringOrObjectId,
        role: UserRole
    ): Promise<IFriendRequest> {
        let request: IFriendRequest = null;

        if (role == UserRole.THERAPIST) {
            request = await FriendRequest.findOne({
                _id: requestId,
                therapistId: userId,
            }).populate(populateOptions);
        }

        if (role == UserRole.CLIENT) {
            request = await FriendRequest.findOne({
                _id: requestId,
                clientId: userId,
            }).populate(populateOptions);
        }

        return request;
    }

    export async function deleteAllRequestsByClientId(
        clientId: StringOrObjectId
    ): Promise<number> {
        const response = await FriendRequest.deleteMany({
            clientId: clientId,
        });

        return response.ok;
    }

    export async function deleteAllRequestsByTherapistId(
        therapistId: StringOrObjectId
    ): Promise<number> {
        const response = await FriendRequest.deleteMany({
            therapistId: therapistId,
        });

        return response.ok;
    }

    export async function getFriendRequestByTherapistIdAndClientId(
        clientId: Types.ObjectId,
        therapistId: Types.ObjectId
    ): Promise<IFriendRequest[]> {
        const response = await FriendRequest.find({
            clientId: clientId,
            therapistId: therapistId,
        });
        return response;
    }

    export async function getAllFriendRequestsBefore30Minutes(): Promise<
        IFriendRequest[]
    > {
        const now = new Date();
        const start = new Date(now.getTime() - 60 * 60000);
        const end = new Date(now.getTime() - 30 * 60000);

        const response = await FriendRequest.find({
            $and: [
                {
                    createdAt: {
                        $gte: start,
                        $lte: end,
                    },
                },
                {
                    status: "APPROVED",
                },
            ],
        })
            .populate(populateOptions)
            .sort({createdAt: -1});

        return response;
    }

    export async function getAllPendingVerifyAccountsBefore10Minutes(): Promise<
        IClient[]
    > {
        const now = new Date();
        const start = new Date(now.getTime() - 15 * 60000);
        const end = new Date(now.getTime() - 5 * 60000);

        const response = await Client.find({
            $and: [
                {
                    createdAt: {
                        $gte: start,
                        $lte: end,
                    },
                },
                {
                    verifiedStatus: "PENDING",
                },
            ],
        }).sort({createdAt: -1});

        return response;
    }

    export async function getInsuranceOrNoSubcriptionAccountsBefore10Minutes(): Promise<
        IClient[]
    > {
        const now = new Date();
        const start = new Date(now.getTime() - 15 * 60000);
        const end = new Date(now.getTime() - 5 * 60000);
        const response = await Client.find({
            $and: [
                {
                    createdAt: {
                        $gte: start,
                        $lte: end,
                    },
                },
                {
                    verifiedStatus: "VERIFIED",
                },
                {
                    $or: [{insuranceId: {$exists: false}}, {insuranceId: null}],
                },
                {
                    $or: [
                        {subscriptionId: {$exists: false}},
                        {subscriptionId: null},
                    ],
                },
            ],
        }).sort({createdAt: -1});

        return response;
    }

    export async function getInsuranceOrNoSubcriptionAccountsBefore48Minutes(): Promise<
        IClient[]
    > {
        const now = new Date();
        const start = new Date(now.getTime() - 48 * 60 * 60 * 1000 - 15 * 60000);
        const end = new Date(now.getTime() - 48 * 60 * 60 * 1000 + 15 * 60000);
        const response = await Client.find({
            $and: [
                {
                    updatedAt: {
                        $gte: start,
                        $lte: end,
                    },
                },
                {
                    verifiedStatus: "VERIFIED",
                },
                {
                    $or: [{insuranceId: {$exists: false}}, {insuranceId: null}],
                },
                {
                    $or: [
                        {subscriptionId: {$exists: false}},
                        {subscriptionId: null},
                    ],
                },
            ],
        }).sort({createdAt: -1});

        return response;
    }

    export async function createDeletedFriendRequest(
        requestDetails: DDeletedFriendRequest
    ) {
        const iDeletedRequest = new DeletedFriendRequest(requestDetails);

        await iDeletedRequest.save();
    }
}
