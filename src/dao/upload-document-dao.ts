import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";
import { DUploadDocument, IUploadDocument } from "../models/upload-documents-model";
import UploadDocument from "../schemas/upload-document-schema";

export namespace UploadDocumentDao {


  export async function CreateUploadDocument(data: DUploadDocument): Promise<IUploadDocument> {
    const doc = new UploadDocument(data);
    let createDoc = await doc.save();

    return createDoc;
  }

  export async function viewUploadDocumentByClientId(
    clientId: StringOrObjectId,
    therapistId: StringOrObjectId,
    limit: number,
    offset: number
  ): Promise<IUploadDocument[]> {
    const response = await UploadDocument.find({
      clientId: clientId,
      therapistId: therapistId,
    })
      .sort({ createdAt: -1 }).skip(limit * (offset - 1))
      .limit(limit)
      .populate([{ path: "uploadDocumentId" }, { path: "clientId" }, { path: "therapistId" }]);
    let res = response;
    return res;
  }

  // export async function viewUploadDocumentByClientId(clientId: Types.ObjectId, therapisId: Types.ObjectId): Promise<IUploadDocument[]> {
  //   const res = await UploadDocument.aggregate([
  //     {
  //       $match: {
  //         $and: [
  //           { "clientId": clientId },
  //           { "therapisId": therapisId }
  //         ]
  //       }
  //     },
  //     {
  //       $lookup: {
  //         from: "users",
  //         localField: "clientId",
  //         foreignField: "_id",
  //         as: "clientId",
  //       }
  //     },
  //     {
  //       $unwind: {
  //         path: "$clientId",
  //         preserveNullAndEmptyArrays: true,
  //       }
  //     },
  //     {
  //       $lookup: {
  //         from: "users",
  //         localField: "therapisId",
  //         foreignField: "_id",
  //         as: "therapisId",
  //       },
  //     },
  //     {
  //       $unwind: {
  //         path: "$therapisId",
  //         preserveNullAndEmptyArrays: true,
  //       },
  //     },
  //     {
  //       $match: {
  //         $and: [
  //           {
  //             $or: [
  //               { "clientId.blockedByAdmin": false },
  //               { "clientId.blockedByAdmin": undefined },
  //             ],
  //           },
  //           {
  //             $or: [
  //               { "therapisId.blockedByAdmin": false },
  //               { "therapisId.blockedByAdmin": undefined },
  //             ],
  //           },
  //         ],
  //       },
  //     },
  //   ]).sort({ createdAt: -1 });

  //   const response = await UploadDocument.populate(res, [{ path: "uploads" }, { path: "clientId" }, { path: "therapisId" }]);

  //   return response;
  // }
}