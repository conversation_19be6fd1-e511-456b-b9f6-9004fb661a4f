import { DTechTicket, ITechTicket } from "../models/tech-ticket-model";
import TechTicket from "../schemas/tech-ticket-schema";
import { Types } from "mongoose";

export namespace TechTicketDao {
  export async function createTechTicket(
    techTicketDetails: DTechTicket
  ): Promise<ITechTicket> {
    const iTechTicket = new TechTicket(techTicketDetails);

    let response = await iTechTicket.save();

    return response;
  }

  export async function getAllTechTickets(limit?: number, offset?: number ): Promise<ITechTicket[]> {
    const techTicketList = await TechTicket.find()
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .populate([
        { path: "createdBy", select: {firstname: 1, lastname: 1, email: 1} },
        { path: "uploadId", select: {path: 1, url: 1, originalName: 1} }
      ])
      .limit(limit);

    return techTicketList;
  }

  export async function getAllTechTicketUnreadCount(): Promise<number> {
    const response = await TechTicket.find({ isRead: false }).countDocuments();

    return response;
  }

  export async function updateTechTicket(
    id: Types.ObjectId,
    data: Partial<DTechTicket>
  ): Promise<ITechTicket> {
    const updatedTechTicket = await TechTicket.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true }
    );

    return updatedTechTicket;
  }
}
