import { Types } from "mongoose";
import User from "../schemas/user-schema";
import Client from "../schemas/client-schema";
import { FriendRequestStatus, IFriendRequest } from "../models/friend-request-model";
import FriendRequest from "../schemas/friend-request-schema";
import { IUser } from "../models/user-model";
import { IClient } from "../models/client-model";
import { DSaasTherapistTask, ISaasTherapistTask } from "../models/saas-therapist-task-model";
import SaasTherapistTask from "../schemas/saas-therapist-task-schema";
import { StringOrObjectId } from "../common/util";
import { IInsuranceAndClient } from "../models/insurance-model";
import Insurance from "../schemas/insurance-schema";

export namespace SaasTherapistDao {
    const populateOptions = [
        { path: "qualifications" },
        { path: "qualifications", populate: { path: "uploadId" } },
        { path: "ethnicityId" },
        { path: "disclosureStatementId" },
        { path: "photoId" },
        // { path: "coverPhotoId" },
        { path: "videoId" },
        { path: "licenseId" },
        { path: "licenseId", populate: { path: "uploadId" } },
        { path: "coverPhotoId" },
        { path: "profession" },
        { path: "professionLicense" },
        { path: "experiencedIn" },
        { path: "insuranceCompanies" },
    ];

    export async function getAllClients(
        therapistId: Types.ObjectId,
        limit?: number,
        offset?: number
    ): Promise<IFriendRequest[]> {
        const pipeline: any[] = [
            {
                $match: {
                    therapistId: therapistId,
                    status: FriendRequestStatus.APPROVED,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                firstname: 1,
                                lastname: 1,
                                primaryPhone: 1,
                                clientActiveStatus: 1,
                                photoId: 1,
                                blockedByAdmin: 1
                            }
                        },
                    ],
                    as: "clientDetails",
                },
            },
            { $unwind: "$clientDetails" },
            {
                $match: {
                    $or: [
                        { "clientDetails.blockedByAdmin": false },
                        { "clientDetails.blockedByAdmin": { $exists: false } }
                    ],
                }
            },
            {
                $lookup: {
                    from: "meetings",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                        { $eq: ["$callingStatus", "COMPLETED"] },
                                    ],
                                },
                            },
                        },
                        { $count: "meetingCount" },
                    ],
                    as: "meetingInfo",
                },
            },
            {
                $addFields: {
                    meetingCount: {
                        $ifNull: [{ $arrayElemAt: ["$meetingInfo.meetingCount", 0] }, 0],
                    },
                },
            },
            {
                $lookup: {
                    from: "diagnosisnotes",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                        { $eq: ["$updatedByTherapist", true] },
                                        { $eq: ["$updated", true] },
                                    ],
                                },
                            },
                        },
                        { $count: "completedNotesCount" },
                    ],
                    as: "completedNotesInfo",
                },
            },
            {
                $addFields: {
                    incompletedNotesCount: {
                        $subtract: ["$meetingCount", { $ifNull: [{ $arrayElemAt: ["$completedNotesInfo.completedNotesCount", 0] }, 0] }],
                    },
                    hasSessionNotes: {
                        $gt: [{ $arrayElemAt: ["$completedNotesInfo.completedNotesCount", 0] }, 0]
                    }
                },
            },
            {
                $lookup: {
                    from: "diagnosisnotes",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                    ],
                                },
                            },
                        },
                        { $count: "notesCount" },
                    ],
                    as: "notesInfo",
                },
            },
            {
                $addFields: {
                    hasSessionNotes: {
                        $gt: [{ $arrayElemAt: ["$notesInfo.notesCount", 0] }, 0]
                    }
                },
            },
            {
                $lookup: {
                    from: "appointments",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                        { $eq: ["$meetingStatus", "COMPLETED"] },
                                        { $lt: ["$start", new Date()] }
                                    ]
                                }
                            }
                        },
                        { $sort: { start: -1 } },
                        { $limit: 1 },
                        { $project: { start: 1 } }
                    ],
                    as: "previousSessionInfo"
                }
            },
            {
                $lookup: {
                    from: "appointments",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                        { $gt: ["$start", new Date()] }
                                    ]
                                },
                                meetingStatus: { $exists: false }
                            }
                        },
                        { $sort: { start: 1 } },
                        { $limit: 1 },
                        { $project: { start: 1 } }
                    ],
                    as: "nextSessionInfo"
                }
            },
            {
                $addFields: {
                    remainingSessions: {
                        $max: [
                            { $subtract: [52, "$meetingCount"] },
                            0
                        ]
                    }
                }
            },
            {
                $project: {
                    _id: 1,
                    clientDetails: 1,
                    meetingCount: 1,
                    completedNotesCount: 1,
                    incompletedNotesCount: 1,
                    remainingSessions: 1,
                    hasSessionNotes: 1,
                    previousSession: {
                        $arrayElemAt: ["$previousSessionInfo.start", 0],
                    },
                    nextSession: {
                        $arrayElemAt: ["$nextSessionInfo.start", 0]
                    }
                },
            },
        ];

        if (limit && offset) {
            pipeline.push({ $skip: limit * (offset - 1) });
            pipeline.push({ $limit: limit });
        }

        const clientList = await FriendRequest.aggregate(pipeline).sort({ "clientDetails.firstname": 1 });

        return clientList;
    }

    export async function getPersonalDetails(
        clientId: Types.ObjectId
    ): Promise<IClient> {
        const personalInfo = await Client.findOne({ _id: clientId });
        return personalInfo;
    }

    export async function updatePersonalDetails(
        clientId: Types.ObjectId,
        updateData: Partial<IClient>
    ): Promise<IClient> {
        const updatedClient = await Client.findByIdAndUpdate(
            clientId,
            { $set: updateData },
            { new: true }
        );
        return updatedClient;
    }

    // This function need to be optimized.
    export async function getTodoDetails(
        therapistId: Types.ObjectId
    ): Promise<any> {
        const pendingClinicalDocuments = await FriendRequest.aggregate([
            {
                $match: {
                    therapistId: therapistId,
                    status: FriendRequestStatus.APPROVED,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        { $project: { _id: 1, firstname: 1, lastname: 1 } },
                    ],
                    as: "clientDetails",
                },
            },
            { $unwind: "$clientDetails" },
            {
                $lookup: {
                    from: "meetings",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                        { $eq: ["$callingStatus", "COMPLETED"] },
                                    ],
                                },
                            },
                        },
                        { $count: "meetingCount" },
                    ],
                    as: "meetingInfo",
                },
            },
            {
                $addFields: {
                    meetingCount: {
                        $ifNull: [{ $arrayElemAt: ["$meetingInfo.meetingCount", 0] }, 0],
                    },
                },
            },
            {
                $lookup: {
                    from: "clinicalassesments",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                    ],
                                },
                            },
                        },
                    ],
                    as: "clinicalAssessments",
                },
            },
            {
                $addFields: {
                    clinicalAssessmentCount: { $size: "$clinicalAssessments" },
                },
            },
            {
                $lookup: {
                    from: "digitalassessments",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                    ],
                                },
                            },
                        },
                    ],
                    as: "digitalAssessments",
                },
            },
            {
                $addFields: {
                    digitalAssessmentCount: { $size: "$digitalAssessments" },
                },
            },
            {
                $match: {
                    $and: [
                        { clinicalAssessmentCount: 0 },
                        { digitalAssessmentCount: 0 },
                    ],
                },
            },
            {
                $project: {
                    _id: 0,
                    clientDetails: 1,
                    meetingCount: 1,
                    clinicalAssessmentCount: 1,
                    digitalAssessmentCount: 1,
                },
            },
        ]);
        const pendingTherapyPlans = await FriendRequest.aggregate([
            {
                $match: {
                    therapistId: therapistId,
                    status: FriendRequestStatus.APPROVED,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        { $project: { _id: 1, firstname: 1, lastname: 1 } },
                    ],
                    as: "clientDetails",
                },
            },
            { $unwind: "$clientDetails" },
            {
                $lookup: {
                    from: "therapyplans",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] }
                                    ]
                                }
                            }
                        }
                    ],
                    as: "therapyPlans",
                }
            },
            {
                $addFields: {
                    therapyPlanCount: { $size: "$therapyPlans" },
                },
            },
            {
                $match: {
                    therapyPlanCount: 0,
                },
            },
            {
                $project: {
                    _id: 0,
                    clientDetails: 1,
                    therapyPlanCount: 1,
                },
            },
        ]);
        const pendingAuthorizationForms = await FriendRequest.aggregate([
            {
                $match: {
                    therapistId: therapistId,
                    status: FriendRequestStatus.APPROVED,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        { $project: { _id: 1, firstname: 1, lastname: 1, insuranceId: 1 } },
                    ],
                    as: "clientDetails",
                },
            },
            {
                $unwind: "$clientDetails"
            },
            {
                $lookup: {
                  from: "insurances",
                  let: { userInsuranceId: "$clientDetails.insuranceId" },
                  pipeline: [
                    { $match: { $expr: { $eq: ["$_id", "$$userInsuranceId"] } } },
                    { $project: { insuranceCompanyId: 1 } }
                  ],
                  as: "primaryInsurance"
                }
            },
            {
                $unwind: {
                    path: "$primaryInsurance",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    primaryInsuranceCompanyId: "$primaryInsurance.insuranceCompanyId"
                }
            },
            {
                $lookup: {
                    from: "insurances",
                    localField: "clientId",
                    foreignField: "clientId",
                    as: "insuranceDetails"
                }
            },
            {
                $unwind: {
                    path: "$insuranceDetails",
                    preserveNullAndEmptyArrays: false
                }
            },
            {
                $lookup: {
                    from: "insurancecompanies",
                    localField: "insuranceDetails.insuranceCompanyId",
                    foreignField: "_id",
                    as: "insuranceCompanyDetails"
                }
            },
            {
                $unwind: {
                    path: "$insuranceCompanyDetails",
                    preserveNullAndEmptyArrays: false
                }
            },
            {
                $lookup: {
                    from: "authorizationforms",
                    let: {
                        clientId: "$clientId",
                        therapistId: "$therapistId",
                        insuranceCompanyId: "$insuranceCompanyDetails._id"
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                        { $eq: ["$insuranceCompanyId", "$$insuranceCompanyId"] }
                                    ],
                                },
                            },
                        },
                    ],
                    as: "authorizationForms",
                },
            },
            {
                $match: {
                    authorizationForms: { $eq: [] }
                }
            },
            {
                $group: {
                  _id: { clientId: "$clientId" },
                  clientDetails: { $first: "$clientDetails" },
                  primaryInsuranceCompanyId: { $first: "$primaryInsuranceCompanyId" },
                  insuranceCompanies: {
                    $addToSet: {
                      _id: "$insuranceCompanyDetails._id",
                      isPrimary: { $eq: ["$insuranceCompanyDetails._id", "$primaryInsuranceCompanyId"] },
                      authorizationFormAvailability: "$insuranceCompanyDetails.authorizationFormAvailability",
                      authFormType: "$insuranceCompanyDetails.authorizationFormType"
                    }
                  },
                }
            },
            {
                $project: {
                  _id: 0,
                  clientDetails: {
                    _id: "$clientDetails._id",
                    firstname: "$clientDetails.firstname",
                    lastname: "$clientDetails.lastname",
                    email: "$clientDetails.email"
                  },
                  insuranceCompanies: 1,
                }
            },
        ]);
        const pendingSessionNotes = await FriendRequest.aggregate([
            {
                $match: {
                    therapistId: therapistId,
                    status: FriendRequestStatus.APPROVED,
                },
            },
            {
                $lookup: {
                    from: "meetings",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                        { $eq: ["$callingStatus", "COMPLETED"] },
                                    ],
                                },
                            },
                        },
                    ],
                    as: "meetingInfo",
                },
            },
            {
                $unwind: {
                    path: "$meetingInfo",
                    preserveNullAndEmptyArrays: false
                }
            },
            {
                $lookup: {
                    from: "diagnosisnotes",
                    let: {
                        clientId: "$meetingInfo.clientId",
                        therapistId: "$meetingInfo.therapistId",
                        meetingId: "$meetingInfo._id"
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                        { $eq: ["$meetingId", "$$meetingId"] },
                                        { $eq: ["$updatedByTherapist", false] }
                                    ],
                                },
                            },
                        },
                    ],
                    as: "incompletedSessionNotes",
                },
            },
            {
                $unwind: {
                    path: "$incompletedSessionNotes",
                    preserveNullAndEmptyArrays: false
                }
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        { $project: { _id: 1, firstname: 1, lastname: 1 } },
                    ],
                    as: "clientDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientDetails",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $group: {
                    _id: "$clientDetails._id",
                    clientDetails: { $first: "$clientDetails" },
                    pendingCount: { $sum: 1 }
                }
            },
            {
                $project: {
                    _id: 0,
                    clientDetails: {
                        _id: "$clientDetails._id",
                        firstname: "$clientDetails.firstname",
                        lastname: "$clientDetails.lastname",
                        pendingCount: "$pendingCount"
                    }
                }
            }
        ]);
        const pendingAppointments = await FriendRequest.aggregate([
            {
                $match: {
                    therapistId: therapistId,
                    status: FriendRequestStatus.APPROVED,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        { $project: { _id: 1, firstname: 1, lastname: 1 } },
                    ],
                    as: "clientDetails",
                },
            },
            { $unwind: "$clientDetails" },
            {
                $lookup: {
                    from: "appointments",
                    let: { clientId: "$clientId", therapistId: "$therapistId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $eq: ["$therapistId", "$$therapistId"] },
                                        { $gt: ["$start", new Date()] }, // Future appointments only
                                    ],
                                },
                                meetingStatus: { $exists: false },
                            },
                        },
                    ],
                    as: "futureAppointments",
                },
            },
            {
                $match: {
                    futureAppointments: { $size: 0 },
                },
            },
            {
                $project: {
                    _id: 0,
                    clientDetails: 1,
                    futureAppointments: 1,
                },
            },
        ]);

        const data = {
            pendingClinicalDocuments: pendingClinicalDocuments,
            pendingTherapyPlans: pendingTherapyPlans,
            pendingAuthorizationForms: pendingAuthorizationForms,
            pendingAppointments: pendingAppointments,
            pendingSessionNotes: pendingSessionNotes,
        };
        return data;
    }

    export async function getTaskDetails(
        therapistId: Types.ObjectId,
        limit: number = 10,
        offset: number = 0
    ): Promise<ISaasTherapistTask[]> {
        const taskList = await SaasTherapistTask.find({ therapistId: therapistId })
            .skip(offset)
            .limit(limit);
        return taskList;
    }

    export async function createTask(
        saasTherapistData: DSaasTherapistTask,
    ): Promise<ISaasTherapistTask> {
        const saasTherapistTask = new SaasTherapistTask(saasTherapistData);
        await saasTherapistTask.save();
        return saasTherapistTask;
    }

    export async function updateTaskById(
        _id: Types.ObjectId,
        updatedData: Partial<ISaasTherapistTask>
    ): Promise<ISaasTherapistTask> {
        const updatedTask = await SaasTherapistTask.findByIdAndUpdate(
            _id,
            { $set: updatedData },
            { new: true }
        ).exec();
        return updatedTask;
    }

    export async function deleteTaskById(
        taskId: Types.ObjectId,
    ): Promise<ISaasTherapistTask> {
        const saasTherapistTask = await SaasTherapistTask.findByIdAndDelete(taskId);
        return saasTherapistTask;
    }

    export async function getInsuranceListByClientId(
        clientId: StringOrObjectId
    ): Promise<IInsuranceAndClient> {
        const client = await User.findOne({ _id: clientId }).select("insuranceId coverPhotoId");
        const insurance = await Insurance.find({ clientId: clientId })
            .populate(populateOptions)
            .populate({
                path: 'insuranceCompanyId',
                model: 'InsuranceCompany',
                select: 'insuranceCompany coPayment',
            }).sort({ createdAt: -1 });
        const response = {
            client: client,
            insurance: insurance
        };
        return response;
    }

    export async function searchClientsByTherapist(
        searchableString: string,
        limit: number,
        offset: number,
        gender?: string,
        status?: string,
        isSubscription?: string,
        zipCode?: string,
        therapistId?: Types.ObjectId,
        clientActiveStatus?: string,
        appointmentStatus?: string,
        insuranceActiveStatus?: string,
        copaymentStatus?: string,
    ): Promise<IFriendRequest[]> {
        const genderQuery = gender != null && gender ? {gender: gender} : {};
        const statusQuery = status != null && status ? {verifiedStatus: status} : {};
        let insuranceActiveStatusQuery = {};
        if (insuranceActiveStatus) {
            if (insuranceActiveStatus === "WITH_INSURANCE") {
                insuranceActiveStatusQuery = {
                    $or: [
                        {insuranceId: {$exists: true, $ne: null}},
                        {secondaryInsuranceId: {$exists: true, $ne: null}}
                    ],
                };
            } else if (insuranceActiveStatus === "WITHOUT_INSURANCE") {
                insuranceActiveStatusQuery = {
                    $and: [
                        {
                            $or: [
                                {insuranceId: {$exists: false}},
                                {insuranceId: null},
                            ],
                        },
                        {
                            $or: [
                                {secondaryInsuranceId: {$exists: false}},
                                {secondaryInsuranceId: null},
                            ],
                        }
                    ]
                };
            }
        }
        const zipCodeQuery = zipCode != null && zipCode ? {zipCode: zipCode} : {};
        const subscriptionQuery =
            isSubscription != null && isSubscription
                ? {subscriptionStatus: isSubscription}
                : {};

        let clientActiveStatusQuery = {};

        if (clientActiveStatus == "ACTIVE") {
            clientActiveStatusQuery = {
                $or: [
                    {clientActiveStatus: true},
                    {clientActiveStatus: {$exists: false}}
                ]
            };
        } else if (clientActiveStatus === "INACTIVE") {
            clientActiveStatusQuery = {clientActiveStatus: false};
        }
        let upcomingMatchQuery = {};
        const currentDate = new Date();
        if (appointmentStatus === "UPCOMMING_APPOINTMENT") {
            upcomingMatchQuery = {'appointments.start': {$gte: currentDate}};
        } else if (appointmentStatus === "NO_UPCOMMING_APPOINTMENT") {
            upcomingMatchQuery = {
                $or: [
                    {'appointments.start': {$exists: false}},
                    {'appointments.start': {$not: {$gte: currentDate}}}
                ]
            };
        }

        let searchedName = null;

        if (searchableString) {
            let seacrhItem = searchableString.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            searchedName = searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
        }

        let searchableStringQuery =
            searchedName != null && searchedName
                ? {
                    $and: [
                        {
                            $or: [
                                {firstname: searchedName},
                                {lastname: searchedName},
                                {email: searchedName},
                                {fullName: searchedName},
                                {primaryPhone: searchedName}
                            ],
                        },
                    ],
                }
                : {};

        let copaymentStatusQuery = {};
        if (copaymentStatus) {
            if (copaymentStatus === "EXISTING") {
                copaymentStatusQuery = {
                    copaymentAmount: {$exists: true},
                };
            } else if (copaymentStatus === "GREATER") {
                copaymentStatusQuery = {
                    copaymentAmount: {$exists: true, $gt: 0},
                };
            }
        }


        const pipeline: any[] = [
            {
                $match: {
                    therapistId: therapistId,
                    status: FriendRequestStatus.APPROVED,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $match: {
                                $and: [
                                    searchableStringQuery,
                                    genderQuery,
                                    statusQuery,
                                    insuranceActiveStatusQuery,
                                    zipCodeQuery,
                                    subscriptionQuery,
                                    clientActiveStatusQuery,
                                    copaymentStatusQuery,
                                ],
                            },
                        },
                        {
                            $project: {
                                _id: 1,
                                firstname: 1,
                                lastname: 1,
                                primaryPhone: 1,
                                clientActiveStatus: 1,
                                photoId: 1
                            }
                        },
                    ],
                    as: "clientDetails",
                },
            },
            {$unwind: "$clientDetails"},
            {
                $lookup: {
                    from: "meetings",
                    let: {clientId: "$clientId", therapistId: "$therapistId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", "$$therapistId"]},
                                        {$eq: ["$callingStatus", "COMPLETED"]},
                                    ],
                                },
                            },
                        },
                        {$count: "meetingCount"},
                    ],
                    as: "meetingInfo",
                },
            },
            {
                $addFields: {
                    meetingCount: {
                        $ifNull: [{$arrayElemAt: ["$meetingInfo.meetingCount", 0]}, 0],
                    },
                },
            },
            {
                $lookup: {
                    from: "diagnosisnotes",
                    let: {clientId: "$clientId", therapistId: "$therapistId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", "$$therapistId"]},
                                        {$eq: ["$updatedByTherapist", true]},
                                        {$eq: ["$updated", true]},
                                    ],
                                },
                            },
                        },
                        {$count: "completedNotesCount"},
                    ],
                    as: "completedNotesInfo",
                },
            },
            {
                $addFields: {
                    completedNotesCount: {
                        $ifNull: [{$arrayElemAt: ["$completedNotesInfo.completedNotesCount", 0]}, 0],
                    },
                    incompletedNotesCount: {
                        $subtract: ["$meetingCount", {$ifNull: [{$arrayElemAt: ["$completedNotesInfo.completedNotesCount", 0]}, 0]}],
                    },
                },
            },
            {
                $lookup: {
                    from: "treatmenthistories",
                    let: {clientId: "$clientId", therapistId: "$therapistId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", "$$therapistId"]},
                                        {$lt: ["$meetingStartedTime", new Date()]}
                                    ]
                                }
                            }
                        },
                        {$sort: {meetingStartedTime: -1}},
                        {$limit: 1},
                        {$project: {meetingStartedTime: 1}}
                    ],
                    as: "previousSessionInfo"
                }
            },
            {
                $lookup: {
                    from: "treatmenthistories",
                    let: {clientId: "$clientId", therapistId: "$therapistId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$clientId", "$$clientId"]},
                                        {$eq: ["$therapistId", "$$therapistId"]},
                                        {$gt: ["$meetingStartedTime", new Date()]}
                                    ]
                                }
                            }
                        },
                        {$sort: {meetingStartedTime: 1}},
                        {$limit: 1},
                        {$project: {meetingStartedTime: 1}}
                    ],
                    as: "nextSessionInfo"
                }
            },
            {
                $addFields: {
                    remainingSessions: {
                        $subtract: [52, "$meetingCount"]
                    }
                }
            },
            {
                $project: {
                    _id: 1,
                    clientDetails: 1,
                    meetingCount: 1,
                    completedNotesCount: 1,
                    incompletedNotesCount: 1,
                    remainingSessions: 1,
                    previousSession: {
                        $arrayElemAt: ["$previousSessionInfo.meetingStartedTime", 0],
                    },
                    nextSession: {
                        $arrayElemAt: ["$nextSessionInfo.meetingStartedTime", 0]
                    }
                },
            },
        ];

        if (limit && offset) {
            pipeline.push({$skip: limit * (offset - 1)});
            pipeline.push({$limit: limit});
        }

        const clientList = await FriendRequest.aggregate(pipeline).sort({"clientDetails.firstname": 1});
        return clientList;
    }
}