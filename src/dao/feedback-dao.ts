import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";
import { DFeedback, IFeedback } from "../models/feedback-model";
import Feedback from "../schemas/feedback-schema";
import { DSessionFeedBack, ISessionFeedBack } from "../models/session-feedback-model";
import SessionFeedback from "../schemas/session-feedback-schema";

export namespace FeedbackDao {
  export async function addFeedback(feedback: DFeedback): Promise<IFeedback> {
    const iFeedback = new Feedback(feedback);
    let response = await iFeedback.save();
    return response;
  }

  export async function updateFeedback(
    id: Types.ObjectId,
    data: Partial<DFeedback>
  ): Promise<IFeedback> {
    const updatedFeedback = await Feedback.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true }
    );
    return updatedFeedback;
  }

  export async function getAllFeedbacks(
    limit: number,
    offset: number
  ): Promise<IFeedback[]> {
    const response = await Feedback.find()
      .populate([{ path: "userId", select: { firstname: 1, lastname: 1 } }])
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return response;
  }

  export async function getAllFeedbackCount(): Promise<number> {
    const response = await Feedback.find({isRead:false}).countDocuments();
    return response;
  }

  export async function deleteAllFeedbackByUserId(user:StringOrObjectId): Promise<number> {
    const response = await Feedback.deleteMany({userId:user});
    return response.ok;
  }

  export async function createSessionFeedback(data: DSessionFeedBack): Promise<ISessionFeedBack> {
    const iSessionFeedback: ISessionFeedBack = new SessionFeedback(data);
    return await iSessionFeedback.save();
  }

  export async function getAllSessionFeedbacks(
    limit: number,
    offset: number,
    searchableString?: string,
    therapistId?: string
  ): Promise<ISessionFeedBack[]> {
    const response = await SessionFeedback.aggregate([
      {
        $lookup: {
          from: 'meetings',
          localField: 'meetingId',
          foreignField: 'meetingId',
          as: 'meetingDetails'
        }
      },
      {
        $unwind: '$meetingDetails'
      },
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          as: 'createdByDetails'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'meetingDetails.clientId',
          foreignField: '_id',
          as: 'clientDetails'
        }
      },
      {
        $unwind: '$clientDetails'
      },
      {
        $lookup: {
          from: 'users',
          localField: 'meetingDetails.therapistId',
          foreignField: '_id',
          as: 'therapistDetails'
        }
      },
      {
        $unwind: '$therapistDetails'
      },
      {
        $addFields: {
          clientFullName: {
            $concat: [
              { $ifNull: ['$clientDetails.firstname', ''] },
              ' ',
              { $ifNull: ['$clientDetails.lastname', ''] }
            ]
          }
        }
      },
      ...(therapistId || searchableString ? [{
        $match: {
          $and: [
            ...(therapistId ? [{ 'therapistDetails._id': new Types.ObjectId(therapistId) }] : []),
            ...(searchableString ? [{
              $or: [
                { clientFullName: { $regex: searchableString.trim(), $options: 'i' } },
                { 'clientDetails.email': { $regex: searchableString.trim(), $options: 'i' } },
                { 'clientDetails.primaryPhone': { $regex: searchableString.trim(), $options: 'i' } }
              ]
            }] : [])
          ]
        }
      }] : []),
      {
        $project: {
          'createdByDetails.firstname': 1,
          'createdByDetails.lastname': 1,
          'createdByDetails.role': 1,
          'meetingDetails.createdAt': 1,
          feedback: 1,
          rate: 1,
          createdAt: 1,
          'clientDetails.firstname': 1,
          'clientDetails.lastname': 1,
          'clientDetails.email': 1,
          'clientDetails.primaryPhone': 1,
          'therapistDetails.firstname': 1,
          'therapistDetails.lastname': 1
        }
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $skip: limit * (offset - 1)
      },
      {
        $limit: limit
      }
    ]);
    return response;
  }

  export async function getAllSessionFeedbacksNeedCall(
    limit: number,
    offset: number,
    searchableString?: string,
    therapistId?: string
  ): Promise<ISessionFeedBack[]> {
    const response = await SessionFeedback.aggregate([
      {
        $match: { needCall: true }
      },
      {
        $lookup: {
          from: 'meetings', 
          localField: 'meetingId',
          foreignField: 'meetingId', 
          as: 'meetingDetails' 
        }
      },
      {
        $unwind: '$meetingDetails'
      },
      {
        $lookup: {
          from: 'transcribes',
          localField: 'meetingId',
          foreignField: 'meetingId',
          as: 'transcriptionDetails'
        }
      },
      {
        $addFields: {
          transcriptionText: { $ifNull: [{ $arrayElemAt: ["$transcriptionDetails.transcriptText", 0] }, []] }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy', 
          foreignField: '_id', 
          as: 'createdByDetails'
        }
      },
      {
        $unwind: '$createdByDetails'
      },
      {
        $lookup: {
          from: 'users', 
          localField: 'meetingDetails.clientId', 
          foreignField: '_id', 
          as: 'clientDetails' 
        }
      },
      {
        $unwind: '$clientDetails'
      },
      {
        $lookup: {
          from: 'users', 
          localField: 'meetingDetails.therapistId', 
          foreignField: '_id',
          as: 'therapistDetails' 
        }
      },
      {
        $unwind: '$therapistDetails'
      },
      {
        $lookup: {
          from: 'insurances',
          localField: 'clientDetails.insuranceId',
          foreignField: '_id',
          as: 'insuranceDetails'
        }
      },
      {
        $unwind: {
          path: '$insuranceDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'insurancecompanies',
          localField: 'insuranceDetails.insuranceCompanyId',
          foreignField: '_id',
          as: 'insuranceCompanyDetails'
        }
      },
      {
        $unwind: {
          path: '$insuranceCompanyDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $addFields: {
          clientFullName: {
            $concat: [
              { $ifNull: ['$clientDetails.firstname', ''] },
              ' ',
              { $ifNull: ['$clientDetails.lastname', ''] }
            ]
          }
        }
      },
      ...(therapistId || searchableString ? [{
        $match: {
          $and: [
            ...(therapistId ? [{ 'therapistDetails._id': new Types.ObjectId(therapistId) }] : []),
            ...(searchableString ? [{
              $or: [
                { clientFullName: { $regex: searchableString.trim(), $options: 'i' } },
                { 'clientDetails.email': { $regex: searchableString.trim(), $options: 'i' } },
                { 'clientDetails.primaryPhone': { $regex: searchableString.trim(), $options: 'i' } }
              ]
            }] : [])
          ]
        }
      }] : []),
      {
        $project: {
          _id: 1,
          meetingId: 1,
          feedback: 1,
          rate: 1,
          createdAt: 1,
          needCall: 1,
          therapistComfortable: 1,
          satisfied: 1,
          nextSessionScheduled: 1,
          transcriptionText: 1,
          is_read: { $ifNull: ["$is_read", false] },
          'meetingDetails': {
            createdAt: '$meetingDetails.createdAt',
            spentDuration: '$meetingDetails.meetingDuration'
          },
          'clientDetails': {
            _id: '$clientDetails._id',
            firstname: '$clientDetails.firstname',
            lastname: '$clientDetails.lastname',
            email: '$clientDetails.email',
            primaryPhone: '$clientDetails.primaryPhone',
            insurance: {
              companyName: '$insuranceCompanyDetails.name',
              memberId: '$insuranceDetails.subscriber.memberId'
            }
          },
          'therapistDetails': {
            _id: '$therapistDetails._id',
            firstname: '$therapistDetails.firstname',
            lastname: '$therapistDetails.lastname'
          },
          'createdByDetails': {
            _id: '$createdByDetails._id',
            firstname: '$createdByDetails.firstname',
            lastname: '$createdByDetails.lastname',
            role: '$createdByDetails.role'
          }
        }
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $skip: limit * (offset - 1)
      },
      {
        $limit: limit
      }
    ]);
    return response;
  }

  export async function updateSessionFeedback(
    id: Types.ObjectId | string,
    data: Partial<DSessionFeedBack>
  ): Promise<ISessionFeedBack> {
    const updatedSessionFeedback = await SessionFeedback.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true }
    );
    return updatedSessionFeedback;
  }

  export async function getSessionFeedbacksNeedCallCount(): Promise<number> {
    const count = await SessionFeedback.countDocuments({ needCall: true, is_read: false });
    return count;
  }
}
