import { Types } from "mongoose";
import { DAmbetterAuthForm } from "../models/ambetter-auth-form-model";
import { DAmeriHealthAuthForm } from "../models/ameri-health-auth-form-model";
import { IAuthorizationForm } from "../models/authorization-form-model";
import { DCarolinaCompleteHealthAuthForm } from "../models/carolina-complete-health-auth-form-model";
import { DHealthyBlueAuthForm } from "../models/healthy-blue-auth-form-model";
import { DUnitedHealthCareAuthForm } from "../models/united-health-care-auth-form-model";
import { DWellCareAuthForm } from "../models/wellcare-auth-form-model";
import AuthorizationForm from "../schemas/authorization-form-schema";
import { IInsuranceCompany } from "../models/Insurance-company-model";
import InsuranceCompany from "../schemas/Insurance-company-schema";

export namespace AuthorizationFormDao {
  export async function createAmeriHealthAuthForm(
    ameriHealthData: DAmeriHealthAuthForm,
  ): Promise<IAuthorizationForm> {
    const ameriHealthAuthForm = new AuthorizationForm(ameriHealthData);
    await ameriHealthAuthForm.save();
    return ameriHealthAuthForm;
  }

  export async function createCarolinaCompleteHealthAuthForm(
    carolinaCompleteHealthData: DCarolinaCompleteHealthAuthForm,
  ): Promise<IAuthorizationForm> {
    const carolinaCompleteHealthAuthForm = new AuthorizationForm(carolinaCompleteHealthData);
    await carolinaCompleteHealthAuthForm.save();
    return carolinaCompleteHealthAuthForm;
  }

  export async function createUnitedHealthCareAuthForm(
    unitedHealthCareData: DUnitedHealthCareAuthForm,
  ): Promise<IAuthorizationForm> {
    const unitedHealthCareAuthForm = new AuthorizationForm(unitedHealthCareData);
    await unitedHealthCareAuthForm.save();
    return unitedHealthCareAuthForm;
  }

  export async function createHealthyBlueAuthForm(
    healthyBlueData: DHealthyBlueAuthForm,
  ): Promise<IAuthorizationForm> {
    const healthyBlueAuthForm = new AuthorizationForm(healthyBlueData);
    await healthyBlueAuthForm.save();
    return healthyBlueAuthForm;
  }

  export async function createWellCareAuthForm(
    wellCareData: DWellCareAuthForm,
  ): Promise<IAuthorizationForm> {
    const wellCareAuthForm = new AuthorizationForm(wellCareData); 
    await wellCareAuthForm.save(); 
    return wellCareAuthForm; 
  }

  export async function createAmbetterAuthForm(
    ambetterData: DAmbetterAuthForm,
  ): Promise<IAuthorizationForm> {
    const ambetterAuthForm = new AuthorizationForm(ambetterData); 
    await ambetterAuthForm.save(); 
    return ambetterAuthForm; 
  }

  export async function getAuthorizationFormData(
    clientId: Types.ObjectId,
    insuranceCompanyId: Types.ObjectId,
    therapistId: Types.ObjectId
  ): Promise<IAuthorizationForm | null> {
    const authorizationForm = await AuthorizationForm.findOne({ clientId: clientId, insuranceCompanyId: insuranceCompanyId, therapistId: therapistId });
    return authorizationForm;
  }

  export async function updateAuthorizationFormById(
    _id: Types.ObjectId,
    updatedData: Partial<IAuthorizationForm>
  ): Promise<IAuthorizationForm> {
    const updatedForm = await AuthorizationForm.findByIdAndUpdate(
      _id,
      { $set: updatedData },
      { new: true }
    ).exec();
    return updatedForm;
  }

  export async function getAllInsuranceCompanyList(): Promise<IInsuranceCompany[]> {
    const InsuranceCompanyList = await InsuranceCompany.find()
      .sort({ createdAt: -1 })
      .select('_id insuranceCompany');
    return InsuranceCompanyList;
  }

  export async function authorizationFormSubmissionConfirmationByTherapist(
    authSubmissionData: any,
  ): Promise<any> {
    const authSubmissionForm = new AuthorizationForm(authSubmissionData);
    await authSubmissionForm.save();
    return authSubmissionForm;
  }

}
