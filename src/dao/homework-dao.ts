import { IHomework } from "../models/homework-model";
import Homework from "../schemas/homework-schema";
import { DHomework } from './../models/homework-model';
import { StringOrObjectId } from '../common/util';
import { Types } from "mongoose";

export namespace HomeworkDao {
  export async function createHomework(homework: DHomework): Promise<IHomework> {
    const iHomework = new Homework(homework);
    let res = await iHomework.save();
    return res;
  }

  export async function updateHomework(homeworkId: Types.ObjectId, homework: DHomework): Promise<IHomework> {
    let res = await Homework.findByIdAndUpdate(homeworkId, homework, { new: true }).populate([
      { path: "uploads" },
      { path: "createdBy" },
      { path: "assignedFor" }
    ]);
    return res;
  }

  export async function getHomeworksByUserId(clientId: Types.ObjectId, therapisId: Types.ObjectId, limit: number,
    offset: number): Promise<IHomework[]> {
    const res = await Homework.aggregate([
      {
        $match: {
          $and: [
            { "assignedFor": clientId },
            { "createdBy": therapisId }
          ]
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          as: "createdBy",
        }
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "assignedFor",
          foreignField: "_id",
          as: "assignedFor",
        },
      },
      {
        $unwind: {
          path: "$assignedFor",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $and: [
            {
              $or: [
                { "createdBy.blockedByAdmin": false },
                { "createdBy.blockedByAdmin": undefined },
              ],
            },
            {
              $or: [
                { "assignedFor.blockedByAdmin": false },
                { "assignedFor.blockedByAdmin": undefined },
              ],
            },
          ],
        },
      },
    ]).sort({ createdAt: -1 }).skip(limit * (offset - 1))
      .limit(limit);

    const response = await Homework.populate(res, [{ path: "uploads" }, { path: "createdBy" }, { path: "assignedFor" }]);

    return response;
  }

  export async function getHomeworkById(homeworkId: StringOrObjectId): Promise<IHomework> {
    const res = await Homework.findById(homeworkId).populate([
      { path: "uploads" },
      { path: "createdBy" },
      { path: "assignedFor" }
    ]);
    return res;
  }

  export async function getHomeworkByIdWithoutPoppulate(homeworkId: StringOrObjectId): Promise<IHomework> {
    const res = await Homework.findById(homeworkId);
    return res;
  }

  export async function deleteHomeworkById(id: StringOrObjectId): Promise<IHomework> {
    const res = await Homework.findByIdAndDelete(id);
    return res;
  }

  export async function deleteHomeworkByClientId(clientId: StringOrObjectId, deleteUploads: (value: StringOrObjectId) => Promise<boolean>): Promise<number> {
    const homeworkList = await Homework.find({ assignedFor: clientId });

    if (homeworkList.length > 0) {
      await Promise.all(
        homeworkList.map((homework: any) => {
          if (homework.uploads.length > 0) {
            homework.uploads.map(async (upload: any) => {
              deleteUploads(upload);
            });
          }
        })
      );
    }

    const response = await Homework.deleteMany({ assignedFor: clientId });

    return response.ok;
  }

  export async function deleteHomeworkByTherapistId(therapistId: StringOrObjectId, deleteUploads: (value: StringOrObjectId) => Promise<boolean>): Promise<number> {
    const homeworkList = await Homework.find({ createdBy: therapistId });

    if (homeworkList.length > 0) {
      await Promise.all(
        homeworkList.map((homework: any) => {
          if (homework.uploads.length > 0) {
            homework.uploads.map(async (upload: any) => {
              deleteUploads(upload);
            })
          }
        })
      );
    }

    const response = await Homework.deleteMany({ createdBy: therapistId });

    return response.ok;
  }
}