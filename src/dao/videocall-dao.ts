import { IHomework } from "../models/homework-model";
import Homework from "../schemas/homework-schema";
import { DHomework } from "../models/homework-model";
import { StringOrObjectId, Util } from "../common/util";
import { Types } from "mongoose";
import { CallingStatus, DMeeting, IMeeting } from "../models/meeting-model";
import Meeting from "../schemas/meeting-schema";
import Transcribe from "../schemas/transcribe-schema";
import { DTranscribe, ITranscribe } from "../models/transcribe-model";
import Transaction from "../schemas/transaction-schema";
import { ITransaction } from "../models/transaction-model";
import Appointment from "../schemas/appointment-schema";
import { AppointmentStatus } from "../models/appointment-model";
import { DDiagnosisNote, IDiagnosisNote } from "../models/diagnosis-note-model";
import DiagnosisNote from "../schemas/diagnosis-note-schema";
import { UserRole } from "../models/user-model";
import { IClient } from "../models/client-model";
import Upload from "../schemas/upload-schema";
import User from "../schemas/user-schema";
import Client from "../schemas/client-schema";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import { DTreatmentHistory, ITreatmentHistory } from "../models/treatment-history-model";

export namespace VideoCallDao {
  export async function createMeeting(meeting: DMeeting): Promise<IMeeting> {
    const iMeeting = new Meeting(meeting);
    let res = await iMeeting.save();
    return res;
  }

  export async function updateMeetingStatus(
    id: string,
    value: boolean
  ): Promise<IMeeting> {
    const updatedMeeting = await Meeting.findOneAndUpdate(
      { meetingId: id },
      { accepted: value },
      { new: true }
    );
    return updatedMeeting;
  }

  export async function updateAppointmentStatus(
    id: any,
    value: AppointmentStatus
  ): Promise<any> {
    const updated = await Appointment.findByIdAndUpdate(id, { status: value });
    return updated;
  }
  export async function updateAppointmentCallingTries(
    id: any,
    role: any
  ): Promise<any> {
    let previous = await Appointment.findById(id);
    let updated;
    if (role == UserRole.CLIENT) {
      let count = 1;
      if (
        previous &&
        previous.noOfCallingTriesByClient &&
        previous.noOfCallingTriesByClient != null
      ) {
        count = previous.noOfCallingTriesByClient + 1;
      }
      updated = await Appointment.findByIdAndUpdate(id, {
        noOfCallingTriesByClient: count,
      });
    } else {
      let count = 1;
      if (
        previous &&
        previous.noOfCallingTriesByTherapist &&
        previous.noOfCallingTriesByTherapist != null
      ) {
        count = previous.noOfCallingTriesByTherapist + 1;
      }
      updated = await Appointment.findByIdAndUpdate(id, {
        noOfCallingTriesByTherapist: count,
      });
    }

    return updated;
  }

  export async function getMeetingByMeetingId(
    meetingid: string,
    therapistid: any
  ): Promise<IMeeting> {
    const meetingDetails = await Meeting.findOne({
      meetingId: meetingid,
      accepted: true,
      therapistId: therapistid,
      transcribingInProcess: false,
      transcribeCreated: false,
    });
    return meetingDetails;
  }

  export async function getMeetingByMeetingIdLink(
    meetingid: string,
    therapistid: any
  ): Promise<IMeeting> {
    const meetingDetails = await Meeting.findOne({
      meetingId: meetingid,
      accepted: true,
      therapistId: therapistid,
      transcribingInProcess: false,
      transcribeCreated: true,
    });
    return meetingDetails;
  }

  export async function getMeetingByAudioId(
    uploadId: Types.ObjectId
  ): Promise<IMeeting> {
    const meetingDetails = await Meeting.findOne({
      audioFiles: { $in: [uploadId] },
      accepted: true,
      recordingSharedWithClient: true,
    });
    return meetingDetails;
  }

  export async function getTransactionBuyMeetingId(
    meetingid: any,
    therapistid: any
  ): Promise<ITransaction> {
    const trans = await Transaction.findOne({
      meetingId: meetingid,
      therapistId: therapistid,
    });

    return trans;
  }

  export async function getTransactionById(
    transactionId: any,
    therapistid: any
  ): Promise<ITransaction> {
    const trans = await Transaction.findOne({
      _id: transactionId,
      therapistId: therapistid,
    });

    return trans;
  }

  export async function getMeetingByMeetingIdOnly(
    meetingid: string
  ): Promise<IMeeting> {
    const meetingDetails = await Meeting.findOne({
      meetingId: meetingid,
    });

    return meetingDetails;
  }


  export async function getMeetingByMeetingIdOnlyTest(meetingid: StringOrObjectId): Promise<IMeeting> {
    const meetingDetails = await Meeting.findOne({
      _id: meetingid,
    });
    return meetingDetails;
  }

  export async function getTranscribeByMeetingIdAndUrlId(
    meetingid: string,
    url: any
  ): Promise<ITranscribe> {
    const transcribeDetails = await Transcribe.findOne({
      meetingId: meetingid,
      videoUrl: url,
    });

    return transcribeDetails;
  }
  export async function getTranscribeByMeetingId(
    meetingId: string
  ): Promise<ITranscribe> {
    const transcribeDetails = await Transcribe.findOne({
      meetingId: meetingId
    });

    return transcribeDetails;
  }

  export async function getTranscribeById(id: any): Promise<ITranscribe> {
    const transcribeDetails = await Transcribe.findById(id);

    return transcribeDetails;
  }

  export async function updateMeetingVideoCount(
    id: string,
    value: number
  ): Promise<any> {
    const updatedMeeting = await Meeting.findOneAndUpdate(
      { meetingId: id },
      { noOfVideose: value },
      { new: true }
    );
    return updatedMeeting;
  }
  export async function updateMeetingVideoCountWithVideoUrls(
    id: string,
    value: number,
    filePathArray: any
  ): Promise<any> {
    const updatedMeeting = await Meeting.findOneAndUpdate(
      { meetingId: id },
      { noOfVideose: value, videoUrls: filePathArray },
      { new: true }
    );
    return updatedMeeting;
  }

  export async function updateMeetingVideoUrlsArray(
    id: string,
    value: string[]
  ): Promise<any> {
    const updatedMeeting = await Meeting.findOneAndUpdate(
      { meetingId: id },
      { videoUrls: value },
      { new: true }
    );
    return updatedMeeting;
  }
  export async function updateMeetingAudioArray(
    id: any,
    value: any[]
  ): Promise<any> {
    const updatedMeeting = await Meeting.findByIdAndUpdate(
      id,
      {
        transcribingInProcess: false,
        transcribeCreated: true,
        audioFiles: value,
      },
      { new: true }
    );
    return updatedMeeting;
  }
  export async function updateBothTranscribeStatus(id: any): Promise<any> {
    const updatedMeeting = await Meeting.findByIdAndUpdate(
      id,
      {
        transcribingInProcess: false,
        transcribeCreated: true,
      },
      { new: true }
    );
    return updatedMeeting;
  }
  export async function updateBothTranscribeStatusManually(
    id: any,
    transcribingInProcessValue: boolean,
    transcribeCreatedValue: boolean
  ): Promise<any> {
    const updatedMeeting = await Meeting.findByIdAndUpdate(
      id,
      {
        transcribingInProcess: transcribingInProcessValue,
        transcribeCreated: transcribeCreatedValue,
      },
      { new: true }
    );
    return updatedMeeting;
  }

  export async function updateMeetingTranscribingStatus(
    id: any,
    value: boolean
  ): Promise<any> {
    const updatedMeeting = await Meeting.findOneAndUpdate(
      { meetingId: id },
      { transcribingInProcess: value, transcribeCreated: true },
      { new: true }
    );
    return updatedMeeting;
  }

  export async function updateMeetingTranscribingInProcessStatus(
    id: any,
    value: boolean
  ): Promise<any> {
    const updatedMeeting = await Meeting.findOneAndUpdate(
      { meetingId: id },
      { transcribingInProcess: value },
      { new: true }
    );
    return updatedMeeting;
  }

  export async function updateMeetingTime(
    id: any,
    value: number
  ): Promise<any> {
    const updatedMeeting = await Meeting.findOneAndUpdate(
      { meetingId: id },
      { spentDuration: value, transcribeCreated: true },
      { new: true }
    );
    return updatedMeeting;
  }

  export async function createTranscribe(
    transcribe: DTranscribe
  ): Promise<ITranscribe> {
    const ITranscribe = new Transcribe(transcribe);
    let res = await ITranscribe.save();
    return res;
  }

  export async function createDiagnosisNote(
    note: DDiagnosisNote
  ): Promise<IDiagnosisNote> {
    const INote = new DiagnosisNote(note);
    let res = await INote.save();
    return res;
  }
  
  export async function updateDiagnosisNote(
    noteId: any,
    data: DDiagnosisNote
  ): Promise<IDiagnosisNote> {
    let res = await DiagnosisNote.findByIdAndUpdate(
      noteId,
      { $set: data },
      { new: true }
    );
    return res;
  }

  export async function findTranscribeByVideoUrl(url: string): Promise<any> {
    const transcribe = await Transcribe.findOne(
      { videoUrl: url },
      { new: true }
    );
    return transcribe;
  }

  export async function updateTranscribeStatus(
    id: any,
    textArray: any,
    videoUrlString: string,
    value: boolean
  ): Promise<any> {
    const updatedTranscribe = await Transcribe.findByIdAndUpdate(
      id,
      {
        transCribeInProcess: value,
        transcriptText: textArray,
        videoUrl: videoUrlString,
      },
      { new: true }
    );
    return updatedTranscribe;
  }

  export async function deleteTranscribe(id: any): Promise<any> {
    const updatedTranscribe = await Transcribe.findByIdAndDelete(id);

    return updatedTranscribe;
  }

  export async function getTranscribeForSpecificClientByTherapist(
    clientId: any,
    therapistId: any
  ): Promise<any> {
    const transcribes = await Transcribe.aggregate([
      {
        $match: {
          $or: [
            {
              therapistId: therapistId,
              clientId: clientId,
              transCribeInProcess: false,
              transcriptText: { $ne: [] },
            },
            {
              therapistId: therapistId,
              clientId: clientId,
              transCribeInProcess: true,
            },
          ],
        },
      },
      {
        $project: {
          videoUrl: 1,
          clientId: 1,
          therapistId: 1,
          transCribeInProcess: 1,
        },
      },
    ]);

    return transcribes;
  }

  export async function getAllMeetingsForSpecificTherapistForCurrentMonthDao(
    therapistId: any
  ): Promise<any> {
    const date = new Date();
    const startOfMonth = new Date(
      date.getFullYear(),
      date.getMonth(),
      1,
      0,
      0,
      0
    );
    const endOfMonth = new Date(
      date.getFullYear(),
      date.getMonth() + 1,
      0,
      23,
      59,
      59
    );
    const meetings = await Meeting.aggregate([
      {
        $match: {
          therapistId: therapistId,
          accepted: true,
          createdAt: {
            $gte: startOfMonth,
            $lte: endOfMonth,
          },
        },
      },
    ]);

    return meetings;
  }

  export async function getAllMeetingsForSpecificTherapistForCurrentWeekDao(
    clientId: any
  ): Promise<any> {
    const meetings = await Meeting.aggregate([
      {
        $match: {
          clientId: clientId,
          callingStatus: CallingStatus.COMPLETED,
        },
      },
    ]);

    return meetings.length;
  }

  export async function getAllMeetingsForSpecificTherapistDao(
    therapistId: any
  ): Promise<any> {
    const meetings = await Meeting.aggregate([
      {
        $match: {
          therapistId: therapistId,
          accepted: true,
        },
      },
    ]);

    return meetings;
  }

  export async function getAllMeetingsForSpecificTherapistForTranscribeDao(
    therapistId: any,
    limit: number,
    offset: number
  ): Promise<any> {
    const meetings = await Meeting.find({
      therapistId: therapistId,
      accepted: true,
      $or: [
        { callingStatus: { $exists: false } },
        { callingStatus: CallingStatus.COMPLETED },
      ],
    })
      .populate([
        {
          path: "clientId",
          select: {
            username: 1,
            _id: 0,
          },
        },
        {
          path: "audioFiles",
        },
      ])
      .sort({ createdAt: -1 }).skip(limit * (offset - 1))
      .limit(limit);

    return meetings;
  }


  export async function getAllMeetingsForSpecificTherapist(date: number
  ): Promise<any> {
    const meetings = await Meeting.find({
      accepted: true,
      therapistId: { $nin: ["6311d089411df48985d51923", "62cca8a423949c3c55888cd7", "62ed5a2da406a17f80591278", "62f17eb1b81f31a68505d9ad"] },
      $or: [
        { callingStatus: { $exists: false } },
        { callingStatus: CallingStatus.COMPLETED },
      ],
    })
      .populate([
        {
          path: "therapistId",
          select: {
            username: 1,
            firstname: 1,
            lastname: 1,
            email: 1,
            _id: 1,
          },
        },
        {
          path: "clientId",
          select: {
            username: 1,
            firstname: 1,
            lastname: 1,
            _id: 1,
          },
        },
      ])
      .sort({ createdAt: -1 })

    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - date);
    const filteredMeetings = meetings.filter((meeting: any) => {
      const meetingDate = new Date(meeting.createdAt);
      return meetingDate >= daysAgo;
    });
    return filteredMeetings;
  }

  export async function getAllMeetingsForThrepistAndClientCurrentMonthDao(
    therapistId: any,
    clientId: any
  ): Promise<any> {
    const meetings = await Meeting.find({
      therapistId: therapistId,
      clientId: clientId,
      accepted: true,
    });
    return meetings;
  }

  export async function deleteMeetingsByClientId(
    clientId: StringOrObjectId
  ): Promise<number> {
    const response = await Meeting.deleteMany({ clientId: clientId });
    return response.ok;
  }

  export async function deleteMeetingsByTherapistId(
    therapistId: StringOrObjectId
  ): Promise<number> {
    const response = await Meeting.deleteMany({ therapistId: therapistId });
    return response.ok;
  }

  export async function updateMeetingSpeakerHistory(
    id: string,
    array: any
  ): Promise<IMeeting> {
    const updatedMeeting = await Meeting.findOneAndUpdate(
      { meetingId: id },
      { speakerHistory: array },
      { new: true }
    );
    return updatedMeeting;
  }

  export async function deleteTreatmentHistory(
    noteId: StringOrObjectId,
    data: Partial<DTreatmentHistory>
  ): Promise<ITreatmentHistory> {
    const updatedDiagnosis = await TreatmentHistory.findByIdAndUpdate(
      noteId,
      { $set: data },
      { new: true }
    );
    return updatedDiagnosis;
  }

  export async function getDiagnosisNoteByIdFunction(
    noteId: StringOrObjectId
  ): Promise<IDiagnosisNote> {
    const noteDetails = await DiagnosisNote.findById(noteId).populate([
      {
        path: "clientId",
        select: {
          firstname: 1,
          lastname: 1,
          email: 1,
          primaryPhone: 1,
          username: 1,
          city: 1,
          dateOfBirth: 1,
          gender: 1,
          state: 1,
          streetAddress: 1,
          insuranceId: 1,
          stripeCustomerId: 1,
          copaymentAmount: 1,
          secondaryInsuranceId: 1,
          claimEligibilityDetails: 1,
          claimEligibilityMdDetails: 1,
          _id: 1,
        },
        populate: [
          {
            path: "insuranceId",
            select: {
              dependent: 1,
              insuranceCardId: 1,
              insuranceCompanyId: 1,
              subscriber: 1,
              _id: 1,
            },
            populate: [
              {
                path: "insuranceCompanyId",
                select: {
                  insuranceCompany: 1
                },
              },
              { path: "insuranceCardId" },
            ],
          },
        ],

      },
      {
        path: "therapistId",
        select: {
          city: 1,
          firstname: 1,
          lastname: 1,
          username: 1,
          license: 1,
          nPI1: 1,
          nPI2: 1,
          state: 1,
          streetAddress: 1,
          taxonomyCode: 1,
          _id: 1,
          claimOpen: 1,
        },
      },
      {
        path: "meetingId",
        select: { meetingDuration: 1, spentDuration: 1, copayment: 1, createdAt: 1, regularMeetingDate: 1, _id: 1 },
      },

      { path: "historyOfPresentIllnessAttachments" },
      { path: "mentalBehavioralStatusAttachments" },
      { path: "assessmentAttachments" },
      { path: "carePlanAttachments" },

    ]);
    return noteDetails;
  }
  export async function getDiagnosisNoteByIdWithOutPopulateFunction(
    noteId: StringOrObjectId
  ): Promise<IDiagnosisNote> {
    const noteDetails = await DiagnosisNote.findById(noteId);
    return noteDetails;
  }

  export async function getAnyPreviousNote(userId: any): Promise<any> {
    const noteDetails = await DiagnosisNote.find({
      clientId: userId,
      updated: true,
    })
      .sort({ _id: -1 })
      .limit(1);
    return noteDetails;
  }

  export async function getAnyPreviousNoteWithSameTherapist(
    clientId: any,
    therapistId: any
  ): Promise<any> {
    const noteDetails = await DiagnosisNote.find({
      clientId: clientId,
      therapistId: therapistId,
      updated: true,
      updatedByTherapist: true,
    })
      .sort({ _id: -1 })
      .limit(1);
    return noteDetails;
  }

  export async function deleteAllDiagnosisNotesByClientId(
    clientId: StringOrObjectId
  ): Promise<number> {
    const response = await DiagnosisNote.deleteMany({
      clientId: clientId,
    });
    return response.ok;
  }

  export async function deleteAllDiagnosisNotesByTherapistId(
    therapistId: StringOrObjectId
  ): Promise<number> {
    const response = await DiagnosisNote.deleteMany({
      therapistId: therapistId,
    });
    return response.ok;
  }

  export async function deleteAllTranscribeNotesByTherapistId(
    therapistId: StringOrObjectId
  ): Promise<number> {
    const response = await Transcribe.deleteMany({
      therapistId: therapistId,
    });
    return response.ok;
  }

  export async function deleteAllTranscribeNotesByClientId(
    clientId: StringOrObjectId
  ): Promise<number> {
    const response = await Transcribe.deleteMany({
      clientId: clientId,
    });
    return response.ok;
  }

  // new calling
  export async function getAvatarDetailsForStartCallWithMeetingDetals(
    recieverId: any,
    role: any,
    currentUserId: any,
    callDuration: number
  ): Promise<any> {
    const response = await User.findById(recieverId);

    if (!response) {
      return null;
    }

    let testSubscriptionStatus;
    let clientSubscriptionId;
    let clientSubStatus;
    let clientPremiumStatus;
    let isMeetingTimeRemained;
    let totalMeetingTime;
    const date = new Date();
    const startOfMonth = new Date(
      date.getFullYear(),
      date.getMonth(),
      1,
      0,
      0,
      0
    );
    const endOfMonth = new Date(
      date.getFullYear(),
      date.getMonth() + 1,
      0,
      23,
      59,
      59
    );

    if (role == UserRole.CLIENT) {
      const remainTimeOne = await Meeting.aggregate([
        {
          $match: {
            therapistId: response._id,
            clientId: currentUserId,
            accepted: true,
            createdAt: {
              $gte: startOfMonth,
              $lte: endOfMonth,
            },
          },
        },
        {
          $group: {
            _id: null,
            total: {
              $sum: {
                $cond: {
                  if: { $gt: ["$spentDuration", null] },
                  then: "$spentDuration",
                  else: "$meetingDuration",
                },
              },
            },
          },
        },
      ]);

      totalMeetingTime =
        remainTimeOne.length !== 0 && remainTimeOne[0] && remainTimeOne[0].total
          ? remainTimeOne[0].total
          : 0;
    } else {
      const remainTimeTwo = await Meeting.aggregate([
        {
          $match: {
            therapistId: currentUserId,
            clientId: response._id,
            accepted: true,
            createdAt: {
              $gte: startOfMonth,
              $lte: endOfMonth,
            },
          },
        },
        {
          $group: {
            _id: null,
            total: {
              $sum: {
                $cond: {
                  if: { $gt: ["$spentDuration", null] },
                  then: "$spentDuration",
                  else: "$meetingDuration",
                },
              },
            },
          },
        },
      ]);

      totalMeetingTime =
        remainTimeTwo.length !== 0 && remainTimeTwo[0] && remainTimeTwo[0].total
          ? remainTimeTwo[0].total
          : 0;
    }

    if (
      (totalMeetingTime && totalMeetingTime >= 300) ||
      (totalMeetingTime && 300 - totalMeetingTime < callDuration)
    ) {
      isMeetingTimeRemained = false;
    } else {
      isMeetingTimeRemained = true;
    }

    if (response.role == UserRole.CLIENT) {
      let responseClient: IClient = await Client.findById(response._id);

      if (responseClient != null) {
        testSubscriptionStatus = responseClient.testSubscriptionStatus
          ? responseClient.testSubscriptionStatus
          : "";
        clientSubscriptionId = responseClient.subscriptionId;
        clientSubStatus = responseClient.subscriptionStatus;
        clientPremiumStatus = responseClient.premiumStatus;
      } else {
        testSubscriptionStatus = "";
        clientSubStatus = "";
        clientSubscriptionId = "";
        clientPremiumStatus = "";
      }
    } else {
      let responseClient: IClient = await Client.findById(currentUserId);

      if (responseClient != null) {
        testSubscriptionStatus = responseClient.testSubscriptionStatus
          ? responseClient.testSubscriptionStatus
          : "";
        clientSubscriptionId = responseClient.subscriptionId;
        clientSubStatus = responseClient.subscriptionStatus;
        clientPremiumStatus = responseClient.premiumStatus;
      } else {
        testSubscriptionStatus = "";
        clientSubStatus = "";
        clientSubscriptionId = "";
        clientPremiumStatus = "";
      }
    }

    let finalData;

    if (response.useDefaultAvatar !== undefined) {
      if (response.useDefaultAvatar) {
        finalData = {
          useDefaultAvatar: true,
          avatarId: response.defaultAvatarId,
          avatarBackgroundId: response.avatarBackgroundId,
          incognito:
            response.incognito != null &&
              response.incognito &&
              response.role == UserRole.CLIENT
              ? true
              : false,
          socketId: response.socketId,
          callerName: response.firstname,
          clientSubscriptionId: clientSubscriptionId,
          clientSubStatus: clientSubStatus,
          clientPremiumStatus: clientPremiumStatus,
          testSubscriptionStatus: testSubscriptionStatus,
          isMeetingTimeRemained: isMeetingTimeRemained,
          remainingMeetingTime: 300 - totalMeetingTime,
          callRecordingAllowed:
            response.callRecordingAllowed != null &&
              response.callRecordingAllowed
              ? true
              : false,
        };
      } else {
        const uploadData = await Upload.findById(response.avatarId);

        finalData = {
          useDefaultAvatar: false,
          avatarId: uploadData?.url,
          avatarBackgroundId: response.avatarBackgroundId,
          incognito:
            response.incognito != null &&
              response.incognito &&
              response.role == UserRole.CLIENT
              ? true
              : false,
          socketId: response.socketId,
          callerName: response.firstname,
          clientSubscriptionId: clientSubscriptionId,
          clientSubStatus: clientSubStatus,
          clientPremiumStatus: clientPremiumStatus,
          testSubscriptionStatus: testSubscriptionStatus,
          isMeetingTimeRemained: isMeetingTimeRemained,
          remainingMeetingTime: 300 - totalMeetingTime,
          callRecordingAllowed:
            response.callRecordingAllowed != null &&
              response.callRecordingAllowed
              ? true
              : false,
        };
      }
    } else {
      finalData = {
        useDefaultAvatar: true,
        avatarId: response.gender === "Female" ? "avatarTwo" : "avatarOne",
        avatarBackgroundId:
          response.avatarBackgroundId !== undefined
            ? response.avatarBackgroundId
            : "backgroundOne",
        incognito:
          response.incognito != null &&
            response.incognito &&
            response.role == UserRole.CLIENT
            ? true
            : false,
        socketId: response.socketId,
        callerName: response.firstname,
        clientSubscriptionId: clientSubscriptionId,
        clientSubStatus: clientSubStatus,
        clientPremiumStatus: clientPremiumStatus,
        testSubscriptionStatus: testSubscriptionStatus,
        isMeetingTimeRemained: isMeetingTimeRemained,
        remainingMeetingTime: 300 - totalMeetingTime,
        callRecordingAllowed:
          response.callRecordingAllowed != null && response.callRecordingAllowed
            ? true
            : false,
      };
    }

    return finalData;
  }

  export async function getAvatarDetailsForStartCall(id: any): Promise<any> {
    const response = await User.findById(id);
    if (!response) {
      return null;
    }
    let finalData;
    if (response.useDefaultAvatar !== undefined) {
      if (response.useDefaultAvatar) {
        finalData = {
          useDefaultAvatar: true,
          avatarId: response.defaultAvatarId,
          avatarBackgroundId: response.avatarBackgroundId,
          incognito:
            response.incognito != null &&
              response.incognito &&
              response.role == UserRole.CLIENT
              ? true
              : false,
          socketId: response.socketId,
          callerName: response.firstname,
          ownRole: response.role,
          callRecordingAllowed:
            response.callRecordingAllowed != null &&
              response.callRecordingAllowed
              ? true
              : false,
        };
      } else {
        const uploadData = await Upload.findById(response.avatarId);

        finalData = {
          useDefaultAvatar: false,
          avatarId: uploadData?.url,
          avatarBackgroundId: response.avatarBackgroundId,
          incognito:
            response.incognito != null &&
              response.incognito &&
              response.role == UserRole.CLIENT
              ? true
              : false,
          socketId: response.socketId,
          callerName: response.firstname,
          // subscriptionStatus: subStatus,
          ownRole: response.role,
          callRecordingAllowed:
            response.callRecordingAllowed != null &&
              response.callRecordingAllowed
              ? true
              : false,
        };
      }
    } else {
      finalData = {
        useDefaultAvatar: true,
        avatarId: response.gender === "Female" ? "avatarTwo" : "avatarOne",
        avatarBackgroundId:
          response.avatarBackgroundId !== undefined
            ? response.avatarBackgroundId
            : "backgroundOne",
        incognito:
          response.incognito != null &&
            response.incognito &&
            response.role == UserRole.CLIENT
            ? true
            : false,
        socketId: response.socketId,
        callerName: response.firstname,
        ownRole: response.role,
        callRecordingAllowed:
          response.callRecordingAllowed != null && response.callRecordingAllowed
            ? true
            : false,
      };
    }

    return finalData;
  }
  export async function checkRemainingMeetingTime(
    recieverId: any,
    role: any,
    currentUserId: any,
    extendingTime: number
  ): Promise<any> {
    const response = await User.findById(recieverId);
    if (!response) {
      return null;
    }
    let testSubscriptionStatus;
    let clientPremiumStatus;
    let isMeetingTimeRemained;
    let totalMeetingTime;
    const date = new Date();
    const startOfMonth = new Date(
      date.getFullYear(),
      date.getMonth(),
      1,
      0,
      0,
      0
    );
    const endOfMonth = new Date(
      date.getFullYear(),
      date.getMonth() + 1,
      0,
      23,
      59,
      59
    );

    if (role == UserRole.CLIENT) {
      const remainTimeOne = await Meeting.aggregate([
        {
          $match: {
            therapistId: response._id,
            clientId: currentUserId,
            accepted: true,
            createdAt: {
              $gte: startOfMonth,
              $lte: endOfMonth,
            },
          },
        },
        {
          $group: {
            _id: null,
            total: {
              $sum: {
                $cond: {
                  if: { $gt: ["$spentDuration", null] },
                  then: "$spentDuration",
                  else: "$meetingDuration",
                },
              },
            },
          },
        },
      ]);
      totalMeetingTime =
        remainTimeOne.length !== 0 && remainTimeOne[0] && remainTimeOne[0].total
          ? remainTimeOne[0].total
          : 0;
    } else {
      const remainTimeTwo = await Meeting.aggregate([
        {
          $match: {
            therapistId: currentUserId,
            clientId: response._id,
            accepted: true,
            createdAt: {
              $gte: startOfMonth,
              $lte: endOfMonth,
            },
          },
        },
        {
          $group: {
            _id: null,
            total: {
              $sum: {
                $cond: {
                  if: { $gt: ["$spentDuration", null] },
                  then: "$spentDuration",
                  else: "$meetingDuration",
                },
              },
            },
          },
        },
      ]);

      totalMeetingTime =
        remainTimeTwo.length !== 0 && remainTimeTwo[0] && remainTimeTwo[0].total
          ? remainTimeTwo[0].total
          : 0;
    }

    if (
      (totalMeetingTime && totalMeetingTime >= 300) ||
      (totalMeetingTime && 300 - totalMeetingTime < extendingTime)
    ) {
      isMeetingTimeRemained = false;
    } else {
      isMeetingTimeRemained = true;
    }

    if (response.role == UserRole.CLIENT) {
      let responseClient: IClient = await Client.findById(response._id);

      if (responseClient != null) {
        clientPremiumStatus = responseClient.premiumStatus;
        testSubscriptionStatus = responseClient.testSubscriptionStatus
          ? responseClient.testSubscriptionStatus
          : "";
      } else {
        clientPremiumStatus = "";
        testSubscriptionStatus = "";
      }
    } else {
      let responseClient: IClient = await Client.findById(currentUserId);

      if (responseClient != null) {
        clientPremiumStatus = responseClient.premiumStatus;
        testSubscriptionStatus = responseClient.testSubscriptionStatus
          ? responseClient.testSubscriptionStatus
          : "";
      } else {
        clientPremiumStatus = "";
        testSubscriptionStatus = "";
      }
    }

    let finalData;

    finalData = {
      clientPremiumStatus: clientPremiumStatus,
      testSubscriptionStatus: testSubscriptionStatus,
      isMeetingTimeRemained: isMeetingTimeRemained,
      remainingMeetingTime: 300 - totalMeetingTime,
    };

    return finalData;
  }
  export async function getAvatarDetailsOfOwnUser(id: any): Promise<any> {
    const response = await User.findById(id);
    if (!response) {
      return null;
    }

    let finalData;
    if (response.useDefaultAvatar !== undefined) {
      if (response.useDefaultAvatar) {
        finalData = {
          useDefaultAvatar: true,
          avatarId: response.defaultAvatarId,
          avatarBackgroundId: response.avatarBackgroundId,
          incognito:
            response.incognito != null &&
              response.incognito &&
              response.role == UserRole.CLIENT
              ? true
              : false,
        };
      } else {
        const uploadData = await Upload.findById(response.avatarId);

        finalData = {
          useDefaultAvatar: false,
          avatarId: uploadData?.url,
          avatarBackgroundId: response.avatarBackgroundId,
          incognito:
            response.incognito != null &&
              response.incognito &&
              response.role == UserRole.CLIENT
              ? true
              : false,
        };
      }
    } else {
      finalData = {
        useDefaultAvatar: true,
        avatarId: response.gender === "Female" ? "avatarTwo" : "avatarOne",
        avatarBackgroundId:
          response.avatarBackgroundId !== undefined
            ? response.avatarBackgroundId
            : "backgroundOne",
        incognito:
          response.incognito != null &&
            response.incognito &&
            response.role == UserRole.CLIENT
            ? true
            : false,
      };
    }

    return finalData;
  }

  export async function updateMeetingIfNoRecordingFoundInSchedular(
    id: any
  ): Promise<any> {
    const updatedMeeting = await Meeting.findByIdAndUpdate(
      id,
      {
        transcribingInProcess: false,
        transcribeCreated: true,
        audioFiles: [],
        videoUrls: [],
      },
      { new: true }
    );
    return updatedMeeting;
  }

  export async function getEndMeetingByMeetingId(
    meetingid: string
  ): Promise<IMeeting> {
    const meetingDetails = await Meeting.findOne({
      _id: meetingid,
    });
    return meetingDetails;
  }

  export async function getTranscribeByMeetingIdAndUpdateAIResponse(
    meetingId: string,
    aiResponse: string
  ): Promise<ITranscribe> {
    const transcribeDetails = await Transcribe.findOneAndUpdate({ meetingId: meetingId }, { aiResponse: aiResponse });
    return transcribeDetails;
  }

  export async function updateParticipantCountInAllMeetings(): Promise<any> {
    const twoDaysAgo = new Date();
    twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
    console.log("#######################################");
    
    const meetings = await Meeting.bulkWrite([
      {
        updateMany: {
          filter: { spentDuration: { $exists: true } }, // Condition to check if spentDuration field exists
          update: { $set: { participantCount: 2 } } // Set participantCount to 2
        }
      },
      {
        updateMany: {
          filter: { spentDuration: { $exists: false } }, // Condition to check if spentDuration field does not exist
          update: { $set: { participantCount: 1 } } // Set participantCount to 1
        }
      }
    ])
    return meetings;
  }

  export async function updatecallingStatusInAllMeetings(): Promise<any> {
    console.log("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
    
    const meetings = await Meeting.updateMany(
       {
        $and: [
          { callingStatus: CallingStatus.COMPLETED }, 
          { bothJoinedAt: { $exists: true } }, 
          { spentDuration: { $exists: false } }
        ]
      },
      { $set: { callingStatus: CallingStatus.CANCELLED } },
    )
    return meetings;
  }

  
  export async function getMeetingByAppoinmentId(
    clientIdFrom: any,
    therapistIdFrom: any,
  ): Promise<any> {
    const meetings = await Meeting.aggregate([
      {
        $match: {
          clientId: clientIdFrom,
          therapistId : therapistIdFrom,
          $or: [
            { callingStatus: CallingStatus.ONGOING },
            { callingStatus: CallingStatus.STARTED },
            { callingStatus: CallingStatus.COMPLETED },
            { createdBy: clientIdFrom }
          ]
        },
      },
    ]);

    return meetings.length;
  }

   export async function checkClientHasCompletedSessionsOnThisWeek(
    _clientId: Types.ObjectId,
    _therapistId: Types.ObjectId,
    _startDateOfWeek: Date,
    _endDateOfWeek: Date
  ): Promise<any> {
    try {
      const meetings = await Meeting.aggregate([
        {
          $match: {
            $and : [
              { clientId: _clientId },
              { therapistId: _therapistId },
              { callingStatus: CallingStatus.COMPLETED },
              { spentDuration :{ $gte : 30 } },
              { createdAt: { $gte: _startDateOfWeek, $lt: _endDateOfWeek } }
            ]
          }
        }
      ])
      
      return meetings.length;
    } catch (error) {
      console.log(error);
    }
  }

}
