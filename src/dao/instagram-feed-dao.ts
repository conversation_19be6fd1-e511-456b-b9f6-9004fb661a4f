import { DInsragramFeed, IInstagramFeed } from "../models/instagram-feed-model";
import InstagramFeed from "../schemas/instagram-feed-schema";

export namespace InstagramFeedDao {
  export async function createInstagramFeed(data: DInsragramFeed): Promise<IInstagramFeed> {
    const iInstagramFeed: IInstagramFeed = new InstagramFeed(data);
    return await iInstagramFeed.save();
  }

  export async function deleteAllInstagramFeed(): Promise<number> {
    const response = await InstagramFeed.deleteMany({});
    return response.ok;
  }

  export async function getAllInstagramFeed(): Promise<IInstagramFeed[]> {
    const response = await InstagramFeed.find({});
    return response;
  }

}
