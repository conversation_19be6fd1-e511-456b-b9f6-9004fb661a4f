import {
  DChatGroup<PERSON>all,
  IChatGroupCall,
} from "../models/chat-group-call-model";
import ChatGroupCall from "../schemas/chat-group-call-schema";

export namespace ChatGroupCallDao {
  export async function createChatGroupCall(
    chatGroupCall: DChatGroupCall
  ): Promise<IChatGroupCall> {
    const iChatGroupCall = new ChatGroupCall(chatGroupCall);
    let response = await iChatGroupCall.save();
    return response;
  }
}
