import {
  DChatGroup<PERSON>all,
  IChatGroupCall,
} from "../models/chat-group-call-model";
import {
  DChatGroupMember,
  IChatGroupMember,
} from "../models/chat-group-member-model";
import {
  DChatGroupMessage,
  IChatGroupMessage,
} from "../models/chat-group-message-model";
import { DChatGroup, IChatGroup } from "../models/chat-group-model";
import {
  DPublicChatGroupRequest,
  IPublicChatGroupRequest,
} from "../models/public-chat-group-request-model";
import ChatGroupCall from "../schemas/chat-group-call-schema";
import ChatGroupMember from "../schemas/chat-group-member-schema";
import ChatGroupMessage from "../schemas/chat-group-message-schema";
import ChatGroup from "../schemas/chat-group-schema";
import PublicChatGroupRequest from "../schemas/public-chat-group-request-schema";
export namespace ChatGroupDao {
  export async function createChatGroup(
    chatGroup: DChatGroup
  ): Promise<IChatGroup> {
    const iChatGroup = new ChatGroup(chatGroup);
    let response = await iChatGroup.save();
    return response;
  }
  export async function createChatGroupMember(
    chatGroupMember: DChatGroupMember
  ): Promise<IChatGroupMember> {
    const iChatGroupMember = new ChatGroupMember(chatGroupMember);
    let response = await iChatGroupMember.save();
    return response;
  }

  export async function createPublicChatGroupRequest(
    pChatGroupRequest: DPublicChatGroupRequest
  ): Promise<IPublicChatGroupRequest> {
    const iPChatGroupRequest = new PublicChatGroupRequest(pChatGroupRequest);
    let response = await iPChatGroupRequest.save();
    return response;
  }

  export async function createChatGroupMessage(
    chatGroupMessage: DChatGroupMessage
  ): Promise<IChatGroupMessage> {
    const iChatGroupMessage = new ChatGroupMessage(chatGroupMessage);
    let response = await iChatGroupMessage.save();
    return response;
  }
}
