import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";
import {
  DTransaction,
  ITransaction,
  TransactionType,
} from "../models/transaction-model";
import Transaction from "../schemas/transaction-schema";
import {
  DReferralEarning,
  IReferralEarning,
} from "../models/referral-earning-model";
import ReferralEarning from "../schemas/referral-earning-schema";
import { VideoCallDao } from "./videocall-dao";
const moment = require("moment");

export namespace TransactionsDao {
  export async function getRecentBalance(
    therapistId: any
  ): Promise<ITransaction> {
    const response = (
      await Transaction.find({ therapistId: therapistId })
        .sort({ _id: -1 })
        .limit(1)
    )[0];

    return response;
  }

  export async function getAllTransactionsTherapist(
    therapistId: any,
    limit?: number,
    offset?: number
  ): Promise<ITransaction[]> {
    const query = Transaction.find({ therapistId: therapistId }).populate([
        {
          path: "meetingId",
          select: {
            meetingDuration: 1,
            spentDuration: 1,
            noOfVideose: 1,
            videoUrls: 1,
            transcribeAllowed: 1,
            transcribingInProcess: 1,
            accepted: 1,
            clientId: 1,
            meetingId: 1,
            meetingType: 1,
          },
          populate: [
            {
              path: "clientId",
              select: {
                firstname: 1,
                lastname: 1,
                email: 1,
                insuranceId: 1,
              },
              populate: {
                path: "insuranceId",
                select: { insuranceCompanyId: 1 },
                populate: {
                  path: "insuranceCompanyId",
                  select: { insuranceCompany: 1, contractPrice: 1 },
                },
              },
            },
          ],
        },
      ]).sort({ createdAt: -1 });

    if (limit && offset) {
      query.skip(limit * (offset - 1)).limit(limit);
    }
    const response = await query.exec();

    return response;
  }

  export async function getAllTransactionsForTest(): Promise<ITransaction[]> {
    const startDate = moment().startOf("day").month(2).date(1); // March 1st
    const endDate = moment().endOf("day"); // Today
    const response = await Transaction.find({
      createdAt: {
        $gte: startDate.toDate(),
        $lte: endDate.toDate(),
      },
      type: TransactionType.EARNING,
    })
      .populate([
        {
          path: "meetingId",
          select: {
            meetingDuration: 1,
            spentDuration: 1,
            noOfVideose: 1,
            videoUrls: 1,
            transcribeAllowed: 1,
            transcribingInProcess: 1,
            accepted: 1,
            clientId: 1,
            meetingId: 1,
            meetingType: 1,
          },
          populate: [
            {
              path: "clientId",
              select: { firstname: 1, lastname: 1, email: 1, insuranceId: 1 },
              populate: {
                path: "insuranceId",
                select: { insuranceCompanyId: 1 },
                populate: {
                  path: "insuranceCompanyId",
                  select: { insuranceCompany: 1, contractPrice: 1 },
                },
              },
            },
          ],
        },
        {
          path: "therapistId",
        },
      ])
      .sort({ createdAt: -1 });
    return response;
  }

  export async function getAllTransactionsAdmin(
    therapistId: any,
    limit: number,
    offset: number
  ): Promise<ITransaction[]> {
    const response = await Transaction.find({ therapistId: therapistId })
      .populate([
        {
          path: "meetingId",
          select: {
            meetingDuration: 1,
            spentDuration: 1,
            noOfVideose: 1,
            videoUrls: 1,
            transcribeAllowed: 1,
            transcribingInProcess: 1,
            accepted: 1,
            clientId: 1,
            meetingId: 1,
            meetingType: 1,
          },
          populate: [
            {
              path: "clientId",
              select: { firstname: 1, lastname: 1, email: 1 },
            },
          ],
        },
      ])
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return response;
  }

  export async function createTransaction(
    therapistId: any,
    transactionAmount: string,
    type: TransactionType,
    meetingId: string,
    insuranceCompany: string
  ): Promise<ITransaction> {
    const recentBalance = await TransactionsDao.getRecentBalance(therapistId);

    let iTransaction;

    if (recentBalance) {
      let response = await TransactionsDao.getHoldTransactions(
        therapistId,
        meetingId
      );
      let eligibleForPayment = !response;

      const transactionDetails: DTransaction = {
        therapistId: therapistId,
        meetingId: meetingId,
        type: type,
        transactionAmount: parseFloat(transactionAmount),
        accumulatedBalance:
          recentBalance.accumulatedBalance + parseFloat(transactionAmount),
        accumulatedTotalEarnings:
          recentBalance.accumulatedTotalEarnings +
          parseFloat(transactionAmount),
        accumulatedWithdrawals: recentBalance.accumulatedWithdrawals,
        insuranceCompany: insuranceCompany,
        eligibleForPayment: eligibleForPayment,
      };

      iTransaction = new Transaction(transactionDetails);
    } else {
      const transactionDetails: DTransaction = {
        therapistId: therapistId,
        meetingId: meetingId,
        type: type,
        transactionAmount: parseFloat(transactionAmount),
        accumulatedBalance: parseFloat(transactionAmount),
        accumulatedTotalEarnings: parseFloat(transactionAmount),
        accumulatedWithdrawals: 0,
        insuranceCompany: insuranceCompany,
      };

      iTransaction = new Transaction(transactionDetails);
    }

    const response = iTransaction.save();

    return response;
  }

  export async function updateTransactionData(
    _id: any,
    therapistId: any,
    transactionAmount: string,
    type: TransactionType,
    meetingId: string,
    insuranceCompany: string
  ): Promise<DTransaction> {
    const recentBalance = await TransactionsDao.getRecentBalance(therapistId);
    let iTransaction;

    if (recentBalance) {
      const transactionDetails: DTransaction = {
        therapistId: therapistId,
        meetingId: meetingId,
        type: type,
        transactionAmount: parseFloat(transactionAmount),
        accumulatedBalance:
          recentBalance.accumulatedBalance -
          recentBalance.transactionAmount +
          parseFloat(transactionAmount),
        accumulatedTotalEarnings:
          recentBalance.accumulatedTotalEarnings -
          recentBalance.transactionAmount +
          parseFloat(transactionAmount),
        accumulatedWithdrawals: recentBalance.accumulatedWithdrawals,
        insuranceCompany: insuranceCompany,
      };

      iTransaction = transactionDetails;
    } else {
      const transactionDetails: DTransaction = {
        therapistId: therapistId,
        meetingId: meetingId,
        type: type,
        transactionAmount: parseFloat(transactionAmount),
        accumulatedBalance: parseFloat(transactionAmount),
        accumulatedTotalEarnings: parseFloat(transactionAmount),
        accumulatedWithdrawals: 0,
        insuranceCompany: insuranceCompany,
      };

      iTransaction = transactionDetails;
    }
    const response = await Transaction.findByIdAndUpdate(
      { _id: _id },
      { $set: iTransaction },
      { new: true }
    );

    return response;
  }

  export async function updateTransaction(
    id: StringOrObjectId,
    data: Partial<DTransaction>
  ): Promise<DTransaction> {
    const insurancePlan = await Transaction.findByIdAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    );

    return insurancePlan;
  }

  export async function updateTransactionList(
    idList: Array<string | ObjectId>
  ): Promise<DTransaction[]> {
    await Transaction.updateMany(
      {
        _id: { $in: idList },
      },
      {
        $set: { paidStatus: "PAID" },
      }
    );
    const updatedTransactions = await Transaction.find({
      _id: { $in: idList },
    });

    return updatedTransactions;
  }
  export async function withdrawTransaction(
    therapistId: any,
    transactionAmount: string,
    type: TransactionType
  ): Promise<ITransaction> {
    const recentBalance = await TransactionsDao.getRecentBalance(therapistId);

    let iTransaction;

    if (recentBalance) {
      const withdrawalTransactionDetails: DTransaction = {
        therapistId: therapistId,
        type: type,
        transactionAmount: parseFloat(transactionAmount),
        accumulatedBalance:
          recentBalance.accumulatedBalance - parseFloat(transactionAmount),
        accumulatedTotalEarnings: recentBalance.accumulatedTotalEarnings,
        accumulatedWithdrawals:
          recentBalance.accumulatedWithdrawals + parseFloat(transactionAmount),
      };

      iTransaction = new Transaction(withdrawalTransactionDetails);
    } else {
      return null;
    }

    const response = iTransaction.save();

    return response;
  }

  export async function deleteAllTransactionsByTherapistId(
    therapistId: StringOrObjectId
  ): Promise<number> {
    const response = await Transaction.deleteMany({
      therapistId: therapistId.toString(),
    });
    return response.ok;
  }

  export async function getHoldTransactions(
    therapistId: any,
    meetingId: any
  ): Promise<any> {
    const meetingClient = await VideoCallDao.getEndMeetingByMeetingId(
      meetingId
    );
    if (!meetingClient) {
      return false;
    }

    const relatedClientId = meetingClient.clientId;

    const transactions = await Transaction.find({
      therapistId: therapistId,
      type: TransactionType.EARNING,
      paidStatus: { $in: [null, "PENDING"] },
      eligibleForPayment: false,
    });

    const meetingClientIds = await Promise.all(
      transactions.map(async (transaction) => {
        const meetingInfo = await VideoCallDao.getEndMeetingByMeetingId(
          transaction.meetingId
        );
        return meetingInfo?.clientId;
      })
    );
    return meetingClientIds.some(
      (clientId) => String(clientId) === String(relatedClientId)
    );
  }
}
