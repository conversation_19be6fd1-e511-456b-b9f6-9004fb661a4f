import { StringOrObjectId, Util } from "../common/util";
import { Types } from "mongoose";
import { DPostSession, IPostSession } from "../models/post-session-model";
import PostSession from "../schemas/post-session-schema";

export namespace PostSessionDao {
  // Add new post-session information
  export async function addPostSessionInfo(
    details: DPostSession
  ): Promise<IPostSession> {
    const postSessionDetails = new PostSession(details);
    const response = await postSessionDetails.save();
    return response as IPostSession;
  }

  // Get post-session information by ID
  export async function getPostSessionById(
    id: StringOrObjectId
  ): Promise<IPostSession> {
    const response = await PostSession.findById(id).populate({
      path: "therapistId",
      select: "firstname lastname email",
    });
    return response;
  }

  // Get all post-session information by meetingId
  export async function getPostSessionByMeetingId(
    meetingId: string
  ): Promise<IPostSession> {
    const response = await PostSession.findOne({ meetingId }).populate({
      path: "therapistId",
      select: "firstname lastname email",
    });
    return response;
  }

  /* Get count of therapist post-sessions that need review */
  export async function getTherapistPostSessionsNeedCallCount(): Promise<number> {
    const count = await PostSession.countDocuments({
      $or: [
        { clinicalDirectorReviewRequired: "Yes" },
        { comments: { $ne: "" } }
      ]
    });
    return count;
  }

  /* Update post-session is_read status */
  export async function updateTherapistPostSessionIsRead(
    id: string,
    is_read: boolean
  ): Promise<IPostSession> {
    const response = await PostSession.findByIdAndUpdate(
      id,
      { is_read }
    );
    return response as IPostSession;
  }

  export async function getAllTherapistPostSessionsNeedCall(
    limit: number,
    offset: number,
    searchableString?: string,
    therapistId?: string
  ): Promise<IPostSession[]> {
    const response = await PostSession.aggregate([
      {
        $match: {
          $or: [
            { clinicalDirectorReviewRequired: "Yes" },
            { comments: { $ne: "" } }
          ]
        }
      },
      {
        $lookup: {
          from: 'meetings',
          localField: 'meetingId',
          foreignField: 'meetingId',
          as: 'meetingDetails'
        }
      },
      {
        $unwind: {
          path: '$meetingDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'therapistId',
          foreignField: '_id',
          as: 'therapistDetails'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'clientId',
          foreignField: '_id',
          as: 'clientDetails'
        }
      },
      {
        $lookup: {
          from: 'transcribes',
          localField: 'meetingId',
          foreignField: 'meetingId',
          as: 'transcriptionDetails'
        }
      },
      {
        $addFields: {
          transcriptionText: { $ifNull: [{ $arrayElemAt: ["$transcriptionDetails.transcriptText", 0] }, []] }
        }
      },
      {
        $unwind: {
          path: '$therapistDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $unwind: {
          path: '$clientDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $addFields: {
          clientFullName: {
            $concat: [
              { $ifNull: ['$clientDetails.firstname', ''] },
              ' ',
              { $ifNull: ['$clientDetails.lastname', ''] }
            ]
          }
        }
      },
      ...(therapistId || searchableString ? [{
        $match: {
          $and: [
            ...(therapistId ? [{ 'therapistDetails._id': new Types.ObjectId(therapistId) }] : []),
            ...(searchableString ? [{
              $or: [
                { clientFullName: { $regex: searchableString.trim(), $options: 'i' } },
                { 'clientDetails.email': { $regex: searchableString.trim(), $options: 'i' } },
                { 'clientDetails.primaryPhone': { $regex: searchableString.trim(), $options: 'i' } }
              ]
            }] : [])
          ]
        }
      }] : []),
      {
        $project: {
          _id: 1,
          serviceProvided: 1,
          suicidalIdeation: 1,
          homicidalIdeation: 1,
          perceptualDisturbance: 1,
          nextSessionScheduled: 1,
          clinicalDirectorReviewRequired: 1,
          transcriptionText: 1,
          comments: 1,
          is_read: 1,
          meetingId: 1,
          therapistId: 1,
          clientId: 1,
          createdAt: 1,
          updatedAt: 1,
          'meetingDetails': {
            createdAt: '$meetingDetails.createdAt',
            spentDuration: '$meetingDetails.meetingDuration'
          },
          'therapistDetails': {
            _id: '$therapistDetails._id',
            firstname: '$therapistDetails.firstname',
            lastname: '$therapistDetails.lastname',
            primaryPhone: '$therapistDetails.primaryPhone'
          },
          'clientDetails': {
            _id: '$clientDetails._id',
            firstname: '$clientDetails.firstname',
            lastname: '$clientDetails.lastname',
            email: '$clientDetails.email',
            primaryPhone: '$clientDetails.primaryPhone'
          }
        }
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $skip: limit * (offset - 1)
      },
      {
        $limit: limit
      }
    ]);
    return response;
  }

  // Get all post-session information by therapistId
  export async function getPostSessionsByTherapistId(
    therapistId: StringOrObjectId
  ): Promise<IPostSession[]> {
    const response = await PostSession.find({ therapistId }).populate({
      path: "therapistId",
      select: "firstname lastname email",
    }).sort({ createdAt: -1 });
    return response;
  }

  // Update post-session information
  export async function updatePostSession(
    id: StringOrObjectId,
    data: Partial<DPostSession>
  ): Promise<IPostSession> {
    const response = await PostSession.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true }
    ).populate({
      path: "therapistId",
      select: "firstname lastname email",
    });
    return response;
  }

  // Delete post-session information
  export async function deletePostSession(
    id: StringOrObjectId
  ): Promise<number> {
    const response = await PostSession.deleteOne({ _id: id });
    return response.ok;
  }
}
