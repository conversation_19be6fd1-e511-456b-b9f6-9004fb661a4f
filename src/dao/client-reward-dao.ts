import { StringOrObjectId } from "../common/util";
import { TransactionType } from "../models/transaction-model";
import { DClientReward, IClientReward } from "../models/client-reward-model";
import ClientReward from "../schemas/client-reward-schema";
import Client from "../schemas/client-schema";
import ClientReferral from "../schemas/client-referral-schema";

export namespace ClientRewardDao {

  export async function getRecentClientRewardBalance(clientId: any): Promise<IClientReward> {
    const response = (await ClientReward.find({ clientId: clientId }).sort({ _id: -1 }).limit(1))[0];
    return response;
  }

  export async function getClientRewardsWithoutFilter(limit: number, offset: number): Promise<any[]> {
    let rewardsList = await ClientReward.find().sort({ createdAt: -1 }).skip(limit * (offset - 1))
    .limit(limit)
    .exec();

    await ClientReward.populate(rewardsList, {
      path: 'clientId',
      model: Client,
      select: 'firstname lastname email clientActiveStatus',
    });
    return rewardsList;
  }

  export async function getClientRewardById(
    id: StringOrObjectId
  ): Promise<IClientReward> {
    const response = await ClientReward.findById(id);
    return response;
  }

  export async function updateClientRewardById(
    id: StringOrObjectId,
    data: Partial<DClientReward>
  ): Promise<IClientReward> {
    let response = await ClientReward.findOneAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    );
    return response;
  }

  export async function deleteClientRewardById(
    id: StringOrObjectId
  ): Promise<IClientReward> {
    const response = await ClientReward.findByIdAndDelete(id);
    return response;
  }

  export async function createClientRewards(clientId: any, transactionAmount: string, type: TransactionType, rewardType: string, rewardReferralId: any ): Promise<IClientReward> {
    const recentBalance = await ClientRewardDao.getRecentClientRewardBalance(clientId);

    let iClientReward;

    if (recentBalance) {
        const referralEarningDetails: DClientReward = {
            clientId: clientId,
            type: type,
            transactionAmount: parseFloat(transactionAmount),
            accumulatedBalance: recentBalance.accumulatedBalance + parseFloat(transactionAmount),
            accumulatedTotalEarnings: recentBalance.accumulatedTotalEarnings + parseFloat(transactionAmount),
            accumulatedWithdrawals: recentBalance.accumulatedWithdrawals,
            rewardType: rewardType,
            rewardReferralId: rewardReferralId
        };

        iClientReward = new ClientReward(referralEarningDetails);
    } else {
        const referralEarningDetails: DClientReward = {
            clientId: clientId,
            type: type,
            transactionAmount: parseFloat(transactionAmount),
            accumulatedBalance: parseFloat(transactionAmount),
            accumulatedTotalEarnings: parseFloat(transactionAmount),
            accumulatedWithdrawals: 0,
            rewardType: rewardType,
            rewardReferralId: rewardReferralId
        };

        iClientReward = new ClientReward(referralEarningDetails);
    }

    const response = iClientReward.save();

    return response;
  }

  export async function getClientRewardDetailsByClientId(clientId: any): Promise<IClientReward[]> {
    const rewards = await ClientReward.find({ clientId: clientId }).sort({ createdAt: -1 });

      await ClientReward.populate(rewards, {
        path: 'rewardReferralId',
        model: ClientReferral,
        select: 'referredUserId -_id',
      }),
      await ClientReward.populate(rewards, {
        path: 'rewardReferralId.referredUserId',
        model: Client,
        select: 'firstname lastname _id',
      })

    return rewards;
  }

  export async function getAllUnpaidClientRewardsCount(): Promise<number> {
    const response = await ClientReward.find({ paidStatus: { $exists: false } }).countDocuments();
    return response;
  }

}
