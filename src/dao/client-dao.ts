import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";
import { IClient, DClient } from "../models/client-model";
import Client from "../schemas/client-schema";
import { DClinicalAssesment } from "../models/clinicalAssesment-model";

export namespace ClientDao {
  const populateOptions = [
    {
      path: "photoId",
    },
    {
      path: "coverPhotoId",
    },
    {
      path: "ethnicityId",
    },
    {
      path: "insuranceId",
      populate: [{ path: "insuranceCardId", model: "Upload" }],
    },
    {
      path: "clientId",
    },
  ];
  
  export async function getUserById(id: StringOrObjectId): Promise<IClient> {
    let user: IClient = await Client.findById(id)
      .populate(populateOptions)
      .select({ password: 0 });

    return user;
  }

  export async function updateClient(
    id: StringOrObjectId,
    data: Partial<DClient>
  ): Promise<IClient> {
    let client = await Client.findByIdAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    );

    return client;
  }

  export async function updateClientInvoiceList(
    clientId: Types.ObjectId,
    invoiceId: Types.ObjectId
  ): Promise<boolean> {
    const updatedClient = await Client.findByIdAndUpdate(
      clientId,
      {
        $push: { invoices: invoiceId },
      },
      { new: true }
    );

    if (updatedClient) {
      return true;
    }

    return false;
  }

  export async function updateClientCoPayment(
    clientId: Types.ObjectId,
    coPayment: number
  ): Promise<IClient> {
    let updatedClient = await Client.findByIdAndUpdate(
      clientId,
      {
        copaymentAmount: coPayment
      },
      { new: true }
    );
    return updatedClient;
  }

  export async function updateClientEligibility(
    id: StringOrObjectId,
    eligibilityStatus: {}
  ): Promise<IClient> {
    let updatedClient = await Client.findByIdAndUpdate(
      id,
      {
        $set: {
          claimEligibilityDetails: eligibilityStatus,
        },
        $unset: {
          claimEligibilityMdErrorDetails: 1,
          claimEligibilityMdDetails: 1,
          claimEliMdInactiveDetails: 1
        }
      },
      { new: true }
    );
    return updatedClient;
  }


  export async function updateClientEligibilityMD(
    id: StringOrObjectId,
    eligibilityInfo: {}
  ): Promise<IClient> {
    let updatedClient = await Client.findByIdAndUpdate(
      id,
      {
        $set: {
          claimEligibilityMdDetails: eligibilityInfo,
        },
        $unset: {
          claimEligibilityMdErrorDetails: 1,
          claimEligibilityDetails: 1,
          claimEliMdInactiveDetails: 1
        }
      },
      { new: true }
    );
    return updatedClient;
  }

  export async function updateClientEligibilityMDError(
    id: StringOrObjectId,
    eligibilityError: String,
  ): Promise<IClient> {
    let updatedClient = await Client.findByIdAndUpdate(
      id,
      { 
        $set: {
          claimEligibilityMdErrorDetails: eligibilityError,
        },
        $unset: {
          claimEligibilityMdDetails: 1,
          claimEligibilityDetails: 1,
          claimEliMdInactiveDetails: 1
        }
      },
      { new: true }
    );
    
    return updatedClient;
  }

  export async function updateClientEligibilityMDErrorForInactiveSubscription(
    id: StringOrObjectId,
    eligibilityError: String,
    eligibilityInfo: {}
  ): Promise<IClient> {
    let updatedClient = await Client.findByIdAndUpdate(
      id,
      {
        $set: {
          claimEligibilityMdErrorDetails: eligibilityError,
          claimEliMdInactiveDetails: eligibilityInfo,
        },
        $unset: {
          claimEligibilityMdDetails: 1,
          claimEligibilityDetails: 1
        }
      },
      { new: true }
    );
    
    return updatedClient;
  }

  export async function getClientCopaymentAmount(
    clientId: StringOrObjectId
  ): Promise<Number | null> {
    try {
      const client = await Client.findById(clientId, { copaymentAmount: 1 });
      return client ? client.copaymentAmount : null;
    } catch (error) {
      console.error("Error fetching client copayment amount:", error);
      return null;
    }
  }
}
