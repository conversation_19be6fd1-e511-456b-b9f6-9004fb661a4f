import { StringOrObjectId } from "../common/util";
import { Types } from "mongoose";
import { DInvoice, IInvoice } from "../models/invoice-model";
import Invoice from "../schemas/invoice-shema";

export namespace InvoiceDao {
  const populateOptions = [{ path: "clientId" }];

  export async function getInvoiceById(
    invoiceId: Types.ObjectId,
    clientId: Types.ObjectId,
  ): Promise<IInvoice> {
    const response = await Invoice.findOne({ _id: invoiceId, clientId: clientId });

    return response;
  }

  export async function addInvoice(details: DInvoice): Promise<IInvoice> {
    const paymentDetails = new Invoice(details);
    const response = await paymentDetails.save();
    return response;
  }

  export async function updateInvoice(
    id: StringOrObjectId,
    data: Partial<DInvoice>
  ): Promise<IInvoice> {
    const insurancePlan = await Invoice.findByIdAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    ).populate(populateOptions);

    return insurancePlan;
  }

  export async function getInvoiceByClient(
    clientId: Types.ObjectId
  ): Promise<IInvoice[]> {
    const response = await Invoice.find({ clientId: clientId }).sort({
      createdAt: -1,
    });
    return response;
  }

  export async function getInvoiceListByClientId(
    clientId: Types.ObjectId,
    limit: number,
    offset: number
  ): Promise<IInvoice[]> {
    const response = await Invoice.find({
      clientId: clientId,
    })
      .populate(populateOptions)
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return response;
  }

  export async function getInvoiceByClientIdAndMonth(
    clientId: Types.ObjectId,
    month: string
  ): Promise<IInvoice> {
    const response = await Invoice.aggregate([
      {
        $match: {
          $and: [{ clientId: clientId }, { paymentMonth: month }],
        },
      },
    ]);

    return response[0];
  }

  export async function deleteInvoicesByClientId(
    clientId: StringOrObjectId
  ): Promise<number> {
    const response = await Invoice.deleteMany({
      clientId: clientId,
    });
    return response.ok;
  }
}
