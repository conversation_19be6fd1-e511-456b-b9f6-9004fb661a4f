import ReferralEarning from "../schemas/referral-earning-schema";
import Therapist from "../schemas/therapist-schema";
import { StringOrObjectId } from "../common/util";
import { DReferralEarning, IReferralEarning } from "../models/referral-earning-model";
import { TransactionType } from "../models/transaction-model";
import TherapistReferral from "../schemas/therapist-referral-schema";

export namespace ReferralEarningDao {

  export async function getRecentReferralEarningBalance(therapistId: any): Promise<IReferralEarning> {
    const response = (await ReferralEarning.find({ therapistId: therapistId }).sort({ _id: -1 }).limit(1))[0];
    return response;
  }

  export async function getReferralEarningsWithoutFilter(limit: number, offset: number): Promise<any[]> {
    let referralEarningsList = await ReferralEarning.find().sort({ createdAt: -1 }).skip(limit * (offset - 1))
    .limit(limit)
    .exec();

    await ReferralEarning.populate(referralEarningsList, {
      path: 'therapistId',
      model: Therapist,
      select: 'firstname lastname email',
    });
    return referralEarningsList;
  }

  export async function getAllReferralEarningsForCurrentMonth(): Promise<any[]> {

    const date = new Date();
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1, 0, 0, 0, 0);
    const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999);

    let referralEarningsList = await ReferralEarning.find({
      verifiedStatus: { $in: ["APPROVED", "PENDING"] },
      paidStatus: { $in: [undefined, null] },
      createdAt: { $gte: startOfMonth, $lte: endOfMonth }
    })
    .sort({ createdAt: -1 })
    .exec();
    return referralEarningsList;
  }

  export async function getApprovedReferralEarningsForCurrentMonth(): Promise<any[]> {

    const date = new Date();
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1, 0, 0, 0, 0);
    const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999);

    let referralEarningsList = await ReferralEarning.find({
      verifiedStatus: "APPROVED",
      paidStatus: { $in: [undefined, null] },
      createdAt: { $gte: startOfMonth, $lte: endOfMonth }
    })
    .sort({ createdAt: -1 })
    .exec();
    return referralEarningsList;
  }

  export async function getReferralEarningById(
    id: StringOrObjectId
  ): Promise<IReferralEarning> {
    const response = await ReferralEarning.findById(id);
    return response;
  }

  export async function getReferralEarningByTherapistId(therapistId: any): Promise<IReferralEarning[]> {
    const rewards = await ReferralEarning.find({ therapistId: therapistId }).sort({ createdAt: -1 });

      await ReferralEarning.populate(rewards, {
        path: 'rewardReferralId',
        model: TherapistReferral,
        select: 'referredUserId -_id',
      }),
      await ReferralEarning.populate(rewards, {
        path: 'rewardReferralId.referredUserId',
        model: Therapist,
        select: 'firstname lastname _id',
      })

    return rewards;
  }

  export async function updateReferralEarningById(
    id: StringOrObjectId,
    data: Partial<DReferralEarning>
  ): Promise<IReferralEarning> {
    let earning = await ReferralEarning.findOneAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    );
    return earning;
  }

  export async function deleteReferralEarningById(
    id: StringOrObjectId
  ): Promise<IReferralEarning> {
    const response = await ReferralEarning.findByIdAndDelete(id);
    return response;
  }

  export async function getReferralEarningForPaymentById(
    referralEarningId: any,
    therapistid: any
  ): Promise<IReferralEarning> {
    const trans = await ReferralEarning.findOne({
      _id: referralEarningId,
      therapistId: therapistid,
    });
    return trans;
  }

  export async function withdrawReferralEarning(therapistId: any, transactionAmount: string, type: TransactionType): Promise<IReferralEarning> {
    const recentBalance = await ReferralEarningDao.getRecentReferralEarningBalance(therapistId);

    let iReferralEarning;

    if (recentBalance) {
        const referralEarningDetails: DReferralEarning = {
            therapistId: therapistId,
            type: type,
            transactionAmount: parseFloat(transactionAmount),
            accumulatedBalance: recentBalance.accumulatedBalance - parseFloat(transactionAmount),
            accumulatedTotalEarnings: recentBalance.accumulatedTotalEarnings,
            accumulatedWithdrawals: recentBalance.accumulatedWithdrawals + parseFloat(transactionAmount),
        };

        iReferralEarning = new ReferralEarning(referralEarningDetails);
    } else {
        return null;
    }

    const response = iReferralEarning.save();

    return response;
  }

  export async function updateReferralEarningList(
    idList: Array<string | ObjectId>
  ): Promise<DReferralEarning[]> {
      await ReferralEarning.updateMany(
          {
              _id: { $in: idList }
          },
          {
              $set: { paidStatus: 'PAID' }
          }
      );
      const updatedReferralEarnings = await ReferralEarning.find({
          _id: { $in: idList }
      });

      return updatedReferralEarnings;
  }
  
  export async function createReferralEarningForRewards(therapistId: any, transactionAmount: string, type: TransactionType, rewardType: string, rewardReferralId: any ): Promise<IReferralEarning> {
    const recentBalance = await ReferralEarningDao.getRecentReferralEarningBalance(therapistId);

    let iReferralEarning;

    if (recentBalance) {
        const referralEarningDetails: DReferralEarning = {
            therapistId: therapistId,
            type: type,
            transactionAmount: parseFloat(transactionAmount),
            accumulatedBalance: recentBalance.accumulatedBalance + parseFloat(transactionAmount),
            accumulatedTotalEarnings: recentBalance.accumulatedTotalEarnings + parseFloat(transactionAmount),
            accumulatedWithdrawals: recentBalance.accumulatedWithdrawals,
            rewardType: rewardType,
            rewardReferralId: rewardReferralId,
            verifiedStatus: "PENDING"
        };

        iReferralEarning = new ReferralEarning(referralEarningDetails);
    } else {
        const referralEarningDetails: DReferralEarning = {
            therapistId: therapistId,
            type: type,
            transactionAmount: parseFloat(transactionAmount),
            accumulatedBalance: parseFloat(transactionAmount),
            accumulatedTotalEarnings: parseFloat(transactionAmount),
            accumulatedWithdrawals: 0,
            rewardType: rewardType,
            rewardReferralId: rewardReferralId,
            verifiedStatus: "PENDING"
        };

        iReferralEarning = new ReferralEarning(referralEarningDetails);
    }

    const response = iReferralEarning.save();

    return response;
  }

  export async function getAllApprovedTherapistRewardsCount(): Promise<number> {
    const response = await ReferralEarning.find({ verifiedStatus: { $eq: "PENDING" } }).countDocuments();
    return response;
  }

}
