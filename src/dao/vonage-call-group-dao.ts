import ChatGroupCall from "../schemas/chat-group-call-schema";
import { Types } from "mongoose";
import ChatGroupMember from "../schemas/chat-group-member-schema";
import { ChatGroupCallCallingStatus } from "../models/chat-group-call-model";

export namespace VonageCallGroupDao {
  export async function getGroupMembersSocketIds(chatGroupCallId: Types.ObjectId): Promise<any> {
   const groupMembersWithUserDetails = await ChatGroupCall.aggregate([
          {
            $match: { _id: chatGroupCallId }  
          },
          {
            $lookup: {
              from: 'chatgroupmembers',      
              localField: 'groupId',
              foreignField: 'groupId', 
              as: 'groupMembersWithUserDetails' 
            }
          },
          {
            $unwind: '$groupMembersWithUserDetails'
          },
          {
            $lookup: {
              from: 'users',  
              localField: 'groupMembersWithUserDetails.userId',
              foreignField: '_id',
              as: 'userDetails' 
            }
          },
          {
            $unwind: '$userDetails' 
          },
          {
            $project: {
              'userDetails.socketId': 1,
              'userDetails._id': 1
            }
          }
    ]);

    return groupMembersWithUserDetails;
  }

  export async function getAllUsersInGroupByGroupCallId(groupCallId: Types.ObjectId): Promise<any>{
    try {
      console.log("################################################");
      const allUsersInGroup = await ChatGroupCall.aggregate([
        {
          $match : { _id: groupCallId }
        },
        {
          $lookup: {
            from: 'chatgroupmembers',
            localField: 'groupId',
            foreignField: 'groupId',
            as: 'groupMembersData'
          }
        },
        {
          $unwind: '$groupMembersData'
        },
        {
          $match: {
            'groupMembersData.role': 'MEMBER'
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'groupMembersData.userId',
            foreignField: '_id',
            as: 'userData'
          }
        },
        {
          $unwind: '$userData'
        },
        {
          $project: {
            userId: '$userData._id',
            userEmail: '$userData.email',
            userPhoneNumber: '$userData.primaryPhone',
          }
        }
      ])

      return allUsersInGroup;
    } catch (error) {
      console.log(error);
      
    }
  }

  export async function getAllUpCommingGroupCalls(userRole: string, userId: Types.ObjectId, userLimit: number): Promise<any> {
    try {
      
      const groupIdsResult = await ChatGroupMember.aggregate([
        { $match: { userId: userId}},
        { $group: { _id: null, groupIds: { $push: '$groupId' } } },
      ]);

      const groupIds = groupIdsResult.length > 0 ? groupIdsResult[0].groupIds : [];

      const groupCallsData = await ChatGroupCall.aggregate([
        { 
          $match:{
            $and : [{ groupId: { $in: groupIds } }, { callingStatus: ChatGroupCallCallingStatus.PENDING }]
          } 
        },
        { 
          $lookup: {
            from: 'chatgroups',
            localField: 'groupId',
            foreignField: '_id',
            as: 'groupData',
          }
        },
        {
          $unwind: '$groupData', 
        },
        {
          $project: {
            groupCallId:'$_id',
            groupId: '$groupId', 
            callingStatus: '$callingStatus', 
            groupName: '$groupData.title',
            startTime: '$start'
          },
        },
        {
          $sort: {
            start: 1, 
          },
        },
        {
          $limit: 3,
        },
      ]);
      return groupCallsData;
    } catch (error) {
      console.error('Error fetching data:', error);
    }
    
  }

  export async function checkOngoingGroupCalls(userId: Types.ObjectId): Promise<any> {
    try {
      const groupIdsResult = await ChatGroupMember.aggregate([
        { $match: { userId: userId}},
        { $group: { _id: null, groupIds: { $push: '$groupId' } } },
      ]);
      const groupIds = groupIdsResult.length > 0 ? groupIdsResult[0].groupIds : [];

      const onGoingGroupCall = await ChatGroupCall.aggregate([
        {
          $match: {
            $and : [{ groupId: { $in: groupIds } }, { callingStatus: ChatGroupCallCallingStatus.ONGOING }]
          }
        },
        { 
          $lookup: {
            from: 'chatgroups',
            localField: 'groupId',
            foreignField: '_id',
            as: 'groupData',
          }
        },
        {
          $unwind: '$groupData', 
        },
        {
          $project: {
            groupCallId:'$_id',
            groupId: '$groupId', 
            callingStatus: '$callingStatus', 
            groupName: '$groupData.title',
            startTime: '$start',
          },
        },
        {
          $sort: {
            start: 1, 
          },
        },
        {
          $limit: 1,
        },
        {
          $lookup: {
            from: 'chatgroupmembers',
            localField: 'groupId',
            foreignField: 'groupId',
            as: 'groupMembersData',
          },
        },
        {
          $unwind: '$groupMembersData',
        },
        {
          $match: {
            'groupMembersData.role': 'SUPER_ADMIN',
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'groupMembersData.userId',
            foreignField: '_id',
            as: 'superAdminData',
          },
        },
        {
          $unwind: '$superAdminData',
        },
        {
          $project: {
            groupCallId: 1,
            groupId: 1,
            callingStatus: 1,
            groupName: 1,
            startTime: 1,
            superAdminFirstName: '$superAdminData.firstname',
            superAdminLastName: '$superAdminData.lastname',
          },
        },
      ]);

      console.log("onGoingGroupCall: ", onGoingGroupCall);
      
      return onGoingGroupCall;
    } catch (error) {
      console.error('Error fetching data:', error);
    }
    
  }

}
