import User from "../schemas/user-schema";
import { UserRole } from "../models/user-model";
import Meeting from "../schemas/meeting-schema";
import { IClient } from "../models/client-model";
import Client from "../schemas/client-schema";
import Upload from "../schemas/upload-schema";
import { CallingStatus } from "../models/meeting-model";
import { Types } from "mongoose";

export namespace VonageNativeCallDao {
    export async function getAvatarDetailsAndClientStatus(
        recieverId: any,
        currentUserId: any,
      ): Promise<any> {
        let testSubscriptionStatus = "";
        let clientSubscriptionId = "";
        let clientSubStatus = "";
        let clientPremiumStatus = "";
        const userData = await User.findById(recieverId);
        if (!userData) {
          return 'user_not_found';
        }
        if (userData.role == UserRole.CLIENT) {
          let responseClient: IClient = await Client.findById(userData._id);
          
          if (responseClient != null) {
            testSubscriptionStatus = responseClient.testSubscriptionStatus
              ? responseClient.testSubscriptionStatus
              : "";
            clientSubscriptionId = responseClient.subscriptionId;
            clientSubStatus = responseClient.subscriptionStatus;
            clientPremiumStatus = responseClient.premiumStatus;
          }
        } else {
          let responseClient: IClient = await Client.findById(currentUserId);

          if (responseClient != null) {
            testSubscriptionStatus = responseClient.testSubscriptionStatus
              ? responseClient.testSubscriptionStatus
              : "";
            clientSubscriptionId = responseClient.subscriptionId;
            clientSubStatus = responseClient.subscriptionStatus;
            clientPremiumStatus = responseClient.premiumStatus;
          }
        }
        let finalData;
        const isUsedDefaultAvatar = userData?.useDefaultAvatar;
        if (isUsedDefaultAvatar !== undefined) {
          if (isUsedDefaultAvatar) {
            finalData = {
              useDefaultAvatar: true,
              avatarId: userData?.defaultAvatarId,
              avatarBackgroundId: userData?.avatarBackgroundId,
              incognito:
                userData?.incognito != null &&
                  userData?.incognito &&
                  userData?.role == UserRole.CLIENT
                  ? true
                  : false,
              socketId: userData?.socketId,
              callerName: userData?.firstname,
              clientSubscriptionId: clientSubscriptionId,
              clientSubStatus: clientSubStatus,
              clientPremiumStatus: clientPremiumStatus,
              testSubscriptionStatus: testSubscriptionStatus,
              // isMeetingTimeRemained: isMeetingTimeRemained,
              // remainingMeetingTime: 300 - totalMeetingTime,
              callRecordingAllowed:
                userData?.callRecordingAllowed != null &&
                  userData?.callRecordingAllowed
                  ? true
                  : false,
            };
          } else {
            const uploadData = await Upload.findById(userData?.avatarId);
    
            finalData = {
              useDefaultAvatar: false,
              avatarId: uploadData?.url,
              avatarBackgroundId: userData?.avatarBackgroundId,
              incognito:
                userData?.incognito != null &&
                  userData?.incognito &&
                  userData?.role == UserRole.CLIENT
                  ? true
                  : false,
              socketId: userData?.socketId,
              callerName: userData?.firstname,
              clientSubscriptionId: clientSubscriptionId,
              clientSubStatus: clientSubStatus,
              clientPremiumStatus: clientPremiumStatus,
              testSubscriptionStatus: testSubscriptionStatus,
              // isMeetingTimeRemained: isMeetingTimeRemained,
              // remainingMeetingTime: 300 - totalMeetingTime,
              callRecordingAllowed:
                userData?.callRecordingAllowed != null &&
                  userData?.callRecordingAllowed
                  ? true
                  : false,
            };
          }
        } else {
          finalData = {
            useDefaultAvatar: true,
            avatarId: userData?.gender === "Female" ? "avatarTwo" : "avatarOne",
            avatarBackgroundId:
              userData?.avatarBackgroundId !== undefined
                ? userData?.avatarBackgroundId
                : "backgroundOne",
            incognito:
              userData?.incognito != null &&
                userData?.incognito &&
                userData?.role == UserRole.CLIENT
                ? true
                : false,
            socketId: userData?.socketId,
            callerName: userData?.firstname,
            clientSubscriptionId: clientSubscriptionId,
            clientSubStatus: clientSubStatus,
            clientPremiumStatus: clientPremiumStatus,
            testSubscriptionStatus: testSubscriptionStatus,
            // isMeetingTimeRemained: isMeetingTimeRemained,
            // remainingMeetingTime: 300 - totalMeetingTime,
            callRecordingAllowed:
              userData?.callRecordingAllowed != null && userData?.callRecordingAllowed
                ? true
                : false,
          };
        }
        return finalData;
      }

  export async function getIncompleteMeetingsByUserId(userId: Types.ObjectId, role: string){
    const userQuery =
    role == UserRole.CLIENT
      ? { clientId: userId }
      : { therapistId: userId };

    const statusQueryForIncompleteMeetings = {
      callingStatus: { $in: [CallingStatus.STARTED, CallingStatus.ONGOING] },
      isVonageNativeSDKCall: { $eq : true}
    };

    // check for incompleted calls
    let incompletedMeetingsResponse = await Meeting.aggregate([
      {
        $match: {
          $and: [userQuery, statusQueryForIncompleteMeetings],
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          as: "therapistData",
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          as: "clientData",
        },
      },

      {
        $project: {
          therapistData: {
            _id: 1,
            firstname: 1,
            lastname: 1
          },
          clientData: {
            _id: 1,
            firstname: 1,
            lastname: 1
          },
          _id: 1,
          meetingId: 1,
          meetingDuration: 1,
          accepted: 1,
          transcribeAllowed: 1,
          isAppointmentBased: 1,
          appointmentId: 1,
          recordingAllowed: 1,
          createdBy: 1,
          callingStatus: 1,
          vonageSessionName: 1
        },
      },
      {
        $unwind: {
          path: "$therapistData",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$clientData",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);

    return incompletedMeetingsResponse;
  }

  export async function getMeetingDetailsBySessionId(vonageSessionName: string){
    const meetingData = await Meeting.findOne(
      {vonageSessionName: vonageSessionName},
      { _id: 1, clientId: 1, therapistId: 1, meetingId: 1 }
    );

    return meetingData;
  }

  export async function updateTranscribeCreatedFieldById(id: Types.ObjectId){
    const meetingData = await Meeting.findByIdAndUpdate(id,
      { transcribeCreated: true }
    );

    return meetingData;
  }

}
