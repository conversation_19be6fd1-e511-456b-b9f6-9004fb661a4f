import { DTherapyPlan, ITherapyPlan } from "../models/therapy-plan-model";
import TherapyPlan from "../schemas/therapy-plan-schema";

export namespace TherapyPlanDao {

  export async function addDetails(
    therapyPlanDetails: DTherapyPlan
  ): Promise<ITherapyPlan> {
      const request = new TherapyPlan(therapyPlanDetails);
      let response = await request.save();
      return response;
  }

  export async function updateDetails(
    id: string,
    therapyPlanDetails: Partial<DTherapyPlan>
  ): Promise<ITherapyPlan | null> {
      const response = await TherapyPlan.findByIdAndUpdate(
        id,
        { $set: therapyPlanDetails },
        { new: true }
      );
      return response;
  }

  export async function getTherapyPlanByClientId(
    therapyPlanId: string
  ): Promise<ITherapyPlan | null> {
    const therapyPlan = await TherapyPlan.findById(therapyPlanId);
    return therapyPlan;
  }

  export async function updateClientTherapyPlanSignature(
    id:string,
    data:Partial<DTherapyPlan>,
    removeData: { removeClientSignature?: boolean, removeLrpSignature?: boolean }
  ): Promise<ITherapyPlan | null> {
      const updateOps: any = {};

      if (data.lrpSignature !== undefined) {
          updateOps.$set = { 
            lrpSignature: data.lrpSignature, 
            isSignature: data.isSignature 
          };
          
          if (data.lrpSignatureDetails) {
            updateOps.$set.lrpSignatureDetails = data.lrpSignatureDetails;
          }
      }
      if (data.clientSignature !== undefined) {
          updateOps.$set = { 
            clientSignature: data.clientSignature, 
            isSignature: data.isSignature 
          };
          
          if (data.clientSignatureDetails) {
            updateOps.$set.clientSignatureDetails = data.clientSignatureDetails;
          }
      }

      updateOps.$set = { ...updateOps.$set, isSignature: data.isSignature };

      if (removeData.removeClientSignature) {
          updateOps.$unset = { clientSignature: "", clientSignatureDetails: "" };
      }
      if (removeData.removeLrpSignature) {
          updateOps.$unset = { lrpSignature: "", lrpSignatureDetails: "" };
      }

      const response = await TherapyPlan.findByIdAndUpdate(id, updateOps, { new: true });
      return response;
  }

}

