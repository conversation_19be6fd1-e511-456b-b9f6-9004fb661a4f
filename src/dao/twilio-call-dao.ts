import * as <PERSON>wi<PERSON> from "twilio";
import { isAValidPhoneNumber } from "./../helpers/twilio-call-helper";

export namespace TwilioCallDao {
  export async function dispatchVoiceToken(identity: string): Promise<string> {
    const token = new Twilio.jwt.AccessToken(
      process.env.TWILIO_ACCOUNT_SID as string,
      process.env.TWILIO_API_KEY as string,
      process.env.TWILIO_API_SECRET as string,
      {
        ttl: 10800,
        identity: identity,
      }
    );

    const voiceGrant = new Twilio.jwt.AccessToken.VoiceGrant({
      outgoingApplicationSid: process.env.TWILIO_TWIML_APP_SID,
      incomingAllow: true,
    });

    token.addGrant(voiceGrant);

    const jwt = token.toJwt();

    return jwt;
  }

  export async function twilioVoiceCall(To: string) {
    const twiml = new Twilio.twiml.VoiceResponse();

    const callerId = process.env.TWILIO_CALLING_PHONE_NUMBER;

    if (To === callerId) {
      const dial = twiml.dial();
      dial.client("Lavni user");
    } else if (To) {
      const dial = twiml.dial({
        callerId,
        record: "record-from-ringing-dual",
      });
      const attr: "number" | "client" = isAValidPhoneNumber(To)
        ? "number"
        : "client";
      dial[attr]({}, To);
    } else {
      twiml.say("Thanks for calling!");
    }
    return twiml.toString();
  }
}
