import {UserStatus} from "../models/user-model";
import Client from "../schemas/client-schema";
import Therapist from '../schemas/therapist-schema';
import Appointment from '../schemas/appointment-schema';
import User from "../schemas/user-schema";
import {IClient, SubscriptionStatus} from "../models/client-model";
import {AppointmentStatus, ApprovalStatus, MeetingStatus} from "../models/appointment-model";
import Meeting from "../schemas/meeting-schema";
import {CallingStatus, IMeeting} from "../models/meeting-model";
import {StringOrObjectId} from "../common/util";
import {ITherapist} from "../models/therapist-model";
import {DTreatmentHistory, ITreatmentHistory} from "../models/treatment-history-model";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import {Types} from "mongoose";
import {IInsurance} from "../models/insurance-model";
import Insurance from "../schemas/insurance-schema";
import {IDiagnosisNote} from "../models/diagnosis-note-model";
import DiagnosisNote from "../schemas/diagnosis-note-schema";
import {AdminDao} from "./admin-dao";
import {IAdminStatistics} from "../models/admin-statistics-model";
import AdminStatistics from "../schemas/admin-statistics";
import AdminStatisticsHourly from "../schemas/admin-statistic-hourly";
import { AppLogger } from "../common/logging";

const moment = require('moment-timezone');

export namespace AdminStatisticsDao {
    const populateOptions = [
        {
            path: "insuranceCardId",
        },
        {
            path: "insuranceCardBackId",
        },
    ];

    const populateOptions2 = [
        {
            path: "therapistId",
            select: {
                firstname: 1,
                lastname: 1,
                email: 1,
                primaryPhone: 1,
                reminderType: 1,
                reminderTime: 1,
            },
        },
        {
            path: "clientId",
            select: {
                firstname: 1,
                lastname: 1,
                email: 1,
                primaryPhone: 1,
                reminderType: 1,
                reminderTime: 1,
                smsStop: 1,
            },
        },
        {
            path: "meetingId",
            select: {
                accepted: 1,
            },
        },
    ];

    export async function getAllClientCount(): Promise<number> {
        const client = await Client.find();
        return client.length;
    }

    export async function getAllPendingClientCount(): Promise<number> {
        const client = await Client.find({verifiedStatus: UserStatus.PENDING}, {new: true});
        return client.length;
    }

    export async function getAllUnpaidClaims(): Promise<IMeeting[]> {
        const meetings = await Meeting.aggregate([
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                firstname: 1,
                                lastname: 1,
                                email: 1,
                                lavniTestAccount: 1,
                                primaryPhone: 1,
                            },
                        },
                    ],
                    as: "clientDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $match: {
                    "copayment.status": "UNPAID",
                },
            },
        ]);

        return meetings;
    }

    export async function getAllTherapistCount(): Promise<number> {
        const therapist = await Therapist.find();
        return therapist.length;
    }

    export async function getaAllAppointmentStatistics(): Promise<any> {
        const appointment = await Appointment.aggregate([
            {
                $group: {
                    _id: "$status",
                    count: {$sum: 1}
                }
            },
            {
                $replaceRoot: {
                    newRoot: {status: "$_id", count: "$count"}
                }
            }
        ]);

        return appointment;
    }

    export async function getPastMissedAppointmentsCount(date: number): Promise<number> {
        const currentDate = new Date();

        let queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
            "therapistId.lavniTestAccount": {$ne: true},
            "status": AppointmentStatus.PENDING
        };

        if (date == 365) {
            const firstDateOfCurrentYear = new Date(currentDate.getFullYear(), 0, 1);

            queryConditions["start"] = {$lte: currentDate, $gte: firstDateOfCurrentYear};
        } else if (date == 30) {
            const firstDayOfCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

            queryConditions["start"] = {$lte: currentDate, $gte: firstDayOfCurrentMonth};
        } else {
            const firstDayOfCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

            var timeDiff = Math.abs(currentDate.getTime() - firstDayOfCurrentMonth.getTime());

            var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));

            if (diffDays > 7) {
                let oneWeekBefore = new Date(currentDate);

                oneWeekBefore.setDate(currentDate.getDate() - 6);

                queryConditions["start"] = {$lte: currentDate, $gte: oneWeekBefore};
            } else {
                queryConditions["start"] = {$lte: currentDate, $gte: firstDayOfCurrentMonth};
            }
        }

        const pastPendingAppointments = await Appointment.aggregate([
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {lavniTestAccount: 1, _id: 1},
                        },
                    ],
                    as: "clientId",
                },
            },
            {
                $unwind: {
                    path: "$clientId",
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "therapistId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {lavniTestAccount: 1, _id: 1},
                        },
                    ],
                    as: "therapistId",
                },
            },
            {
                $unwind: {
                    path: "$therapistId",
                },
            },
            {
                $match: {
                    $and: [queryConditions],
                },
            },
        ]);

        const spentDurationLimit = 30;
        let mergedAppointments = [];

        if (pastPendingAppointments != null) {
            await Promise.all(
                pastPendingAppointments.map(async (appointment) => {
                    try {
                        if (
                            appointment &&
                            appointment._id &&
                            appointment.meetingId &&
                            appointment.clientId &&
                            appointment.therapistId &&
                            appointment.start &&
                            appointment.status == AppointmentStatus.PENDING &&
                            appointment.approvedStatus == ApprovalStatus.APPROVED &&
                            appointment.meetingStatus == MeetingStatus.PENDING
                        ) {
                            const meetingDetails = await Meeting.findOne({
                                clientId: appointment.clientId,
                                therapistId: appointment.therapistId,
                                isAppointmentBased: true,
                                appointmentId: appointment._id,
                                meetingId: appointment.meetingId,
                            });
                            if (
                                meetingDetails &&
                                meetingDetails.callingStatus &&
                                meetingDetails.callingStatus == CallingStatus.CANCELLED
                            ) {
                                const startOfTheDayOfAppointment = moment(
                                    appointment.start
                                ).startOf("day").toDate();
                                const endOfTheDayOfAppointment = moment(
                                    appointment.start
                                ).endOf("day").toDate();

                                const nearestCompletedMeeting = await Meeting.findOne({
                                    clientId: appointment.clientId,
                                    therapistId: appointment.therapistId,
                                    isAppointmentBased: false,
                                    meetingId: {$exists: true},
                                    accepted: true,
                                    callingStatus: CallingStatus.COMPLETED,
                                    spentDuration: {$gte: spentDurationLimit},
                                    $or: [
                                        {isGroupCall: {$exists: false}},
                                        {isGroupCall: false},
                                    ],
                                    createdAt: {
                                        $gte: startOfTheDayOfAppointment,
                                        $lte: endOfTheDayOfAppointment,
                                    },
                                });

                                if (nearestCompletedMeeting && nearestCompletedMeeting.meetingId) {
                                    mergedAppointments.push(appointment);
                                }
                            }
                        }
                    } catch (error) {

                    }
                })
            );
        }

        let finalCount = pastPendingAppointments.length - mergedAppointments.length;

        if (finalCount < 0) {
            finalCount = 0;
        }

        return finalCount;
    }

    export async function getAllPastAppointmentsByTherapistIdCount(userId: StringOrObjectId): Promise<number> {
        const currentDate = new Date();
        const pastAppointments = await Appointment.find({
            therapistId: userId,
            status: AppointmentStatus.PENDING,
            start: {$lt: currentDate}
        });
        return pastAppointments.length;
    }

    export async function getAllPastAppointmentsByClientIdCount(userId: StringOrObjectId): Promise<number> {
        const currentDate = new Date();

        const pastAppointments = await Appointment.find({
            clientId: userId,
            status: AppointmentStatus.PENDING,
            start: {$lt: currentDate}
        });

        return pastAppointments.length;
    }

    export async function getAllSessionsByTherapistIdCount(userId: StringOrObjectId): Promise<number> {
        const today = new Date();
        const year = today.getFullYear();
        const month = today.getMonth();
        const startDate = new Date(year, month, 1);
        const endDate = new Date(year, month + 1, 0);
        const sessionCount = await Meeting.find({
            therapistId: userId,
            callingStatus: "COMPLETED",
            createdAt: {
                $gte: startDate,
                $lte: endDate,
            }
        });
        return sessionCount.length;
    }

    export async function getAllAppointmentsByTherapistIdCount(userId: StringOrObjectId): Promise<number> {
        const totalAppointments = await Appointment.find({
            therapistId: userId,
        });
        return totalAppointments.length;
    }

    export async function getWeeklyAppointmentssByTherapistIdCount(userId: StringOrObjectId): Promise<number> {
        const pastAppointments = await Appointment.find({
            therapistId: userId,
        });
        const daysAgo = new Date();
        daysAgo.setDate(daysAgo.getDate() - 7);
        const filteredMeetings = pastAppointments.filter((meeting: any) => {
            const meetingDate = new Date(meeting.createdAt);
            return meetingDate >= daysAgo;
        });
        return filteredMeetings.length;
    }

    export async function getPastAppointmentCountsForTherapists(searchResult: ITherapist[]): Promise<{
        therapist: ITherapist;
        avarageMissedAppointments: number
    }[]> {
        const therapistsWithCounts: {
            therapist: ITherapist;
            totalAppointments: number,
            averageWeeklyAppointments: number,
            avarageMissedAppointments: number,
            therapistSessionsCountMonth: number
        }[] = [];

        for (const therapist of searchResult) {
            const populatedTherapist = await Therapist.findById(therapist._id).exec();
            const totalAppointments = await getAllAppointmentsByTherapistIdCount(therapist._id);
            const averageWeeklyAppointments = await getWeeklyAppointmentssByTherapistIdCount(therapist._id);
            const avarageMissedAppointments = await getAllPastAppointmentsByTherapistIdCount(therapist._id);
            const therapistSessionsCountMonth = await getAllSessionsByTherapistIdCount(therapist._id);
            therapistsWithCounts.push({
                therapist: populatedTherapist,
                totalAppointments,
                averageWeeklyAppointments,
                avarageMissedAppointments,
                therapistSessionsCountMonth
            });
        }

        return therapistsWithCounts;
    }

    export async function getAllScheduledAppointmentsCount(date: number): Promise<number> {
        const currentDate = new Date();

        let queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
            "therapistId.lavniTestAccount": {$ne: true}
        };

        if (date == 365) {
            const firstDateOfCurrentYear = new Date(currentDate.getFullYear(), 0, 1);

            queryConditions["start"] = {$lte: currentDate, $gte: firstDateOfCurrentYear};
        } else if (date == 30) {
            const firstDayOfCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

            queryConditions["start"] = {$lte: currentDate, $gte: firstDayOfCurrentMonth};
        } else {
            const firstDayOfCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

            var timeDiff = Math.abs(currentDate.getTime() - firstDayOfCurrentMonth.getTime());

            var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));

            if (diffDays > 7) {
                let oneWeekBefore = new Date(currentDate);

                oneWeekBefore.setDate(currentDate.getDate() - 6);

                queryConditions["start"] = {$lte: currentDate, $gte: oneWeekBefore};
            } else {
                queryConditions["start"] = {$lte: currentDate, $gte: firstDayOfCurrentMonth};
            }
        }

        const scheduledAppointments = await Appointment.aggregate([
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {lavniTestAccount: 1, _id: 1},
                        },
                    ],
                    as: "clientId",
                },
            },
            {
                $unwind: {
                    path: "$clientId",
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "therapistId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {lavniTestAccount: 1, _id: 1},
                        },
                    ],
                    as: "therapistId",
                },
            },
            {
                $unwind: {
                    path: "$therapistId",
                },
            },
            {
                $match: {
                    $and: [queryConditions],
                },
            },
        ]);

        return scheduledAppointments.length;
    }

    export async function getAllMonthlyRecurringRevenueCount(): Promise<number> {
        const currentDate = new Date();

        const firstDayOfCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

        const queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
            "callingStatus": "COMPLETED",
            "createdAt": {$lte: currentDate, $gte: firstDayOfCurrentMonth}
        };

        const monthlyRecurringRevenueCount = calculateRecurringValue(queryConditions);

        return monthlyRecurringRevenueCount;
    }

    export async function getAllYearlyRecurringRevenueCount(): Promise<number> {
        const currentDate = new Date();

        const firstDateOfCurrentYear = new Date(currentDate.getFullYear(), 0, 1);

        const queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
            "callingStatus": "COMPLETED",
            "createdAt": {$lte: currentDate, $gte: firstDateOfCurrentYear}
        };

        const yearlyRecurringRevenueCount = calculateRecurringValue(queryConditions);

        return yearlyRecurringRevenueCount;
    }

    async function calculateRecurringValue(queryConditions: any) {
        const meetings = await Meeting.aggregate([
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {lavniTestAccount: 1, _id: 1, insuranceId: 1},
                        },
                    ],
                    as: "client",
                },
            },
            {
                $unwind: {
                    path: "$client",
                },
            },
            {
                $lookup: {
                    from: "insurances",
                    localField: "client.insuranceId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {insuranceCompanyId: 1},
                        },
                    ],
                    as: "insurance",
                },
            },
            {
                $unwind: {
                    path: "$insurance",
                },
            },
            {
                $lookup: {
                    from: "insurancecompanies",
                    localField: "insurance.insuranceCompanyId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {insuranceCompany: 1, contractPrice: 1},
                        },
                    ],
                    as: "insuranceCompany",
                },
            },
            {
                $unwind: {
                    path: "$insuranceCompany",
                },
            },
            {
                $project: {
                    _id: 1,
                    clientId: 1,
                    callingStatus: 1,
                    createdAt: 1,
                    client: 1,
                    insurance: 1,
                    insuranceCompany: 1
                }
            },
            {
                $match: {
                    $and: [queryConditions],
                },
            },
        ]);

        let totalValue = 0;

        meetings.filter((m: any) => {
            totalValue += parseFloat(m.insuranceCompany.contractPrice);
        });

        return parseFloat(totalValue.toFixed(2));
    }

    export async function getAllLifeTimeSalesCount(): Promise<number> {
        const queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
            "callingStatus": "COMPLETED"
        };

        const lifeTimeSales = calculateRecurringValue(queryConditions);

        return lifeTimeSales;
    }

    export async function getAverageCustomerLifetimeValue(): Promise<number> {
        const currentDate = new Date();

        let oneMonthBefore = new Date(currentDate);

        oneMonthBefore.setDate(currentDate.getDate() - 30);

        const queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
            "callingStatus": "COMPLETED",
            "client.createdAt": {$lte: oneMonthBefore}
        };

        const lifeTimeSalesMeetings = await Meeting.aggregate([
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {lavniTestAccount: 1, _id: 1, insuranceId: 1, createdAt: 1},
                        },
                    ],
                    as: "client",
                },
            },
            {
                $unwind: {
                    path: "$client",
                },
            },
            {
                $lookup: {
                    from: "insurances",
                    localField: "client.insuranceId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {insuranceCompanyId: 1},
                        },
                    ],
                    as: "insurance",
                },
            },
            {
                $unwind: {
                    path: "$insurance",
                },
            },
            {
                $lookup: {
                    from: "insurancecompanies",
                    localField: "insurance.insuranceCompanyId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {insuranceCompany: 1, contractPrice: 1},
                        },
                    ],
                    as: "insuranceCompany",
                },
            },
            {
                $unwind: {
                    path: "$insuranceCompany",
                },
            },
            {
                $project: {
                    _id: 1,
                    clientId: 1,
                    callingStatus: 1,
                    createdAt: 1,
                    client: 1,
                    insurance: 1,
                    insuranceCompany: 1,
                    totalSales: 1
                }
            },
            {
                $match: {
                    $and: [queryConditions],
                },
            },
        ]);

        const uniqueClientCount = lifeTimeSalesMeetings.reduce((accumulator, currentValue) => {
            let found = accumulator.some((obj: any) => obj.clientId == currentValue.clientId.toString());

            if (!found) {
                accumulator.push(currentValue);
            }

            return accumulator;
        }, []);

        let lifeTimeSales = 0;

        lifeTimeSalesMeetings.filter((m: any) => {
            lifeTimeSales += parseFloat(m.insuranceCompany.contractPrice);
        });

        const averageCustomerLifetimeValue = (lifeTimeSales / uniqueClientCount.length);

        return averageCustomerLifetimeValue;
    }

    export async function updateAdminStatistics(adminStatistics: any) {
        const iAdminStatistics = await AdminStatistics.updateOne(
            {$set: adminStatistics}
        );
    }

    export async function getAdminStatistics(): Promise<IAdminStatistics> {
        const iAdminStatistics = await AdminStatistics.findOne();
        return iAdminStatistics;
    }

    export async function getAllCompletedSessionsCount(date: number): Promise<number> {
        const currentDate = new Date();

        let queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
            callingStatus: "COMPLETED"
        };

        if (date == 365) {
            const firstDateOfCurrentYear = new Date(currentDate.getFullYear(), 0, 1);

            queryConditions["createdAt"] = {$lte: currentDate, $gte: firstDateOfCurrentYear};
        } else if (date == 30) {
            const firstDayOfCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

            queryConditions["createdAt"] = {$lte: currentDate, $gte: firstDayOfCurrentMonth};
        } else {
            const firstDayOfCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

            var timeDiff = Math.abs(currentDate.getTime() - firstDayOfCurrentMonth.getTime());

            var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));

            if (diffDays > 7) {
                let oneWeekBefore = new Date(currentDate);

                oneWeekBefore.setDate(currentDate.getDate() - 6);

                queryConditions["createdAt"] = {$lte: currentDate, $gte: oneWeekBefore};
            } else {
                queryConditions["createdAt"] = {$lte: currentDate, $gte: firstDayOfCurrentMonth};
            }
        }

        const completedSessions = await Meeting.aggregate([
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {firstname: 1, lavniTestAccount: 1, lastname: 1, _id: 1},
                        },
                    ],
                    as: "clientId",
                },
            },
            {
                $unwind: {
                    path: "$clientId",
                },
            },
            {
                $match: {
                    $and: [queryConditions],
                },
            },
        ]);

        return completedSessions.length;
    }

    export async function getAllSubscribersCount(): Promise<number> {
        const subscribers = await User.find({
            subscriptionStatus: SubscriptionStatus.ACTIVE
        }, {new: true});
        return subscribers.length;
    }

    export async function getTeatmentHistoryNotesByAdmin(
        limit: number,
        offset: number,
        searchableClient: string,
        searchableTherapist: string,
        claimDate: string,
    ): Promise<IDiagnosisNote[]> {
        let searchedName = null;
        if (searchableClient) {
            let searchItem = searchableClient.replace(/\s/g, "");
            searchedName =
                searchableClient != null ? new RegExp(`^${searchItem}`, "i") : null;
        }

        let searchedName1 = null;
        if (searchableTherapist) {
            let searchItem = searchableTherapist.replace(/\s/g, "");
            searchedName1 =
                searchableTherapist != null ? new RegExp(`^${searchItem}`, "i") : null;
        }

        const queryConditions: any = {
            "clientId.lavniTestAccount": {$ne: true},
        };

        if (searchedName || searchedName1) {
            queryConditions[searchedName && searchedName1 ? "$and" : "$or"] = [
                {
                    $or: [
                        {"clientId.firstname": searchedName},
                        {"clientId.lastname": searchedName},
                    ]
                },
                {
                    $or: [
                        {"therapistId.firstname": searchedName1},
                        {"therapistId.lastname": searchedName1},
                    ]
                },
            ];
        }
        if (claimDate) {
            const claimDateObj = new Date(claimDate);
            const startDate = new Date(claimDateObj);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(claimDateObj);
            endDate.setHours(23, 59, 59, 999);

            queryConditions.updatedAt = {$gte: startDate, $lte: endDate};
        }
        const response = await DiagnosisNote.aggregate([
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "clientId",
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "therapistId",
                    foreignField: "_id",
                    as: "therapistId",
                },
            },
            {
                $unwind: {
                    path: "$clientId",
                },
            },
            {
                $unwind: {
                    path: "$therapistId",
                },
            },
            {
                $lookup: {
                    from: "meetings",
                    localField: "meetingId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                bothJoinedAt: 1,
                            },
                        },
                    ],
                    as: "meetingId",
                },
            },
            {
                $unwind: {
                    path: "$meetingId",
                },
            },
            {
                $match: {
                    $and: [queryConditions],
                },
            },
        ]).sort({updatedAt: -1})
            .skip(offset)
            .limit(limit);
        return response;
    }

    export async function updateDiagnosisNoteNotesSMS(diagnosisNoteId: Types.ObjectId, diagnosisDetails: any): Promise<IDiagnosisNote> {
        const response = await DiagnosisNote.findByIdAndUpdate({_id: diagnosisNoteId}, {$set: diagnosisDetails}, {new: true}).populate(
            populateOptions
        );
        return response;
    }


    export async function getPendingDiagnosisNoteNotesByAdmin(): Promise<IDiagnosisNote[]> {
        const now = new Date();
        const start = new Date(now.getTime() - (48 * 60 * 60 * 1000) - 15 * 60000);
        const end = new Date(now.getTime() - (48 * 60 * 60 * 1000) + 15 * 60000);
        const response = await DiagnosisNote.aggregate([
            {
                $match: {
                    createdAt: {$gte: start, $lte: end},
                    updatedByTherapist: false,
                },
            },
            {
                $project: {
                    clientId: 1,
                    therapistId: 1,
                    meetingId: 1,
                    clinicalReminderSMS: 1,
                    clinicalReminderEmail: 1,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                firstname: 1,
                                lastname: 1,
                                email: 1,
                                primaryPhone: 1,
                                reminderType: 1,
                                reminderTime: 1,
                                smsStop: 1,
                            },
                        },
                    ],
                    as: "clientId",
                },
            },
            {
                $unwind: {
                    path: "$clientId",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "therapistId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                firstname: 1,
                                lastname: 1,
                                email: 1,
                                primaryPhone: 1,
                                reminderType: 1,
                                reminderTime: 1,
                            },
                        },
                    ],
                    as: "therapistId",
                },
            },
            {
                $unwind: {
                    path: "$therapistId",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "meetings",
                    localField: "meetingId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                accepted: 1,
                                createdAt: 1,
                            },
                        },
                    ],
                    as: "meeting",
                },
            },
            {
                $unwind: "$meeting",
            },
            {
                $match: {
                    "meeting.accepted": true,
                    "meeting.callingStatus": "COMPLETED"
                },
            },
            {
                $sort: {createdAt: -1},
            },
        ]);
        return response;
    }


    export async function getDiagnosisNoteNotesByAdmin(noteId: StringOrObjectId): Promise<IDiagnosisNote> {
        const response = await DiagnosisNote.findById(noteId).populate(populateOptions2);
        return response;
    }

    export async function getClientsByAdmin(clientId: StringOrObjectId): Promise<IClient> {
        const response = await Client.findById(clientId);
        return response;
    }


    // pass due notes

    export async function getTreatmentHistoryByTherapistId(
        therapistId: Types.ObjectId,
        limit: number,
        offset: number,
    ): Promise<ITreatmentHistory[]> {
        try {
            const query = await TreatmentHistory.find({
                therapistId,
            })
                .skip(offset)
                .limit(limit)
                .exec();
            const treatmentHistory = await query;
            return treatmentHistory;
        } catch (error) {

            throw new Error(
                `Failed to retrieve treatment history by therapistId: ${error}`
            );
        }
    }

    export async function getTeatmentHistoryByAdmin(
        limit: number,
        offset: number,
        searchableClient: string,
        searchableTherapist: string,
        claimStatus: string,
        claimDate: string,
        insuranceCompany: string,
    ): Promise<ITreatmentHistory[]> {
        let searchedNameClient = null;
        if (searchableClient) {
            let searchItemForClient = searchableClient.replace(/\s/g, "");
            searchedNameClient =
                searchableClient != null ? new RegExp(`^${searchItemForClient}`, "i") : null;
        }

        let searchedNameTherapist = null;
        if (searchableTherapist) {
            let searchItemForTherapist = searchableTherapist.replace(/\s/g, "");
            searchedNameTherapist =
                searchableTherapist != null ? new RegExp(`^${searchItemForTherapist}`, "i") : null;
        }

        let clientNameQuery = {};

        if (searchedNameClient) {
            clientNameQuery = {
                $or: [
                    {'clientDetails.firstname': searchedNameClient},
                    {'clientDetails.lastname': searchedNameClient},
                    {'clientDetails.email': searchedNameClient},
                    {'clientDetails.fullName': searchedNameClient},
                ],
            };

        }

        let therapistNameQuery = {};

        if (searchedNameTherapist) {
            therapistNameQuery = {
                $or: [
                    {'therapistDetails.firstname': searchedNameTherapist},
                    {'therapistDetails.lastname': searchedNameTherapist},
                    {'therapistDetails.email': searchedNameTherapist},
                    {'therapistDetails.fullName': searchedNameTherapist},
                ],
            };
        }

        let insuranceCompanyQuery = {};

        if (insuranceCompany) {
            insuranceCompanyQuery = {
                'insuranceCompanyDetails.insuranceCompany': insuranceCompany
            };
        }

        const claimStatusQueryFilter: any = {};
        if (claimStatus) {
            if (claimStatus == "ACTIVE") {
                claimStatusQueryFilter.claimStatus = "ACTIVE";
            } else if (claimStatus == "ACTIVEMD") {
                claimStatusQueryFilter.claimStatus = "ACTIVEMD";
            } else if (claimStatus == "DUPLICATED") {
                claimStatusQueryFilter.claimStatus = "DUPLICATED";
            } else if (claimStatus == "PAID") {
                claimStatusQueryFilter.claimStatus = "PAID";
            } else if (claimStatus == "PAIDMD") {
                claimStatusQueryFilter.claimStatus = "PAIDMD";
            } else if (claimStatus == "UNPAID") {
                claimStatusQueryFilter.claimStatus = "UNPAID";
            } else if (claimStatus == "UNPAIDMD") {
                claimStatusQueryFilter.claimStatus = "UNPAIDMD";
            } else if (claimStatus == "IN_PROGRESS") {
                claimStatusQueryFilter.claimStatus = "IN_PROGRESS";
            } else if (claimStatus == "INACTIVE") {
                claimStatusQueryFilter.claimStatus = "INACTIVE";
            } else if (claimStatus == "PENDING_SUBMISSION") {
                claimStatusQueryFilter.claimStatus = "PENDING_SUBMISSION";
            } else if (claimStatus == "NOTSUBMITTED") {
                claimStatusQueryFilter.claimStatus = {$nin: ["ACTIVE", "INACTIVE", "ACTIVEMD", "PAID", "PAIDMD", "UNPAIDMD", "UNPAID", "IN_PROGRESS", "PENDING_SUBMISSION"]};
            }
        }

        const queryConditions: any = {
            isMeetingTranscribe: true,
            "clientInsuranceDetails._id": {$exists: true},
            "diagnosisNoteId.patientID": {$exists: true},
            "diagnosisNoteId.cptCode": {$ne: "00001"},
            "diagnosisNoteId.encounterID": {$exists: true},
            "clientDetails.lavniTestAccount": {$ne: true},
        };

        // if (searchedName || searchedName1 || insuranceCompany) {
        //   queryConditions[searchedName && searchedName1 && insuranceCompany ? "$and" : "$or"] = [
        //     {
        //       $or: [
        //         { "clientDetails.firstname": searchedName },
        //         { "clientDetails.lastname": searchedName },
        //       ]
        //     },
        //     {
        //       $or: [
        //         { "therapistDetails.firstname": searchedName1 },
        //         { "therapistDetails.lastname": searchedName1 },
        //       ]
        //     },
        //     {
        //       $or: [
        //         { "insuranceCompanyDetails.insuranceCompany": insuranceCompany },
        //       ]
        //     },
        //   ];
        // }

        if (claimDate) {
            const claimDateObj = new Date(claimDate);
            const startDate = new Date(claimDateObj);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(claimDateObj);
            endDate.setHours(23, 59, 59, 999);

            queryConditions.createdAt = {$gte: startDate, $lte: endDate};
        }
        const response = await TreatmentHistory.aggregate([
            {
                $project: {
                    clientId: 1,
                    therapistId: 1,
                    meetingId: 1,
                    diagnosisNoteId: 1,
                    claimStatus: 1,
                    errorMsg: 1,
                    isMeetingTranscribe: 1,
                    updatedAt: 1,
                    createdAt: 1,
                    cronjobCount: 1,
                    deleteTreatmentHistory: 1,
                    flag: 1,
                    meetingStartedTime: 1,
                    paidAmount: 1,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                firstname: 1,
                                lastname: 1,
                                email: 1,
                                lavniTestAccount: 1,
                                insuranceId: 1,
                                fullName: {
                                    $concat: ["$firstname", " ", "$lastname"],
                                }
                            },
                        },
                    ],
                    as: "clientDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "therapistId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                firstname: 1,
                                lastname: 1,
                                email: 1,
                                lavniTestAccount: 1,
                                fullName: {
                                    $concat: ["$firstname", " ", "$lastname"],
                                }
                            },
                        },
                    ],
                    as: "therapistDetails",
                },
            },
            {
                $unwind: {
                    path: "$therapistDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "insurances",
                    localField: "clientDetails.insuranceId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                insuranceCompanyId: 1,
                                subscriber: 1,
                                dependent: 1,
                            },
                        },
                    ],
                    as: "clientInsuranceDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientInsuranceDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "insurancecompanies",
                    localField: "clientInsuranceDetails.insuranceCompanyId",
                    foreignField: "_id",
                    as: "insuranceCompanyDetails"
                }
            },
            {
                $unwind: {
                    path: "$insuranceCompanyDetails",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "diagnosisnotes",
                    localField: "diagnosisNoteId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                patientID: 1,
                                cptCode: 1,
                                encounterID: 1,
                            },
                        },
                    ],
                    as: "diagnosisNoteId",
                },
            },
            {
                $unwind: {
                    path: "$diagnosisNoteId",
                },
            },
            {
                $lookup: {
                    from: "meetings",
                    localField: "meetingId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                sessionAmount: 1,
                                insuranceClaim: 1,
                                copayment: 1,
                            },
                        },
                    ],
                    as: "meetingId",
                },
            },
            {
                $unwind: {
                    path: "$meetingId",
                },
            },
            {
                $match: {
                    $and: [
                        queryConditions,
                        claimStatusQueryFilter,
                        insuranceCompanyQuery,
                        therapistNameQuery,
                        clientNameQuery
                    ],
                },
            },
        ]).sort({createdAt: -1})
            .skip(offset)
            .limit(limit);
        return response;
    }

    export async function getInsurancePlanById(
        insuranceId: Types.ObjectId
    ): Promise<IInsurance> {
        const response = await Insurance.findById(insuranceId).populate(
            populateOptions
        );
        return response;
    }

    export async function getTeatmentHistoryByAdminWithInsurance(searchResult: ITreatmentHistory[], desiredInsuranceCompany: string): Promise<{
        claim: ITreatmentHistory;
        insuranceCompany: String
    }[]> {
        const claimsWithInsurance: {
            claim: ITreatmentHistory; insuranceCompany: String,
        }[] = [];

        for (const claim of searchResult) {
            const populatedTherapist = await TreatmentHistory.findById(claim._id).populate(
                [{path: "clientId"}, {path: "therapistId"}, {path: "meetingId"}]
            );
            const client = await Client.findById(claim?.clientId);
            const insurance = await getInsurancePlanById(client.insuranceId);
            const isFound = await AdminDao.getInsuranceCompanyById(insurance?.insuranceCompanyId);
            claimsWithInsurance.push({claim: populatedTherapist, insuranceCompany: isFound.insuranceCompany});
        }
        if (desiredInsuranceCompany) {
            const filteredClaims = claimsWithInsurance.filter(item => item.insuranceCompany === desiredInsuranceCompany);
            return filteredClaims;
        } else {
            return claimsWithInsurance;
        }

    }

    function getBusinessDaysAgo(daysAgo: number) {
        const today = new Date();
        let businessDays = 0;

        while (businessDays < daysAgo) {
            today.setDate(today.getDate() - 1);

            if (today.getDay() >= 1 && today.getDay() <= 5) {
                businessDays++;
            }
        }

        return today;
    }

    export async function getSuccessTeatmentHistoryByAdmin(): Promise<ITreatmentHistory[]> {

        const date = getBusinessDaysAgo(7);
        const response = await TreatmentHistory.aggregate([
            {
                $project: {
                    clientId: 1,
                    therapistId: 1,
                    meetingId: 1,
                    diagnosisNoteId: 1,
                    claimStatus: 1,
                    errorMsg: 1,
                    isMeetingTranscribe: 1,
                    updatedAt: 1,
                    createdAt: 1,
                    cronjobCount: 1,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                firstname: 1,
                                lastname: 1,
                                email: 1,
                                lavniTestAccount: 1,
                                insuranceId: 1,
                            },
                        },
                    ],
                    as: "clientDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "therapistId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                firstname: 1,
                                lastname: 1,
                                email: 1,
                                lavniTestAccount: 1,
                            },
                        },
                    ],
                    as: "therapistDetails",
                },
            },
            {
                $unwind: {
                    path: "$therapistDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "insurances",
                    localField: "clientDetails.insuranceId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                insuranceCompanyId: 1,
                                subscriber: 1,
                                dependent: 1,
                            },
                        },
                    ],
                    as: "clientInsuranceDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientInsuranceDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "diagnosisnotes",
                    localField: "diagnosisNoteId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                patientID: 1,
                                encounterID: 1,
                            },
                        },
                    ],
                    as: "diagnosisNoteDetails",
                },
            },
            {
                $unwind: {
                    path: "$diagnosisNoteDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "meetings",
                    localField: "meetingId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                sessionAmount: 1,
                                insuranceClaim: 1,
                                copayment: 1,
                            },
                        },
                    ],
                    as: "meetingId",
                },
            },
            {
                $unwind: {
                    path: "$meetingId",
                },
            },
            {
                $match: {
                    claimStatus: 'ACTIVE',
                    errorMsg: {$eq: null},
                    createdAt: {
                        $lt: date,
                    }
                },
            },
        ]);
        return response;
    }

    export async function getSuccessTeatmentHistoryByAdminTest(
        limit: number,
        offset: number,
    ): Promise<ITreatmentHistory[]> {

        const response = await TreatmentHistory.aggregate([
            {
                $project: {
                    clientId: 1,
                    therapistId: 1,
                    meetingId: 1,
                    diagnosisNoteId: 1,
                    claimStatus: 1,
                    errorMsg: 1,
                    isMeetingTranscribe: 1,
                    updatedAt: 1,
                    createdAt: 1,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                firstname: 1,
                                lastname: 1,
                                email: 1,
                                lavniTestAccount: 1,
                                insuranceId: 1,
                            },
                        },
                    ],
                    as: "clientDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "therapistId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                firstname: 1,
                                lastname: 1,
                                email: 1,
                                lavniTestAccount: 1,
                            },
                        },
                    ],
                    as: "therapistDetails",
                },
            },
            {
                $unwind: {
                    path: "$therapistDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "insurances",
                    localField: "clientDetails.insuranceId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                insuranceCompanyId: 1,
                                subscriber: 1,
                                dependent: 1,
                            },
                        },
                    ],
                    as: "clientInsuranceDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientInsuranceDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "insurancecompanies",
                    localField: "clientInsuranceDetails.insuranceCompanyId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                insuranceCompany: 1,
                                contractPrice: 1,
                                coPayment: 1,
                            },
                        },
                    ],
                    as: "clientInsuranceCompanyDetails",
                },
            },
            {
                $unwind: {
                    path: "$clientInsuranceCompanyDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "diagnosisnotes",
                    localField: "diagnosisNoteId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                patientID: 1,
                                encounterID: 1,
                            },
                        },
                    ],
                    as: "diagnosisNoteDetails",
                },
            },
            {
                $unwind: {
                    path: "$diagnosisNoteDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "meetings",
                    localField: "meetingId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                sessionAmount: 1,
                                insuranceClaim: 1,
                                copayment: 1,
                            },
                        },
                    ],
                    as: "meetingId",
                },
            },
            {
                $unwind: {
                    path: "$meetingId",
                },
            },
            {
                $match: {
                    claimStatus: 'ACTIVE',
                    errorMsg: {$eq: null}
                },
            },
        ]).sort({createdAt: -1})
            .skip(limit * (offset - 1))
            .limit(limit);
        return response;
    }

    export async function deleteTreatmentHistoryByAdmin(
        noteId: StringOrObjectId,
        data: Partial<DTreatmentHistory>
    ): Promise<ITreatmentHistory> {
        const updatedDiagnosis = await TreatmentHistory.findByIdAndUpdate(
            noteId,
            {$set: data},
            {new: true}
        );
        return updatedDiagnosis;
    }

    export async function updateTreatmentHistoryClaimStatusByAdmin(
        noteId: StringOrObjectId,
        data: Partial<DTreatmentHistory>
    ): Promise<ITreatmentHistory> {

        const updatedDiagnosis = await TreatmentHistory.findByIdAndUpdate(
            noteId,
            {$set: data},
            {new: true}
        );

        return updatedDiagnosis;
    }

    export async function removeTreatmentHistoryFlag(
        noteId: StringOrObjectId,
    ): Promise<ITreatmentHistory> {

        try {

            const updatedDiagnosis = await TreatmentHistory.findByIdAndUpdate(
                noteId,
                {flag: false},
                {new: true}
            );

            return updatedDiagnosis;
        } catch (error) {
            console.error("Error while updating treatment history flag:", error);
            return null;
        }
    }

    export async function updateFlagAlltreatmentHistories(): Promise<ITreatmentHistory[]> {
        try {
            const duplicateTreatments = await TreatmentHistory.aggregate([
                {
                    $lookup: {
                        from: "diagnosisnotes",
                        localField: "diagnosisNoteId",
                        foreignField: "_id",
                        as: "diagnosisNotes"
                    }
                },
                {
                    $match: {
                        "diagnosisNotes.updatedByTherapist": true
                    }
                },
                {
                    $group: {
                        _id: {
                            clientId: "$clientId",
                            therapistId: "$therapistId",
                            meetingStartedTime: {$dateToString: {format: "%Y-%m-%d", date: "$meetingStartedTime"}}
                        },
                        count: {$sum: 1},
                        treatmentIds: {$push: "$_id"},
                    }
                },
                {
                    $match: {
                        count: {$gt: 1},
                    }
                }
            ]).exec();

            const filteredTreatmentIds = duplicateTreatments?.map((result: any) => result.treatmentIds).flat();

            const treatmentHistoriesWithoutFlag = await TreatmentHistory.find({
                _id: {$in: filteredTreatmentIds}
            });

            const filteredArr = treatmentHistoriesWithoutFlag?.filter((item: ITreatmentHistory) => !item.flag);
            const newArrListIds = filteredArr?.map((obj: ITreatmentHistory) => obj._id);

            if (newArrListIds) {
                await TreatmentHistory.updateMany({_id: {$in: newArrListIds}}, {flag: true});
            }

            const uniqueTreatments = await TreatmentHistory.aggregate([
                {
                    $lookup: {
                        from: "diagnosisnotes",
                        localField: "diagnosisNoteId",
                        foreignField: "_id",
                        as: "diagnosisNotes"
                    }
                },
                {
                    $match: {
                        "diagnosisNotes.updatedByTherapist": true
                    }
                },
                {
                    $group: {
                        _id: {
                            clientId: "$clientId",
                            therapistId: "$therapistId",
                            createdAt: {$dateToString: {format: "%Y-%m-%d", date: "$createdAt"}}
                        },
                        count: {$sum: 1},
                        treatmentIds: {$push: "$_id"}
                    }
                },
                {
                    $match: {
                        count: 1
                    }
                }
            ]).exec();

            const uniqueFilteredTreatmentIds = uniqueTreatments?.map((result: any) => result.treatmentIds).flat();

            const uniqueTreatmentHistoriesWithoutFlag = await TreatmentHistory.find({
                _id: {$in: uniqueFilteredTreatmentIds}
            });

            const treatmentHistoriesWithoutFlag2 = uniqueTreatmentHistoriesWithoutFlag?.filter((history: any) => history.flag === undefined);

            const newArrListIdsList = treatmentHistoriesWithoutFlag2?.map((obj: ITreatmentHistory) => obj._id);

            if (newArrListIdsList) {
                await TreatmentHistory.updateMany({_id: {$in: newArrListIdsList}}, {flag: false});
            }

            const allTreatmentHistories = await TreatmentHistory.find();

            return allTreatmentHistories;
        } catch (error) {
            console.error('Error retrieving and updating treatment histories:', error);
            throw error;
        }
    }

    export async function updateAdminStatisticsDataHourly(data: {
        type: string;
        pastMissedAppointmentsCount: number;
        scheduledAppointmentsCount: number;
        completedSessions: number;
        yearlyRecurringRevenueCount: string;
        monthlyRecurringRevenueCount: string;
        lifeTimeSales: string;
        averageCustomerLifetimeValue: string
    }) {
        try {
            const updateAdminStatisticsHourly = await AdminStatisticsHourly.findOneAndUpdate(
                {type: data.type},
                {$set: data},
                {new: true, upsert: true}
            );
            return updateAdminStatisticsHourly;
        } catch (e) {
            AppLogger.error(`Error retrieving and updating hourly statistic. Error details: ' ${e}`);
            throw e;
        }
    }
}