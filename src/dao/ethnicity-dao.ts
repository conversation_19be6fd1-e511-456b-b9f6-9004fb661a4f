import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";
import { IEthnicity } from "../models/ethnicity-model";
import Ethnicity from "../schemas/ethnicity-schema";

export namespace EthnicityDao {
    export async function getEthnicityById(id:StringOrObjectId):Promise<IEthnicity> {
        const response = await Ethnicity.findById(id);
        return response;
    }

    export async function getAllEthnicityTypes():Promise<IEthnicity[]> {
        const response = await Ethnicity.find();
        return response;
    }

    export async function getEthnicityByName(name:string):Promise<IEthnicity>{
        const response = await Ethnicity.findOne({ethnicity:name});
        return response;
    }
}