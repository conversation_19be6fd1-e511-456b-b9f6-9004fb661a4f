import { App<PERSON>ogger } from "../common/logging";
import { DAdmin, IAdmin } from "../models/admin-model";
import Admin from "../schemas/admin-schema";
import { UserDao } from "./user-dao";
import { <PERSON><PERSON>ser, IUser, UserRole } from "../models/user-model";
import { DEthnicity, IEthnicity } from "../models/ethnicity-model";
import Ethnicity from "../schemas/ethnicity-schema";
import { DExperienceTag, IExperienceTag } from "../models/experience-tag-model";
import ExperienceTag from "../schemas/experience-tag-schema";
import { DProfession, IProfession } from "../models/profession-model";
import Profession from "../schemas/profession-schema";
import { StringOrObjectId } from "../common/util";
import { DHashtag, IHashtag } from "../models/hashtag-model";
import Hashtag from "../schemas/hashtag-schema";
import { IClient, PremiumStatus } from "../models/client-model";
import Client from "../schemas/client-schema";
import { ITherapist } from "../models/therapist-model";
import Therapist from "../schemas/therapist-schema";
import { Types } from "mongoose";
import * as mongoose from "mongoose";
import {
  DProfessionLicense,
  IProfessionLicense,
} from "../models/profession-license-model";
import ProfessionLicense from "../schemas/profession-license-schema";
import User from "../schemas/user-schema";
import { IContact } from "../models/contact-us-model";
import Contact from "../schemas/contact-us-schema";
import { DEducation, IEducation } from "../models/education-model";
import Education from "../schemas/education-schema";
import License from "../schemas/license-schema";
import { DLicense, ILicense } from "../models/license-model";
import { IArticle } from "../models/article-model";
import Article from "../schemas/article-schema";
import { AppointmentStatus, IAppointment } from "../models/appointment-model";
import Appointment from "../schemas/appointment-schema";
import {
  DInsuranceCompany,
  IInsuranceCompany,
} from "../models/Insurance-company-model";
import InsuranceCompany from "../schemas/Insurance-company-schema";
import { ITreatmentHistory } from "../models/treatment-history-model";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import { CallingStatus, IMeeting } from "../models/meeting-model";
import Meeting from "../schemas/meeting-schema";
import { IMarketingEmail } from "../models/marketing-email-model";
import MarketingEmail from "../schemas/marketing-email-schema";
import CustomerReview from "../schemas/customer-reviews-schema";
import {
  DCustomerReview,
  ICustomerReview,
} from "../models/customer-reviews-model";
import { Review, ReviewStatus } from "../models/sub-models/review-model";
import { ObjectId } from 'mongoose';
import FriendRequest from "../schemas/friend-request-schema";
import { FriendRequestStatus, IFriendRequest } from "../models/friend-request-model";
import MonthlyPayment from "../schemas/monthly-payment-schema";
import { IMonthlyPayment } from "../models/monthly-payment-model";
import Transaction from "../schemas/transaction-schema";
import { ITransaction } from "../models/transaction-model";
import { startOfDay, endOfDay } from 'date-fns';
import DiagnosisNote from "../schemas/diagnosis-note-schema";
import { DReminderSMS, IReminderSMS } from "../models/reminder-sms-model";
import ReminderSMS from "../schemas/reminder-sms-schema";
import { DUnsubmittedClaimMd, IUnsubmittedClaimMd } from "../models/unsubmitted-claim-model";
import UnsubmittedClaimMd from "../schemas/unsubmitted-claim-schema";
import LavniReview from "../schemas/lavni-review-schema";
import { DLavniReview, ILavniReview, LavniReviewStatus } from "../models/lavni-review-model";
import { DERAClaimMdSchema, IERAClaimMdSchema } from "../models/era-claim-model";
import ERAClaimMd, { ERAClaimMdSchema } from "../schemas/era-claims-schema";
import { DERAClaimMdListSchema, IERAClaimMdListSchema } from "../models/era-claim-list-model";
import ERAClaimMdList from "../schemas/era-claims-list-schema";
const moment = require('moment-timezone');
import Notification from "../schemas/notification-schema";
import { NotificationEvent } from "../models/notification-model";
import TherapistReferral from "../schemas/therapist-referral-schema";
import ReferralEarning from "../schemas/referral-earning-schema";
import { IReferralEarning } from "../models/referral-earning-model";
import ClientReward from "../schemas/client-reward-schema";
import { IClientReward } from "../models/client-reward-model";
import { DInsuranceDocApproval, IInsuranceDocApproval, SubmissionApprovalStatus } from "../models/insurance-doc-approval-model";
import InsuranceDocApproval from "../schemas/insurance-doc-approval-schema";
import { AddStateResponse, DState, AddStateResponseTwo } from "../models/state-model";
import State from "../schemas/state-schema";

interface AppointmentTherapist {
  _id: any;
  firstname: string;
}

export namespace AdminDao {
  export async function getAllTreatmentHistoryByTherapistId(
    therapistId: Types.ObjectId | string
  ): Promise<ITreatmentHistory[]> {
    try {
      const treatmentHistory = await TreatmentHistory.find({
        therapistId: new Types.ObjectId(therapistId)
      })
      .sort({ createdAt: -1 })
      .populate({
        path: "clientId",
        select: "firstname lastname email",
        populate: {
          path: "insuranceId",
          select: "insuranceCompanyId",
          populate: {
            path: "insuranceCompanyId",
            select: "insuranceCompany"
          }
        }
      })
      .populate("therapistId", "firstname lastname email");
      
      return treatmentHistory;
    } catch (error) {
      throw error;
    }
  }

  const populateOptions2 = [
    {
      path: "clientId",
      select: {
        firstname: 1,
        lastname: 1,
        photoId: 1,
        email: 1,
        gender: 1,
        ethnicityId: 1,
        primaryPhone: 1,
        subscriptionId: 1,
        subscriptionStatus: 1,
        premiumStatus: 1,
      },
      populate: [
        { path: "photoId", model: "Upload" },
      ],
    },
  ];

  const populateOptions3 = [
    {
      path: "therapistId",
      select: {
        firstname: 1,
        lastname: 1,
        photoId: 1,
        email: 1,
        gender: 1,
        ethnicityId: 1,
        primaryPhone: 1,
        subscriptionId: 1,
        subscriptionStatus: 1,
        premiumStatus: 1,
      },
      populate: [
        { path: "photoId", model: "Upload" },
      ],
    },
  ];


  const populateOptions = ["coverPhoto", "profilePhoto"];

  export async function getAllAdmins(): Promise<IAdmin[]> {
    const admins = await Admin.find().populate(populateOptions);
    AppLogger.info(`Got all admins, total: ${admins.length}`);
    return admins;
  }

  export async function getAdminByEmail(email: string): Promise<IUser | null> {
    let admin: IUser = await Admin.findOne({ email: email });
    AppLogger.info(
      `Got admin for email, userID: ${admin ? admin._id : "None"}`
    );
    return admin;
  }

  export async function getAdmin(adminId: string): Promise<IAdmin> {
    const admin = await Admin.findById(adminId).populate(populateOptions);
    AppLogger.info(`Got admin for ID: ${adminId}`);
    return admin;
  }

  export async function createAdmin(data: DAdmin): Promise<string> {
    const iAdmin = new Admin(data);
    let admin = await iAdmin.save();
    AppLogger.info(`Create profile for user ID: ${admin._id}`);
    return await UserDao.authenticateUser(data.email, data.password);
  }

  export async function updateAdmin(
    adminId: string,
    data: Partial<DAdmin>
  ): Promise<IAdmin> {
    const admin = await Admin.findByIdAndUpdate(adminId, { $set: data });
    AppLogger.info(`Update profile for user ID: ${admin._id}`);
    return getAdmin(admin._id);
  }

  export async function getPendingSubmissionTreatmentHistoryByTherapistAndClientIds(
    therapistId: Types.ObjectId,
    clientIds: string[]
  ): Promise<ITreatmentHistory[]> {
    try {
      const query = await TreatmentHistory.find({
        therapistId: therapistId,
        claimStatus: "PENDING_SUBMISSION",
        clientId: { $in: clientIds.map(id => new Types.ObjectId(id)) },
      }).populate({
        path: 'clientId',
        select: '_id insuranceId',
        populate: {
          path: 'insuranceId',
          // model: 'Insurance',
          populate: {
            path: 'insuranceCompanyId',
            model: 'InsuranceCompany',
          }
        }
      })
        .populate({
          path: 'therapistId',
          select: '_id payRate nPI1 taxonomyCode firstname lastname',
        })
        .populate({
          path: 'diagnosisNoteId',
          select: '_id cptCode secondaryDiagnosisICDcodes diagnosisICDcodes',
        })
        .populate({
          path: 'meetingId',
          select: '_id',
        })

      const treatmentHistory = await query;
      return treatmentHistory;
    } catch (error) {

      throw new Error(
        `Failed to retrieve treatment history by therapistId: ${error}`
      );
    }
  }

  export async function getEthnicityByType(
    ethnicity: any
  ): Promise<IEthnicity> {
    const ethnicityType = await Ethnicity.findOne({
      ethnicity: { $regex: ethnicity, $options: "i" },
    });
    return ethnicityType;
  }

  export async function getEthnicityById(
    id: StringOrObjectId
  ): Promise<IEthnicity> {
    let ethnicity: IEthnicity = await Ethnicity.findById(id);

    return ethnicity;
  }

  export async function addEthnicity(
    ethnicity: DEthnicity
  ): Promise<IEthnicity> {
    const iEthnicity = new Ethnicity({ ethnicity });
    let ethnicityType = await iEthnicity.save();
    AppLogger.info(`Created ethnicity. ID: ${ethnicityType._id}`);
    return ethnicityType;
  }

  export async function updateEthnicity(
    ethnicityId: StringOrObjectId,
    data: Partial<DEthnicity>
  ): Promise<IEthnicity> {
    const updatedEthnicity = await Ethnicity.findByIdAndUpdate(
      ethnicityId,
      { $set: data },
      { new: true }
    );

    return updatedEthnicity;
  }

  export async function getAllEthnicityTypes(
    limit?: number,
    offset?: number
  ): Promise<IEthnicity[]> {
    const ethnicityTypes = await Ethnicity.find()
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return ethnicityTypes;
  }

  export async function getAllEthnicityTypes_(): Promise<IEthnicity[]> {
    const ethnicityTypes = await Ethnicity.find().sort({ createdAt: -1 });
    return ethnicityTypes;
  }

  export async function getAllEthnicitysCount(): Promise<any> {
    const ethnicityTypes = await Ethnicity.find();
    return ethnicityTypes.length;
  }

  export async function deleteEthnicity(
    ethnicityId: StringOrObjectId
  ): Promise<IEthnicity> {
    const response = await Ethnicity.findByIdAndDelete(ethnicityId);

    return response;
  }
  export async function getExperienceTag(
    expTag: string
  ): Promise<IExperienceTag> {
    const experienceTag = await ExperienceTag.findOne({
      experienceTag: { $regex: `^${expTag}`, $options: "i" },
    });
    return experienceTag;
  }

  export async function addExperienceTag(
    expTag: string
  ): Promise<IExperienceTag> {
    const iExperienceTag = new ExperienceTag({ experienceTag: expTag });
    let experienceTag = await iExperienceTag.save();
    AppLogger.info(`Created exp tag. ID: ${experienceTag._id}`);
    return experienceTag;
  }

  export async function getAllExperienceTags(
    limit?: number,
    offset?: number
  ): Promise<IExperienceTag[]> {
    const expTagList = await ExperienceTag.find()
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return expTagList;
  }

  export async function getExperienceTagById(
    id: StringOrObjectId
  ): Promise<IExperienceTag> {
    const expTag = await ExperienceTag.findById(id);
    return expTag;
  }

  export async function deleteExperienceTag(
    id: StringOrObjectId
  ): Promise<IExperienceTag> {
    const response = await ExperienceTag.findByIdAndDelete(id);

    return response;
  }

  export async function addProfession(
    professionDetails: DProfession
  ): Promise<IProfession> {
    const iProfession = new Profession(professionDetails);
    let newProfession = await iProfession.save();
    AppLogger.info(`Created profession. ID: ${newProfession._id}`);
    return newProfession;
  }

  export async function updateProfession(id: any, data: Partial<DProfession>) {
    const profession = await Profession.findByIdAndUpdate(id, data, {
      new: true,
    });
    AppLogger.info(`Update profession of profession ID: ${profession._id}`);
    return profession;
  }

  export async function getProfessionId(
    id: StringOrObjectId
  ): Promise<IProfession> {
    const profession = await Profession.findById(id);
    return profession;
  }

  export async function deleteProfession(
    id: StringOrObjectId
  ): Promise<IProfession> {
    const response = await Profession.findByIdAndDelete(id);
    return response;
  }

  export async function addProfessionLicense(
    subProfessionDetails: DProfessionLicense
  ): Promise<IProfessionLicense> {
    const iSubProfession = new ProfessionLicense(subProfessionDetails);
    let newSubProfession = await iSubProfession.save();
    AppLogger.info(`Created profession license. ID: ${newSubProfession._id}`);
    return newSubProfession;
  }

  export async function getProfessionByName(
    profession: string
  ): Promise<IProfession> {
    const response = await Profession.findOne({
      name: { $regex: `^${profession}`, $options: "i" },
    });
    return response;
  }

  export async function getSubProfessionByName(
    subProfession: string
  ): Promise<IProfessionLicense> {
    const response = await ProfessionLicense.findOne({
      name: { $regex: `^${subProfession}`, $options: "i" },
    });
    return response;
  }

  export async function getAllProfessions(
    limit?: number,
    offset?: number
  ): Promise<IProfession[]> {
    const professionList = await Profession.find()
    .sort({ createdAt: -1 })
    .skip(limit * (offset - 1))
    .limit(limit);
    return professionList;
  }

  export async function getAllProfessionsPublic(): Promise<IProfession[]> {
    const professionList = await Profession.find().sort({ createdAt: -1 });
    return professionList;
  }

  export async function getProfessionById(id: any): Promise<IProfession> {
    const profession = await Profession.findById(id);
    return profession;
  }

  export async function getAllProfessionLicens(): Promise<
    IProfessionLicense[]
  > {
    const subProfessionList = await ProfessionLicense.find();
    return subProfessionList;
  }

  export async function getProfessionLicenseId(
    id: StringOrObjectId
  ): Promise<IProfessionLicense> {
    const professionLicense = await ProfessionLicense.findById(id);
    return professionLicense;
  }

  export async function getProfessionLicenseByProfessionId(
    id: StringOrObjectId
  ): Promise<IProfessionLicense[]> {
    const professionLicense = await ProfessionLicense.find({
      professionId: id,
    });
    return professionLicense;
  }

  export async function deleteProfessionLicense(
    id: StringOrObjectId
  ): Promise<IProfessionLicense> {
    const response = await ProfessionLicense.findByIdAndDelete(id);

    return response;
  }

  export async function getHashTagByName(hashTag: string): Promise<IHashtag> {
    const response = await Hashtag.findOne({
      name: { $regex: `^${hashTag}`, $options: "i" },
    });
    return response;
  }

  export async function getExperienceTagsByName(
    expTag: string
  ): Promise<IExperienceTag> {
    const response = await ExperienceTag.findOne({
      experienceTag: { $regex: `^${expTag}`, $options: "i" },
    });
    return response;
  }

  export async function getHashTagById(
    id: StringOrObjectId
  ): Promise<IHashtag> {
    const hashTag = await Hashtag.findById(id);
    return hashTag;
  }

  export async function deleteHashTag(id: StringOrObjectId): Promise<IHashtag> {
    const response = await Hashtag.findByIdAndDelete(id);

    return response;
  }

  export async function addHashtag(hashTag: DHashtag): Promise<IHashtag> {
    const iHashtag = new Hashtag(hashTag);
    let newHashTag = await iHashtag.save();
    AppLogger.info(`Created profession. ID: ${newHashTag._id}`);
    return newHashTag;
  }

  export async function getAllHashTags(
    limit?: number,
    offset?: number
  ): Promise<IHashtag[]> {
    const hashTagList = await Hashtag.find()
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return hashTagList;
  }

  export async function getAllPendingClients(
    limit?: number,
    offset?: number
  ): Promise<IClient[]> {
    const clientList = await Client.find({ adminApproved: false })
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return clientList;
  }

  export async function getAllApprovedClients(
    limit?: number,
    offset?: number
  ): Promise<IClient[]> {
    const clientList = await Client.find({ adminApproved: true })
      .skip(offset)
      .limit(limit);
    return clientList;
  }

  export async function approveRejectClient(
    clientId: Types.ObjectId,
    status: string
  ): Promise<IClient> {
    const client = await Client.findByIdAndUpdate(
      clientId,
      { adminApproved: Boolean(status) },
      { new: true }
    );

    AppLogger.info(`Update client for client Id: ${clientId}`);
    return client;
  }

  export async function getAllPendingTherapists(
    limit?: number,
    offset?: number
  ): Promise<ITherapist[]> {
    const therapistList = await Therapist.find({ adminApproved: false })
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return therapistList;
  }

  export async function getAllApprovedTherapists(
    limit?: number,
    offset?: number
  ): Promise<ITherapist[]> {
    const therapistList = await Therapist.find({ adminApproved: true })
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return therapistList;
  }

  export async function approveRejectTherapist(
    therapistId: Types.ObjectId,
    status: string
  ): Promise<ITherapist> {
    const therapist = await Therapist.findByIdAndUpdate(
      therapistId,
      { adminApproved: Boolean(status) },
      { new: true }
    );

    AppLogger.info(`Update therapist for therapist Id: ${therapistId}`);
    return therapist;
  }

  export async function getAllClients(
    limit?: number,
    offset?: number
  ): Promise<IClient[]> {
    if (limit == -1) {
      const clientsList = await Client.find({}, { email: 1 });

      return clientsList;
    } else {
      const clientsList = await Client.find()
        .sort({ createdAt: -1 })
        .skip(limit * (offset - 1))
        .limit(limit);

      return clientsList;
    }
  }

  export async function getAllAdminUsers(
    limit?: number,
    offset?: number
  ): Promise<IUser[]> {
    const adminLists = await User.find({ role: { $in: [UserRole.SUPER_ADMIN, UserRole.SUB_ADMIN] } })
      .sort({ createdAt: -1 })
      .select({ password: 0 })
      .skip(offset)
      .limit(limit);
    return adminLists;
  }

  export async function getAllClientsSeed(): Promise<IClient[]> {
    const clientsList = await Client.find().sort({ createdAt: -1 });

    return clientsList;
  }

  export async function getAllTherapists(
    limit?: number,
    offset?: number
  ): Promise<ITherapist[]> {
    const therapistList = await Therapist.find()
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return therapistList;
  }

  export async function getAllTherapistsSimple(): Promise<any[]> {
    const therapists = await Therapist.find({ role: UserRole.THERAPIST })
      .select("_id firstname lastname")
      .lean();

    const formattedTherapists = therapists.map(t => ({
      _id: t._id.toString(),
      name: `${t.firstname || ""} ${t.lastname || ""}`.trim()
    }));

    return formattedTherapists;
  }

  export async function getAllClientsCount(): Promise<number | any> {
    const response = await Client.find().countDocuments();
    return response;
  }

  export async function getAllPriorityNumbers(): Promise<number[] | any> {
    try {
      const therapists = await User.find({ priorityNumber: { $exists: true } }, 'priorityNumber').lean();

      if (therapists.length > 0) {
        const numbers = therapists
          .map((therapist) => therapist.priorityNumber)
          .filter((number) => number !== undefined);

        return numbers;
      } else {
        return [];
      }
    } catch (error) {
      return { error: 'Error fetching priority numbers' };
    }
  }

  export async function getAllTherapistsCount(): Promise<number | any> {
    const response = await Therapist.find().countDocuments();
    return response;
  }

  export async function getAllReferralEarningsCount(): Promise<number | any> {
    const response = await TherapistReferral.find().countDocuments();
    return response;
  }

  export async function getAllClientRewardsCount(): Promise<number | any> {
    const response = await ClientReward.find().countDocuments();
    return response;
  }

  export async function viewProfileByUserId(
    userId: StringOrObjectId
  ): Promise<IUser> {
    const userDetails = await User.findById(userId)
      .populate([
        { path: "photoId" },
        { path: "coverPhotoId" },
        { path: "videoId" },
        { path: "profession" },
        { path: "disclosureStatementId" },
        { path: "insuranceCompanies" },
        { path: "professionLicense" },
        { path: "likedTherapists", populate: { path: "photoId" } },
        { path: "likedTherapists", populate: { path: "experiencedIn" } },
        { path: "experiencedIn" },
        { path: "ethnicityId", select: { ethnicity: 1 } },
        { path: "friendRequests" },
        { path: "preference", populate: { path: "experiencedIn" } },
        {
          path: "friendRequests",
          select: { status: 1, clientId: 1, therapistId: 1 },
          populate: [
            {
              path: "clientId",
              select: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                friendRequests: 0,
              },
            },
            {
              path: "therapistId",
              select: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                friendRequests: 0,
              },
            },
          ],
        },
      ])
      .select({ password: 0 });
    return userDetails;
  }

  export async function updateUser(
    userId: StringOrObjectId,
    data: Partial<DUser>
  ): Promise<IUser> {
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: data },
      { new: true }
    );
    return updatedUser;
  }

  export async function createOrUpdateClientReviewByAdmin(
    clientId: StringOrObjectId,
    data: Partial<DLavniReview>
  ): Promise<ILavniReview> {
    const review = await LavniReview.findOneAndUpdate(
      { clientId: clientId },
      { $set: data },
      { new: true, upsert: true }
    );
    return review;
  }

  export async function createOrUpdateTherapistReviewByAdmin(
    therapistId: StringOrObjectId,
    data: Partial<DLavniReview>
  ): Promise<ILavniReview> {
    const review = await LavniReview.findOneAndUpdate(
      { therapistId: therapistId },
      { $set: data },
      { new: true, upsert: true }
    );
    return review;
  }

  export async function getClientReview(
    clientId: StringOrObjectId
  ): Promise<ILavniReview> {
    const response = await LavniReview.findOne({ clientId: clientId });
    return response;
  }

  export async function getTherapistReview(
    therapistId: StringOrObjectId
  ): Promise<ILavniReview> {
    const response = await LavniReview.findOne({ therapistId: therapistId });
    return response;
  }

  export async function getPendingCopaymentsAndUpdate(
    userId: Types.ObjectId,
    copaymentAmount: any,
    previousCopaymentAmount: any,
  ): Promise<IMeeting[]> {
    const response = await Meeting.aggregate([
      {
        $match: {
          clientId: userId,
          'copayment.status': { $in: ["NOTSUBMITTED", "UNPAID"] },
          'copayment.amount': { $gt: 0 }
        },
      }
    ]);
    if (copaymentAmount > previousCopaymentAmount) {
      await Meeting.updateMany(
        {
          clientId: userId,
          'copayment.status': { $in: ["NOTSUBMITTED", "UNPAID"] },
        },
        {
          $set: { 'copayment.amount': copaymentAmount, 'copayment.status': "PAID" }
        }
      );
    } else {
      await Meeting.updateMany(
        {
          clientId: userId,
          'copayment.status': { $in: ["NOTSUBMITTED", "UNPAID"] },
        },
        {
          $set: { 'copayment.amount': copaymentAmount }
        }
      );
    }
    return response;
  }

  export async function getAllContactUsRequests(
    limit?: number,
    offset?: number
  ): Promise<IContact[]> {
    const contactUsRequestList = await Contact.find()
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return contactUsRequestList;
  }

  export async function getAllContactCount(): Promise<number> {
    const response = await Contact.find({ isRead: false }).countDocuments();
    return response;
  }

  export async function getAllPendingEducationalDetails(
    limit?: number,
    offset?: number
  ): Promise<IEducation[]> {
    const educationList = await Education.find({
      reviewStatus: "PENDING",
    })
      .skip(offset)
      .limit(limit)
      .populate([
        { path: "userId", select: { firstname: 1, lastname: 1 } },
        { path: "uploadId" },
      ]);
    return educationList;
  }

  export async function getAllApprovedEducationalDetails(
    limit?: number,
    offset?: number
  ): Promise<IEducation[]> {
    const educationList = await Education.find({
      reviewStatus: "APPROVED",
    })
      .skip(offset)
      .limit(limit)
      .populate([
        { path: "userId", select: { firstname: 1, lastname: 1 } },
        { path: "uploadId" },
      ]);
    return educationList;
  }

  export async function getAllPendingLicenseDetails(
    limit?: number,
    offset?: number,
    name?: string | null
  ): Promise<ILicense[]> {
    let query: any = {
      reviewStatus: "PENDING",
    };

    // If name parameter exists, add search conditions
    if (name) {
      const nameWords = name.trim().split(/\s+/);
      
      if (nameWords.length === 1) {
        // If there's only one word, search by firstname or lastname
        query = {
          ...query,
          $or: [
            { "userId.firstname": { $regex: name, $options: "i" } },
            { "userId.lastname": { $regex: name, $options: "i" } },
          ],
        };
      } else {
        // If there are multiple words
        const firstName = nameWords[0];
        const lastName = nameWords.slice(1).join(" ");
        const fullName = nameWords.join("");
        
        query = {
          ...query,
          $or: [
            { "userId.firstname": { $regex: firstName, $options: "i" }, "userId.lastname": { $regex: lastName, $options: "i" } },
            { "userId.firstname": { $regex: lastName, $options: "i" }, "userId.lastname": { $regex: firstName, $options: "i" } },
            { $expr: { $regexMatch: { input: { $concat: ["$userId.firstname", "$userId.lastname"] }, regex: fullName, options: "i" } } },
          ],
        };
      }
    }

    // Use aggregation pipeline to process complex queries
    const licenseList = await License.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "userId",
        },
      },
      {
        $unwind: "$userId",
      },
      {
        $lookup: {
          from: "uploads",
          localField: "uploadId",
          foreignField: "_id",
          as: "uploadId",
        },
      },
      {
        $unwind: {
          path: "$uploadId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: query,
      },
      {
        $skip: offset || 0,
      },
      {
        $limit: limit || 10,
      },
    ]);

    return licenseList;
  }

  export async function getAllApprovedLicenseDetails(
    limit?: number,
    offset?: number,
    name?: string | null
  ): Promise<ILicense[]> {
    let query: any = {
      reviewStatus: "APPROVED",
    };

    // If name parameter exists, add search conditions
    if (name) {
      const nameWords = name.trim().split(/\s+/);
      
      if (nameWords.length === 1) {
        // If there's only one word, search by firstname or lastname
        query = {
          ...query,
          $or: [
            { "userId.firstname": { $regex: name, $options: "i" } },
            { "userId.lastname": { $regex: name, $options: "i" } },
          ],
        };
      } else {
        // If there are multiple words
        const firstName = nameWords[0];
        const lastName = nameWords.slice(1).join(" ");
        const fullName = nameWords.join("");
        
        query = {
          ...query,
          $or: [
            { "userId.firstname": { $regex: firstName, $options: "i" }, "userId.lastname": { $regex: lastName, $options: "i" } },
            { "userId.firstname": { $regex: lastName, $options: "i" }, "userId.lastname": { $regex: firstName, $options: "i" } },
            { $expr: { $regexMatch: { input: { $concat: ["$userId.firstname", "$userId.lastname"] }, regex: fullName, options: "i" } } },
          ],
        };
      }
    }

    // Use aggregation pipeline to process complex queries
    const licenseList = await License.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "userId",
        },
      },
      {
        $unwind: "$userId",
      },
      {
        $lookup: {
          from: "uploads",
          localField: "uploadId",
          foreignField: "_id",
          as: "uploadId",
        },
      },
      {
        $unwind: {
          path: "$uploadId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: query,
      },
      {
        $skip: offset || 0,
      },
      {
        $limit: limit || 10,
      },
    ]);

    return licenseList;
  }

  export async function updateEducationalDetails(
    id: StringOrObjectId,
    data: Partial<DEducation>
  ): Promise<IEducation> {
    const updatedEducationalDetails = await Education.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true }
    );
    return updatedEducationalDetails;
  }

  export async function updateLicenseDetails(
    id: StringOrObjectId,
    data: Partial<DLicense>
  ): Promise<ILicense> {
    const updatedLicenseDetails = await License.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true }
    );
    return updatedLicenseDetails;
  }

  export async function updateExperienceTag(
    id: StringOrObjectId,
    data: Partial<DExperienceTag>
  ): Promise<IExperienceTag> {
    const updatedTag = await ExperienceTag.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true }
    );
    return updatedTag;
  }

  export async function updateHashTag(
    id: StringOrObjectId,
    data: Partial<DHashtag>
  ): Promise<IHashtag> {
    const updatedHashTag = await Hashtag.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true }
    );
    return updatedHashTag;
  }

  export async function getAllArticles(
    limit?: number,
    offset?: number
  ): Promise<IArticle[]> {
    const articleList = await Article.find()
      .skip(offset)
      .limit(limit)
      .populate([{ path: "createdBy", select: { firstname: 1, lastname: 1 } }])
      .select({ articleTitle: 1, createdBy: 1, createdAt: 1 });
    return articleList;
  }

  export async function deleteArticleById(
    articleId: StringOrObjectId
  ): Promise<IArticle> {
    const deletedArticle = await Article.findByIdAndDelete(articleId);
    return deletedArticle;
  }

  export async function searchTherapistsStatistic(
    searchableString: string,
    limit: number,
    offset: number,
    monthAndYear?: string
  ): Promise<ITherapist[]> {
    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const therapistNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};
    const removeInvalidTherapistsQuery = {
      // blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    const [month, year] = monthAndYear ? monthAndYear.split("/") : [null, null];

    const monthFilter =
      month && year && /^\d{1,2}$/.test(month) && /^\d{4}$/.test(year)
        ? {
          $expr: {
            $and: [
              { $eq: [{ $month: "$createdAt" }, parseInt(month, 10)] },
              { $eq: [{ $year: "$createdAt" }, parseInt(year, 10)] },
            ],
          },
        }
        : {};

    let searchResult: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          zipCode: 1,
          state: 1,
          verifiedStatus: 1,
          blockedByAdmin: 1,
          profession: 1,
          lavniTestAccount: 1,
          primaryPhone: 1,
          _id: 1,
        },
      },
      {
        $match: {
          $and: [therapistNameQuery, removeInvalidTherapistsQuery, monthFilter, { firstname: { $ne: null } }],
        },
      },
      {
        $lookup: {
          from: "professions",
          localField: "profession",
          foreignField: "_id",
          pipeline: [
            {
              $project: { name: 1 },
            },
          ],
          as: "profession",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $skip: offset,
      },
      {
        $limit: limit,
      },
    ]);

    return searchResult;
  }

  export async function searchTherapists(
    searchableString: string,
    limit: number,
    offset: number,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string,
    state?: string,
    therapistCategorizationByType?: string,
    adminApproved?: boolean
  ): Promise<ITherapist[]> {
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery =
      status != null && status ? { verifiedStatus: status } : {};
    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const stateQuery = state != null && state ? { state: state } : {};
    const therapistTypeQuery = therapistCategorizationByType != null && therapistCategorizationByType 
      ? { therapistCategorizationByType: therapistCategorizationByType } 
      : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};
    // const adminApprovedQuery = adminApproved != null && adminApproved
    //   ? { adminApproved: adminApproved }
    //   : { adminApproved: { $ne: false } };

    const adminApprovedQuery = adminApproved === true 
    ? { adminApproved: { $ne: false } } 
    : adminApproved === false 
      ? { adminApproved: { $ne: true } }
      : { adminApproved: { $ne: false } };

    console.log("adminApproved input:", adminApproved, typeof adminApproved);
    console.log("adminApprovedQuery:", JSON.stringify(adminApprovedQuery));

    console.log({adminApprovedQuery});
    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      ...adminApprovedQuery
    };

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

    let searchResult: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          zipCode: 1,
          state: 1,
          verifiedStatus: 1,
          // adminApproved: 1,
          blockedByAdmin: 1,
          profession: 1,
          lavniTestAccount: 1,
          primaryPhone: 1,
          payRateType: 1,
          _id: 1,
          priorityNumber: 1,
          therapistCategorizationByType: 1,
        },
      },
      {
        $match: {
          $and: [
            genderQuery,
            statusQuery,
            subscriptionQuery,
            clientNameQuery,
            zipCodeQuery,
            stateQuery,
            therapistTypeQuery,
            removeInvalidTherapistsQuery,
          ],
        },
      },
      {
        $lookup: {
          from: "professions",
          localField: "profession",
          foreignField: "_id",
          pipeline: [
            {
              $project: { name: 1 },
            },
          ],
          as: "profession",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },

      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]).sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return searchResult;
  }


  export async function searchClientsByAdminStatistic(
    searchableString: string,
    limit: number,
    offset: number,
    monthAndYear?: string
  ): Promise<IClient[]> {
    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName = searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

    const removeInvalidTherapistsQuery = {
      adminApproved: { $ne: false },
    };

    const [month, year] = monthAndYear ? monthAndYear.split("/") : [null, null];

    const monthFilter =
      month && year && /^\d{1,2}$/.test(month) && /^\d{4}$/.test(year)
        ? {
          $expr: {
            $and: [
              { $eq: [{ $month: "$createdAt" }, parseInt(month, 10)] },
              { $eq: [{ $year: "$createdAt" }, parseInt(year, 10)] },
            ],
          },
        }
        : {};

    let searchResult: IClient[] = await Client.aggregate([
      {
        $lookup: {
          from: "meetings",
          localField: "_id",
          foreignField: "clientId",
          pipeline: [
            {
              $match: {
                $and: [{ "callingStatus": "COMPLETED" }]
              },
            },
            {
              $project: { callingStatus: 1, _id: 1 },
            },
          ],
          as: "meetings"
        },
      },
      {
        $addFields: {
          completedMeetingCount: { $size: '$meetings' }
        }
      },
      {
        $lookup: {
          from: "appointments",
          localField: "_id",
          foreignField: "clientId",
          pipeline: [
            {
              $match: {
                $or: [{ "status": "PENDING" }, { "status": "WAITING_FOR_APPROVAL" }]
              }
            },
            {
              $project: { status: 1, _id: 1 },
            }
          ],
          as: "appointments"
        },
      },
      {
        $addFields: {
          missedAppointmentCount: { $size: '$appointments' }
        }
      },
      {
        $lookup: {
          from: "insurances",
          localField: "_id",
          foreignField: "clientId",
          pipeline: [
            {
              $project: { insuranceCompanyId: 1 }
            },
          ],
          as: "insurance"
        },
      },
      {
        $unwind: {
          path: "$insurance"
        },
      },
      {
        $lookup: {
          from: "insurancecompanies",
          localField: "insurance.insuranceCompanyId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { contractPrice: 1 },
            },
          ],
          as: "insuranceCompany"
        },
      },
      {
        $unwind: {
          path: "$insuranceCompany"
        },
      },
      {
        $project: {
          _id: 1,
          firstname: 1,
          lastname: 1,
          createdAt: 1,
          gender: 1,
          state: 1,
          verifiedStatus: 1,
          avatarId: 1,
          blockedByAdmin: 1,
          completedMeetingCount: 1,
          missedAppointmentCount: 1,
          insuranceCompany: 1
        }
      },
      {
        $match: {
          $and: [clientNameQuery, removeInvalidTherapistsQuery, monthFilter, { firstname: { $ne: null } }]
        },
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $skip: offset
      },
      {
        $limit: limit
      },
    ]);

    return searchResult;
  }

  export async function searchClientsByAdmin(
    searchableString: string,
    limit: number,
    offset: number,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string,
    freindListArray?: Types.ObjectId[],
    therapistId?: boolean,
    clientActiveStatus?: string,
    appointmentStatus?: string,
    insuranceActiveStatus?: string,
    copaymentStatus?: string,
    insuranceCompanyId: string = "",
    state: string = "",
  ): Promise<IClient[]> {
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery = status != null && status ? { verifiedStatus: status } : {};

    let copaymentStatusQuery = {};

    if(copaymentStatus) {
      if (copaymentStatus === "EXISTING") {
        copaymentStatusQuery = { 
          copaymentAmount: { $exists: true},
        }
      } else if (copaymentStatus === "GREATER") {
        copaymentStatusQuery = { 
          copaymentAmount: { $exists: true, $gt: 0},
        }
      }
    }

    let insuaranceActiveStatusQuery = {};

    if(insuranceActiveStatus) {
      if (insuranceActiveStatus === "WITH_INSURANCE") {
        insuaranceActiveStatusQuery = { 
          $or: [
            {insuranceId: { $exists: true , $ne: null }},
            {secondaryInsuranceId: { $exists: true , $ne: null }}
          ],
        }
      } else if (insuranceActiveStatus === "WITHOUT_INSURANCE") {
        insuaranceActiveStatusQuery = {
          $and: [
            {
              $or: [
                {insuranceId: { $exists: false }},
                {insuranceId: null},
              ],
            },
            {
              $or: [
                {secondaryInsuranceId: { $exists: false }},
                {secondaryInsuranceId: null},
              ],
            }
          ]
        }
      }
    }

    const removeInvalidTherapistsQuery = {
      adminApproved: { $ne: false },
      firstname: { $exists: true }
    };

    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
                { primaryPhone: searchedName }
              ],
            },
          ],
        }
        : {};

    const friendListQuery = therapistId && freindListArray ? {
      _id: { $in: freindListArray.map(friend => friend) },
    } : {};

    let clientActiveStatusQuery = {};

    if (clientActiveStatus == "ACTIVE") {
      clientActiveStatusQuery = {
        $or: [
          { clientActiveStatus: true },
          { clientActiveStatus: { $exists: false } }
        ]
      };
    } else if (clientActiveStatus == "INACTIVE") {
      clientActiveStatusQuery = { clientActiveStatus: false };
    }

    const currentDate = new Date();

    let upcomingMatchQuery = {};

    if (appointmentStatus == "UPCOMMING_APPOINTMENT") {
      upcomingMatchQuery = { 'appointments.start': { $gte: currentDate } };
    } else if (appointmentStatus == "NO_UPCOMMING_APPOINTMENT") {
      upcomingMatchQuery = {
        $or: [
          { 'appointments.start': { $exists: false } },
          { 'appointments.start': { $not: { $gte: currentDate } } }
        ]
      };
    }

    let searchResult: IClient[] = await Client.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "primaryTherapist",
          foreignField: "_id",
          as: "primaryTherapistDetails",
        }
      },
      {
        $set: {
          primaryTherapistDetails: { $arrayElemAt: ["$primaryTherapistDetails", 0] }
        }
      },
      {
        $lookup: {
          from: "friendrequests",
          let: { clientIdFrom: "$_id" },
          pipeline: [
            { $match: { $expr: { $and: [ { $eq: ["$clientId", "$$clientIdFrom"] }, { $eq: ["$status", "APPROVED"] } ] } } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 } 
          ],
          as: "latestFriendRequest"
        }
      },
      {
        $set: {
          latestMatchedTherapistId: {
            $cond: {
              if: { $gt: [{ $size: "$latestFriendRequest" }, 0] },
              then: { $arrayElemAt: ["$latestFriendRequest.therapistId", 0] },
              else: null
            }
          }
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "latestMatchedTherapistId",
          foreignField: "_id",
          as: "latestMatchedTherapistDetails",
        }
      },
      {
        $set: {
          latestMatchedTherapistDetails: { $arrayElemAt: ["$latestMatchedTherapistDetails", 0] }
        }
      },
      {
        $lookup: {
          from: "appointments",
          localField: "_id",
          foreignField: "clientId",
          as: "appointments",
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "appointments.therapistId",
          foreignField: "_id",
          as: "appointmentTherapist",
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "appointments.meetingStartedBy",
          foreignField: "_id",
          as: "missedCallingUser",
        }
      },
      {
        $project: {
          '_id': 1,
          'firstname': 1,
          'lastname': 1,
          'fullName': {
            $concat: ["$firstname", " ", "$lastname"],
          },
          'email': 1,
          'createdAt': 1,
          'photoId': 1,
          'gender': 1,
          'zipCode': 1,
          'state': 1,
          'role': 1,
          'skip': 1,
          'verifiedStatus': 1,
          'subscriptionId': 1,
          'subscriptionStatus': 1,
          'lavniTestAccount': 1,
          'copaymentAmount': 1,
          'conversationId': 1,
          'blockedByAdmin': 1,
          'adminApproved': 1,
          'insuranceId': 1,
          'premiumStatus': 1,
          'primaryPhone': 1,
          'clientActiveStatus': 1,
          'claimEligibilityMdDetails': 1,
          'claimEligibilityDetails': 1,
          'claimEligibilityMdErrorDetails': 1,
          'friendRequests': 1,
          'appointments._id': 1,
          'appointments.start': 1,
          'appointments.status': 1,
          'appointments.therapistId': 1,
          'appointments.meetingStartedBy': 1,
          'appointmentTherapist.firstname': 1,
          'appointmentTherapist._id': 1,
          'appointmentTherapist.lastname': 1,
          'latestMatchedTherapistDetails.firstname': 1,
          'latestMatchedTherapistDetails.lastname': 1,
          'latestMatchedTherapistDetails._id': 1,
          // 'primaryTherapistDetails.firstname': 1,
          // 'primaryTherapistDetails.lastname': 1,
          // 'primaryTherapistDetails._id': 1,
        }
      },
      ...(insuranceCompanyId ? [{
        $lookup: {
          from: "insurances",
          let: { insuranceId: "$insuranceId", secondaryInsuranceId: "$secondaryInsuranceId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$insuranceCompanyId", new Types.ObjectId(insuranceCompanyId)] },
                    {
                      $or: [
                        { $eq: ["$_id", "$$insuranceId"] },
                        { $eq: ["$_id", "$$secondaryInsuranceId"] }
                      ]
                    }
                  ]
                }
              }
            }
          ],
          as: "matchingInsurance"
        }
      }] : []),
      {
        $match: {
          $and: [
            genderQuery,
            statusQuery,
            subscriptionQuery,
            clientNameQuery,
            zipCodeQuery,
            removeInvalidTherapistsQuery,
            clientActiveStatusQuery,
            ...(therapistId && freindListArray ? [friendListQuery] : []),
            upcomingMatchQuery,
            insuaranceActiveStatusQuery,
            copaymentStatusQuery,
            ...(state && state !== "" ? [{ state: state }] : []),
            ...(insuranceCompanyId && insuranceCompanyId !== "" ? [{ matchingInsurance: { $ne: [] } }] : [])
          ],
        },
      }
    ]).sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return searchResult;

  }

  export async function getDataForClients(userId: Types.ObjectId, searchResult: IClient[]): Promise<any[]> {
    const clientsWithCounts: any[] = [];

    for (const client of searchResult) {
      const currentDate = new Date();

      // const futureAppointments = client.appointments.filter((appointment: any) => appointment.start >= currentDate);

      // futureAppointments.sort((a: any, b: any) => {
      //   return a.start - b.start;
      // });

      const futureAppointments = client.appointments
        .filter((appointment: any) =>
          appointment.start >= currentDate &&
          appointment.status !== "REJECTED"
      );

      futureAppointments.sort((a: any, b: any) => {
        return a.start - b.start;
      });

      const pastAppointments = client.appointments.filter((appointment: any) => appointment.start < currentDate);

      pastAppointments.sort((a: any, b: any) => {
        return b.start - a.start;
      });

      // const latestAppointmentDate = futureAppointments ? futureAppointments[0] : "";
      const latestAppointmentDate = futureAppointments && futureAppointments.length > 0 ? futureAppointments[0] : "";
      const pastAppointmentDate = pastAppointments ? pastAppointments[0] : "";

      let latestClient = "";

      if(client && client?.latestMatchedTherapistDetails && client?.latestMatchedTherapistDetails?._id){

        latestClient = (client?.latestMatchedTherapistDetails?.firstname ?? "") + " " + (client?.latestMatchedTherapistDetails?.lastname ?? "");
        
      }

      // if(client && client?.primaryTherapistDetails && client?.primaryTherapistDetails?._id){

      //   latestClient = (client?.primaryTherapistDetails?.firstname ?? "") + " " + (client?.primaryTherapistDetails?.lastname ?? "");
        
      // }else{

      //   if(client && client?.latestMatchedTherapistDetails && client?.latestMatchedTherapistDetails?._id){

      //     latestClient = (client?.latestMatchedTherapistDetails?.firstname ?? "") + " " + (client?.latestMatchedTherapistDetails?.lastname ?? "");
          
      //   }
      // }

      // if (client?.appointmentTherapist && client?.appointmentTherapist?.length && client?.appointmentTherapist?.length > 0) {
      //   latestClient = (client?.appointmentTherapist[0]?.firstname ?? "") + " " + (client?.appointmentTherapist[0]?.lastname ?? "");
      // }

      if(client?.appointments){       
        const allAppointmentsFrom = client?.appointments;

        allAppointmentsFrom?.sort((a: any, b: any) => {
          return b?.start - a?.start;
        });

        if (allAppointmentsFrom && allAppointmentsFrom?.length && allAppointmentsFrom?.length > 0) {
          if(allAppointmentsFrom[0]?.therapistId){
            const latestTherapistDetails = await Therapist.findById(allAppointmentsFrom[0]?.therapistId);

            latestClient = (latestTherapistDetails?.firstname ?? "") + " " + (latestTherapistDetails?.lastname ?? "");
          }
        }
      }

      let missedJoinCaller = "";

      if (pastAppointments[0]?.meetingStartedBy && pastAppointments[0]?.meetingStartedBy != null) {
        // if (mongoose.Types.ObjectId.isValid(pastAppointments[0]?.meetingStartedBy) &&
        //   mongoose.Types.ObjectId.isValid(client.appointmentTherapist[0]?._id) &&
        //   pastAppointments[0]?.meetingStartedBy.toString() === client.appointmentTherapist[0]?._id.toString()) {
        //   missedJoinCaller = client?.firstname;
        // } else {
        //   missedJoinCaller = client.appointmentTherapist[0]?.firstname;
        // }

        if (mongoose.Types.ObjectId.isValid(pastAppointments[0]?.meetingStartedBy)) {
          const matchingTherapist = client.appointmentTherapist?.find((therapist: AppointmentTherapist) => 
            mongoose.Types.ObjectId.isValid(therapist._id) && 
            therapist._id.toString() === pastAppointments[0]?.therapistId?.toString()
          );
      
          if (pastAppointments[0]?.meetingStartedBy.toString() === pastAppointments[0]?.therapistId?.toString()) {
            missedJoinCaller = client?.firstname;
          } else {
            missedJoinCaller = matchingTherapist?.firstname;
          }
        }
      }

      const unreadTwilioMessageCount = await Notification.find({
        receiverId: userId,
        senderId: client?._id,
        event: NotificationEvent.NEW_TWILIO_MESSAGE_FROM_CLIENT,
        twilioMessageReadStatus: false
      }).count();

      const latestDiagnosisNote = await DiagnosisNote.findOne({clientId: client?._id, updatedByTherapist: true}).sort({ createdAt: -1 });

      let diagnosisCodeForClient:any[] = [];
      let secondaryDiagnosisCodeForClient:any[] = [];

      if(latestDiagnosisNote && latestDiagnosisNote?.diagnosisICDcodes){
        diagnosisCodeForClient = latestDiagnosisNote?.diagnosisICDcodes;
      }

      if(latestDiagnosisNote && latestDiagnosisNote?.secondaryDiagnosisICDcodes){
        secondaryDiagnosisCodeForClient = latestDiagnosisNote?.secondaryDiagnosisICDcodes;
      }

      clientsWithCounts.push({ 
        client: client, 
        latestAppointmentDate: latestAppointmentDate, 
        pastAppointmentDate: pastAppointmentDate, 
        latestClient: latestClient, 
        missedJoinCaller: missedJoinCaller, 
        unreadTwilioMessageCount: unreadTwilioMessageCount, 
        diagnosisCodeForClient: diagnosisCodeForClient, 
        secondaryDiagnosisCodeForClient: secondaryDiagnosisCodeForClient
      });
    }

    return clientsWithCounts;
  }

  export async function getAllAppointmentsByClientId(
    clientId: Types.ObjectId
  ): Promise<IAppointment[]> {
    const response = await Appointment.find({ createdBy: clientId });
    return response;
  }

  export async function deleteAllAppointmentsByClientId(
    clientId: Types.ObjectId
  ): Promise<number> {
    const response = await Appointment.deleteMany({ createdBy: clientId });
    return response.ok;
  }

  export async function getAllPremiumClients(
    limit: number,
    offset: number
  ): Promise<IClient[]> {
    const response = await Client.find({ premiumStatus: PremiumStatus.ACTIVE })
      .populate([{ path: "invoices" }])
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return response;
  }

  export async function getAllPremiumMembershipRevokedClients(
    limit: number,
    offset: number
  ): Promise<IClient[]> {
    const response = await Client.find({ premiumStatus: PremiumStatus.REVOKED })
      .populate([{ path: "invoices" }])
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return response;
  }

  export async function getAllPremiumClientsCount(): Promise<number> {
    const response = await Client.find({ premiumStatus: PremiumStatus.ACTIVE });
    return response.length;
  }

  export async function getAllRevokedClientsCount(): Promise<number> {
    const response = await Client.find({
      premiumStatus: PremiumStatus.REVOKED,
    });
    return response.length;
  }

  export async function getInsuranceCompanyByType(
    insuranceCompany: string
  ): Promise<IInsuranceCompany> {
    const insurance = await InsuranceCompany.findOne({
      insuranceCompany: { $eq: insuranceCompany },
      // insuranceCompany: { $eq: insuranceCompany},
    });
    return insurance;
  }

  export async function checkInsuaranceCompanyExistNyName(
    insuranceCompany: string,
    insuranceCompanyId: string
  ): Promise<IInsuranceCompany> {
    const insurance = await InsuranceCompany.findOne({
      insuranceCompany: { $eq: insuranceCompany },
      _id: { $ne: insuranceCompanyId }
    });
    return insurance;
  }

  export async function addInsuranceCompany(
    insuranceCompany: string,
    coPayment?: number,
    contractPrice?: number,
    states?: string[],
    fax?: string,
    tradingPartnerServiceId?: string,
    organizationName?: string,
    payerName?: string,
    link?: string
  ): Promise<IInsuranceCompany> {
    const iInsuranceC = new InsuranceCompany({
      insuranceCompany: insuranceCompany,
      coPayment,
      contractPrice,
      states,
      fax,
      tradingPartnerServiceId,
      organizationName,
      payerName,
      link
    });
    let insuranceType = await iInsuranceC.save();
    AppLogger.info(`Created insurance company. ID: ${insuranceType._id}`);
    return insuranceType;
  }

  export async function getAllInsuranceCompaniesTypes(
    limit: number,
    offset: number
  ): Promise<IInsuranceCompany[]> {
    const insuranceCompany = await InsuranceCompany.find()
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return insuranceCompany;
  }

  export async function getAllInsuranceCompaniesTypesPublic(): Promise<
    IInsuranceCompany[]
  > {
    const insuranceCompany = await InsuranceCompany.find().sort({
      createdAt: -1,
    });
    return insuranceCompany;
  }

  export async function getInsuranceCompanyById(
    id: StringOrObjectId
  ): Promise<IInsuranceCompany> {
    const insuranceCompany = await InsuranceCompany.findById(id);
    return insuranceCompany;
  }

  export async function deleteInsuranceCompany(
    id: StringOrObjectId
  ): Promise<IInsuranceCompany> {
    const response = await InsuranceCompany.findByIdAndDelete(id);

    return response;
  }

  export async function updateInsuranceCompany(
    id: StringOrObjectId,
    data: Partial<DInsuranceCompany>
  ): Promise<IInsuranceCompany> {
    const response = await InsuranceCompany.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true }
    );
    return response;
  }

  export async function getDiagnosisNotesByClientId(
    clientId: Types.ObjectId,
    limit: number,
    offset: number
  ): Promise<ITreatmentHistory[]> {
    const response = await TreatmentHistory.aggregate([
      // {
      //   $match: {
      //     clientId: clientId,
      //     //isMeetingTranscribe: true,
      //   },
      // },
      // {
      //   $lookup: {
      //     from: "users",
      //     localField: "therapistId",
      //     foreignField: "_id",
      //     pipeline: [
      //       {
      //         $project: {
      //           firstname: 1,
      //           lastname: 1,
      //           photoId: 1,
      //           blockedByAdmin: 1,
      //           adminApproved: 1,
      //         },
      //       },
      //     ],
      //     as: "therapistId",
      //   },
      // },
      // {
      //   $lookup: {
      //     from: "users",
      //     localField: "clientId",
      //     foreignField: "_id",
      //     pipeline: [
      //       {
      //         $project: {
      //           firstname: 1,
      //           lastname: 1,
      //           photoId: 1,
      //           blockedByAdmin: 1,
      //           adminApproved: 1,
      //         },
      //       },
      //     ],
      //     as: "clientId",
      //   },
      // },
      // {
      //   $unwind: {
      //     path: "$therapistId",
      //     preserveNullAndEmptyArrays: true,
      //   },
      // },
      // {
      //   $unwind: {
      //     path: "$clientId",
      //     preserveNullAndEmptyArrays: true,
      //   },
      // },
      // {
      //   $project: {
      //     clientId: 1,
      //     therapistId: 1,
      //     diagnosisNoteId: 1,
      //     meetingStartedTime: 1,
      //   },
      // },
      // {
      //   $sort: { createdAt: -1 },
      // },
      // {
      //   $skip: limit * (offset - 1),
      // },
      // {
      //   $limit: limit,
      // },

      {
        $match: {
          clientId: clientId,
          //isMeetingTranscribe: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          as: "therapistId",
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          therapistId: {
            _id: 1,
            firstname: 1,
            lastname: 1,
          },
          clientId: {
            _id: 1,
          },
          diagnosisNoteId: 1,
          meetingStartedTime: 1,
          isMeetingTranscribe: 1,
          claimStatus: 1,
          errorMsg: 1,
        },
      },
      {
        $sort: { meetingStartedTime: -1 },
      },
      {
        $group: {
          _id: {
            _id: "$therapistId._id",
            fullName: {
              $concat: ["$therapistId.firstname", " ", "$therapistId.lastname"],
            },
          },
          notes: {
            $push: {
              diagnosisNoteId: "$diagnosisNoteId",
              meetingStartedTime: "$meetingStartedTime",
              isMeetingTranscribe: "$isMeetingTranscribe",
              claimStatus: "$claimStatus",
              errorMsg: "$errorMsg",
              clientId: "$client._id",
            },
          },
        },
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $skip: limit * (offset - 1),
      },
      {
        $limit: limit,
      },
    ]);

    return response;
  }

  export async function getAllMeetingsAndRecordings(
    limit: number,
    offset: number
  ): Promise<IMeeting[]> {
    const queryConditions: any = {
      "clientId.lavniTestAccount": { $ne: true },
      "therapistId.lavniTestAccount": { $ne: true },
      meetingId: { $ne: "", $exists: true },
    };

    const meetings = await Meeting.find(queryConditions)
      .populate({
        path: 'clientId',
        select: 'firstname lastname lavniTestAccount _id',
        match: { lavniTestAccount: { $ne: true } },
      })
      .populate({
        path: 'therapistId',
        select: 'firstname lastname lavniTestAccount _id',
        match: { lavniTestAccount: { $ne: true } },
      })
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit)
      .exec();

    return meetings;
  }


  export async function getAllMeetingsAndRecordingsTest(
    limit: number,
    offset: number
  ): Promise<IMeeting[]> {
    const response = await Meeting.find()
      .populate([
        { path: "clientId" },
        { path: "therapistId" },
        { path: "audioFiles" },
      ])
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return response;
  }

  export async function getMeetingsAndRecording(
    id: StringOrObjectId
  ): Promise<IMeeting> {
    const response = await Meeting.findById(id);
    return response;
  }

  export async function searchMeetingsAndRecordingsByAdmin(
    limit: number,
    offset: number,
    searchableText: string
  ): Promise<IMeeting[]> {
    const response = await Meeting.find({ meetingId: searchableText }).populate(
      [{ path: "clientId" }, { path: "therapistId" }, { path: "audioFiles" }]
    );
    return response;
  }

  export async function deleteMeetingsAndRecording(
    id: StringOrObjectId
  ): Promise<IMeeting> {
    const response = await Meeting.findByIdAndDelete(id);
    return response;
  }

  export async function setMarketingEmails(
    emaiList: []
  ): Promise<IMarketingEmail> {
    const iMarketingEmail = new MarketingEmail({ emaiList });
    let marketingEmailType = await iMarketingEmail.save();
    return marketingEmailType;
  }

  export async function getMarketingEmails(
    id: StringOrObjectId
  ): Promise<IMarketingEmail> {
    const response = await MarketingEmail.findById(id);
    return response;
  }

  export async function deleteMarketingEmails(
    id: StringOrObjectId
  ): Promise<IMarketingEmail> {
    const response = await MarketingEmail.findByIdAndDelete(id);
    return response;
  }

  export async function getAllMarketingEmails(
    limit: number,
    offset: number
  ): Promise<any> {
    const meetings = await MarketingEmail.find()
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return meetings;
  }

  export async function getAllPendingTherapistReviews(
    limit?: number,
    offset?: number
  ): Promise<ICustomerReview[]> {
    const reportList = await CustomerReview.find({ status: "PENDING", ganarateSOAP: { $exists: true, $ne: true }, therapistId: { $exists: true, $ne: null } })
      .populate(populateOptions2)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return reportList;
  }

  export async function getAllSOAPTherapistReviews(
    limit?: number,
    offset?: number
  ): Promise<ICustomerReview[]> {
    const reportList = await CustomerReview.find({ ganarateSOAP: true, therapistId: { $exists: true, $ne: null } })
      .populate(populateOptions3)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return reportList;
  }

  export async function getAllApprovedTherapistReviews(
    limit?: number,
    offset?: number
  ): Promise<ICustomerReview[]> {
    const reportList = await CustomerReview.find({ status: "APPROVED", therapistId: { $exists: true, $ne: null } })
      .populate(populateOptions2)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return reportList;
  }

  export async function getReviewById(
    id: StringOrObjectId
  ): Promise<ICustomerReview> {
    let review: ICustomerReview = await CustomerReview.findById(id);

    return review;
  }

  export async function updateReview(
    reviewId: StringOrObjectId,
    data: Partial<DCustomerReview>
  ): Promise<ICustomerReview> {
    const updatedReview = await CustomerReview.findByIdAndUpdate(
      reviewId,
      { $set: data },
      { new: true }
    );
    return updatedReview;
  }

  export async function getAllLavniReviews(
    limit?: number,
    offset?: number,
    reviewStatus?: LavniReviewStatus
  ): Promise<ILavniReview[]> {
    const reviews = await LavniReview.find({ status: reviewStatus })
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);

    return reviews;
  }

  export async function getLavniReviewById(
    id: StringOrObjectId
  ): Promise<ILavniReview> {
    let review: ILavniReview = await LavniReview.findById(id);

    return review;
  }

  export async function updateLavniReview(
    reviewId: StringOrObjectId,
    data: Partial<DLavniReview>
  ): Promise<ILavniReview> {
    const updatedReview = await LavniReview.findByIdAndUpdate(
      reviewId,
      { $set: data },
      { new: true }
    );

    return updatedReview;
  }

  export async function updateMeetingStatusById(
    id: StringOrObjectId,
    data: Partial<any>
  ): Promise<IMeeting> {
    const meeting = await Meeting.findOneAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    );

    return meeting;
  }

  export async function getMeetingStatusById(
    id: StringOrObjectId
  ): Promise<{ copayment: any | undefined, sessionAmount: number | undefined }> {
    const meeting: IMeeting | null = await Meeting.findById(id);

    if (meeting) {
      const { copayment, sessionAmount } = meeting;
      return { copayment, sessionAmount };
    } else {
      return { copayment: undefined, sessionAmount: undefined };
    }
  }

  export async function searchTherapistsByClientIdFriends(
    searchableString: string,
    limit: number,
    offset: number,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string,
    therapistArr?: string[],
  ): Promise<ITherapist[]> {
    const clientIdArray = therapistArr?.map(id => new mongoose.Types.ObjectId(id));
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery =
      status != null && status ? { verifiedStatus: status } : {};
    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

    let searchResult: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          zipCode: 1,
          state: 1,
          verifiedStatus: 1,
          blockedByAdmin: 1,
          profession: 1,
          lavniTestAccount: 1,
          primaryPhone: 1,
          _id: 1,
        },
      },
      {
        $match: {
          $and: [
            genderQuery,
            statusQuery,
            subscriptionQuery,
            clientNameQuery,
            zipCodeQuery,
            removeInvalidTherapistsQuery,
            {
              _id: {
                $nin: clientIdArray.length > 0 ? clientIdArray : [],
              },
            },
            { firstname: { $ne: null } },
          ],
          lavniTestAccount: { $ne: true },
        },
      },
      {
        $lookup: {
          from: "professions",
          localField: "profession",
          foreignField: "_id",
          pipeline: [
            {
              $project: { name: 1 },
            },
          ],
          as: "profession",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },

      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]).skip(limit * (offset - 1))
      .limit(limit);

    return searchResult;
  }

  export async function getTherapistsByClientIdCount(
    searchableString: string,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string,
    therapistArr?: string[]
  ): Promise<number> {
    const clientIdArray = therapistArr?.map(id => new mongoose.Types.ObjectId(id));
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery = status != null && status ? { verifiedStatus: status } : {};
    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let searchedName = null;

    if (searchableString) {
      let searchItem = searchableString.replace(/\s/g, "");
      searchedName = searchableString != null ? new RegExp(`^${searchItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

    const count = await Therapist.countDocuments({
      $and: [
        genderQuery,
        statusQuery,
        subscriptionQuery,
        clientNameQuery,
        zipCodeQuery,
        removeInvalidTherapistsQuery,
        {
          _id: {
            $nin: clientIdArray.length > 0 ? clientIdArray : [],
          },
        },
      ],
    });

    return count;
  }

  export async function getFriendRequestByClientId(
    clientId: Types.ObjectId
  ): Promise<IFriendRequest[]> {
    const response = await FriendRequest.find({
      clientId: clientId,
      status: FriendRequestStatus.APPROVED
    }).populate(populateOptions);

    return response;
  }

  export async function getFriendRequestByClientIdAndTherapistId(
    clientId: Types.ObjectId,
    therapistId: Types.ObjectId
  ): Promise<IFriendRequest[]> {
    const response = await FriendRequest.find({
      clientId: clientId,
      therapistId: { $ne: therapistId },
      status: FriendRequestStatus.APPROVED
    }).populate(populateOptions);

    return response;
  }

  export async function getFriendRequestDataByClientId(
    clientId: Types.ObjectId
  ): Promise<IFriendRequest> {
    const response = await FriendRequest.findOne({
      clientId: clientId,
      status: FriendRequestStatus.APPROVED
    }).sort({ createdAt: -1 }).populate(populateOptions3);

    return response;
  }

  export async function getAppointmentByClientId(clientId: StringOrObjectId): Promise<IAppointment> {
    const response = await Appointment.findOne({ clientId: clientId }).sort({ createdAt: -1 });
    return response;
  }

  export async function searchTherapistsByClientIdMatchedFriends(
    searchableString: string,
    limit: number,
    offset: number,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string,
    therapistArr?: string[],
  ): Promise<ITherapist[]> {
    const clientIdArray = therapistArr?.map(id => new mongoose.Types.ObjectId(id));
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery =
      status != null && status ? { verifiedStatus: status } : {};
    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

    let searchResult: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          zipCode: 1,
          state: 1,
          verifiedStatus: 1,
          blockedByAdmin: 1,
          profession: 1,
          lavniTestAccount: 1,
          primaryPhone: 1,
          blockedDates: 1,
          _id: 1,
        },
      },
      {
        $match: {
          $and: [
            genderQuery,
            statusQuery,
            subscriptionQuery,
            clientNameQuery,
            zipCodeQuery,
            removeInvalidTherapistsQuery,
            {
              _id: {
                $in: clientIdArray.length > 0 ? clientIdArray : [],
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: "professions",
          localField: "profession",
          foreignField: "_id",
          pipeline: [
            {
              $project: { name: 1 },
            },
          ],
          as: "profession",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },

      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]).sort({ updatedAt: -1 })
      .skip(offset)
      .limit(limit);

    return searchResult;
  }


  export async function searchTherapistsByClientIdAndTherapistIdMatchedFriends(
    limit: number,
    offset: number,
    therapistArr?: string[],
  ): Promise<ITherapist[]> {
    const clientIdArray = therapistArr?.map(id => new mongoose.Types.ObjectId(id));
    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let searchResult: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          zipCode: 1,
          state: 1,
          verifiedStatus: 1,
          blockedByAdmin: 1,
          profession: 1,
          lavniTestAccount: 1,
          primaryPhone: 1,
          blockedDates: 1,
          _id: 1,
        },
      },
      {
        $match: {
          $and: [
            removeInvalidTherapistsQuery,
            {
              _id: {
                $in: clientIdArray.length > 0 ? clientIdArray : [],
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: "professions",
          localField: "profession",
          foreignField: "_id",
          pipeline: [
            {
              $project: { name: 1 },
            },
          ],
          as: "profession",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },

      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]).sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);


    return searchResult;
  }

  export async function getMatchedTherapistsByClientIdCount(
    searchableString: string,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string,
    therapistArr?: string[]
  ): Promise<number> {
    const clientIdArray = therapistArr?.map(id => new mongoose.Types.ObjectId(id));
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery = status != null && status ? { verifiedStatus: status } : {};
    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let searchedName = null;

    if (searchableString) {
      let searchItem = searchableString.replace(/\s/g, "");
      searchedName = searchableString != null ? new RegExp(`^${searchItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

    const count = await Therapist.countDocuments({
      $and: [
        genderQuery,
        statusQuery,
        subscriptionQuery,
        clientNameQuery,
        zipCodeQuery,
        removeInvalidTherapistsQuery,
        {
          _id: {
            $in: clientIdArray.length > 0 ? clientIdArray : [],
          },
        },
      ],
    });

    return count;
  }

  export async function getUserById(
    userId: Types.ObjectId
  ): Promise<IUser> {
    const response = await User.findById(userId);

    return response;
  }

  // export async function getTherapistPriorityNumberById(
  //   userId: Types.ObjectId
  // ): Promise<ITherapist> {
  //   const response = await Therapist.findById(userId).populate({
  //     path:"therapistId",
  //     select:"priorityNumber"
  //   });

  //   return response;
  // }

  export async function getDiagnosisNotesById(
    noteId: Types.ObjectId,
  ): Promise<ITreatmentHistory[]> {
    const response = await TreatmentHistory.aggregate([
      {
        $match: {
          diagnosisNoteId: new Types.ObjectId(noteId),
          //isMeetingTranscribe: true,
        },
      },
    ]);
    const mergeMetingsId = response[0]?.mergedMeetings
    return mergeMetingsId;
  }

  export async function getDiagnosisNotesMeetingById(
    noteId: Types.ObjectId,
  ): Promise<ITreatmentHistory[]> {
    const response = await TreatmentHistory.aggregate([
      {
        $match: {
          diagnosisNoteId: new Types.ObjectId(noteId),
          //isMeetingTranscribe: true,
        },
      },
    ]);
    const mergeMetingsId = response[0]?.meetingId
    return mergeMetingsId;
  }

  export async function getAllTherapistList(): Promise<ITherapist[]> {
    const therapistList = await Therapist.find({ payRateType: { $exists: false } })
      .sort({ createdAt: -1 });

    return therapistList;
  }

  export async function searchAllTherapists(
    searchableString: string,
    limit: number,
    offset: number,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string
  ): Promise<ITherapist[]> {
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery =
      status != null && status ? { verifiedStatus: status } : {};
    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};

    // const removeInvalidTherapistsQuery = {
    // blockedByAdmin: { $ne: true },
    // adminApproved: { $ne: false },
    // };

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

    let searchResult: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          zipCode: 1,
          state: 1,
          verifiedStatus: 1,
          blockedByAdmin: 1,
          profession: 1,
          lavniTestAccount: 1,
          primaryPhone: 1,
          payRateType: 1,
          _id: 1,
          priorityNumber: 1,
          adminApproved: 1,
          blackTherapyPriorityNumber: 1,
          relationshipTherapyPriorityNumber: 1,
          mensMentalTherapyPriorityNumber: 1,
          claimOpen: 1,
        },
      },
      {
        $match: {
          $and: [
            genderQuery,
            statusQuery,
            subscriptionQuery,
            clientNameQuery,
            zipCodeQuery,
            // removeInvalidTherapistsQuery,
          ],
        },
      },
      {
        $lookup: {
          from: "professions",
          localField: "profession",
          foreignField: "_id",
          pipeline: [
            {
              $project: { name: 1 },
            },
          ],
          as: "profession",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },

      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]).sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return searchResult;
  }

  export async function searchAllReferralEarnings(
    searchableString: string,
    limit: number,
    offset: number,
    rewardType?: string,
    payStatus?: string,
  ): Promise<IReferralEarning[]> {
    const rewardTypeQuery = rewardType != null && rewardType ? { rewardType: rewardType } : {};
    const paidStatusQuery = payStatus === "UNPAID"
      ? { $or: [{ paidStatus: "UNPAID" }, { paidStatus: { $exists: false } }] }
      : payStatus ? { paidStatus: payStatus } : {};

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName = searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    let searchResult: IReferralEarning[] = await ReferralEarning.aggregate([
      {
        $match: {
          $and: [
            paidStatusQuery,
            rewardTypeQuery
          ],
        },
      },
      {
        $project: {
          transactionAmount: 1,
          accumulatedBalance: 1,
          accumulatedTotalEarnings: 1,
          accumulatedWithdrawals: 1,
          therapistId: 1,
          type: 1,
          rewardType: 1,
          verifiedStatus: 1,
          createdAt: 1,
          paidStatus: {
            $cond: {
              if: { $eq: ["$paidStatus", "PAID"] },
              then: "PAID",
              else: { $ifNull: ["$paidStatus", "UNPAID"] }
            }
          },
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          as: "therapist",
        },
      },
      {
        $unwind: {
          path: "$therapist",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
            $or: [
              { "therapist.firstname": searchedName },
              { "therapist.lastname": searchedName },
              { "therapist.email": searchedName },
              { "therapist.fullName": searchedName },
            ],
        },
      },
      {
        $project: {
          transactionAmount: 1,
          accumulatedBalance: 1,
          accumulatedTotalEarnings: 1,
          accumulatedWithdrawals: 1,
          therapistId: 1,
          type: 1,
          rewardType: 1,
          verifiedStatus: 1,
          email: "$therapist.email",
          firstname: "$therapist.firstname",
          lastname: "$therapist.lastname",
          fullName: {
            $concat: ["$therapist.firstname", " ", "$therapist.lastname"],
          },
          paidStatus: 1,
        },
      },
    ]).sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return searchResult;
  }

  export async function searchAllClientRewards(
    searchableString: string,
    limit: number,
    offset: number,
    rewardType?: string,
    payStatus?: string,
  ): Promise<IClientReward[]> {
    const rewardTypeQuery = rewardType != null && rewardType ? { rewardType: rewardType } : {};
    const paidStatusQuery = payStatus === "UNPAID"
      ? { $or: [{ paidStatus: "UNPAID" }, { paidStatus: { $exists: false } }] }
      : payStatus ? { paidStatus: payStatus } : {};

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName = searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    let searchResult: IClientReward[] = await ClientReward.aggregate([
      {
        $match: {
          $and: [
            paidStatusQuery,
            rewardTypeQuery
          ],
        },
      },
      {
        $project: {
          transactionAmount: 1,
          accumulatedBalance: 1,
          accumulatedTotalEarnings: 1,
          accumulatedWithdrawals: 1,
          clientId: 1,
          type: 1,
          rewardType: 1,
          createdAt: 1,
          paidStatus: {
            $cond: {
              if: { $eq: ["$paidStatus", "PAID"] },
              then: "PAID",
              else: { $ifNull: ["$paidStatus", "UNPAID"] }
            }
          },
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          as: "client",
        },
      },
      {
        $unwind: {
          path: "$client",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
            $or: [
              { "client.firstname": searchedName },
              { "client.lastname": searchedName },
              { "client.email": searchedName },
              { "client.fullName": searchedName },
            ],
        },
      },
      {
        $project: {
          transactionAmount: 1,
          accumulatedBalance: 1,
          accumulatedTotalEarnings: 1,
          accumulatedWithdrawals: 1,
          clientId: 1,
          type: 1,
          rewardType: 1,
          email: "$client.email",
          firstname: "$client.firstname",
          lastname: "$client.lastname",
          fullName: {
            $concat: ["$client.firstname", " ", "$client.lastname"],
          },
          clientActiveStatus: "$client.clientActiveStatus",
          paidStatus: 1,
        },
      },
    ]).sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return searchResult;
  }

  export async function getMonthlyPaymentById(
    id: StringOrObjectId
  ): Promise<IMonthlyPayment> {
    let monthlyPayment: IMonthlyPayment = await MonthlyPayment.findById(id);

    return monthlyPayment;
  }

  export async function getTransactionById(
    id: StringOrObjectId
  ): Promise<ITransaction> {
    let transaction: ITransaction = await Transaction.findById(id);

    return transaction;
  }
  export async function updateTransaction(
    id: StringOrObjectId,
    data: Partial<ITransaction>
  ): Promise<ITransaction> {
    let transaction = await Transaction.findOneAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    );
    return transaction;
  }

  export async function deleteFlagTretmentHistory(): Promise<number | any> {
    try {
      const update = { $unset: { flag: 1 } };
      const options: mongoose.QueryOptions = {};
      await TreatmentHistory.updateMany({}, update, options);
      return "Flag removed from all TreatmentHistory documents";
    } catch (error) {
      return error;
    }
  }

  export async function getPendingSubmitionTreatmentHistoryByTherapistId(
    therapistId: Types.ObjectId,
  ): Promise<ITreatmentHistory[]> {
    try {
      const query = await TreatmentHistory.find({
        therapistId: therapistId,
        claimStatus: "PENDING_SUBMISSION",
      }).populate({
        path: 'clientId',
        select: '_id insuranceId',
        populate: {
          path: 'insuranceId',
          // model: 'Insurance',
          populate: {
            path: 'insuranceCompanyId',
            model: 'InsuranceCompany',
          }
        }
      })
        .populate({
          path: 'therapistId',
          select: '_id payRate nPI1 taxonomyCode firstname lastname',
        })
        .populate({
          path: 'diagnosisNoteId',
          select: '_id cptCode secondaryDiagnosisICDcodes diagnosisICDcodes',
        })
        .populate({
          path: 'meetingId',
          select: '_id',
        })

      const treatmentHistory = await query;
      return treatmentHistory;
    } catch (error) {

      throw new Error(
        `Failed to retrieve treatment history by therapistId: ${error}`
      );
    }
  }

  export async function getAllDiagnosisNotes(): Promise<number | any> {
    const startDate = new Date('2024-02-15');
    const endDate = new Date();
    endDate.setHours(23, 59, 59, 999);

    try {
      const transactions = await Transaction.find({}, 'meetingId');

      const transactionMeetingIds = transactions.map(transaction => transaction.meetingId);
      const response = await DiagnosisNote.find({
        updatedByTherapist: true,
        createdAt: {
          $gte: startOfDay(startDate),
          $lte: endOfDay(endDate)
        },
        meetingId: { $nin: transactionMeetingIds }
      }, 'meetingId _id createdAt')
        .populate({
          path: 'clientId',
          select: { _id: 1, lavniTestAccount: 1 },
          match: { lavniTestAccount: { $ne: true } }
        })
        .populate({
          path: 'therapistId',
          select: { _id: 1, lavniTestAccount: 1 },
          match: { lavniTestAccount: { $ne: true } }
        });

      return response;

    } catch (error) {
      console.error("Error fetching diagnosis notes:", error);
      return error;
    }
  }

  export async function getAllTherapistsList(): Promise<ITherapist[]> {
    const therapistList = await Therapist.find()
      .sort({ createdAt: -1 })
      .select('_id email firstname lastname')

    return therapistList;
  }

  export async function getFriendRequestById(
    friendRequest: Types.ObjectId
  ): Promise<IFriendRequest> {
    const response = await FriendRequest.findById(friendRequest);

    return response;
  }

  export async function getReminderSmsClientList(): Promise<IClient[]> {
    const fifteenMinutesAgo = new Date(new Date().getTime() - (15 * 60 * 1000));
    const oneHourAgo = new Date(new Date().getTime() - (60 * 60 * 1000));

    const clientsList = await Client.find({
      createdAt: {
        $gte: oneHourAgo,
        $lte: fifteenMinutesAgo
      },
      reminderSMS: { $exists: false },
    })
      .sort({ createdAt: -1 })

    return clientsList;
  }

  export async function getNewestClientList(): Promise<IClient[]> {

    const startDate = new Date('2024-05-20T00:00:00Z');

    const clientsList = await Client.find({
      createdAt: { $gt: startDate },
      missed1stAppointment: { $ne: true }
    })
      .sort({ createdAt: -1 });

    return clientsList;
  }


  export async function getPendingUpcommingAppointmentsByClientId(
    clientId: Types.ObjectId,
  ): Promise<any[]> {
    const appointmentList = await Appointment.find({
      clientId: clientId,
    })
      .populate({
        path: 'therapistId',
        select: 'firstname lastname'
      })
      .sort({ start: 1 })
      .exec();

    return appointmentList;
  }

  export async function getAllAppointmentByClientId(
    clientId: Types.ObjectId,
  ): Promise<any[]> {

    const appointmentList = await Appointment.find({
      clientId: clientId,
    })
      .populate({
        path: 'therapistId',
        select: 'firstname lastname'
      })
      .sort({ createdAt: 1 })
      .exec();

    return appointmentList;
  }

  export async function addReminderSMS(
    reminderSMSData: DReminderSMS
  ): Promise<IReminderSMS> {
    const request = new ReminderSMS(reminderSMSData);
    let response = await request.save();
    return response;
  }

  export async function getReminderSms(
    limit?: number,
    offset?: number
  ): Promise<IReminderSMS[]> {
    const reminderSms = await ReminderSMS.find()
      .populate({
        path: 'clientId',
        select: 'firstname lastname email'
      })
      .sort({ createdAt: -1 })
      .skip(offset * limit)
      .limit(limit);

    return reminderSms;
  }

  export async function getReminderSmsByClientIdWithPagination(
    limit?: number,
    offset?: number,
    clientId?: string,
  ): Promise<IReminderSMS[]> {

    const reminderSms = await ReminderSMS.find({ clientId: clientId, })
      .populate({
        path: 'clientId',
        select: 'firstname lastname email'
      })
      .sort({ createdAt: -1 })
      .skip(offset * limit)
      .limit(limit);

    return reminderSms;
  }

  export async function getReminderSmsByClientId(
    clientId: string,
  ): Promise<IReminderSMS[]> {
    const reminderSms = await ReminderSMS.find({ clientId: clientId, })
      .populate({
        path: 'clientId',
        select: 'firstname lastname email'
      })
      .sort({ createdAt: -1 });

    return reminderSms;
  }
  export async function UpdateReminderSmsByClient(
    clientId: string,
    data: Partial<DReminderSMS>
  ): Promise<IReminderSMS | null> {
    try {
      const response = await ReminderSMS.findOneAndUpdate(
        { clientId: clientId },
        { $set: data },
        { new: true }
      );

      return response;
    } catch (error) {
      console.error("Error updating reminderSMS details:", error);
      return null;
    }
  }

  export async function getReminderSmsById(
    reminderId: Types.ObjectId,
  ): Promise<IReminderSMS | null> {
    try {
      const response = await ReminderSMS.findById(reminderId);
      return response;
    } catch (error) {
      console.error("Error fetching reminderSMS details:", error);
      return null;
    }
  }

  export async function getClientById(
    clientId: Types.ObjectId,
  ): Promise<IClient | null> {
    try {
      const response = await Client.findById(clientId);

      return response;
    } catch (error) {
      console.error("Error fetching client details:", error);
      return null;
    }
  }

  export async function UpdateReminderSmsDataByID(
    reminderId: Types.ObjectId,
    data: Partial<DReminderSMS>
  ): Promise<IReminderSMS | null> {
    try {
      const response = await ReminderSMS.findOneAndUpdate(
        { _id: reminderId },
        data,
        { new: true }
      );

      return response;
    } catch (error) {
      console.error("Error updating reminderSMS details:", error);
      return null;
    }
  }


  export async function getAllInsuranceCompaniesByState(
    state: string
  ): Promise<IInsuranceCompany[]> {
    const insuranceCompanies = await InsuranceCompany.find({
      $or: [
        { states: { $in: [state] } },
        { states: { $in: ["All"] } }
      ]
    }).sort({ createdAt: -1 });
    return insuranceCompanies;
  }

  export async function addUnsubmittedClaimsMd(
    claimInformationMD: DUnsubmittedClaimMd,
    treatmentHistoryId: Types.ObjectId
  ): Promise<IUnsubmittedClaimMd> {
    claimInformationMD.status = "PENDING";
    claimInformationMD.treatmentHistoryId = treatmentHistoryId;
    const claimUpdate = new UnsubmittedClaimMd(claimInformationMD);
    let response = await claimUpdate.save();
    return response;
  }

  export async function updateUnsubmittedClaimsMd(
    id: StringOrObjectId,
    status: String
  ): Promise<IUnsubmittedClaimMd> {
    const response = await UnsubmittedClaimMd.findOneAndUpdate(
      { _id: id },
      { $set: { status: status } },
      { new: true }
    );
    return response;
  }

  export async function getAllPendingClaimMd(): Promise<IUnsubmittedClaimMd[]> {
    const oneDayTwoHoursAgo = new Date(new Date().getTime() - (1 * 24 * 60 * 60 * 1000) - (2 * 60 * 60 * 1000));
    const claims = await UnsubmittedClaimMd.find({
      status: "PENDING",
      createdAt: { $lt: oneDayTwoHoursAgo }
    })
      .sort({ createdAt: -1 });

    return claims;
  }

  export async function filterTherapistByTimeRange(
    searchableString: string,
    limit: number,
    offset: number,
    isFilterByDateRange: boolean,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string,
    startDateFromFrontEnd?: string,
    endDateFromFrontEnd?: string,
    selectDaysFromFrontEnd?: string[],
    startTimeFromFrontEnd?: string,
    endTimeFromFrontEnd?: string,
  ): Promise<any[]> {

    let selectedStartDate = moment(startDateFromFrontEnd);
    let selectedEndDate = moment(endDateFromFrontEnd);

    const usedTimeZone = 'America/New_York'

    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery =
      status != null && status ? { verifiedStatus: status } : {};
    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

    let pipeline: any[] = [];

    pipeline = [
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          zipCode: 1,
          state: 1,
          verifiedStatus: 1,
          blockedByAdmin: 1,
          profession: 1,
          lavniTestAccount: 1,
          primaryPhone: 1,
          payRateType: 1,
          _id: 1,
          priorityNumber: 1,
          workingHours: 1,
          blockedDates: 1,
        },
      },
      {
        $match: {
          $and: [
            genderQuery,
            statusQuery,
            subscriptionQuery,
            clientNameQuery,
            zipCodeQuery,
            removeInvalidTherapistsQuery,
          ],
        },
      },
      {
        $lookup: {
          from: "professions",
          localField: "profession",
          foreignField: "_id",
          pipeline: [
            {
              $project: { name: 1 },
            },
          ],
          as: "profession",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },
    ];

    // Conditionally add the appointments lookup stage
    if (startDateFromFrontEnd != undefined && endDateFromFrontEnd != undefined && isFilterByDateRange) {

      pipeline.push({
        $lookup: {
          from: "appointments",
          let: { therapistId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$therapistId", "$$therapistId"] },
                    { $gte: ["$start", selectedStartDate.toDate()] },
                    { $lte: ["$start", selectedEndDate.toDate()] }
                  ],
                },
              },
            },
            {
              $project: { _id: 1, start: 1, status: 1, clientId: 1, end: 1 },
            },
          ],
          as: "appointments",
        },
      });
    }

    pipeline.push(
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      }
    );

    if (startDateFromFrontEnd != undefined && endDateFromFrontEnd != undefined && isFilterByDateRange) {
      let searchResult = await Therapist.aggregate(pipeline)
        .sort({ createdAt: -1 })

      const convertStartTimeToDate = (startTimeFromFrontEnd == undefined || startTimeFromFrontEnd) == "" ? moment("00:00 AM", "hh:mm A") : moment(startTimeFromFrontEnd, "hh:mm A");
      const convertEndTimeToDate = (startTimeFromFrontEnd == undefined || startTimeFromFrontEnd) == "" ? moment("11:30 PM", "hh:mm A") : moment(endTimeFromFrontEnd, "hh:mm A");
      const selectedDays = selectDaysFromFrontEnd;

      const selectedTimeSlots: any[] = [];
      while (convertStartTimeToDate <= convertEndTimeToDate) {
        selectedTimeSlots.push(convertStartTimeToDate.format('HH:mm'));
        convertStartTimeToDate.add(30, 'minutes');
      }

      let dates = [];
      let localDates: any[] = [];
      for (let date = selectedStartDate.clone(); date.isBefore(selectedEndDate) || date.isSame(selectedEndDate); date.add(1, 'days')) {
        if (selectedDays.includes(date.format('dddd'))) {
          dates.push(date.clone());
        }
      }


      dates.forEach(inputDate => {
        let date = moment.utc(inputDate);
        date = date.local();
        const formattedDate = date.format('ddd MMM DD YYYY HH:mm:ss [GMT]Z');
        localDates.push(formattedDate);
      });

      const newTherapiList = searchResult.map((therapi: any) => {
        let therapistIsAvailable = false;
        const appointmentsList = therapi.appointments;
        const availableDays: { startTime: any; endTime: any; daysOfWeek: any[] }[] = [];

        const blockedDates = therapi.blockedDates;
        therapi?.workingHours?.map((obj: any) => {
          if (obj) {
            const dayAsNumber = dayOfWeekAsNumber(obj.day);

            if (convertUTCDateToLocalDate(obj.endTime) > convertUTCDateToLocalDate(obj.startTime)) {
              return availableDays.push({
                startTime: convertUTCDateToLocalDate(obj.startTime),
                endTime: convertUTCDateToLocalDate(obj.endTime),
                daysOfWeek: [dayAsNumber],
              });
            } else {
              availableDays.push({
                startTime: convertUTCDateToLocalDate(obj.startTime),
                endTime: "24:00 AM",
                daysOfWeek: [dayAsNumber],
              });

              return availableDays.push({
                startTime: "00:00 AM",
                endTime: convertUTCDateToLocalDate(obj.endTime),
                daysOfWeek: dayAsNumber < 6 ? [dayAsNumber + 1] : [0],
              });
            }
          }
        })

        for (const localDay of localDates) {

          const checkAvailability = checkForAvailableDateFunction(localDay, appointmentsList, blockedDates, availableDays, selectedTimeSlots, usedTimeZone);

          if (checkAvailability) {
            therapistIsAvailable = true;
          }
        }

        return {
          ...therapi,
          therapistIsAvailable
        };
      })

      const finalTherapiList = newTherapiList.filter((therapist: any) => therapist.therapistIsAvailable);
      const startIndex = limit * (offset - 1);
      let endIndex = (limit * (offset - 1)) + limit
      if (endIndex > finalTherapiList.length) {
        endIndex = finalTherapiList.length;
      }
      return finalTherapiList.slice(startIndex, endIndex);
    } else {
      let searchResult = await Therapist.aggregate(pipeline)
        .sort({ createdAt: -1 })
        .skip(limit * (offset - 1))
        .limit(limit)
        .exec();
      return searchResult;
    }

  }

  function checkForAvailableDateFunction(
    calendarDate: Date,
    prevAppointmentsInFu: any[],
    prevBlockedDatesListInFu: any[],
    availableSlotsFromInFu: any[],
    selectedTimeSlots: any[],
    usedTimeZone: string
  ): boolean {
    try {
      if (prevAppointmentsInFu && availableSlotsFromInFu && availableSlotsFromInFu.length && availableSlotsFromInFu.length > 0 && calendarDate) {
        const selectedDyOfWeek = (new Date(calendarDate)).getDay();


        const availableHours: string[] = [];
        availableSlotsFromInFu
          .filter((obj: any) => obj.daysOfWeek[0] === selectedDyOfWeek)
          .map((obj: any) => {

            for (let hour = parseInt(obj.startTime); hour <= parseInt(obj.endTime); hour++) {
              if (hour == parseInt(obj.startTime)) {
                const mST = moment(obj.startTime, "HH:mm").minute();

                if (mST == 0) {
                  availableHours.push(moment({ hour }).format("H:mm A"));
                }
              } else {
                availableHours.push(moment({ hour }).format("H:mm A"));
              }

              if (hour != parseInt(obj.endTime)) {
                availableHours.push(
                  moment({
                    hour,
                    minute: 30,
                  }).format("H:mm A")
                );
              } else {
                const mET = moment(obj.endTime, "HH:mm").minute();

                if (mET == 30) {
                  availableHours.push(
                    moment({
                      hour,
                      minute: 30,
                    }).format("H:mm A")
                  );
                }
              }
            }
          });

        let allAvailableSlotsForSelectedDateOld: any[] = [];

        availableHours.map((slot: string, i: number) => {
          const startTimeString = slot;
          const startTimeStringTime = moment(startTimeString, "HH:mm A");
          const selectedStartDateTime = moment(calendarDate)
            .hours(startTimeStringTime.hours())
            .minutes(startTimeStringTime.minutes())
            .seconds(0)
            .format("YYYY-MM-DD HH:mm:ss");

          const convertedStartDate = moment.utc(selectedStartDateTime, "YYYY-MM-DD HH:mm:ss");

          // Convert the UTC time to America/New_York time zone
          const easternTime = convertedStartDate.tz(usedTimeZone).toDate();

          // Create moments for checking
          const startTForCheck = moment(easternTime);
          const endTForCheck = moment(startTForCheck).add(60, "minutes");


          allAvailableSlotsForSelectedDateOld.push({ startTime: easternTime });

        });

        const selectedTimeSlotsArray: any[] = []
        const selectedDate = moment(calendarDate).format('YYYY-MM-DD');

        selectedTimeSlots.map(time => {
          const dateTimeStr = `${selectedDate} ${time}`;
          const utcTime = moment.utc(dateTimeStr, 'YYYY-MM-DD HH:mm:ss');
          const easternTime = utcTime.tz(usedTimeZone);
          const formattedEasternTime = easternTime.format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
          const formattedUtcTime = easternTime.toDate();
          // const dateTime = moment(`${selectedDate} ${time}`, 'YYYY-MM-DD HH:mm:ss');
          // const easternTime = moment.tz(dateTimeStr, 'America/New_York');
          // const utcTime = easternTime.utc();
          // const formattedUtcTimeStr = utcTime.format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
          // const formattedUtcTime = moment(formattedUtcTimeStr).toDate();
          selectedTimeSlotsArray.push({ startTime: formattedUtcTime });
        });

        let allAvailableSlotsForSelectedDate = selectedTimeSlotsArray.filter(item =>
          allAvailableSlotsForSelectedDateOld.some(oldItem => oldItem.startTime.toString() == item.startTime.toString())
        );

        if (allAvailableSlotsForSelectedDate.length == 1) {
          allAvailableSlotsForSelectedDate = [];
        }

        allAvailableSlotsForSelectedDate = allAvailableSlotsForSelectedDate.filter(
          (slot, index, self) => index === self.findIndex(s => moment(s.startTime).valueOf() === moment(slot.startTime).valueOf())
        );

        allAvailableSlotsForSelectedDate = allAvailableSlotsForSelectedDate.filter(slot => {
          const isInBlockedRange = prevBlockedDatesListInFu?.some((range: any) => {
            const startTimeMoment = moment.parseZone(range.start);
            const startUtcTime = startTimeMoment.utc();
            const startNyTime = startUtcTime.clone().tz(usedTimeZone);

            const endTimeMoment = moment.parseZone(range.end);
            const endUtcTime = endTimeMoment.utc();
            const endNyTime = endUtcTime.clone().tz(usedTimeZone);
            return isDateInBlockedRange(startNyTime, endNyTime, moment.utc(slot.startTime));
          });
          if (isInBlockedRange) {
            return false;
          }

          const appointmentAlreadyExists = prevAppointmentsInFu?.some((range: any) => {
            const startInNewYork = moment(range.start).tz(usedTimeZone);
            const endInNewYork = moment(range.end).tz(usedTimeZone);
            const slotStartTimeInNewYork = moment(slot.startTime).tz(usedTimeZone);

            return isDateInBlockedRange(startInNewYork, endInNewYork, moment.utc(slot.startTime));
          });
          return !isInBlockedRange && !appointmentAlreadyExists;
        });
        allAvailableSlotsForSelectedDate.sort((slot1, slot2) => {
          return moment(slot1.startTime).valueOf() - moment(slot2.startTime).valueOf();
        });

        if (allAvailableSlotsForSelectedDate && allAvailableSlotsForSelectedDate.length && allAvailableSlotsForSelectedDate.length > 0) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  }

  const convertUTCDateToLocalDate = (dateString: string) => {
    if (dateString) {
      const getHour = dateString.split(":")[0];
      const getMinute = dateString.split(":")[1];

      const UTC = moment.utc().hour(parseInt(getHour)).minute(parseInt(getMinute)).second(0);

      const localTime = UTC.tz('America/New_York');

      return localTime.format('HH:mm A');
    } else {
      return "";
    }
  }

  const dayOfWeekAsNumber = (day: string) => {
    return ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"].indexOf(day);
  };

  const isDateInBlockedRange = (startDate: moment.Moment, endDate: moment.Moment, checkDate: moment.Moment) => {
    startDate = moment(startDate.add(-0.5, "hours").format("YYYY/MM/DD hh:mm A"));
    endDate = moment(endDate.format("YYYY/MM/DD hh:mm A"));
    checkDate = moment(checkDate.format("YYYY/MM/DD hh:mm A"));

    return startDate <= checkDate && checkDate <= endDate;
  }


  export async function addERAClaimsMd(
    eraClaimInformationMD: DERAClaimMdSchema,
  ): Promise<IERAClaimMdSchema> {
    eraClaimInformationMD.status = "PENDING";
    const claimUpdate = new ERAClaimMd(eraClaimInformationMD);
    let response = await claimUpdate.save();
    return response;
  }

  export async function addERAClaimDataMd(
    eraClaimInformationMD: DERAClaimMdListSchema,
  ): Promise<IERAClaimMdListSchema> {
    eraClaimInformationMD.status = "PENDING";
    const claimUpdate = new ERAClaimMdList(eraClaimInformationMD);
    let response = await claimUpdate.save();
    return response;
  }


  export async function getLastERAClaim(): Promise<IERAClaimMdSchema> {
    const lastERAClaim = await ERAClaimMd.findOne().sort({ _id: -1 }).limit(1);
    return lastERAClaim;
  }


  export async function getAllERAPendingClaims(
  ): Promise<IERAClaimMdSchema[]> {
    const eraPendingClaims = await ERAClaimMd.find(
      { status: "PENDING" }
    ).sort({ createdAt: -1 })
    return eraPendingClaims;
  }

  export async function getAllERAPendingClaimLists(
  ): Promise<IERAClaimMdListSchema[]> {
    const eraPendingClaims = await ERAClaimMdList.find(
      { status: "PENDING" }
    ).sort({ createdAt: -1 })
    return eraPendingClaims;
  }


  export async function findAndUpdateTreatmentHistory(
    eraListId: StringOrObjectId,
    pcn: string,
    pat_name_f: string,
    pat_name_l: string,
    from_dos: string,
    claim_received_date: string,
    total_paid: string,
    adjustmentCode: number,
    eraid: StringOrObjectId,
  ): Promise<ITreatmentHistory[]> {

    const ClaimErrorList = {
      1: 'Deductible Amount',
      2: 'Coinsurance Amount',
      3: 'Co-payment Amount',
      4: 'The procedure code is inconsistent with the modifier used. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',

      5: 'The procedure code/type of bill is inconsistent with the place of service. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      6: 'The procedure/revenue code is inconsistent with the patients age. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present ',
      7: 'The procedure/revenue code is inconsistent with the patients gender. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present ',
      8: 'The procedure code is inconsistent with the provider type/specialty (taxonomy). Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',

      9: 'The diagnosis is inconsistent with the patients age. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      10: 'The diagnosis is inconsistent with the patients gender. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      11: 'The diagnosis is inconsistent with the procedure. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      12: 'The diagnosis is inconsistent with the provider type. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      13: 'The date of death precedes the date of service',
      14: 'The date of birth follows the date of service',
      16: 'Claim/service lacks information or has submission/billing error(s). Usage: Do not use this code for claims attachment(s)/other documentation. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.) Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      18: 'Exact duplicate claim/service (Use only with Group Code OA except where state workers compensation regulations requires CO)',
      19: 'This is a work-related injury/illness and thus the liability of the Workers Compensation Carrier',
      20: 'This injury/illness is covered by the liability carrier',
      21: 'This injury/illness is the liability of the no-fault carrier',
      22: 'This care may be covered by another payer per coordination of benefits',
      23: 'The impact of prior payer(s) adjudication including payments and/or adjustments. (Use only with Group Code OA)',
      24: 'Charges are covered under a capitation agreement/managed care plan',
      26: 'Expenses incurred prior to coverage',
      27: 'Expenses incurred after coverage terminated',
      29: 'The time limit for filing has expired',
      31: 'Patient cannot be identified as our insured',
      32: 'Our records indicate the patient is not an eligible dependent',
      33: 'Insured has no dependent coverage',
      34: 'Insured has no coverage for newborns',
      35: 'Lifetime benefit maximum has been reached',
      39: 'Services denied at the time authorization/pre-certification was requested',
      40: 'Charges do not meet qualifications for emergent/urgent care. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      44: 'Prompt-pay discount',
      45: 'Charge exceeds fee schedule/maximum allowable or contracted/legislated fee arrangement. Usage: This adjustment amount cannot equal the total service or claim charge amount; and must not duplicate provider adjustment amounts (payments and contractual reductions) that have resulted from prior payer(s) adjudication. (Use only with Group Codes PR or CO depending upon liability)',
      49: 'This is a non-covered service because it is a routine/preventive exam or a diagnostic/screening procedure done in conjunction with a routine/preventive exam. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      50: 'These are non-covered services because this is not deemed a medical necessity by the payer. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      51: 'These are non-covered services because this is a pre-existing condition. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      53: 'Services by an immediate relative or a member of the same household are not covered',
      54: 'Multiple physicians/assistants are not covered in this case. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      55: 'Procedure/treatment/drug is deemed experimental/investigational by the payer. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      56: 'Procedure/treatment has not been deemed proven to be effective by the payer. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      58: 'Treatment was deemed by the payer to have been rendered in an inappropriate or invalid place of service. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      59: 'Processed based on multiple or concurrent procedure rules. (For example multiple surgery or diagnostic imaging, concurrent anesthesia.). Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      60: 'Charges for outpatient services are not covered when performed within a period of time prior to or after inpatient services',
      61: 'Adjusted for failure to obtain second surgical opinion',
      66: 'Blood Deductible',
      69: 'Day outlier amount',
      70: 'Cost outlier - Adjustment to compensate for additional costs',
      74: 'Indirect Medical Education Adjustment',
      75: 'Direct Medical Education Adjustment',
      76: 'Disproportionate Share Adjustment',
      78: 'Non-Covered days/Room charge adjustment',
      85: 'Patient Interest Adjustment (Use Only Group code PR)',
      89: 'Professional fees removed from charges',
      90: 'Ingredient cost adjustment. Usage: To be used for pharmaceuticals only',
      91: 'Dispensing fee adjustment',
      94: 'Processed in Excess of charges',
      95: 'Plan procedures not followed',
      96: 'Non-covered charge(s). At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.). Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      97: 'The benefit for this service is included in the payment/allowance for another service/procedure that has already been adjudicated. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      100: 'Payment made to patient/insured/responsible party',
      101: 'Predetermination: anticipated payment upon completion of services or claim adjudication',
      102: 'Major Medical Adjustment',
      103: 'Provider promotional discount (e.g., Senior citizen discount)',
      104: 'Managed care withholding',
      105: 'Tax withholding',
      106: 'Patient payment option/election not in effect',
      107: 'The related or qualifying claim/service was not identified on this claim. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      108: 'Rent/purchase guidelines were not met. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      109: 'Claim/service not covered by this payer/contractor. You must send the claim/service to the correct payer/contractor',
      110: 'Billing date predates service date',
      111: 'Not covered unless the provider accepts assignment',
      112: 'Service not furnished directly to the patient and/or not documented',
      114: 'Procedure/product not approved by the Food and Drug Administration',
      115: 'Procedure postponed, canceled, or delayed',
      116: 'The advance indemnification notice signed by the patient did not comply with requirements',
      117: 'Transportation is only covered to the closest facility that can provide the necessary care',
      118: 'ESRD network support adjustment',
      119: 'Benefit maximum for this time period or occurrence has been reached',
      121: 'Indemnification adjustment - compensation for outstanding member responsibility',
      122: 'Psychiatric reduction',
      128: 'Newborns services are covered in the mothers Allowance',
      129: 'Prior processing information appears incorrect. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)',
      130: 'Claim submission fee',
      131: 'Claim specific negotiated discount',
      132: 'Prearranged demonstration project adjustment',
      133: 'The disposition of this service line is pending further review. (Use only with Group Code OA). Usage: Use of this code requires a reversal and correction when the service line is finalized (use only in Loop 2110 CAS segment of the 835 or Loop 2430 of the 837)',
      134: 'Technical fees removed from charges',
      135: 'Interim bills cannot be processed',
      136: 'Failure to follow prior payers coverage rules. (Use only with Group Code OA)',
      137: 'Regulatory Surcharges, Assessments, Allowances or Health Related Taxes',
      139: 'Contracted funding agreement - Subscriber is employed by the provider of services. Use only with Group Code CO',
      140: 'Patient/Insured health identification number and name do not match',
      142: 'Monthly Medicaid patient liability amount',
      143: 'Portion of payment deferred',
      144: 'Incentive adjustment, e.g. preferred product/service',
      146: 'Diagnosis was invalid for the date(s) of service reported',
      147: 'Provider contracted/negotiated rate expired or not on file',
      148: 'Information from another provider was not provided or was insufficient/incomplete. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)',
      149: 'Lifetime benefit maximum has been reached for this service/benefit category',
      150: 'Payer deems the information submitted does not support this level of service',
      151: 'Payment adjusted because the payer deems the information submitted does not support this many/frequency of services',
      152: 'Payer deems the information submitted does not support this length of service. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      153: 'Payer deems the information submitted does not support this dosage',
      154: 'Payer deems the information submitted does not support this days supply',
      155: 'Patient refused the service/procedure',
      157: 'Service/procedure was provided as a result of an act of war',
      158: 'Service/procedure was provided outside of the United States',
      159: 'Service/procedure was provided as a result of terrorism',
      160: 'Injury/illness was the result of an activity that is a benefit exclusion',
      161: 'Provider performance bonus',
      163: 'Attachment/other documentation referenced on the claim was not received',
      164: 'Attachment/other documentation referenced on the claim was not received in a timely fashion',
      166: 'These services were submitted after this payers responsibility for processing claims under this plan ended',
      167: 'This (these) diagnosis(es) is (are) not covered. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present ',
      169: 'Alternate benefit has been provided',
      170: 'Payment is denied when performed/billed by this type of provider. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      171: 'Payment is denied when performed/billed by this type of provider in this type of facility. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      172: 'Payment is adjusted when performed/billed by a provider of this specialty. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      173: 'Service/equipment was not prescribed by a physician',
      174: 'Service was not prescribed prior to delivery',
      175: 'Prescription is incomplete',
      176: 'Prescription is not current',
      177: 'Patient has not met the required eligibility requirements',
      178: 'Patient has not met the required spend down requirements',
      179: 'Patient has not met the required waiting requirements. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      180: 'Patient has not met the required residency requirements',
      181: 'Procedure code was invalid on the date of service',
      182: 'Procedure modifier was invalid on the date of service',
      183: 'The referring provider is not eligible to refer the service billed. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      184: 'The prescribing/ordering provider is not eligible to prescribe/order the service billed. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      185: 'The rendering provider is not eligible to perform the service billed. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      186: 'Level of care change adjustment',
      187: 'Consumer Spending Account payments (includes but is not limited to Flexible Spending Account, Health Savings Account, Health Reimbursement Account, etc.)',
      188: 'This product/procedure is only covered when used according to FDA recommendations',
      189: 'Not otherwise classified or unlisted procedure code (CPT/HCPCS) was billed when there is a specific procedure code for this procedure/service',
      190: 'Payment is included in the allowance for a Skilled Nursing Facility (SNF) qualified stay',
      192: 'Non standard adjustment code from paper remittance. Usage: This code is to be used by providers/payers providing Coordination of Benefits information to another payer in the 837 transaction only. This code is only used when the non-standard code cannot be reasonably mapped to an existing Claims Adjustment Reason Code, specifically Deductible, Coinsurance and Co-payment',
      193: 'Original payment decision is being maintained. Upon review, it was determined that this claim was processed properly',
      194: 'Anesthesia performed by the operating physician, the assistant surgeon or the attending physician',
      195: 'Refund issued to an erroneous priority payer for this claim/service',
      197: 'Precertification/authorization/notification/pre-treatment absent',
      198: 'Precertification/notification/authorization/pre-treatment exceeded',
      199: 'Revenue code and Procedure code do not match',
      200: 'Expenses incurred during lapse in coverage',
      201: 'Patient is responsible for amount of this claim/service through set aside arrangement or other agreement. (Use only with Group Code PR) At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.) ',
      202: 'Non-covered personal comfort or convenience services',
      203: 'Discontinued or reduced service',
      204: 'This service/equipment/drug is not covered under the patients current benefit plan',
      205: 'Pharmacy discount card processing fee',
      206: 'National Provider Identifier - missing',
      207: 'National Provider identifier - Invalid format',
      208: 'National Provider Identifier - Not matched',
      209: 'Per regulatory or other agreement. The provider cannot collect this amount from the patient. However, this amount may be billed to subsequent payer. Refund to patient if collected. (Use only with Group code OA)',
      210: 'Payment adjusted because pre-certification/authorization not received in a timely fashion',
      211: 'National Drug Codes (NDC) not eligible for rebate, are not covered',
      212: 'Administrative surcharges are not covered',
      213: 'Non-compliance with the physician self referral prohibition legislation or payer policy',
      215: 'Based on subrogation of a third party settlement',
      216: 'Based on the findings of a review organization',
      219: 'Based on extent of injury. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF)',
      222: 'Exceeds the contracted maximum number of hours/days/units by this provider for this period. This is not patient specific. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      223: 'Adjustment code for mandated federal, state or local law/regulation that is not already covered by another code and is mandated before a new code can be created',
      224: 'Patient identification compromised by identity theft. Identity verification required for processing this and future claims',
      225: 'Penalty or Interest Payment by Payer (Only used for plan to plan encounter reporting within the 837)',
      226: 'Information requested from the Billing/Rendering Provider was not provided or not provided timely or was insufficient/incomplete. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.) ',
      227: 'Information requested from the patient/insured/responsible party was not provided or was insufficient/incomplete. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)',
      228: 'Denied for failure of this provider, another provider or the subscriber to supply requested information to a previous payer for their adjudication',
      229: 'Partial charge amount not considered by Medicare due to the initial claim Type of Bill being 12X. Usage: This code can only be used in the 837 transaction to convey Coordination of Benefits information when the secondary payers cost avoidance policy allows providers to bypass claim submission to a prior payer. (Use only with Group Code PR)',
      231: 'Mutually exclusive procedures cannot be done in the same day/setting. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      232: 'Institutional Transfer Amount. Usage: Applies to institutional claims only and explains the DRG amount difference when the patient care crosses multiple institutions',
      233: 'Services/charges related to the treatment of a hospital-acquired condition or preventable medical error',
      234: 'This procedure is not paid separately. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)',
      235: 'Sales Tax',
      236: 'This procedure or procedure/modifier combination is not compatible with another procedure or procedure/modifier combination provided on the same day according to the National Correct Coding Initiative or workers compensation state regulations/ fee schedule requirements',
      237: 'Legislated/Regulatory Penalty. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)',
      238: 'Claim spans eligible and ineligible periods of coverage, this is the reduction for the ineligible period. (Use only with Group Code PR)',
      239: 'Claim spans eligible and ineligible periods of coverage. Rebill separate claims',
      240: 'The diagnosis is inconsistent with the patients birth weight. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      241: 'Low Income Subsidy (LIS) Co-payment Amount',
      242: 'Services not provided by network/primary care providers',
      243: 'Services not authorized by network/primary care providers',
      245: 'Provider performance program withhold',
      246: 'This non-payable code is for required reporting only',
      247: 'Deductible for Professional service rendered in an Institutional setting and billed on an Institutional claim',
      248: 'Coinsurance for Professional service rendered in an Institutional setting and billed on an Institutional claim',
      249: 'This claim has been identified as a readmission. (Use only with Group Code CO)',
      250: 'The attachment/other documentation that was received was the incorrect attachment/document. The expected attachment/document is still missing. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT)',
      251: 'The attachment/other documentation that was received was incomplete or deficient. The necessary information is still needed to process the claim. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT)',
      252: 'An attachment/other documentation is required to adjudicate this claim/service. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT)',
      253: 'Sequestration - reduction in federal payment',
      254: 'Claim received by the dental plan, but benefits not available under this plan. Submit these services to the patients medical plan for further consideration',
      256: 'Service not payable per managed care contract',
      257: 'The disposition of the claim/service is undetermined during the premium payment grace period, per Health Insurance Exchange requirements. This claim/service will be reversed and corrected when the grace period ends (due to premium payment or lack of premium payment). (Use only with Group Code OA)',
      258: 'Claim/service not covered when patient is in custody/incarcerated. Applicable federal, state or local authority may cover the claim/service',
      259: 'Additional payment for Dental/Vision service utilization',
      260: 'Processed under Medicaid ACA Enhanced Fee Schedule',
      261: 'The procedure or service is inconsistent with the patients history',
      262: 'Adjustment for delivery cost. Usage: To be used for pharmaceuticals only',
      263: 'Adjustment for shipping cost. Usage: To be used for pharmaceuticals only',
      264: 'Adjustment for postage cost. Usage: To be used for pharmaceuticals only',
      265: 'Adjustment for administrative cost. Usage: To be used for pharmaceuticals only',
      266: 'Adjustment for compound preparation cost. Usage: To be used for pharmaceuticals only',
      267: 'Claim/service spans multiple months. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)',
      268: 'The Claim spans two calendar years. Please resubmit one claim per calendar year',
      269: 'Anesthesia not covered for this service/procedure. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      270: 'Claim received by the medical plan, but benefits not available under this plan. Submit these services to the patients dental plan for further consideration',
      271: 'Prior contractual reductions related to a current periodic payment as part of a contractual payment schedule when deferred amounts have been previously reported. (Use only with Group Code OA)',
      272: 'Coverage/program guidelines were not met',
      273: 'Coverage/program guidelines were exceeded',
      274: 'Fee/Service not payable per patient Care Coordination arrangement',
      275: 'Prior payers (or payers) patient responsibility (deductible, coinsurance, co-payment) not covered. (Use only with Group Code PR)',
      276: 'Services denied by the prior payer(s) are not covered by this payer',
      277: 'The disposition of the claim/service is undetermined during the premium payment grace period, per Health Insurance SHOP Exchange requirements. This claim/service will be reversed and corrected when the grace period ends (due to premium payment or lack of premium payment). (Use only with Group Code OA)',
      278: 'Performance program proficiency requirements not met. (Use only with Group Codes CO or PI). Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      279: 'Services not provided by Preferred network providers. Usage: Use this code when there are member network limitations. For example, using contracted providers not in the members narrow network',
      280: 'Claim received by the medical plan, but benefits not available under this plan. Submit these services to the patients Pharmacy plan for further consideration',
      281: 'Deductible waived per contractual agreement. Use only with Group Code CO',
      282: 'The procedure/revenue code is inconsistent with the type of bill. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      283: 'Attending provider is not eligible to provide direction of care',
      284: 'Precertification/authorization/notification/pre-treatment number may be valid but does not apply to the billed services',
      285: 'Appeal procedures not followed',
      286: 'Appeal time limits not met',
      287: 'Referral exceeded',
      288: 'Referral absent',
      289: 'Services considered under the dental and medical plans, benefits not available',
      290: 'Claim received by the dental plan, but benefits not available under this plan. Claim has been forwarded to the patients medical plan for further consideration',
      291: 'Claim received by the medical plan, but benefits not available under this plan. Claim has been forwarded to the patients dental plan for further consideration',
      292: 'Claim received by the medical plan, but benefits not available under this plan. Claim has been forwarded to the patients pharmacy plan for further consideration',
      293: 'Payment made to employer',
      294: 'Payment made to attorney',
      295: 'Pharmacy Direct/Indirect Remuneration (DIR)',
      296: 'Precertification/authorization/notification/pre-treatment number may be valid but does not apply to the provider',
      297: 'Claim received by the medical plan, but benefits not available under this plan. Submit these services to the patients vision plan for further consideration',
      298: 'Claim received by the medical plan, but benefits not available under this plan. Claim has been forwarded to the patients vision plan for further consideration',
      299: 'The billing provider is not eligible to receive payment for the service billed',
      300: 'Claim received by the Medical Plan, but benefits not available under this plan. Claim has been forwarded to the patients Behavioral Health Plan for further consideration',
      301: 'Claim received by the Medical Plan, but benefits not available under this plan. Submit these services to the patients Behavioral Health Plan for further consideration',
      302: 'Precertification/notification/authorization/pre-treatment time limit has expired',
      303: 'Prior payers (or payers) patient responsibility (deductible, coinsurance, co-payment) not covered for Qualified Medicare and Medicaid Beneficiaries. (Use only with Group Code CO)',
      304: 'Claim received by the medical plan, but benefits not available under this plan. Submit these services to the patients hearing plan for further consideration',
      305: 'Claim received by the medical plan, but benefits not available under this plan. Claim has been forwarded to the patients hearing plan for further consideration',
      306: 'Type of bill is inconsistent with the patient status. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      'A0': 'Patient refund amount',
      'A1': 'Claim/Service denied. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.) Usage: Use this code only when a more specific Claim Adjustment Reason Code is not available',
      'A5': 'Medicare Claim PPS Capital Cost Outlier Amount',
      'A6': 'Prior hospitalization or 30 day transfer requirement not met',
      'A8': 'Ungroupable DRG',
      'B1': 'Non-covered visits',
      'B4': 'Late filing penalty',
      'B7': 'This provider was not certified/eligible to be paid for this procedure/service on this date of service. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      'B8': 'Alternative services were available, and should have been utilized. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      'B9': 'Patient is enrolled in a Hospice',
      'B10': 'Allowed amount has been reduced because a component of the basic procedure/test was paid. The beneficiary is not liable for more than the charge limit for the basic procedure/test',
      'B11': 'The claim/service has been transferred to the proper payer/processor for processing. Claim/service not covered by this payer/processor',
      'B12': 'Services not documented in patients medical records',
      'B13': 'Previously paid. Payment for this claim/service may have been provided in a previous payment',
      'B14': 'Only one visit or consultation per physician per day is covered',
      'B15': 'This service/procedure requires that a qualifying service/procedure be received and covered. The qualifying other service/procedure has not been received/adjudicated. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present',
      'B16': 'New Patient qualifications were not met',
      'B20': 'Procedure/service was partially or fully furnished by another provider',
      'B22': 'This payment is adjusted based on the diagnosis',
      'B23': 'Procedure billed is not authorized per your Clinical Laboratory Improvement Amendment (CLIA) proficiency test',
      'P1': 'State-mandated Requirement for Property and Casualty, see Claim Payment Remarks Code for specific explanation. To be used for Property and Casualty only',
      'P2': 'Not a work related injury/illness and thus not the liability of the workers compensation carrier. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF). To be used for Workers Compensation only',
      'P3': 'Workers Compensation case settled. Patient is responsible for amount of this claim/service through WC Medicare set aside arrangement or other agreement. To be used for Workers Compensation only. (Use only with Group Code PR)',
      'P4': 'Workers Compensation claim adjudicated as non-compensable. This Payer not liable for claim or service/treatment. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF). To be used for Workers Compensation only',
      'P5': 'Based on payer reasonable and customary fees. No maximum allowable defined by legislated fee arrangement. To be used for Property and Casualty only',
      'P6': 'Based on entitlement to benefits. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF). To be used for Property and Casualty only',
      'P7': 'The applicable fee schedule/fee database does not contain the billed code. Please resubmit a bill with the appropriate fee schedule/fee database code(s) that best describe the service(s) provided and supporting documentation if required. To be used for Property and Casualty only',
      'P8': 'Claim is under investigation. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF). To be used for Property and Casualty only',
      'P9': 'No available or correlating CPT/HCPCS code to describe this service. To be used for Property and Casualty only',
      'P10': 'Payment reduced to zero due to litigation. Additional information will be sent following the conclusion of litigation. To be used for Property and Casualty only',
      'P11': 'The disposition of the related Property & Casualty claim (injury or illness) is pending due to litigation. To be used for Property and Casualty only. (Use only with Group Code OA)',
      'P12': 'Workers compensation jurisdictional fee schedule adjustment. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Workers Compensation only',
      'P13': 'Payment reduced or denied based on workers compensation jurisdictional regulations or payment policies, use only if no other code is applicable. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Workers Compensation only',
      'P14': 'The Benefit for this Service is included in the payment/allowance for another service/procedure that has been performed on the same day. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present. To be used for Property and Casualty only',
      'P15': 'Workers Compensation Medical Treatment Guideline Adjustment. To be used for Workers Compensation only',
      'P16': 'Medical provider not authorized/certified to provide treatment to injured workers in this jurisdiction. To be used for Workers Compensation only. (Use with Group Code CO or OA)',
      'P17': 'Referral not authorized by attending physician per regulatory requirement. To be used for Property and Casualty only',
      'P18': 'Procedure is not listed in the jurisdiction fee schedule. An allowance has been made for a comparable service. To be used for Property and Casualty only',
      'P19': 'Procedure has a relative value of zero in the jurisdiction fee schedule, therefore no payment is due. To be used for Property and Casualty only',
      'P20': 'Service not paid under jurisdiction allowed outpatient facility fee schedule. To be used for Property and Casualty only',
      'P21': 'Payment denied based on the Medical Payments Coverage (MPC) and/or Personal Injury Protection (PIP) Benefits jurisdictional regulations, or payment policies. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only',
      'P22': 'Payment adjusted based on the Medical Payments Coverage (MPC) and/or Personal Injury Protection (PIP) Benefits jurisdictional regulations, or payment policies. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only',
      'P23': 'Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional fee schedule adjustment. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only',
      'P24': 'Payment adjusted based on Preferred Provider Organization (PPO). Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty only. Use only with Group Code CO',
      'P25': 'Payment adjusted based on Medical Provider Network (MPN). Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty only. (Use only with Group Code CO)',
      'P26': 'Payment adjusted based on Voluntary Provider network (VPN). Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty only. (Use only with Group Code CO)',
      'P27': 'Payment denied based on the Liability Coverage Benefits jurisdictional regulations and/or payment policies. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only',
      'P28': 'Payment adjusted based on the Liability Coverage Benefits jurisdictional regulations and/or payment policies. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier IG) if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only',
      'P29': 'Liability Benefits jurisdictional fee schedule adjustment. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only',
      'P30': 'Payment denied for exacerbation when supporting documentation was not complete. To be used for Property and Casualty only',
      'P31': 'Payment denied for exacerbation when treatment exceeds time allowed. To be used for Property and Casualty only',
      'P32': 'Payment adjusted due to Apportionment',
      } as const;




    const adjustmentValueKey = adjustmentCode as keyof typeof ClaimErrorList;
    const adjustmentValue = ClaimErrorList[adjustmentValueKey];

    const response = await TreatmentHistory.aggregate([
      {
        $facet: {
          pcnMatch: [
            {
              $match: {
                pcn: { $regex: new RegExp(`^${pcn}$`, 'i') }
              }
            }
          ],
          dateNameMatch: [
            {
              $match: {
                $expr: {
                  $or: [
                    {
                      $eq: [
                        { $dateToString: { format: "%Y-%m-%d", date: "$meetingStartedTime" } },
                        from_dos
                      ]
                    },
                    {
                      $eq: [
                        { $dateToString: { format: "%Y-%m-%d", date: "$claimSubmittedTime" } },
                        claim_received_date
                      ]
                    }
                  ]
                }
              }
            },
            {
              $lookup: {
                from: "users",
                localField: 'clientId',
                foreignField: '_id',
                as: 'user_info'
              }
            },
            {
              $unwind: '$user_info'
            },
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $regexMatch: {
                        input: { $trim: { input: "$user_info.firstname" } },
                        regex: new RegExp(`^${pat_name_f}$`, 'i')
                      }
                    },
                    {
                      $regexMatch: {
                        input: { $trim: { input: "$user_info.lastname" } },
                        regex: new RegExp(`^${pat_name_l}$`, 'i')
                      }
                    }
                  ]
                }
              }
            }
          ]
        }
      },
      {
        $project: {
          result: {
            $cond: {
              if: { $gt: [{ $size: "$pcnMatch" }, 0] },
              then: "$pcnMatch",
              else: "$dateNameMatch"
            }
          }
        }
      },
      {
        $unwind: "$result"
      },
      {
        $replaceRoot: { newRoot: "$result" }
      }
    ]);
    if (response.length > 0) {
      const claimStatus = Number(total_paid) > 0 ? "PAIDMD" : "UNPAIDMD";
      const eraStatus = Number(total_paid) > 0 ? "COMPLETED" : "FAILED";

      interface UpdateFields {
        claimStatus: string;
        paidAmount: string;
        errorMsg?: string;
      }

      const updateFields: UpdateFields = {
        claimStatus,
        paidAmount: total_paid
      };

      if (claimStatus == "UNPAIDMD") {
        updateFields.errorMsg = adjustmentValue;
      }

      const updatedHistory = await TreatmentHistory.findByIdAndUpdate(
        response[0]._id,
        { $set: updateFields },
        { new: true }
      );

      // Update ERAClaimMd
      const updatedERAStatus = await ERAClaimMd.findByIdAndUpdate(
        eraid,
        { $set: { status: eraStatus } },
        { new: true }
      );

      // Update ERAClaimMdList
      const updatedERAListStatus = await ERAClaimMdList.findByIdAndUpdate(
        eraListId,
        { $set: { status: eraStatus, eraClaimId: eraid } },
        { new: true }
      );
    } else {
      // Update ERAClaimMd if there's no response
      const updatedERAStatus = await ERAClaimMd.findByIdAndUpdate(
        eraid,
        { $set: { status: "FAILED" } },
        { new: true }
      );

      // Update ERAClaimMdList if there's no response
      const updatedERAListStatus = await ERAClaimMdList.findByIdAndUpdate(
        eraListId,
        { $set: { status: "FAILED", eraClaimId: eraid } },
        { new: true }
      );
    }

    return response;
  }

  export async function getAllERALists(
    claimStatus: string,
    limit?: number,
    offset?: number
  ): Promise<IERAClaimMdListSchema[]> {
    const query = claimStatus !== "ALL" ? { status: claimStatus } : {};
    const eraPendingClaims = await ERAClaimMdList.find(query).sort({ _id: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);
    return eraPendingClaims;
  }

  export async function getRecentSessionsForSelectedClient(
    clientId: Types.ObjectId,
    therapistId: Types.ObjectId,
    limit: number,
  ): Promise<IMeeting[]> {
    const queryConditions: any = {
      clientId: clientId,
      therapistId: therapistId,
      // callingStatus: { $eq: "COMPLETED", $exists: true }
    };

    const meetings = await Meeting.find(queryConditions)
      .populate({
        path: 'clientId',
        select: 'firstname lastname lavniTestAccount _id',
        match: { lavniTestAccount: { $ne: true } },
      })
      .populate({
        path: 'therapistId',
        select: 'firstname lastname lavniTestAccount _id',
        match: { lavniTestAccount: { $ne: true } },
      })
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();

    return meetings;
  }

  export async function searchInsuranceDocAndFilter(
    searchableStringClient: string,
    searchableStringTherapist: string,
    searchableInsuranceCompany: Types.ObjectId,
    limit: number,
    offset: number,
  ): Promise<IFriendRequest[]> {

    let searchedNameClient = null;
    if (searchableStringClient && searchableStringClient.trim() != "") {
      searchableStringClient = searchableStringClient.trim();
      let seacrhItem = searchableStringClient.replace(/\s/g, "");
      searchedNameClient = searchableStringClient != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
    searchedNameClient
        ? {
          $and: [
            {
              $or: [
                { 'client.firstname': searchedNameClient },
                { 'client.lastname': searchedNameClient },
                { 'client.email': searchedNameClient },
                { 'client.fullName': searchedNameClient },
              ],
            },
          ],
        }
        : {};
    
    let searchedNameTherapist = null;
    if (searchableStringTherapist && searchableStringTherapist.trim() != "") {
      searchableStringTherapist =searchableStringTherapist.trim();
      let seacrhItem = searchableStringTherapist.replace(/\s/g, "");
      searchedNameTherapist = searchableStringTherapist != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const therapistNameQuery =
    searchedNameTherapist
        ? {
          $and: [
            {
              $or: [
                { 'therapist.firstname': searchedNameTherapist },
                { 'therapist.lastname': searchedNameTherapist },
                { 'therapist.email': searchedNameTherapist },
                { 'therapist.fullName': searchedNameTherapist },
              ],
            },
          ],
        }
        : {};

    let insuranceCompanyQuery: any = {};
    if (searchableInsuranceCompany) {
      insuranceCompanyQuery['insuranceCompanyDetails._id'] = new Types.ObjectId(searchableInsuranceCompany);
    }

    const searchResult = await FriendRequest.aggregate([
      {
        $sort: {
          createdAt: -1
        }
      },
      {
        $match: {
          status: FriendRequestStatus.APPROVED
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          as: "clientInfo"
        }
      },
      {
        $unwind: {
          path: "$clientInfo"
        }
      },
      {
        $match: {
          $or: [
            { "clientInfo.blockedByAdmin": false },
            { "clientInfo.blockedByAdmin": { $exists: false } }
          ]
        }
      },
      {
        $group: {
          _id: { clientId: "$clientId", therapistId: "$therapistId" }
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "_id.clientId",
          foreignField: "_id",
          as: "userDetails"
        }
      },
      {
        $unwind: "$userDetails"
      },
      {
        $lookup: {
          from: "insurances",
          let: { userInsuranceId: "$userDetails.insuranceId" },
          pipeline: [
            { $match: { $expr: { $eq: ["$_id", "$$userInsuranceId"] } } },
            { $project: { insuranceCompanyId: 1 } }
          ],
          as: "primaryInsurance"
        }
      },
      {
        $unwind: {
          path: "$primaryInsurance",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $addFields: {
          primaryInsuranceCompanyId: "$primaryInsurance.insuranceCompanyId"
        }
      },
      {
        $lookup: {
          from: "insurances",
          localField: "_id.clientId",
          foreignField: "clientId",
          as: "insuranceDetails"
        }
      },
      {
        $unwind: {
          path: "$insuranceDetails",
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $lookup: {
          from: "insurancecompanies",
          localField: "insuranceDetails.insuranceCompanyId",
          foreignField: "_id",
          as: "insuranceCompanyDetails"
        }
      },
      {
        $unwind: {
          path: "$insuranceCompanyDetails",
          preserveNullAndEmptyArrays: false
        }
      },
      // {
      //   $match: {
      //     $and: [
      //       { "insuranceCompanyDetails.fax": { $exists: true, $ne: null }},
      //       { "insuranceCompanyDetails.authorizationFormAvailability": { $exists: true, $eq: true }}
      //     ],
      //   }
      // },
      {
        $lookup: {
          from: "users",
          localField: "_id.clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {_id: 1, firstname: 1, lastname: 1, email: 1, createdAt: 1 }
            }
          ],
          as: "clientDetails"
        }
      },
      {
        $unwind: {
          path: "$clientDetails",
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "_id.therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {_id: 1, firstname: 1, lastname: 1, email: 1, createdAt: 1 }
            }
          ],
          as: "therapistDetails"
        }
      },
      {
        $unwind: {
          path: "$therapistDetails",
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $match: insuranceCompanyQuery
      },
      {
        $lookup: {
          from: "clinicalassesments",
          let: {
            clientId: "$_id.clientId",
            therapistId: "$_id.therapistId"
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$clientId", "$$clientId"] },
                    { $eq: ["$therapistId", "$$therapistId"] }
                  ]
                }
              }
            }
          ],
          as: "clinicalAssessments"
        }
      },
      {
        $unwind: {
          path: "$clinicalAssessments",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: "digitalassessments",
          let: {
            clientId: "$_id.clientId",
            therapistId: "$_id.therapistId"
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$clientId", "$$clientId"] },
                    { $eq: ["$therapistId", "$$therapistId"] }
                  ]
                }
              }
            }
          ],
          as: "digitalAssessments"
        }
      },
      {
        $unwind: {
          path: "$digitalAssessments",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: "therapyplans",
          let: {
            clientId: "$_id.clientId",
            therapistId: "$_id.therapistId"
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$clientId", "$$clientId"] },
                    { $eq: ["$therapistId", "$$therapistId"] }
                  ]
                }
              }
            }
          ],
          as: "therapyPlans"
        }
      },
      {
        $unwind: {
          path: "$therapyPlans",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: "authorizationforms",
          let: { 
            clientId: "$_id.clientId",
            therapistId: "$_id.therapistId",
            insuranceCompanyId: "$insuranceCompanyDetails._id"
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$clientId", "$$clientId"] },
                    { $eq: ["$therapistId", "$$therapistId"] },
                    { $eq: ["$insuranceCompanyId", "$$insuranceCompanyId"] }
                  ]
                }
              }
            }
          ],
          as: "authorizationForms"
        }
      },
      {
        $unwind: {
          path: "$authorizationForms",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: "meetings",
          let: { clientId: "$_id.clientId", therapistId: "$_id.therapistId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$clientId", "$$clientId"] },
                    { $eq: ["$therapistId", "$$therapistId"] },
                    { $eq: ["$callingStatus", "COMPLETED"] }
                  ]
                }
              }
            }
          ],
          as: "meetingDetails"
        }
      },
      {
        $addFields: {
          meetingCount: { $size: "$meetingDetails" }
        }
      },
      {
        $lookup: {
          from: "insurancedocapprovals",
          let: { 
            clientId: "$_id.clientId",
            therapistId: "$_id.therapistId",
            insuranceCompanyId: "$insuranceCompanyDetails._id"
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$clientId", "$$clientId"] },
                    { $eq: ["$therapistId", "$$therapistId"] },
                    { $eq: ["$insuranceCompanyId", "$$insuranceCompanyId"] }
                  ]
                }
              }
            }
          ],
          as: "insuranceDocApprovals"
        }
      },
      {
        $unwind: {
          path: "$insuranceDocApprovals",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: { clientId: "$_id.clientId", therapistId: "$_id.therapistId" },
          clientDetails: { $first: "$clientDetails" },
          therapistDetails: { $first: "$therapistDetails" },
          primaryInsuranceCompanyId: { $first: "$primaryInsuranceCompanyId" },
          insuranceCompanies: {
            $addToSet: {
              _id: "$insuranceCompanyDetails._id",
              isPrimary: { $eq: ["$insuranceCompanyDetails._id", "$primaryInsuranceCompanyId"] },
              name: "$insuranceCompanyDetails.insuranceCompany",
              fax: "$insuranceCompanyDetails.fax",
              authorizationFormAvailability: "$insuranceCompanyDetails.authorizationFormAvailability",
              authorizationForms: { $ifNull: ["$authorizationForms._id", null] },
              authFormType: { $ifNull: ["$authorizationForms.authFormType", null] },
              insuranceDocApprovalId: { $ifNull: ["$insuranceDocApprovals._id", null] },
              therapistApprovalStatus: { $ifNull: ["$insuranceDocApprovals.therapistApprovalStatus", null] },
              adminApprovalStatus: { $ifNull: ["$insuranceDocApprovals.adminApprovalStatus", null] },
              messageId: { $ifNull: ["$insuranceDocApprovals.messageId", null] },
              messageStatus: { $ifNull: ["$insuranceDocApprovals.messageStatus", null] },
              link: {$ifNull: ["$insuranceCompanyDetails.link", null]}
            }
          },
          clinicalAssessment: { $first: "$clinicalAssessments._id" },
          digitalAssessment: { $first: "$digitalAssessments._id" },
          therapyPlan: { $first: "$therapyPlans._id" },
          meetingCount: { $first: "$meetingCount" }
        }
      },
      {
        $project: {
          _id: 0,
          clientCreatedAtDate: '$clientDetails.createdAt',
          therapistCreatedAtDate: '$therapistDetails.createdAt',
          client: {
            _id: "$clientDetails._id",
            firstname: "$clientDetails.firstname",
            lastname: "$clientDetails.lastname",
            email: "$clientDetails.email"
          },
          therapist: {
            _id: "$therapistDetails._id",
            firstname: "$therapistDetails.firstname",
            lastname: "$therapistDetails.lastname",
            email: "$therapistDetails.email"
          },
          insuranceCompanies: 1,
          clinicalAssessment: 1,
          digitalAssessment: 1,
          therapyPlan: 1,
          meetingCount: 1
        }
      },
      {
        $sort: {
          clientCreatedAtDate: -1,
          therapistCreatedAtDate: -1
        }
      },
      {
        $match: {
          $and: [
            clientNameQuery,
            therapistNameQuery
          ],
        },
      },
      {
        $skip: offset
      },
      {
        $limit: limit
      }
    ]);

    return searchResult;
  }

  export async function updateInsuranceDocApprovalsById(
    insuranceDocApprovalId: StringOrObjectId,
    data: Partial<DInsuranceDocApproval>
  ): Promise<IInsuranceDocApproval> {
    const updatedInsuranceDocApproval = await InsuranceDocApproval.findByIdAndUpdate(
      insuranceDocApprovalId,
      { $set: data },
      { new: true }
    );
    return updatedInsuranceDocApproval;
  }

  export async function getUploadedInsuranceDocStatistics(insuranceDocApprovalId: any) {
    try {
      const id = Types.ObjectId(insuranceDocApprovalId);
      const documentData = await InsuranceDocApproval.findById(id)
      .select('clinicalAssessmentUploadId therapyPlanUploadId authorizationFormUploadId insuranceCompanyId')
      .populate({
        path: 'clinicalAssessmentUploadId',
        select: 'name',
      })
      .populate({
        path: 'therapyPlanUploadId',
        select: 'name',
      })
      .populate({
        path: 'authorizationFormUploadId',
        select: 'name',
      })
      .populate({
        path: 'insuranceCompanyId',
        model: 'InsuranceCompany',
        select: 'fax',
      });

      return documentData;
    } catch (error) {
      return null;
    }
  }

  export async function getAllPendingAdminApprovalsReached24Hours(): Promise<IInsuranceDocApproval[]>
  {
    const now = new Date();
    const deadline = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const response = await InsuranceDocApproval.find({
      therapistApprovalStatus: SubmissionApprovalStatus.APPROVED,
      adminApprovalStatus: { $exists: false },
      createdAt: { $lt: deadline }
    })
    .populate({
      path: 'therapistId',
      select: 'firstname lastname',
    })
    .populate({
      path: 'clientId',
      select: 'firstname lastname',
    })
    .populate({
      path: 'insuranceCompanyId',
      model: 'InsuranceCompany',
      select: 'insuranceCompany',
    })
    .sort({ createdAt: -1 });

    return response;
  }

  export async function getMeetingByIds(
    clientId: StringOrObjectId,
    therapistId: StringOrObjectId,
  ): Promise<IMeeting[]> {
    const matchedMeetings = await Meeting.find(
      { 
        clientId,
        therapistId,
        callingStatus: CallingStatus.COMPLETED,
      }
    ).select('_id therapistId');
    return matchedMeetings;
  }

  export async function updateEligibilityForPayment(
    id: StringOrObjectId,
    data: Partial<ITransaction>
  ): Promise<ITransaction> {
    let transaction = await Transaction.findOneAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    );
    return transaction;
  }

  export async function getFriendRequestByTherapistId(
    therapistId: Types.ObjectId
  ): Promise<IFriendRequest[]> {
    const response = await FriendRequest.find({
      therapistId: therapistId,
      status: FriendRequestStatus.APPROVED
    }).populate(populateOptions);

    return response;
  }

  export async function searchClientsByTherapistIdFriends(
    searchableString: string,
    limit: number,
    offset: number,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string,
    clientArr?: string[],
  ): Promise<IClient[]> {
    const clientIdArray = clientArr?.map(id => new mongoose.Types.ObjectId(id));
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery =
      status != null && status ? { verifiedStatus: status } : {};
    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};

    const removeInvalidClientsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

        const searchResult: IClient[] = await Client.aggregate([
          {
            $project: {
              fullName: {
                $concat: ["$firstname", "$lastname"],
              },
              firstname: 1,
              lastname: 1,
              email: 1,
              createdAt: 1,
              photoId: 1,
              role: 1,
              gender: 1,
              zipCode: 1,
              state: 1,
              verifiedStatus: 1,
              blockedByAdmin: 1,
              profession: 1,
              lavniTestAccount: 1,
              primaryPhone: 1,
              _id: 1,
            },
          },
          {
            $match: {
              $and: [
                genderQuery,
                statusQuery,
                subscriptionQuery,
                clientNameQuery,
                zipCodeQuery,
                removeInvalidClientsQuery,
                {
                  _id: {
                    ...(clientIdArray.length > 0 ? { $in: clientIdArray } : {}),
                  },
                },
                { firstname: { $ne: null } },
              ],
              lavniTestAccount: { $ne: true },
            },
          },
          {
            $lookup: {
              from: "uploads",
              localField: "photoId",
              foreignField: "_id",
              pipeline: [
                {
                  $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
                },
              ],
              as: "photoId",
            },
          },
          {
            $unwind: {
              path: "$photoId",
              preserveNullAndEmptyArrays: true,
            },
          },
        ])
        .skip(limit * (offset - 1))
        .limit(limit);

    return searchResult;
  }

  export async function searchMatchedTherapistsByClientIdFriends(
    searchableString: string,
    limit: number,
    offset: number,
    gender?: string,
    status?: string,
    isSubscription?: string,
    zipCode?: string,
    therapistArr?: string[],
  ): Promise<ITherapist[]> {
    const therapistIdArray = therapistArr?.map(id => new mongoose.Types.ObjectId(id));
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const statusQuery =
      status != null && status ? { verifiedStatus: status } : {};
    const zipCodeQuery = zipCode != null && zipCode ? { zipCode: zipCode } : {};
    const subscriptionQuery =
      isSubscription != null && isSubscription
        ? { subscriptionStatus: isSubscription }
        : {};

    const removeInvalidTherapistQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let searchedName = null;

    if (searchableString) {
      let seacrhItem = searchableString.replace(/\s/g, "");
      searchedName =
        searchableString != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const therapistNameQuery =
      searchedName != null && searchedName
        ? {
          $and: [
            {
              $or: [
                { firstname: searchedName },
                { lastname: searchedName },
                { email: searchedName },
                { fullName: searchedName },
              ],
            },
          ],
        }
        : {};

        const searchResult: ITherapist[] = await Therapist.aggregate([
          {
            $project: {
              fullName: {
                $concat: ["$firstname", "$lastname"],
              },
              firstname: 1,
              lastname: 1,
              email: 1,
              createdAt: 1,
              photoId: 1,
              role: 1,
              gender: 1,
              zipCode: 1,
              state: 1,
              verifiedStatus: 1,
              blockedByAdmin: 1,
              profession: 1,
              lavniTestAccount: 1,
              primaryPhone: 1,
              _id: 1,
            },
          },
          {
            $match: {
              $and: [
                genderQuery,
                statusQuery,
                subscriptionQuery,
                therapistNameQuery,
                zipCodeQuery,
                removeInvalidTherapistQuery,
                {
                  _id: {
                    ...(therapistIdArray.length > 0 ? { $in: therapistIdArray } : {}),
                  },
                },
                { firstname: { $ne: null } },
              ],
              lavniTestAccount: { $ne: true },
            },
          },
          {
            $lookup: {
              from: "uploads",
              localField: "photoId",
              foreignField: "_id",
              pipeline: [
                {
                  $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
                },
              ],
              as: "photoId",
            },
          },
          {
            $unwind: {
              path: "$photoId",
              preserveNullAndEmptyArrays: true,
            },
          },
        ])
        .skip(limit * (offset - 1))
        .limit(limit);

    return searchResult;
  }

  export async function addStateByAdmin(
    stateName: string,
  ): Promise<AddStateResponse> {
    try {
      const existingState = await State.findOne({ stateName });

      if (existingState) {
        return {
          success: false,
          error: "State already exists",
        };
      }

      const iState = new State({
        stateName,
      });
      const state = await iState.save();
      return {
        success: true,
        state: state
      };
    } catch (error) {
      return {
        success: false,
        error: error
      }
    }
  }

  export async function deleteStateByAdmin(stateId: string): Promise<AddStateResponse> {
    try {
      const deletedState = await State.findByIdAndDelete(stateId);
      if (!deletedState) {
        return {
          success: false,
          error: `State not found for this Id: ${stateId}`,
        };
      }
      return {
        success: true,
        state: deletedState, 
      };
    } catch (error) {
      return {
        success: false,
        error: error,
      };
    }
  }

  export async function getAllStatesByAdmin(): Promise<AddStateResponseTwo> {
    try {
      const states = await State.find(); // Retrieves all states from the database
      return {
        success: true,
        states: states, // Returning all the states
      };
    } catch (error) {
      return {
        success: false,
        error: error,
      };
    }
  }

}
