import { Types } from "mongoose";
import { DClinicalAssesment, IClinicalAssesment } from "../models/clinicalAssesment-model";
import ClinicalAssesment from "../schemas/clinical-assesment-schema";
import { StringOrObjectId } from "../common/util";


export namespace ClinicalAssesmentDao {

  export async function addDetails(
    assestmentDetails: DClinicalAssesment
  ): Promise<IClinicalAssesment> {
    try {
      const request = new ClinicalAssesment(assestmentDetails);
      let response = await request.save();
      return response;
    } catch (error) {
      console.log("error saving details")
    }
  }

  export async function updateDetails(
    id: string,
    assestmentDetails: Partial<DClinicalAssesment>
  ): Promise<IClinicalAssesment | null> {
    try {
      const response = await ClinicalAssesment.findByIdAndUpdate(
        id,
        { $set: assestmentDetails },
        { new: true }
      );

      return response;
    } catch (error) {
      console.error("Error updating assessment details:>>>>>>>>>", error);
      return null;
    }
  }

  export async function getClinicalAssesmentByClientId(
    clientId: string,
    therapistId: string
  ): Promise<IClinicalAssesment | null> {
    const clinicalAssesment = await ClinicalAssesment.findOne({ clientId: clientId, therapistId: therapistId }).populate({
      path: 'clientId',
      select: 'assesmentSignature'
    });

    return clinicalAssesment;
  }

  export async function updateClientAssesmentSignature(
    id:string,
    clientSignature:Partial<DClinicalAssesment>
  ): Promise<IClinicalAssesment | null> {
    try {
      const response = await ClinicalAssesment.findByIdAndUpdate(
        id,
        { $set: clientSignature },
        { new: true }
      );

      return response;
    } catch (error) {
      console.error("Error updating assessment client signature:>>>>>>>>>", error);
      return null;
    }
  }

}

