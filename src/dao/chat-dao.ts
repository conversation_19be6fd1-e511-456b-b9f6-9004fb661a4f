import { Types } from "mongoose";
import User from "../schemas/user-schema";
import Chat from "../schemas/chat-schema";
import { ChatData, IChatData } from "../models/chat-model";
import { AppLogger } from "../common/logging";
import { IMessageData, MessageData } from "../models/chat-message-model";
import Message from "../schemas/message-schema";
import { StringOrObjectId } from "../common/util";

export namespace ChatDao {
  export async function getAllUsers(userId: Types.ObjectId) {
    const foundCreators = await User.aggregate([
      {
        $match: { _id: { $ne: userId } },
      },
    ]);

    return foundCreators;
  }

  export async function createChat(data: ChatData) {
    const iChatData: IChatData = new Chat(data);
    AppLogger.info(`Create chat with id ${iChatData.members}`);
    return await iChatData.save();
  }

  export async function updateChatIsActiveStatus(
    chatId: string,
    status: boolean
  ) {
    AppLogger.info(`updated chat with id ${chatId}`);
    return await Chat.findByIdAndUpdate(chatId, { $set: { isActive: status } });
  }

  export async function updateChatIsActiveStatusByUserIds(
    members: Types.ObjectId[],
    status: boolean
  ) {
    AppLogger.info(`updated chat with members ${members}`);
    return await Chat.findOneAndUpdate(
      { members: { $all: members } },
      { $set: { isActive: status } }
    );
  }

  export async function findChatByMemberIds(data: Types.ObjectId[]) {
    const chat = await Chat.findOne({
      members: { $all: data },
    });
    return chat;
  }

  export async function getAllUserChatsSendSMS() {
    const foundCreators = await Chat.find({
      isActive: true,
      $or: [{ unreadSMS: false }, { unreadSMS: { $exists: false } }],
      "unreadMessage.unreadUserId": { $ne: null },
    })
      .populate([
        {
          path: "unreadMessage.unreadUserId",
        },
        {
          path: "unreadMessage.lastMessage",
          populate: {
            path: "senderId",
          },
        },
      ])
      .sort({ lastActiveTime: -1 });
    return foundCreators;
  }

  export async function adminGetAllUserChats() {
    const foundCreators = await Chat.find()
      .populate({
        path: "members",
        populate: {
          path: "profileImageId",
        },
      })
      .sort({ lastActiveTime: -1 });
    return foundCreators;
  }

  export async function createMessage(data: MessageData) {
    const iMessageData: IMessageData = new Message(data);
    AppLogger.info(`Create chat with id ${data.chatId}`);
    return await iMessageData.save();
  }

  export async function getAllMessagesByChatId(
    chatId: Types.ObjectId,
    limit: number,
    offset: number
  ) {
    const foundCreators = await Message.find({
      chatId: chatId,
      isActive: true,
    })
      .sort({ createdAt: -1 })
      .populate([
        {
          path: "senderId",
          select:
            "_id role firstname lastname photoId socketId",
          populate: {
            path: "profileImageId",
          },
        },
        {
          path: "AttachmentUploadId",
        },
        {
          path: "mentionedMessage",
          populate: [
            {
              path: "senderId",
              select:
                "_id role firstname lastname photoId socketId",
              populate: {
                path: "profileImageId",
              },
            },
            {
              path: "AttachmentUploadId",
            },
          ],
        },
      ])
      .skip(limit * (offset - 1))
      .limit(limit);
    return foundCreators;
  }

  export async function updateLastActiveInChat(
    chatId: Types.ObjectId,
    date: Date,
    unreadMessageObj: any
  ) {
    const foundCreators = await Chat.findByIdAndUpdate(
      chatId,
      {
        unreadSMS: false,
        lastActiveTime: date,
        unreadMessage: unreadMessageObj,
      },
      {
        new: true,
      }
    );
    return foundCreators;
  }

  export async function MarkAsReadMessagesByChatId(chatId: Types.ObjectId) {
    const foundCreators = await Chat.findByIdAndUpdate(
      chatId,
      { unreadSMS: true, "unreadMessage.unreadUserId": undefined },
      {
        new: true,
      }
    );
    const markMessagesAsSeen = await Message.updateMany(
      { chatId: chatId },
      { isMsgSeen: true },
      {
        new: true,
      }
    );
    return foundCreators;
  }

  export async function MarkAsSemdSMSByChatId(chatId: Types.ObjectId) {
    const foundCreators = await Chat.findByIdAndUpdate(
      chatId,
      { unreadSMS: true },
      {
        new: true,
      }
    );
    return foundCreators;
  }
  export async function deleteMessageByMessageId(messageId: Types.ObjectId) {
    const foundCreators = await Message.findByIdAndUpdate(
      messageId,
      { isActive: false },
      {
        new: true,
      }
    );
    return foundCreators;
  }

  export async function getUserDetails(userId: Types.ObjectId) {
    const foundCreators = await User.findById(userId);
    return foundCreators;
  }

  export async function getUserByNameInnerFunc(
    firstName: string,
    lastName: string
  ) {
    const foundCreators = await User.find({
      firstname: firstName,
      lastname: lastName,
    });
    return foundCreators;
  }

  export async function getUserUnreadChatCount(userId: Types.ObjectId) {
    const unreadChatCount = await Chat.find({
      members: userId,
      "unreadMessage.unreadUserId": userId,
    }).count();
    return unreadChatCount;
  }

  export async function searchChats(userId: Types.ObjectId, searchTag: string) {
    const chats = await Chat.aggregate([
      {
        $match: {
          members: { $in: [userId] },
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "members",
          foreignField: "_id",
          as: "partipantDetails",
        },
      },
      { $unwind: "$partipantDetails" },
      {
        $match: { "partipantDetails._id": { $ne: userId } },
      },
      {
        $match: {
          $or: [
            { "partipantDetails.name": { $regex: searchTag, $options: "i" } },
            {
              "partipantDetails.pageName": { $regex: searchTag, $options: "i" },
            },
          ],
        },
      },
      {
        $project: {
          participantDetails: 0,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "members",
          foreignField: "_id",
          as: "members",
        },
      },
      {
        $project: {
          _id: 1,
          isActive: 1,
          lastActiveTime: 1,
          unreadMessage: 1,
          createdAt: 1,
          updatedAt: 1,
          __v: 1,
          "members._id": 1,
          "members.name": 1,
          "members.email": 1,
          "members.profileImageId": 1,
          "members.pageName": 1,
        },
      },
    ]);
    return chats;
  }

  export async function searchChatsForAdmin(searchTag: string) {
    const chats = await Chat.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "members",
          foreignField: "_id",
          as: "partipantDetails",
        },
      },
      {
        $match: {
          "partipantDetails.name": { $regex: searchTag, $options: "i" },
        },
      },
      {
        $project: {
          participantDetails: 0,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "members",
          foreignField: "_id",
          as: "members",
        },
      },
      {
        $project: {
          _id: 1,
          isActive: 1,
          lastActiveTime: 1,
          unreadMessage: 1,
          createdAt: 1,
          updatedAt: 1,
          __v: 1,
          "members._id": 1,
          "members.name": 1,
          "members.email": 1,
          "members.profileImageId": 1,
          "members.pageName": 1,
        },
      },
    ]);
    return chats;
  }

  export async function getAllUnreadMessagesFromUserId(userId: Types.ObjectId) {
    try {
      const chats = await Chat.find({ members: userId }).exec();
      const chatIds = chats.map((chat) => chat._id);

      const unreadMessages = await Message.find({
        chatId: { $in: chatIds },
        isMsgSeen: false,
        senderId: { $ne: userId },
      })
        .populate([
          {
            path: "senderId",
            select: "firstname lastname photoId",
            populate: {
              path: "photoId",
            },
          },
        ])
        .exec();
      return unreadMessages;
    } catch (error) {}
  }

  export async function getAllUnreadChatsFromUserId(userId: Types.ObjectId) {
    const foundCreators = await Chat.find({
      members: userId,
      isActive: true,
    })
      .sort({ updatedAt: -1 })
      .populate([
        {
          path: "members",
          match: { firstname: { $exists: true } },
          populate: {
            path: "photoId",
          },
        },
        {
          path: "unreadMessage.lastMessage",
        },
      ]);

    //------------2023/12/01--------------------

    // const filteredData : any = foundCreators.filter((c: any) => {
    //   if (c.unreadMessage?.lastMessage?.isMsgSeen == false && c.unreadMessage?.lastMessage?.senderId != userId.toString()){
    //     return c;
    //   }

    // })
    const filteredData: any[] = [];
    const otherData: any[] = [];

    foundCreators.forEach((c: any) => {
      if (
        c.unreadMessage?.lastMessage?.isMsgSeen == false &&
        c.unreadMessage?.lastMessage?.senderId != userId.toString()
      ) {
        filteredData.push(c);
      } else {
        otherData.push(c);
      }
    });

    let newChatList = [...filteredData, ...otherData];
    if (newChatList.length > 5) {
      newChatList = newChatList.splice(0, 5);
    }
    //----------- End-2023/12/01--------------------
    let chatsWithUnreadMessageCount: {
      chat: IChatData;
      unreadMessageCount: number;
    }[] = [];
    for (const chat of newChatList) {
      const unreadMessageCount = await getUnreadMessageCountByChatId(
        userId,
        chat._id
      );
      chatsWithUnreadMessageCount.push({ chat, unreadMessageCount });
    }

    return chatsWithUnreadMessageCount;
  }

  export async function getChatByChatIdForDashboard(
    userId: Types.ObjectId,
    chatId: Types.ObjectId
  ) {
    const chat = await Chat.findById(chatId).populate([
      {
        path: "members",
        match: { firstname: { $exists: true } },
        populate: {
          path: "photoId",
        },
      },
      {
        path: "unreadMessage.lastMessage",
      },
    ]);
    let chatsWithUnreadMessageCount: {
      chat: IChatData;
      unreadMessageCount: number;
    }[] = [];
    const unreadMessageCount = await getUnreadMessageCountByChatId(
      userId,
      chat._id
    );
    chatsWithUnreadMessageCount.push({ chat, unreadMessageCount });
    return chatsWithUnreadMessageCount[0];
  }

  export async function getUnreadMessageCountByChatId(
    userId: Types.ObjectId,
    chatId: Types.ObjectId
  ) {
    const unreadMessagescount = await Message.countDocuments({
      chatId: chatId,
      senderId: { $ne: userId },
      isMsgSeen: { $eq: false },
    });

    return unreadMessagescount;
  }

  export async function deleteAllChatsByClientId(
    clientId: StringOrObjectId
  ): Promise<number> {
    const response = await Chat.deleteMany({
      members: {
        $elemMatch: {
          $eq: clientId,
        },
      },
    });

    return response.deletedCount || 0;
  }

  export async function deleteAllChatsByTherapistId(
    therapistId: StringOrObjectId
  ): Promise<number> {
    const response = await Chat.deleteMany({
      members: {
        $elemMatch: {
          $eq: therapistId,
        },
      },
    });

    return response.deletedCount || 0;
  }
}
