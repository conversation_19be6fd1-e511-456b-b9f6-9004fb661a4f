import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";
import { DNotification, INotification } from "../models/notification-model";
import Notification from "../schemas/notification-schema";
import {admin} from '../utils/firebase/firebase'

export namespace NotificationDao {
  export async function getNotificationById(
    id: StringOrObjectId
  ): Promise<INotification> {
    let notification: INotification = await Notification.findById(id);

    return notification;
  }

  export async function getNotifications(
    userId: Types.ObjectId,
    limit: number,
    offset: number
  ): Promise<INotification[]> {
    const response = await Notification.find({ receiverId: userId })
    .sort({ createdAt: -1 })
    .skip(offset)
    .limit(limit);

    return response;
  }

  export async function getUnreadNotificationsCount(
    userId: Types.ObjectId
  ): Promise<number> {
    const response = await Notification.find({ receiverId: userId, readStatus: false }).count();

    return response;
  }

  export async function markAllNotificationsAsRead(
    userId: Types.ObjectId
  ): Promise<boolean> {
    let response = await Notification.updateMany(
      { receiverId: userId },
      { $set: { "readStatus": true } },
      { new: true }
    );

    if(response) {
      return true;
    } else {
      return false;
    }
  }

  export async function createNotification(
    notificationDetails: DNotification
  ): Promise<INotification> {
    const iNotification = new Notification(notificationDetails);
    let response = await iNotification.save();

    return response;
  }
  
  export async function deleteNotification(
    notificationId: StringOrObjectId
  ): Promise<INotification> {
    const response = await Notification.findByIdAndDelete(notificationId);

    return response;
  }

  export async function deleteSentNotificationsByUserId(userId:StringOrObjectId):Promise<number>{
    const response = await Notification.deleteMany({senderId:userId});
    return response.ok;
  }

  export async function deleteReceivedNotificationsByUserId(userId:StringOrObjectId):Promise<number>{
    const response = await Notification.deleteMany({receiverId:userId});
    return response.ok;
  }

  export async function sendFirebasePushNotification (deviceToken: string, title: string, body: string):Promise<{success:boolean, res?: any, error?: any}> {
    console.log("deviceToken ", deviceToken, " title ", title);
    
    const message = {
      notification : {
        title,
        body
      },
      token: deviceToken,
      data: {
        url: body
      }
    };
    try {
      const res = await admin.messaging().send(message);
      return {success: true, res};
    } catch (error) {
      return {success: false, error}
    }
  }
}