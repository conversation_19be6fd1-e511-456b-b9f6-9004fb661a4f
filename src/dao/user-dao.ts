import {AppLogger} from "../common/logging";
import {ApplicationError} from "../common/application-error";
import {
    AppointmentSMSStatus,
    DUser,
    IUser,
    Medium,
    PayRateTypes,
    Permission,
    UserRole,
    UserStatus
} from "../models/user-model";
import User from "../schemas/user-schema";
import {StringOrObjectId} from "../common/util";
import {DUpload, IUpload} from "../models/upload-model";
import * as path from "path";
import {Types} from "mongoose";
import {UploadDao} from "./upload-dao";
import {DClient, IClient, PremiumStatus} from "../models/client-model";
import {D<PERSON><PERSON>apist, ITherapist} from "../models/therapist-model";
import {DContact, IContact} from "../models/contact-us-model";
import {DLavniReview, ILavniReview, LavniReviewStatus} from "../models/lavni-review-model";
import Contact from "../schemas/contact-us-schema";
import {IEducation} from "../models/education-model";
import Education from "../schemas/education-schema";
import {UploadCategory} from "../end-point/user-ep";
import {ILicense} from "../models/license-model";
import License from "../schemas/license-schema";
import {TherapistDao} from "./therapist-dao";
import {ClientDao} from "./client-dao";
import {Payment} from "../models/sub-models/payment-model";
import Upload from "../schemas/upload-schema";
import Client from "../schemas/client-schema";
import Meeting from "../schemas/meeting-schema";
import {DInsurance, IInsurance} from "../models/insurance-model";
import Insurance from "../schemas/insurance-schema";
import Therapist from "../schemas/therapist-schema";
import {DCustomerReview, ICustomerReview,} from "../models/customer-reviews-model";
import CustomerReview from "../schemas/customer-reviews-schema";
import Appointment from "../schemas/appointment-schema";
import {IAppointment} from "../models/appointment-model";
import Review from "../schemas/lavni-review-schema";
import LavniReview from "../schemas/lavni-review-schema";
import ExperienceTagSymptomSchema from '../schemas/experience-tag-symptom-schema';
import moment = require("moment");

export namespace UserDao {
    const populateOptions = [
        {path: "qualifications"},
        {path: "qualifications", populate: {path: "uploadId"}},
        {path: "ethnicityId"},
        {path: "disclosureStatementId"},
        {path: "photoId"},
        {path: "coverPhotoId"},
        {path: "videoId"},
        {path: "licenseId"},
        {path: "licenseId", populate: {path: "uploadId"}},
        {path: "coverPhotoId"},
        {path: "profession"},
        {path: "professionLicense"},
        {path: "experiencedIn"},
        {path: "insuranceCompanies"},
    ];

    const populateOptions2 = [
        {
            path: "clientId",
            select: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                email: 1,
                gender: 1,
                ethnicityId: 1,
                primaryPhone: 1,
                subscriptionId: 1,
                subscriptionStatus: 1,
                premiumStatus: 1,
            },
            populate: [
                {path: "photoId", model: "Upload"}
            ],
        },
    ];

    export async function getUserByEmail(email: string): Promise<IUser | null> {
        let user = await User.findOne({email: email});
        return user;
    }

    export async function getUserByPrimaryPhone(
        phone: string
    ): Promise<IUser | null> {
        let user = await User.findOne({primaryPhone: phone});
        return user;
    }

    export async function updateUserByEmail(
        email: string,
        data: any
    ): Promise<IUser | null> {
        let user = await User.findOneAndUpdate(
            {email: email},
            {$set: data},
            {new: true}
        );
        return user;
    }

    export async function getUser(id: Types.ObjectId): Promise<IUser> {
        let user: IUser = await User.findById(id)
            .populate([
                {path: "photoId"},
                {path: "coverPhotoId"},
                {path: "videoId"},
                {path: "profession"},
                {path: "disclosureStatementId"},
                {path: "professionLicense"},
                {
                    path: "likedTherapists",
                    populate: [{path: "photoId"}, {path: "experiencedIn"}],
                },
                {path: "experiencedIn"},
                {path: "insuranceCompanies"},
                {path: "insuranceId"},
                {path: "ethnicityId"},
                {path: "friendRequests"},
                {path: "preference", populate: {path: "experiencedIn"}},
                {
                    path: "friendRequests",
                    select: {status: 1, clientId: 1, therapistId: 1},
                    populate: [
                        {
                            path: "clientId",
                            select: {
                                firstname: 1,
                                lastname: 1,
                                photoId: 1,
                                friendRequests: 0,
                            },
                        },
                        {
                            path: "therapistId",
                            select: {
                                firstname: 1,
                                lastname: 1,
                                photoId: 1,
                                friendRequests: 0,
                            },
                        },
                    ],
                },
                {path: "primaryTherapist", select: {firstname: 1, lastname: 1}},
            ])
            .select({password: 0});

        AppLogger.info(`Got user for id, userID: ${user._id}`);

        user.save();

        return user;
    }

    export async function getAllUsers(): Promise<IUser[]> {
        let user: IUser[] = await User.find({adminApproved: true})
            .populate([
                {path: "photoId"},
                {path: "coverPhotoId"},
            ])
            .select({password: 0});
        return user;
    }

    export async function getUserById(id: StringOrObjectId): Promise<IUser> {
        let user: IUser = await User.findById(id).populate("photo");

        if (!user) {
            throw new ApplicationError("User not found for Id: " + id);
        }

        AppLogger.info(`Got user for id, userID: ${user._id}`);

        // user.lastLogin = new Date();
        // await user.save();

        return user;
    }

    export async function getUserByPhoneNumber(
        phoneNumber: string
    ): Promise<IClient> {
        let user: IClient = await Client.findOne({primaryPhone: phoneNumber});
        if (!user) {
            throw new ApplicationError(
                "User not found for phoneNumber: " + phoneNumber
            );
        }
        return user;
    }

    export async function getTherapistById(
        id: StringOrObjectId
    ): Promise<ITherapist> {
        let user: ITherapist = await Therapist.findById(id).populate("photo");
        if (!user) {
            throw new ApplicationError("User not found for Id: " + id);
        }

        AppLogger.info(`Got user for id, userID: ${user._id}`);
        user.lastLogin = new Date();
        await user.save();
        return user;
    }

    export async function getUserByUserId(id: StringOrObjectId): Promise<IUser> {
        let user: IUser = await User.findById(id)
            .populate([
                {path: "qualifications"},
                {path: "experiencedIn"},
                {path: "insuranceCompanies"},
                {path: "profession"},
                {path: "professionLicense"},
                {path: "disclosureStatementId"},
                {path: "qualifications", populate: {path: "uploadId"}},
                {path: "ethnicityId"},
                {path: "photoId"},
                {path: "coverPhotoId"},
                {path: "videoId"},
                {path: "licenseId"},
                {path: "licenseId", populate: {path: "uploadId"}},
            ])
            .select({password: 0});
        return user;
    }

    export async function updateUser(
        id: StringOrObjectId,
        data: Partial<DUser>
    ): Promise<IUser> {
        let user = await User.findByIdAndUpdate(id, {$set: data}, {new: true})
            .populate(populateOptions)
            .select({password: 0});

        return user;
    }

    export async function updateUserPriorityNumber(
        id: StringOrObjectId,
        data: Partial<DUser>
    ): Promise<IUser> {

        const {priorityNumber} = data;

        const existingUserWithPriority = await User.findOne({
            _id: {$ne: id},
            priorityNumber: priorityNumber !== 0 ? priorityNumber : {$exists: false}
        });

        if (existingUserWithPriority && priorityNumber !== 0) {
            throw new Error("Priority number already assigned to another user.");
        }

        if (priorityNumber === undefined || priorityNumber === null || isNaN(priorityNumber)) {
            throw new Error("Priority number is required and must be a valid number.");
        }

        const userBlockedByAdmin = await User.findOne({
            _id: id,
            blockedByAdmin: true
        });

        if (userBlockedByAdmin) {
            throw new Error("This user is blocked by admin.");
        }

        let user = await User.findByIdAndUpdate(id, {$set: data}, {new: true})
            .populate(populateOptions)
            .select({password: 0});
        return user;
    }

    export async function updateUserBlackTherapyPriorityNumber(
        id: StringOrObjectId,
        data: Partial<DUser>
    ): Promise<IUser> {

        const {blackTherapyPriorityNumber} = data;

        const existingUserWithPriority = await User.findOne({
            _id: {$ne: id},
            blackTherapyPriorityNumber: blackTherapyPriorityNumber !== 0 ? blackTherapyPriorityNumber : {$exists: false}
        });

        if (existingUserWithPriority && blackTherapyPriorityNumber !== 0) {
            throw new Error("Priority number already assigned to another user.");
        }

        if (blackTherapyPriorityNumber === undefined || blackTherapyPriorityNumber === null || isNaN(blackTherapyPriorityNumber)) {
            throw new Error("Priority number is required and must be a valid number.");
        }

        const userBlockedByAdmin = await User.findOne({
            _id: id,
            blockedByAdmin: true
        });

        if (userBlockedByAdmin) {
            throw new Error("This user is blocked by admin.");
        }

        let user = await User.findByIdAndUpdate(id, {$set: data}, {new: true})
            .populate(populateOptions)
            .select({password: 0});
        return user;
    }

    export async function updateUserRelationShipTherapyPriorityNumber(
        id: StringOrObjectId,
        data: Partial<DUser>
    ): Promise<IUser> {

        const {relationshipTherapyPriorityNumber} = data;

        const existingUserWithPriority = await User.findOne({
            _id: {$ne: id},
            relationshipTherapyPriorityNumber: relationshipTherapyPriorityNumber !== 0 ? relationshipTherapyPriorityNumber : {$exists: false}
        });

        if (existingUserWithPriority && relationshipTherapyPriorityNumber !== 0) {
            throw new Error("Priority number already assigned to another user.");
        }

        if (relationshipTherapyPriorityNumber === undefined || relationshipTherapyPriorityNumber === null || isNaN(relationshipTherapyPriorityNumber)) {
            throw new Error("Priority number is required and must be a valid number.");
        }

        const userBlockedByAdmin = await User.findOne({
            _id: id,
            blockedByAdmin: true
        });

        if (userBlockedByAdmin) {
            throw new Error("This user is blocked by admin.");
        }

        let user = await User.findByIdAndUpdate(id, {$set: data}, {new: true})
            .populate(populateOptions)
            .select({password: 0});
        return user;
    }

    export async function updateUserMensMentalTherapyPriorityNumber(
        id: StringOrObjectId,
        data: Partial<DUser>
    ): Promise<IUser> {

        const {mensMentalTherapyPriorityNumber} = data;

        const existingUserWithPriority = await User.findOne({
            _id: {$ne: id},
            mensMentalTherapyPriorityNumber: mensMentalTherapyPriorityNumber !== 0 ? mensMentalTherapyPriorityNumber : {$exists: false}
        });

        if (existingUserWithPriority && mensMentalTherapyPriorityNumber !== 0) {
            throw new Error("Priority number already assigned to another user.");
        }

        if (mensMentalTherapyPriorityNumber === undefined || mensMentalTherapyPriorityNumber === null || isNaN(mensMentalTherapyPriorityNumber)) {
            throw new Error("Priority number is required and must be a valid number.");
        }

        const userBlockedByAdmin = await User.findOne({
            _id: id,
            blockedByAdmin: true
        });

        if (userBlockedByAdmin) {
            throw new Error("This user is blocked by admin.");
        }

        let user = await User.findByIdAndUpdate(id, {$set: data}, {new: true})
            .populate(populateOptions)
            .select({password: 0});
        return user;
    }

    export async function updateUserSmsStop(
        id: StringOrObjectId
    ): Promise<IUser> {
        let user = await User.findByIdAndUpdate(id, {
            smsStop: true,
        });
        return user;
    }

    export async function updateInsurance(
        id: StringOrObjectId,
        data: Partial<DInsurance>
    ): Promise<IInsurance> {
        let res = await Insurance.findByIdAndUpdate(
            id,
            {$set: data},
            {new: true}
        );

        return res;
    }

    export async function updateUserForSocketIo(
        id: StringOrObjectId,
        data: Partial<DUser>
    ): Promise<IUser> {
        let user = await User.findByIdAndUpdate(
            id,
            {$set: data},
            {returnDocument: "after"}
        )
            .populate(populateOptions)
            .select({password: 0});
        return user;
    }

    export async function authenticateUser(
        email: string,
        password: string
    ): Promise<string> {
        const user = await getUserByEmail(email);
        if (user) {
            const isMatch = await user.comparePassword(password);

            if (!isMatch) {
                throw new ApplicationError("Incorrect email/password combination.");
            }

            return user.createAccessToken();
        } else {
            throw new ApplicationError("User not found in the system.");
        }
    }

    export async function signUpWithEmail(
        email: string,
        primaryPhone: string,
        password: string,
        role: string,
        verifiedStatus?: string
    ): Promise<IUser> {
        const payRate = process.env.DEFAULT_PAY_RATE_OF_THERAPIST;
        let userDetails: DTherapist | DClient = null;

        let isEmailUsed = await User.findOne({email: email});

        if (isEmailUsed) {
            throw new ApplicationError("Provided email is already taken.");
        }

        if (role == UserRole.THERAPIST) {
            let payRateTypeData: any = {
                type: PayRateTypes.FLAT_USER,
                flatValue: 50,
            };
            userDetails = {
                email: email,
                primaryPhone: primaryPhone,
                password: password,
                role: role,
                medium: Medium.EMAIL,
                photoId: null,
                coverPhotoId: null,
                verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                adminApproved: false,
                blockedByAdmin: false,
                callRecordingAllowed: true,
                hideCallTimer: false,
                payRate: Number(payRate),
                payRateType: payRateTypeData,
                claimOpen: false,
            };
        }

        if (role == UserRole.CLIENT) {
            userDetails = {
                email: email,
                primaryPhone: primaryPhone,
                password: password,
                role: role,
                medium: Medium.EMAIL,
                photoId: null,
                coverPhotoId: null,
                verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                adminApproved: true,
                blockedByAdmin: false,
                premiumStatus: PremiumStatus.NOT_SET,
                chatWordCount: 0,
                callRecordingAllowed: true,
                hideCallTimer: false,
                incognito: false,
                incognitoPopupShow: false,
            };
        }

        const user = new User(userDetails);
        const newUser = await user.save();

        return newUser;
    }

    export async function signUpWithEmailForTherapistWithName(
        email: string,
        primaryPhone: string,
        password: string,
        role: string,
        verifiedStatus: string,
        firstName: string,
        lastName: string
    ): Promise<IUser> {
        const payRate = process.env.DEFAULT_PAY_RATE_OF_THERAPIST;
        let userDetails: DTherapist = null;

        let isEmailUsed = await User.findOne({email: email});

        if (isEmailUsed) {
            throw new ApplicationError("Provided email is already taken.");
        }

        let payRateTypeData: any = {
            type: PayRateTypes.FLAT_USER,
            flatValue: 50,
        };

        userDetails = {
            email: email,
            primaryPhone: primaryPhone,
            password: password,
            role: role,
            medium: Medium.EMAIL,
            photoId: null,
            coverPhotoId: null,
            verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
            adminApproved: false,
            blockedByAdmin: false,
            callRecordingAllowed: true,
            hideCallTimer: false,
            payRate: Number(payRate),
            payRateType: payRateTypeData,
            claimOpen: false,
            firstname: firstName,
            lastname: lastName,
        };

        const user = new User(userDetails);
        const newUser = await user.save();

        return newUser;
    }

    export async function signUpWithEmailAdmin(
        email: string,
        primaryPhone: string,
        firstname: string,
        lastname: string,
        username: string,
        gender: string,
        password: string,
        role: string,
        verifiedStatus?: string,
        message?: string,
        premiumUser?: boolean,
        adminPermission?: string,
        clientLocationState?: string
    ): Promise<IUser> {
        const payRate = process.env.DEFAULT_PAY_RATE_OF_THERAPIST;
        let userDetails: DTherapist | DClient = null;
        let permissions;

        let isEmailUsed = await User.findOne({email: email});

        if (isEmailUsed) {
            throw new ApplicationError("Provided email is already taken.");
        }

        if (role == UserRole.THERAPIST) {
            userDetails = {
                email: email,
                primaryPhone: primaryPhone,
                firstname: firstname,
                lastname: lastname,
                username: username,
                gender: gender,
                password: password,
                role: role,
                medium: Medium.EMAIL,
                photoId: null,
                coverPhotoId: null,
                verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                adminApproved: false,
                blockedByAdmin: false,
                callRecordingAllowed: true,
                hideCallTimer: false,
                payRate: Number(payRate),
                message: message,
                adminCreated: true,
                claimOpen: false
            };
        }

        if (role == UserRole.CLIENT) {
            userDetails = {
                email: email,
                primaryPhone: primaryPhone,
                firstname: firstname,
                lastname: lastname,
                gender: gender,
                password: password,
                role: role,
                medium: Medium.EMAIL,
                photoId: null,
                coverPhotoId: null,
                verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                adminApproved: true,
                blockedByAdmin: false,
                premiumStatus: premiumUser ? PremiumStatus.ACTIVE : PremiumStatus.NOT_SET,
                chatWordCount: 0,
                callRecordingAllowed: true,
                hideCallTimer: false,
                incognito: false,
                incognitoPopupShow: false,
                message: message,
                adminCreated: true,
                state: clientLocationState
            };
        }

        if (role == UserRole.SUPER_ADMIN) {
            userDetails = {
                email: email,
                primaryPhone: primaryPhone,
                firstname: firstname,
                lastname: lastname,
                gender: gender,
                password: password,
                role: role,
                medium: Medium.EMAIL,
                photoId: null,
                coverPhotoId: null,
                verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                adminApproved: true,
                blockedByAdmin: false,
                premiumStatus: PremiumStatus.NOT_SET,
                chatWordCount: 0,
                callRecordingAllowed: true,
                hideCallTimer: false,
                incognito: false,
                incognitoPopupShow: false,
                message: message,
                adminCreated: true,
                isLoginFirstTime: true
            };
        }

        if (role == UserRole.SUB_ADMIN) {
            permissions = {
                statistics: adminPermission.includes(Permission.statistics) ? true : false,
                adminDashboard: adminPermission.includes(Permission.adminDashboard) ? true : false,
                viewEthnicity: adminPermission.includes(Permission.viewEthnicity) ? true : false,
                viewExperienceTags: adminPermission.includes(Permission.viewExperienceTags) ? true : false,
                viewInsuranceCompanies: adminPermission.includes(Permission.viewInsuranceCompanies) ? true : false,
                createAppointmentAdmin: adminPermission.includes(Permission.createAppointmentAdmin) ? true : false,
                viewProfessions: adminPermission.includes(Permission.viewProfessions) ? true : false,
                documents: adminPermission.includes(Permission.documents) ? true : false,
                accessManagement: adminPermission.includes(Permission.accessManagement) ? true : false,
                viewHashTags: adminPermission.includes(Permission.viewHashTags) ? true : false,
                viewThemeImage: adminPermission.includes(Permission.viewThemeImage) ? true : false,
                reportReviews: adminPermission.includes(Permission.reportReviews) ? true : false,
                reviews: adminPermission.includes(Permission.reviews) ? true : false,
                contactUs: adminPermission.includes(Permission.contactUs) ? true : false,
                articles: adminPermission.includes(Permission.articles) ? true : false,
                feedback: adminPermission.includes(Permission.feedback) ? true : false,
                newsLetterEmails: adminPermission.includes(Permission.newsLetterEmails) ? true : false,
                marketingEmails: adminPermission.includes(Permission.marketingEmails) ? true : false,
                viewMeetingsAndRecordings: adminPermission.includes(Permission.viewMeetingsAndRecordings) ? true : false,
                viewAllClients: adminPermission.includes(Permission.viewAllClients) ? true : false,
                manageClients: adminPermission.includes(Permission.manageClients) ? true : false,
                premiumClients: adminPermission.includes(Permission.premiumClients) ? true : false,
                reminderSms: adminPermission.includes(Permission.reminderSms) ? true : false,
                viewAllTherapists: adminPermission.includes(Permission.viewAllTherapists) ? true : false,
                manageTherapists: adminPermission.includes(Permission.manageTherapists) ? true : false,
                viewTherapistReviews: adminPermission.includes(Permission.viewTherapistReviews) ? true : false,
                viewTherapistsSoapReviews: adminPermission.includes(Permission.viewTherapistsSoapReviews) ? true : false,
                educationalDetails: adminPermission.includes(Permission.educationalDetails) ? true : false,
                licenseDetails: adminPermission.includes(Permission.licenseDetails) ? true : false,
                therapistRequests: adminPermission.includes(Permission.therapistRequests) ? true : false,
                availableBalances: adminPermission.includes(Permission.availableBalances) ? true : false,
                adminApprovePayment: adminPermission.includes(Permission.adminApprovePayment) ? true : false,
                referralEarnings: adminPermission.includes(Permission.referralEarnings) ? true : false,
                clientRewards: adminPermission.includes(Permission.clientRewards) ? true : false,
                notifications: adminPermission.includes(Permission.notifications) ? true : false,
                sessionFeedback: adminPermission.includes(Permission.sessionFeedback) ? true : false,
                approvalQueue: adminPermission.includes(Permission.approvalQueue) ? true : false,
                techTickets: adminPermission.includes(Permission.techTickets) ? true : false,
                profile: true
            };

            userDetails = {
                email: email,
                primaryPhone: primaryPhone,
                firstname: firstname,
                lastname: lastname,
                gender: gender,
                password: password,
                role: role,
                medium: Medium.EMAIL,
                photoId: null,
                coverPhotoId: null,
                verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                adminApproved: true,
                blockedByAdmin: false,
                premiumStatus: PremiumStatus.NOT_SET,
                chatWordCount: 0,
                callRecordingAllowed: true,
                hideCallTimer: false,
                incognito: false,
                incognitoPopupShow: false,
                message: message,
                adminCreated: true,
                adminPermission: permissions,
                isLoginFirstTime: true
            };
        }

        const user = new User(userDetails);
        const newUser = await user.save();

        return newUser;
    }

    export async function signUpWithEmailPublic(
        firstname: string,
        lastname: string,
        email: string,
        username: string,
        dateOfBirth: Date,
        ethnicityId: Types.ObjectId,
        gender: string,
        primaryPhone: string,
        state: string,
        zipCode: string,
        password: string,
        role: string,
        PersonalizeMatchData: any,
        skip?: boolean,
        verifiedStatus?: string,
    ): Promise<IUser> {
        const payRate = process.env.DEFAULT_PAY_RATE_OF_THERAPIST;
        let userDetails: DTherapist | DClient = null;

        let isEmailUsed = await User.findOne({email: email});

        if (isEmailUsed) {
            throw new ApplicationError("Provided email is already taken.");
        }

        if (role == UserRole.CLIENT) {
            userDetails = {
                firstname: firstname.trim(),
                lastname: lastname.trim(),
                email: email,
                username: username.trim(),
                dateOfBirth: dateOfBirth,
                ethnicityId: ethnicityId,
                gender: gender,
                primaryPhone: primaryPhone,
                state: state,
                zipCode: zipCode,
                password: password,
                role: role,
                medium: Medium.EMAIL,
                photoId: null,
                coverPhotoId: null,
                verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                adminApproved: true,
                blockedByAdmin: false,
                premiumStatus: PremiumStatus.NOT_SET,
                chatWordCount: 0,
                callRecordingAllowed: true,
                hideCallTimer: false,
                incognito: false,
                incognitoPopupShow: false,
                skip: skip,
                PersonalizeMatchData: PersonalizeMatchData
            };
        }

        const user = new User(userDetails);
        const newUser = await user.save();

        return newUser;
    }

    export async function signUpwithSocialMedia(
        uniqueId: string,
        email: string,
        verifiedStatus: string,
        role: string,
        medium: Medium,
        firstName: string,
        lastName: string,
    ): Promise<IUser> {
        const payRate = process.env.DEFAULT_PAY_RATE_OF_THERAPIST;
        let userDetails: DTherapist | DClient = null;

        if (medium == Medium.FACEBOOK) {
            if (role == UserRole.THERAPIST) {
                let payRateTypeData: any = {
                    type: PayRateTypes.FLAT_USER,
                    flatValue: 50,
                };
                userDetails = {
                    email: email,
                    facebookId: uniqueId,
                    medium: medium,
                    photoId: null,
                    coverPhotoId: null,
                    verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                    role: role,
                    adminApproved: false,
                    blockedByAdmin: false,
                    callRecordingAllowed: true,
                    hideCallTimer: false,
                    payRate: Number(payRate),
                    payRateType: payRateTypeData,
                    claimOpen: false,
                    firstname: firstName,
                    lastname: lastName
                };
            }

            if (role == UserRole.CLIENT) {
                userDetails = {
                    email: email,
                    facebookId: uniqueId,
                    medium: medium,
                    photoId: null,
                    coverPhotoId: null,
                    verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                    role: role,
                    adminApproved: true,
                    blockedByAdmin: false,
                    premiumStatus: PremiumStatus.NOT_SET,
                    chatWordCount: 0,
                    callRecordingAllowed: true,
                    hideCallTimer: false,
                    firstname: firstName,
                    lastname: lastName
                };
            }
        }

        if (medium == Medium.GOOGLE) {
            if (role == UserRole.THERAPIST) {
                let payRateTypeData: any = {
                    type: PayRateTypes.FLAT_USER,
                    flatValue: 50,
                };
                userDetails = {
                    email: email,
                    googleId: uniqueId,
                    medium: medium,
                    photoId: null,
                    coverPhotoId: null,
                    verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                    role: role,
                    adminApproved: false,
                    blockedByAdmin: false,
                    callRecordingAllowed: true,
                    hideCallTimer: false,
                    payRate: Number(payRate),
                    payRateType: payRateTypeData,
                    claimOpen: false,
                    firstname: firstName,
                    lastname: lastName
                };
            }

            if (role == UserRole.CLIENT) {
                userDetails = {
                    email: email,
                    googleId: uniqueId,
                    medium: medium,
                    photoId: null,
                    coverPhotoId: null,
                    verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                    role: role,
                    adminApproved: true,
                    blockedByAdmin: false,
                    premiumStatus: PremiumStatus.NOT_SET,
                    chatWordCount: 0,
                    callRecordingAllowed: true,
                    hideCallTimer: false,
                    firstname: firstName,
                    lastname: lastName
                };
            }
        }

        const user = new User(userDetails);
        const newUser = await user.save();

        // if (newUser) {
        //   const authToken = await user.createAccessToken();

        //   const data = {
        //     userId: newUser._id,
        //     authToken: authToken,
        //   };

        //   return data;
        // }

        return newUser;
    }

    export async function signUpwithClientSocialMedia(
        uniqueId: string,
        email: string,
        verifiedStatus: string,
        role: string,
        medium: Medium,
        firstname: string,
        lastname: string,
        gender: string,
        dateOfBirth: Date,
        ethnicityId: Types.ObjectId,
        username: string,
        state: string,
        zipCode: string,
        primaryPhone: string,
        PersonalizeMatchData: any,
        skip?: boolean,
    ): Promise<any> {
        const payRate = process.env.DEFAULT_PAY_RATE_OF_THERAPIST;
        let userDetails: DTherapist | DClient = null;

        if (medium == Medium.FACEBOOK) {
            if (role == UserRole.CLIENT) {
                userDetails = {
                    email: email,
                    facebookId: uniqueId,
                    medium: medium,
                    photoId: null,
                    coverPhotoId: null,
                    verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                    role: role,
                    adminApproved: true,
                    blockedByAdmin: false,
                    premiumStatus: PremiumStatus.NOT_SET,
                    chatWordCount: 0,
                    callRecordingAllowed: true,
                    hideCallTimer: false,
                    firstname: firstname,
                    lastname: lastname,
                    gender: gender,
                    dateOfBirth: dateOfBirth,
                    ethnicityId: ethnicityId,
                    username: username,
                    state: state,
                    zipCode: zipCode,
                    primaryPhone: primaryPhone,
                    PersonalizeMatchData: PersonalizeMatchData,
                    skip: skip,
                };
            }
        }

        if (medium == Medium.GOOGLE) {
            if (role == UserRole.CLIENT) {
                userDetails = {
                    email: email,
                    googleId: uniqueId,
                    medium: medium,
                    photoId: null,
                    coverPhotoId: null,
                    verifiedStatus: verifiedStatus ? verifiedStatus : UserStatus.PENDING,
                    role: role,
                    adminApproved: true,
                    blockedByAdmin: false,
                    premiumStatus: PremiumStatus.NOT_SET,
                    chatWordCount: 0,
                    callRecordingAllowed: true,
                    hideCallTimer: false,
                    firstname: firstname,
                    lastname: lastname,
                    gender: gender,
                    dateOfBirth: dateOfBirth,
                    ethnicityId: ethnicityId,
                    username: username,
                    state: state,
                    zipCode: zipCode,
                    primaryPhone: primaryPhone,
                    PersonalizeMatchData: PersonalizeMatchData,
                    skip: skip,
                };
            }
        }

        const user = new User(userDetails);
        const newUser = await user.save();

        if (newUser) {
            const authToken = await user.createAccessToken();

            const data = {
                userId: newUser._id,
                email: newUser.email,
                authToken: authToken,
            };

            return data;
        }
        return null;
    }

    export async function addClientDetailsInSignUp(
        userId: Types.ObjectId,
        photoDetails: any,
        clientDetails: DClient
    ): Promise<IUser> {
        let upload;
        let userData = await UserDao.getUserById(userId);

        if (userData !== null) {
            if (photoDetails.photo) {
                const photo: DUpload = {
                    userId: userData._id as unknown as Types.ObjectId,
                    originalName: photoDetails.photo.originalname,
                    name: photoDetails.photo.filename,
                    type: photoDetails.photo.mimetype,
                    path: photoDetails.photo.path,
                    fileSize: photoDetails.photo.size,
                    extension: path.extname(photoDetails.photo.originalname),
                    category: UploadCategory.PROFILE_IMAGE,
                    signRequired: photoDetails.signRequired,
                };

                upload = await UploadDao.createUpload(photo);
            }

            const clientData: DClient = {
                firstname: clientDetails.firstname,
                lastname: clientDetails.lastname,
                dateOfBirth: clientDetails.dateOfBirth,
                gender: clientDetails.gender,
                username: clientDetails.username,
                photoId: upload ? upload._id : null,
                adminApproved: true,
            };

            userData = await UserDao.updateUser(userData._id, clientData);

            return userData;
        } else {
            throw new ApplicationError("Registration failed.");
        }
    }

    export async function addTherapistDetailsInSignUp(
        userId: Types.ObjectId,
        photoDetails: any,
        therapistDetails: DTherapist
    ): Promise<IUser> {
        let upload;
        let userData = await UserDao.getUserById(userId);

        if (userData != null) {
            if (photoDetails.photo) {
                const photo: DUpload = {
                    userId: userData._id as unknown as Types.ObjectId,
                    originalName: photoDetails.photo.originalname,
                    name: photoDetails.photo.filename,
                    type: photoDetails.photo.mimetype,
                    path: photoDetails.photo.path,
                    fileSize: photoDetails.photo.size,
                    extension: path.extname(photoDetails.photo.originalname),
                    category: UploadCategory.PROFILE_IMAGE,
                    signRequired: photoDetails.signRequired,
                };

                upload = await UploadDao.createUpload(photo);
            }

            const therapistData: DTherapist = {
                firstname: therapistDetails.firstname,
                lastname: therapistDetails.lastname,
                dateOfBirth: therapistDetails.dateOfBirth,
                gender: therapistDetails.gender,
                ethnicityId: therapistDetails.ethnicityId,
                photoId: upload ? upload._id : null,
                adminApproved: true,
            };

            userData = await TherapistDao.updateTherapist(
                userData._id,
                therapistData
            );

            return userData;
        } else {
            throw new ApplicationError("Registration failed.");
        }
    }

    export async function contactRequest(
        contactDetails: DContact
    ): Promise<IContact> {
        const request = new Contact(contactDetails);
        let response = await request.save();

        return response;
    }

    export async function submitReview(
        reviewDetails: DLavniReview
    ): Promise<ILavniReview> {
        const review = new Review(reviewDetails);
        let response = await review.save();

        return response;
    }

    export async function updateContactRequest(
        id: Types.ObjectId,
        data: Partial<DContact>
    ): Promise<IContact> {
        const updatedContact = await Contact.findByIdAndUpdate(
            id,
            {$set: data},
            {new: true}
        );

        return updatedContact;
    }

    export async function customerReviewDao(
        cusReview: DCustomerReview
    ): Promise<ICustomerReview> {
        const request = new CustomerReview(cusReview);
        let response = await request.save();
        return response;
    }

    export async function therapistReviewDao(
        therapistReview: DCustomerReview
    ): Promise<ICustomerReview> {
        const request = new CustomerReview(therapistReview);
        let response = await request.save();
        return response;
    }

    export async function addEducationalInfo(details: any): Promise<IEducation> {
        const educationDetails = new Education(details);
        let response = await educationDetails.save();

        if (response) {
            let updatedTherapist = await User.findByIdAndUpdate(
                response.userId,
                {$push: {qualifications: response._id}},
                {new: true}
            );
        }

        return response;
    }

    export async function addLicenseInfo(details: any): Promise<ILicense> {
        const licenseDetails = new License(details);
        let response = await licenseDetails.save();

        if (response) {
            let updatedTherapist = await User.findByIdAndUpdate(
                response.userId,
                {$push: {licenseId: response._id}},
                {new: true}
            );
        }

        return response;
    }

    export async function unSetField(
        userId: Types.ObjectId,
        _fieldName?: string
    ): Promise<any> {
        const response = await User.findByIdAndUpdate(
            userId,
            {
                $unset: {verificationCode: ""},
            },
            {new: true}
        );
        return response;
    }

    // export async function likeTherapist(
    //   clientId: Types.ObjectId,
    //   therapistId: Types.ObjectId
    // ): Promise<boolean> {
    //   const sendingClient = await User.findByIdAndUpdate(
    //     clientId,
    //     {
    //       $push: { likedTherapists: therapistId },
    //     },
    //     { new: true }
    //   );

    //   const receivingTherapist = await User.findByIdAndUpdate(
    //     therapistId,
    //     {
    //       $push: { clientRequests: clientId },
    //     },
    //     { new: true }
    //   );

    //   if (sendingClient && receivingTherapist) {
    //     return true;
    //   }
    //   return false;
    // }

    export async function dislikeTherapist(
        clientId: Types.ObjectId,
        therapistId: Types.ObjectId
    ): Promise<boolean> {
        const updatedClient = await User.findByIdAndUpdate(
            clientId,
            {
                $push: {dislikedTherapists: therapistId},
            },
            {new: true}
        );

        if (updatedClient) {
            return true;
        }

        return false;
    }

    export async function dislikeClient(
        therapistId: Types.ObjectId,
        clientId: Types.ObjectId
    ): Promise<boolean> {
        const updatedTherapist = await User.findByIdAndUpdate(
            therapistId,
            {
                $push: {dislikedClients: clientId},
            },
            {new: true}
        );

        if (updatedTherapist) {
            return true;
        }

        return false;
    }

    export async function blockUser(
        role: string,
        blockedByUserId: Types.ObjectId,
        blockingUserList: Types.ObjectId[],
        // likedClientList: Types.ObjectId[],
        clientRequestsList: Types.ObjectId[]
        // dislikedClientsList: Types.ObjectId[],
        // likedTherapistList: Types.ObjectId[],
        // dislikedTherapistList: Types.ObjectId[]
    ): Promise<IUser> {
        let response = null;
        let clientData: DClient = null;
        let therapistData: DTherapist = null;

        if (role == UserRole.CLIENT) {
            clientData = {
                blockedUser: blockingUserList,
                // likedTherapists: likedTherapistList,
                // dislikedTherapists: dislikedTherapistList,
            };

            response = await ClientDao.updateClient(blockedByUserId, clientData);
        }

        if (role == UserRole.THERAPIST) {
            therapistData = {
                blockedUser: blockingUserList,
                // likedClients: likedClientList,
                // dislikedClients: dislikedClientsList,
            };

            response = await TherapistDao.updateTherapist(
                blockedByUserId,
                clientData
            );
        }
        // const response = await User.findByIdAndUpdate(
        //   blockedByUserId,
        //   {
        //     $push: { blockedUser: blockingUserId },
        //   },
        //   { new: true }
        // );
        return response;
    }

    export async function updatePaymentDetails(
        userId: StringOrObjectId,
        data: Payment
    ): Promise<IUser> {
        const response = await User.findByIdAndUpdate(
            userId,
            {
                $push: {paymentDetails: data},
            },
            {new: true}
        );
        return response;
    }

    export async function getUserByFacebookId(
        facebookId: string
    ): Promise<IUser> {
        const response = await User.findOne({facebookId: facebookId});
        return response;
    }

    export async function getUserByGoogleId(googleId: string): Promise<IUser> {
        const response = await User.findOne({googleId: googleId});

        return response;
    }

    export async function updateRequestByUserId(
        userId: Types.ObjectId,
        requestId: Types.ObjectId
    ): Promise<ITherapist | IClient> {
        let response = await User.findByIdAndUpdate(
            userId,
            {
                $push: {friendRequests: requestId},
            },
            {new: true}
        );
        return response;
    }

    export async function getAllThemeImages(): Promise<IUpload[]> {
        const response = await Upload.find({category: UploadCategory.THEMES});
        return response;
    }

    export async function getUserByUsername(username: string): Promise<IUser> {
        const response = await User.findOne({username: username});
        return response;
    }

    export async function updateUserAvatarUrl(
        id: ObjectId,
        avatarId: ObjectId,
        avatarBackgroundId: string,
        avatarImage: string
    ): Promise<any> {
        const updatedUser = await User.findByIdAndUpdate(
            id,
            {
                avatarId: avatarId,
                avatarBackgroundId: avatarBackgroundId,
                useDefaultAvatar: false,
                avatarImage: avatarImage,
            },
            {new: true}
        );
        const uploadData = await Upload.findById(updatedUser.avatarId);
        const finalData = {
            useDefaultAvatar: false,
            avatarId: uploadData.url,
            avatarBackgroundId: updatedUser.avatarBackgroundId,
        };
        return finalData;
    }

    export async function updateUserDefaultAvatar(
        id: ObjectId,
        avatarId: string
        // avatarBackgroundId: string
    ): Promise<IUser> {
        const updatedUser = await User.findByIdAndUpdate(
            id,
            {
                defaultAvatarId: avatarId,
                // avatarBackgroundId: avatarBackgroundId,
                useDefaultAvatar: true,
            },
            {new: true}
        );
        return updatedUser;
    }

    export async function changeAvatarBackground(
        id: ObjectId,
        avatarBackgroundId: string
    ): Promise<IUser> {
        const updatedUser = await User.findByIdAndUpdate(
            id,
            {avatarBackgroundId: avatarBackgroundId},
            {new: true}
        );
        return updatedUser;
    }

    export async function getAvatarDetails(id: any): Promise<any> {
        const response = await User.findById(id);
        let subStatus;
        if (response.role == UserRole.CLIENT) {
            let responseClient: IClient = await Client.findById(response._id);
            if (responseClient != null) {
                subStatus = responseClient.subscriptionStatus;
            } else {
                subStatus = "";
            }
        } else {
            subStatus = "active";
        }
        let finalData;
        if (response.useDefaultAvatar !== undefined) {
            if (response.useDefaultAvatar) {
                finalData = {
                    useDefaultAvatar: true,
                    avatarId: response.defaultAvatarId,
                    avatarBackgroundId: response.avatarBackgroundId,
                    incognito:
                        response.incognito != null &&
                        response.incognito &&
                        response.role == UserRole.CLIENT
                            ? true
                            : false,
                    socketId: response.socketId,
                    callerName: response.firstname,
                    subscriptionStatus: subStatus,
                    ownRole: response.role,
                    callRecordingAllowed:
                        response.callRecordingAllowed != null &&
                        response.callRecordingAllowed
                            ? true
                            : false,
                };
            } else {
                const uploadData = await Upload.findById(response.avatarId);
                finalData = {
                    useDefaultAvatar: false,
                    avatarId: uploadData.url,
                    avatarBackgroundId: response.avatarBackgroundId,
                    incognito:
                        response.incognito != null &&
                        response.incognito &&
                        response.role == UserRole.CLIENT
                            ? true
                            : false,
                    socketId: response.socketId,
                    callerName: response.firstname,
                    subscriptionStatus: subStatus,
                    ownRole: response.role,
                    callRecordingAllowed:
                        response.callRecordingAllowed != null &&
                        response.callRecordingAllowed
                            ? true
                            : false,
                };
            }
        } else {
            finalData = {
                useDefaultAvatar: true,
                avatarId: response.gender === "Female" ? "avatarTwo" : "avatarOne",
                avatarBackgroundId:
                    response.avatarBackgroundId !== undefined
                        ? response.avatarBackgroundId
                        : "backgroundOne",
                incognito:
                    response.incognito != null &&
                    response.incognito &&
                    response.role == UserRole.CLIENT
                        ? true
                        : false,
                socketId: response.socketId,
                callerName: response.firstname,
                subscriptionStatus: subStatus,
                ownRole: response.role,
                callRecordingAllowed:
                    response.callRecordingAllowed != null && response.callRecordingAllowed
                        ? true
                        : false,
            };
        }

        return finalData;
    }

    export async function getAvatarDetailsWithMeetingDetails(
        id: any,
        role: any,
        currentUserId: any
    ): Promise<any> {
        const response = await User.findById(id);

        let subscriptionId;
        let subStatus;
        let premiumStatus;
        let isMeetingTimeRemained;
        let totalMeetingTime;

        if (role == UserRole.CLIENT) {
            const remainTimeOne = await Meeting.aggregate([
                {
                    $match: {
                        therapistId: response._id,
                        clientId: currentUserId,
                        accepted: true,
                    },
                },
                {
                    $group: {
                        _id: null,
                        total: {
                            $sum: {
                                $cond: {
                                    if: {$gt: ["$spentDuration", null]},
                                    then: "$spentDuration",
                                    else: "$meetingDuration",
                                },
                            },
                        },
                    },
                },
            ]);
            totalMeetingTime =
                remainTimeOne.length !== 0 && remainTimeOne[0] && remainTimeOne[0].total
                    ? remainTimeOne[0].total
                    : 0;
        } else {
            const remainTimeTwo = await Meeting.aggregate([
                {
                    $match: {
                        therapistId: currentUserId,
                        clientId: response._id,
                        accepted: true,
                    },
                },
                {
                    $group: {
                        _id: null,
                        total: {
                            $sum: {
                                $cond: {
                                    if: {$gt: ["$spentDuration", null]},
                                    then: "$spentDuration",
                                    else: "$meetingDuration",
                                },
                            },
                        },
                    },
                },
            ]);

            totalMeetingTime =
                remainTimeTwo.length !== 0 && remainTimeTwo[0] && remainTimeTwo[0].total
                    ? remainTimeTwo[0].total
                    : 0;
        }

        if (
            (totalMeetingTime && totalMeetingTime >= 240) ||
            (totalMeetingTime && 240 - totalMeetingTime < 30)
        ) {
            isMeetingTimeRemained = false;
        } else {
            isMeetingTimeRemained = true;
        }

        if (response.role == UserRole.CLIENT) {
            let responseClient: IClient = await Client.findById(response._id);

            if (responseClient != null) {
                subscriptionId = responseClient.subscriptionId;
                subStatus = responseClient.subscriptionStatus;
                premiumStatus = responseClient.premiumStatus;
            } else {
                subStatus = "";
                subscriptionId = "";
                premiumStatus = "";
            }
        }

        let finalData;

        if (response.useDefaultAvatar !== undefined) {
            if (response.useDefaultAvatar) {
                finalData = {
                    useDefaultAvatar: true,
                    avatarId: response.defaultAvatarId,
                    avatarBackgroundId: response.avatarBackgroundId,
                    incognito:
                        response.incognito != null &&
                        response.incognito &&
                        response.role == UserRole.CLIENT
                            ? true
                            : false,
                    socketId: response.socketId,
                    callerName: response.firstname,
                    subscriptionId: subscriptionId,
                    subscriptionStatus: subStatus,
                    premiumStatus: premiumStatus,
                    isMeetingTimeRemained: isMeetingTimeRemained,
                    remainingMeetingTime: 240 - totalMeetingTime,
                    callRecordingAllowed:
                        response.callRecordingAllowed != null &&
                        response.callRecordingAllowed
                            ? true
                            : false,
                };
            } else {
                const uploadData = await Upload.findById(response.avatarId);
                finalData = {
                    useDefaultAvatar: false,
                    avatarId: uploadData.url,
                    avatarBackgroundId: response.avatarBackgroundId,
                    incognito:
                        response.incognito != null &&
                        response.incognito &&
                        response.role == UserRole.CLIENT
                            ? true
                            : false,
                    socketId: response.socketId,
                    callerName: response.firstname,
                    subscriptionId: subscriptionId,
                    subscriptionStatus: subStatus,
                    premiumStatus: premiumStatus,
                    isMeetingTimeRemained: isMeetingTimeRemained,
                    remainingMeetingTime: 240 - totalMeetingTime,
                    callRecordingAllowed:
                        response.callRecordingAllowed != null &&
                        response.callRecordingAllowed
                            ? true
                            : false,
                };
            }
        } else {
            finalData = {
                useDefaultAvatar: true,
                avatarId: response.gender === "Female" ? "avatarTwo" : "avatarOne",
                avatarBackgroundId:
                    response.avatarBackgroundId !== undefined
                        ? response.avatarBackgroundId
                        : "backgroundOne",
                incognito:
                    response.incognito != null &&
                    response.incognito &&
                    response.role == UserRole.CLIENT
                        ? true
                        : false,
                socketId: response.socketId,
                callerName: response.firstname,
                subscriptionId: subscriptionId,
                subscriptionStatus: subStatus,
                premiumStatus: premiumStatus,
                isMeetingTimeRemained: isMeetingTimeRemained,
                remainingMeetingTime: 240 - totalMeetingTime,
                callRecordingAllowed:
                    response.callRecordingAllowed != null && response.callRecordingAllowed
                        ? true
                        : false,
            };
        }

        return finalData;
    }

    export async function findUserBySocketId(id: any): Promise<IUser> {
        const user = await User.findOne({socketId: id}, {new: true});
        return user;
    }

    export async function updateClientIncognitoMode(
        id: StringOrObjectId,
        mode: boolean
    ): Promise<IUser> {
        let user = await Client.findByIdAndUpdate(
            id,
            {
                incognito: mode,
                incognitoPopupShow: true,
            },
            {new: true}
        );
        return user;
    }

    export async function getUserIncognitoModeById(
        id: StringOrObjectId
    ): Promise<any> {
        let user = await User.findById(id);
        const incognitoMode = user.incognito;
        return {incognitoMode};
    }

    export async function updateClientIncognitoPopupShow(
        id: StringOrObjectId
    ): Promise<IUser> {
        let user = await Client.findByIdAndUpdate(
            id,
            {
                incognitoPopupShow: true,
            },
            {new: true}
        );
        return user;
    }

    export async function updateClientIncognitoPopupShowFalse(
        id: StringOrObjectId
    ): Promise<IUser> {
        let user = await Client.findByIdAndUpdate(id, {
            incognitoPopupShow: false,
        });
        return user;
    }

    export async function deleteUserById(
        userId: StringOrObjectId,
        deleteUploads: (value: StringOrObjectId) => Promise<boolean>
    ): Promise<IUser> {
        const user: any = await User.findById(userId).populate([
            {path: "coverPhotoId"},
        ]);

        if (user?.coverPhotoId?.category !== "THEMES") {
            await deleteUploads(user?.coverPhotoId);
        }

        if (user?.photoId) {
            await deleteUploads(user?.photoId);
        }

        const response = await User.findByIdAndDelete(userId);

        return response;
    }

    export async function getTherapistByProfessionId(
        pId: StringOrObjectId,
        limit: number,
        offset: number
    ): Promise<ITherapist[]> {
        const response = await Therapist.find({profession: pId})
            .sort({createdAt: -1})
            .skip(offset)
            .limit(limit);
        return response;
    }

    export async function updateCallRecordingAllowed(
        id: StringOrObjectId,
        value: boolean
    ): Promise<IUser> {
        let user = await User.findByIdAndUpdate(id, {
            callRecordingAllowed: value,
        });
        return user;
    }

    export async function updateHideCallTimer(
        id: StringOrObjectId,
        value: boolean
    ): Promise<IUser> {
        let user = await User.findByIdAndUpdate(id, {
            hideCallTimer: value,
        });
        return user;
    }

    export async function createGoolgeCalendarRefreshToken(
        id: ObjectId,
        token: string,
        googleCalendarAccess: boolean
    ): Promise<IUser> {
        const updatedUser = await User.findByIdAndUpdate(
            id,
            {
                googleCalendarRefreshToken: token,
                googleCalendarAccess: googleCalendarAccess,
            },
            {new: true}
        );
        return updatedUser;
    }

    export async function getUserByIdGooleClendar(
        id: StringOrObjectId
    ): Promise<IUser> {
        let user: IUser = await User.findById(id);

        return user;
    }

    export async function getUserGoogleCalendarRefreshTokenById(
        id: StringOrObjectId
    ): Promise<{ googleCalendarRefreshToken: string | null }> {
        const user = await User.findById(id).select("googleCalendarRefreshToken").lean();
        return user ? {googleCalendarRefreshToken: user.googleCalendarRefreshToken} : {googleCalendarRefreshToken: null};
    }

    export async function inactiveGoogleCalendarStatus(
        id: ObjectId,
        googleCalendarAccess: boolean
    ): Promise<IUser> {
        const updatedUser = await User.findByIdAndUpdate(
            id,
            {
                googleCalendarAccess: googleCalendarAccess,
            },
            {new: true}
        );
        return updatedUser;
    }

    export async function getAllApprovedLavniReviews(
        limit?: number,
        offset?: number
    ): Promise<ILavniReview[]> {
        const rivewList = await LavniReview.find({
            status: LavniReviewStatus.APPROVED
        })
            .sort({createdAt: -1})
            .skip(offset)
            .limit(limit);

        return rivewList;
    }

    export async function getAllAppointmentsByClientId(
        clientId: string
    ): Promise<IAppointment[]> {
        const response = await Appointment.find({
            clientId: Types.ObjectId(clientId),
            smsStatus: AppointmentSMSStatus.BEFORE24SMS,
        }).sort({createdAt: -1});
        return response;
    }

    // get therapist reviews
    export async function getAllApprovedTherapistReviews(
        limit?: number,
        offset?: number,
        therapistId?: string
    ): Promise<ICustomerReview[]> {
        const reportList = await CustomerReview.find({
            status: "APPROVED",
            therapistId: therapistId,
        })
            .populate(populateOptions2)
            .sort({createdAt: -1})
            .skip(offset)
            .limit(limit);
        return reportList;
    }

    export async function getClientAndTherapistEmailsByMeetingId(meetingId: string) {
        const meeting = await Meeting.findOne({meetingId}, 'clientId therapistId');

        let returnObj = {
            clientEmail: '',
            therapistEmail: '',
            meetingId: ''
        };

        if (meeting == null) {
            return returnObj;
        }
        const clientEmail = await User.findById(meeting.clientId, 'email');
        const therapistEmail = await User.findById(meeting.therapistId, 'email');

        returnObj.clientEmail = clientEmail.email;
        returnObj.therapistEmail = therapistEmail.email;
        returnObj.meetingId = meetingId;

        return returnObj;
    }

    export async function getInsuranceListByClientId(
        clientId: StringOrObjectId
    ): Promise<IInsurance[]> {
        const response = await Insurance.find({clientId: clientId})
            .populate(populateOptions)
            .populate({
                path: 'insuranceCompanyId',
                model: 'InsuranceCompany',
                select: 'insuranceCompany isCommercialInsurance isMedicaid',
            }).sort({createdAt: -1});

        return response;
    }

    export async function getInsuranceById(
        id: StringOrObjectId
    ): Promise<IInsurance> {
        const insuranceData = await Insurance.findById(id).populate('insuranceCompanyId').exec();
        return insuranceData;
    }

    export async function updateUserInsurance(
        id: StringOrObjectId,
        insuranceId: StringOrObjectId,
        secondaryInsuranceId: StringOrObjectId,
        copaymentAmount: number,
    ): Promise<IUser> {
        const insuranceObjectId = new Types.ObjectId(insuranceId);
        const secondaryInsuranceIdObjectId = new Types.ObjectId(secondaryInsuranceId);
        let user = await User.findByIdAndUpdate(
            id,
            {
                insuranceId: insuranceObjectId,
                secondaryInsuranceId: secondaryInsuranceIdObjectId,
                copaymentAmount: copaymentAmount
            },
            {new: true}
        );
        return user;
    }

    export async function addUserSecondaryInsurance(
        id: StringOrObjectId,
        secondaryInsuranceId: StringOrObjectId,
    ): Promise<IUser> {
        const secondaryInsuranceIdObjectId = new Types.ObjectId(secondaryInsuranceId);
        let user = await User.findByIdAndUpdate(
            id,
            {
                secondaryInsuranceId: secondaryInsuranceIdObjectId,
            },
            {new: true}
        );
        await Meeting.updateMany(
            {
                clientId: id,
                'copayment.status': {$in: ["NOTSUBMITTED", "UNPAID"]},
            },
            {
                $set: {'copayment.status': "PAID", 'copayment.details': "Skip with secondary insurance"}
            }
        );
        return user;
    }

    export async function getTherapistByUserIdLimited(
        id: StringOrObjectId
    ): Promise<any> {
        const startOfTheCurrentDay = moment().startOf("day").toDate();

        const response = await User.aggregate([
            {$match: {_id: id}},
            {
                $project: {
                    _id: 1,
                    role: 1,
                    therapyState: 1,
                    workingHours: 1,
                    state: 1,
                    blockedDates: 1,
                    firstname: 1,
                    lastname: 1,
                    disclosureStatementId: 1,
                    coverPhotoId: 1,
                    photoId: 1,
                },
            },
            {
                $addFields: {
                    blockedDates: {
                        $cond: {
                            if: {$ne: ["$blockedDates", null]},
                            then: {
                                $map: {
                                    input: {
                                        $filter: {
                                            input: "$blockedDates",
                                            as: "dateRange",
                                            cond: {
                                                $or: [
                                                    {
                                                        $gte: [
                                                            {$toDate: "$$dateRange.start"},
                                                            startOfTheCurrentDay,
                                                        ],
                                                    },
                                                    {
                                                        $gte: [
                                                            {$toDate: "$$dateRange.end"},
                                                            startOfTheCurrentDay,
                                                        ],
                                                    },
                                                ],
                                            },
                                        },
                                    },
                                    as: "dateRange",
                                    in: {
                                        start: "$$dateRange.start",
                                        end: "$$dateRange.end",
                                    },
                                },
                            },
                            else: null,
                        },
                    },
                },
            },
        ]);

        return response[0];
    }

    export async function searchInsuranceData(): Promise<any[]> {
        let searchResult: any[] = await Insurance.aggregate([
            {
                $match: {
                    'insuranceCompanyId': Types.ObjectId('64c2c18d11b5d3eccc3f2bfe'),
                },
            },
            {
                $project: {
                    '_id': 1,
                    'clientId': 1,
                    'insuranceCompanyId': 1,
                    'clientTreatmenthistories.meetingId': 1,
                }
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {email: 1, firstname: 1, _id: 1},
                        },
                    ],
                    as: "clientId",
                },
            },
            {
                $unwind: {
                    path: "$clientId",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "treatmenthistories",
                    localField: "clientId._id",
                    foreignField: "clientId",
                    pipeline: [
                        {
                            $project: {
                                createdAt: 1,
                                meetingId: 1,
                                meetingStartedTime: 1,
                                _id: 1
                            },
                        },
                    ],
                    as: "clientTreatmenthistories",
                },
            },
            {
                $unwind: {
                    path: "$clientTreatmenthistories",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: "meetings",
                    localField: "clientTreatmenthistories.meetingId",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                callingStatus: 1,
                            },
                        },
                    ],
                    as: "meeting",
                },
            },
            {
                $unwind: "$meeting",
            },
        ]);
        return searchResult;
    }

    export async function getAllExperienceTagSymptomsPublic(): Promise<any[]> {
        try {
            const allIssues = await ExperienceTagSymptomSchema.find().sort({symptomName: 1});

            const data = allIssues;
            data.sort((a: any, b: any) => a.symptomName.localeCompare(b.symptomName));
            const middleIndex = Math.ceil(data.length / 2);
            const firstHalf = data.slice(0, middleIndex);
            const secondHalf = data.slice(middleIndex);

            const combinedArray = [];
            for (let i = 0; i < Math.max(firstHalf.length, secondHalf.length); i++) {
                if (i < firstHalf.length) combinedArray.push(firstHalf[i]);
                if (i < secondHalf.length) combinedArray.push(secondHalf[i]);
            }
            return combinedArray;
        } catch (error) {
            return [];
        }
    }

  export async function updateUserFCMToken(userId: StringOrObjectId, token: string): Promise<any> {
    const userIdAsObjectId = new Types.ObjectId(userId);
    const user = await User.findByIdAndUpdate(
      userIdAsObjectId, 
      { FCMToken: token },
      { new: true }
    ).select('_id');;
    if (!user) {
      throw new ApplicationError("User not found for Id: " + userId);
    }
    return user;
  }

  export async function getUserFCMTokenById(userId: StringOrObjectId): Promise<any> {
    const userIdAsObjectId = new Types.ObjectId(userId);
    const user = await User.findById(userIdAsObjectId).select("FCMToken");
    if (!user) {
      throw new ApplicationError("User not found for Id: " + userId);
    }
    return user;
  }
  

  export async function getUserByFirstNameLastName(firstName: string, lastName: string): Promise<IUser> {
    let user: IUser = await Therapist.findOne({
      firstname: { $regex: new RegExp(`^${firstName}$`, 'i') },
      lastname: { $regex: new RegExp(`^${lastName}$`, 'i') }
    }).populate([
      {path: "photoId"},
      {path: "coverPhotoId"},
      {path: "videoId"},
      {path: "profession"},
      {path: "disclosureStatementId"},
      {path: "professionLicense"},
      {path: "experiencedIn"},
      {path: "insuranceCompanies"},
      {path: "insuranceId"},
      {path: "ethnicityId"},
      {path: "preference", populate: {path: "experiencedIn"}},
      {
        path: "friendRequests",
        select: {status: 1, clientId: 1, therapistId: 1},
      }
    ]);

    console.log("user ",user)

    if (!user) {
      throw new ApplicationError("User not found with firstname: " + firstName + " and lastname: " + lastName);
    }

    return user;
  }

  export async function getUserMediumByEmail(email: string): Promise<IUser | null> {
    let user = await User.findOne({ email: email }).select("medium");
    return user;
}
}
