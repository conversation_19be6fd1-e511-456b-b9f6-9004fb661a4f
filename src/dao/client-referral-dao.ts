import { IClientReferral } from "../models/client-referral-model";
import ClientReferral from "../schemas/client-referral-schema";

export namespace ClientReferralDao {

  export async function addDetails(
    referrerUserId: ObjectId,
    referredUserId: ObjectId,
  ): Promise<IClientReferral> {

    const referral = new ClientReferral({
      referrerUserId: referrerUserId,
      referredUserId: referredUserId
    });

    const clientReferral = await referral.save();

    return clientReferral;
  }

}

