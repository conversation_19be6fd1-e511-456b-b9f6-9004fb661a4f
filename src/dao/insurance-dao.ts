import { StringOrObjectId, Util } from "../common/util";
import { Types } from "mongoose";
import { DInsurance, IInsurance } from "../models/insurance-model";
import Insurance from "../schemas/insurance-schema";
import Client from "../schemas/client-schema";
import DiagnosisNote from "../schemas/diagnosis-note-schema";
import { DDiagnosisNote, IDiagnosisNote } from "../models/diagnosis-note-model";
import { IMeeting } from "../models/meeting-model";
import Meeting from "../schemas/meeting-schema";
import InsuranceCompany from "../schemas/Insurance-company-schema";

export namespace InsuranceDao {
  const populateOptions = [
    {
      path: "insuranceCardId",
    },
    {
      path: "insuranceCardBackId",
    },
    {
      path: "insuranceCompanyId",
    },
  ];

  // to create
  export async function addInsuranceInfo(
    userId: Types.ObjectId,
    details: any
  ): Promise<IInsurance> {
    const insuranceDetails = new Insurance(details);
    let response = await insuranceDetails.save();

    if (response) {
      let insuranceCompanyData = await Insurance.findById(response._id).populate('insuranceCompanyId').exec();
      const copaymentAmount = insuranceCompanyData?.insuranceCompanyId?.coPayment ? insuranceCompanyData?.insuranceCompanyId?.coPayment : 0;
      let updatedTherapist = await Client.findByIdAndUpdate(
        userId,
        {
          insuranceId: response._id,
          copaymentAmount: copaymentAmount,
        },
        { new: true }
      );
    }

    const res = await Insurance.populate(response, populateOptions);

    return res;
  }

  export async function addSeondaryInsuranceInfo(
    userId: Types.ObjectId,
    details: any
  ): Promise<IInsurance> {
    const seondaryInsurance = new Insurance(details);
    let response = await seondaryInsurance.save();

    if (response) {
      let updatedTherapist = await Client.findByIdAndUpdate(
        userId,
        { secondaryInsuranceId: response._id },
        { new: true }
      );

      await Meeting.updateMany(
        {
          clientId: userId,
          'copayment.status': { $in: ["NOTSUBMITTED", "UNPAID"] },
        },
        {
          $set: { 'copayment.status': "PAID", 'copayment.details': "Skip with secondary insurance" }
        }
      );
    }

    const res = await Insurance.populate(response, populateOptions);

    return res;
  }

  export async function updateInsurancePlan(
    id: StringOrObjectId,
    data: Partial<DInsurance>
  ): Promise<IInsurance> {
    let insurancePlan = await Insurance.findByIdAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    ).populate(populateOptions);
    return insurancePlan;
  }

  export async function getInsurancePlanById(
    insuranceId: Types.ObjectId
  ): Promise<IInsurance> {
    const response = await Insurance.findById(insuranceId).populate(
      populateOptions
    );
    return response;
  }

  export async function getInsuranceByClientId(
    clientId: StringOrObjectId
  ): Promise<IInsurance> {
    const response = await Insurance.findOne({ clientId: clientId })
      .populate(populateOptions)
      .populate({
        path: 'insuranceCompanyId',
        model: 'InsuranceCompany',
        select: 'insuranceCompany isMedicaid',
      }).sort({ createdAt: -1 });

    return response;
  }

  export async function deleteInsuranceDetailsByClientId(
    clientId: StringOrObjectId
    //deleteUploads:(value:StringOrObjectId) => Promise<boolean>
  ): Promise<number> {
    const response = await Insurance.deleteMany({
      clientId: clientId,
    });
    return response.ok;
  }

  export async function getDetailsForEligibility(
    insuranceId: Types.ObjectId,
    therapistId?: Types.ObjectId
  ): Promise<any[]> {
    const response = await Insurance.aggregate([
      {
        $match: {
          _id: insuranceId,
        },
      },
      {
        $project: {
          clientId: 1,
          insuranceCompanyId: 1,
          subscriber: 1,
          dependent: 1,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                primaryPhone: 1,
                email: 1,
                firstname: 1,
                dateOfBirth: 1,
                gender: 1,
                lastname: 1,
                stripeCustomerId: 1,
                copaymentAmount: 1
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);

    return response;
  }


  export async function getDetailsForClaimSubmission(
    noteId: Types.ObjectId
  ): Promise<IInsurance[]> {
    const response = await DiagnosisNote.aggregate([
      {
        $match: {
          _id: noteId,
        },
      },
      {
        $project: {
          clientId: 1,
          therapistId: 1,
          diagnosisICDcodes: 1,
          secondaryDiagnosisICDcodes: 1,
          patientID: 1,
          encounterID: 1,
          meetingId: 1,
          encounterDate: 1,
          cptCode: 1,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                nPI1: 1,
                nPI2: 1,
                primaryPhone: 1,
                firstname: 1,
                lastname: 1,
                middlename: 1,
                socialSecurity: 1,
                taxonomyCode: 1,
              },
            },
          ],
          as: "therapistDetails",
        },
      },
      {
        $unwind: {
          path: "$therapistDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                primaryPhone: 1,
                firstname: 1,
                lastname: 1,
                dateOfBirth: 1,
                gender: 1,
                copaymentAmount: 1,
                claimEligibilityDetails: 1,
                insuranceId: 1
              },
            },
          ],
          as: "clientDetails",
        },
      },
      {
        $unwind: {
          path: "$clientDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "meetings",
          localField: "meetingId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                createdAt: 1,
                spentDuration: 1,
                meetingDuration: 1,
                _id: 1,
              },
            },
          ],
          as: "meetingId",
        },
      },
      {
        $unwind: {
          path: "$meetingId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "insurances",
          localField: "clientDetails.insuranceId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                _id: 1,
                insuranceCompanyId: 1,
                subscriber: 1,
                dependent: 1,
              },
            },
          ],
          as: "clientInsuranceDetails",
        },
      },
      {
        $unwind: {
          path: "$clientInsuranceDetails",
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: "insurancecompanies",
          localField: "clientInsuranceDetails.insuranceCompanyId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                _id: 1,
                insuranceCompany: 1,
                contractPrice: 1,
                coPayment: 1,
              },
            },
          ],
          as: "clientInsuranceCompanyDetails",
        },
      },
      {
        $unwind: {
          path: "$clientInsuranceCompanyDetails",
          preserveNullAndEmptyArrays: true,
        },
      },

    ]);

    return response;
  }

  export async function getDetailsForClaimStatus(
    noteId: Types.ObjectId
  ): Promise<any[]> {
    const response = await DiagnosisNote.aggregate([
      {
        $match: {
          _id: noteId,
        },
      },
      {
        $project: {
          clientId: 1,
          therapistId: 1,
          encounterDate: 1,
          meetingId: 1,
          createdAt: 1,
        },
      },
      {
        $lookup: {
          from: "insurances",
          localField: "clientId",
          foreignField: "clientId",
          pipeline: [
            {
              $project: {
                insuranceCompanyId: 1,
                subscriber: 1,
                dependent: 1,
              },
            },
          ],
          as: "clientInsuranceDetails",
        },
      },
      {
        $unwind: {
          path: "$clientInsuranceDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                nPI1: 1,
                nPI2: 1,
                firstname: 1,
                lastname: 1,
                taxonomyCode: 1,
                claimOpen: 1,
                _id: 1,
              },
            },
          ],
          as: "therapistDetails",
        },
      },
      {
        $lookup: {
          from: "meetings",
          localField: "meetingId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                sessionAmount: 1,
                insuranceClaim: 1,
                copayment: 1,
                createdAt: 1,
                _id: 1,
              },
            },
          ],
          as: "meetingDetails",
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                email: 1,
                primaryPhone: 1,
                username: 1,
                city: 1,
                dateOfBirth: 1,
                gender: 1,
                state: 1,
                zipCode: 1,
                streetAddress: 1,
                insuranceId: 1,
                stripeCustomerId: 1,
                copaymentAmount: 1,
                secondaryInsuranceId: 1,
                claimEligibilityDetails: 1,
                claimEligibilityMdDetails: 1,
                _id: 1,
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$meetingDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$therapistDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);
    return response;
  }

  export async function getInsuranceByControlNumber(
    controlNumber: string
  ): Promise<IInsurance[]> {
    const response = Insurance.find({ controlNumber: controlNumber });
    return response;
  }

  export async function getPendingCopayments(
    userId: Types.ObjectId
  ): Promise<IMeeting[]> {
    const response = await Meeting.aggregate([
      {
        $match: {
          clientId: userId,
          'copayment.status': { $in: ["NOTSUBMITTED", "UNPAID"] },
          'copayment.amount': { $gt: 0 }
        },
      },
      {
        $project: {
          clientId: 1,
          copayment: 1,
          callingStatus: 1,
          therapistId: 1,
          createdAt: 1,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
              },
            },
          ],
          as: "therapistId",
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                gender: 1,
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);

    return response;
  }

  export async function getPaidCopayments(
    userId: Types.ObjectId
  ): Promise<IMeeting[]> {
    const response = await Meeting.aggregate([
      {
        $match: {
          clientId: userId,
          'copayment.status': { $in: ["PAID"] },
          'copayment.amount': { $gt: 0 }
        },
      },
      {
        $project: {
          clientId: 1,
          copayment: 1,
          callingStatus: 1,
          therapistId: 1,
          createdAt: 1,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
              },
            },
          ],
          as: "therapistId",
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                gender: 1,
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);

    return response;
  }

  export async function getInsuaranceCoPaymentAndContractPriceById(
    insuranceId: Types.ObjectId
  ): Promise<any> {
    const insurance = await InsuranceCompany.findById(insuranceId);
    return insurance;
  }

  export async function getInsuranceWithAuthorizationForms(
    clientId: Types.ObjectId
  ): Promise<IInsurance[]> {
    const client = await Client.findById(clientId).select('insuranceId').lean();
    const primaryInsuranceId = client?.insuranceId;
  
    const response = await Insurance.aggregate([
      {
        $match: {
          clientId: new Types.ObjectId(clientId)
        }
      },
      {
        $lookup: {
          from: 'insurancecompanies',
          localField: 'insuranceCompanyId',
          foreignField: '_id',
          pipeline: [
            {
              $match: {
                authorizationFormAvailability: { $exists: true, $eq: true }
              }
            },
            {
              $project: {
                _id: 1,
                authorizationFormType: 1
              }
            }
          ],
          as: 'insuranceCompanyId'
        }
      },
      { $unwind: '$insuranceCompanyId' },
      {
        $addFields: {
          'insuranceCompanyId.isPrimary': {
            $eq: ['$_id', primaryInsuranceId]
          }
        }
      },
      {
        $project: {
          _id: 1,
          insuranceCompanyId: 1
        }
      },
      { $sort: { createdAt: -1 } }
    ]);

    return response;
  }

  export async function getInsuranceCompanyById(
    id: StringOrObjectId
  ): Promise<any> {
    const insuranceCompany = await InsuranceCompany.findById(id);
    return insuranceCompany;
  }
}
