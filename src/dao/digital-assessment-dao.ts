import DigitalAssessmentForms from "./../schemas/digital-assessment-form-schema";
import {DDigitalAssessment, } from "../models/digital-assessment-model";
import {Types} from "mongoose";
import {ExperienceSymptoms} from "./../models/sub-models/Digital-AssessmentFormSubModels/experienceSymptoms-model";
import {IDigitalAssessmentGeneration} from "../models/digital-assessment-generation-model";
import DigitalAssessmentGeneration from "../schemas/digital-assessment-generation-shema";

export namespace DigitalAssessmentDao {
    export async function addDigitalAssessmentDetails(
        details: DDigitalAssessment
    ) {
        const request = new DigitalAssessmentForms(details);
        const response = await request.save();

        return response;
    }

    export async function UpdateDigitalAssessmentDetails(
        details: DDigitalAssessment,
        id: string,
        isSendClientEmail: boolean
    ) {
        const updateData: any = {};

        if (details.formHeader) {
            updateData["formHeader"] = details.formHeader;
        }

        if (details.generalInformation) {
            if (details.generalInformation.name) {
                updateData["generalInformation.name"] = details.generalInformation.name;
            }
            if (details.generalInformation.dob) {
                updateData["generalInformation.dob"] = details.generalInformation.dob;
            }
            if (details.generalInformation.knownAs) {
                updateData["generalInformation.knownAs"] =
                    details.generalInformation.knownAs;
            }
            if (details.generalInformation.age) {
                updateData["generalInformation.age"] = details.generalInformation.age;
            }
            if (details.generalInformation.primaryLanguage) {
                updateData[
                    "generalInformation.primaryLanguage.primaryLanguageReadablityStatus"
                    ] =
                    details.generalInformation.primaryLanguage.primaryLanguageReadablityStatus;

                updateData["generalInformation.primaryLanguage.english"] =
                    details.generalInformation.primaryLanguage.english;

                updateData["generalInformation.primaryLanguage.spanish"] =
                    details.generalInformation.primaryLanguage.spanish;

                updateData["generalInformation.primaryLanguage.other"] =
                    details.generalInformation.primaryLanguage.other;

                updateData["generalInformation.primaryLanguage.otherLanguage"] =
                    details.generalInformation.primaryLanguage.otherLanguage;
            }

            // start

            // end

            if (details.generalInformation.consumerPhone) {
                updateData["generalInformation.consumerPhone.consumerPhone.home"] =
                    details.generalInformation.consumerPhone.consumerPhone.home;

                updateData["generalInformation.consumerPhone.consumerPhone.work"] =
                    details.generalInformation.consumerPhone.consumerPhone.work;
                updateData["generalInformation.consumerPhone.consumerPhone.cellPhone"] =
                    details.generalInformation.consumerPhone.consumerPhone.cellPhone;
                updateData["generalInformation.consumerPhone.consumerPhone.other"] =
                    details.generalInformation.consumerPhone.consumerPhone.other;

                updateData["generalInformation.consumerPhone.otherConsumerPhoneType"] =
                    details.generalInformation.consumerPhone.otherConsumerPhoneType;

                updateData[
                    "generalInformation.consumerPhone.otherConsumerPhoneNumber"
                    ] = details.generalInformation.consumerPhone.otherConsumerPhoneNumber;
            }

            if (details.generalInformation.legalGuarianship) {
                updateData["generalInformation.legalGuarianship.legalGuarianship"] =
                    details.generalInformation.legalGuarianship.legalGuarianship;

                updateData[
                    "generalInformation.legalGuarianship.otherLegalGuarianship"
                    ] = details.generalInformation.legalGuarianship.otherLegalGuarianship;
            }
            console.log(details.generalInformation.guardianPhone);
            if (details.generalInformation.guardianPhone) {
                updateData["generalInformation.guardianPhone"] =
                    details.generalInformation.guardianPhone;
            }

            if (details.generalInformation.primaryInformant.primaryInformant) {
                updateData["generalInformation.primaryInformant.primaryInformant"] =
                    details.generalInformation.primaryInformant.primaryInformant;
            }

            if (details.generalInformation.primaryInformant.other) {
                updateData["generalInformation.primaryInformant.other"] =
                    details.generalInformation.primaryInformant.other;
            }

            if (details.generalInformation.informantPhone) {
                updateData["generalInformation.informantPhone"] =
                    details.generalInformation.informantPhone;
            }

            if (details.generalInformation.genderAtBirth) {
                updateData["generalInformation.genderAtBirth"] =
                    details.generalInformation.genderAtBirth;
            }

            if (details.generalInformation.genderIdentity) {
                updateData["generalInformation.genderIdentity"] =
                    details.generalInformation.genderIdentity;
            }

            if (details.generalInformation.sexualOrientation) {
                if (typeof details.generalInformation.sexualOrientation === 'object') {
                    if (details.generalInformation.sexualOrientation.sexualOrientations) {
                        updateData["generalInformation.sexualOrientation.sexualOrientations"] =
                            details.generalInformation.sexualOrientation.sexualOrientations;
                    }
                    if (details.generalInformation.sexualOrientation.isInterestingLGBTService !== undefined) {
                        updateData["generalInformation.sexualOrientation.isInterestingLGBTService"] =
                            details.generalInformation.sexualOrientation.isInterestingLGBTService;
                    }
                } else {
                    // Handle legacy string value
                    updateData["generalInformation.sexualOrientation"] = {
                        sexualOrientations: details.generalInformation.sexualOrientation,
                        isInterestingLGBTService: false
                    };
                }
            }

            if (details.generalInformation.ethnicity) {
                updateData["generalInformation.ethnicity"] =
                    details.generalInformation.ethnicity;
            }
            if (details.generalInformation.raceType) {
                if (details.generalInformation.raceType.race) {
                    updateData["generalInformation.raceType.race"] =
                        details.generalInformation.raceType.race;
                }
                if (details.generalInformation.raceType.otherRace) {
                    updateData["generalInformation.raceType.otherRace"] =
                        details.generalInformation.raceType.otherRace;
                }
            } else {
                updateData["generalInformation.raceType"] = {
                    race: {
                        blackAfricanAmerican: false,
                        alaskaNative: false,
                        whiteCaucasian: false,
                        asian: false,
                        americanIndianNativeAmerican: false,
                        pacificIslander: false,
                        multiracial: false,
                        other: false,
                    },
                    otherRace: ""
                };
            }

            if (details.generalInformation.livingArrangement.livingArrangement) {
                updateData["generalInformation.livingArrangement.livingArrangement"] =
                    details.generalInformation.livingArrangement.livingArrangement;
            }

            if (details.generalInformation.livingArrangement.other) {
                updateData["generalInformation.livingArrangement.other"] =
                    details.generalInformation.livingArrangement.other;
            }

            if (details.generalInformation.maritalStatus) {
                updateData["generalInformation.maritalStatus"] =
                    details.generalInformation.maritalStatus;
            }
            if (details.generalInformation.familySize) {
                updateData["generalInformation.familySize"] =
                    details.generalInformation.familySize;
            }
            if (details.generalInformation.currentEmploymentStatus) {
                updateData["generalInformation.currentEmploymentStatus"] =
                    details.generalInformation.currentEmploymentStatus;
            }
            if (details.generalInformation.employmentHistory) {
                updateData["generalInformation.employmentHistory"] =
                    details.generalInformation.employmentHistory;
            }
            if (details.generalInformation.education) {
                updateData["generalInformation.education"] =
                    details.generalInformation.education;
            }
        }

        if (details.presentingProblem) {
            updateData["presentingProblem"] = details.presentingProblem;
        }

        if (details.historyOfProblem) {
            updateData["historyOfProblem"] = details.historyOfProblem;
        }

        if (details.symptomChicklist) {
            if (details.symptomChicklist.depressiveSymptoms.symtoms) {
                updateData["symptomChicklist.depressiveSymptoms.symtoms"] =
                    details.symptomChicklist.depressiveSymptoms.symtoms;
            }
            if (details.symptomChicklist.depressiveSymptoms.comment) {
                updateData["symptomChicklist.depressiveSymptoms.comment"] =
                    details.symptomChicklist.depressiveSymptoms.comment;
            }
            if (details.symptomChicklist.manicSymptoms.symtoms) {
                updateData["symptomChicklist.manicSymptoms.symtoms"] =
                    details.symptomChicklist.manicSymptoms.symtoms;
            }
            if (details.symptomChicklist.manicSymptoms.comment) {
                updateData["symptomChicklist.manicSymptoms.comment"] =
                    details.symptomChicklist.manicSymptoms.comment;
            }

            if (details.symptomChicklist.conductLegalProblem.symtoms) {
                updateData["symptomChicklist.conductLegalProblem.symtoms"] =
                    details.symptomChicklist.conductLegalProblem.symtoms;
            }
            if (details.symptomChicklist.conductLegalProblem.comment) {
                updateData["symptomChicklist.conductLegalProblem.comment"] =
                    details.symptomChicklist.conductLegalProblem.comment;
            }

            if (details.symptomChicklist.psychosis.symtoms) {
                updateData["symptomChicklist.psychosis.symtoms"] =
                    details.symptomChicklist.psychosis.symtoms;
            }
            if (details.symptomChicklist.psychosis.comment) {
                updateData["symptomChicklist.psychosis.comment"] =
                    details.symptomChicklist.psychosis.comment;
            }

            if (details.symptomChicklist.anxietySymptoms.symtoms) {
                updateData["symptomChicklist.anxietySymptoms.symtoms"] =
                    details.symptomChicklist.anxietySymptoms.symtoms;
            }
            if (details.symptomChicklist.anxietySymptoms.comment) {
                updateData["symptomChicklist.anxietySymptoms.comment"] =
                    details.symptomChicklist.anxietySymptoms.comment;
            }

            if (details.symptomChicklist.attentionSymptoms.symtoms) {
                updateData["symptomChicklist.attentionSymptoms.symtoms"] =
                    details.symptomChicklist.attentionSymptoms.symtoms;
            }
            if (details.symptomChicklist.attentionSymptoms.comment) {
                updateData["symptomChicklist.attentionSymptoms.comment"] =
                    details.symptomChicklist.attentionSymptoms.comment;
            }
        }

        if (details.biologicalFunction) {
            if (
                details.biologicalFunction.sleep !== undefined ||
                details.biologicalFunction.sleep !== null
            ) {
                if (
                    details.biologicalFunction.sleep !== undefined &&
                    details.biologicalFunction.sleep !== null
                ) {
                    if (Object.keys(details.biologicalFunction.sleep).length !== 0) {
                        if (details.biologicalFunction.sleep.sleepStatus) {
                            updateData["biologicalFunction.sleep.sleepStatus"] =
                                details.biologicalFunction.sleep.sleepStatus;
                        }
                        if (details.biologicalFunction.sleep.comment) {
                            updateData["biologicalFunction.sleep.comment"] =
                                details.biologicalFunction.sleep.comment;
                        }
                    }
                }
            }

            if (
                details.biologicalFunction.nutritionalStatus !== undefined &&
                details.biologicalFunction.nutritionalStatus !== null
            ) {
                if (
                    details.biologicalFunction.nutritionalStatus !== undefined &&
                    details.biologicalFunction.nutritionalStatus !== null
                ) {
                    console.log(details.biologicalFunction.nutritionalStatus);
                    if (
                        Object.keys(details.biologicalFunction.nutritionalStatus).length !==
                        0
                    ) {
                        if (
                            details.biologicalFunction.nutritionalStatus.nutritionalStatus
                        ) {
                            updateData[
                                "biologicalFunction.nutritionalStatus.nutritionalStatus"
                                ] =
                                details.biologicalFunction.nutritionalStatus.nutritionalStatus;
                        }
                        if (details.biologicalFunction.nutritionalStatus.comment) {
                            updateData["biologicalFunction.nutritionalStatus.comment"] =
                                details.biologicalFunction.nutritionalStatus.comment;
                        }
                    }
                }
            }

            if (details.biologicalFunction.otherBiologicalFunction) {
                updateData["biologicalFunction.otherBiologicalFunction"] =
                    details.biologicalFunction.otherBiologicalFunction;
            }
            if (
                details.biologicalFunction.sexualActivity !== undefined &&
                details.biologicalFunction.sexualActivity !== null
            ) {
                if (
                    Object.keys(details.biologicalFunction.sexualActivity).length !== 0
                ) {
                    if (
                        details.biologicalFunction.sexualActivity.sexuallyActive
                            .sexuallyActiveness !== undefined
                    ) {
                        updateData[
                            "biologicalFunction.sexualActivity.sexuallyActive.sexuallyActiveness"
                            ] =
                            details.biologicalFunction.sexualActivity.sexuallyActive.sexuallyActiveness;
                    }

                    if (
                        details.biologicalFunction.sexualActivity.sexuallyActive.activeWith
                    ) {
                        updateData[
                            "biologicalFunction.sexualActivity.sexuallyActive.activeWith"
                            ] =
                            details.biologicalFunction.sexualActivity.sexuallyActive.activeWith;
                    }
                    if (
                        details.biologicalFunction.sexualActivity
                            .protectionAgainstHepatitisHiv.protection
                    ) {
                        updateData[
                            "biologicalFunction.sexualActivity.protectionAgainstHepatitisHiv.protection"
                            ] =
                            details.biologicalFunction.sexualActivity.protectionAgainstHepatitisHiv.protection;
                    }

                    if (
                        details.biologicalFunction.sexualActivity
                            .protectionAgainstHepatitisHiv.how
                    ) {
                        updateData[
                            "biologicalFunction.sexualActivity.protectionAgainstHepatitisHiv.how"
                            ] =
                            details.biologicalFunction.sexualActivity.protectionAgainstHepatitisHiv.how;
                    }

                    if (
                        details.biologicalFunction.sexualActivity.protectionAgainstPregnancy
                            .protection
                    ) {
                        updateData[
                            "biologicalFunction.sexualActivity.protectionAgainstPregnancy.protection"
                            ] =
                            details.biologicalFunction.sexualActivity.protectionAgainstPregnancy.protection;
                    }

                    if (
                        details.biologicalFunction.sexualActivity.protectionAgainstPregnancy
                            .how
                    ) {
                        updateData[
                            "biologicalFunction.sexualActivity.protectionAgainstPregnancy.how"
                            ] =
                            details.biologicalFunction.sexualActivity.protectionAgainstPregnancy.how;
                    }

                    if (
                        details.biologicalFunction.sexualActivity.protectionAgainstPregnancy
                            .how
                    ) {
                        updateData[
                            "biologicalFunction.sexualActivity.protectionAgainstPregnancy.how"
                            ] =
                            details.biologicalFunction.sexualActivity.protectionAgainstPregnancy.how;
                    }

                    if (
                        details.biologicalFunction.sexualActivity.atRiskBehavior.describe
                    ) {
                        updateData[
                            "biologicalFunction.sexualActivity.atRiskBehavior.describe"
                            ] =
                            details.biologicalFunction.sexualActivity.atRiskBehavior.describe;
                    }

                    if (details.biologicalFunction.sexualActivity.atRiskBehavior.risk) {
                        updateData[
                            "biologicalFunction.sexualActivity.atRiskBehavior.risk"
                            ] = details.biologicalFunction.sexualActivity.atRiskBehavior.risk;
                    }

                    if (details.biologicalFunction.sexualActivity.otherSymtoms) {
                        updateData["biologicalFunction.sexualActivity.otherSymtoms"] =
                            details.biologicalFunction.sexualActivity.otherSymtoms;
                    }

                    if (details.biologicalFunction.sexualActivity.comments) {
                        updateData["biologicalFunction.sexualActivity.comments"] =
                            details.biologicalFunction.sexualActivity.comments;
                    }
                }
            }
        }

        if (details.alcoholAndDrugUseHistory) {
            if (details.alcoholAndDrugUseHistory.historyStatus) {
                updateData["alcoholAndDrugUseHistory.historyStatus"] =
                    details.alcoholAndDrugUseHistory.historyStatus;
            }
        }

        if (details.nameOfSubstance && details.nameOfSubstance.length !== 0) {
            updateData["nameOfSubstance"] = details.nameOfSubstance;
        }

        if (details.tobaccoUseTypeDetails) {
            if (details.tobaccoUseTypeDetails.tobaccoUse !== undefined) {
                updateData["tobaccoUseTypeDetails.tobaccoUse"] =
                    details.tobaccoUseTypeDetails.tobaccoUse;
            }
            if (details.tobaccoUseTypeDetails.tobaccoType) {
                updateData["tobaccoUseTypeDetails.tobaccoType"] =
                    details.tobaccoUseTypeDetails.tobaccoType;
            }
            if (details.tobaccoUseTypeDetails.howOften) {
                updateData["tobaccoUseTypeDetails.howOften"] =
                    details.tobaccoUseTypeDetails.howOften;
            }
            if (details.tobaccoUseTypeDetails.howMuch) {
                updateData["tobaccoUseTypeDetails.howMuch"] =
                    details.tobaccoUseTypeDetails.howMuch;
            }
            if (details.tobaccoUseTypeDetails.howLong) {
                updateData["tobaccoUseTypeDetails.howLong"] =
                    details.tobaccoUseTypeDetails.howLong;
            }
            if (details.tobaccoUseTypeDetails.interestEndUse !== undefined) {
                updateData["tobaccoUseTypeDetails.interestEndUse"] =
                    details.tobaccoUseTypeDetails.interestEndUse;
            }
            if (details.tobaccoUseTypeDetails.interestReducingUse !== undefined) {
                updateData["tobaccoUseTypeDetails.interestReducingUse"] =
                    details.tobaccoUseTypeDetails.interestReducingUse;
            }
            if (details.tobaccoUseTypeDetails.endingUse) {
                updateData["tobaccoUseTypeDetails.endingUse"] =
                    details.tobaccoUseTypeDetails.endingUse;
            }
            if (details.tobaccoUseTypeDetails.longestAbstinence) {
                updateData["tobaccoUseTypeDetails.longestAbstinence"] =
                    details.tobaccoUseTypeDetails.longestAbstinence;
            }
            if (details.tobaccoUseTypeDetails.whenWas) {
                updateData["tobaccoUseTypeDetails.whenWas"] =
                    details.tobaccoUseTypeDetails.whenWas;
            }
            if (details.tobaccoUseTypeDetails.relapse) {
                updateData["tobaccoUseTypeDetails.relapse"] =
                    details.tobaccoUseTypeDetails.relapse;
            }
            if (details.tobaccoUseTypeDetails.comment) {
                updateData["tobaccoUseTypeDetails.comment"] =
                    details.tobaccoUseTypeDetails.comment;
            }

            // Conditionally update nested fields in experiencedSymptoms
            if (details.tobaccoUseTypeDetails.experiencedSymptoms) {
                const symptoms = details.tobaccoUseTypeDetails.experiencedSymptoms;
                Object.keys(symptoms).forEach((key) => {
                    updateData[
                        `tobaccoUseTypeDetails.experiencedSymptoms.${
                            key as keyof ExperienceSymptoms
                        }`
                        ] = symptoms[key as keyof ExperienceSymptoms];
                });
            }
        }

        if (details.releventMedicalInformation) {
            if (details.releventMedicalInformation.comment) {
                updateData["releventMedicalInformation.comment"] =
                    details.releventMedicalInformation.comment;
            }
            const medicalInfo = details.releventMedicalInformation;

            // Update fields in physicianDiagnosedConditions
            if (medicalInfo.physicianDiagnosedConditions) {
                Object.keys(medicalInfo.physicianDiagnosedConditions).forEach((key) => {
                    updateData[
                        `releventMedicalInformation.physicianDiagnosedConditions.${key}`
                        ] =
                        medicalInfo.physicianDiagnosedConditions[
                            key as keyof typeof medicalInfo.physicianDiagnosedConditions
                            ];
                });
            }

            // Update other fields in releventMedicalInformation
            if (medicalInfo.cancerType) {
                updateData["releventMedicalInformation.cancerType"] =
                    medicalInfo.cancerType;
            }else if (medicalInfo.cancerType===""){
                 updateData["releventMedicalInformation.cancerType"]="";
            }
            if (medicalInfo.otherType) {
                updateData["releventMedicalInformation.otherType"] =
                  medicalInfo.otherType;
              }else if (medicalInfo.otherType==""){
                updateData["releventMedicalInformation.otherType"]="";
              }
                if (medicalInfo.lastVisitPrimaryCarePhysician) {
                  updateData[
                    "releventMedicalInformation.lastVisitPrimaryCarePhysician"
                  ] = medicalInfo.lastVisitPrimaryCarePhysician;
                }
            if (medicalInfo.comment) {
                updateData["releventMedicalInformation.comment"] = medicalInfo.comment;
            }
            if (medicalInfo.anyAllergies) {
                updateData["releventMedicalInformation.anyAllergies"] =
                    medicalInfo.anyAllergies;
            }
            if (medicalInfo.hypersensitivities) {
                updateData["releventMedicalInformation.hypersensitivities"] =
                    medicalInfo.hypersensitivities;
            }

            // Update patientPainLevel if it exists
            if (medicalInfo.patientPainLevel) {
                const painLevel = medicalInfo.patientPainLevel;

                if (painLevel.currentLevelOfPhysicalPain !== undefined) {
                    updateData[
                        "releventMedicalInformation.patientPainLevel.currentLevelOfPhysicalPain"
                        ] = painLevel.currentLevelOfPhysicalPain;
                }
                if (painLevel.isCurrentReceivingTreatementPain !== undefined) {
                    updateData[
                        "releventMedicalInformation.patientPainLevel.isCurrentReceivingTreatementPain"
                        ] = painLevel.isCurrentReceivingTreatementPain;
                }
                if (painLevel.isReceivingTreatement) {
                    updateData[
                        "releventMedicalInformation.patientPainLevel.isReceivingTreatement"
                        ] = painLevel.isReceivingTreatement;
                }
                if (painLevel.treatementDetails) {
                    updateData[
                        "releventMedicalInformation.patientPainLevel.treatementDetails"
                        ] = painLevel.treatementDetails;
                }

                // new change
                if (painLevel.disablityStatus) {
                    const disabilityStatus = painLevel.disablityStatus;
                    Object.keys(disabilityStatus).forEach((key) => {
                        updateData[
                            `releventMedicalInformation.patientPainLevel.disablityStatus.${key}`
                            ] = disabilityStatus[key as keyof typeof painLevel.disablityStatus];
                    });
                }

                if (painLevel.adjustDisability) {
                    updateData[
                        "releventMedicalInformation.patientPainLevel.adjustDisability"
                        ] = painLevel.adjustDisability;
                }
                if (painLevel.requireEquipmentOrServices) {
                    updateData[
                        "releventMedicalInformation.patientPainLevel.requireEquipmentOrServices"
                        ] = painLevel.requireEquipmentOrServices;
                }
            }
        }

        if (details.outPatientTreatementHistory) {
            if (
                details.outPatientTreatementHistory.outPatienttreatementHistoryDetails &&
                details.outPatientTreatementHistory.outPatienttreatementHistoryDetails.length !== 0
            ) {
                updateData[
                    "outPatientTreatementHistory.outPatienttreatementHistoryDetails"
                    ] =
                    details.outPatientTreatementHistory.outPatienttreatementHistoryDetails;
            }

            if (
                details.outPatientTreatementHistory.applicableState !== undefined &&
                details.outPatientTreatementHistory.applicableState !== null
            ) {
                updateData["outPatientTreatementHistory.applicableState"] =
                    details.outPatientTreatementHistory.applicableState;
            }
        }

        // if (details.outPatientTreatementHistory) {
        //   if (
        //     details.outPatientTreatementHistory.outPatienttreatementHistoryDetails
        //       .length !== 0
        //   ) {
        //     updateData[
        //       "outPatientTreatementHistory.outPatienttreatementHistoryDetails"
        //     ] =
        //       details.outPatientTreatementHistory.outPatienttreatementHistoryDetails;
        //   }

        //   if (details.outPatientTreatementHistory.applicableState) {
        //     updateData[
        //       "outPatientTreatementHistory.outPatienttreatementHistoryDetails"
        //     ] = details.outPatientTreatementHistory.applicableState;
        //   }
        // }

        // end of outpatient history

        if (details.inpatientTreatementHistory) {
            if (details.inpatientTreatementHistory.applicableState) {
                updateData["inpatientTreatementHistory.applicableState"] =
                    details.inpatientTreatementHistory.applicableState;
            }

            if (details.inpatientTreatementHistory.inPatientTreatementHistory.length !== 0) {
                updateData["inpatientTreatementHistory.inPatientTreatementHistory"] =
                    details.inpatientTreatementHistory.inPatientTreatementHistory;
            }

            if (details.inpatientTreatementHistory.pastTreatement) {
                updateData["inpatientTreatementHistory.pastTreatement"] =
                    details.inpatientTreatementHistory.pastTreatement;
            }
            if (details.inpatientTreatementHistory.additionalMedicalInformation) {
                updateData["inpatientTreatementHistory.additionalMedicalInformation"] =
                    details.inpatientTreatementHistory.additionalMedicalInformation;
            }
            if (details.inpatientTreatementHistory.applicableState && details.inpatientTreatementHistory.pastTreatement === "") {
                updateData["inpatientTreatementHistory.pastTreatement"] =
                    details.inpatientTreatementHistory.pastTreatement;
            }
        }

        if (details.bioPsychosocialDevelopemntHistory) {
            if (details.bioPsychosocialDevelopemntHistory.developmentHistory) {
                updateData["bioPsychosocialDevelopemntHistory.developmentHistory"] =
                    details.bioPsychosocialDevelopemntHistory.developmentHistory;
            }
            if (details.bioPsychosocialDevelopemntHistory.childhoodHistory) {
                updateData["bioPsychosocialDevelopemntHistory.childhoodHistory"] =
                    details.bioPsychosocialDevelopemntHistory.childhoodHistory;
            }
            if (details.bioPsychosocialDevelopemntHistory.fosterCare) {
                updateData["bioPsychosocialDevelopemntHistory.fosterCare"] =
                    details.bioPsychosocialDevelopemntHistory.fosterCare;
            }
            if (details.bioPsychosocialDevelopemntHistory.siblings) {
                updateData["bioPsychosocialDevelopemntHistory.siblings"] =
                    details.bioPsychosocialDevelopemntHistory.siblings;
            }
            if (
                details.bioPsychosocialDevelopemntHistory.familyHistoryOfMHOrSAIssues
            ) {
                updateData[
                    "bioPsychosocialDevelopemntHistory.familyHistoryOfMHOrSAIssues"
                    ] =
                    details.bioPsychosocialDevelopemntHistory.familyHistoryOfMHOrSAIssues;
            }
            if (details.bioPsychosocialDevelopemntHistory.currentSpouse) {
                updateData["bioPsychosocialDevelopemntHistory.currentSpouse"] =
                    details.bioPsychosocialDevelopemntHistory.currentSpouse;
            }
            if (details.bioPsychosocialDevelopemntHistory.childrenStepChildren) {
                updateData["bioPsychosocialDevelopemntHistory.childrenStepChildren"] =
                    details.bioPsychosocialDevelopemntHistory.childrenStepChildren;
            }
            if (details.bioPsychosocialDevelopemntHistory.relationsIssues) {
                updateData["bioPsychosocialDevelopemntHistory.relationsIssues"] =
                    details.bioPsychosocialDevelopemntHistory.relationsIssues;
            }

            if (details.bioPsychosocialDevelopemntHistory.otherSupports) {
                if (details.bioPsychosocialDevelopemntHistory.otherSupports.family) {
                    updateData["bioPsychosocialDevelopemntHistory.otherSupports.family"] =
                        details.bioPsychosocialDevelopemntHistory.otherSupports.family;
                }

                if (details.bioPsychosocialDevelopemntHistory.otherSupports.church) {
                    updateData["bioPsychosocialDevelopemntHistory.otherSupports.church"] =
                        details.bioPsychosocialDevelopemntHistory.otherSupports.church;
                }

                if (details.bioPsychosocialDevelopemntHistory.otherSupports.employer) {
                    updateData[
                        "bioPsychosocialDevelopemntHistory.otherSupports.employer"
                        ] = details.bioPsychosocialDevelopemntHistory.otherSupports.employer;
                }
                if (details.bioPsychosocialDevelopemntHistory.otherSupports.friends) {
                    updateData[
                        "bioPsychosocialDevelopemntHistory.otherSupports.friends"
                        ] = details.bioPsychosocialDevelopemntHistory.otherSupports.friends;
                }
                if (details.bioPsychosocialDevelopemntHistory.otherSupports.other) {
                    updateData["bioPsychosocialDevelopemntHistory.otherSupports.other"] =
                        details.bioPsychosocialDevelopemntHistory.otherSupports.other;
                }
                if (
                    details.bioPsychosocialDevelopemntHistory.otherSupports.otherSupport
                ) {
                    updateData[
                        "bioPsychosocialDevelopemntHistory.otherSupports.otherSupport"
                        ] =
                        details.bioPsychosocialDevelopemntHistory.otherSupports.otherSupport;
                }
            }
        }

        if (details.bioPsychosocialEducation) {
            if (details.bioPsychosocialEducation.isApplicable) {
                updateData["bioPsychosocialEducation.isApplicable"] =
                    details.bioPsychosocialEducation.isApplicable;
            }
            if (details.bioPsychosocialEducation.schoolGradeName) {
                updateData["bioPsychosocialEducation.schoolGradeName"] =
                    details.bioPsychosocialEducation.schoolGradeName;
            }

            if (details.bioPsychosocialEducation.lastIep) {
                updateData["bioPsychosocialEducation.lastIep"] =
                    details.bioPsychosocialEducation.lastIep;
            }
            if (details.bioPsychosocialEducation.teacher) {
                updateData["bioPsychosocialEducation.teacher"] =
                    details.bioPsychosocialEducation.teacher;
            }
            if (details.bioPsychosocialEducation.learningAbility) {
                updateData["bioPsychosocialEducation.learningAbility"] =
                    details.bioPsychosocialEducation.learningAbility;
            }
            if (details.bioPsychosocialEducation.disabilityArea) {
                updateData["bioPsychosocialEducation.disabilityArea"] =
                    details.bioPsychosocialEducation.disabilityArea;
            }
            if (details.bioPsychosocialEducation.educationalProblems) {
                updateData["bioPsychosocialEducation.educationalProblems"] =
                    details.bioPsychosocialEducation.educationalProblems;
            }
            if (details.bioPsychosocialEducation.behaviorProblem) {
                updateData["bioPsychosocialEducation.behaviorProblem"] =
                    details.bioPsychosocialEducation.behaviorProblem;
            }
            if (details.bioPsychosocialEducation.repetedGrades) {
                updateData["bioPsychosocialEducation.repetedGrades"] =
                    details.bioPsychosocialEducation.repetedGrades;
            }
            if (details.bioPsychosocialEducation.socialInteraction) {
                updateData["bioPsychosocialEducation.socialInteraction"] =
                    details.bioPsychosocialEducation.socialInteraction;
            }
            if (details.bioPsychosocialEducation.suspension) {
                updateData["bioPsychosocialEducation.suspension"] =
                    details.bioPsychosocialEducation.suspension;
            }

            if (details.bioPsychosocialEducation.expulsion) {
                updateData["bioPsychosocialEducation.expulsion"] =
                    details.bioPsychosocialEducation.expulsion;
            }
            if (details.bioPsychosocialEducation.isPsychologicalTestingCompleted) {
                updateData["bioPsychosocialEducation.isPsychologicalTestingCompleted"] =
                    details.bioPsychosocialEducation.isPsychologicalTestingCompleted;
            }
            if (details.bioPsychosocialEducation.date) {
                updateData["bioPsychosocialEducation.date"] =
                    details.bioPsychosocialEducation.date;
            }
            if (details.bioPsychosocialEducation.expulsionTestResults) {
                updateData["bioPsychosocialEducation.expulsionTestResults"] =
                    details.bioPsychosocialEducation.expulsionTestResults;
            }
        }

        if (details.traumaHistory) {
            if (details.traumaHistory.isBadAccident !== undefined) {
                updateData["traumaHistory.isBadAccident"] =
                    details.traumaHistory.isBadAccident;
            }

            if (details.traumaHistory.badAccidentDescribe !== undefined) {
                updateData["traumaHistory.badAccidentDescribe"] =
                    details.traumaHistory.badAccidentDescribe;
            }

            if (details.traumaHistory.isNaturalDisaster !== undefined) {
                updateData["traumaHistory.isNaturalDisaster"] =
                    details.traumaHistory.isNaturalDisaster;
            }

            if (details.traumaHistory.naturalDisasterdDescribe !== undefined) {
                updateData["traumaHistory.naturalDisasterdDescribe"] =
                    details.traumaHistory.naturalDisasterdDescribe;
            }

            if (details.traumaHistory.isMilitaryCombat !== undefined) {
                updateData["traumaHistory.isMilitaryCombat"] =
                    details.traumaHistory.isMilitaryCombat;
            }

            if (details.traumaHistory.militoryCombatDescribe !== undefined) {
                updateData["traumaHistory.militoryCombatDescribe"] =
                    details.traumaHistory.militoryCombatDescribe;
            }
            if (details.traumaHistory.isSeriousAttack !== undefined) {
                updateData["traumaHistory.isSeriousAttack"] =
                    details.traumaHistory.isSeriousAttack;
            }

            if (details.traumaHistory.seriousAttackDescribe !== undefined) {
                updateData["traumaHistory.seriousAttackDescribe"] =
                    details.traumaHistory.seriousAttackDescribe;
            }
            if (details.traumaHistory.isSexualCoercion !== undefined) {
                updateData["traumaHistory.isSexualCoercion"] =
                    details.traumaHistory.isSexualCoercion;
            }
            if (details.traumaHistory.ageOfSexualCoercion !== undefined) {
                updateData["traumaHistory.ageOfSexualCoercion"] =
                    details.traumaHistory.ageOfSexualCoercion;
            }
            if (details.traumaHistory.describeSexualCoercion !== undefined) {
                updateData["traumaHistory.describeSexualCoercion"] =
                    details.traumaHistory.describeSexualCoercion;
            }
            if (details.traumaHistory.describeSexualCoercion !== undefined) {
                updateData["traumaHistory.describeSexualCoercion"] =
                    details.traumaHistory.describeSexualCoercion;
            }

            if (details.traumaHistory.isTraumaticExperience !== undefined) {
                updateData["traumaHistory.isTraumaticExperience"] =
                    details.traumaHistory.isTraumaticExperience;
            }
            if (details.traumaHistory.describeTraumaticExperience !== undefined) {
                updateData["traumaHistory.describeTraumaticExperience"] =
                    details.traumaHistory.describeTraumaticExperience;
            }
            if (details.traumaHistory.intrusiveThoughts !== undefined) {
                updateData["traumaHistory.intrusiveThoughts"] =
                    details.traumaHistory.intrusiveThoughts;
            }
            if (details.traumaHistory.avoidanceBehavior !== undefined) {
                updateData["traumaHistory.avoidanceBehavior"] =
                    details.traumaHistory.avoidanceBehavior;
            }
            if (details.traumaHistory.hypervigilance !== undefined) {
                updateData["traumaHistory.hypervigilance"] =
                    details.traumaHistory.hypervigilance;
            }
            if (details.traumaHistory.emotionalDetachment !== undefined) {
                updateData["traumaHistory.emotionalDetachment"] =
                    details.traumaHistory.emotionalDetachment;
            }
            if (details.traumaHistory.selfBlame !== undefined) {
                updateData["traumaHistory.selfBlame"] = details.traumaHistory.selfBlame;
            }
        }

        if (details.riskToSelfAndOthers) {
            if (details.riskToSelfAndOthers.currentRiskToSelf) {
                if (
                    details.riskToSelfAndOthers.currentRiskToSelf.isSelfInjured !==
                    undefined
                ) {
                    updateData["riskToSelfAndOthers.currentRiskToSelf.isSelfInjured"] =
                        details.riskToSelfAndOthers.currentRiskToSelf.isSelfInjured;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToSelf.describeSelfInjured !==
                    undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToSelf.describeSelfInjured"
                        ] = details.riskToSelfAndOthers.currentRiskToSelf.describeSelfInjured;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToSelf.isSuicidalThreats !==
                    undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToSelf.isSuicidalThreats"
                        ] = details.riskToSelfAndOthers.currentRiskToSelf.isSuicidalThreats;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToSelf
                        .describeSuicidalThreats !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToSelf.describeSuicidalThreats"
                        ] =
                        details.riskToSelfAndOthers.currentRiskToSelf.describeSuicidalThreats;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToSelf.repetitiveIdeation !==
                    undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToSelf.repetitiveIdeation"
                        ] = details.riskToSelfAndOthers.currentRiskToSelf.repetitiveIdeation;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToSelf.isSuicidePlan !==
                    undefined
                ) {
                    updateData["riskToSelfAndOthers.currentRiskToSelf.isSuicidePlan"] =
                        details.riskToSelfAndOthers.currentRiskToSelf.isSuicidePlan;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToSelf.isLethalIntent !==
                    undefined
                ) {
                    updateData["riskToSelfAndOthers.currentRiskToSelf.isLethalIntent"] =
                        details.riskToSelfAndOthers.currentRiskToSelf.isLethalIntent;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToSelf
                        .isCommandHallucinations !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToSelf.isCommandHallucinations"
                        ] =
                        details.riskToSelfAndOthers.currentRiskToSelf.isCommandHallucinations;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToSelf
                        .describeCommandHallucinations !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToSelf.describeCommandHallucinations"
                        ] =
                        details.riskToSelfAndOthers.currentRiskToSelf.describeCommandHallucinations;
                }
            }

            if (details.riskToSelfAndOthers.currentRiskToOthers) {
                if (
                    details.riskToSelfAndOthers.currentRiskToOthers.isHomicidalThreats !==
                    undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToOthers.isHomicidalThreats"
                        ] =
                        details.riskToSelfAndOthers.currentRiskToOthers.isHomicidalThreats;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToOthers
                        .isPersistentIdeation !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToOthers.isPersistentIdeation"
                        ] =
                        details.riskToSelfAndOthers.currentRiskToOthers.isPersistentIdeation;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToOthers.isSpecificPlan !==
                    undefined
                ) {
                    updateData["riskToSelfAndOthers.currentRiskToOthers.isSpecificPlan"] =
                        details.riskToSelfAndOthers.currentRiskToOthers.isSpecificPlan;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToOthers.isLethalIntent !==
                    undefined
                ) {
                    updateData["riskToSelfAndOthers.currentRiskToOthers.isLethalIntent"] =
                        details.riskToSelfAndOthers.currentRiskToOthers.isLethalIntent;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToOthers
                        .isHallucinationCommands !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToOthers.isHallucinationCommands"
                        ] =
                        details.riskToSelfAndOthers.currentRiskToOthers.isHallucinationCommands;
                }
                if (
                    details.riskToSelfAndOthers.currentRiskToOthers
                        .describeHallucination !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.currentRiskToOthers.describeHallucination"
                        ] =
                        details.riskToSelfAndOthers.currentRiskToOthers.describeHallucination;
                }
            }

            if (details.riskToSelfAndOthers.historyOfSuicidalBehavior) {
                if (
                    details.riskToSelfAndOthers.historyOfSuicidalBehavior
                        .isHistoryOfSuicidalBehavior !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.historyOfSuicidalBehavior.isHistoryOfSuicidalBehavior"
                        ] =
                        details.riskToSelfAndOthers.historyOfSuicidalBehavior.isHistoryOfSuicidalBehavior;
                }

                if (
                    details.riskToSelfAndOthers.historyOfSuicidalBehavior
                        .describeHistoryOfSuicidalBehavior !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.historyOfSuicidalBehavior.describeHistoryOfSuicidalBehavior"
                        ] =
                        details.riskToSelfAndOthers.historyOfSuicidalBehavior.describeHistoryOfSuicidalBehavior;
                }

                if (
                    details.riskToSelfAndOthers.historyOfSuicidalBehavior
                        .isHomicidalBehavior !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.historyOfSuicidalBehavior.isHomicidalBehavior"
                        ] =
                        details.riskToSelfAndOthers.historyOfSuicidalBehavior.isHomicidalBehavior;
                }

                if (
                    details.riskToSelfAndOthers.historyOfSuicidalBehavior
                        .describeHomicidalBehavior !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.historyOfSuicidalBehavior.describeHomicidalBehavior"
                        ] =
                        details.riskToSelfAndOthers.historyOfSuicidalBehavior.describeHomicidalBehavior;
                }

                if (
                    details.riskToSelfAndOthers.historyOfSuicidalBehavior
                        .isSeriousHarmSelfOthers !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.historyOfSuicidalBehavior.isSeriousHarmSelfOthers"
                        ] =
                        details.riskToSelfAndOthers.historyOfSuicidalBehavior.isSeriousHarmSelfOthers;
                }

                if (
                    details.riskToSelfAndOthers.historyOfSuicidalBehavior
                        .describeSeriousPhysicalHarm !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.historyOfSuicidalBehavior.describeSeriousPhysicalHarm"
                        ] =
                        details.riskToSelfAndOthers.historyOfSuicidalBehavior.describeSeriousPhysicalHarm;
                }

                if (
                    details.riskToSelfAndOthers.historyOfSuicidalBehavior
                        .isAggressionViolenceOthers !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.historyOfSuicidalBehavior.isAggressionViolenceOthers"
                        ] =
                        details.riskToSelfAndOthers.historyOfSuicidalBehavior.isAggressionViolenceOthers;
                }

                if (
                    details.riskToSelfAndOthers.historyOfSuicidalBehavior
                        .describeAggression !== undefined
                ) {
                    updateData[
                        "riskToSelfAndOthers.historyOfSuicidalBehavior.describeAggression"
                        ] =
                        details.riskToSelfAndOthers.historyOfSuicidalBehavior.describeAggression;
                }
            }

            if (details.riskToSelfAndOthers.evaluationOfRisk) {
                if (details.riskToSelfAndOthers.evaluationOfRisk.selfHarming) {
                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.selfHarming.lowToNoRisk"
                        ] =
                        details.riskToSelfAndOthers.evaluationOfRisk.selfHarming.lowToNoRisk;

                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.selfHarming.moderateRisk"
                        ] =
                        details.riskToSelfAndOthers.evaluationOfRisk.selfHarming.moderateRisk;

                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.selfHarming.highRisk"
                        ] = details.riskToSelfAndOthers.evaluationOfRisk.selfHarming.highRisk;

                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.selfHarming.imminentRisk"
                        ] =
                        details.riskToSelfAndOthers.evaluationOfRisk.selfHarming.imminentRisk;
                }
                if (details.riskToSelfAndOthers.evaluationOfRisk.assaultive) {
                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.assaultive.lowToNoRisk"
                        ] =
                        details.riskToSelfAndOthers.evaluationOfRisk.assaultive.lowToNoRisk;

                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.assaultive.moderateRisk"
                        ] =
                        details.riskToSelfAndOthers.evaluationOfRisk.assaultive.moderateRisk;

                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.assaultive.highRisk"
                        ] = details.riskToSelfAndOthers.evaluationOfRisk.assaultive.highRisk;

                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.assaultive.imminentRisk"
                        ] =
                        details.riskToSelfAndOthers.evaluationOfRisk.assaultive.imminentRisk;
                }

                if (details.riskToSelfAndOthers.evaluationOfRisk.actionEvaluation) {
                    updateData["riskToSelfAndOthers.evaluationOfRisk.actionEvaluation"] =
                        details.riskToSelfAndOthers.evaluationOfRisk.actionEvaluation;
                }
                if (details.riskToSelfAndOthers.evaluationOfRisk.beliefSystem) {
                    updateData["riskToSelfAndOthers.evaluationOfRisk.beliefSystem"] =
                        details.riskToSelfAndOthers.evaluationOfRisk.beliefSystem;
                }
                if (details.riskToSelfAndOthers.evaluationOfRisk.roleOfBeliefinlife) {
                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.roleOfBeliefinlife"
                        ] = details.riskToSelfAndOthers.evaluationOfRisk.roleOfBeliefinlife;
                }
                if (details.riskToSelfAndOthers.evaluationOfRisk.roleOfBeliefRecovery) {
                    updateData[
                        "riskToSelfAndOthers.evaluationOfRisk.roleOfBeliefRecovery"
                        ] = details.riskToSelfAndOthers.evaluationOfRisk.roleOfBeliefRecovery;
                }
            }
        }

        if (details.mentalStatus) {
            // physical structure
            if (details.mentalStatus.physicalStature) {
                Object.keys(details.mentalStatus.physicalStature).forEach(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.physicalStature.${key}`] =
                            details.mentalStatus.physicalStature[
                                key as keyof typeof details.mentalStatus.physicalStature
                                ];
                    }
                );
            }
            // weight
            if (details.mentalStatus.weight) {
                Object.keys(details.mentalStatus.weight).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.weight.${key}`] =
                            details.mentalStatus.weight[
                                key as keyof typeof details.mentalStatus.weight
                                ];
                    }
                );
            }

            // grooming
            if (details.mentalStatus.grooming) {
                Object.keys(details.mentalStatus.grooming).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.grooming.${key}`] =
                            details.mentalStatus.grooming[
                                key as keyof typeof details.mentalStatus.grooming
                                ];
                    }
                );
            }

            // clothing
            if (details.mentalStatus.clothing) {
                Object.keys(details.mentalStatus.clothing).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.clothing.${key}`] =
                            details.mentalStatus.clothing[
                                key as keyof typeof details.mentalStatus.clothing
                                ];
                    }
                );
            }

            // posture
            if (details.mentalStatus.posture) {
                Object.keys(details.mentalStatus.posture).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.posture.${key}`] =
                            details.mentalStatus.posture[
                                key as keyof typeof details.mentalStatus.posture
                                ];
                    }
                );
            }

            // attitude
            if (details.mentalStatus.attitude) {
                Object.keys(details.mentalStatus.attitude).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.attitude.${key}`] =
                            details.mentalStatus.attitude[
                                key as keyof typeof details.mentalStatus.attitude
                                ];
                    }
                );
            }

            // motor
            if (details.mentalStatus.motor) {
                Object.keys(details.mentalStatus.motor).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.motor.${key}`] =
                            details.mentalStatus.motor[
                                key as keyof typeof details.mentalStatus.motor
                                ];
                    }
                );
            }

            // speech

            if (details.mentalStatus.speech) {
                Object.keys(details.mentalStatus.speech).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.speech.${key}`] =
                            details.mentalStatus.speech[
                                key as keyof typeof details.mentalStatus.speech
                                ];
                    }
                );
            }

            // affect

            if (details.mentalStatus.affect) {
                Object.keys(details.mentalStatus.affect).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.affect.${key}`] =
                            details.mentalStatus.affect[
                                key as keyof typeof details.mentalStatus.affect
                                ];
                    }
                );
            }

            // mood
            if (details.mentalStatus.mood) {
                Object.keys(details.mentalStatus.mood).map((key: string, i: number) => {
                    updateData[`mentalStatus.mood.${key}`] =
                        details.mentalStatus.mood[
                            key as keyof typeof details.mentalStatus.mood
                            ];
                });
            }

            // thoughtForm
            if (details.mentalStatus.thoughtForm) {
                Object.keys(details.mentalStatus.thoughtForm).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.thoughtForm.${key}`] =
                            details.mentalStatus.thoughtForm[
                                key as keyof typeof details.mentalStatus.thoughtForm
                                ];
                    }
                );
            }

            // thoughtContent
            if (details.mentalStatus.thoughtContent) {
                Object.keys(details.mentalStatus.thoughtContent).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.thoughtContent.${key}`] =
                            details.mentalStatus.thoughtContent[
                                key as keyof typeof details.mentalStatus.thoughtContent
                                ];
                    }
                );
            }

            // preoccupations
            if (details.mentalStatus.preoccupations) {
                Object.keys(details.mentalStatus.preoccupations).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.preoccupations.${key}`] =
                            details.mentalStatus.preoccupations[
                                key as keyof typeof details.mentalStatus.preoccupations
                                ];
                    }
                );
            }

            // hallucinations
            if (details.mentalStatus.hallucinations) {
                Object.keys(details.mentalStatus.hallucinations).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.hallucinations.${key}`] =
                            details.mentalStatus.hallucinations[
                                key as keyof typeof details.mentalStatus.hallucinations
                                ];
                    }
                );
            }

            // orientation
            if (details.mentalStatus.orientation) {
                Object.keys(details.mentalStatus.orientation).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.orientation.${key}`] =
                            details.mentalStatus.orientation[
                                key as keyof typeof details.mentalStatus.orientation
                                ];
                    }
                );
            }

            // levelOfIntellectualFunctioning
            if (details.mentalStatus.levelOfIntellectualFunctioning) {
                Object.keys(details.mentalStatus.levelOfIntellectualFunctioning).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.levelOfIntellectualFunctioning.${key}`] =
                            details.mentalStatus.levelOfIntellectualFunctioning[
                                key as keyof typeof details.mentalStatus.levelOfIntellectualFunctioning
                                ];
                    }
                );
            }

            // fundofknowledge
            if (details.mentalStatus.fundofKnowledge) {
                Object.keys(details.mentalStatus.fundofKnowledge).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.fundofKnowledge.${key}`] =
                            details.mentalStatus.fundofKnowledge[
                                key as keyof typeof details.mentalStatus.fundofKnowledge
                                ];
                    }
                );
            }

            // judgement
            if (details.mentalStatus.judgment) {
                Object.keys(details.mentalStatus.judgment).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.judgment.${key}`] =
                            details.mentalStatus.judgment[
                                key as keyof typeof details.mentalStatus.judgment
                                ];
                    }
                );
            }

            // insightintoproblem
            if (details.mentalStatus.insightIntoProblems) {
                Object.keys(details.mentalStatus.insightIntoProblems).map(
                    (key: string, i: number) => {
                        updateData[`mentalStatus.insightIntoProblems.${key}`] =
                            details.mentalStatus.insightIntoProblems[
                                key as keyof typeof details.mentalStatus.insightIntoProblems
                                ];
                    }
                );
            }

            if (details.mentalStatus.clinicalImpressionSummary) {
                updateData[`mentalStatus.clinicalImpressionSummary`] =
                    details.mentalStatus.clinicalImpressionSummary;
            }
        }
        // relevent information
        if (details.releventDDSInformation) {
            Object.keys(details.releventDDSInformation).map(
                (key: string, i: number) => {
                    updateData[`releventDDSInformation.${key}`] =
                        details.releventDDSInformation[
                            key as keyof typeof details.releventDDSInformation
                            ];
                }
            );
        }

        if (details.relevantLegalInformation) {
            Object.keys(details.relevantLegalInformation).map(
                (key: string, i: number) => {
                    updateData[`relevantLegalInformation.${key}`] =
                        details.relevantLegalInformation[
                            key as keyof typeof details.relevantLegalInformation
                            ];
                }
            );
        }

        if (details.diagnoseRecommendationDetails) {
            // if (details.diagnoseRecommendationDetails.diagnosis.code) {
            //   updateData["diagnoseRecommendationDetails.diagnosis.code"] =
            //     details.diagnoseRecommendationDetails.diagnosis.code;
            // }
            if (details.diagnoseRecommendationDetails.diagnosis) {
                if (details.diagnoseRecommendationDetails.diagnosis.length !== 0) {
                    updateData["diagnoseRecommendationDetails.diagnosis"] =
                        details.diagnoseRecommendationDetails.diagnosis;
                }
            }

            if (details.diagnoseRecommendationDetails.recommendation) {
                Object.keys(details.diagnoseRecommendationDetails.recommendation).map(
                    (key: string, i: number) => {
                        updateData[`diagnoseRecommendationDetails.recommendation.${key}`] =
                            details.diagnoseRecommendationDetails.recommendation[
                                key as keyof typeof details.diagnoseRecommendationDetails.recommendation
                                ];
                    }
                );
            }

            if (details.diagnoseRecommendationDetails.printedName) {
                updateData[`diagnoseRecommendationDetails.printedName`] =
                    details.diagnoseRecommendationDetails.printedName;
            }
            if (details.diagnoseRecommendationDetails.signature) {
                updateData[`diagnoseRecommendationDetails.signature`] =
                    details.diagnoseRecommendationDetails.signature;
            }

            if (details.diagnoseRecommendationDetails.date) {
                updateData[`diagnoseRecommendationDetails.date`] =
                    details.diagnoseRecommendationDetails.date;
            }

            if (details.diagnoseRecommendationDetails.dateOfClinicianSignature) {
                updateData[`diagnoseRecommendationDetails.dateOfClinicianSignature`] =
                    details.diagnoseRecommendationDetails.dateOfClinicianSignature;
            }

            //  console.log(details.diagnoseRecommendationDetails.clientSignature)
            // if (
            //   details.diagnoseRecommendationDetails.clientSignature === undefined ||
            //   details.diagnoseRecommendationDetails.clientSignature === ""
            // ) {
            //   console.log("clientSignature");
            //   console.log(isSendClientEmail)
            //   if (isSendClientEmail) {
            //     let client = await ClientDao.getUserById(details.clientId);
            //     let therapist = await TherapistDao.getUserById(details.therapistId);
            //     console.log(client);
            //     if (client) {
            //       await EmailService.sendDigitalAssesmentSignatureEmail(
            //         client,
            //         therapist,
            //         "Digital Assesment Client Signature Request",
            //         id
            //       );
            //     }
            //   }
            // }
        }

        const result = await DigitalAssessmentForms.updateOne(
            {
                therapistId: details.therapistId,
                clientId: details.clientId,
                _id: id,
            }, // filter
            {$set: updateData}, // update operation
            {new: true} // option to return the updated document
        );

        return result;
    }

    export async function getDigitalAssessmentDetailsByClientIdAndTherapistIdAndId(
        clientId: string,
        therapistId: string,
        id: string
    ) {
        const result = await DigitalAssessmentForms.find({
            clientId: Types.ObjectId(clientId), // Ensure it's an ObjectId
            therapistId: Types.ObjectId(therapistId),
            _id: Types.ObjectId(id), // Ensure it's an ObjectId
        });

        return result;
    }

    export async function getDigitalAssessmentDetailsByClientIdAndTherapistId(
        clientId: string,
        therapistId: string
    ) {
        const result = await DigitalAssessmentForms.find({
            clientId: Types.ObjectId(clientId), // Ensure it's an ObjectId
            therapistId: Types.ObjectId(therapistId),
            // Ensure it's an ObjectId
        });

        return result;
    }

    export async function getSingleDigitalAssessmentDetailsByTherapist(
        clientId: Types.ObjectId,
        therapistId: Types.ObjectId
    ) {
        const result = await DigitalAssessmentForms.findOne({
            clientId: clientId,
            therapistId: therapistId,
        });

        return result;
    }

    export async function getSingleDigitalAssessmentDetailsByAdmin(
        assesmentId: Types.ObjectId
    ) {
        const result = await DigitalAssessmentForms.findById(assesmentId);
        return result;
    }

    export async function updateDigitalAssessmentClientSignature(
        id: Types.ObjectId,
        signature: string
    ) {
        const document = await DigitalAssessmentForms.findById(id);
        if (!document) {
            throw new Error("Document not found");
        }
        console.log(signature);
        // Update the nested field directly
        document.diagnoseRecommendationDetails.clientSignature = signature;

        // Save the document, triggering Mongoose middleware and validations
        await document.save();

        return document;
    }

    export async function getPreviousAIGeneratedAssessmentByIds(
        meetingId: Types.ObjectId,
        clientId: Types.ObjectId,
        therapistId: Types.ObjectId
    ): Promise<IDigitalAssessmentGeneration> {
        const aiGeneratedData = await DigitalAssessmentGeneration.findOne({
            meetingId: meetingId,
            clientId: clientId,
            therapistId: therapistId,
        });
        return aiGeneratedData;
    }

    export async function saveGeneratedAIResponseForAssessment(
        meetingId: Types.ObjectId,
        clientId: Types.ObjectId,
        therapistId: Types.ObjectId,
        aiResponse: string
    ): Promise<IDigitalAssessmentGeneration> {
        const data = {
            meetingId: meetingId,
            clientId: clientId,
            therapistId: therapistId,
            aiResponse: aiResponse
        };
        const request = new DigitalAssessmentGeneration(data);
        const response = await request.save();
        return response;
    }

    export async function processGeneratedData(
        processedText: string
    ): Promise<any> {
        let dataExtracted: any;

        const objectText = processedText.includes('TGenerated Assessment Groups: ')
            ? processedText.split('TGenerated Assessment Groups: ')[1]
            : processedText.split('Generated Assessment Groups: ')[1];

        const extractComment = (path: string) => {
            const normalizedPath = path.replace(/^\./, '');

            const patterns = [
                new RegExp(`['"](?:\\.)?${normalizedPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]:\\s*{\\s*['"]response['"]:\\s*['"](.+?)['"]}`),
                new RegExp(`['"](?:\\.)?${normalizedPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]:\\s*{\\s*['"]response['"]:\\s*['"](.+?)['"]}`),
            ];

            for (const pattern of patterns) {
                const match = objectText.match(pattern);
                if (match && match[1]) {
                    return match[1].replace(/\\'/g, "'").replace(/\\"/g, '"');
                }
            }

            return '';
        };

        const depressiveSymptomsComment = extractComment('.symptomChecklists.depressiveSymptoms.comment');
        const manicSymptomsComment = extractComment('.symptomChicklist.manicSymptoms.symtoms.comment');
        const conductLegalProblemComment = extractComment('.symptomChicklist.conductLegalProblem.comment');
        const psychosisComment = extractComment('symptomChecklists.psychosis.comment');
        const anxietySymptomsComment = extractComment('symptomChecklists.anxietySymptoms.comment');
        const attentionSymptomsComment = extractComment('symptomChecklists.attentionSymptoms.comment');
        const sleepComment = extractComment('.biologicalFunction.sleep.comment');
        const nutritionalStatusComment = extractComment('biologicalFunction.nutritionalStatus.comment');
        // const otherBiologicalFunctionComment = extractComment('.biologicalFunction.otherBiologicalFunction.other');
        // const sexualActivityComment = extractComment('.biologicalFunction.sexualActivity.atRiskBehavior.describe');
        const badAccidentDescribeComment = extractComment('traumaHistory.badAccidentDescribe');
        const seriousAttackDescribeComment = extractComment('traumaHistory.seriousAttackDescribe');
        const describeSexualCoercionComment = extractComment('traumaHistory.describeSexualCoercion');
        const describeTraumaticExperienceComment = extractComment('traumaHistory.describeTraumaticExperience');
        // const intrusiveThoughtsComment = extractComment('traumaHistory.intrusiveThoughts');
        // const avoidanceBehaviorComment = extractComment('traumaHistory.avoidanceBehavior');
        // const hypervigilanceComment = extractComment('traumaHistory.hypervigilance');
        // const emotionalDetachmentComment = extractComment('traumaHistory.emotionalDetachment');
        // const selfBlameComment = extractComment('traumaHistory.selfBlame');
        const presentingProblemComment = extractComment('presentingProblem.description');
        const historyOfProblemComment = extractComment('historyOfProblem');
        const releventMedicalInformationComment = extractComment('releventMedicalInformation.comment');
        // const recommendationsComment = extractComment('recommendations.level_of_care');
        const speechComment = extractComment('mentalStatus.speech');
        const moodComment = extractComment('mentalStatus.mood');
        const affectComment = extractComment('mentalStatus.affect');
        const thoughtFormComment = extractComment('mentalStatus.thoughtForm');
        const thoughtContentComment = extractComment('mentalStatus.thoughtContent');

        const combinedComment = speechComment.trim() + ' ' + moodComment.trim() + ' ' + affectComment.trim() + ' ' + thoughtFormComment.trim() + ' ' + thoughtContentComment.trim();

        dataExtracted = {
            symptomChicklist: {
                depressiveSymptoms: {symtoms: {}, comment: depressiveSymptomsComment},
                manicSymptoms: {symtoms: {}, comment: manicSymptomsComment},
                conductLegalProblem: {symtoms: {}, comment: conductLegalProblemComment},
                psychosis: {symtoms: {}, comment: psychosisComment},
                anxietySymptoms: {symtoms: {}, comment: anxietySymptomsComment},
                attentionSymptoms: {symtoms: {}, comment: attentionSymptomsComment},
            },
            biologicalFunction: {
                sleep: {sleepStatus: {}, comment: sleepComment},
                nutritionalStatus: {nutritionalStatus: {}, comment: nutritionalStatusComment},
                otherBiologicalFunction: {
                    amenorrhea: "",
                    encopresis: "",
                    increased: "",
                    decreased: "",
                    other: "",
                    enuresis: "",
                },
                sexualActivity: {
                    sexuallyActive: {sexuallyActiveness: null, activeWith: ""},
                    protectionAgainstHepatitisHiv: {protection: null, how: ""},
                    protectionAgainstPregnancy: {protection: null, how: ""},
                    atRiskBehavior: {risk: null, describe: ""},
                    otherSymtoms: "",
                    comments: "",
                },
            },
            traumaHistory: {
                isBadAccident: null,
                badAccidentDescribe: badAccidentDescribeComment,
                isNaturalDisaster: null,
                naturalDisasterdDescribe: "",
                isMilitaryCombat: null,
                militoryCombatDescribe: "",
                isSeriousAttack: null,
                seriousAttackDescribe: seriousAttackDescribeComment,
                isSexualCoercion: null,
                ageOfSexualCoercion: 0,
                describeSexualCoercion: describeSexualCoercionComment,
                isTraumaticExperience: null,
                describeTraumaticExperience: describeTraumaticExperienceComment,
                intrusiveThoughts: null,
                avoidanceBehavior: null,
                hypervigilance: null,
                emotionalDetachment: null,
                selfBlame: null,
            },
            presentingProblem: {
                description: presentingProblemComment,
                historyOfProblem: "",
            },
            historyOfProblem: historyOfProblemComment,
            releventMedicalInformation: {
                physicianDiagnosedConditions: {
                    allergies: "",
                    gynecological: "",
                    pancreatitis: "",
                    anemia: "",
                    headInjury: "",
                    respiratory: "",
                    arthritis: "",
                    heartDisease: "",
                    seizureDisorder: "",
                    asthma: "",
                    hepatitis: "",
                    std: "",
                    brainDisorder: "",
                    highBloodPressure: "",
                    stroke: "",
                    cancer: "",
                    lowBloodPressure: "",
                    thyroidDisease: "",
                    chronicPain: "",
                    cirrhosisoftheliver: "",
                    immuneDisease: "",
                    tuberculosis: "",
                    diabetes: "",
                    kidneyDisease: "",
                    ulcer: "",
                    eatingDisorder: "",
                    muscleDisorder: "",
                    mensHealthProblems: "",
                    hivAids: "",
                    none: "",
                    other: "",
                },
                cancerType: "",
                otherType: "",
                lastVisitPrimaryCarePhysician: "",
                comment: releventMedicalInformationComment,
                anyAllergies: "",
                hypersensitivities: "",
                patientPainLevel: {
                    currentLevelOfPhysicalPain: "",
                    isCurrentReceivingTreatementPain: "",
                    isReceivingTreatement: "",
                    treatementDetails: [],
                    disablityStatus: {
                        hearingImpairment: "",
                        sightImpairment: "",
                        intellectualDevelopmentalDisability: "",
                        other: "",
                        none: "",
                        comment: "",
                    },
                    adjustDisability: "",
                    requireEquipmentOrServices: "",
                },
            },
            // recommendations: {
            //   level_of_care: recommendationsComment
            // },
            mentalStatus: {
                physicalStature: {
                    small: null,
                    average: null,
                    tall: null,
                },
                weight: {
                    thin: null,
                    average: null,
                    overweight: null,
                    obese: null,
                },
                grooming: {
                    wellGroomed: null,
                    normal: null,
                    neglected: null,
                    bizarre: null,
                },
                clothing: {
                    neatClean: null,
                    inappropriate: null,
                    dirty: null,
                    seductive: null,
                    disheveled: null,
                    bizarre: null,
                },
                posture: {
                    normal: null,
                    tense: null,
                    stooped: null,
                    rigid: null,
                    slumped: null,
                    bizarre: null,
                },
                attitude: {
                    cooperative: null,
                    passive: null,
                    guarded: null,
                    irritable: null,
                    manipulative: null,
                    seductive: null,
                    suspicious: null,
                    defensive: null,
                    dramatic: null,
                    silly: null,
                    hostile: null,
                    critical: null,
                    resistant: null,
                    sarcastic: null,
                    uninterested: null,
                    argumentative: null,
                },
                motor: {
                    nonremarkable: null,
                    tremor: null,
                    slowed: null,
                    tics: null,
                    restless: null,
                    agitated: null,
                },
                speech: {
                    normal: null,
                    rapid: null,
                    slurred: null,
                    loud: null,
                    paucity: null,
                    pressured: null,
                    mute: null,
                },
                affect: {
                    appropriate: null,
                    inappropriate: null,
                    flat: null,
                    restricted: null,
                    blunted: null,
                    labile: null,
                },
                mood: {
                    euthymic: null,
                    confused: null,
                    pessimistic: null,
                    depressed: null,
                    anxious: null,
                    euphoric: null,
                    apathetic: null,
                    angry: null,
                },
                thoughtForm: {
                    goaldirected: null,
                    appropriate: null,
                    logical: null,
                    tangentialthinking: null,
                    circumstantial: null,
                    looseassociations: null,
                    confused: null,
                    incoherent: null,
                    perseverations: null,
                    flightofidea: null,
                    slownessofthought: null,
                },
                thoughtContent: {
                    appropriate: null,
                    paranoid: null,
                    suspicions: null,
                    persecutions: null,
                    paucity: null,
                    delusions: null,
                    bizarre: null,
                    hypochondriac: null,
                    ideasofreference: null,
                },
                preoccupations: {
                    phobias: null,
                    guilt: null,
                    other: null,
                    somatic: null,
                    religion: null,
                    suicide: null,
                    homicidal: null,
                },
                hallucinations: {
                    auditory: null,
                    other: null,
                    visual: null,
                    sensory: null,
                },
                orientation: {
                    person: null,
                    place: null,
                    time: null,
                    situation: null,
                },
                levelOfIntellectualFunctioning: {
                    belowAverage: null,
                    average: null,
                    aboveAverage: null,
                },
                fundofKnowledge: {
                    belowAverage: null,
                    average: null,
                    aboveAverage: null,
                },
                judgment: {
                    belowAverage: null,
                    average: null,
                    aboveAverage: null,
                },
                insightIntoProblems: {
                    belowAverage: null,
                    average: null,
                    aboveAverage: null,
                },
                clinicalImpressionSummary: combinedComment,
            },
        };

        return dataExtracted;
    }

    /**
     * Updates an existing digital assessment with AI-generated data, but only for fields that are empty
     * @param clientId - The client ID
     * @param therapistId - The therapist ID
     * @param aiGeneratedData - The AI-generated data
     * @returns The result of the update operation
     */
    export async function updateDigitalAssessmentWithAIData(
        clientId: Types.ObjectId,
        therapistId: Types.ObjectId,
        aiGeneratedData: any
    ): Promise<any> {
        // 1. Find the existing assessment
        const existingAssessment = await DigitalAssessmentForms.findOne({
            clientId: clientId,
            therapistId: therapistId,
        });

        if (!existingAssessment) {
            throw new Error("No existing assessment found for this client and therapist");
        }

        // 2. Define the mapping between AI data and database schema
        const fieldMapping: { [key: string]: string } = {
            // Trauma History mappings
            "traumaHistory.badAccidentDescribe": "traumaHistory.badAccidentDescribe",
            "traumaHistory.naturalDisasterdDescribe": "traumaHistory.naturalDisasterdDescribe",
            "traumaHistory.militoryCombatDescribe": "traumaHistory.militoryCombatDescribe",
            "traumaHistory.seriousAttackDescribe": "traumaHistory.seriousAttackDescribe",
            "traumaHistory.describeSexualCoercion": "traumaHistory.describeSexualCoercion",
            "traumaHistory.describeTraumaticExperience": "traumaHistory.describeTraumaticExperience",

            // Symptom Checklist comment mappings
            "symptomChicklist.depressiveSymptoms.comment": "symptomChicklist.depressiveSymptoms.comment",
            "symptomChicklist.manicSymptoms.comment": "symptomChicklist.manicSymptoms.comment",
            "symptomChicklist.conductLegalProblem.comment": "symptomChicklist.conductLegalProblem.comment",
            "symptomChicklist.psychosis.comment": "symptomChicklist.psychosis.comment",
            "symptomChicklist.anxietySymptoms.comment": "symptomChicklist.anxietySymptoms.comment",
            "symptomChicklist.attentionSymptoms.comment": "symptomChicklist.attentionSymptoms.comment",

            // Biological Function comment mappings
            "biologicalFunction.sleep.comment": "biologicalFunction.sleep.comment",
            "biologicalFunction.nutritionalStatus.comment": "biologicalFunction.nutritionalStatus.comment",

            // Presenting Problem mappings
            "presentingProblem.description": "presentingProblem.description",
            "presentingProblem.historyOfProblem": "presentingProblem.historyOfProblem",
            "historyOfProblem": "historyOfProblem",

            // Relevant Medical Information mappings
            "releventMedicalInformation.comment": "releventMedicalInformation.comment",

            // Mental Status mappings
            "mentalStatus.clinicalImpressionSummary": "mentalStatus.clinicalImpressionSummary",
        };

        // 3. Prepare the update object
        const updateData: any = {};

        /**
         * Helper function to check if a field is empty
         */
        const isEmpty = (value: any): boolean => {
            return value === null || value === undefined || value === "";
        };

        /**
         * Helper function to get nested value from object using dot notation
         */
        const getNestedValue = (obj: any, path: string): any => {
            return path.split('.').reduce((current, key) => current?.[key], obj);
        };

        /**
         * Helper function to set nested value using dot notation
         */
        const setNestedValue = (obj: any, path: string, value: any): void => {
            const keys = path.split('.');
            const lastKey = keys.pop()!;
            const target = keys.reduce((current, key) => {
                if (!current[key]) current[key] = {};
                return current[key];
            }, obj);
            target[lastKey] = value;
        };

        // 4. Process the mapping
        for (const [aiPath, dbPath] of Object.entries(fieldMapping)) {
            const aiValue = getNestedValue(aiGeneratedData, aiPath);
            const existingValue = getNestedValue(existingAssessment, dbPath);

            // Only process if AI has a non-empty string value
            if (typeof aiValue === 'string' && aiValue.trim() !== "") {
                if (isEmpty(existingValue)) {
                    // If DB field is empty, just use AI value
                    updateData[dbPath] = aiValue;
                } else {
                    // If DB field has existing value, combine with AI value
                    const combinedValue = `${existingValue}\n\nAI generated:\n${aiValue}`;
                    updateData[dbPath] = combinedValue;
                }
            }
        }

        // 5. Perform the update if there are fields to update
        if (Object.keys(updateData).length > 0) {
            console.log("Fields to update:", updateData);
            const result = await DigitalAssessmentForms.updateOne(
                {
                    clientId: clientId,
                    therapistId: therapistId,
                },
                { $set: updateData }
            );
            return { result, updatedFields: Object.keys(updateData), updatedData: updateData };
        } else {
            return { message: "No empty fields to update" };
        }
    }
}
