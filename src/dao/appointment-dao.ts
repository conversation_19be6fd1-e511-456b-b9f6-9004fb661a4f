import moment = require("moment");
import { Types } from "mongoose";
import { start } from "repl";
import { StringOrObjectId } from "../common/util";
import { AppointmentStatus, ApprovalStatus, DAppointment, IAppointment, MeetingStatus, RepeatType } from "../models/appointment-model";
import { AppointmentSMSStatus, UserRole } from "../models/user-model";
import Appointment from "../schemas/appointment-schema";
import Transaction from "../schemas/transaction-schema";
import User from "../schemas/user-schema";
import { TransactionType } from "../models/transaction-model";
export namespace AppointmentDao {
  const populateOptions = [
    {
      path: "therapistId",
      select: {
        firstname: 1,
        lastname: 1,
        photoId: 1,
        coverPhotoId: 1,
        workingHours: 1,
        blockedByAdmin: 1,
        adminApproved: 1,
      },
      populate: [
        { path: "photoId", model: "Upload" },
        { path: "coverPhotoId", model: "Upload" },
      ],
    },
    {
      path: "clientId",
      select: {
        firstname: 1,
        lastname: 1,
        photoId: 1,
        coverPhotoId: 1,
        verifiedStatus: 1,
        blockedByAdmin: 1,
        adminApproved: 1,
        premiumStatus: 1,
        subscriptionStatus: 1
      },
      populate: [
        { path: "photoId", model: "Upload" },
        { path: "coverPhotoId", model: "Upload" },
      ],
    },
    {
      path: "createdBy",
      select: {
        firstname: 1,
        lastname: 1,
        photoId: 1,
        coverPhotoId: 1,
        verifiedStatus: 1,
        blockedByAdmin: 1,
        adminApproved: 1,
      },
      populate: [
        { path: "photoId", model: "Upload" },
        { path: "coverPhotoId", model: "Upload" },
      ],
    },
  ];

  const populateOptions2 = [
    {
      path: "therapistId",
      select: {
        firstname: 1,
        lastname: 1,
        email: 1,
        primaryPhone: 1,
        reminderType: 1,
        reminderTime: 1,
      },
    },
    {
      path: "createdBy",
      select: {
        firstname: 1,
        lastname: 1,
        email: 1,
        primaryPhone: 1,
      },
    },
    {
      path: "clientId",
      select: {
        firstname: 1,
        lastname: 1,
        email: 1,
        primaryPhone: 1,
        reminderType: 1,
        reminderTime: 1,
        smsStop: 1,
      },
    },
  ];

  export async function createAppointment(appointmentDetails: DAppointment): Promise<IAppointment> {
    const iAppointment = new Appointment(appointmentDetails);
    const response = await iAppointment.save();
    await User.findOneAndUpdate(
      { _id: response.clientId },
      { $set: { clientActiveStatus: true } },
      { new: true }
    );
    return response;
  }

  export async function updateAppointment(appointmentId: Types.ObjectId, appointmentDetails: Partial<DAppointment>): Promise<IAppointment> {
    const response = await Appointment.findByIdAndUpdate({ _id: appointmentId }, { $set: appointmentDetails }, { new: true }).populate(
      populateOptions
    );
    
    await User.findOneAndUpdate(
      { _id: response.clientId },
      { $set: { clientActiveStatus: true } },
      { new: true }
    );
    return response;
  }


  export async function updateAppointmentData(
    id: StringOrObjectId,
    value: string
  ): Promise<IAppointment> {
    let user = await Appointment.findByIdAndUpdate(id, {
      smsStatus: value,
    });
    await User.findOneAndUpdate(
      { _id: user.clientId },
      { $set: { clientActiveStatus: true } },
      { new: true }
    );
    return user;
  }

  export async function getAppointmentById(appointmentId: StringOrObjectId): Promise<IAppointment> {
    const response = await Appointment.findById(appointmentId);
    return response;
  }

  export async function getAppointmentSendSMSById(appointmentId: StringOrObjectId): Promise<IAppointment> {
    const response = await Appointment.findById(appointmentId).populate(populateOptions2);
    return response;
  }

  export async function deleteAppointment(appointmentId: StringOrObjectId): Promise<IAppointment> {
    const response = await Appointment.findByIdAndDelete(appointmentId).populate(populateOptions);
    return response;
  }

  export async function deleteMultipleAppointments(appointments: StringOrObjectId[]): Promise<any> {
    const response = await Appointment.deleteMany({ _id: { $in: appointments } });

    return response;
  }

  export async function getAppointmentByAppointmentId(appointmentId: StringOrObjectId): Promise<IAppointment> {
    const response = await Appointment.findById(appointmentId).populate(populateOptions);
    return response;
  }

  export async function searchAppointmentByDate(date: Date, createdBy: Types.ObjectId): Promise<IAppointment[]> {
    const response = await Appointment.find({
      start: date,
      createdBy: createdBy,
    }).populate(populateOptions);
    return response;
  }

  export async function getAllAppointmentBySpecipicUsers(therapistId: any, clientId: any): Promise<IAppointment[]> {
    const response = await Appointment.find({
      therapistId: therapistId,
      clientId: clientId,
      status: { $ne: AppointmentStatus.COMPLETED }
    });
    return response;
  }

  export async function getAllPendingAppointmentsByClientId(clientId: Types.ObjectId, limit?: number): Promise<IAppointment[]> {
    const now = new Date(moment().toISOString());
    const response = await Appointment.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                blockedByAdmin: 1,
                adminApproved: 1,
              },
            },
          ],
          as: "createdBy",
        },
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "createdBy.photoId",
          foreignField: "_id",
          as: "createdBy.photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "createdBy.coverPhotoId",
          foreignField: "_id",
          as: "createdBy.coverPhotoId",
        },
      },
      {
        $unwind: {
          path: "$createdBy.photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$createdBy.coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                blockedByAdmin: 1,
                adminApproved: 1,
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "clientId.photoId",
          foreignField: "_id",
          as: "clientId.photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "clientId.coverPhotoId",
          foreignField: "_id",
          as: "clientId.coverPhotoId",
        },
      },
      {
        $unwind: {
          path: "$clientId.photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$clientId.coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                coverPhotoId: 1,
                workingHours: 1,
                blockedByAdmin: 1,
                adminApproved: 1,
                blockedDates: 1
              },
            },
          ],
          as: "therapistId",
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "therapistId.photoId",
          foreignField: "_id",
          as: "therapistId.photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "therapistId.coverPhotoId",
          foreignField: "_id",
          as: "therapistId.coverPhotoId",
        },
      },
      {
        $unwind: {
          path: "$therapistId.photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$therapistId.coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $and: [
            { "clientId._id": clientId },
            { "createdBy._id": { $ne: clientId } },
            { approvedStatus: ApprovalStatus.PENDING },
            { status: MeetingStatus.WAITING_FOR_APPROVAL },
            {
              end: {
                $gte: now,
              },
            },
            {
              $or: [{ "therapistId.blockedByAdmin": false }, { "therapistId.blockedByAdmin": undefined }],
            },
          ],
        },
      },
      {
        $limit: limit,
      },
      {
        $sort: { createdAt: -1 },
      },
    ]);
    return response;
  }


  export async function getAllPendingAppointmentsByTherapistId(therapistId: Types.ObjectId, limit?: number): Promise<IAppointment[]> {
    const now = new Date(moment().toISOString());
    const response = await Appointment.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                blockedByAdmin: 1,
                adminApproved: 1,
              },
            },
          ],
          as: "createdBy",
        },
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "createdBy.photoId",
          foreignField: "_id",
          as: "createdBy.photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "createdBy.coverPhotoId",
          foreignField: "_id",
          as: "createdBy.coverPhotoId",
        },
      },
      {
        $unwind: {
          path: "$createdBy.photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$createdBy.coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                blockedByAdmin: 1,
                adminApproved: 1,
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "clientId.photoId",
          foreignField: "_id",
          as: "clientId.photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "clientId.coverPhotoId",
          foreignField: "_id",
          as: "clientId.coverPhotoId",
        },
      },
      {
        $unwind: {
          path: "$clientId.photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$clientId.coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                coverPhotoId: 1,
                workingHours: 1,
                blockedByAdmin: 1,
                adminApproved: 1,
                blockedDates: 1
              },
            },
          ],
          as: "therapistId",
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "therapistId.photoId",
          foreignField: "_id",
          as: "therapistId.photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "therapistId.coverPhotoId",
          foreignField: "_id",
          as: "therapistId.coverPhotoId",
        },
      },
      {
        $unwind: {
          path: "$therapistId.photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$therapistId.coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $and: [
            { "therapistId._id": therapistId },
            { "createdBy._id": { $ne: therapistId } },
            { approvedStatus: ApprovalStatus.PENDING },
            { status: MeetingStatus.WAITING_FOR_APPROVAL },
            {
              end: {
                $gte: now,
              },
            },
            {
              $or: [{ "clientId.blockedByAdmin": false }, { "clientId.blockedByAdmin": undefined }],
            },
          ],
        },
      },
      {
        $limit: limit,
      },
      {
        $sort: { createdAt: -1 },
      },
    ]);

    return response;
  }

  export async function getAllAppointmentsByClientIdNew(clientId: Types.ObjectId, customActiveTab?: string, limit?: number, offset?: number): Promise<IAppointment[]> {
    let status = null;
    const currentDate = new Date();

    if (customActiveTab == "all") {
      status = null
    } else if (customActiveTab == "1") {
      status = null
    } else if (customActiveTab == "2") {
      status = {
        $or: [
          { status: AppointmentStatus.PENDING, end: { $gt: currentDate } },
          { status: AppointmentStatus.WAITING_FOR_APPROVAL, start: { $gt: currentDate } }
        ]
      };
    } else if (customActiveTab == "3") {
      status = {
        $or: [
          { start: { $lt: currentDate }, status: AppointmentStatus.PENDING },
          { start: { $lt: currentDate }, status: AppointmentStatus.WAITING_FOR_APPROVAL },
          { status: AppointmentStatus.COMPLETED }
        ]
      };
    } else if (customActiveTab == "4") {
      status = { status: AppointmentStatus.REJECTED }
    }

    if (limit) {
      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                  blockedDates: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              { "clientId._id": clientId },
              {
                $or: [{ "therapistId.blockedByAdmin": false }, { "therapistId.blockedByAdmin": undefined }],
              },
              status,
            ],
          },
        },
      ]).sort({ createdAt: 1 })
        .skip(limit * (offset - 1))
        .limit(limit);

      return response;
    } else {
      let status = null;

      const currentDate = new Date();

      if (customActiveTab == "all") {
        status = null
      } else if (customActiveTab == "1") {
        status = { status: AppointmentStatus.WAITING_FOR_APPROVAL, start: { $gt: currentDate } }
      } else if (customActiveTab == "2") {
        status = { status: AppointmentStatus.PENDING, end: { $gt: currentDate } }
      } else if (customActiveTab == "3") {
        status = {
          $or: [
            { start: { $lt: currentDate }, status: AppointmentStatus.PENDING },
            { start: { $lt: currentDate }, status: AppointmentStatus.WAITING_FOR_APPROVAL },
            { status: AppointmentStatus.COMPLETED }
          ]
        };
      } else if (customActiveTab == "4") {
        status = { status: AppointmentStatus.REJECTED }
      }

      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              { "clientId._id": clientId },
              {
                $or: [{ "therapistId.blockedByAdmin": false }, { "therapistId.blockedByAdmin": undefined }],
              },
              status,
            ],
          },
        },
      ]).sort({ createdAt: 1 })
        .skip(limit * (offset - 1))
        .limit(limit);

      return response;
    }
  }

  export async function getAllAppointmentsByClientId(clientId: Types.ObjectId, limit?: number): Promise<IAppointment[]> {
    const now = new Date(moment().toISOString());

    if (limit) {
      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              //{ "createdBy._id": clientId },
              { "clientId._id": clientId },
              {
                $or: [{ "therapistId.blockedByAdmin": false }, { "therapistId.blockedByAdmin": undefined }],
              },
            ],
          },
        },
        {
          $limit: limit,
        },
        {
          $sort: { createdAt: -1 },
        },
      ]);
      return response;
    } else {
      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              { "clientId._id": clientId },
              {
                $or: [{ "therapistId.blockedByAdmin": false }, { "therapistId.blockedByAdmin": undefined }],
              },
            ],
          },
        },
      ]);

      return response;
    }
  }

  export async function getAllUpcomingAppointmentsByClientId(clientId: Types.ObjectId, limit?: number): Promise<IAppointment[]> {
    const now = new Date(moment().toISOString());
    if (limit) {
      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              //{ "createdBy._id": clientId },
              { "clientId._id": clientId },
              { approvedStatus: ApprovalStatus.APPROVED },
              { status: MeetingStatus.PENDING },
              {
                end: {
                  $gte: now,
                },
              },
              {
                $or: [{ "therapistId.blockedByAdmin": false }, { "therapistId.blockedByAdmin": undefined }],
              },
            ],
          },
        },
        {
          $limit: limit,
        },
        {
          $sort: { start: 1 },
        },
      ]);
      return response;
    } else {
      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              //{ "createdBy._id": clientId },
              { "clientId._id": clientId },
              { approvedStatus: ApprovalStatus.APPROVED },
              {
                end: {
                  $gte: now,
                },
              },
              {
                $or: [{ "therapistId.blockedByAdmin": false }, { "therapistId.blockedByAdmin": undefined }],
              },
            ],
          },
        },
      ]);

      return response;
    }
  }

  export async function getAllAppointmentsByTherapistIdNew(therapistId: Types.ObjectId, customActiveTab?: string, limit?: number, offset?: number): Promise<IAppointment[]> {
    const now = new Date(moment().toISOString());

    if (limit) {
      let status = null;
      const currentDate = new Date();

      if (customActiveTab == "all") {
        status = null
      } else if (customActiveTab == "1") {
        status = { status: AppointmentStatus.WAITING_FOR_APPROVAL, start: { $gt: currentDate } }
      } else if (customActiveTab == "2") {
        status = { status: AppointmentStatus.PENDING, end: { $gt: currentDate } }
      } else if (customActiveTab == "3") {
        status = {
          $or: [
            { start: { $lt: currentDate }, status: AppointmentStatus.PENDING },
            { start: { $lt: currentDate }, status: AppointmentStatus.WAITING_FOR_APPROVAL },
            { status: AppointmentStatus.COMPLETED }
          ]
        };
      } else if (customActiveTab == "4") {
        status = { status: AppointmentStatus.REJECTED }
      }

      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              { "therapistId._id": therapistId },
              {
                $or: [{ "clientId.blockedByAdmin": false }, { "clientId.blockedByAdmin": undefined }],
              },
              status,
            ],
          },
        },
      ]).sort({ createdAt: 1 })
        .skip(limit * (offset - 1))
        .limit(limit);

      return response;
    } else {
      let status = null;

      const currentDate = new Date();

      if (customActiveTab == "all") {
        status = null
      } else if (customActiveTab == "1") {
        status = { status: AppointmentStatus.WAITING_FOR_APPROVAL, start: { $gt: currentDate } }
      } else if (customActiveTab == "2") {
        status = { status: AppointmentStatus.PENDING, end: { $gt: currentDate } }
      } else if (customActiveTab == "3") {
        status = {
          $or: [
            { start: { $lt: currentDate }, status: AppointmentStatus.PENDING },
            { start: { $lt: currentDate }, status: AppointmentStatus.WAITING_FOR_APPROVAL },
            { status: AppointmentStatus.COMPLETED }
          ]
        };
      } else if (customActiveTab == "4") {
        status = { status: AppointmentStatus.REJECTED }
      }

      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              { "therapistId._id": therapistId },
              {
                $or: [{ "clientId.blockedByAdmin": false }, { "clientId.blockedByAdmin": undefined }],
              },
              status,
            ],
          },
        },
      ]).sort({ createdAt: 1 })
        .skip(limit * (offset - 1))
        .limit(limit);

      return response;
    }
  }

  export async function getAllAppointmentsByTherapistId(therapistId: Types.ObjectId, limit: number): Promise<IAppointment[]> {
    const now = new Date(moment().toISOString());
    if (limit) {
      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                  subscriptionStatus: 1,
                  premiumStatus: 1
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              { "therapistId._id": therapistId },
              { approvedStatus: { $ne: ApprovalStatus.REJECTED } },
              { status: { $ne: ApprovalStatus.REJECTED } },
              {
                $or: [{ "clientId.blockedByAdmin": false }, { "clientId.blockedByAdmin": undefined }],
              },
            ],
          },
        },
        {
          $limit: limit,
        },
        {
          $sort: { createdAt: -1 },
        },
      ]);

      return response;
    } else {
      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                  subscriptionStatus: 1,
                  premiumStatus: 1
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              { "therapistId._id": therapistId },
              { approvedStatus: { $ne: ApprovalStatus.REJECTED } },
              { status: { $ne: ApprovalStatus.REJECTED } },
              {
                $or: [{ "clientId.blockedByAdmin": false }, { "clientId.blockedByAdmin": undefined }],
              },
            ],
          },
        },
      ]);

      return response;
    }
  }

  export async function getAllUpcomingAppointmentsByTherapistId(therapistId: Types.ObjectId, limit: number): Promise<IAppointment[]> {
    const now = new Date(moment().toISOString());
    if (limit) {
      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              { "therapistId._id": therapistId },
              { approvedStatus: ApprovalStatus.APPROVED },
              { status: MeetingStatus.PENDING },
              {
                end: {
                  $gte: now,
                },
              },
              {
                $or: [{ "clientId.blockedByAdmin": false }, { "clientId.blockedByAdmin": undefined }],
              },
            ],
          },
        },
        {
          $sort: { start: 1 },
        },
        {
          $limit: limit,
        },
      ]);

      return response;
    } else {
      const response = await Appointment.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "createdBy",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "createdBy",
          },
        },
        {
          $unwind: {
            path: "$createdBy",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.photoId",
            foreignField: "_id",
            as: "createdBy.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "createdBy.coverPhotoId",
            foreignField: "_id",
            as: "createdBy.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$createdBy.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$createdBy.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clientId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1,
                },
              },
            ],
            as: "clientId",
          },
        },
        {
          $unwind: {
            path: "$clientId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.photoId",
            foreignField: "_id",
            as: "clientId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "clientId.coverPhotoId",
            foreignField: "_id",
            as: "clientId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$clientId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$clientId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "therapistId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstname: 1,
                  lastname: 1,
                  photoId: 1,
                  coverPhotoId: 1,
                  workingHours: 1,
                  blockedByAdmin: 1,
                  adminApproved: 1
                },
              },
            ],
            as: "therapistId",
          },
        },
        {
          $unwind: {
            path: "$therapistId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.photoId",
            foreignField: "_id",
            as: "therapistId.photoId",
          },
        },
        {
          $lookup: {
            from: "uploads",
            localField: "therapistId.coverPhotoId",
            foreignField: "_id",
            as: "therapistId.coverPhotoId",
          },
        },
        {
          $unwind: {
            path: "$therapistId.photoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$therapistId.coverPhotoId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $and: [
              { "therapistId._id": therapistId },
              { approvedStatus: ApprovalStatus.APPROVED },
              {
                end: {
                  $gte: now,
                },
              },
              {
                $or: [{ "clientId.blockedByAdmin": false }, { "clientId.blockedByAdmin": undefined }],
              },
            ],
          },
        },
      ]);

      return response;
    }
  }

  export async function getAllUpcomingAppointmentById(id: Types.ObjectId, limit?: number): Promise<IAppointment[]> {
    const start = moment(new Date()).format("L");
    const end = moment().add(6).format("L");

    if (limit) {
      const response = await Appointment.find(
        //{ createdBy: id },
        { clientId: id },
        {
          start: {
            $gt: start,
            $lt: end,
          },
        }
      )
        .populate(populateOptions)
        .limit(limit)
        .sort({ createdAt: -1 });

      const appointmentList = response.filter((appointment: any) =>
        appointment.therapistId.blockedByAdmin ? appointment.therapistId.blockedByAdmin === false : appointment
      );

      return appointmentList;
    } else {
      const response = await Appointment.find({ createdBy: id }).populate(populateOptions);

      const appointmentList = response.filter((appointment: any) =>
        appointment.therapistId.blockedByAdmin ? appointment.therapistId.blockedByAdmin === false : appointment
      );

      return appointmentList;
    }
  }

  export async function viewAllAppointmentsByTherapistId(therapistId: string): Promise<IAppointment[]> {
    const response = await Appointment.find({
      therapistId: Types.ObjectId(therapistId),
      approvedStatus: { $ne: ApprovalStatus.REJECTED },
      status: { $ne: AppointmentStatus.REJECTED },
    }).populate(populateOptions);
    return response;
  }

  export async function getAllAppointmentsListByClientId(userId: Types.ObjectId): Promise<any[]> {
    const response = await Appointment.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                blockedByAdmin: 1,
                adminApproved: 1,
              },
            },
          ],
          as: "createdBy",
        },
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "createdBy.photoId",
          foreignField: "_id",
          as: "createdBy.photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "createdBy.coverPhotoId",
          foreignField: "_id",
          as: "createdBy.coverPhotoId",
        },
      },
      {
        $unwind: {
          path: "$createdBy.photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$createdBy.coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                blockedByAdmin: 1,
                adminApproved: 1,
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "clientId.photoId",
          foreignField: "_id",
          as: "clientId.photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "clientId.coverPhotoId",
          foreignField: "_id",
          as: "clientId.coverPhotoId",
        },
      },
      {
        $unwind: {
          path: "$clientId.photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$clientId.coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                photoId: 1,
                coverPhotoId: 1,
                workingHours: 1,
                blockedByAdmin: 1,
                adminApproved: 1
              },
            },
          ],
          as: "therapistId",
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "therapistId.photoId",
          foreignField: "_id",
          as: "therapistId.photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "therapistId.coverPhotoId",
          foreignField: "_id",
          as: "therapistId.coverPhotoId",
        },
      },
      {
        $unwind: {
          path: "$therapistId.photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$therapistId.coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $and: [
            //{ "createdBy._id": clientId },
            { "clientId._id": userId },
            {
              $or: [{ "therapistId.blockedByAdmin": false }, { "therapistId.blockedByAdmin": undefined }],
            },
          ],
        },
      },
    ]);

    return response;
  }


  export async function getAllAppointmentsByUserId(userId: Types.ObjectId, limit: number, offset: number, role: string): Promise<any[]> {
    let response;
    if (role == UserRole.CLIENT) {
      response = await Appointment.aggregate([
        {
          $match: {
            createdBy: userId,
          },
        },
        {
          $group: {
            _id: { $dateToString: { format: "%Y-%m-%d", date: "$start" } },
            appointments: { $push: "$_id" },
          },
        },
        { $sort: { _id: 1 } },
        {
          $lookup: {
            from: "appointments",
            localField: "appointments",
            foreignField: "_id",
            as: "appointments",
          },
        },
        {
          $unwind: {
            path: "$appointments",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "appointments.therapistId",
            foreignField: "_id",
            as: "appointments.therapistId",
          },
        },
        { $unwind: "$appointments.therapistId" },
        {
          $lookup: {
            from: "users",
            localField: "appointments.createdBy",
            foreignField: "_id",
            as: "appointments.createdBy",
          },
        },
        { $unwind: "$appointments.createdBy" },
        {
          $match: {
            $and: [
              {
                $or: [{ "appointments.therapistId.blockedByAdmin": false }, { "appointments.therapistId.blockedByAdmin": undefined }],
              },
              {
                $or: [{ "appointments.createdBy.blockedByAdmin": false }, { "appointments.createdBy.blockedByAdmin": undefined }],
              },
            ],
          },
        },
        {
          $group: {
            _id: "$_id",
            appointments: { $push: "$appointments" },
          },
        },
        { $skip: limit * (offset - 1) },
        { $limit: limit },
      ]);
    } else {
      response = await Appointment.aggregate([
        {
          $match: {
            therapistId: userId,
          },
        },
        {
          $group: {
            _id: { $dateToString: { format: "%Y-%m-%d", date: "$start" } },
            appointments: { $push: "$_id" },
          },
        },
        { $sort: { _id: 1 } },
        {
          $lookup: {
            from: "appointments",
            localField: "appointments",
            foreignField: "_id",
            as: "appointments",
          },
        },
        {
          $unwind: {
            path: "$appointments",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "appointments.therapistId",
            foreignField: "_id",
            as: "appointments.therapistId",
          },
        },
        { $unwind: "$appointments.therapistId" },
        {
          $lookup: {
            from: "users",
            localField: "appointments.createdBy",
            foreignField: "_id",
            as: "appointments.createdBy",
          },
        },
        { $unwind: "$appointments.createdBy" },
        {
          $match: {
            $and: [
              {
                $or: [{ "appointments.therapistId.blockedByAdmin": false }, { "appointments.therapistId.blockedByAdmin": undefined }],
              },
              {
                $or: [{ "appointments.createdBy.blockedByAdmin": false }, { "appointments.createdBy.blockedByAdmin": undefined }],
              },
            ],
          },
        },
        {
          $group: {
            _id: "$_id",
            appointments: { $push: "$appointments" },
          },
        },
        { $skip: limit * (offset - 1) },
        { $limit: limit },
      ]);
    }

    return response;
  }

  export async function getAllPendingAndCompletedAppointmentsByTherapistId(user: any): Promise<any> {
    if (!user) {
      return "user not found!";
    }

    if (user.role == UserRole.THERAPIST) {
      const completedAppointments = await Appointment.find({
        therapistId: user._id,
        status: AppointmentStatus.COMPLETED,
      });
      const pendingAppointments = await Appointment.find({
        therapistId: user._id,
        status: AppointmentStatus.PENDING,
      });

      let resObj = {
        completedAppointments: completedAppointments.length,
        pendingAppointments: pendingAppointments.length,
      };

      return resObj;
    }
  }

  export async function getAllPendingAndCompletedAppointmentsByUserId(user: any, filterValue: any): Promise<any> {
    if (!user) {
      return "user not found!";
    }

    if (user.role == UserRole.THERAPIST) {
      let dateFilter: any = {};
      switch (filterValue) {
        case "WEEK":
          const currentDay = new Date().getDay();
          const startOfWeekDate = new Date(
            new Date().getFullYear(),
            new Date().getMonth(),
            new Date().getDate() - currentDay,
            0,
            0,
            0
          );
          const endOfWeekDate = new Date(
            startOfWeekDate.getFullYear(),
            startOfWeekDate.getMonth(),
            startOfWeekDate.getDate() + 6,
            23,
            59,
            59
          );
          dateFilter = {
            createdAt: {
              $gte: startOfWeekDate,
              $lte: endOfWeekDate,
            },
          };
          break;

        case "MONTH":
          const startOfMonthDate = new Date(
            new Date().getFullYear(),
            new Date().getMonth(),
            1,
            0,
            0,
            0
          );
          const endOfMonthDate = new Date(
            new Date().getFullYear(),
            new Date().getMonth() + 1,
            0,
            23,
            59,
            59
          );
          dateFilter = {
            createdAt: {
              $gte: startOfMonthDate,
              $lte: endOfMonthDate,
            },
          };
          break;

        case "4MONTH":
          const currentDate = new Date();
          const startOfFourMonthsAgo = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() - 4,
            1,
            0,
            0,
            0
          );
          const endOfFourMonthsAgo = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() + 1,
            0,
            23,
            59,
            59
          );
          dateFilter = {
            createdAt: {
              $gte: startOfFourMonthsAgo,
              $lte: endOfFourMonthsAgo,
            },
          };
          break;

        case "YEAR":
          const startOfYearDate = new Date(new Date().getFullYear(), 0, 1, 0, 0, 0);
          const endOfYearDate = new Date(new Date().getFullYear() + 1, 0, 0, 23, 59, 59);
          dateFilter = {
            createdAt: {
              $gte: startOfYearDate,
              $lte: endOfYearDate,
            },
          };
          break;

        default:
          break;
      }

      const completedAppointments = await Appointment.find({
        therapistId: user._id,
        status: AppointmentStatus.COMPLETED,
        ...dateFilter
      });
      const pendingAppointments = await Appointment.find({
        therapistId: user._id,
        status: AppointmentStatus.PENDING,
        ...dateFilter
      });

      let resObj = {
        completedAppointments: completedAppointments.length,
        pendingAppointments: pendingAppointments.length,
      };

      return resObj;
    }
  }

  export async function getAllTherapistRevanuMonthlyStats(user: any): Promise<any> {
    if (!user) {
      return "user not found!";
    }

    if (user.role == UserRole.THERAPIST) {
      const today = new Date();
      const startOfCurrentMonth = new Date(today.getFullYear(), today.getMonth(), 1, 0, 0, 0);

      const currentMonthFilter = {
        createdAt: {
          $gte: startOfCurrentMonth,
          $lte: today,
        },
      };


      const currentMonthResponse = await Transaction.find({ therapistId: user._id, type: TransactionType.EARNING, ...currentMonthFilter });
      const pastMonthResponse = await Transaction.find({ therapistId: user._id, type: TransactionType.EARNING });

      const totalRevenueCurrentMonth = currentMonthResponse.reduce((acc, transaction) => acc + transaction.transactionAmount, 0);
      const totalRevenue = pastMonthResponse.reduce((acc, transaction) => acc + transaction.transactionAmount, 0);

      return {
        totalRevenueCurrentMonth,
        totalRevenue,
      };
    }
  }

  export async function getAllTherapistRevanuStats(user: any, filterValue: any): Promise<any> {
    if (!user) {
      return "user not found!";
    }

    if (user.role == UserRole.THERAPIST) {
      let dateFilter: any = {};
      const monthNames = [
        "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
      ];

      const weekNames = [
        "Week1", "Week2", "Week3", "Week4",
      ];

      const dayNames = [
        "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"
      ];



      const monthlyStats: any[] = [];

      switch (filterValue) {
        case "WEEK":
          const currentDay = new Date().getDay();
          const startOfWeekDate = new Date(
            new Date().getFullYear(),
            new Date().getMonth(),
            new Date().getDate() - currentDay,
            0,
            0,
            0
          );
          const endOfWeekDate = new Date(
            startOfWeekDate.getFullYear(),
            startOfWeekDate.getMonth(),
            startOfWeekDate.getDate() + 6,
            23,
            59,
            59
          );

          for (let day = 0; day < 7; day++) {
            const startOfDay = new Date(startOfWeekDate);
            startOfDay.setDate(startOfWeekDate.getDate() + day);
            const endOfDay = new Date(startOfDay);
            endOfDay.setHours(23, 59, 59);

            const dateFilter = {
              createdAt: {
                $gte: startOfDay,
                $lte: endOfDay,
              },
            };

            const response = await Transaction.find({ therapistId: user._id, type: TransactionType.EARNING, ...dateFilter });
            const totalRevenue = response.reduce((acc, transaction) => acc + transaction.transactionAmount, 0);

            monthlyStats.push({
              month: dayNames[day],
              totalRevenue,
            });
          }
          break;

        case "MONTH":

          for (let week = 0; week < 4; week++) {
            const startOfWeekDate = new Date(
              new Date().getFullYear(),
              new Date().getMonth(),
              new Date().getDate() - (new Date().getDay() + week * 7),
              0,
              0,
              0
            );
            const endOfWeekDate = new Date(
              startOfWeekDate.getFullYear(),
              startOfWeekDate.getMonth(),
              startOfWeekDate.getDate() + 6,
              23,
              59,
              59
            );
            dateFilter = {
              createdAt: {
                $gte: startOfWeekDate,
                $lte: endOfWeekDate,
              },
            };


            const response = await Transaction.find({ therapistId: user._id, type: TransactionType.EARNING, ...dateFilter });
            const totalRevenue = response.reduce((acc, transaction) => acc + transaction.transactionAmount, 0);

            monthlyStats.push({
              month: weekNames[week],
              totalRevenue,
            });
          }
          break;

        case "4MONTH":
          const currentDate = new Date();
          const startOfFourMonthsAgo = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() - 4,
            1,
            0,
            0,
            0
          );
          const endOfFourMonthsAgo = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() + 1,
            0,
            23,
            59,
            59
          );
          dateFilter = {
            createdAt: {
              $gte: startOfFourMonthsAgo,
              $lte: endOfFourMonthsAgo,
            },
          };
          for (let month = 3; month >= 0; month--) {
            const startOfMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - month, 1, 0, 0, 0);
            const endOfMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - month + 1, 0, 23, 59, 59);

            const dateFilter = {
              createdAt: {
                $gte: startOfMonthDate,
                $lte: endOfMonthDate,
              },
            };

            const response = await Transaction.find({ therapistId: user._id, type: TransactionType.EARNING, ...dateFilter });

            const totalRevenue = response.reduce((acc, transaction) => acc + transaction.transactionAmount, 0);

            monthlyStats.push({
              month: monthNames[currentDate.getMonth() - month],
              totalRevenue,
            });
          }
          break;

        case "YEAR":
          const startOfYearDate = new Date(new Date().getFullYear(), 0, 1, 0, 0, 0);
          const endOfYearDate = new Date(new Date().getFullYear() + 1, 0, 0, 23, 59, 59);
          dateFilter = {
            createdAt: {
              $gte: startOfYearDate,
              $lte: endOfYearDate,
            },
          };
          for (let month = 0; month < 12; month++) {
            const startOfMonthDate = new Date(new Date().getFullYear(), month, 1, 0, 0, 0);
            const endOfMonthDate = new Date(new Date().getFullYear(), month + 1, 0, 23, 59, 59);

            const dateFilter = {
              createdAt: {
                $gte: startOfMonthDate,
                $lte: endOfMonthDate,
              },
            };

            const response = await Transaction.find({ therapistId: user._id, type: TransactionType.EARNING, ...dateFilter });

            const totalRevenue = response.reduce((acc, transaction) => acc + transaction.transactionAmount, 0);

            monthlyStats.push({
              month: monthNames[month],
              totalRevenue,
            });
          }
          break;

        default:
          const startOfYearDateD = new Date(new Date().getFullYear(), 0, 1, 0, 0, 0);
          const endOfYearDateD = new Date(new Date().getFullYear() + 1, 0, 0, 23, 59, 59);
          dateFilter = {
            createdAt: {
              $gte: startOfYearDateD,
              $lte: endOfYearDateD,
            },
          };
          for (let month = 0; month < 12; month++) {
            const startOfMonthDate = new Date(new Date().getFullYear(), month, 1, 0, 0, 0);
            const endOfMonthDate = new Date(new Date().getFullYear(), month + 1, 0, 23, 59, 59);

            const dateFilter = {
              createdAt: {
                $gte: startOfMonthDate,
                $lte: endOfMonthDate,
              },
            };

            const response = await Transaction.find({ therapistId: user._id, type: TransactionType.EARNING, ...dateFilter });

            const totalRevenue = response.reduce((acc, transaction) => acc + transaction.transactionAmount, 0);

            monthlyStats.push({
              month: monthNames[month],
              totalRevenue,
            });
          }
          break;
      }


      const labels = monthlyStats.map(stat => stat.month);
      const value = monthlyStats.map(stat => stat.totalRevenue);

      const therapistEarningsStats = {
        labels,
        value,
      };

      return therapistEarningsStats;
    }
  }

  export async function deleteAppointmentByClientId(clientId: StringOrObjectId): Promise<number> {
    const response = await Appointment.deleteMany({ clientId: clientId });
    return response.ok;
  }

  export async function deleteAppointmentByTherapistId(therapistId: StringOrObjectId): Promise<number> {
    const response = await Appointment.deleteMany({ therapistId: therapistId });
    return response.ok;
  }

  export async function getAllAppointmentsAndDetailsByUserId(userId: Types.ObjectId, role: string): Promise<any[]> {
    let response;
    if (role == UserRole.CLIENT) {
      response = await Appointment.find({ createdBy: userId });
    }

    return response;
  }

  export async function getAllAppointmentsBySelectedWeek(datesOfWeek: any) {
    const sessionByWeek = await Appointment.find({
      clientId: datesOfWeek.clientId,
      therapistId: datesOfWeek.therapistId,
      status: { $ne: AppointmentStatus.REJECTED },
      start: {
        $gte: new Date(datesOfWeek.startOfWeek),
        $lt: new Date(datesOfWeek.endOfWeek),
      },
    }).populate(populateOptions);

    return sessionByWeek;
  }

  export async function getAllAppointmentsBySelectedWeek2(datesOfWeek: any): Promise<IAppointment[]> {
    const sessionByWeek = await Appointment.find({
      clientId: datesOfWeek.clientId,
      therapistId: datesOfWeek.therapistId,
      status: { $ne: AppointmentStatus.REJECTED },
      start: {
        $gte: new Date(datesOfWeek.startOfWeek),
        $lt: new Date(datesOfWeek.endOfWeek),
      },
    }).populate(populateOptions);

    return sessionByWeek;
  }

  export async function getAllAppointmentsByTwoDates(tId: Types.ObjectId, dates: any) {
    const sessionByWeek = await Appointment.find({
      therapistId: tId,
      start: {
        $gte: new Date(dates.beginDate),
        $lt: new Date(dates.endDate),
      },
    })
      .sort({ createdAt: 1 })
      .distinct("clientId");

    return sessionByWeek;
  }

  export async function getAllAppointmentsBySelectedMonth(id: Types.ObjectId, therapistId: Types.ObjectId, datesOfMonth: any) {
    const sessionByWeek = await Appointment.find({
      createdBy: id,
      therapistId: therapistId,
      start: {
        $gte: new Date(datesOfMonth.startOfMonth),
        $lt: new Date(datesOfMonth.endOfMonth),
      },
    }).populate(populateOptions);

    return sessionByWeek;
  }

  export async function getAllAppointmentsInNext15Minutes(): Promise<IAppointment[]> {
    const start = new Date();
    const end = new Date(start.getTime() + 15 * 60000);

    const response = await Appointment.find({
      $and: [
        {
          start: {
            $gte: start,
            $lte: end,
          },
        },
        {
          $or: [
            { status: AppointmentStatus.PENDING },
            { status: AppointmentStatus.WAITING_FOR_APPROVAL }
          ]
        }
      ],
    })
      .select({ start: 1, color: 1 })
      .populate(populateOptions2)
      .sort({ createdAt: -1 });

    return response;
  }
  export async function getAllAppointmentsInNext30Minutes(): Promise<IAppointment[]> {
    const start = new Date();
    const end = new Date(start.getTime() + 30 * 60000);

    const response = await Appointment.find({
      $and: [
        {
          start: {
            $gte: start,
            $lte: end,
          },
        },
        {
          $or: [
            { status: AppointmentStatus.PENDING },
            { status: AppointmentStatus.WAITING_FOR_APPROVAL }
          ]
        }
      ],
    })
      .select({ start: 1, color: 1 })
      .populate(populateOptions2)
      .sort({ createdAt: -1 });

    return response;
  }

  export async function getAllAppointmentsInNextHour(): Promise<IAppointment[]> {
    const curTime = new Date();
    const start = new Date(curTime.getTime() + 55 * 60000);
    const end = new Date(start.getTime() + 15 * 60000);
    console.log("start", start)
    console.log("end", end)
    const response = await Appointment.find({
      $and: [
        {
           start: {
            $gte: start,
            $lte: end,
           },
        },
        {
          $or: [
            { status: AppointmentStatus.PENDING },
            { status: AppointmentStatus.WAITING_FOR_APPROVAL }
          ]
        },
      ],
    })
      .select({ start: 1, color: 1 })
      .populate(populateOptions2)
      .sort({ createdAt: -1 });

    return response;
  }


  export async function getAllAppointmentsInNextDay(): Promise<IAppointment[]> {
    const now = new Date();
    const start = new Date(now.getTime() + 1440 * 60000 - 15 * 60000);
    const end = new Date(now.getTime() + 1440 * 60000 + 15 * 60000);
    const response = await Appointment.find({
      $and: [
        {
          start: { $gte: start, $lte: end },
        },
        {
          $or: [
            { status: AppointmentStatus.PENDING },
            { status: AppointmentStatus.WAITING_FOR_APPROVAL }
          ]
        }
      ],
    })
      .select({ start: 1, color: 1 })
      .populate(populateOptions2)
      .sort({ createdAt: -1 });
    return response;
  }

  export async function getAllAppointmentsInNext18Hours(): Promise<IAppointment[]> {
    const now = new Date();
    const start = new Date(now.getTime() + 18 * 3600000 - 15 * 60000);
    const end = new Date(now.getTime() + 18 * 3600000 + 15 * 60000);
    const response = await Appointment.find({
      $and: [
        {
          start: { $gte: start, $lte: end },
        },
        {
          status: AppointmentStatus.PENDING,
        },
        {
          smsStatus: AppointmentSMSStatus.BEFORE24SMS,
        },
      ],
    })
      .select({ start: 1, color: 1 })
      .populate(populateOptions2)
      .sort({ createdAt: -1 });
    return response;
  }

  export async function getAllAppointmentsInNext12Hours(): Promise<IAppointment[]> {
    const now = new Date();
    const start = new Date(now.getTime() + 12 * 3600000 - 15 * 60000);
    const end = new Date(now.getTime() + 12 * 3600000 + 15 * 60000);
    const response = await Appointment.find({
      $and: [
        {
          start: { $gte: start, $lte: end },
        },
        {
          status: AppointmentStatus.PENDING,
        },
        {
          smsStatus: AppointmentSMSStatus.BEFORE24SMS,
        },
      ],
    })
      .select({ start: 1, color: 1 })
      .populate(populateOptions2)
      .sort({ createdAt: -1 });
    return response;
  }

  export async function getAppointmentsOfTherapistByStartTime(
    startTime: Date,
    endTime: Date,
    duration: number,
    therapistId: Types.ObjectId
  ): Promise<IAppointment[]> {
    const sessionStart1 = moment(startTime).subtract(1, "m").toDate();
    const sessionEnd1 = moment(endTime).subtract(1, "m").toDate();

    const sessionStart2 = moment(startTime).add(1, "m").toDate();
    const sessionEnd2 = moment(endTime).add(1, "m").toDate();

    let response: IAppointment[] = [];

    response = await Appointment.aggregate([
      {
        $match: {
          $and: [
            { therapistId: Types.ObjectId(therapistId.toString()) },
            {
              $or: [
                { start: { $gte: new Date(sessionStart1), $lte: new Date(sessionEnd1) } },
                { end: { $gte: new Date(sessionStart2), $lte: new Date(sessionEnd2) } },
              ],
            },
            { $and: [{ status: { $ne: AppointmentStatus.COMPLETED } }, { status: { $ne: AppointmentStatus.REJECTED } }] },
          ],
        },
      },
    ]);

    return response;
  }

  export async function getAppointmentsOfClientByStartTime(
    startTime: Date,
    endTime: Date,
    duration: number,
    clientId: Types.ObjectId
  ): Promise<IAppointment[]> {
    const sessionStart1 = moment(startTime).subtract(1, "m").toDate();
    const sessionEnd1 = moment(endTime).subtract(1, "m").toDate();

    const sessionStart2 = moment(startTime).add(1, "m").toDate();
    const sessionEnd2 = moment(endTime).add(1, "m").toDate();

    let response: IAppointment[] = [];

    response = await Appointment.aggregate([
      {
        $match: {
          $and: [
            { clientId: Types.ObjectId(clientId.toString()) },
            {
              $or: [
                { start: { $gte: new Date(sessionStart1), $lte: new Date(sessionEnd1) } },
                { end: { $gte: new Date(sessionStart2), $lte: new Date(sessionEnd2) } },
              ],
            },
            { $and: [{ status: { $ne: AppointmentStatus.COMPLETED } }, { status: { $ne: AppointmentStatus.REJECTED } }] },
          ],
        },
      },
    ]);

    return response;
  }

  export async function getWeeklyAppointmentsByGroupId(groupId: string): Promise<IAppointment[]> {
    const response = await Appointment.find({ groupId: groupId }).sort({ start: 1 });
    return response;
  }

  export async function getAppointmentsOfTherapistByDateRange(
    startTime: Date,
    endTime: Date,
    therapistId: Types.ObjectId
  ): Promise<IAppointment[]> {
    const sessionStart1 = moment(startTime).subtract(1, "m").toDate();
    const sessionEnd1 = moment(endTime).subtract(1, "m").toDate();

    const sessionStart2 = moment(startTime).add(1, "m").toDate();
    const sessionEnd2 = moment(endTime).add(1, "m").toDate();

    let response: IAppointment[] = [];

    response = await Appointment.aggregate([
      {
        $match: {
          $and: [
            { therapistId: Types.ObjectId(therapistId.toString()) },
            {
              $or: [
                { start: { $gte: new Date(sessionStart1), $lte: new Date(sessionEnd1) } },
                { end: { $gte: new Date(sessionStart2), $lte: new Date(sessionEnd2) } },
              ],
            },
            { status : {$ne: AppointmentStatus.REJECTED} }
          ],
        },
      },
      {
        $project: {
          _id: 1
        }
      },
    ]);

    return response;
  }

  export async function viewAllAppointmentsByTherapistIdForMonth(
    therapistId: string,
    selectedMonth: Date
  ): Promise<IAppointment[]> {
    const startOfMonth = moment(selectedMonth).startOf("month").toDate();
    const response = await Appointment.find({
      therapistId: Types.ObjectId(therapistId),
      approvedStatus: { $ne: ApprovalStatus.REJECTED },
      status: { $ne: AppointmentStatus.REJECTED },
      start: { $gte: startOfMonth },
    })
      .populate(populateOptions)
      .select({ start: 1, end: 1 });
    return response;
  }
  
}