import { Mongoose, Types } from "mongoose";
import { StringOrObjectId, Util } from "../common/util";
import { IExperienceTag } from "../models/experience-tag-model";
import { IProfession } from "../models/profession-model";
import { Review } from "../models/sub-models/review-model";
import { TransactionType } from "../models/transaction-model";
import {
  DTreatmentHistory,
  ITreatmentHistory,
} from "../models/treatment-history-model";
import { DTherapist, ITherapist } from "../models/therapist-model";
import ExperienceTag from "../schemas/experience-tag-schema";
import Profession from "../schemas/profession-schema";
import Therapist from "../schemas/therapist-schema";
import { UserDao } from "./user-dao";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import { ClientDao } from "./client-dao";
import { IUser, UserStatus } from "../models/user-model";
import MonthlyPayment from "../schemas/monthly-payment-schema";
import {
  DMonthlyPayment,
  IMonthlyPayment,
} from "../models/monthly-payment-model";
import Transaction from "../schemas/transaction-schema";
import { ITransaction } from "../models/transaction-model";
import DiagnosisNote from "../schemas/diagnosis-note-schema";
import User from "../schemas/user-schema";
import Transcribe from "../schemas/transcribe-schema";
import { ITranscribe } from "../models/transcribe-model";
import { startOfMonth, addDays, endOfMonth, isAfter } from 'date-fns';
import Meeting from "../schemas/meeting-schema";
import Client from "../schemas/client-schema";
import Insurance from "../schemas/insurance-schema";
import InsuranceCompany from "../schemas/Insurance-company-schema";
import { AdminDao } from "./admin-dao";
import { AppointmentStatus } from "../models/appointment-model";
import { CallingStatus } from "../models/meeting-model";
import { ITherapistScore } from "../models/therapist-score-model";
import TherapistScoreSchema from "../schemas/therapist-score-schema";
import { ITherapistScoreConstants, DTherapistScoreConstants } from "../models/therapist-score-constants-model";
import TherapistScoreConstants from "../schemas/therapist-score-constants-schema";
import moment = require("moment");
import { FriendRequestStatus, IFriendRequest } from "../models/friend-request-model";
import FriendRequest from "../schemas/friend-request-schema";
import ExperienceTagSymptomsSchema from '../schemas/experience-tag-symptom-schema'
import { DInsuranceDocApproval, IInsuranceDocApproval } from "../models/insurance-doc-approval-model";
import InsuranceDocApproval from "../schemas/insurance-doc-approval-schema";
import AWS = require('aws-sdk');
import { AppLogger } from "../common/logging";


export namespace TherapistDao {
  export async function getUserById(id: StringOrObjectId): Promise<ITherapist> {
    let user: ITherapist = await Therapist.findById(id);

    return user;
  }

  export async function updateTherapist(
    id: StringOrObjectId,
    data: Partial<DTherapist>
  ): Promise<ITherapist> {
    let therapist = await Therapist.findOneAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    )
      .populate([
        { path: "photoId" },
        { path: "experiencedIn" },
        { path: "profession" },
      ])
      .select({ password: 0 });
    return therapist;
  }

  export async function getAllTherapists(
    userId: Types.ObjectId,
    limit: number,
    offset: number
  ): Promise<ITherapist[]> {
    let blockList: Types.ObjectId[] = [];
    const user = await UserDao.getUserByUserId(userId);
    blockList = user.blockedUser;
    let therapistList = await Therapist.find()
      .select({ password: 0 })
      .where("_id")
      .nin(blockList)
      .populate([
        { path: "photoId" },
        { path: "experiencedIn" },
        { path: "profession" },
        {
          path: "licenseId",
          populate: [
            {
              path: "uploadId",
              model: "Upload",
              select: { url: 1, name: 1, title: 1 },
            },
          ],
        },
        {
          path: "qualifications",
          populate: [
            {
              path: "uploadId",
              model: "Upload",
              select: { url: 1, name: 1, title: 1 },
            },
          ],
        },
      ])
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);
    return therapistList;
  }

  export async function getAllVerifiedTherapists(): Promise<any[]> {
    let therapistList = await Therapist.find({
      verifiedStatus: UserStatus.VERIFIED,
      email: {
        $nin: [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ],
      },
      stripeConnectedAccountId: { $exists: true },
    })
      .select({
        recentTransaction: 1,
        stripeConnectedAccountId: 1,
        firstname: 1,
        lastname: 1,
        email: 1,
      })
      .populate([{ path: "recentTransaction" }]);

    return therapistList;
  }


  export async function saveAllVerifiedTherapists1stOfMonth(): Promise<IMonthlyPayment> {
    let therapistList = await Therapist.find({
      verifiedStatus: UserStatus.VERIFIED,
      email: { $nin: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"] },
      stripeConnectedAccountId: { $exists: true },
    }).select({ stripeConnectedAccountId: 1, firstname: 1, lastname: 1, email: 1 });

    const currentDate = new Date();
    const thisMonthStart = startOfMonth(currentDate);
    const thisMonth15th = addDays(thisMonthStart, 14);
    // const lastMonthStart = startOfMonth(addDays(currentDate, -currentDate.getDate()));
    // const lastMonth15th = addDays(lastMonthStart, 14);

    try {
      const listOfTherapists = await Promise.all(therapistList.map(async (therapist) => {
        // const transactions = await Transaction.find({
        //   therapistId: therapist._id,
        //   createdAt: { $gte: thisMonthStart, $lte: thisMonth15th },
        //   type: TransactionType.EARNING,
        //   paidStatus: { $in: [null, "PENDING"] },
        //   $or: [
        //     { eligibleForPayment: { $eq: true } },
        //     { eligibleForPayment: { $exists: false } },
        //   ],
        // }).sort({ createdAt: -1 });

        const transactions = await Transaction.find({
          therapistId: therapist._id,
          type: TransactionType.EARNING,
          paidStatus: { $in: [null, "PENDING"] },
          $and: [
            {
              $or: [
                { paymentReleaseDate: { $gte: thisMonthStart, $lte: thisMonth15th } },
                {
                  paymentReleaseDate: { $exists: false },
                  createdAt: { $gte: thisMonthStart, $lte: thisMonth15th }
                }
              ]
            },
            {
              $or: [
                { eligibleForPayment: { $eq: true } },
                { eligibleForPayment: { $exists: false } }
              ]
            }
          ]
        }).sort({ createdAt: -1 });

        const transactionsWithTreatmentHistories = await (await Promise.all(transactions.map(async (transaction) => {

          const treatmentHistory = await TreatmentHistory.findOne({
            meetingId: transaction.meetingId,
            deleteTreatmentHistory: { $ne: true },
          });
          if (treatmentHistory) {
            return {
              ...transaction.toObject(),
              treatmentHistory: treatmentHistory ? {
                deleteTreatmentHistory: treatmentHistory.deleteTreatmentHistory,
              } : null,
            };
          } else {
            return null;
          }

        }))).filter((result: any) => result !== null);
        return {
          role: therapist.role,
          _id: therapist._id,
          email: therapist.email,
          firstname: therapist.firstname,
          lastname: therapist.lastname,
          stripeConnectedAccountId: therapist.stripeConnectedAccountId,
          recentTransaction: transactionsWithTreatmentHistories.length > 0 ? transactionsWithTreatmentHistories : null,
        };
      }));

      const data = {
        therapistTransaction: listOfTherapists,
        verifiedStatus: "PENDING",
        crownJobType: "1st",
        timePeriodStartAt: thisMonthStart,
        timePeriodEndAt: thisMonth15th,
      };

      const ipayment = new MonthlyPayment(data);

      let response = await ipayment.save();

      return response;
    } catch (error) {
      AppLogger.error(`save-All-Verified-Therapists-1st-Of-Month - Error details: ${error}`);
      throw error;
    }
  }

  //admin function
  export async function saveAllVerifiedTherapists1stOfMonthAdmin(): Promise<IMonthlyPayment> {
    let therapistList = await Therapist.find({
      verifiedStatus: UserStatus.VERIFIED,
      email: { $nin: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"] },
      stripeConnectedAccountId: { $exists: true },
    }).select({ stripeConnectedAccountId: 1, firstname: 1, lastname: 1, email: 1 });

    // const currentDate = new Date();
    // const thisMonthStart = startOfMonth(currentDate);
    // const thisMonth15th = addDays(thisMonthStart, 14);
    // const lastMonthStart = startOfMonth(addDays(currentDate, -currentDate.getDate()));
    // const lastMonth15th = addDays(lastMonthStart, 14);

    const currentDate = new Date();
    const thisMonthStart = startOfMonth(currentDate);
    const thisMonth25th = addDays(thisMonthStart, 25);

    let finalStartDate: Date;
    let finalEndDate: Date;

    if (isAfter(currentDate, thisMonth25th)) {
      finalStartDate = thisMonthStart;
      finalEndDate = addDays(thisMonthStart, 14);
    } else {
      const lastMonthStart = startOfMonth(addDays(thisMonthStart, -1));
      finalStartDate = lastMonthStart;
      finalEndDate = addDays(lastMonthStart, 14);
    }

    try {
      const listOfTherapists = await Promise.all(therapistList.map(async (therapist) => {
        // const transactions = await Transaction.find({
        //   therapistId: therapist._id,
        //   createdAt: { $gte: finalStartDate, $lte: finalEndDate },
        //   type: TransactionType.EARNING,
        //   paidStatus: { $in: [null, "PENDING"] },
        //   $or: [
        //     { eligibleForPayment: { $eq: true } },
        //     { eligibleForPayment: { $exists: false } },
        //   ],
        // }).sort({ createdAt: -1 });

        const transactions = await Transaction.find({
          therapistId: therapist._id,
          type: TransactionType.EARNING,
          paidStatus: { $in: [null, "PENDING"] },
          $and: [
            {
              $or: [
                { paymentReleaseDate: { $gte: finalStartDate, $lte: finalEndDate } },
                {
                  paymentReleaseDate: { $exists: false },
                  createdAt: { $gte: finalStartDate, $lte: finalEndDate }
                }
              ]
            },
            {
              $or: [
                { eligibleForPayment: { $eq: true } },
                { eligibleForPayment: { $exists: false } }
              ]
            }
          ]
        }).sort({ createdAt: -1 });

        const transactionsWithTreatmentHistories = await (await Promise.all(transactions.map(async (transaction) => {

          const treatmentHistory = await TreatmentHistory.findOne({
            meetingId: transaction.meetingId,
            deleteTreatmentHistory: { $ne: true },
          });
          if (treatmentHistory) {
            return {
              ...transaction.toObject(),
              treatmentHistory: treatmentHistory ? {
                deleteTreatmentHistory: treatmentHistory.deleteTreatmentHistory,
              } : null,
            };
          } else {
            return null;
          }

        }))).filter((result: any) => result !== null);
        return {
          role: therapist.role,
          _id: therapist._id,
          email: therapist.email,
          firstname: therapist.firstname,
          lastname: therapist.lastname,
          stripeConnectedAccountId: therapist.stripeConnectedAccountId,
          recentTransaction: transactionsWithTreatmentHistories.length > 0 ? transactionsWithTreatmentHistories : null,
        };
      }));

      const data = {
        therapistTransaction: listOfTherapists,
        verifiedStatus: "PENDING",
        crownJobType: "1st",
        timePeriodStartAt: finalStartDate,
        timePeriodEndAt: finalEndDate,
      };

      const ipayment = new MonthlyPayment(data);

      let response = await ipayment.save();

      return response;
    } catch (error) {
      throw error;
    }
  }


  async function updateMonthlyPaymentByFlag(monthlyPaymentID: string) {
    const monthlyPaymentId = monthlyPaymentID;

    if (!monthlyPaymentId) {
      return;
    }
    try {
      const monthlyPayment = await AdminDao.getMonthlyPaymentById(monthlyPaymentId);
      const getAllInsuranceCompaniesTypesPublic = await AdminDao.getAllInsuranceCompaniesTypesPublic();
      let insuranceCompanies: string[] = [];

      if (getAllInsuranceCompaniesTypesPublic) {
        getAllInsuranceCompaniesTypesPublic.forEach(obj => {
          insuranceCompanies.push(obj.insuranceCompany);
        });
      }
      if (!monthlyPayment) {
        return;
      }
      if (monthlyPayment && monthlyPayment.therapistTransaction) {
        monthlyPayment.therapistTransaction.forEach(transaction => {
          if (transaction.recentTransaction !== null) {
            transaction.recentTransaction?.forEach((recentTransaction: any) => {
              if (recentTransaction && insuranceCompanies && insuranceCompanies.includes(recentTransaction?.insuranceCompany)) {
                recentTransaction.isFlag = true;
              }
            })
          }
        });
      }
      const updateMonthlyPaynemt = await TherapistDao.updateMonthlyPaynemt(monthlyPaymentId, monthlyPayment)
      const updatedMonthlyPayment = await AdminDao.getMonthlyPaymentById(monthlyPaymentId);
      return updatedMonthlyPayment;
    } catch (error) {
      return;
    }
  }

  export async function getAllMonthlyPaynemts(): Promise<any[]> {
    let paymentList = await MonthlyPayment.find({
      verifiedStatus: UserStatus.PENDING,
    }).sort({ createdAt: -1 });
    return paymentList;
  }


  export async function getAllMonthlyPaynemtsLastMonth1stAnd15th(): Promise<IMonthlyPayment> {
    try {
      let therapistList = await Therapist.find({
        verifiedStatus: UserStatus.VERIFIED,
        email: {
          $nin: [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
          ],
        },
        stripeConnectedAccountId: { $exists: true },
      })
        .select({
          stripeConnectedAccountId: 1,
          firstname: 1,
          lastname: 1,
          email: 1,
        });

      const currentDate = new Date();
      const lastMonthStart = startOfMonth(addDays(currentDate, -currentDate.getDate()));
      const lastMonth15th = addDays(lastMonthStart, 14);
      const lastMonthEnd = endOfMonth(lastMonthStart);

      const listOfTherapists = await Promise.all(therapistList.map(async (therapist) => {
        // const transactions = await Transaction.find({
        //   therapistId: therapist._id,
        //   createdAt: {
        //     $gte: lastMonth15th,
        //     $lte: lastMonthEnd
        //   },
        //   type: TransactionType.EARNING,
        //   paidStatus: { $in: [null, "PENDING"] },
        //   $or: [
        //     { eligibleForPayment: { $eq: true } },
        //     { eligibleForPayment: { $exists: false } },
        //   ],
        // }).sort({ createdAt: -1 });

        const transactions = await Transaction.find({
          therapistId: therapist._id,
          type: TransactionType.EARNING,
          paidStatus: { $in: [null, "PENDING"] },
          $and: [
            {
              $or: [
                { paymentReleaseDate: { $gte: lastMonth15th, $lte: lastMonthEnd } },
                {
                  paymentReleaseDate: { $exists: false },
                  createdAt: { $gte: lastMonth15th, $lte: lastMonthEnd }
                }
              ]
            },
            {
              $or: [
                { eligibleForPayment: { $eq: true } },
                { eligibleForPayment: { $exists: false } }
              ]
            }
          ]
        }).sort({ createdAt: -1 });

        const transactionsWithTreatmentHistories = await (await Promise.all(transactions.map(async (transaction) => {

          const treatmentHistory = await TreatmentHistory.findOne({
            meetingId: transaction.meetingId,
            deleteTreatmentHistory: { $ne: true },
          });

          if (treatmentHistory) {
            return {
              ...transaction.toObject(),
              treatmentHistory: treatmentHistory ? {
                deleteTreatmentHistory: treatmentHistory.deleteTreatmentHistory,
              } : null,
            };
          } else {
            return null;
          }

        }))).filter((result: any) => result !== null);
        return {
          role: therapist.role,
          _id: therapist._id,
          email: therapist.email,
          firstname: therapist.firstname,
          lastname: therapist.lastname,
          stripeConnectedAccountId: therapist.stripeConnectedAccountId,
          recentTransaction: transactionsWithTreatmentHistories.length > 0 ? transactionsWithTreatmentHistories : null,
        };
      }));

      const data = {
        therapistTransaction: listOfTherapists,
        verifiedStatus: "PENDING",
        crownJobType: "15th",
        timePeriodStartAt: lastMonth15th,
        timePeriodEndAt: lastMonthEnd,
      };

      const ipayment = new MonthlyPayment(data);

      let response = await ipayment.save();

      return response;
    } catch (error) {
      AppLogger.error(`get-All-Monthly-Paynemts-Last-Month-1st-And-15th - Error details: ${error}`);
      throw error;
    }
  }


  export async function saveAllVerifiedTherapists1stOfMonthByAdmin(): Promise<any> {
    let therapistList = await Therapist.find({
      verifiedStatus: UserStatus.VERIFIED,
      email: { $nin: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"] },
      stripeConnectedAccountId: { $exists: true },
    }).select({ stripeConnectedAccountId: 1, firstname: 1, lastname: 1, email: 1 });

    const currentDate = new Date();
    const thisMonthStart = startOfMonth(currentDate);
    const thisMonth15th = addDays(thisMonthStart, 14);
    // const lastMonthStart = startOfMonth(addDays(currentDate, -currentDate.getDate()));
    // const lastMonth15th = addDays(lastMonthStart, 14);

    try {
      const listOfTherapists = await Promise.all(therapistList.map(async (therapist) => {
        // const transactions = await Transaction.find({
        //   therapistId: therapist._id,
        //   createdAt: { $gte: thisMonthStart, $lte: thisMonth15th },
        //   type: TransactionType.EARNING,
        //   paidStatus: { $in: [null, "PENDING"] },
        //   $or: [
        //     { eligibleForPayment: { $eq: true } },
        //     { eligibleForPayment: { $exists: false } },
        //   ],
        // }).sort({ createdAt: -1 });

        const transactions = await Transaction.find({
          therapistId: therapist._id,
          type: TransactionType.EARNING,
          paidStatus: { $in: [null, "PENDING"] },
          $and: [
            {
              $or: [
                { paymentReleaseDate: { $gte: thisMonthStart, $lte: thisMonth15th } },
                {
                  paymentReleaseDate: { $exists: false },
                  createdAt: { $gte: thisMonthStart, $lte: thisMonth15th }
                }
              ]
            },
            {
              $or: [
                { eligibleForPayment: { $eq: true } },
                { eligibleForPayment: { $exists: false } }
              ]
            }
          ]
        }).sort({ createdAt: -1 });

        const transactionsWithTreatmentHistories = await (await Promise.all(transactions.map(async (transaction) => {

          const treatmentHistory = await TreatmentHistory.findOne({
            meetingId: transaction.meetingId,
            deleteTreatmentHistory: { $ne: true },
          });
          if (treatmentHistory) {
            return {
              ...transaction.toObject(),
              treatmentHistory: treatmentHistory ? {
                deleteTreatmentHistory: treatmentHistory.deleteTreatmentHistory,
              } : null,
            };
          } else {
            return null;
          }

        }))).filter((result: any) => result !== null);
        return {
          recentTransaction: transactionsWithTreatmentHistories.length > 0 ? transactionsWithTreatmentHistories : null,
        };
      }));

      const totalTransactionAmount = listOfTherapists.reduce((totalSum: any, therapist: any) => {
        if (therapist.recentTransaction && Array.isArray(therapist.recentTransaction)) {
          const recentSum = therapist.recentTransaction.reduce((sum: any, transaction: any) => {
            return sum + (transaction.transactionAmount || 0);
          }, 0);
          return totalSum + recentSum;
        }
        return totalSum;
      }, 0);
      return totalTransactionAmount;
    } catch (error) {
      throw error;
    }
  }


  export async function getAllMonthlyPaynemtsLastMonth1stAnd15thByAdmin(): Promise<any> {
    try {
      let therapistList = await Therapist.find({
        verifiedStatus: UserStatus.VERIFIED,
        email: {
          $nin: [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
          ],
        },
        stripeConnectedAccountId: { $exists: true },
      })
        .select({
          stripeConnectedAccountId: 1,
          firstname: 1,
          lastname: 1,
          email: 1,
        });

      const currentDate = new Date();
      const lastMonthStart = startOfMonth(addDays(currentDate, -currentDate.getDate()));
      const lastMonth15th = addDays(lastMonthStart, 14);
      const lastMonthEnd = endOfMonth(lastMonthStart);

      const listOfTherapists = await Promise.all(therapistList.map(async (therapist) => {
        // const transactions = await Transaction.find({
        //   therapistId: therapist._id,
        //   createdAt: {
        //     $gte: lastMonth15th,
        //     $lte: lastMonthEnd
        //   },
        //   type: TransactionType.EARNING,
        //   paidStatus: { $in: [null, "PENDING"] },
        //   $or: [
        //     { eligibleForPayment: { $eq: true } },
        //     { eligibleForPayment: { $exists: false } },
        //   ],
        // }).sort({ createdAt: -1 });

        const transactions = await Transaction.find({
          therapistId: therapist._id,
          type: TransactionType.EARNING,
          paidStatus: { $in: [null, "PENDING"] },
          $and: [
            {
              $or: [
                { paymentReleaseDate: { $gte: lastMonth15th, $lte: lastMonthEnd } },
                {
                  paymentReleaseDate: { $exists: false },
                  createdAt: { $gte: lastMonth15th, $lte: lastMonthEnd }
                }
              ]
            },
            {
              $or: [
                { eligibleForPayment: { $eq: true } },
                { eligibleForPayment: { $exists: false } }
              ]
            }
          ]
        }).sort({ createdAt: -1 });

        const transactionsWithTreatmentHistories = await (await Promise.all(transactions.map(async (transaction) => {

          const treatmentHistory = await TreatmentHistory.findOne({
            meetingId: transaction.meetingId,
            deleteTreatmentHistory: { $ne: true },
          });

          if (treatmentHistory) {
            return {
              ...transaction.toObject(),
              treatmentHistory: treatmentHistory ? {
                deleteTreatmentHistory: treatmentHistory.deleteTreatmentHistory,
              } : null,
            };
          } else {
            return null;
          }

        }))).filter((result: any) => result !== null);
        return {
          recentTransaction: transactionsWithTreatmentHistories.length > 0 ? transactionsWithTreatmentHistories : null,
        };
      }));
      const totalTransactionAmount = listOfTherapists.reduce((totalSum: any, therapist: any) => {
        if (therapist.recentTransaction && Array.isArray(therapist.recentTransaction)) {
          const recentSum = therapist.recentTransaction.reduce((sum: any, transaction: any) => {
            return sum + (transaction.transactionAmount || 0);
          }, 0);
          return totalSum + recentSum;
        }
        return totalSum;
      }, 0);
      return totalTransactionAmount;
    } catch (error) {
      throw error;
    }
  }


  export async function getMonthlyPaynemtsById(
    id: StringOrObjectId
  ): Promise<IMonthlyPayment> {
    const response = await MonthlyPayment.findById(id);
    return response;
  }

  export async function deleteMonthlyPaynemtsById(
    id: StringOrObjectId
  ): Promise<IMonthlyPayment> {
    const response = await MonthlyPayment.findByIdAndDelete(id);
    return response;
  }


  export async function getAllMonthlyPaynemtsWithoutFilter(limit: number,
    offset: number): Promise<any[]> {
    let paymentList = await MonthlyPayment.find().sort({ createdAt: -1 }).skip(limit * (offset - 1))
      .limit(limit)
      .exec();
    await MonthlyPayment.populate(paymentList, {
      path: 'therapistTransaction.recentTransaction._id',
      model: Transaction,
      select: 'createdAt type updatedAt paidStatus',
    }),
      await MonthlyPayment.populate(paymentList, {
        path: 'therapistTransaction.recentTransaction.meetingId',
        model: Meeting,
        select: 'clientId createdAt regularMeetingDate meetingId therapistId',
      }),
      await MonthlyPayment.populate(paymentList, {
        path: 'therapistTransaction.recentTransaction.meetingId.clientId',
        model: Client,
        select: 'firstname lastname insuranceId',
      }),
      await MonthlyPayment.populate(paymentList, {
        path: 'therapistTransaction.recentTransaction.meetingId.therapistId',
        model: Therapist,
        select: 'firstname lastname payRateType',
      }),
      await MonthlyPayment.populate(paymentList, {
        path: 'therapistTransaction.recentTransaction.meetingId.clientId.insuranceId',
        model: Insurance,
        select: 'insuranceCompanyId',
      }),
      await MonthlyPayment.populate(paymentList, {
        path: 'therapistTransaction.recentTransaction.meetingId.clientId.insuranceId.insuranceCompanyId',
        model: InsuranceCompany,
        select: 'insuranceCompany contractPrice',
      })

    return paymentList;
  }


  export async function updateMonthlyPaynemt(
    id: StringOrObjectId,
    data: Partial<DMonthlyPayment>
  ): Promise<IMonthlyPayment> {
    let therapist = await MonthlyPayment.findOneAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    );
    return therapist;
  }
  export async function getAllMonthlyPaynemtsById(
    paymentId: string
  ): Promise<IMonthlyPayment | null> {
    let pay: IMonthlyPayment = await MonthlyPayment.findOne({ _id: paymentId });
    return pay;
  }

  export async function searchTherapistsPublic(
    gender: string,
    ethnicity: string,
    insuranceCompanies: string[],
    profession: string,
    experiencedIn: string[],
    searchTherapistName: string,
    state: string,
    limit: number,
    offset: number,
    symptoms: string[]
  ): Promise<ITherapist[]> {

    const genderQuery = gender ? { gender } : {};
    const ethnicityQuery = ethnicity ? { ethnicityId: Types.ObjectId(ethnicity) } : {};
    const professionQuery = profession ? { profession: Types.ObjectId(profession) } : {};
    let insuranceCompanyObjectArray: Types.ObjectId[] = [];
    if (insuranceCompanies && insuranceCompanies.length) {
      insuranceCompanyObjectArray = insuranceCompanies
        .filter(eI => Util.isObjectId(eI) && eI !== "")
        .map(eI => Types.ObjectId(eI));
    }

    const insuranceCompanyQuery = insuranceCompanyObjectArray.length
      ? { insuranceCompanies: { $in: insuranceCompanyObjectArray } }
      : {};
    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false }
    };
    let experiencedInObjectArray: Types.ObjectId[] = [];
    if (experiencedIn && experiencedIn.length) {
      experiencedInObjectArray = experiencedIn
        .filter(eI => Util.isObjectId(eI) && eI !== "")
        .map(eI => Types.ObjectId(eI));
    }
    const experiencedInQuery = experiencedInObjectArray.length
      ? { experiencedIn: { $in: experiencedInObjectArray } }
      : {};
    let searchedName = null;
    if (searchTherapistName) {
      let searchItem = searchTherapistName.replace(/\s/g, "");
      searchedName = new RegExp(`^${searchItem}`, "i");
    }
    const therapistNameQuery = searchedName
      ? {
        $or: [
          { firstname: searchedName },
          { lastname: searchedName },
          { fullName: searchedName }
        ]
      }
      : {};

    const mainExperienceTagsFromSymptoms = await ExperienceTagSymptomsSchema.aggregate([
        {
          $match: {
            _id: { $in: symptoms.map(s => Types.ObjectId(s)) }
          }
        },
        {
          $unwind: "$experienceMainTags"
        },
        {
          $group: {
            _id: null,
            experienceMainTags: { $addToSet: "$experienceMainTags" }
          }
        }
      ]);

    const mainExperienceTagIds = mainExperienceTagsFromSymptoms[0]?.experienceMainTags || [];

    const experienceTags = await ExperienceTag.aggregate([
      {
        $match: {
          experienceMainTags: { $in: mainExperienceTagIds }
        }
      },
      {
        $project: { _id: 1 }
      }
    ]);

    const experienceTagIds = experienceTags.length> 0 ? experienceTags.map(et => Types.ObjectId(et._id)): [];


    const searchResults: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: { $concat: ["$firstname", "$lastname"] },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          profession: 1,
          experiencedIn: 1,
          insuranceCompanies: 1,
          ethnicityId: 1,
          dislikedClients: 1,
          blockedByAdmin: 1,
          adminApproved: 1,
          therapyState: 1,
          state: 1,
          _id: 1,
          lavniTestAccount: 1,
          vimeoId: 1,
          priorityNumber: 1,
        }
      },
      {
        $addFields: {
          statePriority: {
            $cond: {
              if: {
                $and: [
                  { $isArray: "$therapyState" },
                  { $in: [state.trim(), "$therapyState"] }
                ]
              },
              then: 1,
              else: 2
              // then: {
              //   $cond: {
              //     if: { $gte: ["$priorityNumber", 1] },
              //     then: 1,
              //     else: 2
              //   }
              // },
              // else: {
              //   $cond: {
              //     if: { $gte: ["$priorityNumber", 1] },
              //     then: 3,
              //     else: 4
              //   }
              // }
            }
          }
        }
      },
      // {
      //   $lookup: {
      //     from: 'experiencetags',
      //     localField: 'experiencedIn',
      //     foreignField: '_id',
      //     as: 'experienceTags'
      //   }
      // },
      // {
      //   $addFields: {
      //     experiencedInPriority: {
      //       $cond: {
      //         if: {
      //           $gt: [
      //             {
      //               $size: {
      //                 $filter: {
      //                   input: "$experienceTags",
      //                   as: "tag",
      //                   cond: {
      //                     $in: [Types.ObjectId(mainExperienceTag), { $ifNull: ["$$tag.experienceMainTags", []] }]
      //                   }
      //                 }
      //               }
      //             },
      //             0
      //           ]
      //         },
      //         then: 1,
      //         else: 2
      //       }
      //     }
      //   }
      // },
      // {
      //   $unset: 'experienceTags'
      // },
      {
        $addFields: {
          experiencedInPriority: {
            $cond: {
              if: {
                $gt: [
                  {
                    $size: {
                      $filter: {
                        input: { $ifNull: ["$experiencedIn", []] }, // Therapist's experience tags array
                        as: "tag",
                        cond: {
                          $in: ["$$tag", experienceTagIds] // Check if each tag is in the required experienceTagIds array
                        }
                      }
                    }
                  },
                  0
                ]
              },
              then: 1,
              else: 2
            }
          }
        }
      },
      {
        $sort: { statePriority: 1, experiencedInPriority: 1,  priorityNumber: 1 }
      },
      {
        $match: {
          $and: [
            therapistNameQuery,
            genderQuery,
            ethnicityQuery,
            insuranceCompanyQuery,
            professionQuery,
            removeInvalidTherapistsQuery,
            experiencedInQuery,
          ],
          lavniTestAccount: { $ne: true },
        }
      },
      {
        $lookup: {
          from: "friendrequests",
          localField: "_id",
          foreignField: "therapistId",
          as: "friendRequests",
        },
      },
      {
        $unwind: {
          path: "$friendRequests.clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [{ $project: { isUrl: 1, path: 1, originalName: 1, url: 1 } }],
          as: "photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "coverPhotoId",
          foreignField: "_id",
          pipeline: [{ $project: { isUrl: 1, path: 1, originalName: 1, url: 1 } }],
          as: "coverPhotoId",
        },
      },
      // {
      //   $lookup: {
      //     from: "experiencetags",
      //     localField: "experiencedIn",
      //     foreignField: "_id",
      //     pipeline: [
      //       {
      //         $lookup: {
      //           from: "experiencetagsymptoms",
      //           localField: "experienceMainTags", // Field in experiencetags
      //           foreignField: "experienceMainTags", // Field in experiencetagsymptoms
      //           as: "symptoms"
      //         }
      //       },
      //       {
      //         $unwind: "$symptoms" // Unwind the symptoms array to process each symptom individually
      //       },
      //       {
      //         $group: {
      //           _id: "$symptoms._id", // Group by symptom ID
      //           symptom: { $first: "$symptoms.symptomName" } // Keep the symptom name
      //         }
      //       },
      //       {
      //         $project: {
      //           _id: 1,
      //           symptom: 1 // Only return the unique symptoms
      //         }
      //       }
      //     ],
      //     as: "experiencedIn",
      //   },
      // },
      {
        $lookup: {
          from: "experiencetags",
          localField: "experiencedIn",
          foreignField: "_id",
          pipeline: [
            {
              $project: { experienceTag: 1, experienceMainTags: 1 }
            }
          ],
          as: "experiencedIn",
        },
      },

      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);
    const startIndex = (offset - 1) * limit;
    const endIndex = startIndex + limit;
    const limitedResults = searchResults.slice(startIndex, endIndex);
    return limitedResults;
  }

  export async function searchBlackTherapistsPublic(
    gender: string,
    ethnicity: string,
    insuranceCompanies: string[],
    profession: string,
    experiencedIn: string[],
    searchTherapistName: string,
    state: string,
    limit: number,
    offset: number
  ): Promise<ITherapist[]> {
    const genderQuery = gender ? { gender } : {};
    const ethnicityQuery = ethnicity ? { ethnicityId: Types.ObjectId(ethnicity) } : {};
    const professionQuery = profession ? { profession: Types.ObjectId(profession) } : {};
    let insuranceCompanyObjectArray: Types.ObjectId[] = [];
    if (insuranceCompanies && insuranceCompanies.length) {
      insuranceCompanyObjectArray = insuranceCompanies
        .filter(eI => Util.isObjectId(eI) && eI !== "")
        .map(eI => Types.ObjectId(eI));
    }
    const insuranceCompanyQuery = insuranceCompanyObjectArray.length
      ? { insuranceCompanies: { $in: insuranceCompanyObjectArray } }
      : {};
    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false }
    };
    let experiencedInObjectArray: Types.ObjectId[] = [];
    if (experiencedIn && experiencedIn.length) {
      experiencedInObjectArray = experiencedIn
        .filter(eI => Util.isObjectId(eI) && eI !== "")
        .map(eI => Types.ObjectId(eI));
    }
    const experiencedInQuery = experiencedInObjectArray.length
      ? { experiencedIn: { $in: experiencedInObjectArray } }
      : {};
    let searchedName = null;
    if (searchTherapistName) {
      let searchItem = searchTherapistName.replace(/\s/g, "");
      searchedName = new RegExp(`^${searchItem}`, "i");
    }
    const therapistNameQuery = searchedName
      ? {
        $or: [
          { firstname: searchedName },
          { lastname: searchedName },
          { fullName: searchedName }
        ]
      }
      : {};
    const searchResults: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: { $concat: ["$firstname", "$lastname"] },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          profession: 1,
          experiencedIn: 1,
          insuranceCompanies: 1,
          ethnicityId: 1,
          dislikedClients: 1,
          blockedByAdmin: 1,
          adminApproved: 1,
          therapyState: 1,
          state: 1,
          _id: 1,
          lavniTestAccount: 1,
          vimeoId: 1,
          priorityNumber: 1,
          blackTherapyPriorityNumber: 1,
        }
      },
      {
        $addFields: {
          priority: {
            $cond: {
              if: {
                $and: [
                  { $isArray: "$therapyState" },
                  { $in: [state.trim(), "$therapyState"] }
                ]
              },
              then: {
                $cond: {
                  if: { $gte: ["$blackTherapyPriorityNumber", 1] },
                  then: 1,
                  else: 2
                }
              },
              else: {
                $cond: {
                  if: { $gte: ["$blackTherapyPriorityNumber", 1] },
                  then: 3,
                  else: 4
                }
              }
            }
          }
        }
      },
      {
        $sort: { priority: 1, blackTherapyPriorityNumber: 1 }
      },
      {
        $match: {
          $and: [
            therapistNameQuery,
            genderQuery,
            ethnicityQuery,
            insuranceCompanyQuery,
            professionQuery,
            removeInvalidTherapistsQuery,
            experiencedInQuery
          ],
          lavniTestAccount: { $ne: true },
        }
      },
      {
        $lookup: {
          from: "friendrequests",
          localField: "_id",
          foreignField: "therapistId",
          as: "friendRequests",
        },
      },
      {
        $unwind: {
          path: "$friendRequests.clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [{ $project: { isUrl: 1, path: 1, originalName: 1, url: 1 } }],
          as: "photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "coverPhotoId",
          foreignField: "_id",
          pipeline: [{ $project: { isUrl: 1, path: 1, originalName: 1, url: 1 } }],
          as: "coverPhotoId",
        },
      },
      {
        $lookup: {
          from: "experiencetags",
          localField: "experiencedIn",
          foreignField: "_id",
          pipeline: [{ $project: { experienceTag: 1 } }],
          as: "experiencedIn",
        },
      },
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);
    const startIndex = (offset - 1) * limit;
    const endIndex = startIndex + limit;
    const limitedResults = searchResults.slice(startIndex, endIndex);
    return limitedResults;
  }

  export async function searchMensTherapistsPublic(
    gender: string,
    ethnicity: string,
    insuranceCompanies: string[],
    profession: string,
    experiencedIn: string[],
    searchTherapistName: string,
    state: string,
    limit: number,
    offset: number
  ): Promise<ITherapist[]> {
    const genderQuery = gender ? { gender } : {};
    const ethnicityQuery = ethnicity ? { ethnicityId: Types.ObjectId(ethnicity) } : {};
    const professionQuery = profession ? { profession: Types.ObjectId(profession) } : {};
    let insuranceCompanyObjectArray: Types.ObjectId[] = [];
    if (insuranceCompanies && insuranceCompanies.length) {
      insuranceCompanyObjectArray = insuranceCompanies
        .filter(eI => Util.isObjectId(eI) && eI !== "")
        .map(eI => Types.ObjectId(eI));
    }
    const insuranceCompanyQuery = insuranceCompanyObjectArray.length
      ? { insuranceCompanies: { $in: insuranceCompanyObjectArray } }
      : {};
    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false }
    };
    let experiencedInObjectArray: Types.ObjectId[] = [];
    if (experiencedIn && experiencedIn.length) {
      experiencedInObjectArray = experiencedIn
        .filter(eI => Util.isObjectId(eI) && eI !== "")
        .map(eI => Types.ObjectId(eI));
    }
    const experiencedInQuery = experiencedInObjectArray.length
      ? { experiencedIn: { $in: experiencedInObjectArray } }
      : {};
    let searchedName = null;
    if (searchTherapistName) {
      let searchItem = searchTherapistName.replace(/\s/g, "");
      searchedName = new RegExp(`^${searchItem}`, "i");
    }
    const therapistNameQuery = searchedName
      ? {
        $or: [
          { firstname: searchedName },
          { lastname: searchedName },
          { fullName: searchedName }
        ]
      }
      : {};
    const searchResults: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: { $concat: ["$firstname", "$lastname"] },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          profession: 1,
          experiencedIn: 1,
          insuranceCompanies: 1,
          ethnicityId: 1,
          dislikedClients: 1,
          blockedByAdmin: 1,
          adminApproved: 1,
          therapyState: 1,
          state: 1,
          _id: 1,
          lavniTestAccount: 1,
          vimeoId: 1,
          mensMentalTherapyPriorityNumber: 1,
        }
      },
      {
        $addFields: {
          priority: {
            $cond: {
              if: {
                $and: [
                  { $isArray: "$therapyState" },
                  { $in: [state.trim(), "$therapyState"] }
                ]
              },
              then: {
                $cond: {
                  if: { $gte: ["$mensMentalTherapyPriorityNumber", 1] },
                  then: 1,
                  else: 2
                }
              },
              else: {
                $cond: {
                  if: { $gte: ["$mensMentalTherapyPriorityNumber", 1] },
                  then: 3,
                  else: 4
                }
              }
            }
          }
        }
      },
      {
        $sort: { priority: 1, mensMentalTherapyPriorityNumber: 1 }
      },
      {
        $match: {
          $and: [
            therapistNameQuery,
            genderQuery,
            ethnicityQuery,
            insuranceCompanyQuery,
            professionQuery,
            removeInvalidTherapistsQuery,
            experiencedInQuery
          ],
          lavniTestAccount: { $ne: true },
        }
      },
      {
        $lookup: {
          from: "friendrequests",
          localField: "_id",
          foreignField: "therapistId",
          as: "friendRequests",
        },
      },
      {
        $unwind: {
          path: "$friendRequests.clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [{ $project: { isUrl: 1, path: 1, originalName: 1, url: 1 } }],
          as: "photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "coverPhotoId",
          foreignField: "_id",
          pipeline: [{ $project: { isUrl: 1, path: 1, originalName: 1, url: 1 } }],
          as: "coverPhotoId",
        },
      },
      {
        $lookup: {
          from: "experiencetags",
          localField: "experiencedIn",
          foreignField: "_id",
          pipeline: [{ $project: { experienceTag: 1 } }],
          as: "experiencedIn",
        },
      },
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);
    const startIndex = (offset - 1) * limit;
    const endIndex = startIndex + limit;
    const limitedResults = searchResults.slice(startIndex, endIndex);
    return limitedResults;
  }

  export async function searchRelationshipTherapistsPublic(
    gender: string,
    ethnicity: string,
    insuranceCompanies: string[],
    profession: string,
    experiencedIn: string[],
    searchTherapistName: string,
    state: string,
    limit: number,
    offset: number
  ): Promise<ITherapist[]> {
    const genderQuery = gender ? { gender } : {};
    const ethnicityQuery = ethnicity ? { ethnicityId: Types.ObjectId(ethnicity) } : {};
    const professionQuery = profession ? { profession: Types.ObjectId(profession) } : {};
    let insuranceCompanyObjectArray: Types.ObjectId[] = [];
    if (insuranceCompanies && insuranceCompanies.length) {
      insuranceCompanyObjectArray = insuranceCompanies
        .filter(eI => Util.isObjectId(eI) && eI !== "")
        .map(eI => Types.ObjectId(eI));
    }
    const insuranceCompanyQuery = insuranceCompanyObjectArray.length
      ? { insuranceCompanies: { $in: insuranceCompanyObjectArray } }
      : {};
    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false }
    };
    let experiencedInObjectArray: Types.ObjectId[] = [];
    if (experiencedIn && experiencedIn.length) {
      experiencedInObjectArray = experiencedIn
        .filter(eI => Util.isObjectId(eI) && eI !== "")
        .map(eI => Types.ObjectId(eI));
    }
    const experiencedInQuery = experiencedInObjectArray.length
      ? { experiencedIn: { $in: experiencedInObjectArray } }
      : {};
    let searchedName = null;
    if (searchTherapistName) {
      let searchItem = searchTherapistName.replace(/\s/g, "");
      searchedName = new RegExp(`^${searchItem}`, "i");
    }
    const therapistNameQuery = searchedName
      ? {
        $or: [
          { firstname: searchedName },
          { lastname: searchedName },
          { fullName: searchedName }
        ]
      }
      : {};
    const searchResults: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: { $concat: ["$firstname", "$lastname"] },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          role: 1,
          gender: 1,
          profession: 1,
          experiencedIn: 1,
          insuranceCompanies: 1,
          ethnicityId: 1,
          dislikedClients: 1,
          blockedByAdmin: 1,
          adminApproved: 1,
          therapyState: 1,
          state: 1,
          _id: 1,
          lavniTestAccount: 1,
          vimeoId: 1,
          priorityNumber: 1,
          relationshipTherapyPriorityNumber: 1,
        }
      },
      {
        $addFields: {
          priority: {
            $cond: {
              if: {
                $and: [
                  { $isArray: "$therapyState" },
                  { $in: [state.trim(), "$therapyState"] }
                ]
              },
              then: {
                $cond: {
                  if: { $gte: ["$relationshipTherapyPriorityNumber", 1] },
                  then: 1,
                  else: 2
                }
              },
              else: {
                $cond: {
                  if: { $gte: ["$relationshipTherapyPriorityNumber", 1] },
                  then: 3,
                  else: 4
                }
              }
            }
          }
        }
      },
      {
        $sort: { priority: 1, relationshipTherapyPriorityNumber: 1 }
      },
      {
        $match: {
          $and: [
            therapistNameQuery,
            genderQuery,
            ethnicityQuery,
            insuranceCompanyQuery,
            professionQuery,
            removeInvalidTherapistsQuery,
            experiencedInQuery
          ],
          lavniTestAccount: { $ne: true },
        }
      },
      {
        $lookup: {
          from: "friendrequests",
          localField: "_id",
          foreignField: "therapistId",
          as: "friendRequests",
        },
      },
      {
        $unwind: {
          path: "$friendRequests.clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [{ $project: { isUrl: 1, path: 1, originalName: 1, url: 1 } }],
          as: "photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "coverPhotoId",
          foreignField: "_id",
          pipeline: [{ $project: { isUrl: 1, path: 1, originalName: 1, url: 1 } }],
          as: "coverPhotoId",
        },
      },
      {
        $lookup: {
          from: "experiencetags",
          localField: "experiencedIn",
          foreignField: "_id",
          pipeline: [{ $project: { experienceTag: 1 } }],
          as: "experiencedIn",
        },
      },
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);
    const startIndex = (offset - 1) * limit;
    const endIndex = startIndex + limit;
    const limitedResults = searchResults.slice(startIndex, endIndex);
    return limitedResults;
  }

  export async function searchTherapistsPublicCouple(
    gender: string,
    ethnicity: string,
    insuranceCompanies: string[],
    profession: string,
    experiencedIn: string[],
    searchTherapistName: string,
    limit: number,
    offset: number
  ): Promise<ITherapist[]> {
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const ethnicityQuery =
      ethnicity != null && ethnicity
        ? { ethnicityId: Types.ObjectId(ethnicity) }
        : {};

    let insuranceCompanyObjectArray: Types.ObjectId[] = [];

    if (insuranceCompanies && insuranceCompanies != null) {
      for (let eI of insuranceCompanies) {
        if (Util.isObjectId(eI) && eI !== "") {
          insuranceCompanyObjectArray.push(Types.ObjectId(eI));
        }
      }
    }

    const insuranceCompanyQuery =
      insuranceCompanyObjectArray !== null &&
        insuranceCompanyObjectArray.length !== 0
        ? { insuranceCompanies: { $in: insuranceCompanyObjectArray } }
        : {};

    const professionQuery =
      profession != null && profession
        ? { profession: Types.ObjectId(profession) }
        : {};

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let experiencedInObjectArray: Types.ObjectId[] = [];

    if (experiencedIn && experiencedIn != null) {
      for (let eI of experiencedIn) {
        if (Util.isObjectId(eI) && eI !== "") {
          experiencedInObjectArray.push(Types.ObjectId(eI));
        }
      }
    }

    const experiencedInQuery =
      experiencedInObjectArray !== null && experiencedInObjectArray.length !== 0
        ? { experiencedIn: { $in: experiencedInObjectArray } }
        : {};

    let searchedName = null;

    if (searchTherapistName) {
      let searchItem = searchTherapistName.replace(/\s/g, "");
      searchedName =
        searchTherapistName != null ? new RegExp(`^${searchItem}`, "i") : null;
    }

    const therapistNameQuery =
      searchedName != null && searchedName
        ? {
          $or: [
            { firstname: searchedName },
            { lastname: searchedName },
            { fullName: searchedName },
          ],
        }
        : {};

    const experienceTagPriority = [
      "Marriage Therapy",
      "Partners",
      "Couples",
      "Love",
      "Relationships",
    ];

    const startIndex = (offset - 1) * limit;
    let searchResults: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          coverPhotoId: 1,
          role: 1,
          gender: 1,
          profession: 1,
          experiencedIn: 1,
          insuranceCompanies: 1,
          ethnicityId: 1,
          dislikedClients: 1,
          blockedByAdmin: 1,
          adminApproved: 1,
          zipCode: 1,
          state: 1,
          _id: 1,
          lavniTestAccount: 1,
          vimeoId: 1,
        },
      },
      {
        $match: {
          $and: [
            therapistNameQuery,
            genderQuery,
            ethnicityQuery,
            insuranceCompanyQuery,
            professionQuery,
            removeInvalidTherapistsQuery,
            experiencedInQuery,
          ],
          lavniTestAccount: { $ne: true },
        },
      },
      {
        $lookup: {
          from: "friendrequests",
          localField: "_id",
          foreignField: "therapistId",
          as: "friendRequests",
        },
      },
      {
        $unwind: {
          path: "$friendRequests.clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "coverPhotoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "coverPhotoId",
        },
      },
      {
        $lookup: {
          from: "experiencetags",
          localField: "experiencedIn",
          foreignField: "_id",
          pipeline: [
            {
              $project: { experienceTag: 1 },
            },
          ],
          as: "experiencedIn",
        },
      },
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          priority: {
            $cond: {
              if: {
                $or: experienceTagPriority.map((tag) => {
                  return { $in: [tag, "$experiencedIn.experienceTag"] };
                }),
              },
              then: 1,
              else: 0,
            },
          },
        },
      },
      {
        $sort: { priority: -1 },
      },
      {
        $skip: startIndex,
      },
      {
        $limit: limit,
      },
    ]);

    return searchResults;
  }

  // export async function searchTherapistsPublicCouple(
  //   gender: string,
  //   ethnicity: string,
  //   insuranceCompanies: string[],
  //   profession: string,
  //   experiencedIn: string[],
  //   searchTherapistName: string,
  //   limit: number,
  //   offset: number
  // ): Promise<ITherapist[]> {
  //   const genderQuery = gender != null && gender ? { gender: gender } : {};
  //   const ethnicityQuery =
  //     ethnicity != null && ethnicity
  //       ? { ethnicityId: Types.ObjectId(ethnicity) }
  //       : {};

  //   let insuranceCompanyObjectArray: Types.ObjectId[] = [];

  //   if (insuranceCompanies && insuranceCompanies != null) {
  //     for (let eI of insuranceCompanies) {
  //       if (Util.isObjectId(eI) && eI !== "") {
  //         insuranceCompanyObjectArray.push(Types.ObjectId(eI));
  //       }
  //     }
  //   }

  //   const insuranceCompanyQuery =
  //     insuranceCompanyObjectArray !== null && insuranceCompanyObjectArray.length !== 0
  //       ? { insuranceCompanies: { $in: insuranceCompanyObjectArray } }
  //       : {};

  //   const professionQuery =
  //     profession != null && profession
  //       ? { profession: Types.ObjectId(profession) }
  //       : {};

  //   const removeInvalidTherapistsQuery = {
  //     blockedByAdmin: { $ne: true },
  //     adminApproved: { $ne: false },
  //   };

  //   let experiencedInObjectArray: Types.ObjectId[] = [];

  //   if (experiencedIn && experiencedIn != null) {
  //     for (let eI of experiencedIn) {
  //       if (Util.isObjectId(eI) && eI !== "") {
  //         experiencedInObjectArray.push(Types.ObjectId(eI));
  //       }
  //     }
  //   }

  //   const experiencedInQuery =
  //     experiencedInObjectArray !== null && experiencedInObjectArray.length !== 0
  //       ? { experiencedIn: { $in: experiencedInObjectArray } }
  //       : {};

  //   let searchedName = null;

  //   if (searchTherapistName) {
  //     let seacrhItem = searchTherapistName.replace(/\s/g, "");
  //     searchedName =
  //       searchTherapistName != null ? new RegExp(`^${seacrhItem}`, "i") : null;
  //   }

  //   const therapistNameQuery =
  //     searchedName != null && searchedName
  //       ? {
  //         $or: [
  //           { firstname: searchedName },
  //           { lastname: searchedName },
  //           { fullName: searchedName },
  //         ],
  //       }
  //       : {};

  //   const experienceTagPriority = [
  //     "Somatic Symptom Disorder",
  //     "Trauma-Related Disorders",
  //     "Tobacco Use Disorder",
  //     "Somatic Disorders",
  //   ];

  //   let searchResults: ITherapist[] = await Therapist.aggregate([
  //     {
  //       $project: {
  //         fullName: {
  //           $concat: ["$firstname", "$lastname"],
  //         },
  //         firstname: 1,
  //         lastname: 1,
  //         email: 1,
  //         createdAt: 1,
  //         photoId: 1,
  //         coverPhotoId: 1,
  //         role: 1,
  //         gender: 1,
  //         profession: 1,
  //         experiencedIn: 1,
  //         insuranceCompanies: 1,
  //         ethnicityId: 1,
  //         dislikedClients: 1,
  //         blockedByAdmin: 1,
  //         adminApproved: 1,
  //         zipCode: 1,
  //         state: 1,
  //         _id: 1,
  //         lavniTestAccount: 1,
  //         vimeoId: 1,
  //       },
  //     },
  //     {
  //       $match: {
  //         $and: [
  //           therapistNameQuery,
  //           genderQuery,
  //           ethnicityQuery,
  //           insuranceCompanyQuery,
  //           professionQuery,
  //           removeInvalidTherapistsQuery,
  //           experiencedInQuery
  //         ],
  //         lavniTestAccount: { $ne: true }
  //       },
  //     },
  //     {
  //       $lookup: {
  //         from: "friendrequests",
  //         localField: "_id",
  //         foreignField: "therapistId",
  //         as: "friendRequests",
  //       },
  //     },
  //     {
  //       $unwind: {
  //         path: "$friendRequests.clientId",
  //         preserveNullAndEmptyArrays: true,
  //       },
  //     },
  //     {
  //       $lookup: {
  //         from: "uploads",
  //         localField: "photoId",
  //         foreignField: "_id",
  //         pipeline: [
  //           {
  //             $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
  //           },
  //         ],
  //         as: "photoId",
  //       },
  //     },
  //     {
  //       $lookup: {
  //         from: "uploads",
  //         localField: "coverPhotoId",
  //         foreignField: "_id",
  //         pipeline: [
  //           {
  //             $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
  //           },
  //         ],
  //         as: "coverPhotoId",
  //       },
  //     },
  //     {
  //       $lookup: {
  //         from: "experiencetags",
  //         localField: "experiencedIn",
  //         foreignField: "_id",
  //         pipeline: [
  //           {
  //             $project: { experienceTag: 1 },
  //           },
  //         ],
  //         as: "experiencedIn",
  //       },
  //     },
  //     {
  //       $unwind: {
  //         path: "$profession",
  //         preserveNullAndEmptyArrays: true,
  //       },
  //     },
  //     {
  //       $unwind: {
  //         path: "$coverPhotoId",
  //         preserveNullAndEmptyArrays: true,
  //       },
  //     },
  //     {
  //       $unwind: {
  //         path: "$photoId",
  //         preserveNullAndEmptyArrays: true,
  //       },
  //     },

  //   ]);

  //   const startIndex = (offset - 1) * limit;
  //   const endIndex = startIndex + limit;
  //   const limitedResults = searchResults.slice(startIndex, endIndex);

  //   return limitedResults;
  // }

  export async function searchTherapistsPublicTeen(
    gender: string,
    ethnicity: string,
    insuranceCompanies: string[],
    profession: string,
    experiencedIn: string[],
    searchTherapistName: string,
    limit: number,
    offset: number
  ): Promise<ITherapist[]> {
    const genderQuery = gender != null && gender ? { gender: gender } : {};
    const ethnicityQuery =
      ethnicity != null && ethnicity
        ? { ethnicityId: Types.ObjectId(ethnicity) }
        : {};

    let insuranceCompanyObjectArray: Types.ObjectId[] = [];

    if (insuranceCompanies && insuranceCompanies != null) {
      for (let eI of insuranceCompanies) {
        if (Util.isObjectId(eI) && eI !== "") {
          insuranceCompanyObjectArray.push(Types.ObjectId(eI));
        }
      }
    }

    const insuranceCompanyQuery =
      insuranceCompanyObjectArray !== null &&
        insuranceCompanyObjectArray.length !== 0
        ? { insuranceCompanies: { $in: insuranceCompanyObjectArray } }
        : {};

    const professionQuery =
      profession != null && profession
        ? { profession: Types.ObjectId(profession) }
        : {};

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let experiencedInObjectArray: Types.ObjectId[] = [];

    if (experiencedIn && experiencedIn != null) {
      for (let eI of experiencedIn) {
        if (Util.isObjectId(eI) && eI !== "") {
          experiencedInObjectArray.push(Types.ObjectId(eI));
        }
      }
    }

    const experiencedInQuery =
      experiencedInObjectArray !== null && experiencedInObjectArray.length !== 0
        ? { experiencedIn: { $in: experiencedInObjectArray } }
        : {};

    let searchedName = null;

    if (searchTherapistName) {
      let seacrhItem = searchTherapistName.replace(/\s/g, "");
      searchedName =
        searchTherapistName != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const therapistNameQuery =
      searchedName != null && searchedName
        ? {
          $or: [
            { firstname: searchedName },
            { lastname: searchedName },
            { fullName: searchedName },
          ],
        }
        : {};

    let searchResults: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          coverPhotoId: 1,
          role: 1,
          gender: 1,
          profession: 1,
          experiencedIn: 1,
          insuranceCompanies: 1,
          ethnicityId: 1,
          dislikedClients: 1,
          blockedByAdmin: 1,
          adminApproved: 1,
          zipCode: 1,
          state: 1,
          _id: 1,
          lavniTestAccount: 1,
          vimeoId: 1,
        },
      },
      {
        $match: {
          $and: [
            therapistNameQuery,
            genderQuery,
            ethnicityQuery,
            insuranceCompanyQuery,
            professionQuery,
            removeInvalidTherapistsQuery,
            experiencedInQuery,
          ],
          lavniTestAccount: { $ne: true },
        },
      },
      {
        $lookup: {
          from: "friendrequests",
          localField: "_id",
          foreignField: "therapistId",
          as: "friendRequests",
        },
      },
      {
        $unwind: {
          path: "$friendRequests.clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "coverPhotoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "coverPhotoId",
        },
      },
      {
        $lookup: {
          from: "experiencetags",
          localField: "experiencedIn",
          foreignField: "_id",
          pipeline: [
            {
              $project: { experienceTag: 1 },
            },
          ],
          as: "experiencedIn",
        },
      },
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);

    const emailPriority = [
      "<EMAIL>",
      "<EMAIL>",
    ];
    searchResults.sort((a, b) => {
      const indexA = emailPriority.indexOf(a.email);
      const indexB = emailPriority.indexOf(b.email);
      if (a.email == "<EMAIL>") {
        return 1;
      }

      if (b.email == "<EMAIL>") {
        return -1;
      }

      if (indexA === -1 && indexB === -1) {
        return 0;
      } else if (indexA === -1) {
        return 1;
      } else if (indexB === -1) {
        return -1;
      } else {
        return indexA - indexB;
      }
    });
    const startIndex = (offset - 1) * limit;
    const endIndex = startIndex + limit;
    const limitedResults = searchResults.slice(startIndex, endIndex);

    return limitedResults;
  }

  export async function allTherapistsPublic(): Promise<ITherapist[]> {
    let insuranceCompanyObjectArray: Types.ObjectId[] = [];
    const insuranceCompanyQuery =
      insuranceCompanyObjectArray !== null &&
        insuranceCompanyObjectArray.length !== 0
        ? { insuranceCompanies: { $in: insuranceCompanyObjectArray } }
        : {};

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let experiencedInObjectArray: Types.ObjectId[] = [];

    const experiencedInQuery =
      experiencedInObjectArray !== null && experiencedInObjectArray.length !== 0
        ? { experiencedIn: { $in: experiencedInObjectArray } }
        : {};

    let searchedName = null;

    const therapistNameQuery =
      searchedName != null && searchedName
        ? {
          $or: [
            { firstname: searchedName },
            { lastname: searchedName },
            { fullName: searchedName },
          ],
        }
        : {};

    let searchResults: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,
          coverPhotoId: 1,
          role: 1,
          gender: 1,
          profession: 1,
          experiencedIn: 1,
          insuranceCompanies: 1,
          ethnicityId: 1,
          dislikedClients: 1,
          blockedByAdmin: 1,
          adminApproved: 1,
          zipCode: 1,
          state: 1,
          _id: 1,
          lavniTestAccount: 1,
          vimeoId: 1,
        },
      },
      {
        $match: {
          $and: [
            therapistNameQuery,
            insuranceCompanyQuery,
            removeInvalidTherapistsQuery,
            experiencedInQuery,
            // stateQuery
          ],
          lavniTestAccount: { $ne: true },
        },
      },
      {
        $lookup: {
          from: "friendrequests",
          localField: "_id",
          foreignField: "therapistId",
          as: "friendRequests",
        },
      },
      {
        $unwind: {
          path: "$friendRequests.clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "coverPhotoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "coverPhotoId",
        },
      },

      {
        $lookup: {
          from: "experiencetags",
          localField: "experiencedIn",
          foreignField: "_id",
          pipeline: [
            {
              $project: { experienceTag: 1 },
            },
          ],
          as: "experiencedIn",
        },
      },
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $sort: { createdAt: -1 },
      },
    ]);

    const emailPriority = [
      "<EMAIL>",
      "<EMAIL>",
    ];

    searchResults.sort((a, b) => {
      const indexA = emailPriority.indexOf(a.email);
      const indexB = emailPriority.indexOf(b.email);
      if (a.email == "<EMAIL>") {
        return 1;
      }

      if (b.email == "<EMAIL>") {
        return -1;
      }

      if (indexA === -1 && indexB === -1) {
        return 0;
      }

      if (indexA === -1) {
        return 1;
      }

      if (indexB === -1) {
        return -1;
      }

      return indexA - indexB;
    });
    return searchResults;
  }

  export async function searchTherapists(
    userId: Types.ObjectId,
    gender: string,
    ethnicity: string,
    profession: string,
    experiencedIn: string[],
    searchTherapistName: string,
    dislikedClients: Types.ObjectId[],
    limit: number,
    offset: number
  ): Promise<ITherapist[]> {
    const client = await ClientDao.getUserById(userId);

    const genderQuery = gender != null && gender ? { gender: gender } : {};

    const ethnicityQuery =
      ethnicity != null && ethnicity
        ? { ethnicityId: Types.ObjectId(ethnicity) }
        : {};

    const professionQuery =
      profession != null && profession
        ? { profession: Types.ObjectId(profession) }
        : {};

    const removeDislikedQuery = { dislikedClients: { $nin: dislikedClients } };

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let experiencedInObjectArray: Types.ObjectId[] = [];

    if (experiencedIn && experiencedIn != null) {
      for (let eI of experiencedIn) {
        if (Util.isObjectId(eI) && eI !== "") {
          experiencedInObjectArray.push(Types.ObjectId(eI));
        }
      }
    }

    const experiencedInQuery =
      experiencedInObjectArray !== null && experiencedInObjectArray.length !== 0
        ? { experiencedIn: { $in: experiencedInObjectArray } }
        : {};

    let searchedName = null;

    if (searchTherapistName) {
      let seacrhItem = searchTherapistName.replace(/\s/g, "");
      searchedName =
        searchTherapistName != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const therapistNameQuery =
      searchedName != null && searchedName
        ? {
          $or: [
            { firstname: searchedName },
            { lastname: searchedName },
            { fullName: searchedName },
          ],
        }
        : {};

    const lavniTestAccountQuery = !client.lavniTestAccount
      ? { lavniTestAccount: { $ne: true } }
      : {};

    const lifeCoachProfession = await TherapistDao.getProfessionByName(
      "Life Coach"
    );

    const stateQuery =
      client.state !== null && client.state && client.state !== ""
        ? lifeCoachProfession
          ? {
            $or: [
              { state: client.state },
              { profession: lifeCoachProfession._id },
            ],
          }
          : { state: client.state }
        : {};

    let searchResults: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,

          coverPhotoId: 1,
          role: 1,
          gender: 1,
          profession: 1,
          experiencedIn: 1,
          ethnicityId: 1,
          dislikedClients: 1,
          blockedByAdmin: 1,
          adminApproved: 1,
          zipCode: 1,
          state: 1,
          _id: 1,
          lavniTestAccount: 1,
          vimeoId: 1,
          therapyState: 1,
        },
      },
      {
        $match: {
          $and: [
            therapistNameQuery,
            genderQuery,
            ethnicityQuery,
            professionQuery,
            removeDislikedQuery,
            removeInvalidTherapistsQuery,
            experiencedInQuery,
            lavniTestAccountQuery,
            // stateQuery
          ],
          lavniTestAccount: { $ne: true },
        },
      },
      {
        $lookup: {
          from: "friendrequests",
          localField: "_id",
          foreignField: "therapistId",
          as: "friendRequests",
        },
      },
      {
        $unwind: {
          path: "$friendRequests.clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          "friendRequests.clientId": { $ne: userId },
        },
      },
      {
        $lookup: {
          from: "professions",
          localField: "profession",
          foreignField: "_id",
          pipeline: [
            {
              $project: { name: 1 },
            },
          ],
          as: "profession",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "coverPhotoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "coverPhotoId",
        },
      },
      {
        $lookup: {
          from: "experiencetags",
          localField: "experiencedIn",
          foreignField: "_id",
          pipeline: [
            {
              $project: { experienceTag: 1 },
            },
          ],
          as: "experiencedIn",
        },
      },
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      // {
      //   $sort: { createdAt: -1 },
      // },
      // {
      //   $skip: offset,
      // },
      // {
      //   $limit: limit,
      // },
      // {
      //   $sample: { size: 99 },
      // },
    ]).sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return searchResults;
  }

  export async function getProfessionById(
    id: StringOrObjectId
  ): Promise<IProfession> {
    const response = await Profession.findById(id);
    return response;
  }

  export async function getProfessionByName(
    professionName: string
  ): Promise<IProfession> {
    const profession = await Profession.findOne({
      name: professionName,
    });

    return profession;
  }

  export async function getExperienceTagById(
    id: StringOrObjectId
  ): Promise<IExperienceTag> {
    const response = await ExperienceTag.findById(id);
    return response;
  }

  export async function addReview(
    therapistId: Types.ObjectId,
    reviewDetails: Review
  ): Promise<ITherapist> {
    let response = await Therapist.findByIdAndUpdate(
      therapistId,
      { $push: { reviews: reviewDetails } },
      { new: true }
    ).populate([
      {
        path: "reviews.client",
        populate: [
          {
            path: "photoId",
            model: "Upload",
          },
        ],
      },
    ]);
    return response;
  }

  export async function viewReviewsByTherapistId(
    therapistId: Types.ObjectId,
    limit?: number,
    offset?: number
  ): Promise<Review[]> {
    let therapist = await Therapist.findById(therapistId).populate({
      path: "reviews.client",
      select: { firstname: 1, lastname: 1, photoId: 1, },
    });
    let populated = await Therapist.populate(therapist, {
      path: "reviews.client.photoId",
    });

    let reviewList: Review[];

    if (populated) {
      reviewList = populated.reviews;
    } else {
      reviewList = [];
    }

    return reviewList;
  }

  export async function searchClientByParams(
    gender: string,
    therapistId: Types.ObjectId,
    ethnicityId: StringOrObjectId,
    experiencedIn: Types.ObjectId[],
    limit: number,
    offset: number
  ): Promise<any> {
    let response = await Therapist.aggregate([
      {
        $match: {
          _id: therapistId,
        },
      },
      {
        $group: {
          _id: "$clientRequests",
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "_id",
        },
      },
      {
        $match: {
          $or: [
            { "_id.gender": gender },
            { "_id.ethnicityId": ethnicityId ? ethnicityId : null },
            {
              "_id.diseaseId": {
                $in: experiencedIn.length > 0 ? experiencedIn : [],
              },
            },
          ],
        },
      },
      {
        $project: {
          "_id._id": 1,
          "_id.firstname": 1,
          "_id.lastname": 1,
          "_id.email": 1,
          "_id.role": 1,
          "_id.photoId": 1,
          "_id.diseaseId": 1,
        },
      },
    ])
      .sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    let populated = await Therapist.populate(response, {
      path: "_id.photoId",
      model: "Upload",
    });

    return populated;
  }

  export async function addTreatmentHistory(
    treatmentHistoryDetails: DTreatmentHistory
  ): Promise<ITreatmentHistory> {
    const iTreatmentHistory = new TreatmentHistory(treatmentHistoryDetails);
    let response = await iTreatmentHistory.save();
    return response;
  }

  export async function viewTreatmentHistorysByMeetingId(
    id: StringOrObjectId
  ): Promise<ITreatmentHistory> {
    const response = await TreatmentHistory.findOne({ meetingId: id });
    return response;
  }

  export async function viewTreatmentHistorysById(
    id: StringOrObjectId
  ): Promise<ITreatmentHistory> {
    const response = await TreatmentHistory.findOne({ _id: id });
    return response;
  }

  export async function viewTransactionsByMeetingId(
    id: string
  ): Promise<ITransaction> {
    const response = await Transaction.findOne({ meetingId: id });
    return response;
  }

  export async function viewTreatmentHistorysByClientId(
    clientId: StringOrObjectId,
    therapistId: StringOrObjectId,
    limit: number,
    offset: number
  ): Promise<ITreatmentHistory[]> {
    const response = await TreatmentHistory.aggregate([
      {
        $match: {
          $and: [
            { clientId: clientId },
            {
              $or: [
                { therapistId: therapistId },
                { subTherapistId: therapistId }
              ]
            },
            {
              $or: [
                { "deleteTreatmentHistory": false },
                { "deleteTreatmentHistory": { "$exists": false } }
              ]
            }
          ],
        },
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $skip: offset,
      },
      {
        $limit: limit,
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          // pipeline: [
          //   {
          //     $project: {
          //       firstname: 1,
          //       lastname: 1,
          //       email: 1,
          //     },
          //   },
          // ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          // pipeline: [
          //   {
          //     $project: {
          //       firstname: 1,
          //       lastname: 1,
          //       email: 1,
          //     },
          //   },
          // ],
          as: "therapistId",
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "meetings",
          localField: "meetingId",
          foreignField: "_id",
          as: "meetingId",
        },
      },
      {
        $unwind: {
          path: "$meetingId",
        },
      },
      {
        $lookup: {
          from: "diagnosisnotes",
          localField: "diagnosisNoteId",
          foreignField: "_id",
          as: "diagnosisNoteDetails",
        },
      },
      {
        $unwind: {
          path: "$diagnosisNoteDetails",
        },
      },
      {
        $lookup: {
          from: "diagnosisnoteversions",
          let: { diagnosisNoteId: "$diagnosisNoteId" },
          pipeline: [
            { $match: { $expr: { $eq: ["$diagnosisNoteId", "$$diagnosisNoteId"] } } },
            { $project: { _id: 1, versionCreatedAt: 1, reasonForEdit: 1, noteType: 1 } }
          ],
          as: "diagnosisNoteVersions"
        }
      },
      {
        $lookup: {
          from: "appointments",
          localField: "meetingId.appointmentId",
          foreignField: "_id",
          // pipeline: [
          //   {
          //     $project: {
          //       _id: 1,
          //       start: 1,
          //     },
          //   },
          // ],
          as: "appointmentDetails",
        },
      },
      {
        $unwind: {
          path: "$appointmentDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "meetingId.audioFiles",
          foreignField: "_id",
          as: "meetingId.audioFiles",
        },
      }
    ]).sort({ _id: -1 });

    return response;
  }


  // export async function viewAllTreatmentHistorysByTherapistId(
  //   therapistId: StringOrObjectId,
  //   limit: number,
  //   offset: number
  // ): Promise<ITreatmentHistory[]> {
  //   const response = await TreatmentHistory.find({
  //     therapistId: therapistId,
  //   }).sort({ createdAt: -1 }).populate([
  //     { path: "clientId", select: { firstname: 1, lastname: 1, _id: 1 } },
  //     { path: "therapistId", select: { firstname: 1, lastname: 1, _id: 1 } },
  //     { path: "diagnosisNoteId", select: { _id: 1, updatedByTherapist: 1, } },
  //     { path: "meetingId", populate: { path: "audioFiles" } },
  //   ])

  //   const sortedResponse = response.sort((a, b) => {
  //     const aIsFalse = a.diagnosisNoteId?.updatedByTherapist === false;
  //     const bIsFalse = b.diagnosisNoteId?.updatedByTherapist === false;

  //     if (aIsFalse && !bIsFalse) {
  //       return -1;
  //     } else if (!aIsFalse && bIsFalse) {
  //       return 1;
  //     }
  //     return 0;
  //   }).slice(limit * (offset - 1), limit * offset);
  //   return sortedResponse;
  // }

  export async function viewAllTreatmentHistorysByTherapistId(
    therapistId: StringOrObjectId,
    limit: number,
    offset: number
  ): Promise<ITreatmentHistory[]> {
    const response = await TreatmentHistory.aggregate([
      {
        $project: {
          clientId: 1,
          therapistId: 1,
          meetingId: 1,
          diagnosisNoteId: 1,
          updatedAt: 1,
          createdAt: 1,
          isMeetingTranscribe: 1,
          meetingStartedTime: 1,
          mergedMeetings: 1,
          deleteTreatmentHistory: 1
        },
      },
      {
        $match: {
          $and: [
            { "therapistId": therapistId },
            {
              $or: [
                { "deleteTreatmentHistory": false },
                { "deleteTreatmentHistory": { "$exists": false } }
              ]
            }
          ],
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                email: 1,
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                email: 1,
              },
            },
          ],
          as: "therapistId",
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "diagnosisnotes",
          localField: "diagnosisNoteId",
          foreignField: "_id",
          as: "diagnosisNoteDetails",
        },
      },
      {
        $unwind: {
          path: "$diagnosisNoteDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "meetings",
          localField: "meetingId",
          foreignField: "_id",
          as: "meetingId",
        },
      },
      {
        $unwind: {
          path: "$meetingId",
        },
      },
      {
        $lookup: {
          from: "appointments",
          localField: "meetingId.appointmentId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                _id: 1,
                start: 1,
              },
            },
          ],
          as: "appointmentDetails",
        },
      },
      {
        $unwind: {
          path: "$appointmentDetails",
          preserveNullAndEmptyArrays: true,
        },
      },

    ]).sort({ createdAt: -1 })
    const sortedResponse = response.sort((a, b) => {
      const aIsFalse = a.diagnosisNoteDetails?.updatedByTherapist === false;
      const bIsFalse = b.diagnosisNoteDetails?.updatedByTherapist === false;

      if (aIsFalse && !bIsFalse) {
        return -1;
      } else if (!aIsFalse && bIsFalse) {
        return 1;
      }
      return 0;
    }).slice(limit * (offset - 1), limit * offset);
    return sortedResponse;
  }



  // export async function viewAllTreatmentHistorysByTherapistId(
  //   therapistId: StringOrObjectId,
  //   limit: number,
  //   offset: number
  // ): Promise<ITreatmentHistory[]> {
  //   const response = await TreatmentHistory.find({
  //     therapistId: therapistId,
  //     $or: [
  //       { 'diagnosisNoteId.updatedByTherapist': false },
  //       { 'diagnosisNoteId.updatedByTherapist': true },
  //     ],
  //   })
  //     .sort({ 'diagnosisNoteId.updatedByTherapist': -1, createdAt: -1 })
  //     .skip(offset)
  //     .limit(limit)
  //     .populate([
  //       { path: 'clientId' },
  //       { path: 'therapistId' },
  //       { path: 'diagnosisNoteId' },
  //       { path: 'meetingId', populate: { path: 'audioFiles' } },
  //     ])
  //     .sort({ _id: -1 });

  //   let res = response;
  //   return res;
  // }

  export async function viewTreatmentHistorysByTherapistIdViewMore(
    therapistId: StringOrObjectId,
    searchClientName: string,
    limit: number,
    offset: number
  ): Promise<ITreatmentHistory[]> {
    let searchedName = null;

    if (searchClientName) {
      let seacrhItem = searchClientName.replace(/\s/g, "");
      searchedName =
        searchClientName != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const clientNameQuery =
      searchedName != null && searchedName
        ? {
          $or: [
            { "clientId.firstname": searchedName },
            { "clientId.lastname": searchedName },
            { "clientId.fullName": searchedName },
          ],
        }
        : {};

    const response = await TreatmentHistory.aggregate([
      {
        $project: {
          clientId: 1,
          therapistId: 1,
          meetingId: 1,
          diagnosisNoteId: 1,
          updatedAt: 1,
          createdAt: 1,
          isMeetingTranscribe: 1,
          meetingStartedTime: 1,
          mergedMeetings: 1,
          deleteTreatmentHistory: 1
        },
      },
      {
        $match: {
          $and: [
            { therapistId: therapistId },
            {
              $or: [
                { "deleteTreatmentHistory": false },
                { "deleteTreatmentHistory": { "$exists": false } }
              ]
            }
          ],
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                email: 1,
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                email: 1,
              },
            },
          ],
          as: "therapistId",
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "diagnosisnotes",
          localField: "diagnosisNoteId",
          foreignField: "_id",
          as: "diagnosisNoteDetails",
        },
      },
      {
        $unwind: {
          path: "$diagnosisNoteDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "meetings",
          localField: "meetingId",
          foreignField: "_id",
          as: "meetingId",
        },
      },
      {
        $unwind: {
          path: "$meetingId",
        },
      },
      {
        $lookup: {
          from: "appointments",
          localField: "meetingId.appointmentId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                _id: 1,
                start: 1,
              },
            },
          ],
          as: "appointmentDetails",
        },
      },
      {
        $unwind: {
          path: "$appointmentDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $and: [
            clientNameQuery,
          ],
          $or: [
            { "clientId.blockedByAdmin": false },
            { "clientId.blockedByAdmin": undefined },
          ],
        },
      },

    ]).sort({ createdAt: -1 })
    const sortedResponse = response.sort((a: any, b: any) => {
      const aIsFalse = a.diagnosisNoteDetails?.updatedByTherapist === false;
      const bIsFalse = b.diagnosisNoteDetails?.updatedByTherapist === false;

      if (aIsFalse && !bIsFalse) {
        return -1;
      } else if (!aIsFalse && bIsFalse) {
        return 1;
      }
      return 0;
    }).slice(limit * (offset - 1), limit * offset);
    return sortedResponse;
  }

  export async function viewTreatmentHistorysByTherapistId(
    therapistId: StringOrObjectId,
    clientId: StringOrObjectId,
    limit: number,
    offset: number
  ): Promise<ITreatmentHistory[]> {
    const response = await TreatmentHistory.aggregate([
      {
        $match: {
          $and: [
            { therapistId: therapistId },
            { clientId: clientId },
            {
              $or: [
                { "deleteTreatmentHistory": false },
                { "deleteTreatmentHistory": { "$exists": false } }
              ]
            }
          ],
        },
      },
      {
        $project: {
          clientId: 1,
          therapistId: 1,
          meetingId: 1,
          diagnosisNoteId: 1,
          updatedAt: 1,
          createdAt: 1,
          isMeetingTranscribe: 1,
          meetingStartedTime: 1,
          mergedMeetings: 1,
          deleteTreatmentHistory: 1
        },
      },

      {
        $sort: { createdAt: -1 },
      },
      {
        $skip: offset,
      },
      {
        $limit: limit,
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                email: 1,
              },
            },
          ],
          as: "clientId",
        },
      },
      {
        $unwind: {
          path: "$clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                email: 1,
              },
            },
          ],
          as: "therapistId",
        },
      },
      {
        $unwind: {
          path: "$therapistId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "diagnosisnotes",
          localField: "diagnosisNoteId",
          foreignField: "_id",
          as: "diagnosisNoteDetails",
        },
      },
      {
        $unwind: {
          path: "$diagnosisNoteDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "meetings",
          localField: "meetingId",
          foreignField: "_id",
          as: "meetingId",
        },
      },
      {
        $unwind: {
          path: "$meetingId",
        },
      },
      {
        $lookup: {
          from: "appointments",
          localField: "meetingId.appointmentId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                _id: 1,
                start: 1,
              },
            },
          ],
          as: "appointmentDetails",
        },
      },
      {
        $unwind: {
          path: "$appointmentDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
    ]).sort({ _id: -1 });

    // const response = await TreatmentHistory.find({
    //   therapistId: therapistId,
    //   clientId: clientId,
    //   $or: [
    //     { deleteTreatmentHistory: { $exists: false } },
    //     { deleteTreatmentHistory: false }
    //   ]
    // })
    //   .sort({ createdAt: -1 })
    //   .skip(offset)
    //   .limit(limit)
    //   .populate([
    //     { path: "clientId" },
    //     { path: "therapistId" },
    //     { path: "meetingId", populate: { path: "audioFiles" } },
    //   ])
    //   .populate({
    //     path: "meetingId.appointmentId",
    //     select: "_id start",
    //   })
    //   .sort({ _id: -1 });
    let res = response;
    return res;
  }

  export async function deleteTreatmentHistoryById(
    noteId: StringOrObjectId
  ): Promise<ITreatmentHistory> {
    let treatmentHestory: ITreatmentHistory =
      await TreatmentHistory.findByIdAndDelete(noteId);

    return treatmentHestory;
  }

  export async function updateTreatmentHistoryStatusById(
    id: StringOrObjectId,
    submittedUser: String
  ): Promise<ITreatmentHistory> {
    const currentTime = new Date();
    const therapist = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { $set: { claimStatus: "ACTIVE", claimSubmittedUser: submittedUser, claimSubmittedTime: currentTime }, $unset: { errorMsg: 1 } },
      { new: true }
    );
    return therapist;
  }

  export async function updateTreatmentHistoryStatusByIdMD(
    id: StringOrObjectId,
    submittedUser: String,
    pcn: String
  ): Promise<ITreatmentHistory> {
    const currentTime = new Date();
    const therapist = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { $set: { claimStatus: "ACTIVEMD", claimSubmittedUser: submittedUser, claimSubmittedTime: currentTime, pcn: pcn }, $unset: { errorMsg: 1 } },
      { new: true }
    );
    return therapist;
  }

  export async function updateTreatmentHistoryCloseStatusById(
    id: StringOrObjectId
  ): Promise<ITreatmentHistory> {
    const currentTime = new Date();
    const therapist = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { $set: { claimStatus: "PENDING_SUBMISSION" }, $unset: { errorMsg: 1 } },
      { new: true }
    );
    return therapist;
  }

  export async function updateTreatmentHistoryStatusByIdDayAfter(
    id: StringOrObjectId
  ): Promise<ITreatmentHistory> {
    const currentTime = new Date();
    const therapist = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { $set: { claimStatus: "IN_PROGRESS" }, $unset: { errorMsg: 1 } },
      { new: true }
    );
    return therapist;
  }

  export async function updateToPaidTreatmentHistoryStatusById(
    id: StringOrObjectId
  ): Promise<ITreatmentHistory> {
    const therapist = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { $set: { claimStatus: "PAID" }, $unset: { errorMsg: 1 } },
      { new: true }
    );
    return therapist;
  }

  export async function updateToUnPaidTreatmentHistoryStatusById(
    id: StringOrObjectId
  ): Promise<ITreatmentHistory> {
    const therapist = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { $set: { claimStatus: "UNPAID" }, $unset: { errorMsg: 1 } },
      { new: true }
    );
    return therapist;
  }

  export async function updateToCrownJobRunCountTreatmentHistoryById(
    id: StringOrObjectId,
    crwonJobRunCount: number,
  ): Promise<ITreatmentHistory> {
    const crownJobCountNew = crwonJobRunCount + 1
    const therapist = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { $set: { cronjobCount: crownJobCountNew }, $unset: { errorMsg: 1 } },
      { new: true }
    );
    return therapist;
  }

  export async function updateTreatmentHistoryErrorById(
    id: StringOrObjectId,
    errorMsg: string
  ): Promise<ITreatmentHistory> {
    const therapist = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { errorMsg: errorMsg, claimStatus: "INACTIVE" },
      { new: true }
    );

    return therapist;
  }

  export async function updateTreatmentHistoryById(
    id: StringOrObjectId,
    data: Partial<DTreatmentHistory>
  ): Promise<ITreatmentHistory> {
    let therapist = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { $set: data },
      { new: true }
    );

    return therapist;
  }

  export async function deleteTreatmentHistoryByClientId(
    clientId: StringOrObjectId
  ): Promise<number> {
    const response = await TreatmentHistory.deleteMany({ clientId: clientId });
    return response.ok;
  }

  export async function deleteTreatmentHistoryByTherapistId(
    therapistId: StringOrObjectId
  ): Promise<number> {
    const response = await TreatmentHistory.deleteMany({
      therapistId: therapistId,
    });
    return response.ok;
  }

  export async function filterTreamentHistoriesByIdList(
    idList: any[],
    clientId: StringOrObjectId,
    therapistId: StringOrObjectId
  ): Promise<ITreatmentHistory[]> {
    let filterArr = [];

    for (const element of idList) {
      filterArr.push({ _id: Types.ObjectId(element) });
    }

    const response = await TreatmentHistory.find({
      $or: filterArr,
      clientId: clientId,
      therapistId: therapistId,
    })
      .sort({ meetingStartedTime: 1 })
      .populate([{ path: "meetingId", select: { meetingId: 1, callingStatus: 1, spentDuration: 1 } }, { path: "diagnosisNoteId", select: { updatedByTherapist: 1, noteType: 1 } }]);

    return response;
  }

  export async function filterTranscribesByIdList(
    idList: any[],
    clientId: StringOrObjectId,
    therapistId: StringOrObjectId
  ): Promise<ITranscribe[]> {
    const response = await Transcribe.find(
      {
        meetingId: { $in: idList },
        clientId: clientId,
        therapistId: therapistId,
      },
      { transcriptText: 1 }
    ).sort({ createdAt: -1 });

    return response;
  }

  export async function updateMergedRecords(
    treatmentHistoryId: StringOrObjectId,
    meetingId: any,
    vonageMeetingId: any,
    meetingIdList: StringOrObjectId[],
    diagnosisNoteIdList: any[],
    removeTreamentIdList: any[],
    finalTranscriptText: any[],
    clientId: StringOrObjectId,
    therapistId: StringOrObjectId,
    totalSpentDuration: number
  ): Promise<ITreatmentHistory> {
    let filterDiagnosisNoteArr = [];
    let filterRemoveTreamentIdArr = [];

    for (const element of diagnosisNoteIdList) {
      filterDiagnosisNoteArr.push({ _id: Types.ObjectId(element._id) });
    }

    for (const element2 of removeTreamentIdList) {
      filterRemoveTreamentIdArr.push({ _id: Types.ObjectId(element2) });
    }

    await DiagnosisNote.remove({
      $or: filterRemoveTreamentIdArr,
      clientId: clientId,
      therapistId: therapistId,
    });

    await TreatmentHistory.remove({
      $or: filterRemoveTreamentIdArr,
      clientId: clientId,
      therapistId: therapistId,
    });

    await Transcribe.findOneAndUpdate(
      { meetingId: vonageMeetingId },
      { $set: { transcriptText: finalTranscriptText } }
    );

    await Meeting.findOneAndUpdate(
      { _id: meetingId },
      { $set: { spentDuration: totalSpentDuration } }
    );

    const response = await TreatmentHistory.findByIdAndUpdate(
      treatmentHistoryId,
      { $set: { mergedMeetings: meetingIdList } },
      { new: true }
    );

    return response;
  }

  export async function updateTherapistPinNumber(
    id: StringOrObjectId,
    pinNumber: string
  ): Promise<ITherapist> {
    const response = await User.findByIdAndUpdate(
      id,
      { $set: { pinNumber: pinNumber } },
      { new: true }
    ).populate([
      { path: "photoId" },
      { path: "experiencedIn" },
      { path: "profession" },
      { path: "coverPhotoId" },
    ]);

    return response;
  }

  export async function updateAiGeneratedCount(
    id: StringOrObjectId,
    aiGenerateCount: number
  ): Promise<ITherapist> {
    const response = await Therapist.findByIdAndUpdate(
      id,
      { $set: { aiGenerateCount: aiGenerateCount, } },
      { new: true }
    );

    return response;
  }


  export async function updateAiGenerateReview(
    id: StringOrObjectId,
  ): Promise<ITherapist> {
    const response = await Therapist.findByIdAndUpdate(
      id,
      { $set: { aiReviewSubmitted: true } },
      { new: true }
    );

    return response;
  }

  export async function getAiGeneratedCount(
    userId: Types.ObjectId
  ): Promise<number | null> {
    const therapist = await Therapist.findOne({
      _id: userId,
    });
    if (therapist) {
      return therapist.aiGenerateCount;
    }

    return null;
  }

  export async function updateMonthlyPaymentPaidStatus(
    monthlyPaymentId: Types.ObjectId,
    therapistId: Types.ObjectId,
    data: any
  ): Promise<IMonthlyPayment | null> {

    const { paidStatus, ...rest } = data;

    const updateData = {
      $set: {
        "therapistTransaction.$[elem].paidStatus": "paid",
        ...rest
      }
    };

    const options = {
      new: true,
      arrayFilters: [{ "elem._id": therapistId }]
    };

    try {
      const updatedMonthlyPayment = await MonthlyPayment.findOneAndUpdate(
        { _id: monthlyPaymentId },
        updateData,
        options
      );

      return updatedMonthlyPayment;
    } catch (error) {
      throw error;
    }
  }

  export async function getRecentMonthlyPaynemtsWithoutFilter(): Promise<any[]> {
    let paymentList = await MonthlyPayment.aggregate([
      {
        $match: {
          crownJobType: { $in: ["1st", "15th"] }
        }
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $group: {
          _id: "$crownJobType",
          latestPaymentId: { $first: "$_id" },
          document: { $first: "$$ROOT" }
        }
      }
    ]);

    let latestPayments = paymentList.map(entry => entry.document);

    await MonthlyPayment.populate(paymentList, {
      path: 'therapistTransaction.recentTransaction._id',
      model: Transaction,
      select: 'createdAt type updatedAt paidStatus',
    });

    await MonthlyPayment.populate(paymentList, {
      path: 'therapistTransaction.recentTransaction.meetingId',
      model: Meeting,
      select: 'clientId createdAt regularMeetingDate meetingId',
    });

    await MonthlyPayment.populate(paymentList, {
      path: 'therapistTransaction.recentTransaction.meetingId.clientId',
      model: Client,
      select: 'firstname lastname insuranceId',
    });

    await MonthlyPayment.populate(paymentList, {
      path: 'therapistTransaction.recentTransaction.meetingId.clientId.insuranceId',
      model: Insurance,
      select: 'insuranceCompanyId',
    });

    await MonthlyPayment.populate(paymentList, {
      path: 'therapistTransaction.recentTransaction.meetingId.clientId.insuranceId.insuranceCompanyId',
      model: InsuranceCompany,
      select: 'insuranceCompany',
    });

    return paymentList;
  }

  export async function getTherapistScoreDetails(limit: number, offset: number): Promise<any[]> {
    const therapistScoreList = await TherapistScoreSchema.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "therapistId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { firstname: 1, lastname: 1, email: 1, adminApproved: 1, priorityNumber: 1 },
            },
          ],
          as: "therapists"
        }
      },
      {
        $unwind: "$therapists"
      },
      // {
      //   $match: {
      //     "therapists.adminApproved": true
      //   }
      // },
      {
        $project: { _id: 0, createdAt: 0, updatedAt: 0, therapistId: 0 },
      },
      {
        $sort: { score: -1 },
      }
    ]).skip(limit * (offset - 1))
      .limit(limit);

    return therapistScoreList;
  }

  export async function getAlltherapistsForScoring(): Promise<any[]> {
    const therapistList = await Therapist.find({}, { _id: 1, email: 1, createdAt: 1 });
    return therapistList;
  }

  export async function getScoreOfTheTherapist(therapistId: Types.ObjectId): Promise<any> {
    const now = new Date();
    const fortyEightHoursLater = new Date(now.getTime() + 48 * 60 * 60 * 1000);
    const upcomingTwoDates = getNextTwoDaysDates();
    const lastDateOf48Hours = new Date(upcomingTwoDates[upcomingTwoDates.length - 1]);
    const firstDateOf48Hours = new Date(upcomingTwoDates[0]);
    const allTimeSlotsInUpcoming48Hours = totalSlotsOf2DaysAnd7Days(firstDateOf48Hours, lastDateOf48Hours);

    const upcomingSevenDates = getNextSevenDaysDates();
    const lastDateOf7Days = new Date(upcomingSevenDates[upcomingSevenDates.length - 1]);
    const firstDateOf7Days = new Date(upcomingSevenDates[0]);
    const allTimeSlotsInUpcoming7Days = totalSlotsOf2DaysAnd7Days(firstDateOf7Days, lastDateOf7Days);
    const sevenDaysLater = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

    const therapistDataSet: any = await Therapist.aggregate([
      {
        $match: {
          "_id": therapistId
        }
      },
      {
        $facet: {
          "next48HoursAppointments": [
            {
              $lookup: {
                from: "appointments",
                localField: "_id",
                foreignField: "therapistId",
                as: "appointments"
              }
            },
            {
              $unwind: "$appointments"
            },
            {
              $match: {
                "appointments.start": {
                  $gte: now,
                  $lte: fortyEightHoursLater
                }
              }
            },
            {
              $group: {
                _id: "$_id",
                totalNext48HoursAppointments: { $sum: 1 }
              }
            },
            {
              $project: {
                _id: 1,
                next48HoursAppointments: "$totalNext48HoursAppointments"
              }
            }
          ],
          "next7DaysAppointments": [
            {
              $lookup: {
                from: "appointments",
                localField: "_id",
                foreignField: "therapistId",
                as: "appointments"
              }
            },
            {
              $unwind: "$appointments"
            },
            {
              $match: {
                "appointments.start": {
                  $gte: now,
                  $lte: sevenDaysLater
                }
              }
            },
            {
              $group: {
                _id: "$_id",
                totalNext7DaysAppointments: { $sum: 1 }
              }
            },
            {
              $project: {
                _id: 1,
                next7DaysAppointments: "$totalNext7DaysAppointments"
              }
            }
          ],
          "missedAppointments": [
            {
              $lookup: {
                from: "appointments",
                localField: "_id",
                foreignField: "therapistId",
                as: "appointments"
              }
            },
            {
              $unwind: "$appointments"
            },
            {
              $match: {
                $and: [
                  { "appointments.status": AppointmentStatus.PENDING },
                  {
                    "appointments.start": {
                      $lte: now
                    }
                  }
                ]

              }
            },
            {
              $group: {
                _id: "$_id",
                missedAppointments: { $sum: 1 }
              }
            },
            {
              $project: {
                _id: 1,
                missedAppointments: 1
              }
            }
          ],
          "noOfSessions": [
            {
              $lookup: {
                from: "meetings",
                localField: "_id",
                foreignField: "therapistId",
                as: "meetings"
              }
            },
            {
              $unwind: "$meetings"
            },
            {
              $match: {
                "meetings.callingStatus": CallingStatus.COMPLETED
              }
            },
            {
              $group: {
                _id: "$_id",
                noOfSessions: { $sum: 1 }
              }
            },
            {
              $project: {
                _id: 1,
                noOfSessions: 1
              }
            }
          ],
          "noOfScheduledAppointments": [
            {
              $lookup: {
                from: "appointments",
                localField: "_id",
                foreignField: "therapistId",
                as: "appointments"
              }
            },
            {
              $unwind: "$appointments"
            },
            {
              $match: {
                $and: [
                  {
                    "appointments.start": {
                      $gte: now
                    }
                  },
                  {
                    $or: [
                      { "appointments.status": AppointmentStatus.PENDING },
                      { "appointments.status": AppointmentStatus.WAITING_FOR_APPROVAL }
                    ]
                  }
                ]
              }
            },
            {
              $group: {
                _id: "$_id",
                noOfScheduledAppointments: { $sum: 1 }
              }
            },
            {
              $project: {
                _id: 1,
                noOfScheduledAppointments: 1
              }
            }
          ],
          "noOfFriendRequests": [
            {
              $lookup: {
                from: "friendrequests",
                localField: "_id",
                foreignField: "therapistId",
                as: "friendrequests"
              }
            },
            {
              $unwind: "$friendrequests"
            },
            {
              $group: {
                _id: "$_id",
                noOfFriendRequests: { $sum: 1 }
              }
            },
            {
              $project: {
                _id: 1,
                noOfFriendRequests: 1
              }
            }
          ],
          "totalResponseTime": [
            {
              $lookup: {
                from: "chats",
                localField: "_id",
                foreignField: "members",
                as: "chats"
              }
            },
            {
              $unwind: "$chats"
            },
            {
              $addFields: {
                "chats.otherMember": {
                  $arrayElemAt: [
                    {
                      $filter: {
                        input: "$chats.members",
                        cond: { $ne: ["$$this", therapistId] }
                      }
                    },
                    0
                  ]
                }
              }
            },
            {
              $group: {
                _id: "$chats._id",
                otherMember: { $first: "$chats.otherMember" }
              }
            },
            {
              $project: {
                chatIdd: "$_id",
                clientId: "$otherMember"
              }
            },
            {
              $lookup: {
                from: "messages",
                let: { chatId: "$chatIdd", therapistId: therapistId },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ["$chatId", "$$chatId"] },
                          { $eq: ["$senderId", "$$therapistId"] }
                        ]
                      }
                    }
                  },
                  {
                    $sort: { createdAt: 1 }
                  },
                  {
                    $limit: 1
                  },
                  {
                    $project: {
                      _id: 1,
                      senderId: 1,
                      createdAt: 1
                    }
                  }
                ],
                as: "messages"
              }
            },
            {
              $lookup: {
                from: "appointments",
                let: { clientId: "$clientId", therapistId: therapistId },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ["$clientId", "$$clientId"] },
                          { $eq: ["$therapistId", "$$therapistId"] }
                        ]
                      }
                    }
                  },
                  {
                    $sort: { createdAt: 1 } // Sorting appointments by createdAt in ascending order
                  },
                  {
                    $limit: 1 // Limiting to only the first appointment
                  },
                  {
                    $project: {
                      _id: 1,
                      createdAt: 1,
                      start: 1,
                      title: 1,
                      therapistChangeApprovedStatusAt: 1
                    }
                  }
                ],
                as: "appointments"
              }
            },
            {
              $lookup: {
                from: "users",
                let: { clientId: "$clientId" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$_id", "$$clientId"]
                      }
                    }
                  },
                  {
                    $project: {
                      _id: 1,
                      firstname: 1,
                      lastname: 1,
                      email: 1,
                      createdAt: 1
                    }
                  }
                ],
                as: "users"
              }
            },
            {
              $project: {
                "chatIdd": 1,
                "clientId": 1,
                "users": 1,
                "appointments": 1,
                "messages": 1,
                "userCreatedAt": { $arrayElemAt: ["$users.createdAt", 0] },
                "appointmentCreatedAt": { $arrayElemAt: ["$appointments.createdAt", 0] },
                "appointmentApprovedAt": { $arrayElemAt: ["$appointments.therapistChangeApprovedStatusAt", 0] },
                "messageCreatedAt": { $arrayElemAt: ["$messages.createdAt", 0] },
              }
            },
            {
              $addFields: {
                "closestToUser": {
                  $cond: {
                    if: { $and: [{ $eq: ["$appointmentApprovedAt", null] }, { $eq: ["$messageCreatedAt", null] }] },
                    then: null, // Both appointmentApprovedAt and messageCreatedAt are missing
                    else: {
                      $cond: {
                        if: { $eq: ["$appointmentApprovedAt", null] },
                        then: { $divide: [{ $abs: { $subtract: ["$messageCreatedAt", "$userCreatedAt"] } }, 3600000] }, // Only appointmentApprovedAt is missing
                        else: {
                          $cond: {
                            if: { $eq: ["$messageCreatedAt", null] },
                            then: { $divide: [{ $abs: { $subtract: ["$appointmentApprovedAt", "$userCreatedAt"] } }, 3600000] }, // Only messageCreatedAt is missing
                            else: { // Both appointmentApprovedAt and messageCreatedAt exist
                              $min: [
                                { $divide: [{ $abs: { $subtract: ["$appointmentApprovedAt", "$userCreatedAt"] } }, 3600000] },
                                { $divide: [{ $abs: { $subtract: ["$messageCreatedAt", "$userCreatedAt"] } }, 3600000] }
                              ]
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            },
            {
              $group: {
                _id: null,
                totalResponseTimeInHours: { $sum: "$closestToUser" }
              }
            }
          ],
          "therapistsWithWorkingHoursInNextTwoDays": [
            {
              $addFields: {
                workingHours: {
                  $filter: {
                    input: '$workingHours',
                    as: 'hours',
                    cond: {
                      $in: [
                        '$$hours.day',
                        upcomingTwoDates.map(date => getDayOfWeek(date))
                      ]
                    }
                  }
                }
              }
            },
            {
              $project: {
                _id: 1,
                workingHours: 1,
                blockedDates: 1
              }
            }
          ],
          "therapistsWithWorkingHoursInNextSevenDays": [
            {
              $addFields: {
                workingHours: {
                  $filter: {
                    input: '$workingHours',
                    as: 'hours',
                    cond: {
                      $in: [
                        '$$hours.day',
                        upcomingSevenDates.map(date => getDayOfWeek(date))
                      ]
                    }
                  }
                }
              }
            },
            {
              $project: {
                _id: 1,
                workingHours: 1,
                blockedDates: 1
              }
            }
          ],
          "checkFollowUpAppointments": [
            {
              $lookup: {
                from: "appointments",
                let: { therapistId: "$_id" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ["$therapistId", "$$therapistId"] },
                          { $eq: ["$status", "COMPLETED"] },
                          { $gte: ["$start", threeMonthsAgo] }
                        ]
                      }
                    }
                  },
                  {
                    $sort: { start: -1 } // Sort appointments by start date in descending order
                  },
                  {
                    $group: {
                      _id: "$clientId",
                      mostRecentAppointment: { $first: "$start" },
                      start: { $first: "$start" },
                      status: { $first: "$status" },
                      clientId: { $first: "$clientId" }
                    }
                  },
                  {
                    $project: {
                      _id: 0,
                      clientId: "$_id",
                      start: 1,
                      status: 1
                    }
                  }
                ],
                as: "appointmentsData"
              }
            },
            {
              $unwind: "$appointmentsData"
            },
            {
              $lookup: {
                from: "appointments",
                let: { clientId: "$appointmentsData.clientId", therapistId: therapistId },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ["$clientId", "$$clientId"] },
                          { $eq: ["$therapistId", "$$therapistId"] },
                          { $eq: ["$status", "PENDING"] },
                          { $gt: ["$start", now] }
                        ]
                      }
                    }
                  },
                  {
                    $project: {
                      _id: 0,
                      start: 1,
                      status: 1
                    }
                  }
                ],
                as: "pendingAppointments"
              }
            },
            {
              $addFields: {
                hasPendingAppointments: { $gt: [{ $size: "$pendingAppointments" }, 0] }
              }
            },
            {
              $project: {
                _id: 0,
                clientId: "$appointmentsData.clientId",
                mostRecentAppointment: "$appointmentsData.start",
                status: "$appointmentsData.status",
                appointmentCount: "$appointmentsData.appointmentCount",
                hasPendingAppointments: 1
              }
            }
          ]
        }
      },
    ]);

    const checkFollowUpAppointments = therapistDataSet[0]?.checkFollowUpAppointments.length === 0 ? [] : therapistDataSet[0]?.checkFollowUpAppointments;
    const followUpAppointmentCompleted = checkFollowUpAppointments.length;
    const noOfFollowUpAppointmentPending = checkFollowUpAppointments.filter((item: any) => item.hasPendingAppointments).length;
    // const followUpAppointmentsAsPercentage = followUpAppointmentCompleted === 0 ? 0 : (noOfFollowUpAppointmentPending/followUpAppointmentCompleted)*100;
    const next48HoursAppointments = therapistDataSet[0]?.next48HoursAppointments[0]?.next48HoursAppointments ?
      therapistDataSet[0]?.next48HoursAppointments[0]?.next48HoursAppointments : 0;
    const totalSessions = therapistDataSet[0]?.noOfSessions[0]?.noOfSessions;
    const missedAppointments = therapistDataSet[0]?.missedAppointments[0]?.missedAppointments;
    const noOfScheduledAppointments = therapistDataSet[0]?.noOfScheduledAppointments[0]?.noOfScheduledAppointments ?
      therapistDataSet[0]?.noOfScheduledAppointments[0]?.noOfScheduledAppointments : 0;
    const noOfFriendRequests = therapistDataSet[0]?.noOfFriendRequests[0]?.noOfFriendRequests;
    // const noOfFollowUpAppointments = therapistDataSet[0]?.followUpAppointments[0]?.totalAppointmentCount;
    const totalResponseTimeInHours = therapistDataSet[0]?.totalResponseTime[0]?.totalResponseTimeInHours.toFixed(1);
    const therapistsWithWorkingHoursInNextTwoDays = therapistDataSet[0]?.therapistsWithWorkingHoursInNextTwoDays[0];
    const therapistsWithWorkingHoursInNextSevenDays = therapistDataSet[0]?.therapistsWithWorkingHoursInNextSevenDays[0];
    const next7DaysAppointments = therapistDataSet[0]?.next7DaysAppointments[0]?.next7DaysAppointments ?
      therapistDataSet[0]?.next7DaysAppointments[0]?.next7DaysAppointments : 0;

    // ------------------ Start - Calculate availability in next 48 Hours ----------------------
    const therapistWorkingSlotsInUpcming48Hours: string[] = [];
    therapistsWithWorkingHoursInNextTwoDays?.workingHours?.forEach((workingHour: any) => {
      if (workingHour) {
        const { startTime, endTime, day } = workingHour;
        const startTimeIn24Hours = convertTo24Hour(startTime);
        const endTimeIn24Hours = convertTo24Hour(endTime);
        const start = parseTime(startTimeIn24Hours);
        const end = parseTime(endTimeIn24Hours);
        let currentTime = start;
        const intervalMinutes = 30;

        while (currentTime.hour < end.hour || (currentTime.hour === end.hour && currentTime.minute < end.minute)) {
          const time1 = currentTime;
          currentTime = addMinutes(currentTime, intervalMinutes);
          const time2 = currentTime
          therapistWorkingSlotsInUpcming48Hours.push(formatTime(time1, time2, day));
        }
      }

    });
    const blockedTimeSlotsInUpcming48Hours: string[] = [];
    therapistsWithWorkingHoursInNextTwoDays?.blockedDates?.forEach((blockedDate: any) => {
      const { start, end } = blockedDate;
      const blockDateStart = moment(start).utc();
      const blockDateEnd = moment(end).utc();
      const interval = 30;
      if (blockDateStart.isBefore(lastDateOf48Hours) && blockDateEnd.isSameOrAfter(firstDateOf48Hours)) {
        while (blockDateStart.isBefore(blockDateEnd)) {
          const time1 = blockDateStart.format('dddd HH:mm');
          blockDateStart.add(interval, 'minutes');
          const time2 = blockDateStart.format('HH:mm');
          blockedTimeSlotsInUpcming48Hours.push(`${time1}-${time2}`);
        }
      }
    })


    const uniqueTherapistWorkingSlotsInUpcming48Hours = Array.from(new Set(therapistWorkingSlotsInUpcming48Hours));// Remove duplicates
    const UniqueBlockedTimeSlotsInUpcming48Hours = Array.from(new Set(blockedTimeSlotsInUpcming48Hours)); // Remove duplicates
    const commonSlotsInUpcming48Hours = findCommonElements(uniqueTherapistWorkingSlotsInUpcming48Hours, allTimeSlotsInUpcoming48Hours);
    const filteredCommonElements = commonSlotsInUpcming48Hours.filter((element: string) => !UniqueBlockedTimeSlotsInUpcming48Hours.includes(element));
    const finalAvailableSlotsInHoursUpcoming48Hours = filteredCommonElements.length % 2 === 0 ? (filteredCommonElements.length / 2) : ((filteredCommonElements.length - 1) / 2);
    const availabilityInUpcming48Hours = finalAvailableSlotsInHoursUpcoming48Hours > next48HoursAppointments ? (finalAvailableSlotsInHoursUpcoming48Hours - next48HoursAppointments) : 0;
    // ------------------ End - Calculate availability in next 48 Hours ----------------------

    // ------------------ Start - Calculate availability in next 7 Days ----------------------
    const therapistWorkingSlotsInUpcming7Days: string[] = [];
    therapistsWithWorkingHoursInNextSevenDays?.workingHours?.forEach((workingHour: any) => {
      if (workingHour) {
        const { startTime, endTime, day } = workingHour;
        const startTimeIn24Hours = convertTo24Hour(startTime);
        const endTimeIn24Hours = convertTo24Hour(endTime);
        const start = parseTime(startTimeIn24Hours);
        const end = parseTime(endTimeIn24Hours);
        let currentTime = start;
        const intervalMinutes = 30;

        while (currentTime.hour < end.hour || (currentTime.hour === end.hour && currentTime.minute < end.minute)) {
          const time1 = currentTime;
          currentTime = addMinutes(currentTime, intervalMinutes);
          const time2 = currentTime
          therapistWorkingSlotsInUpcming7Days.push(formatTime(time1, time2, day));
        }
      }

    });
    const blockedTimeSlotsInUpcming7Days: string[] = [];
    therapistsWithWorkingHoursInNextSevenDays?.blockedDates?.forEach((blockedDate: any) => {
      const { start, end } = blockedDate;
      const blockDateStart = moment(start).utc();
      const blockDateEnd = moment(end).utc();
      const interval = 30;
      if (blockDateStart.isBefore(lastDateOf7Days) && blockDateEnd.isSameOrAfter(firstDateOf7Days)) {
        while (blockDateStart.isBefore(blockDateEnd)) {
          const time1 = blockDateStart.format('dddd HH:mm');
          blockDateStart.add(interval, 'minutes');
          const time2 = blockDateStart.format('HH:mm');
          blockedTimeSlotsInUpcming7Days.push(`${time1}-${time2}`);
        }
      }
    })
    const uniqueTherapistWorkingSlotsInUpcming7Days = Array.from(new Set(therapistWorkingSlotsInUpcming7Days));// Remove duplicates
    const UniqueBlockedTimeSlotsInUpcming7Days = Array.from(new Set(blockedTimeSlotsInUpcming7Days)); // Remove duplicates
    const commonSlotsInUpcming7Days = findCommonElements(uniqueTherapistWorkingSlotsInUpcming7Days, allTimeSlotsInUpcoming7Days);
    const filteredCommonElements7Days = commonSlotsInUpcming7Days.filter((element: string) => !UniqueBlockedTimeSlotsInUpcming7Days.includes(element));
    const finalAvailableSlotsInHoursUpcoming7Days = filteredCommonElements7Days.length % 2 === 0 ? (filteredCommonElements7Days.length / 2) : ((filteredCommonElements7Days.length - 1) / 2);
    const availabilityInUpcming7Days = finalAvailableSlotsInHoursUpcoming7Days > next7DaysAppointments ? (finalAvailableSlotsInHoursUpcoming7Days - next7DaysAppointments) : 0;

    // ------------------ End - Calculate availability in next 7 Days ----------------------
    const availability = ((0.75 * availabilityInUpcming48Hours) + (0.25 * availabilityInUpcming7Days))

    return {
      availability: availability,
      totalSessions,
      missedAppointments,
      noOfScheduledAppointments,
      noOfFriendRequests: noOfFriendRequests,
      noOfFollowUpAppointments: noOfFollowUpAppointmentPending,
      totalResponseTimeInHours
    }
  }

  export async function createTherapistScore(data: any): Promise<ITherapistScore> {
    const therapistid = data.therapistId;
    await TherapistScoreSchema.deleteMany({
      therapistId: therapistid
    });

    const therapistScore = new TherapistScoreSchema(data);
    const response = await therapistScore.save();

    return response;

  }

  export async function updateTherapistPriorityNumberUsingBulkWrite(data: any): Promise<any> {
    const therapist = await Therapist.bulkWrite(data);

    return therapist;
  }

  export async function createTherapistScoreConstants(data: DTherapistScoreConstants): Promise<any> {
    try {
      await TherapistScoreConstants.deleteMany({});
      const therapistScoreConstants = new TherapistScoreConstants(data);
      const response = await therapistScoreConstants.save();
      return response;
    } catch (error) {

    }

  }

  export async function getTherapistScoreConstants(): Promise<ITherapistScoreConstants[]> {
    try {
      const therapistScoreConstants = await TherapistScoreConstants.find();
      return therapistScoreConstants;
    } catch (error) {
      throw error;
    }

  }
  const getNextTwoDaysDates = (): Date[] => {
    const now = new Date();
    const dates = [new Date(now)];
    for (let i = 1; i <= 2; i++) {
      const nextDate = new Date(now);
      nextDate.setDate(now.getDate() + i);
      dates.push(nextDate);
    }

    return dates;
  };
  const getNextSevenDaysDates = (): Date[] => {
    const now = new Date();
    const dates = [new Date(now)];
    for (let i = 1; i <= 7; i++) {
      const nextDate = new Date(now);
      nextDate.setDate(now.getDate() + i);
      dates.push(nextDate);
    }
    return dates;
  };
  const getDayOfWeek = (date: Date): string => {
    const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    return days[date.getDay()];
  };
  interface Time {
    hour: number;
    minute: number;
  }

  function parseTime(timeString: string): Time {
    const [hour, minute] = timeString.split(':').map(Number);
    return { hour, minute };
  }

  function convertTo24Hour(time: string): string {
    return moment(time, ["h:mm A"]).format("HH:mm");
  }

  function addMinutes(time: Time, minutes: number): Time {
    let newHour = time.hour;
    let newMinute = time.minute + minutes;

    if (newMinute >= 60) {
      newHour += Math.floor(newMinute / 60);
      newMinute %= 60;
    }

    return { hour: newHour, minute: newMinute };
  }
  function formatTime(time1: Time, time2: Time, day: String): string {
    const hour = time1.hour.toString().padStart(2, '0');
    const minute = time1.minute.toString().padStart(2, '0');

    const hour2 = time2.hour.toString().padStart(2, '0');
    const minute2 = time2.minute.toString().padStart(2, '0');
    return `${day} ${hour}:${minute}-${hour2}:${minute2}`;
  }
  function totalSlotsOf2DaysAnd7Days(startTime: Date, endTime: Date) {
    const start = moment(startTime).utc();
    const end = moment(endTime).utc();
    if (start.minutes() !== 0) {
      start.add(1, 'hour').startOf('hour');
    }
    if (end.minutes() !== 0) {
      end.add(1, 'hour').startOf('hour');
    }
    const timeSlots = [];
    const interval = 30;
    while (start.isBefore(end)) {
      const time1 = start.format('dddd HH:mm')
      start.add(interval, 'minutes');
      const start2 = start.format('HH:mm')
      timeSlots.push(`${time1}-${start2}`);
    }
    return timeSlots;
  }

  function findCommonElements(arr1: String[], arr2: String[]) {
    return arr1.filter((element: String) => arr2.includes(element));
  }

  export async function forTesting(therapistId: Types.ObjectId): Promise<any[]> {
    const therapists = await Therapist.aggregate([
      {
        $match: {
          _id: therapistId
        }
      },
      {
        $lookup: {
          from: "chats",
          localField: "_id",
          foreignField: "members",
          as: "chats"
        }
      },
      {
        $unwind: "$chats"
      },
      {
        $addFields: {
          "chats.otherMember": {
            $arrayElemAt: [
              {
                $filter: {
                  input: "$chats.members",
                  cond: { $ne: ["$$this", therapistId] }
                }
              },
              0
            ]
          }
        }
      },
      {
        $group: {
          _id: "$chats._id",
          otherMember: { $first: "$chats.otherMember" }
        }
      },
      {
        $project: {
          chatIdd: "$_id",
          clientId: "$otherMember"
        }
      },
      {
        $lookup: {
          from: "messages",
          let: { chatId: "$chatIdd", therapistId: therapistId },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$chatId", "$$chatId"] },
                    { $eq: ["$senderId", "$$therapistId"] }
                  ]
                }
              }
            },
            {
              $sort: { createdAt: 1 }
            },
            {
              $limit: 1
            },
            {
              $project: {
                _id: 1,
                senderId: 1,
                createdAt: 1
              }
            }
          ],
          as: "messages"
        }
      },
      {
        $lookup: {
          from: "appointments",
          let: { clientId: "$clientId", therapistId: therapistId },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$clientId", "$$clientId"] },
                    { $eq: ["$therapistId", "$$therapistId"] }
                  ]
                }
              }
            },
            {
              $sort: { createdAt: 1 } // Sorting appointments by createdAt in ascending order
            },
            {
              $limit: 1 // Limiting to only the first appointment
            },
            {
              $project: {
                _id: 1,
                createdAt: 1,
                start: 1,
                title: 1,
                therapistChangeApprovedStatusAt: 1
              }
            }
          ],
          as: "appointments"
        }
      },
      {
        $lookup: {
          from: "users",
          let: { clientId: "$clientId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$clientId"]
                }
              }
            },
            {
              $project: {
                _id: 1,
                firstname: 1,
                lastname: 1,
                email: 1,
                createdAt: 1
              }
            }
          ],
          as: "users"
        }
      },
      {
        $project: {
          "chatIdd": 1,
          "clientId": 1,
          "users": 1,
          "appointments": 1,
          "messages": 1,
          "userCreatedAt": { $arrayElemAt: ["$users.createdAt", 0] },
          "appointmentCreatedAt": { $arrayElemAt: ["$appointments.createdAt", 0] },
          "appointmentApprovedAt": { $arrayElemAt: ["$appointments.therapistChangeApprovedStatusAt", 0] },
          "messageCreatedAt": { $arrayElemAt: ["$messages.createdAt", 0] },
        }
      },
      {
        $addFields: {
          "closestToUser": {
            $cond: {
              if: { $and: [{ $eq: ["$appointmentApprovedAt", null] }, { $eq: ["$messageCreatedAt", null] }] },
              then: null, // Both appointmentApprovedAt and messageCreatedAt are missing
              else: {
                $cond: {
                  if: { $eq: ["$appointmentApprovedAt", null] },
                  then: { $divide: [{ $abs: { $subtract: ["$messageCreatedAt", "$userCreatedAt"] } }, 3600000] }, // Only appointmentApprovedAt is missing
                  else: {
                    $cond: {
                      if: { $eq: ["$messageCreatedAt", null] },
                      then: { $divide: [{ $abs: { $subtract: ["$appointmentApprovedAt", "$userCreatedAt"] } }, 3600000] }, // Only messageCreatedAt is missing
                      else: { // Both appointmentApprovedAt and messageCreatedAt exist
                        $min: [
                          { $divide: [{ $abs: { $subtract: ["$appointmentApprovedAt", "$userCreatedAt"] } }, 3600000] },
                          { $divide: [{ $abs: { $subtract: ["$messageCreatedAt", "$userCreatedAt"] } }, 3600000] }
                        ]
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      {
        $group: {
          _id: 1,
          totalResponseTimeInHours: { $sum: "$closestToUser" }
        }
      }
    ]);

    return therapists;
  }

  export async function getSearchTherapistsList(
    // userId: Types.ObjectId,
    gender: string,
    ethnicity: string,
    profession: string,
    experiencedIn: string[],
    searchTherapistName: string,
    // dislikedClients: Types.ObjectId[],
    limit: number,
    offset: number
  ): Promise<ITherapist[]> {
    // const client = await ClientDao.getUserById(userId);

    const genderQuery = gender != null && gender ? { gender: gender } : {};

    const ethnicityQuery =
      ethnicity != null && ethnicity
        ? { ethnicityId: Types.ObjectId(ethnicity) }
        : {};

    const professionQuery =
      profession != null && profession
        ? { profession: Types.ObjectId(profession) }
        : {};

    // const removeDislikedQuery = { dislikedClients: { $nin: dislikedClients } };

    const removeInvalidTherapistsQuery = {
      blockedByAdmin: { $ne: true },
      adminApproved: { $ne: false },
    };

    let experiencedInObjectArray: Types.ObjectId[] = [];

    if (experiencedIn && experiencedIn != null) {
      for (let eI of experiencedIn) {
        if (Util.isObjectId(eI) && eI !== "") {
          experiencedInObjectArray.push(Types.ObjectId(eI));
        }
      }
    }

    const experiencedInQuery =
      experiencedInObjectArray !== null && experiencedInObjectArray.length !== 0
        ? { experiencedIn: { $in: experiencedInObjectArray } }
        : {};

    let searchedName = null;

    if (searchTherapistName) {
      let seacrhItem = searchTherapistName.replace(/\s/g, "");
      searchedName =
        searchTherapistName != null ? new RegExp(`^${seacrhItem}`, "i") : null;
    }

    const therapistNameQuery =
      searchedName != null && searchedName
        ? {
          $or: [
            { firstname: searchedName },
            { lastname: searchedName },
            { fullName: searchedName },
          ],
        }
        : {};

    // const lavniTestAccountQuery = !client.lavniTestAccount
    //   ? { lavniTestAccount: { $ne: true } }
    //   : {};

    const lifeCoachProfession = await TherapistDao.getProfessionByName(
      "Life Coach"
    );

    // const stateQuery =
    //   client.state !== null && client.state && client.state !== ""
    //     ? lifeCoachProfession
    //       ? {
    //         $or: [
    //           { state: client.state },
    //           { profession: lifeCoachProfession._id },
    //         ],
    //       }
    //       : { state: client.state }
    //     : {};

    let searchResults: ITherapist[] = await Therapist.aggregate([
      {
        $project: {
          fullName: {
            $concat: ["$firstname", "$lastname"],
          },
          firstname: 1,
          lastname: 1,
          email: 1,
          createdAt: 1,
          photoId: 1,

          coverPhotoId: 1,
          role: 1,
          gender: 1,
          profession: 1,
          experiencedIn: 1,
          ethnicityId: 1,
          dislikedClients: 1,
          blockedByAdmin: 1,
          adminApproved: 1,
          zipCode: 1,
          state: 1,
          _id: 1,
          lavniTestAccount: 1,
          vimeoId: 1,
          therapyState: 1,
        },
      },
      {
        $match: {
          $and: [
            therapistNameQuery,
            genderQuery,
            ethnicityQuery,
            professionQuery,
            // removeDislikedQuery,
            removeInvalidTherapistsQuery,
            experiencedInQuery,
            // lavniTestAccountQuery,
            // stateQuery
          ],
          lavniTestAccount: { $ne: true },
        },
      },
      {
        $lookup: {
          from: "friendrequests",
          localField: "_id",
          foreignField: "therapistId",
          as: "friendRequests",
        },
      },
      {
        $unwind: {
          path: "$friendRequests.clientId",
          preserveNullAndEmptyArrays: true,
        },
      },
      // {
      //   $match: {
      //     "friendRequests.clientId": { $ne: userId },
      //   },
      // },
      {
        $lookup: {
          from: "professions",
          localField: "profession",
          foreignField: "_id",
          pipeline: [
            {
              $project: { name: 1 },
            },
          ],
          as: "profession",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "photoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "photoId",
        },
      },
      {
        $lookup: {
          from: "uploads",
          localField: "coverPhotoId",
          foreignField: "_id",
          pipeline: [
            {
              $project: { isUrl: 1, path: 1, originalName: 1, url: 1 },
            },
          ],
          as: "coverPhotoId",
        },
      },
      {
        $lookup: {
          from: "experiencetags",
          localField: "experiencedIn",
          foreignField: "_id",
          pipeline: [
            {
              $project: { experienceTag: 1 },
            },
          ],
          as: "experiencedIn",
        },
      },
      {
        $unwind: {
          path: "$profession",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$coverPhotoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$photoId",
          preserveNullAndEmptyArrays: true,
        },
      },
      // {
      //   $sort: { createdAt: -1 },
      // },
      // {
      //   $skip: offset,
      // },
      // {
      //   $limit: limit,
      // },
      // {
      //   $sample: { size: 99 },
      // },
    ]).sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    return searchResults;
  }

  export async function getTreatmentById(id: StringOrObjectId): Promise<ITreatmentHistory> {
    let treatment: ITreatmentHistory = await TreatmentHistory.findById(id);

    return treatment;
  }

  export async function updateTreatmentHistorySubTherapistById(
    id: StringOrObjectId,
    subTherapist: StringOrObjectId,
  ): Promise<ITreatmentHistory> {
    const subTherapistId = new Types.ObjectId(subTherapist);
    const treatmentHistory = await TreatmentHistory.findOneAndUpdate(
      { _id: id },
      { $set: { subTherapistId: subTherapistId }, $unset: { errorMsg: 1 } },
      { new: true }
    );
    return treatmentHistory;
  }

  export async function submitDocsForAdminApprovalByTherapist(
    submissionDetails: DInsuranceDocApproval
  ): Promise<IInsuranceDocApproval> {
    const { clientId, therapistId, insuranceCompanyId, therapistApprovalStatus } = submissionDetails;
    let response = await InsuranceDocApproval.findOneAndUpdate(
      { clientId, therapistId, insuranceCompanyId },
      { therapistApprovalStatus },
      { new: true, upsert: true, setDefaultsOnInsert: true }
    );
    return response;
  }

  //start
  export async function getPresignUrlRegulerSessionAudioByPathAndId(id:Types.ObjectId, path: string ) {

      const response = await Meeting.findById(id);
      if(!response) {
        return
      }

      AWS.config.update({
           accessKeyId: process.env.AWS_S3_ACCESSKEY_ID,
           secretAccessKey: process.env.AWS_S3_SECRET_ACCESSKEY,
           region: process.env.AWS_S3_REGION,
      });

      const s3 = new AWS.S3();

      const params = {
           Bucket: process.env.AWS_S3_BUCKET_REGULER_AUDIO_CALLS,
           Key: path,
           Expires: 300,
      };
      const url = await s3.getSignedUrlPromise("getObject", params);
      return url

  }
  //end

  export async function getTherapistScoreByTherapistID(id:Types.ObjectId):Promise<any> {
    try {
      const therapistScoreAndPriorityNumber = await Therapist.findById(id).select('score priorityNumber');
      return therapistScoreAndPriorityNumber;
      // throw new Error("Simulated error for testing");
    } catch (error) {
      return null;
    }
  }

  export async function getTherapistScoreAllDetailsByTherapistId(id:Types.ObjectId):Promise<any> {
    try {
      const therapistScoreDetails = await TherapistScoreSchema.findOne({
        therapistId: id
      }).populate({
        path: 'therapistId',  
        model: Therapist.modelName, 
        select: 'firstname lastname score priorityNumber' 
      })

      return therapistScoreDetails;

    } catch (error) {
      return null;
    }
  }

  export async function getFirstMeetingDataByClientIdAndTherapistId(clientId: Types.ObjectId, therapistId: Types.ObjectId): Promise<any> {
    const firstMeetingData = await Meeting.findOne({
      clientId: clientId,
      therapistId: therapistId
    }).sort({ createdAt: 1 });
    
    return firstMeetingData;
  }

}
