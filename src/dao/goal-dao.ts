import { Types } from "mongoose";
import { DGoal, IGoal } from "../models/goal-model";
import Goal from "../schemas/goal-schema";
import { StringOrObjectId } from "../common/util";

export namespace GoalDao {
  // to create
  export async function createGoal(goal: DGoal): Promise<IGoal> {
    const iGoal = new Goal(goal);
    let res = await iGoal.save();
    return res;
  }

  // to update
  export async function updateGoal(
    goalId: StringOrObjectId,
    goal: DGoal
  ): Promise<IGoal> {
    let res = await Goal.findByIdAndUpdate(goalId, goal, {
      new: true,
    }).populate([{ path: "createdBy" }, { path: "assignedFor" }]);
    return res;
  }

  // to get all by ids
  export async function getGoalsByIds(
    creatorId: Types.ObjectId,
    assignerId: Types.ObjectId,
    limit: number,
    offset: number
  ): Promise<IGoal[]> {
    // const res = await Goal.find({assignedFor: assignerId, createdBy: creatorId}).populate([
    //     { path: "createdBy"},
    //     { path: "assignedFor"}
    // ]).sort({ createdAt: -1 });
    const res = await Goal.aggregate([
      {
        $match: {
          $and: [{ createdBy: creatorId }, { assignedFor: assignerId }],
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "createdBy",
          foreignField: "_id",
          as: "createdBy",
        },
      },
      {
        $unwind: {
          path: "$createdBy",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "assignedFor",
          foreignField: "_id",
          as: "assignedFor",
        },
      },
      {
        $unwind: {
          path: "$assignedFor",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $and: [
            {
              $or: [
                { "createdBy.blockedByAdmin": false },
                { "createdBy.blockedByAdmin": undefined },
              ],
            },
            {
              $or: [
                { "assignedFor.blockedByAdmin": false },
                { "assignedFor.blockedByAdmin": undefined },
              ],
            },
          ],
        },
      },
    ]).sort({ createdAt: -1 })
      .skip(limit * (offset - 1))
      .limit(limit);

    const response = await Goal.populate(res, [
      { path: "createdBy" },
      { path: "assignedFor" },
    ]);
    return response;
  }

  // to get goal by id
  export async function getGoalById(goalId: StringOrObjectId): Promise<IGoal> {
    const res = await Goal.findById(goalId).populate([
      { path: "createdBy" },
      { path: "assignedFor" },
    ]);
    return res;
  }

  // to delete by id
  export async function deleteGoalById(
    goalId: StringOrObjectId
  ): Promise<IGoal> {
    const res = await Goal.findByIdAndDelete(goalId);
    return res;
  }

  export async function deleteGoalsByClientId(clientId: StringOrObjectId): Promise<number> {
    const response = await Goal.deleteMany({ assignedFor: clientId });
    return response.ok;
  }

  export async function deleteGoalsByTherapistId(therapistId: StringOrObjectId): Promise<number> {
    const response = await Goal.deleteMany({ createdBy: therapistId });
    return response.ok;
  }
}
