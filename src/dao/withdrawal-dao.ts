import { StringOrObjectId } from "../common/util";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Withdrawal } from "../models/withdrawal-model";
import Withdrawal from "../schemas/withdrawal-shema";

export namespace WithdrawalDao {
  export async function createWithdrawal(withdrawal: DWithdrawal): Promise<IWithdrawal> {
    const withdrawalDetails = new Withdrawal(withdrawal);

    const response = await withdrawalDetails.save();

    return response;
  }

  export async function deleteAllWithdrawalDetailsByTherapistId(therapistId:StringOrObjectId): Promise<number> {
    const response = await Withdrawal.deleteMany({therapistId:therapistId});
    return response.ok;
  }
}