import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";
import { DMedicalPhrase, IMedicalPhrase } from "../models/medical-phrases";
import MedicalPhrase from "../schemas/medical-phrase-schema";

export namespace MedicalPhraseDao {
  export async function createMedicalPhrase(
    medicalPhrase: DMedicalPhrase
  ): Promise<IMedicalPhrase> {
    const iPhrase = new MedicalPhrase(medicalPhrase);
    let response = await iPhrase.save();
    return response;
  }

  export async function getMedicalPhraseById(
    phraseId: StringOrObjectId
  ): Promise<DMedicalPhrase> {
    const response = await MedicalPhrase.findById(phraseId);
    return response;
  }

  export async function getMedicalPhrase(
    phrase: Partial<DMedicalPhrase>
  ): Promise<IMedicalPhrase[]> {
    const response = await MedicalPhrase.find(phrase);
    return response;
  }

  export async function updateMedicalPhrase(
    phraseId: StringOrObjectId,
    data: Partial<DMedicalPhrase>
  ): Promise<IMedicalPhrase> {
    let response = await MedicalPhrase.findByIdAndUpdate(
      { _id: phraseId },
      { $set: data },
      { new: true }
    );
    return response;
  }

  export async function getAllMedicalPhrasesByStage(
    stage: string,
    therapistId: string
  ): Promise<IMedicalPhrase[]> {
    const response = await MedicalPhrase.aggregate([
      {
        $match: {
          stage: stage,
          $or: [
            { createdBy: { $exists: false } },
            { createdBy: Types.ObjectId(therapistId) },
          ],
        },
      },
    ]);
    return response;
  }
}
