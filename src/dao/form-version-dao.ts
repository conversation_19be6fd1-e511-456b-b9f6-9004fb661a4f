import { Types } from "mongoose";
import { ITherapyPlan } from "../models/therapy-plan-model";
import TherapyPlan from "../schemas/therapy-plan-schema";
import { DTherapyPlanVersion, ITherapyPlanVersion } from "../models/therapy-plan-version-model";
import TherapyPlanVersion from "../schemas/therapy-plan-version-schema";
import { DDiagnosisNoteVersion, IDiagnosisNoteVersion } from "../models/diagnosis-note-version-model";
import DiagnosisNoteVersion from "../schemas/diagnosis-note-version-schema";
import { StringOrObjectId } from "../common/util";
import ClinicalAssesment from "../schemas/clinical-assesment-schema";
import { IClinicalAssesment } from "../models/clinicalAssesment-model";
import { DClinicalAssesmentVersion, IClinicalAssesmentVersion } from "../models/clinicalAssesment-version-model";
import ClinicalAssesmentVersion from "../schemas/clinical-assesment-version-schema";
import DigitalAssessmentForms from "./../schemas/digital-assessment-form-schema";
import { DDigitalAssessmentVersion, IDigitalAssessmentVersion } from "../models/digital-assessment-version-model";
import DigitalAssessmentVersion from "../schemas/digital-assessment-form-version-schema";
import { IDigitalAssessment } from "../models/digital-assessment-model";

export namespace FormVersionDao {
  export async function getCurrentTherapyPlanById(
    formId: Types.ObjectId
  ): Promise<ITherapyPlan> {
    const therapyPlan = await TherapyPlan.findById(formId);
    return therapyPlan;
  }

  export async function createTherapyPlanVersion(
    therapyPlanVersionData: DTherapyPlanVersion,
  ): Promise<ITherapyPlanVersion> {
    const therapyPlanVersion = new TherapyPlanVersion(therapyPlanVersionData);
    await therapyPlanVersion.save();
    return therapyPlanVersion;
  }

  export async function getTherapyPlansWithAllVersions(
    clientId: Types.ObjectId,
    therapistId: Types.ObjectId
  ): Promise<any[]> {
    const therapyPlans = await TherapyPlan.find({ clientId: clientId, therapistId: therapistId })
    .select('_id createdAt');

    const therapyPlansWithVersions = await Promise.all(
      therapyPlans.map(async (plan) => {
        const versions = await TherapyPlanVersion.find({ therapyPlanId: plan._id }).select('_id versionCreatedAt reasonForEdit');
        return { ...plan.toObject(), versions };
      })
    );
    return therapyPlansWithVersions;
  }

  export async function getTherapyPlanVersion(
    tpVersionId: Types.ObjectId
  ): Promise<ITherapyPlanVersion> {
    const therapyPlanVersion = await TherapyPlanVersion.findById(tpVersionId);
    return therapyPlanVersion;
  }

  export async function createDiagnosisNoteVersion(
    diagnosisNoteVersionData: DDiagnosisNoteVersion,
  ): Promise<IDiagnosisNoteVersion> {
    const diagnosisNoteVersion = new DiagnosisNoteVersion(diagnosisNoteVersionData);
    await diagnosisNoteVersion.save();
    return diagnosisNoteVersion;
  }

  export async function getDiagnosisNoteVersionByIdWithOutPopulateFunction(
    noteId: StringOrObjectId
  ): Promise<IDiagnosisNoteVersion> {
    const noteDetails = await DiagnosisNoteVersion.findById(noteId);
    return noteDetails;
  }

  export async function getDiagnosisNoteVersionByIdFunction(
    noteId: StringOrObjectId
  ): Promise<IDiagnosisNoteVersion> {
    const noteDetails = await DiagnosisNoteVersion.findById(noteId).populate([
      {
        path: "clientId",
        select: {
          firstname: 1,
          lastname: 1,
          email: 1,
          primaryPhone: 1,
          username: 1,
          city: 1,
          dateOfBirth: 1,
          gender: 1,
          state: 1,
          streetAddress: 1,
          insuranceId: 1,
          stripeCustomerId: 1,
          copaymentAmount: 1,
          secondaryInsuranceId: 1,
          claimEligibilityDetails: 1,
          claimEligibilityMdDetails: 1,
          _id: 1,
        },
        populate: [
          {
            path: "insuranceId",
            select: {
              dependent: 1,
              insuranceCardId: 1,
              insuranceCompanyId: 1,
              subscriber: 1,
              _id: 1,
            },
            populate: [
              {
                path: "insuranceCompanyId",
                select: {
                  insuranceCompany: 1
                },
              },
              { path: "insuranceCardId" },
            ],
          },
        ],

      },
      {
        path: "therapistId",
        select: {
          city: 1,
          firstname: 1,
          lastname: 1,
          username: 1,
          license: 1,
          nPI1: 1,
          nPI2: 1,
          state: 1,
          streetAddress: 1,
          taxonomyCode: 1,
          _id: 1,
          claimOpen: 1,
        },
      },
      {
        path: "meetingId",
        select: { meetingDuration: 1, spentDuration: 1, copayment: 1, createdAt: 1, regularMeetingDate: 1, _id: 1 },
      },

      { path: "historyOfPresentIllnessAttachments" },
      { path: "mentalBehavioralStatusAttachments" },
      { path: "assessmentAttachments" },
      { path: "carePlanAttachments" },

    ]);
    return noteDetails;
  }

  export async function getCurrentClinicalAssessmentDataById(
    formId: Types.ObjectId
  ): Promise<IClinicalAssesment> {
    const clinicalAssessment = await ClinicalAssesment.findById(formId);
    return clinicalAssessment;
  }

  export async function createNewClinicalAssessmentVersion(
    clinicalAssesmentVersionData: DClinicalAssesmentVersion,
  ): Promise<IClinicalAssesmentVersion> {
    const clinicalAssesmentVersion = new ClinicalAssesmentVersion(clinicalAssesmentVersionData);
    await clinicalAssesmentVersion.save({ validateBeforeSave: false });
    // await clinicalAssesmentVersion.save();
    return clinicalAssesmentVersion;
  }

  export async function getTheClinicalAssessmentsWithAllVersions(
    clientId: Types.ObjectId,
    therapistId: Types.ObjectId
  ): Promise<any[]> {
    const clinicalAssessments = await ClinicalAssesment.find({ clientId: clientId, therapistId: therapistId })
    .select('_id createdAt');

    const clinicalAssessmentsWithVersions = await Promise.all(
      clinicalAssessments.map(async (assessment) => {
        const versions = await ClinicalAssesmentVersion.find({ clinicalAssesmentId: assessment._id }).select('_id versionCreatedAt reasonForEdit');
        return { ...assessment.toObject(), versions };
      })
    );
    return clinicalAssessmentsWithVersions;
  }

  export async function getTheClinicalAssessmentVersion(
    caVersionId: Types.ObjectId
  ): Promise<IClinicalAssesmentVersion> {
    const clinicalAssesmentVersion = await ClinicalAssesmentVersion.findById(caVersionId);
    return clinicalAssesmentVersion;
  }

  export async function getTheNewTypeOfClinicalAssessmentWithAllVersions(
    clientId: Types.ObjectId,
    therapistId: Types.ObjectId
  ): Promise<any[]> {
    const digitalAssessment = await DigitalAssessmentForms.find({ clientId: clientId, therapistId: therapistId })
    .select('_id createdAt');

    const digitalAssessmentWithVersions = await Promise.all(
      digitalAssessment.map(async (assessment) => {
        const versions = await DigitalAssessmentVersion.find({ digitalAssessmentId: assessment._id }).select('_id versionCreatedAt reasonForEdit');
        return { ...assessment.toObject(), versions };
      })
    );
    return digitalAssessmentWithVersions;
  }

  export async function getTheCurrentNewTypeOfClinicalAssessment(
    formId: Types.ObjectId
  ): Promise<IDigitalAssessment> {
    const digitalAssessment = await DigitalAssessmentForms.findById(formId);
    return digitalAssessment;
  }

  export async function createTheNewTypeOfClinicalAssessmentVersion(
    digitalAssesmentVersionData: DDigitalAssessmentVersion,
  ): Promise<IDigitalAssessmentVersion> {
    const digitalAssesmentVersion = new DigitalAssessmentVersion(digitalAssesmentVersionData);
    await digitalAssesmentVersion.save();
    return digitalAssesmentVersion;
  }

  export async function getTheNewTypeOfClinicalAssessmentDetailsForDownload(
    assesmentId: Types.ObjectId
  ): Promise<IDigitalAssessment> {
    const digitalAssesment = await DigitalAssessmentForms.findById(assesmentId);
    return digitalAssesment;
  }

  export async function getTheNewTypeOfClinicalAssessmentVersionForDownload(
    daVersionId: Types.ObjectId
  ): Promise<IDigitalAssessmentVersion> {
    const digitalAssesmentVersion = await DigitalAssessmentVersion.findById(daVersionId);
    return digitalAssesmentVersion;
  }

}
