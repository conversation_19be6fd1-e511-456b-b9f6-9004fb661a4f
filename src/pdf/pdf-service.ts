import moment = require("moment");

const pdf = require("pdfkit");
const fs = require("fs");

const PDF_PATH = "./invoices/SENT_INVOICES/";

export namespace PdfService {
  export async function generateInvoice(
    paidAmount: string,
    dueAmount: string,
    paymentPeriod: string,
    clientId: string
  ) {
    const invoice = new pdf({ margin: 65 }, { compress: false });

    invoice.pipe(
      fs.createWriteStream(
        `${PDF_PATH}/${clientId}-${paymentPeriod.replace(" ", "_")}.pdf`
      )
    );

    invoice
      .image("./src/assets/images/logo.png", 60, 55, { width: 100 })
      .fillColor("#444444")
      .fontSize(12)
      .text("2500 Blue Ridge Road,", 200, 65, { align: "right" })
      .text("Suite 421,", 200, 80, { align: "right" })
      .text("Raleigh, North Carolina", 200, 95, { align: "right" })
      .text("27607", 200, 110, { align: "right" })
      .moveDown();

    invoice
      .fontSize(12)
      .text("+ 1 (843) 460 4292", 200, 130, { align: "right" })
      .moveDown();

    invoice.text(`INVOICE`, 65, 145, { align: "center" }).moveDown();

    invoice
      .strokeColor("#aaaaaa")
      .lineWidth(1)
      .moveTo(65, 170)
      .lineTo(550, 170)
      .stroke();

    invoice
      .text(`Invoice Number : #12345`, 65, 185)
      .text(`Invoice Date : ${moment().format("YYYY-MM-DD")}`, 65, 215)
      .text(`Balance Due : ${dueAmount}`, 65, 200)
      .text(`Paid Aount : ${paidAmount}`, 65, 230)
      .text(`Payment Period : ${paymentPeriod}`, 65, 245)
      .text(`Name : John Doe`, 300, 185)
      .text("Address : 221/B, Baker Street, London", 300, 200)
      .text(`Somewhere on earth`, 300, 215)
      .moveDown();

    invoice
      .strokeColor("#aaaaaa")
      .lineWidth(1)
      .moveTo(65, 260)
      .lineTo(550, 260)
      .stroke();

    invoice
      .fontSize(12)
      .text("Invoice Number", 65, 300, { width: 90 })
      .text("Date", 130, 300, { width: 90, align: "right" })
      .text("Payment Period", 215, 300, { width: 90, align: "right" })
      .text("Paid Amount", 295, 300, { width: 90, align: "right" })
      .text("Due Amount", 370, 300, { width: 120, align: "right" })
      .moveDown();

    invoice
      .strokeColor("#aaaaaa")
      .lineWidth(1)
      .moveTo(65, 315)
      .lineTo(550, 315)
      .stroke();

    invoice
      .fontSize(12)
      .text("#12345", 65, 330, { width: 90 })
      .text(moment().format("YYYY-DD-MM"), 130, 330, {
        width: 90,
        align: "right",
      })
      .text(`$${paymentPeriod}`, 215, 330, { width: 90, align: "right" })
      .text(`$${paidAmount}`, 295, 330, { width: 90, align: "right" })
      .text(`$${dueAmount}`, 370, 330, { width: 120, align: "right" })
      .moveDown();

    invoice.end();

    return `${PDF_PATH}/${clientId}-${paymentPeriod.replace(" ", "_")}.pdf`;
  }
}
