import nltk
import openai
from spacy.lang.en.stop_words import STOP_WORDS
from string import punctuation
from heapq import nlargest
import sys
from get_data2 import *

# openAIAPIKey = "***************************************************"
# meetingId = "ans0nIuTQQqL3WPo07ZwVw=="
# mongoConnectString = "****************************************************************************************"
# dbName = "lavni_db_2023"

openAIAPIKey = sys.argv[1]
meetingId = sys.argv[2]
mongoConnectString = sys.argv[3]
dbName = sys.argv[4]

# print("openAIAPIKey: " + openAIAPIKey) 
# print("meetingId: " + meetingId)
# print("mongoConnectString: " + mongoConnectString)
# nltk.download('punkt')

openai.api_key = openAIAPIKey

maxToken = 4097

convo = groupConversation(getTranscript(meetingId, mongoConnectString, dbName))

per = (maxToken/len(nltk.word_tokenize(convo)))/5

def summarize(text, per):
    import spacy
    import en_core_web_sm
    nlp = spacy.load('en_core_web_sm')
    doc= nlp(text)
    tokens=[token.text for token in doc]
    word_frequencies={}
    for word in doc:
        if word.text.lower() not in list(STOP_WORDS):
            if word.text.lower() not in punctuation:
                if word.text not in word_frequencies.keys():
                    word_frequencies[word.text] = 1
                else:
                    word_frequencies[word.text] += 1
    max_frequency=max(word_frequencies.values())
    for word in word_frequencies.keys():
        word_frequencies[word]=word_frequencies[word]/max_frequency
    sentence_tokens= [sent for sent in doc.sents]
    sentence_scores = {}
    for sent in sentence_tokens:
        for word in sent:
            if word.text.lower() in word_frequencies.keys():
                if sent not in sentence_scores.keys():
                    sentence_scores[sent]=word_frequencies[word.text.lower()]
                else:
                    sentence_scores[sent]+=word_frequencies[word.text.lower()]
    select_length=int(len(sentence_tokens)*per)
    summary=nlargest(select_length, sentence_scores,key=sentence_scores.get)
    final_summary=[word.text for word in summary]
    summary=''.join(final_summary)
    return summary

summary_convo = summarize(convo, per)

# print(summary_convo)

# print(len(nltk.word_tokenize(convo)))
# print(len(nltk.word_tokenize(summary_convo)))

nltk.word_tokenize(convo)
nltk.word_tokenize(summary_convo)

response = openai.Completion.create(
  model="gpt-3.5-turbo-instruct",
  prompt="use DSM-5 to create SOAP notes for mental health given this conversation {}".format(summary_convo),
  temperature=1,
  max_tokens=800,
  top_p=1,
  frequency_penalty=0,
  presence_penalty=0
)

soap_notes = response['choices'][0]['text']

print(soap_notes)