import os
import nltk
import openai
from Get_data import *
client = pymongo.MongoClient('**************************************************************************')

openai.api_key = "***************************************************"

import en_core_web_sm

nltk.download('punkt')

nlp = en_core_web_sm.load()

db = client['lavni_db']
users = db['users']
max_token = 4097

client_email = '<EMAIL>'
therapist_email = '<EMAIL>'
date = '2023-03-27'

users_cols = ['_id', 'firstname' ]
client = get_data(users, client_email, 'CLIENT' )[users_cols]
client_name = client['firstname'][0]
client_id = client['_id'][0]
therapist = get_data(users ,therapist_email, 'THERAPIST' )[users_cols]
therapist_name = therapist['firstname'][0]
meeting_id = get_meetings(client_id)

client_name = "Corinthia";
client_id = "640bfb0cee8ba62aa12506c1";
therapist_name = "Shavonna (Shay)";
meeting_id = "V7LSdGd0R9K7VoabQr7GBQ==";

print("AAAAAAAAAAA");
print(meeting_id);
print(client);
print(therapist);


convo = groupconvos(get_transcript(meeting_id))

per = (max_token /len(nltk.word_tokenize(convo)))/5

summary_convo = summarize(convo, per)
print(len(nltk.word_tokenize(convo)))
print(len(nltk.word_tokenize(summary_convo)))
print(summary_convo)

response = openai.Completion.create(
  model="gpt-3.5-turbo-instruct",
  prompt="use DSM-5 to create SOAP notes for mental health given this conversation {}".format(summary_convo),
  temperature=1,
  max_tokens=800,
  top_p=1,
  frequency_penalty=0,
  presence_penalty=0
)
print(response)

soap_notes = response['choices'][0]['text']

subjective = soap_notes.split(':')[1].split('\n\n')[0]
objective = soap_notes.split(':')[2]
Assesment = soap_notes.split(':')[3]
Plan = soap_notes.split(':')[4]

print('subjective \n' + subjective)
print('Objective \n' +  objective)
print(Assesment)
print(Plan)