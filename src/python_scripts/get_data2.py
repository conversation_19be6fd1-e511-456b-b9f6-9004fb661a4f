import pymongo
from itertools import groupby

def getTranscript(meetingId, mongoURL, dbName):
    client = pymongo.MongoClient(mongoURL)
    db = client[dbName]

    x = db['transcribes'].find_one({'meetingId':meetingId})['transcriptText']
    x = '\n '.join(x)
    return x

def groupConversation(text):
    gen = (line for line in text.split("\n") if line)
    output = []

    for speaker, text in groupby(gen, lambda line: line.split(":")[0]):
        text = " ".join([x[len(speaker)+2:] for x in text])
        response = "{}: {}".format(speaker, text)
        # print(response)
        output.append(response)
    return '\n'.join(output)