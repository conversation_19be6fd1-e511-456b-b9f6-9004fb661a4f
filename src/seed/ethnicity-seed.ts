import { AdminDao } from "../dao/admin-dao";
import { DEthnicity } from "../models/ethnicity-model";


export default async function seedEthnicityTypes() {
    var promiseList:any[]=[];

    const items:any[] = [
        {
            ethnicity:"Asian",
        },
        {
            ethnicity:"American African",
        },
        {
            ethnicity:"American Indian",
        },
        {
            ethnicity:"Latino",
        },
        {
            ethnicity:"White",
        }
    ]

    for(var item of items){
        promiseList.push(createEthnicity(item.ethnicity));
    }

    const newTypes = await Promise.all(promiseList);
    return [newTypes];
}


async function createEthnicity(ethnicity:DEthnicity) {
    const existingType = await AdminDao.getEthnicityByType(ethnicity);
    if (existingType) {
        return existingType;
    }
    return await AdminDao.addEthnicity(ethnicity);
}
