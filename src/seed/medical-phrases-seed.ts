import { Medical<PERSON>hraseDao } from "../dao/medical-phrase-dao";
import { DMedicalPhrase, Stages, TabTypes } from "../models/medical-phrases";

export default async function seedMedicalPhrases() {
  var promiseList: any[] = [];
  const items: DMedicalPhrase[] = [
    {
      value: "Relational difficulties",
      priority: "a_01",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Recent relational conflict",
      priority: "a_02",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with relational frustrations",
      priority: "a_03",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Struggles with social anxiety",
      priority: "a_04",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with anger and envy in interpersonal relationships",
      priority: "a_05",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with feelings of rejection in interpersonal relationships",
      priority: "a_06",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Difficulties with boundaries in interpersonal relationships",
      priority: "a_07",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Securing appropriate boundaries",
      priority: "a_08",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Crisis situation in family",
      priority: "a_09",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Conflict with family members",
      priority: "a_10",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Working through interpersonal/family experiences",
      priority: "a_11",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Interpersonal difficulties with spouse/partner",
      priority: "a_12",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Sexual difficulties and/or concerns",
      priority: "a_13",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of family life",
      priority: "a_14",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Partner issues or concerns",
      priority: "a_15",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Sexual difficulties and/or concerns",
      priority: "a_16",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of relationship with father",
      priority: "a_17",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of relationship with mother",
      priority: "a_18",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of relationship with sibling (s)",
      priority: "a_19",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of relationships with children",
      priority: "a_20",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of parenting concerns",
      priority: "a_21",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of relationships with friends",
      priority: "a_22",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of relational concerns with friends",
      priority: "a_23",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of relational concerns and issues in the workplace",
      priority: "a_24",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with conflict and authorities at work",
      priority: "a_25",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploring relational concerns and issues in school",
      priority: "a_26",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of positive interpersonal experiences",
      priority: "a_27",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Recent positive relational experiences",
      priority: "a_28",
      tabType: TabTypes.RELATIONAL,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },

    {
      value: "Expression of stressful experiences",
      priority: "b_01",
      tabType: TabTypes.TRAUMA,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of childhood traumas and neglect",
      priority: "b_02",
      tabType: TabTypes.TRAUMA,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of multiple traumatic events and cumulative trauma in childhood/adolescence",
      priority: "b_03",
      tabType: TabTypes.TRAUMA,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of impact of sexual abuse experiences experienced in childhood/adolescence",
      priority: "b_04",
      tabType: TabTypes.TRAUMA,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of emotional neglect experienced during childhood/adolescence",
      priority: "b_05",
      tabType: TabTypes.TRAUMA,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of transgenerational traumas",
      priority: "b_06",
      tabType: TabTypes.TRAUMA,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of vicarious traumatic experiences",
      priority: "b_07",
      tabType: TabTypes.TRAUMA,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of health concerns and illness",
      priority: "b_08",
      tabType: TabTypes.TRAUMA,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of end of life anxieties, fear and concerns",
      priority: "b_09",
      tabType: TabTypes.TRAUMA,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },

    {
      value: "Exploration of low self-esteem and poor confidence",
      priority: "c_01",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Working towards improved self-esteem and confidence",
      priority: "c_02",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of bodily anxieties and distorted body image",
      priority: "c_03",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Conflicts and concerns regarding ethnic/cultural identity",
      priority: "c_04",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of anxieties regarding gender and/or sexuality identity",
      priority: "c_05",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of conflicts regarding masculinity and/or issues of male sexuality",
      priority: "c_06",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of conflicts regarding femininity and/or issues of female sexuality",
      priority: "c_07",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of conflicts and anxieties regarding masculinity",
      priority: "c_08",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of conflicts and anxieties regarding femininity",
      priority: "c_09",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of obstacles to identify development in adolescence",
      priority: "c_10",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of adolescent identity concerns",
      priority: "c_11",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Expression and exploration of crisis of identity",
      priority: "c_12",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Working towards integration of adult identity",
      priority: "c_13",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of mid-life identity concerns and meaning",
      priority: "c_14",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of late-life identity formation and concerns",
      priority: "c_15",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Life-review as part of later-life identity consolidation",
      priority: "c_16",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Expression and exploration of discontinuities in self-state experiences",
      priority: "c_17",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Difficulties with boundaries and self-assertion",
      priority: "c_18",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Awareness and exploration of deficits in self-preservation",
      priority: "c_19",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of feelings of lack of entitlement",
      priority: "c_20",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Working towards securing a ‘right to a life’",
      priority: "c_21",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "appropriate entitlement and independence",
      priority: "c_22",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and working through a ‘false-self’ states and their relationship to early life experiences",
      priority: "c_23",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of guilt feelings and inhibition regarding pleasure and success",
      priority: "c_24",
      tabType: TabTypes.IDENTITY,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },

    {
      value: "Exploration of life experiences and self-understanding",
      priority: "d_01",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Expression and development of ‘true-self’ experiences and enhanced authenticity",
      priority: "d_02",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Development of self-care and self-preservation",
      priority: "d_03",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Development of inner security",
      priority: "d_04",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Ongoing work toward greater self-empathy",
      priority: "d_05",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Ongoing work towards greater mindfulness",
      priority: "d_06",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and working through of inner self-criticism, self-punishment and self-denial",
      priority: "d_07",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Working through and exploration of inhibitions regarding success and accomplishment",
      priority: "d_08",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of conflicts regarding entitlement and self-validation",
      priority: "d_09",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of dream life",
      priority: "d_10",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of fantasy life",
      priority: "d_11",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of therapeutic relationship",
      priority: "d_12",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Ongoing working through of transferences",
      priority: "d_13",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of characterological and interpersonal difficulties as revisited within the therapeutic relationship",
      priority: "d_14",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Continued exploration of the impact of early life on current identity",
      priority: "d_15",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and evaluation of target behaviors and areas of concern",
      priority: "d_16",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Discussion of medication and its impact",
      priority: "d_17",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration and discussion of recent homework assignments",
      priority: "d_18",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Review of the therapeutic work to date",
      priority: "d_19",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of therapeutic goals and aims",
      priority: "d_20",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Revision of therapeutic goals and aims",
      priority: "d_21",
      tabType: TabTypes.PROCESS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },

    {
      value: "Expression and exploration of recent loss",
      priority: "e_01",
      tabType: TabTypes.MOURNING_LOSS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Continued working through of traumatic losses",
      priority: "e_02",
      tabType: TabTypes.MOURNING_LOSS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Continued mourning of childhood losses",
      priority: "e_03",
      tabType: TabTypes.MOURNING_LOSS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of unhappy childhood experiences and losses throughout life",
      priority: "e_04",
      tabType: TabTypes.MOURNING_LOSS,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },

    {
      value: "Transition to adulthood and adult responsibilities",
      priority: "f_01",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Strain of mid-life transitions",
      priority: "f_02",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with recent changes in family life",
      priority: "f_03",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Late-life adjustment and coping with age-related changes",
      priority: "f_04",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Adjustment to recent motherhood",
      priority: "f_05",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Adjustment to recent fatherhood",
      priority: "f_06",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Parenting anxieties and concerns",
      priority: "f_07",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Adjustment to separation from child/children",
      priority: "f_08",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Work life and occupational concerns",
      priority: "f_09",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with stress at school",
      priority: "f_10",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Adjustment to university life and studies",
      priority: "f_11",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Adjustment to new demands in life situation",
      priority: "f_12",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Adaptation to physical illness",
      priority: "f_13",
      tabType: TabTypes.ADJUSTMENT,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },

    {
      value: "Management and coping with daily life",
      priority: "g_01",
      tabType: TabTypes.DAILY_LIFE,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Structuring of daily life and making plans for the future",
      priority: "g_02",
      tabType: TabTypes.DAILY_LIFE,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with day to day organizational difficulties",
      priority: "g_03",
      tabType: TabTypes.DAILY_LIFE,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Dealing with self-care (e.g. eating, sleeping, finances)",
      priority: "g_04",
      tabType: TabTypes.DAILY_LIFE,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Dealing with inhibition regarding help seeking and turning to others for support",
      priority: "g_05",
      tabType: TabTypes.DAILY_LIFE,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Working towards the future development of hobbies and constructive leisure activities",
      priority: "g_06",
      tabType: TabTypes.DAILY_LIFE,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Working toward improved health lifestyle",
      priority: "g_07",
      tabType: TabTypes.DAILY_LIFE,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "How to create better balance between work, relational,recreational and self needs",
      priority: "g_08",
      tabType: TabTypes.DAILY_LIFE,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Development of greater self-assertion and affective interpersonal communication",
      priority: "g_09",
      tabType: TabTypes.DAILY_LIFE,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },

    {
      value: "Dealing with panic and anxiety states",
      priority: "h_01",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration and coping with depression and sadness",
      priority: "h_02",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Awareness and exploration of feelings of powerlessness, hopelessness and helplessness",
      priority: "h_03",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of capacity to return to work",
      priority: "h_04",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with and management of impulses to self-mutilate",
      priority: "h_05",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with and management of impulses to self-harm",
      priority: "h_06",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with suicidal thoughts and feelings",
      priority: "h_07",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with exhaustion and burn out in work activities",
      priority: "h_08",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Working through and management of anorectic behavior and related difficulties",
      priority: "h_09",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Coping and working through of difficulties with chronic binge eating",
      priority: "h_10",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and coping with obesity and related eating difficulties",
      priority: "h_11",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with and working through of drug dependence and abuse",
      priority: "h_12",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Coping with and working through of addictions and related concerns",
      priority: "h_13",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of sleep disturbances",
      priority: "h_14",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Coping with sexual difficulties",
      priority: "h_15",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Awareness and exploration of possible psychosomatic reactions",
      priority: "h_16",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Awareness and exploration of perfectionistic tendencies",
      priority: "h_17",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Issues of impulse control and containment of affects",
      priority: "h_18",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Awareness and exploration of underlying aggression and feelings of rage",
      priority: "h_19",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration and working through of guilt and related anxieties",
      priority: "h_20",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Exploration of anxieties regarding healthy dependency",
      priority: "h_21",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Awareness and exploration of underlying needs for care and support",
      priority: "h_22",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Awareness and exploration of tendency to push others away in response to anxiety",
      priority: "h_23",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and working through of difficulties with trust in interpersonal relationships",
      priority: "h_24",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Development of internal self-care and maternal self-function",
      priority: "h_25",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Development of working towards greater creativity and capacity for play",
      priority: "h_26",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Development of expanded spiritual and creative experience",
      priority: "h_27",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },
    {
      value:
        "Development of occupational functioning and pleasure in work activities",
      priority: "h_28",
      tabType: TabTypes.SPECIFIC_DIFFICULTIES,
      stage: Stages.SUBJECTIVE,
      systemGenerated: true,
    },

    {
      value: "Generally positive",
      priority: "a_01",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Generally content",
      priority: "a_02",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Calm and reflective",
      priority: "a_03",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Improved,",
      priority: "a_04",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Mainly unhappy",
      priority: "a_05",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Sad",
      priority: "a_06",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Dysphoric",
      priority: "a_07",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Rather distressed ",
      priority: "a_08",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Mildly depressed ",
      priority: "a_09",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Moderately depressed ",
      priority: "a_10",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Quite depressed",
      priority: "a_11",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Marked by hopelessness ",
      priority: "a_12",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Withdrawn ",
      priority: "a_13",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Detached",
      priority: "a_14",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Somewhat flat",
      priority: "a_15",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Rather flat",
      priority: "a_16",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Nervous",
      priority: "a_17",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Mildly anxious",
      priority: "a_18",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Moderately anxious ",
      priority: "a_19",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Quite anxious",
      priority: "a_20",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Panicky",
      priority: "a_21",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Agitated",
      priority: "a_22",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Over-stimulated",
      priority: "a_23",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Excited",
      priority: "a_24",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Irritable",
      priority: "a_25",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Angry",
      priority: "a_26",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Enraged",
      priority: "a_27",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Regressed",
      priority: "a_28",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Uncontained",
      priority: "a_29",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Labile",
      priority: "a_30",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Tired",
      priority: "a_31",
      tabType: TabTypes.AFFECTIVE_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },

    {
      value: "Changes in memory function",
      priority: "b_01",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Memory loss and impairment",
      priority: "b_02",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Deficits in attention and concentration",
      priority: "b_03",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Disorientation and visuospatial deficits",
      priority: "b_04",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "A decline in general cognitive/intellectual functioning ",
      priority: "b_05",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Poor capacity for reflection",
      priority: "b_06",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Impaired judgment",
      priority: "b_07",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Concrete thinking with little capacity for abstraction",
      priority: "b_08",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "A slowing of thought and reduced mental energy",
      priority: "b_09",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Inhibition of thought and associations",
      priority: "b_10",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Obsessional preoccupation",
      priority: "b_11",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Dissociative features",
      priority: "b_12",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Phobic preoccupation ",
      priority: "b_13",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "A post-traumatic reaction",
      priority: "b_14",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "A potential underlying psychotic process",
      priority: "b_15",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Some deficits in reality testing",
      priority: "b_16",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Serious reality testing deficits",
      priority: "b_17",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Confusion and disorganization of thought",
      priority: "b_18",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Hypomanic features",
      priority: "b_19",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Manic features",
      priority: "b_20",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },
    {
      value: "Suicidal indeation/fantasy ",
      priority: "b_21",
      tabType: TabTypes.MENTAL_STATE,
      stage: Stages.OBJECTIVE,
      systemGenerated: true,
    },

    {
      value:
        "Appeared to be superior in most areas of life with no indication of emotional difficulty or distress. The client is not overwhelmed by life’s difficulties and is able to enjoy relationships, work/school, and leisure time",
      priority: "a_01",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Appeared to be very good with minimal difficulties and is able to cope well in most areas of life (e.g., relationships, work/school, and leisure time). The client is quite content and reports only everyday difficulties and concerns",
      priority: "a_02",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Appeared reasonably good with only short lived and expectable reactions to everyday stressful events. The client shows only slight difficulty in relational and/or work/school functioning",
      priority: "a_03",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Indicates a mild level of difficulty with some problems in relationships, work, or school functioning. The client is nonetheless functioning well and has some significant relationships",
      priority: "a_04",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Suggests a moderate degree of emotional distress and difficulty dealing with relationships, work and/or school life.",
      priority: "a_05",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Suggests some serious emotional and psychological difficulties and quite serious impairment in relational, work, and/or school functioning.",
      priority: "a_06",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Indicates the client is struggling with very serious emotional and psychological difficulties that appear to be interfering with perceptions, judgment, thinking,communication and mood. The client is showing severe difficulties in most areas of life (e.g., relationships, work, and school life). ",
      priority: "a_07",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Suggests that the client’s behavior may be influenced by severe psychological difficulties, delusions and/or hallucinations and is unable to function in most areas of life (e.g., self-care, relationships, work life).",
      priority: "a_08",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Suggests that the client is in some danger of harm to self or others and is having serious difficulty maintaining self-care and basic life functioning.",
      priority: "a_09",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Suggests that there is a persistent and serious risk of causing harm to self or others.",
      priority: "a_10",
      tabType: TabTypes.GLOBAL_ASSESSMENT,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },

    {
      value:
        "The client continues to make good progress with self-understanding and self-insight",
      priority: "c_01",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "There is continued development of the client’s capacity for self-care and life management",
      priority: "c_02",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Developments continue in the areas of family and relational functioning",
      priority: "c_03",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Developments continue in the areas of occupational functioning and achievement",
      priority: "c_04",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Developments continue in the areas of healthy separation and interdependence",
      priority: "c_05",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Developments continue in the area of containment of harmful acting out behavior",
      priority: "c_06",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "The client continues to make steady gains in self-esteem and confidence",
      priority: "c_07",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "The client shows reductions in self-destructive behavior",
      priority: "c_08",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "A significant reduction in symptoms continues",
      priority: "c_09",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "The client demonstrates a growing capacity for greater pleasure in relationships and work",
      priority: "c_10",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "The client demonstrates a capacity for greater enjoyment of leisure time and creative Projects",
      priority: "c_11",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "The client shows an enhanced capacity for parenting and improved parenting of children",
      priority: "c_12",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Significant gains in self-assertion and positive self-promotion are evident",
      priority: "c_13",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "The client displays enhanced feelings of appropriate entitlement",
      priority: "c_14",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Continued improvement in maintenance and respect of boundaries is evident",
      priority: "c_15",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "The client continues to sustain gains made in reduced self-destructive behavior",
      priority: "c_16",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "Developments are being made in the therapeutic alliance",
      priority: "c_17",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "The client continues in process of reworking and alleviation of traumatic pain and stress",
      priority: "c_18",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "The client continues to gain greater mastery over traumatic stimuli and fears",
      priority: "c_19",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "The client demonstrates greater capacity for thought and reflection",
      priority: "c_20",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "The recent crisis appears better contained",
      priority: "c_21",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "The client is in a regressive period",
      priority: "c_22",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Recent crisis appears to be overwhelming client’s current resources",
      priority: "c_23",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "Planning for termination is underway",
      priority: "c_24",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "Termination process is in progress",
      priority: "c_25",
      tabType: TabTypes.SIGNIFICANT_DEVELOPMENTS,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },

    {
      value: "There are no current outstanding therapeutic issues or concerns",
      priority: "d_01",
      tabType: TabTypes.OUTSTANDING_ISSUES,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Self-destructive wishes and behaviors remain a significant therapeutic concerns",
      priority: "d_02",
      tabType: TabTypes.OUTSTANDING_ISSUES,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "Resistance to treatment recommendations remains a significant concern",
      priority: "d_03",
      tabType: TabTypes.OUTSTANDING_ISSUES,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value:
        "The client’s help-rejecting behaviors remain a significant therapeutic concern",
      priority: "d_04",
      tabType: TabTypes.OUTSTANDING_ISSUES,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "The client’s current regressive state is a significant concern",
      priority: "d_05",
      tabType: TabTypes.OUTSTANDING_ISSUES,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "Treatment continues to show good evolution and development",
      priority: "d_06",
      tabType: TabTypes.OUTSTANDING_ISSUES,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },
    {
      value: "Treatment to continue as indicated",
      priority: "d_07",
      tabType: TabTypes.OUTSTANDING_ISSUES,
      stage: Stages.ASSESSMENT,
      systemGenerated: true,
    },

    {
      value: "Supportive techniques",
      priority: "a_01",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "An emphasis on the establishment and maintenance of the therapeutic frame",
      priority: "a_02",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on self-expression",
      priority: "a_03",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on here-and-now functioning",
      priority: "a_04",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on containment of affect",
      priority: "a_05",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on coping with current life difficulties",
      priority: "a_06",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "A focus on improved problem solving and coping",
      priority: "a_07",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on containment of recent regressive episode",
      priority: "a_08",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "A focus in impulse control and anger management",
      priority: "a_09",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "A focus on containment of acting-out behavior",
      priority: "a_10",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "A focus on helping the client expand problem solving capacities",
      priority: "a_11",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on strengthening self-care and self-preservation",
      priority: "a_12",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Developing and maintaining the therapeutic alliance so as to facilitate and foster continued therapeutic work",
      priority: "a_13",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "An emphasis on helping the client develop greater trust in the therapeutic setting, therapist and interpersonal relationships",
      priority: "a_14",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on clinical management",
      priority: "a_15",
      tabType: TabTypes.SUPPORTIVE,
      stage: Stages.PLAN,
      systemGenerated: true,
    },

    {
      value: "Cognitive-behavioral techniques",
      priority: "b_01",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "An emphasis on evaluation and exploration of problematic automatic thoughts",
      priority: "b_02",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Setting goals for the treatment and the session",
      priority: "b_03",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Targeting more effective problem solving and coping skills in daily life",
      priority: "b_04",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on addressing problematic core beliefs",
      priority: "b_05",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Evaluating and addressing dysfunctional thoughts",
      priority: "b_06",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Reviewing and exploring homework assignments",
      priority: "b_07",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "A review of therapeutic work done in previous sessions",
      priority: "b_08",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An evaluation and exploration of target issues and behaviors",
      priority: "b_09",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "An exploration of feedback regarding previous sessions an emphasis on developing new homework assignments in collaboration with the client",
      priority: "b_10",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Addressing the impact of current medications and their effects",
      priority: "b_11",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "The use of role play to help address interpersonal difficulties",
      priority: "b_12",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "The use of imagery to address fears and anxieties",
      priority: "b_13",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "The use of mindfulness to address fears and anxieties",
      priority: "b_14",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and planning of in vivo exposure to help address anxieties and fears",
      priority: "b_15",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Helping overcome overgeneralization in client’s cognitive style",
      priority: "b_16",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Addressing the client’s tendency towards all-or-nothing thinking and how this impacts perceptions",
      priority: "b_17",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Addressing and working to help client overcome the tendency to engage in catastrophization",
      priority: "b_18",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of underlying schemas that impact thought and behavior",
      priority: "b_19",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "An emphasis on the implementation and practice of relaxation techniques",
      priority: "b_20",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on self-monitoring of thought, emotions and behavior",
      priority: "b_21",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Socratic questioning or guided discovery to help client address mental style",
      priority: "b_22",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Providing client feedback regarding development and progress",
      priority: "b_23",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "An emphasis on techniques for the development of greater confidence and self-efficacy",
      priority: "b_24",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "An emphasis on self-assertiveness and confidence building",
      priority: "b_25",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "An emphasis on the development of greater mindfulness and reflective capacity",
      priority: "b_26",
      tabType: TabTypes.COGNITIVE_BEHAVIORAL,
      stage: Stages.PLAN,
      systemGenerated: true,
    },

    {
      value: "Psychodynamic techniques",
      priority: "c_01",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Exploration of fantasy life in light of ongoing anxieties",
      priority: "c_02",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Exploration and interpretation of the transference",
      priority: "c_03",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of fears of abandonment and loss as experienced in the transference",
      priority: "c_04",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of fears of rejection and judgment as experienced in the transference",
      priority: "c_05",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Exploration of the clients underlying sexual wishes and desires",
      priority: "c_06",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of the clients underlying aggressive wishes and impulses Psychodynamic listening",
      priority: "c_07",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretation of anxieties regarding boundaries within the therapeutic relationship and the frame",
      priority: "c_08",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretation of dream material with emphasis on awareness of underlying anxieties, desires and wishes",
      priority: "c_09",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Interpretation of dream material with emphasis on manifest content and its relationship to current conflicts and concerns",
      priority: "c_10",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Exploration and interpretations regarding possible resistances",
      priority: "c_11",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretations of underlying sexual anxieties and conflicts",
      priority: "c_12",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretations of underlying anxieties regarding loss and abandonment",
      priority: "c_13",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretations of underlying anxieties regarding fear of punishment physical threat and/or retaliation",
      priority: "c_14",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretations of underlying anxieties regarding destructive impulses and wishes",
      priority: "c_15",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretations of underlying annihilation anxiety and fears of fragmentation",
      priority: "c_16",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretations of underlying fears of engulfment and merger in relationships",
      priority: "c_17",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Addressing defenses against aggressive impulses and desires",
      priority: "c_18",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Addressing defenses against sexual impulses and desires",
      priority: "c_19",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretations of inhibitions regarding pleasure and enjoyment",
      priority: "c_20",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretation of inhibitions regarding self-expression and assertion",
      priority: "c_21",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "An emphasis on support of defenses to contains impulses life and regressive experiences",
      priority: "c_22",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "An emphasis on support of defenses to help contain regressive experiences",
      priority: "c_23",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretation of possible physical manifestation of psychic conflict and pain",
      priority: "c_24",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretation of psychosomatic concerns and difficulties",
      priority: "c_25",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of self-destructive motives underlying acting-out behavior",
      priority: "c_26",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of suicidal impulses in relation to unacknowledged and unmetabolized anger",
      priority: "c_27",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration and interpretation of projections that are interfering with social and interpersonal functioning",
      priority: "c_28",
      tabType: TabTypes.PSYCHOANALYTIC,
      stage: Stages.PLAN,
      systemGenerated: true,
    },

    {
      value:
        "Discussion and establishment of plans and goals for better self-care",
      priority: "d_01",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "A focus on anger management and containment of acting out",
      priority: "d_02",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Exploration of appropriate interaction and communication",
      priority: "d_03",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Focus on social skills enhancement and practice",
      priority: "d_04",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Discussion of better life management and organization of projects",
      priority: "d_05",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Exploration of means of overcoming phobic avoidance",
      priority: "d_06",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Psychoeducation and instruction regarding panic attacks and their management",
      priority: "d_07",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Instruction and management of sleep difficulties",
      priority: "d_08",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Supportive psychoeducation regarding recovery from psychosis",
      priority: "d_09",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Discussion of plans for drug cessation",
      priority: "d_10",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Continues psychoeducation on addiction and their management",
      priority: "d_11",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Discussion of ways of managing current crisis situations",
      priority: "d_12",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Establishing a plan of how to proceed in the event of a suicidal crisis",
      priority: "d_13",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Informing the client of how to reach me by telephone between sessions as needed",
      priority: "d_14",
      tabType: TabTypes.OTHER_INTERVENTIONS,
      stage: Stages.PLAN,
      systemGenerated: true,
    },

    {
      value:
        "Continued support and maintenance of the psychotherapeutic process",
      priority: "e_01",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Continues support of client’s self-exploration and understanding",
      priority: "e_02",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Suppose of the client’s capacity for thought and reflection",
      priority: "e_03",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Building and development of safety and trust in the relationship with the therapist",
      priority: "e_04",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Therapeutic work on building and maintaining self-esteem and self-confidence",
      priority: "e_05",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Therapeutic work on building and maintaining self-care",
      priority: "e_06",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Therapeutic interventions aimed at helping with target behaviors and difficulties",
      priority: "e_07",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Addressing problematic coping strategies and mechanisms",
      priority: "e_08",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Building greater social and interpersonal skills",
      priority: "e_09",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Adjunctive psychiatric/medical assistance",
      priority: "e_10",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Continued exploration of meaning and significance of client’s difficulties in light of past",
      priority: "e_11",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Relational patterns and underlying belief",
      priority: "e_12",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Continues exploration of current life difficulties in light of early childhood experiences",
      priority: "e_13",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Helping the client gain greater awareness of underlying fantasies wishes and fear",
      priority: "e_14",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Exploration of meaning and significance of client’s difficulties in light of psychic conflict",
      priority: "e_15",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Better understanding the impact of the client’s traumatic history on present day relationships and experiences",
      priority: "e_16",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Helping client gain greater awareness, understanding and expression of underlying emotions",
      priority: "e_17",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Therapeutic work on understanding, containment and management of self-destructive impulses and/or behaviors",
      priority: "e_18",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Management of uncontained affects and drive states",
      priority: "e_19",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value:
        "Support of the client’s adaptive defenses and/or coping capacities",
      priority: "e_20",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Crisis management and supportive assistance",
      priority: "e_21",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Movement towards termination of the treatment process",
      priority: "e_22",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
    {
      value: "Planning for termination of the treatment",
      priority: "e_23",
      tabType: TabTypes.ONGOING_PLAN,
      stage: Stages.PLAN,
      systemGenerated: true,
    },
  ];

  for (var item of items) {
    promiseList.push(createMedicalPhrases(item));
  }

  const newTypes = await Promise.all(promiseList);
  return [newTypes];
}

async function createMedicalPhrases(phrase: DMedicalPhrase) {
  const isExistingType = await MedicalPhraseDao.getMedicalPhrase(phrase);

  if (isExistingType.length > 0) {
    return isExistingType;
  }

  return await MedicalPhraseDao.createMedicalPhrase(phrase);
}
