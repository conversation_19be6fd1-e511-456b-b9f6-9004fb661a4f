import { AdminDao } from "../dao/admin-dao";
import { DProfession } from "../models/profession-model";

export default async function seedProfessions() {
  var promiseList: any[] = [];
  const items: DProfession[] = [
    {
      name: "Counselors, Clinicians, Therapists",
      disabled: false,
    },
    {
      name: "Clinical Social Workers",
      disabled: false,
    },
    {
      name: "Psychiatrists",
      disabled: false,
    },
    {
      name: "Psychiatric Or Mental Health Nurse Practitioners",
      disabled: false,
    },
    {
      name: "Family Nurse Practitioners",
      disabled: false,
    },
    {
      name: "Certified Peer Specialists",
      disabled: false,
    },
    {
      name: "Life Coach",
      disabled: false,
    }
  ];

  for (var item of items) {
    promiseList.push(createProfession(item));
  }

  const newTypes = await Promise.all(promiseList);
  return [newTypes];
}

async function createProfession(professionDetails: DProfession) {
  const existingType = await AdminDao.getProfessionByName(
    professionDetails.name
  );
  if (existingType) {
    return existingType;
  }
  return await AdminDao.addProfession(professionDetails);
}
