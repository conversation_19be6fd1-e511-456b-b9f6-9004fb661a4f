import { DExperienceMainTags } from "../models/experience-main-tag-model";
import ExperienceMainTags from "../schemas/experience-main-tag-schema";

export default async function therapistScoreMainTagsSeed() {
    const mainTags = [
        "Anxiety Disorders",
        "Depressive Disorders",
        "Self-Care / Wellness",
        "Trauma and Stressor Related Disorders",
        "Relationship Issues",
        "Personality Disorders",
        "Substance Related and Addictive Disorders",
        "Neurodevelopmental Disorders",
        "Obsessive-Compulsive and Related Disorders",
        "Sleep-Wake Disorders",
        "Disruptive, Impulse-Control, and Conduct Disorders",
        "Feeding and Eating Disorders",
        "Bipolar and Related Disorders",
        "NeuroCognitive Disorders",
        "Schizophrenia Spectrum and Other Psychotic Disorders",
        "Sexual Dysfunctions",
        "Gender Dysphoria"
    ];

    try {
        // Clear all data in the therapistscoremaintags collection
        const allmainTags = await ExperienceMainTags.find();
        if(allmainTags.length > 0){
            console.log("Already have data in experiencemaintags table");
            
            return;
        }
        await ExperienceMainTags.deleteMany({});
        console.log('Cleared all data in ExperienceMainTags collection.');

        // Prepare the data for insertMany
        const mainTagDocs = mainTags.map(tag => ({ mainTagName: tag }));

        // Insert the new data using insertMany
        const response = await ExperienceMainTags.insertMany(mainTagDocs);
        console.log('Successfully added main tags:', response);
    } catch (error) {
        console.error('Error during seeding:', error);
    }
}