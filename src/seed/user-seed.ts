import { UserDao } from "../dao/user-dao";
import { UserRole, UserStatus } from "../models/user-model";

export default async function seedUsers() {
    let userPromises: any[] = [];
    
    const userList: any[] = [
        {
            email: "<EMAIL>",
            password: "abc123",
            role: UserRole.THERAPIST,
            verifiedStatus: UserStatus.VERIFIED,
        },
        {
            email: "<EMAIL>",
            password: "abc123",
            role: UserRole.CLIENT,
            verifiedStatus: UserStatus.VERIFIED,
        }
    ];

    for (let user of userList) {
       userPromises.push(createUser(user.email, user.password, user.role, user.verifiedStatus));
    }

    const newUsers = await Promise.all(userPromises);
    return [newUsers];
}


async function createUser(email: string, password: string, role: string, verifiedStatus: string) {
    const existingUser = await UserDao.getUserByEmail(email);
    
    if (existingUser) {
        return existingUser;
    }

    return await UserDao.signUpWithEmail(email, password, role, verifiedStatus);
}
