import { AdminDao } from "../dao/admin-dao";

export default async function seedExperienceTag() {
  var promiseList: any[] = [];
  
  const items: any[] = [
    {
      experienceTag: "Addictions",
    },
    {
      experienceTag: "Agoraphobia",
    },
    {
      experienceTag: "Antisocial Personality Disorder",
    },
    {
      experienceTag: "Anxiety Disorders",
    },
    {
      experienceTag: "Asperger’s Syndrome",
    },
    {
      experienceTag: "Attention Deﬁcit Hyperactivity Disorder (ADHD)",
    },
    {
      experienceTag: "Autism Spectrum Disorder",
    },
    {
      experienceTag: "Bipolar Disorders",
    },
    {
      experienceTag: "Bipolar II Disorder",
    },
    {
      experienceTag: "Cyclothymic Disorder",
    },
    {
      experienceTag: "Hypomanic Episode",
    },
    {
      experienceTag: "Bipolarity",
    },
    {
      experienceTag: "Body Dysmorphic Disorder",
    },
    {
      experienceTag: "Breathing Related Sleep Disorders",
    },
    {
      experienceTag: "Central Sleep Apnea",
    },
    {
      experienceTag: "Obstructive Sleep Apnea Hypopnea",
    },
    {
      experienceTag: "Sleep-Related Hypoventilation",
    },
    {
      experienceTag: "Conduct Disorder",
    },
    {
      experienceTag: "Delusional Disorder",
    },
    {
      experienceTag: "Depression",
    },
    {
      experienceTag: "Disruptive Impulse-Control Disorders",
    },
    {
      experienceTag: "Disruptive Mood Dysregulation Disorder",
    },
    {
      experienceTag: "Dissociative Disorders",
    },
    {
      experienceTag: "Dissociative Identity Disorder (Multiple Personality)",
    },
    {
      experienceTag: "Dysthymia",
    },
    {
      experienceTag: "Anorexia Nervosa",
    },
    {
      experienceTag: "Binge Eating Disorder",
    },
    {
      experienceTag: "Bulimia Nervosa",
    },
    {
      experienceTag: "Elimination Disorders",
    },
    {
      experienceTag: "Encopresis",
    },
    {
      experienceTag: "Enuresis",
    },
    {
      experienceTag: "Excoriation Disorder",
    },
    {
      experienceTag: "Feeding Disorders",
    },
    {
      experienceTag: "Grief",
    },
    {
      experienceTag: "Gambling Disorder",
    },
    {
      experienceTag: "Gender Dysphoria",
    },
    {
      experienceTag: "Generalized Anxiety Disorder",
    },
    {
      experienceTag: "Major Depressive Disorder",
    },
    {
      experienceTag: "Mania",
    },
    {
      experienceTag: "Neurocognitive Disorders",
    },
    {
      experienceTag: "Delirium",
    },
    {
      experienceTag: "Neurocognitive Disorders",
    },
    {
      experienceTag: "Neurodevelopmental Disorders",
    },
    {
      experienceTag: "Obsessive Compulsive Disorder",
    },
    {
      experienceTag: "Panic Disorder",
    },
    {
      experienceTag: "Paraphilic Disorders",
    },
    {
      experienceTag: "Parasomnias",
    },
    {
      experienceTag: "Antisocial Personality Disorder",
    },
    {
      experienceTag: "Avoidant Personality Disorder",
    },
    {
      experienceTag: "Borderline Personality Disorder",
    },
    {
      experienceTag: "Dependent Personality Disorder",
    },
    {
      experienceTag: "Histrionic Personality Disorder",
    },
    {
      experienceTag: "Narcissistic Personality Disorder",
    },
    {
      experienceTag: "Obsessive-Compulsive Personality Disorder",
    },
    {
      experienceTag: "Paranoid Personality Disorder",
    },
    {
      experienceTag: "Schizoid Personality Disorder",
    },
    {
      experienceTag: "Schizotypal Personality Disorder",
    },
    {
      experienceTag: "Post Traumatic Stress Disorder (PTSD)",
    },
    {
      experienceTag: "Premenstrual Dysphoric Disorder",
    },
    {
      experienceTag: "Psychosis",
    },
    {
      experienceTag: "Schizoaﬀective Disorder",
    },
    {
      experienceTag: "Schizophrenia",
    },
    {
      experienceTag: "Separation Anxiety Disorder",
    },
    {
      experienceTag: "Sexual Disorders",
    },
    {
      experienceTag: "Sleep Disorders",
    },
    {
      experienceTag: "Somatic Disorders",
    },
    {
      experienceTag: "Somatic Symptom Disorder",
    },
    {
      experienceTag: "Substance Abuse",
    },
    {
      experienceTag: "Tobacco Use Disorder",
    },
    {
      experienceTag: "Trauma-Related Disorders",
    }


  ];

  for (var item of items) {
    promiseList.push(createExperienceTags(item.experienceTag));
  }

  const newTypes = await Promise.all(promiseList);
  return [newTypes];
}

async function createExperienceTags(expTag: string) {
  const existingType = await AdminDao.getExperienceTag(expTag);
  if (existingType) {
    return existingType;
  }
  return await AdminDao.addExperienceTag(expTag);
}
