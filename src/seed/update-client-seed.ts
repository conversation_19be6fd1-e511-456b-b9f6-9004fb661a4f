import { AdminDao } from "../dao/admin-dao";
import { EthnicityDao } from "../dao/ethnicity-dao";
import { UserDao } from "../dao/user-dao";
import { UserRole, UserStatus } from "../models/user-model";

export default async function seedClientsUpdate() {
    let userPromises: any[] = [];

    const userList = await AdminDao.getAllClientsSeed();

    for (let user of userList) {
        if (user.incognito) {
            userPromises.push(UserDao.updateClientIncognitoPopupShow(user._id));
        } else {
            userPromises.push(UserDao.updateClientIncognitoPopupShowFalse(user._id));
        }
    }

    let list = await Promise.all(userPromises);
    
    return list;
}
