import { EthnicityDao } from "../dao/ethnicity-dao";
import { UserDao } from "../dao/user-dao";
import { UserRole, UserStatus } from "../models/user-model";

export default async function seedUpdate() {
  let userPromises: any[] = [];
  let ethnicity = await getEthnicity();
  
  const userList: any[] = [
    {
      email: "<EMAIL>",
      data: {
        firstname: "<PERSON><PERSON>",
        lastname: "<PERSON>",
        dateOfBirth: "2000-10-01",
        gender: "Female",
        ethnicityId: ethnicity._id,
      },
    },
    {
      email: "<EMAIL>",
      data: {
        firstname: "<PERSON>",
        lastname: "Matt<PERSON>",
        dateOfBirth: "2000-10-01",
        gender: "Female",
        ethnicityId: ethnicity._id,
      },
    },
  ];

  for (let user of userList) {
    userPromises.push(UserDao.updateUserByEmail(user.email, user.data));
  }

  let list = await Promise.all(userPromises);
  
  return list;
}

async function getEthnicity() {
  const ethnicityList = await EthnicityDao.getEthnicityByName("White");
  return ethnicityList;
}
