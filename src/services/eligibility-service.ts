import { Types } from "mongoose";
import { IClient, PremiumStatus } from "../models/client-model";
import { InsuranceDao } from "../dao/insurance-dao";
import { AdminDao } from "../dao/admin-dao";
import { VideoChatEP } from "../end-point/video-chat-ep";
import { Tradingpartner_ServiceId } from "../models/insurance-model";
import * as moment from "moment";
import { AppLogger } from "../common/logging";
import { ClientDao } from "../dao/client-dao";
import Insurance from "../schemas/insurance-schema";
import { getStateNameFromAbbreviation } from "../common/state-utils";
import { EmailService } from "../mail/config";

/**
 * Kiểm tra eligibility của bảo hiểm cho một client
 * @param client Thông tin client cần kiểm tra
 * @returns Kết quả kiểm tra eligibility
 */
export async function checkClientEligibility(client: IClient) {
  try {
    AppLogger.info(`Starting eligibility check for client ${client._id} (${client.firstname} ${client.lastname})`);
    const currentDate = new Date();
    const results = {
      success: false,
      primaryInsurance: {
        status: "",
        error: "",
        updated: false,
        eligibilityInfoData: null
      },
      secondaryInsurance: {
        status: "",
        error: "",
        updated: false
      },
      premiumStatusUpdated: false,
      clientAddressUpdated: false
    };

    // Kiểm tra bảo hiểm phụ nếu có
    if (client?.secondaryInsuranceId) {
      try {
        // Lấy thông tin secondary insurance
        const secondaryInsurance = await InsuranceDao.getInsurancePlanById(client.secondaryInsuranceId);
        if (secondaryInsurance) {
          // Lấy thông tin công ty bảo hiểm của secondary
          const secondaryIsFound = await AdminDao.getInsuranceCompanyById(secondaryInsurance?.insuranceCompanyId);
          const secondaryInsurancePartner = secondaryIsFound?.insuranceCompany as keyof typeof Tradingpartner_ServiceId;

          // Tạo eligibility payload cho secondary insurance
          const secondaryEligibilityPayload = {
            "AccountKey": "18420_X07Qzhib2EmpXA42arGNCR6e",
            "ins_name_f": client?.firstname,
            "ins_name_l": client?.lastname,
            "payerid": Tradingpartner_ServiceId[secondaryInsurancePartner],
            "pat_rel": "18",
            "fdos": moment.utc(currentDate).format('YYYYMMDD'),
            "ins_dob": moment.utc(client?.dateOfBirth).format('YYYYMMDD'),
            "ins_sex": (client?.gender == "Male" ? "M" : "F"),
            "ins_number": secondaryInsurance?.subscriber?.memberId,
            "prov_npi": "**********"
          };

          // Gọi check eligibility cho secondary insurance
          const secondaryEligibilityResult = await VideoChatEP.checkClaimMdEligibility2(secondaryEligibilityPayload, client._id);
          AppLogger.info(`Secondary insurance eligibility check for client ${client._id}: ${secondaryEligibilityResult?.finalStatus}`);
          results.secondaryInsurance.status = secondaryEligibilityResult?.finalStatus || "";
          results.secondaryInsurance.error = secondaryEligibilityResult?.finalError || "";

          // Nếu secondary insurance ACTIVE thì update thông tin vào bảng insurance
          if (secondaryEligibilityResult?.finalStatus === "ACTIVE") {
            const secondaryEligibilityInfo = secondaryEligibilityResult?.eligibilityInfoData;
            try {
              const secondaryInsuranceRecord = await InsuranceDao.getInsurancePlanById(client.secondaryInsuranceId);
              if (secondaryInsuranceRecord) {
                const directMongoDBUpdate: Record<string, any> = {};
                if (secondaryEligibilityInfo.ins_number) {
                  directMongoDBUpdate['subscriber.memberId'] = secondaryEligibilityInfo.ins_number;
                }
                if (secondaryEligibilityInfo.ins_name_f) {
                  directMongoDBUpdate['subscriber.firstName'] = secondaryEligibilityInfo.ins_name_f;
                }
                if (secondaryEligibilityInfo.ins_name_l) {
                  directMongoDBUpdate['subscriber.lastName'] = secondaryEligibilityInfo.ins_name_l;
                }
                if (secondaryEligibilityInfo.ins_addr_1 || secondaryEligibilityInfo.pat_addr_1) {
                  directMongoDBUpdate['subscriber.address.address1'] = secondaryEligibilityInfo.ins_addr_1 || secondaryEligibilityInfo.pat_addr_1;
                }
                if (secondaryEligibilityInfo.ins_city || secondaryEligibilityInfo.pat_city) {
                  directMongoDBUpdate['subscriber.address.city'] = secondaryEligibilityInfo.ins_city || secondaryEligibilityInfo.pat_city;
                }
                if (secondaryEligibilityInfo.ins_state || secondaryEligibilityInfo.pat_state) {
                  const stateAbbr = secondaryEligibilityInfo.ins_state || secondaryEligibilityInfo.pat_state;
                  const fullStateName = getStateNameFromAbbreviation(stateAbbr);
                  directMongoDBUpdate['subscriber.address.state'] = fullStateName;
                }
                if (secondaryEligibilityInfo.ins_zip || secondaryEligibilityInfo.pat_zip) {
                  directMongoDBUpdate['subscriber.address.postalCode'] = secondaryEligibilityInfo.ins_zip || secondaryEligibilityInfo.pat_zip;
                }
                if (!secondaryInsuranceRecord.subscriber.address && Object.keys(directMongoDBUpdate).some(key => key.startsWith('subscriber.address.'))) {
                  await Insurance.updateOne(
                    { _id: secondaryInsuranceRecord._id },
                    { $set: { 'subscriber.address': {} } }
                  );
                }
                if (Object.keys(directMongoDBUpdate).length > 0) {
                  await Insurance.updateOne(
                    { _id: secondaryInsuranceRecord._id },
                    { $set: directMongoDBUpdate },
                    { upsert: false }
                  );
                  results.secondaryInsurance.updated = true;
                  AppLogger.info(`Updated secondary insurance record ${secondaryInsuranceRecord._id} with eligibility data (using ${secondaryEligibilityInfo.ins_addr_1 ? 'ins_' : 'pat_'} fields)`);
                }
              }
            } catch (err) {
              AppLogger.error(`Error updating secondary insurance with eligibility data for client ${client._id}:`, err);
            }
          } else {
            // Send notification email to admins (<EMAIL>, <EMAIL>)
            try {
              const adminEmails = ["<EMAIL>", "<EMAIL>"];
              const subject = `Secondary Insurance eligibility error for client ${client?.firstname || ''} ${client?.lastname || ''}`;
              const body = `Secondary Insurance of user <b>${client?.firstname || ''} ${client?.lastname || ''} (${client?.email || ''})</b> has an eligibility issue.<br><br>Reason: <b>${secondaryEligibilityResult?.finalError || 'Unknown error'}</b><br><br>Please check and update copayment if necessary.`;
              for (const email of adminEmails) {
                await EmailService.sendEmailToSelectedUsers(email, body, subject);
              }
            } catch (adminMailError) {
              AppLogger.error("Error sending insurance eligibility error email to admins:", adminMailError);
            }
          }
        }
      } catch (err) {
        AppLogger.error(`Error processing secondary insurance eligibility for client ${client._id}:`, err);
      }
    }

    // Kiểm tra bảo hiểm chính
    const insurance = await InsuranceDao.getInsurancePlanById(client?.insuranceId);
    if (!insurance?.insuranceCompanyId) {
      AppLogger.error(`No insurance company ID found for client ${client._id}`);
      return { 
        ...results, 
        primaryInsurance: { 
          ...results.primaryInsurance, 
          error: "No insurance company ID found" 
        }
      };
    }

    const isFound = await AdminDao.getInsuranceCompanyById(insurance?.insuranceCompanyId);
    if (!isFound?.insuranceCompany) {
      AppLogger.error(`Insurance company not found for client ${client._id}`);
      return { 
        ...results, 
        primaryInsurance: { 
          ...results.primaryInsurance, 
          error: "Insurance company not found" 
        }
      };
    }
    
    const insurancePartner = isFound?.insuranceCompany as keyof typeof Tradingpartner_ServiceId;

    const eligibility_payload_md = {
      "AccountKey": "18420_X07Qzhib2EmpXA42arGNCR6e",
      "ins_name_f": client?.firstname,
      "ins_name_l": client?.lastname,
      "payerid": Tradingpartner_ServiceId[insurancePartner],
      "pat_rel": "18",
      "fdos": moment.utc(currentDate).format('YYYYMMDD'),
      "ins_dob": moment.utc(client?.dateOfBirth).format('YYYYMMDD'),
      "ins_sex": (client?.gender == "Male" ? "M" : "F"),
      "ins_number": insurance?.subscriber?.memberId,
      "prov_npi": "**********"
    }
    
    const claimEligibilityAndActiveData = await VideoChatEP.checkClaimMdEligibility(eligibility_payload_md, client?._id);
    results.primaryInsurance.status = claimEligibilityAndActiveData?.finalStatus || "";
    results.primaryInsurance.error = claimEligibilityAndActiveData?.finalError || "";

    if (claimEligibilityAndActiveData?.finalStatus == "ACTIVE") {
      results.success = true;
      const eligibilityInfo = claimEligibilityAndActiveData?.eligibilityInfoData;
      results.primaryInsurance.eligibilityInfoData = eligibilityInfo;
      
      // If client has REVOKED premium status and insurance is now active, restore premium status
      if (client && client.premiumStatus === PremiumStatus.REVOKED) {
        try {
          // Update client's premium status to ACTIVE
          await ClientDao.updateClient(client._id, {
            premiumStatus: PremiumStatus.ACTIVE,
          });
          
          results.premiumStatusUpdated = true;
          AppLogger.info(`Client ${client._id} upgraded to premium due to restored active insurance`);
        } catch (upgradeError) {
          AppLogger.error(`Error during client premium upgrade process for client ${client._id}:`, upgradeError);
        }
      }

      // Update insurance record with the fetched eligibility data
      if (eligibilityInfo && client?.insuranceId) {
        try {
          // Get the insurance record
          const insuranceRecord = await InsuranceDao.getInsurancePlanById(client.insuranceId);
          
          if (insuranceRecord) {
            // Prepare update object for MongoDB
            const directMongoDBUpdate: Record<string, any> = {};
            
            // Update only if values exist in the eligibility response
            if (eligibilityInfo.ins_number) {
              directMongoDBUpdate['subscriber.memberId'] = eligibilityInfo.ins_number;
            }
            
            if (eligibilityInfo.ins_name_f) {
              directMongoDBUpdate['subscriber.firstName'] = eligibilityInfo.ins_name_f;
            }
            
            if (eligibilityInfo.ins_name_l) {
              directMongoDBUpdate['subscriber.lastName'] = eligibilityInfo.ins_name_l;
            }
            
            if (eligibilityInfo.ins_addr_1 || eligibilityInfo.pat_addr_1) {
              directMongoDBUpdate['subscriber.address.address1'] = eligibilityInfo.ins_addr_1 || eligibilityInfo.pat_addr_1;
            }
            
            if (eligibilityInfo.ins_city || eligibilityInfo.pat_city) {
              directMongoDBUpdate['subscriber.address.city'] = eligibilityInfo.ins_city || eligibilityInfo.pat_city;
            }
            
            if (eligibilityInfo.ins_state || eligibilityInfo.pat_state) {
              // Convert state abbreviation to full state name
              const stateAbbr = eligibilityInfo.ins_state || eligibilityInfo.pat_state;
              const fullStateName = getStateNameFromAbbreviation(stateAbbr);
              directMongoDBUpdate['subscriber.address.state'] = fullStateName;
            }
            
            if (eligibilityInfo.ins_zip || eligibilityInfo.pat_zip) {
              directMongoDBUpdate['subscriber.address.postalCode'] = eligibilityInfo.ins_zip || eligibilityInfo.pat_zip;
            }

            // Create address object if it doesn't exist but we have address fields to update
            if (!insuranceRecord.subscriber.address && Object.keys(directMongoDBUpdate).some(key => key.startsWith('subscriber.address.'))) {
              await Insurance.updateOne(
                { _id: insuranceRecord._id },
                { $set: { 'subscriber.address': {} } }
              );
            }

            // Apply the updates if we have any
            if (Object.keys(directMongoDBUpdate).length > 0) {
              await Insurance.updateOne(
                { _id: insuranceRecord._id },
                { $set: directMongoDBUpdate },
                { upsert: false }
              );
              results.primaryInsurance.updated = true;
              AppLogger.info(`Updated insurance record ${insuranceRecord._id} with eligibility data (using ${eligibilityInfo.ins_addr_1 ? 'ins_' : 'pat_'} fields)`);
            }
          }
        } catch (err) {
          AppLogger.error(`Error updating insurance with eligibility data for client ${client._id}:`, err);
        }
      }

      // Update client profile with address information from primary insurance only if fields are empty
      try {
        const userUpdateFields: Record<string, any> = {};
        
        // Add all available fields from eligibility data
        if ((eligibilityInfo.ins_addr_1 || eligibilityInfo.pat_addr_1) && !client.streetAddress) {
          userUpdateFields.streetAddress = eligibilityInfo.ins_addr_1 || eligibilityInfo.pat_addr_1;
        }
        
        if ((eligibilityInfo.ins_city || eligibilityInfo.pat_city) && !client.city) {
          userUpdateFields.city = eligibilityInfo.ins_city || eligibilityInfo.pat_city;
        }
        
        if ((eligibilityInfo.ins_state || eligibilityInfo.pat_state) && !client.state) {
          // Convert state abbreviation to full state name
          const stateAbbr = eligibilityInfo.ins_state || eligibilityInfo.pat_state;
          const fullStateName = getStateNameFromAbbreviation(stateAbbr);
          userUpdateFields.state = fullStateName;
        }
        
        if ((eligibilityInfo.ins_zip || eligibilityInfo.pat_zip) && !client.zipCode) {
          userUpdateFields.zipCode = eligibilityInfo.ins_zip || eligibilityInfo.pat_zip;
        }
        
        // Only update user if we have fields to update
        if (Object.keys(userUpdateFields).length > 0) {
          await ClientDao.updateClient(client._id, userUpdateFields);
          results.clientAddressUpdated = true;
          AppLogger.info(`Updated user ${client._id} address information from insurance eligibility data (using ${eligibilityInfo.ins_addr_1 ? 'ins_' : 'pat_'} fields)`);
        }
      } catch (userUpdateError) {
        AppLogger.error(`Error updating user profile with address data for client ${client._id}:`, userUpdateError);
        // Continue with the process even if user update fails
      }
    }else {
      // Insurance is inactive - handle downgrading client and sending notifications
      try {
        if (client && client.premiumStatus === PremiumStatus.ACTIVE) {
          // // 1. Update client's premium status to REVOKED
          // await ClientDao.updateClient(client._id, {
          //   premiumStatus: PremiumStatus.REVOKED,
          //   premiumMembershipRevokedDate: new Date()
          // });
          
          // 2. Get therapist information for the client (if available)
          // let therapist = null;
          // if (client.primaryTherapist) {
          //   therapist = await TherapistDao.getUserById(client.primaryTherapist);
          // }
          
          // 3. Send email to the therapist if one is assigned
          // if (therapist) {
          //   await EmailService.sendInsuranceInactiveNotificationToTherapist(
          //     therapist.email,
          //     {
          //       therapistName: `${therapist.firstname} ${therapist.lastname}`,
          //       clientName: `${client.firstname} ${client.lastname}`,
          //       errorMessage: claimEligibilityAndActiveData?.finalError || "Insurance verification failed"
          //     }
          //   );
          // }
          
          // // 4. Send email to the client
          // await EmailService.sendInsuranceInactiveNotificationToClient(
          //   client.email,
          //   {
          //     clientName: `${client.firstname} ${client.lastname}`,
          //     errorMessage: claimEligibilityAndActiveData?.finalError || "Insurance verification failed",
          //     loginUrl: "https://mylavni.com/signin/",
          //   }
          // );
          
          AppLogger.info(`Client ${client._id} downgraded from premium due to inactive insurance`);
        }
      } catch (downgradeError) {
        // Log error but still return the original error to the client
        console.error("Error during client downgrade process:", downgradeError);
      }
      
      // Send notification email to admins (<EMAIL>, <EMAIL>)
      try {
        const adminEmails = ["<EMAIL>", "<EMAIL>"];
        const subject = `Insurance eligibility error for client ${client?.firstname || ''} ${client?.lastname || ''}`;
        const body = `Insurance of user <b>${client?.firstname || ''} ${client?.lastname || ''} (${client?.email || ''})</b> has an eligibility issue.<br><br>Reason: <b>${claimEligibilityAndActiveData?.finalError || 'Unknown error'}</b><br><br>Please check and update copayment if necessary.`;
        for (const email of adminEmails) {
          await EmailService.sendEmailToSelectedUsers(email, body, subject);
        }
      } catch (adminMailError) {
        AppLogger.error("Error sending insurance eligibility error email to admins:", adminMailError);
      }
      
      // return res.sendError(claimEligibilityAndActiveData?.finalError);
    }
    
    AppLogger.info(`Primary insurance eligibility check for client ${client._id}: ${claimEligibilityAndActiveData?.finalStatus}`);
    
    return results;
  } catch (error) {
    AppLogger.error(`Error in checkClientEligibility for client ${client?._id}:`, error);
    return {
      success: false,
      primaryInsurance: {
        status: "ERROR",
        error: error.message || "Unknown error in eligibility check",
        updated: false,
        eligibilityInfoData: null
      },
      secondaryInsurance: {
        status: "",
        error: "",
        updated: false
      },
      premiumStatusUpdated: false,
      clientAddressUpdated: false
    };
  }
} 