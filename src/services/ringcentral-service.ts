import { format } from 'date-fns';
import { SDK } from '@ringcentral/sdk';
import { AppLogger } from '../common/logging';

// Simple cache interface for RingCentral API responses
interface CacheItem {
  value: string | null;
  expiry: number;
}

// Cache with 10-minute TTL
const CACHE_TTL_MS = 10 * 60 * 1000; // 10 minutes in milliseconds
const interactionCache = new Map<string, CacheItem>();

// RingCentral API authentication information
const RC_CLIENT_ID = "dNChvmCXQ3jaXWLgvhM0nd";
const RC_CLIENT_SECRET = "06zBcEECihQe2WkyFU3A8D0cQLqmCRpOybJzaeaKAcXN";
const RC_SERVER_URL = "https://platform.ringcentral.com";
const JWT_TOKEN = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

/**
 * Safely get value from an object
 */
function safeGet(obj: any, key: string, defaultValue: any = null): any {
  if (!obj) {
    return defaultValue;
  }
  
  if (typeof obj === 'object' && obj !== null) {
    if (key in obj) {
      return obj[key] !== undefined ? obj[key] : defaultValue;
    }
    
    if (typeof obj.get === 'function') {
      return obj.get(key, defaultValue);
    }
  }
  
  return defaultValue;
}

/**
 * Get the last interaction time with a phone number from RingCentral.
 * Returns null if not found or if there's an error.
 */
export async function getLastInteraction(phoneNumber: string): Promise<string | null> {
  try {
    // Initialize SDK
    const sdk = new SDK({
      server: RC_SERVER_URL,
      clientId: RC_CLIENT_ID,
      clientSecret: RC_CLIENT_SECRET
    });
    
    const platform = sdk.platform();
    
    // Login with JWT
    await platform.login({ jwt: JWT_TOKEN });
    
    let lastInteractionTime: string | null = null;
    
    // ------ Try to get from call-log ------
    try {
      // Get date 90 days ago in ISO format with Z at the end
      const dateFrom = new Date();
      dateFrom.setDate(dateFrom.getDate() - 90);
      const dateFromStr = dateFrom.toISOString().replace('+00:00', 'Z');
      
      const callParams = {
        phoneNumber: phoneNumber,
        dateFrom: dateFromStr,
        perPage: 100
      };
      
      AppLogger.info(`Getting call log data for ${phoneNumber}...`);
      const callResponse = await platform.get('/restapi/v1.0/account/~/extension/~/call-log', callParams);
      const callData = await callResponse.json();
      AppLogger.info('Successfully retrieved call log data');
      
      // Find the most recent call
      const callRecords = safeGet(callData, 'records', []);
      AppLogger.info(`Found ${callRecords.length} calls`);
      
      if (callRecords && callRecords.length > 0) {
        let latestTime: string | null = null;
        
        for (const record of callRecords) {
          const startTime = safeGet(record, 'startTime', '');
          if (startTime && (!latestTime || startTime > latestTime)) {
            latestTime = startTime;
          }
        }
        
        if (latestTime) {
          lastInteractionTime = latestTime;
          AppLogger.info(`Most recent call time: ${latestTime}`);
        }
      }
    } catch (error: any) {
      // Handle error when getting call log
      AppLogger.error(`Error fetching call logs: ${error.message || error}`);
    }
    
    // ------ Try to get from message-store (SMS) if we have permission ------
    try {
      // Get date 90 days ago in ISO format with Z at the end
      const dateFrom = new Date();
      dateFrom.setDate(dateFrom.getDate() - 90);
      const dateFromStr = dateFrom.toISOString().replace('+00:00', 'Z');
      
      const smsParams = {
        phoneNumber: phoneNumber,
        dateFrom: dateFromStr,
        perPage: 100
      };
      
      AppLogger.info(`Getting message data for ${phoneNumber}...`);
      
      // Check permissions before calling API
      try {
        const smsResponse = await platform.get('/restapi/v1.0/account/~/extension/~/message-store', smsParams);
        const smsData = await smsResponse.json();
        AppLogger.info('Successfully retrieved message data');
        
        // Find the most recent message
        const smsRecords = safeGet(smsData, 'records', []);
        AppLogger.info(`Found ${smsRecords.length} messages`);
        
        if (smsRecords && smsRecords.length > 0) {
          let latestTime: string | null = null;
          
          for (const record of smsRecords) {
            const creationTime = safeGet(record, 'creationTime', '');
            if (creationTime && (!latestTime || creationTime > latestTime)) {
              latestTime = creationTime;
            }
          }
          
          // Update if SMS is more recent than call
          if (latestTime) {
            AppLogger.info(`Most recent message time: ${latestTime}`);
            if (!lastInteractionTime || latestTime > lastInteractionTime) {
              lastInteractionTime = latestTime;
              AppLogger.info(`Updated last interaction time to: ${latestTime}`);
            }
          }
        }
      } catch (permissionError: any) {
        if (permissionError.message && permissionError.message.includes('ReadMessages')) {
          AppLogger.info('JWT Token does not have ReadMessages permission, skipping message data');
        } else {
          throw permissionError;
        }
      }
    } catch (error: any) {
      // Handle error when getting message store
      AppLogger.error(`Error fetching message store: ${error.message || error}`);
      AppLogger.info('Continuing with data from call log');
    }
    
    return lastInteractionTime;
  } catch (error: any) {
    AppLogger.error(`Error in getLastInteraction: ${error.message || error}`);
    return null;
  }
}

/**
 * Check if the phone number is valid for RingCentral API
 */
export function isValidPhoneNumber(phoneNumber: string | undefined | null): boolean {
  if (!phoneNumber) return false;
  return /^\+?[0-9]{10,15}$/.test(phoneNumber.replace(/\D/g, ''));
}

/**
 * Get the formatted last interaction date for display
 * Returns the formatted date or null if no interaction is found
 * Uses a cache with 10-minute TTL to avoid API rate limits
 */
export async function getFormattedLastInteraction(phoneNumber: string | undefined | null): Promise<string | null> {
  if (!isValidPhoneNumber(phoneNumber)) {
    AppLogger.warn(`Invalid phone number provided: ${phoneNumber}`);
    return null; // Return null for invalid phone numbers
  }
  
  try {
    // Remove any non-digit characters and ensure only digits remain
    const cleanPhoneNumber = String(phoneNumber).replace(/\D/g, '');
    
    // Check if we have a valid cache entry
    const cacheKey = `phone_${cleanPhoneNumber}`;
    const now = Date.now();
    const cachedItem = interactionCache.get(cacheKey);
    
    if (cachedItem && cachedItem.expiry > now) {
      AppLogger.info(`Using cached interaction data for ${phoneNumber}`);
      return cachedItem.value;
    }
    
    // No cache hit or expired cache, fetch from API
    AppLogger.info(`Fetching fresh interaction data for ${phoneNumber}`);
    const lastInteraction = await getLastInteraction(cleanPhoneNumber);
    
    let result: string | null = null;
    if (lastInteraction) {
      // Format the date as YYYY-MM-DD
      result = lastInteraction.split('T')[0];
    } else {
      AppLogger.info(`No interaction found for ${phoneNumber}, returning null`);
    }
    
    // Store result in cache with 10-minute expiry
    interactionCache.set(cacheKey, {
      value: result,
      expiry: now + CACHE_TTL_MS
    });
    
    return result;
  } catch (error) {
    AppLogger.error(`Error getting formatted last interaction: ${error}`);
    return null; // Return null on errors
  }
}
