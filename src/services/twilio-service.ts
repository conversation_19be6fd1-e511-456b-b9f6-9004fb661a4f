import { config } from 'dotenv';
config();
import { AppLogger } from '../common/logging';

// Cache configuration
const CACHE_TTL_MS = 10 * 60 * 1000; // 10 minutes in milliseconds

// Type for cache entry
interface CacheEntry {
  value: string | null;
  expiry: number;
}

// Simple in-memory cache for interaction data
const interactionCache = new Map<string, CacheEntry>();

// Setting up Twilio API keys
const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID || "**********************************";
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN || "9c6a982b3969e37f4f20875844c4b9b7";

// Import node-fetch
import type { RequestInfo, RequestInit } from 'node-fetch';

// Dynamic import for node-fetch
const fetchModule = async () => await import('node-fetch');

// Type-safe fetch wrapper
const fetch = async (url: RequestInfo, init?: RequestInit) => {
  const { default: nodeFetch } = await fetchModule();
  return nodeFetch(url, init);
};

/**
 * Interface for Twilio message response
 */
interface TwilioMessage {
  sid: string;
  body: string;
  from: string;
  to: string;
  status: string;
  date_sent: string;
  direction: string;
}

/**
 * Interface for the Twilio interaction result
 */
interface TwilioInteractionResult {
  formattedDate: string | null;
  latestMessage?: TwilioMessage;
}

/**
 * TwilioService class to handle all Twilio-related operations
 */
export class TwilioService {
  /**
   * Fetch the last time a phone number interacted with Twilio
   * @param phoneNumber - The phone number to check
   * @returns The formatted date of the last interaction or null if no interactions found
   */
  static async getLastInteractionTime(phoneNumber?: string): Promise<string | null> {
    if (!phoneNumber) {
      return null;
    }
    
    try {
      // Clean the phone number
      const cleanPhoneNumber = String(phoneNumber).replace(/\D/g, '');
      
      // Check if we have a valid cache entry
      const cacheKey = `twilio_${cleanPhoneNumber}`;
      const now = Date.now();
      const cachedItem = interactionCache.get(cacheKey);
      
      if (cachedItem && cachedItem.expiry > now) {
        AppLogger.info(`Using cached Twilio interaction data for ${phoneNumber}`);
        return cachedItem.value;
      }
      
      // No cache hit or expired cache, fetch from API
      AppLogger.info(`Fetching fresh Twilio interaction data for ${phoneNumber}`);
      const result = await this.fetchLastTwilioInteraction(phoneNumber);
      const formattedDate = result ? result.formattedDate : null;
      
      // Store result in cache with 10-minute expiry
      interactionCache.set(cacheKey, {
        value: formattedDate,
        expiry: now + CACHE_TTL_MS
      });
      
      return formattedDate;
    } catch (error) {
      AppLogger.error(`Error getting formatted Twilio last interaction: ${error}`);
      return null; // Return null on errors
    }
  }

  /**
   * Check the last message of a phone number through Twilio
   * @param phoneNumber - The phone number to check
   * @returns Information about the most recent message
   */
  static async fetchLastTwilioInteraction(phoneNumber: string): Promise<TwilioInteractionResult | null> {
    // Ensure phone number has the correct format
    if (!phoneNumber.startsWith('+')) {
      phoneNumber = '+' + phoneNumber;
    }

    console.log(`Checking phone number: ${phoneNumber}`);

    const baseUrl = `https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/Messages.json`;
    
    // Create Header for Basic Authentication
    const authHeader = Buffer.from(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`).toString('base64');
    
    // Check both incoming and outgoing messages
    const paramsTo = new URLSearchParams({
      To: phoneNumber,
      PageSize: '20'
    });
    
    const paramsFrom = new URLSearchParams({
      From: phoneNumber,
      PageSize: '20'
    });
    
    try {
      // Get messages TO the phone number
      const responseTo = await fetch(`${baseUrl}?${paramsTo.toString()}`, {
        headers: {
          'Authorization': `Basic ${authHeader}`
        }
      });
      
      // Get messages FROM the phone number
      const responseFrom = await fetch(`${baseUrl}?${paramsFrom.toString()}`, {
        headers: {
          'Authorization': `Basic ${authHeader}`
        }
      });
      
      let allMessages: TwilioMessage[] = [];
      
      if (responseTo.ok) {
        const dataTo = await responseTo.json();
        allMessages = allMessages.concat(dataTo.messages || []);
      }
      
      if (responseFrom.ok) {
        const dataFrom = await responseFrom.json();
        allMessages = allMessages.concat(dataFrom.messages || []);
      }
      
      if (allMessages.length === 0) {
        console.log(`No messages found for number ${phoneNumber}`);
        return null;
      }
      
      // Sort messages by time and get the most recent
      const sortedMessages = allMessages.sort((a, b) => {
        const dateA = new Date(a.date_sent);
        const dateB = new Date(b.date_sent);
        return dateB.getTime() - dateA.getTime();
      });
      
      const latestMessage = sortedMessages[0];
      
      // Log details about the message
      console.log("Most recent message:");
      console.log(`  Content: ${latestMessage.body}`);
      console.log(`  From: ${latestMessage.from}`);
      console.log(`  To: ${latestMessage.to}`);
      console.log(`  Status: ${latestMessage.status}`);
      
      // Process time format
      const dateSent = latestMessage.date_sent;
      if (dateSent) {
        try {
          // Convert time string to Date object
          const dateObj = new Date(dateSent);
          // Format the date
          const formattedDate = `${dateObj.getFullYear()}-${(dateObj.getMonth() + 1).toString().padStart(2, '0')}-${dateObj.getDate().toString().padStart(2, '0')}`;
          return { formattedDate, latestMessage };
        } catch (e) {
          console.log(`Error when processing date format: ${e.message}`);
          console.log(`Original format: ${dateSent}`);
          return { formattedDate: dateSent, latestMessage };
        }
      }
      
      return null;
      
    } catch (error) {
      console.error(`Error connecting to Twilio API: ${error.message}`);
      return null;
    }
  }

  /**
   * Check a list of phone numbers and display results
   * @param phoneNumbers - The list of phone numbers to check
   */
  static async checkPhoneNumbers(phoneNumbers: string[]): Promise<void> {
    for (const phone of phoneNumbers) {
      console.log("\n" + "=".repeat(50));
      const result = await this.fetchLastTwilioInteraction(phone);
      
      if (result) {
        const { formattedDate } = result;
        console.log(`Most recent message for number ${phone} was sent on: ${formattedDate}`);
      } else {
        console.log(`No messages found for number ${phone}`);
      }
      console.log("=".repeat(50) + "\n");
    }
  }
}
