import * as mongoose from "mongoose";
import { ITreatmentHistory } from "../models/treatment-history-model";
import TreatmentHistory from "../schemas/treatment-history-scheama";
import { AppLogger } from "../common/logging";
import databaseSetup from "../startup/database";
import * as dotenv from "dotenv";
import * as path from "path";

// Load environment variables from .env file when run directly
dotenv.config({ path: path.join(__dirname, '../../.env') });

/**
 * Cron job to detect and mark duplicate treatment history records
 * Runs every 15 minutes to find records with the same meetingStartedTime
 * If a group has more than 2 records with the same meetingStartedTime and at least one record
 * has claimStatus set, mark others without claimStatus as "DUPLICATED"
 */
export async function detectDuplicateTreatmentHistories() {
  try {
    console.log("[CRON] Starting duplicate treatment history detection");
    
    // Step 1: Apply the same filters as getAllClaims API and find records with the same meetingStartedTime
    const duplicateGroups = await TreatmentHistory.aggregate([
      {
        $match: {
          meetingStartedTime: { $exists: true },
          deleteTreatmentHistory: { $ne: true }, // Exclude deleted records
          isMeetingTranscribe: true
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "clientId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                firstname: 1,
                lastname: 1,
                email: 1,
                lavniTestAccount: 1,
                insuranceId: 1,
                fullName: {
                  $concat: ["$firstname", " ", "$lastname"],
                }
              },
            },
          ],
          as: "clientDetails",
        },
      },
      {
        $unwind: {
          path: "$clientDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "insurances",
          localField: "clientDetails.insuranceId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                _id: 1,
                insuranceCompanyId: 1,
                subscriber: 1,
                dependent: 1,
              },
            },
          ],
          as: "clientInsuranceDetails",
        },
      },
      {
        $unwind: {
          path: "$clientInsuranceDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "diagnosisnotes",
          localField: "diagnosisNoteId",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                _id: 1,
                patientID: 1,
                cptCode: 1,
                encounterID: 1,
              },
            },
          ],
          as: "diagnosisNote",
        },
      },
      {
        $unwind: {
          path: "$diagnosisNote",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          "clientInsuranceDetails._id": { $exists: true },
          "diagnosisNote.patientID": { $exists: true },
          "diagnosisNote.cptCode": { $ne: "00001" },
          "diagnosisNote.encounterID": { $exists: true },
          "clientDetails.lavniTestAccount": { $ne: true }
        }
      },
      {
        $group: {
          _id: "$meetingStartedTime",
          count: { $sum: 1 },
          records: { $push: "$$ROOT" }
        }
      },
      {
        $match: {
          count: { $gte: 2 } // Groups with 2 or more records
        }
      }
    ]);

    console.log(`[CRON] Found ${duplicateGroups.length} groups with potential duplicates`);
    
    let totalUpdated = 0;

    // Step 2: Process each group
    for (const group of duplicateGroups) {
      const records = group.records;
      
      // Check if at least one record in the group has claimStatus
      const hasClaimStatus = records.some(record => record.claimStatus);
      
      if (hasClaimStatus) {
        // Find IDs of records without claimStatus
        const idsToUpdate = records
          .filter(record => !record.claimStatus)
          .map(record => record._id);
        
        if (idsToUpdate.length > 0) {
          // Update all records without claimStatus to be marked as DUPLICATED
          const updateResult = await TreatmentHistory.updateMany(
            { 
              _id: { $in: idsToUpdate }
            },
            {
              $set: { 
                claimStatus: "DUPLICATED",
                errorMsg: "Automatically marked as duplicate by system",
                flag: true
              }
            }
          );
          
          totalUpdated += updateResult.nModified || 0;
          console.log(`[CRON] Updated ${updateResult.nModified || 0} records for meetingStartedTime: ${group._id}`);
        }
      }
    }
    
    console.log(`[CRON] Duplicate detection completed. Total updated: ${totalUpdated}`);
  } catch (error) {
    console.error("[CRON] Error in duplicate treatment detection:", error);
  }
}

// Execute the function when this file is run directly
if (require.main === module) {
  // Initialize database connection when running directly
  databaseSetup().then(() => {
    console.log("[CRON] Database connected");
    detectDuplicateTreatmentHistories()
      .then(() => {
        console.log("[CRON] Duplicate detection completed, exiting process");
        process.exit(0);
      })
      .catch((error) => {
        console.error("[CRON] Error running duplicate detection:", error);
        process.exit(1);
      });
  });
}
