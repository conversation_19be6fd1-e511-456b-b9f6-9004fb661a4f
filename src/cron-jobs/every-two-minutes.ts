import { AppLogger } from "../common/logging";
import { TherapistEp } from "../end-point/therapist-ep";
import { VonageCallEp } from '../end-point/vonage-call-ep';

export async function everyTwoMinCronJob() {
  const executionId = `job-${Date.now()}`;
  
  const logInfo = (message: string) => 
      AppLogger.info(`Every Two Min Job [${executionId}] | ${message}`);
  const logError = (message: string) => 
      AppLogger.error(`Every Two Min Job [${executionId}] | ${message}`);

  logInfo('Starting job execution');

  const tasks = [
    //   {
    //       name: 'getArchiveByDeepgramForVonageExpressCalls',
    //       fn: VonageCallEp.getArchiveByDeepgramForVonageExpressCalls
    //   },
      {
          name: 'updateSpentDurationForVonageNativeCalls',
          fn: VonageCallEp.updateSpentDurationForVonageNativeCalls
      },
      {
          name: 'getAchiveAudioRecordsByDeepgram',
          fn: VonageCallEp.getAchiveAudioRecordsByDeepgram
      },
      {
          name: 'getScoreOfTheTherapist',
          fn: TherapistEp.getScoreOfTheTherapist
      }
  ];

  for (const task of tasks) {
      try {
          logInfo(`Starting task: ${task.name}`);
          await task.fn();
          logInfo(`Completed task: ${task.name}`);
      } catch (error) {
          logError(`Error in task ${task.name}: ${error}`);
      }
  }

  logInfo('Job execution completed');
}