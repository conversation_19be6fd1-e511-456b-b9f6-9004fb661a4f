import axios from 'axios';
import { AppLogger } from '../common/logging';
import JotformSubmission from '../schemas/jotform-submissions';
import Jotform from '../schemas/jotforms';
import { Types } from 'mongoose';
import databaseSetup from '../startup/database';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '../../.env') });

const JOTFORM_API_KEY = process.env.JOTFORM_API_KEY;
const ACCEPTED_FORM_IDS = process.env.ACCEPTED_FORM_IDS;
const ACCEPTED_FORM_IDS_PHQ9_AND_GAD7 = process.env.ACCEPTED_FORM_IDS_PHQ9_AND_GAD7;
const ACCEPTED_FORM_IDS_DAST = process.env.ACCEPTED_FORM_IDS_DAST;
const ACCEPTED_FORM_IDS_AUDIT = process.env.ACCEPTED_FORM_IDS_AUDIT;

const JOTFORM_API_BASE_URL = process.env.JOTFORM_API_BASE_URL;

interface JotformForm {
    id: string;
    title: string;
    status: string;
}

interface JotformAnswer {
    name: string;
    type: string;
    text?: string;
    answer?: string | {
        [key: string]: string;
    };
}

interface JotformSubmissionResponse {
    id: string;
    form_id: string;
    created_at: string;
    answers: {
        [key: string]: JotformAnswer;
    };
    status: string;
}

// Function to convert answer to score for PHQ-9
function getScoreFromAnswerPHQ9(answer: string): number {
    const normalizedAnswer = answer?.toLowerCase().trim() || '';

    if (normalizedAnswer === 'not at all') {
        return 0;
    }
    if (normalizedAnswer === 'several days') {
        return 1;
    }
    if (normalizedAnswer === 'more than half the days') {
        return 2;
    }
    if (normalizedAnswer === 'nearly every day') {
        return 3;
    }
    return 0;
}

// Function to convert answer to score for GAD-7
function getScoreFromAnswerGAD7(answer: string): number {
    const normalizedAnswer = answer?.toLowerCase().trim() || '';

    if (normalizedAnswer === 'not at all sure') {
        return 0;
    }
    if (normalizedAnswer === 'several days') {
        return 1;
    }
    if (normalizedAnswer === 'over half the days') {
        return 2;
    }
    if (normalizedAnswer === 'nearly every day') {
        return 3;
    }
    return 0;
}

// PHQ9 questions
const phq9Questions = [
    '1. Little interest or pleasure in doing things.',
    '2. Feeling down, depressed, or hopeless.',
    '3. Trouble falling or staying asleep, or sleeping too much.',
    '4. Feeling tired or having little energy.',
    '5. Poor appetite or overeating.',
    '6. Feeling bad about yourself – or that you are a failure or have let yourself or your family down.',
    '7. Trouble concentrating on things, such as reading thenewspaper or watching television.',
    '8. Moving or speaking so slowly that other people could have noticed. Or the opposite – being so fidgety or restless that you have been moving around a lot more than usual.',
    '9. Thoughts that you would be better off dead, or of hurting yourself in some way.'
];

// GAD7 questions
const gad7Questions = [
    '1. Feeling nervous, anxious, or on edge',
    '2. Not being able to stop or control worrying',
    '3. Worrying too much about different things',
    '4. Trouble relaxing',
    "5. Being so restless that it’s hard to sit still.",
    '6. Becoming easily annoyed or irritable',
    '7. Feeling afraid as if something awful might happen'
];

// Function to calculate PHQ-9 score
function calculatePHQ9Score(answers: { [key: string]: JotformAnswer }): number {
    return Object.entries(answers)
        .filter(([_, answer]) => phq9Questions.includes(answer.text || ''))
        .reduce((score, [_, answer]) => {
            return score + getScoreFromAnswerPHQ9(answer.answer as string);
        }, 0);
}

function calculateDASTScore(answers: { [key: string]: JotformAnswer }): number {
    // console.log("answers", answers);
    let score = 0;

    // Các câu hỏi DAST (1-10)
    const dastQuestions = [
        '1. Have you used drugs other than those required for medical reasons?',
        '2. Do you abuse more than one drug at a time?',
        '3. Are you always able to stop using drugs when you want to?',
        '4. Have you ever had blackouts or flashbacks as a result of drug use?',
        '5. Do you ever feel bad or guilty about your drug use?',
        '6. Does your spouse (or parents) ever complain about your involvement with drugs?',
        '7. Have you neglected your family because of your use of drugs?',
        '8. Have you engaged in illegal activities in order to obtain drugs?',
        '9. Have you ever experienced withdrawal symptoms (felt sick) when you stopped taking drugs?',
        '10. Have you had medical problems as a result of your drug use (e.g. memory loss, hepatitis, convulsions, bleeding)?'
    ];

    // Loop through all answers
    Object.entries(answers).forEach(([key, answer]) => {
        // Check if the question is a DAST question
        if (answer.text) {
            const questionIndex = dastQuestions.findIndex(q =>
                answer.text.includes(q) || answer.text.startsWith(q.split('.')[0] + '.')
            );

            if (questionIndex !== -1) {
                // Question 3 is scored in reverse (No = 1, Yes = 0)
                if (questionIndex === 2) { // Question 3 (index 2)
                    score += answer.answer === 'No' ? 1 : 0;
                } else {
                    score += answer.answer === 'Yes' ? 1 : 0;
                }
            }
        }
    });
    console.log("score", score);

    return score;
}

function calculateAUDITScore(answers: { [key: string]: JotformAnswer }): number {
    // console.log("answers", answers);
    let score = 0;

    // Define AUDIT questions
    const auditQuestions = [
        '1. How often do you have a drink containing alcohol?',
        '2. How many drinks containing alcohol do you have on a typical day when you are drinking?',
        '3. How often do you have four or more drinks on one occasion?',
        '4. How often during the last year have you found that you were not able to stop drinking once you had started?',
        '5. How often during the last year have you failed to do what was normally expected of you because of drinking?',
        '6. How often during the last year have you needed a first drink in the morning to get yourself going after a heavy drinking session?',
        '7. How often during the last year have you had a feeling of guilt or remorse after drinking?',
        '8. How often during the last year have you been unable to remember what happened the night before because of your drinking?',
        '9. Have you or someone else been injured because of your drinking?',
        '10. Has a relative, friend, doctor, or other health care worker been concerned about your drinking or suggested you cut down?'
    ];

    // Loop through the answers
    Object.entries(answers).forEach(([key, answer]) => {
        if (answer.text) {
            const questionIndex = auditQuestions.findIndex(q =>
                answer.text.includes(q) || answer.text.startsWith(q.split('.')[0] + '.')
            );

            if (questionIndex !== -1) {
                // Get the answer
                const answerValue = answer.answer as string;

                // Calculate score based on the answer
                if (questionIndex === 0) { // Question 1
                    if (answerValue === 'Never') score += 0;
                    else if (answerValue === 'Monthly or less') score += 1;
                    else if (answerValue === '2-4 times a month') score += 2;
                    else if (answerValue === '2-3 times a week') score += 3;
                    else if (answerValue === '4 or more times a week') score += 4;
                }
                else if (questionIndex === 1) { // Question 2
                    if (answerValue === '0 - 2') score += 0;
                    else if (answerValue === '3 or 4') score += 1;
                    else if (answerValue === '5 or 6') score += 2;
                    else if (answerValue === '7 - 9') score += 3;
                    else if (answerValue === '10 or more') score += 4;
                }
                else if (questionIndex === 8 || questionIndex === 9) { // Questions 9 and 10
                    if (answerValue === 'No') score += 0;
                    else if (answerValue === 'Yes, but not in the last year') score += 2;
                    else if (answerValue === 'Yes, in the last year') score += 4;
                }
                else { // Questions 3-8
                    if (answerValue === 'Never') score += 0;
                    else if (answerValue === 'Less than monthly') score += 1;
                    else if (answerValue === 'Monthly') score += 2;
                    else if (answerValue === 'Weekly') score += 3;
                    else if (answerValue === 'Daily or almost daily') score += 4;
                }
            }
        }
    });

    console.log("AUDIT score", score);
    return score;
}

// Function to calculate GAD-7 score
function calculateGAD7Score(answers: { [key: string]: JotformAnswer }): number {
    return Object.entries(answers)
        .filter(([_, answer]) => gad7Questions.includes(answer.text || ''))
        .reduce((score, [_, answer]) => {
            return score + getScoreFromAnswerGAD7(answer.answer as string);
        }, 0);
}

// Function to find field IDs for PHQ9 and GAD7 scores
function findScoreFieldIds(answers: { [key: string]: JotformAnswer }): { phq9FieldId: string | null, gad7FieldId: string | null } {
    let phq9FieldId: string | null = null;
    let gad7FieldId: string | null = null;

    // Loop through all answers to find fields with matching names
    Object.entries(answers).forEach(([id, answer]) => {
        if (answer.name === 'totalScorePHQ9') {
            phq9FieldId = id;
        } else if (answer.name === 'totalScoreGAD7') {
            gad7FieldId = id;
        }
    });

    return { phq9FieldId, gad7FieldId };
}

export async function processJotformSubmissions() {
    try {
        const jobId = `jotform-job-${Date.now()}`;
        const logInfo = (message: string) => AppLogger.info(`Jotform Submission Job [${jobId}] | ${message}`);
        const logError = (message: string) => AppLogger.error(`Jotform Submission Job [${jobId}] | ${message}`);


        // Connect to MongoDB
        await databaseSetup();
        //logInfo("Connected to MongoDB");
        //logInfo("Starting job execution");

        // Get all forms first
        const formsResponse = await axios.get(`${JOTFORM_API_BASE_URL}/user/forms`, {
            headers: {
                "APIKEY": JOTFORM_API_KEY
            }
        });

        // Debug logs
        //logInfo(`API Response: ${JSON.stringify(formsResponse.data, null, 2)}`);

        const forms: JotformForm[] = formsResponse.data.content;

        // Filter only accepted forms
        const acceptedForms = forms.filter(form => ACCEPTED_FORM_IDS.includes(form.id));
        //logInfo(`Found ${forms.length} forms, processing ${acceptedForms.length} accepted forms`);

        // Save or update forms information
        for (const form of acceptedForms) {
            await Jotform.findOneAndUpdate(
                { form_id: form.id },
                {
                    title_form: form.title,
                    status: form.status,
                    form_id: form.id
                },
                { upsert: true, new: true }
            );
        }

        //logInfo(`Found ${acceptedForms.length} accepted forms`);

        // Process each form
        for (const form of acceptedForms) {
            //logInfo(`Processing form: ${form.title} (${form.id})`);

            // Get form submissions
            // Initialize score variables for each submission
            let score_PHQ_9 = 0;
            let score_GAD_7 = 0;
            let score_DAST = 0;
            let score_AUDIT = 0;

            const submissionsResponse = await axios.get(
                `${JOTFORM_API_BASE_URL}/form/${form.id}/submissions`,
                {
                    headers: {
                        'APIKEY': JOTFORM_API_KEY
                    }
                }
            );

            const submissions: JotformSubmissionResponse[] = submissionsResponse.data.content;
            //logInfo(`Found ${submissions.length} submissions for form ${form.id}`);
            // console.log('Raw submissions data:', JSON.stringify(submissions, null, 2));

            // Process each submission
            for (const submission of submissions) {
                // logInfo(`Processing submission: ${submission.id}`);

                // Log submission answers for debugging
                // console.log("Submission answers:", submission.answers);



                // Find hash fields in answers
                const therapistIdAnswer = Object.values(submission.answers).find(
                    answer => answer.name === 'hash_therapist'
                );

                const clientIdAnswer = Object.values(submission.answers).find(
                    answer => answer.name === 'hash_clientid'
                );

                // Extract the actual ID values, handling both string and object cases
                const therapistId = typeof therapistIdAnswer?.answer === 'string'
                    ? therapistIdAnswer.answer
                    : therapistIdAnswer?.answer?.toString();

                const clientId = typeof clientIdAnswer?.answer === 'string'
                    ? clientIdAnswer.answer
                    : clientIdAnswer?.answer?.toString();

                // Skip if no valid hash found
                if (!therapistId || !clientId) {
                    // logInfo(`Skipping submission ${submission.id} - no valid hash found`);
                    continue;
                }

                // Validate ObjectIds
                if (!Types.ObjectId.isValid(therapistId) || !Types.ObjectId.isValid(clientId)) {
                    logError(`Error processing submission ${submission.id}: Invalid therapistId or clientId format`);
                    continue;
                }

                if(form.id==ACCEPTED_FORM_IDS_PHQ9_AND_GAD7){
                    // Calculate scores
                    score_PHQ_9 = calculatePHQ9Score(submission.answers);
                    score_GAD_7 = calculateGAD7Score(submission.answers);
                    // console.log("Scores:", score_PHQ_9, score_GAD_7);

                    // Get field IDs for scores
                    const { phq9FieldId, gad7FieldId } = findScoreFieldIds(submission.answers);
                    // console.log("Score field IDs:", { phq9FieldId, gad7FieldId });

                    // Only update if we found both field IDs
                    if (phq9FieldId && gad7FieldId) {
                        // Update scores in Jotform
                        try {
                            const updateScoreResponse = await axios.post(
                                `${JOTFORM_API_BASE_URL}/submission/${submission.id}`,
                                new URLSearchParams({
                                    [`submission[${phq9FieldId}]`]: score_PHQ_9.toString(),
                                    [`submission[${gad7FieldId}]`]: score_GAD_7.toString()
                                }),
                                {
                                    headers: {
                                        'APIKEY': JOTFORM_API_KEY,
                                        'Content-Type': 'application/x-www-form-urlencoded'
                                    }
                                }
                            );
                            //logInfo(`Updated scores in Jotform for submission ${submission.id}`);
                        } catch (error: any) {
                            logError(`Failed to update scores in Jotform for submission ${submission.id}: ${error.message}`);
                        }
                    } else {
                        logError(`Could not find score field IDs for submission ${submission.id}`);
                    }

                    score_DAST = 0;
                    score_AUDIT = 0;
                }

                else if(form.id==ACCEPTED_FORM_IDS_DAST){
                    // Calculate scores
                    score_PHQ_9 = 0;
                    score_GAD_7 = 0;
                    score_DAST = calculateDASTScore(submission.answers);
                    score_AUDIT = 0;
                }

                else if(form.id==ACCEPTED_FORM_IDS_AUDIT){
                    // Calculate scores
                    score_PHQ_9 = 0;
                    score_GAD_7 = 0;
                    score_DAST = 0;
                    score_AUDIT = calculateAUDITScore(submission.answers);
                }

                try {
                    // Create or update submission record
                    await JotformSubmission.findOneAndUpdate(
                        {
                            formId: form.id,
                            submissionId: submission.id
                        },
                        {
                            formName: form.title,
                            formId: form.id,
                            resultLink: `${process.env.API}/api/jotform/generatePDF/${form.id}/${submission.id}`,
                            submissionDate: new Date(submission.created_at),
                            therapistId: new Types.ObjectId(therapistId),
                            clientId: new Types.ObjectId(clientId),
                            submissionId: submission.id,
                            status: submission.status,
                            score_PHQ_9: score_PHQ_9,
                            score_GAD_7: score_GAD_7,
                            score_DAST: score_DAST,
                            score_AUDIT: score_AUDIT
                        },
                        {
                            upsert: true,
                            new: true
                        }
                    );
                    //logInfo(`Processed submission ${submission.id} successfully`);
                } catch (error) {
                    logError(`Error processing submission ${submission.id}: ${error.message}`);
                }
            }
        }

        //logInfo("Job execution completed successfully");
        console.log("Job completed successfully");
    } catch (error) {
        AppLogger.error(`Error in JotForm submission job: ${error.message}`);
        console.error("Job failed:", error);
        throw error; // Re-throw the error to be handled by the caller
    }
}

// Execute the function when this file is run directly
if (require.main === module) {
    processJotformSubmissions()
        .then(() => {
            console.log('Job completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('Job failed:', error);
            process.exit(1);
        });
}
