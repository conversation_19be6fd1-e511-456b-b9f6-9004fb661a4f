import { AppLogger } from "../common/logging";
import User from "../schemas/user-schema";
import * as moment from "moment";
import databaseSetup from '../startup/database';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '../../.env') });

// Use default values to ensure authentication information
const accountSid = "**********************************";
const authToken = "9c6a982b3969e37f4f20875844c4b9b7";
const twilioPhoneNumber =  "+***********"; // Add default from log information
let twilioClient: any;

try {
  twilioClient = require("twilio")(accountSid, authToken);
} catch (error) {
  console.error("Could not initialize Twilio client:", error);
}

/**
 * <PERSON><PERSON> job to check and update the number of successful and failed SMS
 */
export async function trackTwilioMessageStatus() {
  const jobId = `twilio-job-${Date.now()}`;
  const logInfo = (message: string) => AppLogger.info(`Twilio Tracking Job [${jobId}] | ${message}`);
  const logError = (message: string) => AppLogger.error(`Twilio Tracking Job [${jobId}] | ${message}`);
  const logWarning = (message: string) => AppLogger.info(`Twilio Tracking Job [${jobId}] | WARNING: ${message}`);

  try {
    logInfo("Starting SMS check job");
    
    // Check if can't connect to Twilio
    const isApiAvailable = await checkTwilioApiAccess();
    
    if (!isApiAvailable) {
      logWarning("Unable to connect to Twilio API. Will only process SMS blocking rules for inactive phone numbers.");
      // Process when no API connection
      await processWithoutTwilioApi(logInfo, logError);
      return true;
    }
    
    // API connection available, proceed to query all failed messages
    logInfo("Starting to query messages with failed status from Twilio");
    
    // Get time from 24 hours ago instead of 7 days
    const dateFrom = moment().subtract(24, 'hours').toISOString();
    
    try {
      // Query sent messages from Twilio number, both successful and failed
      logInfo(`Querying messages sent from number ${twilioPhoneNumber} in the last 24 hours`);
      const sentMessages = await twilioClient.messages.list({
        from: twilioPhoneNumber,
        dateSentAfter: dateFrom
      });
      
      logInfo(`Found ${sentMessages.length} messages sent from number ${twilioPhoneNumber} in the last 24 hours`);
      
      // Create map to count successful/failed attempts for each phone number
      const phoneStats = new Map();
      let totalSuccessCount = 0;
      let totalFailedCount = 0;
      
      // Process messages and count statuses
      for (const message of sentMessages) {
        const toNumber = message.to;
        const status = message.status;
        
        if (!phoneStats.has(toNumber)) {
          phoneStats.set(toNumber, { success: 0, failed: 0 });
        }
        
        const stats = phoneStats.get(toNumber);
        
        if (status === 'delivered') {
          stats.success += 1;
          totalSuccessCount += 1;
        } else if (['failed', 'undelivered'].includes(status)) {
          stats.failed += 1;
          totalFailedCount += 1;
          logInfo(`Found failed message - ID: ${message.sid}, To: ${maskPhoneNumber(toNumber)}, Status: ${status}`);
        }
      }
      
      // Log summary of message statuses
      logInfo(`Messages in the last 24 hours - Total: ${sentMessages.length}, Success: ${totalSuccessCount}, Failed: ${totalFailedCount}`);
      
      // Update information in DB
      let updatedCount = 0;
      for (const [phoneNumber, stats] of phoneStats.entries()) {
        // Only update if there are failed messages
        if (stats.failed > 0) {
          const user = await User.findOne({ primaryPhone: phoneNumber });
          if (user) {
            const currentSuccessCount = user.successfulSmsCount || 0;
            const currentFailedCount = user.failedSmsCount || 0;
            
            await User.updateOne(
              { _id: user._id },
              { 
                $set: { 
                  successfulSmsCount: currentSuccessCount + stats.success,
                  failedSmsCount: currentFailedCount + stats.failed
                } 
              }
            );
            
            updatedCount++;
            logInfo(`Updated user ${user._id} - Phone: ${maskPhoneNumber(phoneNumber)}, +${stats.success} successful, +${stats.failed} failed`);
          } else {
            logWarning(`User not found with phone: ${maskPhoneNumber(phoneNumber)}`);
          }
        }
      }
      
      logInfo(`Update completed: ${updatedCount} users had their SMS counters updated`);
    } catch (error) {
      logError(`Error when querying message list: ${error}`);
    }
    
    logInfo("SMS check job completed");
    return true;
  } catch (error) {
    logError(`Error in Twilio message status check job: ${error}`);
    return false;
  }
}

/**
 * Check connection to Twilio API
 */
async function checkTwilioApiAccess(): Promise<boolean> {
  if (!twilioClient) return false;
  
  try {
    // Try a simple API call to check connection
    await twilioClient.api.accounts(accountSid).fetch();
    return true;
  } catch (error) {
    console.error("Unable to connect to Twilio API:", error);
    return false;
  }
}

/**
 * Process when there is no Twilio API connection
 */
async function processWithoutTwilioApi(logInfo: Function, logError: Function) {
  try {
    // Get all users with phone numbers
    const users = await User.find({ primaryPhone: { $exists: true, $ne: null } });
    logInfo(`Found ${users.length} users with phone numbers to check`);
    
    // Count users exceeding the failure threshold
    let blockedCount = 0;
    
    for (const user of users) {
      if (user.failedSmsCount && user.failedSmsCount > 10) {
        blockedCount++;
        logInfo(`User ${user._id} with phone ${maskPhoneNumber(user.primaryPhone)} has exceeded the SMS failure threshold (${user.failedSmsCount} > 10)`);
      }
    }
    
    logInfo(`Total of ${blockedCount}/${users.length} users have exceeded the failed message threshold`);
  } catch (error) {
    logError(`Error when processing without API connection: ${error}`);
  }
}

/**
 * Mask phone number for logs
 */
function maskPhoneNumber(phoneNumber: string): string {
  if (!phoneNumber || phoneNumber.length <= 6) return phoneNumber;
  const totalLength = phoneNumber.length;
  const midStart = Math.floor((totalLength - 4) / 2);
  const midEnd = totalLength - 2;
  return (
    phoneNumber.slice(0, 2) +
    '****' +
    phoneNumber.slice(midEnd)
  );
}

// Allow direct execution through command line
if (require.main === module) {
  (async () => {
    try {
      console.log('Setting up MongoDB connection...');
      await databaseSetup();
      console.log('MongoDB connection successful!');
      
      console.log('Starting SMS check job...');
      await trackTwilioMessageStatus();
      console.log('Job has completed!');
      
      // Close process after completion
      process.exit(0);
    } catch (error) {
      console.error('Error running job:', error);
      process.exit(1);
    }
  })();
}