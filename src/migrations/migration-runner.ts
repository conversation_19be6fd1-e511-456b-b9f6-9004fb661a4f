import databaseSetup from "../startup/database";
import * as mongoose from 'mongoose';
require('dotenv').config();

const MIGRATIONS_COLLECTION = "migrations";
const MIGRATIONS_NAME = "name";
const MIGRATIONS_EXECUTED_AT = "executedAt";

async function runMigrations (): Promise<void> {
    await databaseSetup();

    const filesNames = getFileNames();
    console.log(" ================== All Migrations ================== ");
    console.log(filesNames);
    const executedMigrations = await getExecutedMigrations();
    console.log(" ================ Executed Migrations =============== ");
    console.log(executedMigrations);

    const pendingMigrations = filesNames.filter(name => !executedMigrations.find(m => m === name));
    console.log(" ================ Pending Migrations ================ ");
    console.log(pendingMigrations);
    try {
        for (const pendingMigration of pendingMigrations) {
            console.log("\nexecuting => " + pendingMigration + "\n");
            await executeMigration(pendingMigration);
            await addMigrationToCollection(pendingMigration);
        }
        console.log("\n ==================== Completed ===================== \n");
    } catch (e) {
       console.error(e);
       console.log("\n ========= Some Migrations Failed to Execute ========= \n");
    }
    await mongoose.disconnect();
}

runMigrations().catch(console.error);

function getFileNames (): string[] {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const fs = require('fs');
    const files: string[] = fs.readdirSync('src/migrations');
    const otherFiles = ["migration-runner.ts"];
    const filtered = files.filter(f => !otherFiles.includes(f));
    filtered.sort();
    return filtered;
}

async function getExecutedMigrations (): Promise<any[]> {
    const collection = await mongoose.connection.collection(MIGRATIONS_COLLECTION);
    return (await collection.find().toArray()).map(m => m.name);
}


async function addMigrationToCollection (name: string): Promise<void> {
    const collection = await mongoose.connection.collection(MIGRATIONS_COLLECTION);
    await collection.insertOne({[MIGRATIONS_NAME]: name, [MIGRATIONS_EXECUTED_AT]: new Date()} as any);
}

async function executeMigration (name: string): Promise<void> {
    try {
        const x = await import(`./${name}`);
        await x.migrate();
    } catch (e) {
        console.error(e);
        throw new Error(`Failed to execute: ${name}`);
    }
}
