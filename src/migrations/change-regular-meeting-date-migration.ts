import * as mongoose from "mongoose";
import Meeting from "../schemas/meeting-schema";
import TreatmentHistory from "../schemas/treatment-history-scheama";
const moment = require("moment-timezone");
import { Types } from "mongoose";

async function changeRegularMeetingDate() {
    try {
        const allRegularMeetings = await Meeting.find(
            { $and: [ { regularMeetingDate: { $exists: true } }, { meetingId: /Regular Call/ } ] },
            { regularMeetingDate: 1, regularMeetingStartTime: 1, _id: 1 } 
        )

        let treatmentHistoryUpdateCount = 0;
        let meetingUpdateCount = 0;
        const treatmentHistoriesArray = [];
        for (const meeting of allRegularMeetings) {
            
            const timeString = meeting.regularMeetingStartTime;
            const dateValue = moment.tz(meeting.regularMeetingDate, "America/New_York");

            const selectedTime = moment(timeString, ["h:mm A"]);
            const selectedHour = selectedTime.hour();
            const selectedMinute = selectedTime.minute();

            const inAmericanTimeZone = moment(dateValue)
            .hour(selectedHour)
            .minute(selectedMinute)
            .second(0);

            const finalDateForStore = inAmericanTimeZone.utc().toDate();

            // const regularMeetingDate = moment(meeting.regularMeetingDate).set({ hour: moment(meeting.regularMeetingStartTime, ["h:mm A"]).hour(), minute: moment(meeting.regularMeetingStartTime, ["h:mm A"]).minute(), second: 0});

            const treatmentHistoryUpdate = await TreatmentHistory.findOneAndUpdate(
                { meetingId: meeting._id },
                { meetingStartedTime: finalDateForStore },
                { useFindAndModify: false }
            );
            if (treatmentHistoryUpdate) {
                treatmentHistoryUpdateCount++;
            }
            if(!treatmentHistoryUpdate){
                treatmentHistoriesArray.push(meeting._id);
            }

            const meetingUpdate = await Meeting.findByIdAndUpdate(meeting._id, 
                { regularMeetingDate: finalDateForStore },
                { useFindAndModify: false }
            );
            if (meetingUpdate) {
                meetingUpdateCount++;
            }
        }

        console.log(`Total TreatmentHistory records updated: ${treatmentHistoryUpdateCount}`);
        console.log(`Total Meeting records updated: ${meetingUpdateCount}`);
        console.log(treatmentHistoriesArray);
        
    } catch (error) {
        console.error("Error seeding symptoms:", error);
    }

}

export async function migrate () {
    console.log("Change Regular Meeting Date Migration...");
    await changeRegularMeetingDate();
}