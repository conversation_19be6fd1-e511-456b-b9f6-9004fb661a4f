import * as mongoose from "mongoose";
import ExperienceTagSymptomSchema from "../schemas/experience-tag-symptom-schema";
import ExperienceMainTags from "../schemas/experience-main-tag-schema";

async function addExperienceTagSymptomsMigration() {
    const symptoms = [
        {
            symptomName: "Changes in Mood",
            mainTags: [
                "Bipolar and Related Disorders",
                "Depressive Disorders"
            ]
        },
        {
            symptomName: "Difficulty Sleeping",
            mainTags: [
                "Trauma and Stressor Related Disorders",
                "Sleep-Wake Disorders",
                "Anxiety Disorders",
                "Depressive Disorders"
            ]
        },
        {
            symptomName: "Restlessness",
            mainTags: [
                "Anxiety Disorders",
                "Neurodevelopmental Disorders"
            ]
        },
        {
            symptomName: "Fatigue",
            mainTags: [
                "Depressive Disorders"
            ]
        },
        {
            symptomName: "Repetitive Behaviors",
            mainTags: [
                "Neurodevelopmental Disorders",
                "Obsessive-Compulsive and Related Disorders"
            ]
        },
        {
            symptomName: "Impulse Control",
            mainTags: [
                "Obsessive-Compulsive and Related Disorders",
                "Disruptive, Impulse-Control, and Conduct Disorders",
                "Personality Disorders",
                "Substance Related and Addictive Disorders",
                "Bipolar and Related Disorders"
            ]
        },
        {
            symptomName: "Changes in Appetite",
            mainTags: [
                "Depressive Disorders",
                "Feeding and Eating Disorders"
            ]
        },
        {
            symptomName: "Excessive Worry",
            mainTags: [
                "Anxiety Disorders"
            ]
        },
        {
            symptomName: "Difficulty Concentrating",
            mainTags: [
                "Depressive Disorders",
                "Neurodevelopmental Disorders"
            ]
        },
        {
            symptomName: "Burned Out",
            mainTags: [
                "Self-Care / Wellness",
                "Depressive Disorders"
            ]
        },
        {
            symptomName: "Stressed",
            mainTags: [
                "Anxiety Disorders",
                "Trauma and Stressor Related Disorders",
                "Self-Care / Wellness"
            ]
        },
        {
            symptomName: "Social Difficulties",
            mainTags: [
                "Anxiety Disorders",
                "Neurodevelopmental Disorders"
            ]
        },
        {
            symptomName: "Hallucinations",
            mainTags: [
                "Schizophrenia Spectrum and Other Psychotic Disorders"
            ]
        },
        {
            symptomName: "Delusions",
            mainTags: [
                "Schizophrenia Spectrum and Other Psychotic Disorders"
            ]
        },
        {
            symptomName: "Persistent Sadness",
            mainTags: [
                "Depressive Disorders"
            ]
        },
        {
            symptomName: "Intrusive Thoughts",
            mainTags: [
                "Trauma and Stressor Related Disorders",
                "Obsessive-Compulsive and Related Disorders"
            ]
        },
        {
            symptomName: "Flashbacks",
            mainTags: [
                "Trauma and Stressor Related Disorders"
            ]
        },
        {
            symptomName: "Nightmares",
            mainTags: [
                "Trauma and Stressor Related Disorders"
            ]
        },
        {
            symptomName: "Body Image Distortion",
            mainTags: [
                "Feeding and Eating Disorders"
            ]
        },
        {
            symptomName: "Binging (Food)",
            mainTags: [
                "Feeding and Eating Disorders"
            ]
        },
        {
            symptomName: "Restricting (Food)",
            mainTags: [
                "Feeding and Eating Disorders"
            ]
        },
        {
            symptomName: "Daytime Sleepiness",
            mainTags: [
                "Sleep-Wake Disorders",
                "Depressive Disorders"
            ]
        },
        {
            symptomName: "Low Sex Drive",
            mainTags: [
                "Depressive Disorders",
                "Sexual Dysfunctions"
            ]
        },
        {
            symptomName: "Erectile Dysfunction",
            mainTags: [
                "Sexual Dysfunctions"
            ]
        },
        {
            symptomName: "Gender Discomfort",
            mainTags: [
                "Gender Dysphoria"
            ]
        },
        {
            symptomName: "Aggressive Behavior",
            mainTags: [
                "Disruptive, Impulse-Control, and Conduct Disorders",
                "Personality Disorders"
            ]
        },
        {
            symptomName: "Substance Cravings",
            mainTags: [
                "Substance Related and Addictive Disorders"
            ]
        },
        {
            symptomName: "Lack of Self-Control",
            mainTags: [
                "Disruptive, Impulse-Control, and Conduct Disorders",
                "Substance Related and Addictive Disorders"
            ]
        },
        {
            symptomName: "Memory Issues",
            mainTags: [
                "NeuroCognitive Disorders"
            ]
        },
        {
            symptomName: "Confusion",
            mainTags: [
                "NeuroCognitive Disorders"
            ]
        },
        {
            symptomName: "Communication Issues",
            mainTags: [
                "Neurodevelopmental Disorders",
                "Relationship Issues",
                "Disruptive, Impulse-Control, and Conduct Disorders"
            ]
        },
        {
            symptomName: "Unstable Relationships",
            mainTags: [
                "Relationship Issues",
                "Personality Disorders",
                "Disruptive, Impulse-Control, and Conduct Disorders"
            ]
        },
        {
            symptomName: "Argumentative Behavior",
            mainTags: [
                "Relationship Issues",
                "Personality Disorders"
            ]
        },
    ];

    try {
        const allmainTags = await ExperienceTagSymptomSchema.find();
        if(allmainTags.length > 0){
            console.log("Already have data in Experiencetagsymptom  table");
            
            return;
        }
        const mainTags = await ExperienceMainTags.find();

        const mainTagMap: any = {};
        mainTags.forEach(tag => {
            mainTagMap[tag.mainTagName] = tag._id;
        });

        const promises = symptoms.map(symptom => {
            const mainTagIds = symptom.mainTags.map(tag => mainTagMap[tag]);
            return ExperienceTagSymptomSchema.create({
                symptomName: symptom.symptomName,
                experienceMainTags: mainTagIds
            });
        });

        await Promise.all(promises);

        console.log("Symptoms have been successfully seeded.");
    } catch (error) {
        console.error("Error seeding symptoms:", error);
    }

}

export async function migrate () {
    console.log("Changing plan state structure");
    await addExperienceTagSymptomsMigration();
}