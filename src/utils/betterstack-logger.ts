import axios from 'axios';
import { AppLogger } from '../common/logging';

const BETTERSTACK_API_URL = process.env.BETTERSTACK_API_URL || 'https://s1257217.eu-nbg-2.betterstackdata.com';
const BETTERSTACK_API_KEY = process.env.BETTERSTACK_API_KEY || '************************';

/**
 * Send log data to BetterStack
 * @param message - Log message
 * @param metadata - Additional metadata
 * @returns Promise<void>
 */
export const sendLogToBetterStack = async (
  message: string,
  metadata: Record<string, any> = {}
): Promise<void> => {
  try {
    const timestamp = new Date().toISOString();
    
    const payload = {
      dt: timestamp,
      message,
      ...metadata
    };
    
    // Asynchronously send log to BetterStack without awaiting the response
    axios({
      method: 'post',
      url: BETTERSTACK_API_URL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${BETTERSTACK_API_KEY}`
      },
      data: payload
    }).catch(error => {
      // Just log locally the error but don't interrupt application flow
      AppLogger.error(`Failed to send log to BetterStack: ${error.message}`);
    });
    
  } catch (error) {
    // Log error locally but don't interrupt application flow
    AppLogger.error(`Error preparing log for BetterStack: ${error.message}`);
  }
};

/**
 * Log HTTP request to BetterStack
 * @param method - HTTP method
 * @param url - Request URL
 * @param statusCode - Response status code
 * @param responseTime - Response time in ms
 * @param metadata - Additional metadata
 */
export const logHttpRequest = (
  method: string,
  url: string,
  statusCode: number,
  responseTime: number,
  metadata: Record<string, any> = {}
): void => {
  // Build a descriptive log message
  const message = `${method} ${url} ${statusCode} ${responseTime}ms`;
  
  // Send log to BetterStack
  sendLogToBetterStack(message, {
    http: {
      method,
      url,
      status_code: statusCode,
      response_time_ms: responseTime
    },
    ...metadata
  });
}; 