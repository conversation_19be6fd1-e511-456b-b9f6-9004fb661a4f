import openai
from get_diagnosis import *
from Notes import *
from Get_transcript import *
from summary import *
import sys

openAIAPIKey = sys.argv[1]
meeting_id = sys.argv[2]
mongoConnectString = sys.argv[3]
dbName = sys.argv[4]
notes_type = sys.argv[5] 

openai.api_key = openAIAPIKey

# Function to summarize text
def summarize_text(text, max_tokens=1000):  # Default max_tokens is 500 if not provided
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4o-mini",  # You can also use "gpt-3.5-turbo" to save tokens
            messages=[
                {"role": "system", "content": "You are an assistant who helps summarize text."},
                {"role": "user", "content": f"Summarize the following text:\n\n{text}"}
            ],
            max_tokens=max_tokens,  # Dynamically setting max_tokens based on input
            temperature=0.5
        )
        summary = response['choices'][0]['message']['content']
        return summary
    except Exception as e:
        # print(f"Error summarizing text: {e}")
        return text  # If summarization fails, return the original text

# Function to generate notes (same as before but now it can use summaries)
def generate_notes(conversation_text, note_type, diagnosis=None, assessment=None, previous_notes=None):
    # Summarize inputs if they are too long
    if len(conversation_text) > 5000:  # Example threshold, adjust as needed
        conversation_text = summarize_text_with_limit(conversation_text, max_output_length=5000)

    if diagnosis and len(diagnosis) > 100:
        diagnosis = summarize_text(diagnosis, max_tokens = 100)

    if previous_notes and len(previous_notes) > 500:
        previous_notes = summarize_text(previous_notes, max_tokens=500)

    # Prepare context details based on the available information
    context = "This is a follow-up session with the client."

    if diagnosis:
        context += f" The client has been diagnosed with {diagnosis}."

    if assessment:
        context += f" The client's assessment indicated {assessment}."

    if previous_notes:
        context += f" In the previous session, the following was discussed: {previous_notes}."

    # Define the prompt structure based on note_type
    if note_type.lower() == 'pie':
        messages = [
            {"role": "system", "content": "You are a skilled therapist who uses DSM-5 criteria."},
            {"role": "user", "content": f"""
            {context}

            Please generate a PIE note for the following therapist-client session. Follow the PIE format:

            P (Problem): Describe the client's ongoing issues or any new developments in their symptoms or situation. Mention whether these issues align with the initial diagnosis or suggest a need for reassessment.

            I (Intervention): Summarize the therapist's intervention and suggestions during this session. Note how the interventions relate to the client's diagnosis, assessment, and therapy plan (if applicable).

            E (Evaluation): Evaluate the effectiveness of the interventions in this session. Provide recommendations for the next steps and note if further reassessment or adjustments to the therapy plan are necessary.

            Conversation: {conversation_text}
            """}
        ]

    elif note_type.lower() == 'soap':
        messages = [
            {"role": "system",
             "content": "You are a skilled therapist who uses DSM-5 criteria and follows SOAP note format."},
            {"role": "user", "content": f"""
            {context}

            Please generate a SOAP note for the following therapist-client session. Follow the SOAP format:

            S (Subjective): Summarize the client's subjective experience during the session, including any symptoms, feelings, or concerns they express.

            O (Objective): Record observable facts during the session. This could include the therapist's observations of the client's behavior, mood, and any relevant test results or measurements.

            A (Assessment): Provide an interpretation of the subjective and objective information. Relate it to the client's diagnosis (if applicable) and assess the client's progress. Mention if there is a need for further assessment or modification of the diagnosis.

            P (Plan): Outline the next steps for therapy. This includes future goals, planned interventions, and any adjustments to the current therapy plan.

            Conversation: {conversation_text}
            """}
        ]

    else:
        raise ValueError("Invalid note type. Please use 'pie' or 'soap'.")

    # Call the GPT API using ChatCompletion
    response = openai.ChatCompletion.create(
        model="gpt-4o-mini",  # or "gpt-3.5-turbo"
        messages=messages,
        max_tokens=1448,  # Adjust token limit as needed
        temperature=0.5  # Control the creativity level
    )

    # Extract the generated note from the response
    generated_note = response['choices'][0]['message']['content']

    return generated_note

conversation_text =  ', '.join(get_conversation_from_transcript(meeting_id, mongoConnectString, dbName)['convo'])

# print(len(conversation_text), conversation_text)
previous_data = ', '.join(f"{key}: {value}" for key, value in get_previous_encounter_data(meeting_id, mongoConnectString, dbName).items())
# print(len(previous_data), previous_data)
Diagnosis = extract_labels(get_diagnosis_codes(meeting_id, mongoConnectString, dbName))
diagnosis_codes = ', '.join(f"{key}: {value}" for key, value in Diagnosis.items())

# print(diagnosis_codes)

Session_notes = generate_notes(conversation_text, note_type=notes_type, diagnosis=diagnosis_codes, assessment=None, previous_notes=previous_data)
# print("clinical Note:\n", Session_notes)
print(Session_notes)