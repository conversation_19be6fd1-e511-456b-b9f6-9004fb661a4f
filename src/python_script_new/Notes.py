from pymongo import MongoClient
from bson import ObjectId

def get_previous_encounter_data(meetingId, mongoURL, dbName):

    client = MongoClient(mongoURL)
    db = client[dbName]
    collection = db['diagnosisnotes']

    # Step 1: Find the current encounter by meetingId
    current_encounter = collection.find_one({'meetingId': ObjectId(meetingId)})

    if not current_encounter:
        return {'error': 'No encounter found with this meetingId'}

    client_id = current_encounter.get('clientId')
    current_date = current_encounter.get('encounterDate')

    # Step 2: Find previous encounters for the same clientId before the current encounterDate
    previous_encounters = collection.find({
        'clientId': client_id,
        'encounterDate': {'$lt': current_date},
        '$or': [
            {'chiefComplaint': {'$exists': True, '$ne': None}},
            {'carePlan': {'$exists': True, '$ne': None}},
            {'assessments': {'$exists': True, '$ne': None}},
            {'mentalBehavioralStatus': {'$exists': True, '$ne': None}},
            {'procedureNotes': {'$exists': True, '$ne': None}}
        ]
    }).sort('encounterDate', -1).limit(1)  # Sort by encounterDate descending and limit to 1

    # Step 3: Get the closest previous encounter
    previous_encounter = next(previous_encounters, None)  # Safely get the first document, if any

    if not previous_encounter:
        return {'error': 'No previous encounter found with relevant fields'}

    # Step 4: Extract relevant fields
    result = {
        'chiefComplaint': previous_encounter.get('chiefComplaint'),
        'carePlan': previous_encounter.get('carePlan'),
        'assessments': previous_encounter.get('assessments'),
        'mentalBehavioralStatus': previous_encounter.get('mentalBehavioralStatus'),
        'procedureNotes': previous_encounter.get('procedureNotes')
    }

    # Return only non-empty fields
    return {k: v for k, v in result.items() if v is not None}


# Example usage
# meeting_id = "664e723684adeefbe4adc8bf"  # Replace with your actual meetingId
# previous_data = get_previous_encounter_data(meeting_id)
# # print(previous_data)