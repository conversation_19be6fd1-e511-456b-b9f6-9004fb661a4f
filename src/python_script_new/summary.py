import openai


def chunk_text_by_turns(text, max_chunk_size=5000):
    """
    Splits the text into chunks based on dialogue turns. Ensures that each chunk contains full conversational turns
    between therapist and client.
    """
    turns = text.split(", ")

    chunks = []
    current_chunk = ""

    for turn in turns:
        if len(current_chunk) + len(turn) <= max_chunk_size:
            current_chunk += turn + ", "
        else:
            chunks.append(current_chunk.strip())
            current_chunk = turn + ", "

    if current_chunk:
        chunks.append(current_chunk.strip())

    return chunks


def summarize_text_with_limit(text, max_output_length=3000, max_chunk_size=5000, max_summary_tokens=500):
    """
    Summarizes large conversational text while ensuring the final output is not more than the specified length.
    """
    # Step 1: Split the text into conversational chunks
    chunks = chunk_text_by_turns(text, max_chunk_size=max_chunk_size)

    # Step 2: Summarize each chunk
    summaries = []
    for i, chunk in enumerate(chunks):
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4o-mini",  # or "gpt-3.5-turbo"
                messages=[
                    {"role": "system", "content": "You are an assistant who helps summarize conversations."},
                    {"role": "user",
                     "content": f"Summarize the following conversation while preserving context and the flow of the dialogue:\n\n{chunk}"}
                ],
                max_tokens=max_summary_tokens,
                temperature=0.5
            )
            summary = response['choices'][0]['message']['content']
            summaries.append(summary)
        except Exception as e:
            # print(f"Error summarizing chunk {i}: {e}")
            summaries.append(chunk)  # If summarization fails, retain the original chunk

    # Step 3: Combine all summaries
    combined_summary = " ".join(summaries)

    # Step 4: Ensure the combined summary is not more than max_output_length
    if len(combined_summary) > max_output_length:
        # If the combined summary exceeds the limit, summarize it further
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4o-mini",  # or "gpt-3.5-turbo"
                messages=[
                    {"role": "system", "content": "You are an assistant who helps condense text."},
                    {"role": "user",
                     "content": f"Please condense the following text while retaining the most important details:\n\n{combined_summary}"}
                ],
                max_tokens=(max_output_length // 4),  # Approximate token limit to fit within final length
                temperature=0.5
            )
            final_summary = response['choices'][0]['message']['content']
            return final_summary
        except Exception as e:
            # print(f"Error condensing the summary: {e}")
            return combined_summary[:max_output_length]  # As a fallback, truncate the combined summary
    else:
        return combined_summary


# Example usage
# conversation_text = """
# THERAPIST: Hello?, CLIENT: Hello?, THERAPIST: Hi. I don't know. My camera is not working., CLIENT: It's okay. Sometimes in the lower hand corner, it'll be a camera. But if it's not working, it's okay., THERAPIST: Yeah. I'm in the I'm in, like, the kitchen area right now of my dorm because, basically, I went to use the bathroom, like, in the lobby because, my sink is clogged, like, in my room. And then when I came back, ... (rest of the conversation)
# """
# final_summary = summarize_text_with_limit(conversation_text, )
# print(final_summary)