from pymongo import MongoClient
from bson import ObjectId

def get_diagnosis_codes(meetingId, mongoURL, dbName ):

    client = MongoClient(mongoURL)
    db = client[dbName]
    collection = db['diagnosisnotes']

    # Find the document with the given meetingId
    meeting = collection.find_one({'meetingId': ObjectId(meetingId)})

    if not meeting:
        return {'error': 'No meeting found with this meetingId'}

    # Extract the clientId from the found meeting
    client_id = meeting.get('clientId')

    # Query for all documents with the same clientId
    client_documents = collection.find({'clientId': client_id}).sort('encounter_date', 1)  # Sort by encounter date ascending

    primary_code = None
    secondary_code = None

    for doc in client_documents:
        # Check if primary diagnosis code exists
        if not primary_code and 'diagnosisICDcodes' in doc and doc['diagnosisICDcodes']:
            primary_code = doc['diagnosisICDcodes']

        # Check if secondary diagnosis code exists
        if not secondary_code and 'secondaryDiagnosisICDcodes' in doc and doc['secondaryDiagnosisICDcodes']:
            secondary_code = doc['secondaryDiagnosisICDcodes']

        # Stop once we have both primary and secondary codes
        if primary_code and secondary_code:
            break

    return {
        'primaryDiagnosisICDcodes': primary_code,
        'secondaryDiagnosisICDcodes': secondary_code
    }


def extract_labels(diagnosis_codes):
    # Initialize payload dictionary
    payload = {
        'primaryDiagnosisICDcodes': None,
        'secondaryDiagnosisICDcodes': None
    }

    # Extract label from primaryDiagnosisICDcodes if it exists
    if diagnosis_codes.get('primaryDiagnosisICDcodes'):
        primary_codes = diagnosis_codes['primaryDiagnosisICDcodes']
        if isinstance(primary_codes, list) and primary_codes:
            payload['primaryDiagnosisICDcodes'] = primary_codes[0].get('label')  # Take the label of the first entry

    # Extract label from secondaryDiagnosisICDcodes if it exists
    if diagnosis_codes.get('secondaryDiagnosisICDcodes'):
        secondary_codes = diagnosis_codes['secondaryDiagnosisICDcodes']
        if isinstance(secondary_codes, list) and secondary_codes:
            payload['secondaryDiagnosisICDcodes'] = secondary_codes[0].get('label')  # Take the label of the first entry

    return payload


# meeting_id = "62ed4b81d756b857261d4be7"
# diagnosis_codes = get_diagnosis_codes(meeting_id)
# payload = extract_labels(diagnosis_codes)