from pymongo import MongoClient
from bson import ObjectId

def get_conversation_from_transcript(meetingId, mongoURL, dbName):

    client = MongoClient(mongoURL)
    db = client[dbName]
    meetings_collection = db['meetings']
    transcribes_collection = db['transcribes']

    # Step 1: Query the `meetings` table using _id
    meeting_record = meetings_collection.find_one({'_id': ObjectId(meetingId)})

    if not meeting_record:
        return {'error': 'No meeting found with this _id'}

    # Step 2: Extract the meetingId from the record, call it transcriptId
    transcript_id = meeting_record.get('meetingId')
    if not transcript_id:
        return {'error': 'No transcriptId (meetingId) found in the meeting record'}

    # Step 3: Query the `transcribes` table using transcriptId (which is meetingId)
    transcribe_record = transcribes_collection.find_one({'meetingId': transcript_id})

    if not transcribe_record:
        return {'error': 'No transcribe found for this transcriptId'}

    # Step 4: Extract transcriptText from the transcribe record, call it convo
    convo = transcribe_record.get('transcriptText')

    if not convo:
        return {'error': 'No transcriptText found in the transcribe record'}

    return {'convo': convo}


# Example usage
# meeting_id = "62ed4b81d756b857261d4be7"  # Replace with your actual meeting _id
# conversation_data = get_conversation_from_transcript(meeting_id)
# print(conversation_data)