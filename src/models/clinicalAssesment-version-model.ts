import { Types } from "mongoose";
import * as mongoose from "mongoose";
import { AssesmentHeaderModel } from "./sub-models/ClinicalAssesmentSubModels/assesmentHeader-model";
import { GeneralInforModel } from "./sub-models/ClinicalAssesmentSubModels/generalInfor-model";
import { ChiefComplaintModel } from "./sub-models/ClinicalAssesmentSubModels/chiefComplaint-model";
import { CurrentServicesModel } from "./sub-models/ClinicalAssesmentSubModels/currentServices-model";
import { LeisureActivityModel } from "./sub-models/ClinicalAssesmentSubModels/leisureActivity-model";
import { HouseHoldMemberModel } from "./sub-models/ClinicalAssesmentSubModels/houseHoldMember-model";
import { HistoryofAbuseModel } from "./sub-models/ClinicalAssesmentSubModels/historyofAbuse-model";
import { PSAServiceHistoryModel } from "./sub-models/ClinicalAssesmentSubModels/PSAServiceHistory-model";
import { HistoryofPsychiatricDiagnosesModel } from "./sub-models/ClinicalAssesmentSubModels/historyofPsychiatricDiagnoses-model";
import { HistoryofSymptomsModel } from "./sub-models/ClinicalAssesmentSubModels/historyofSymptoms-model";
import { CurrentMedicationsModel } from "./sub-models/ClinicalAssesmentSubModels/currentMedications-model";
import { MedicalHistoryModel } from "./sub-models/ClinicalAssesmentSubModels/medicalHistory-model";
import { HealthNotesModel } from "./sub-models/ClinicalAssesmentSubModels/healthNotes-model";
import { LegalHistoryModel } from "./sub-models/ClinicalAssesmentSubModels/LegalHistory-model";
import { SubstanceInfoModel } from "./sub-models/ClinicalAssesmentSubModels/substanceInfo-model";
import { AsamDimensionsModel } from "./sub-models/ClinicalAssesmentSubModels/asamDimensions-model";
import { EmploymentVocationalModel } from "./sub-models/ClinicalAssesmentSubModels/employmentVocational-model";
import { IndependentLivingModel } from "./sub-models/ClinicalAssesmentSubModels/independentLiving-model";
import { MentalStatusExamModel } from "./sub-models/ClinicalAssesmentSubModels/mentalStatusExam-model";
import { SuicideRiskPotentialModel } from "./sub-models/ClinicalAssesmentSubModels/suicideRiskPotential-model";
import { SummaryofNeedSchemaModel } from "./sub-models/ClinicalAssesmentSubModels/summeryofNeed-model";
import { RecoveryHistoryandEnvironmentModel } from "./sub-models/ClinicalAssesmentSubModels/RecoveryHistory-model";
import { ReligiousCulturalModel } from "./sub-models/ClinicalAssesmentSubModels/religious-model";
import { CurrentImpairmentssModel } from "./sub-models/ClinicalAssesmentSubModels/currentImpairments-model";
import { psychiatricSymptomModel } from "./sub-models/ClinicalAssesmentSubModels/statementBehaviour-model";
import { SummeryOfFindingModel } from "./sub-models/ClinicalAssesmentSubModels/summeryOfFInding-model";
import { ClientSaftyPlanModel } from "./sub-models/ClinicalAssesmentSubModels/ClientSaftyPlan-model";
import { GAD7AnxietyModel } from "./sub-models/ClinicalAssesmentSubModels/gad7Anxiety-model";
import { SignatureDetailsModel } from "./sub-models/ClinicalAssesmentSubModels/signaturePage-model";
import { patientHealthQuestionaireModel } from "./sub-models/ClinicalAssesmentSubModels/patientHealthQuestionaire-model";
import { RecoveryAssesmentScaleModel } from "./sub-models/ClinicalAssesmentSubModels/RecoveryAssesmentScale-model";
import { AceScoreModel } from "./sub-models/ClinicalAssesmentSubModels/aceScore-model";

export enum currentLivingSituation {
  RENTING = "RENTING",
  OWN = "OWN",
  HOUSE = "HOUSE",
  APT = "APT",
  MOBILEHOME = "MOBILE_HOME"
}


export enum mortivationEngageInServices {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
}

export interface Common {
  clinicalAssesmentId: Types.ObjectId,
  therapistId?:ObjectId,
  clientId?:ObjectId,
  assesmentHeader?: AssesmentHeaderModel;
  lengthOfAssestment?: string;
  dateOfAssesment?: Date;
  comprehensiveAssessment?: boolean;
  other?: string;
  generalInfor?: GeneralInforModel;
  chiefComplaint?: ChiefComplaintModel;
  currentServices?: CurrentServicesModel;
  religiousCulturalLanguagePref?: ReligiousCulturalModel;
  leisureActivity?: LeisureActivityModel[];
  identifiedStrengths?: string;
  identifiedNeeds?: string;
  currentLivingSituation?: currentLivingSituation;
  currentAddressDuration?: string;
  frequentMoves?: boolean;
  strengths?: string;
  needs?: string;
  houseHoldMember?: HouseHoldMemberModel[];
  immediateFamilyOutside?: {
    nonReported?: boolean;
    explanation?: string;
  },
  feelingUnsafe?: {
    nonReported?: boolean;
    explanation?: string;
  },
  incomeSource?: {
    employed?: boolean;
    where?: string;
    SSDI?: boolean;
    unemployed?: boolean;
    yes?: boolean;
    explanation?: string;
  },
  transportSource?: {
    hasaCar?: boolean;
    publicTransport?: boolean;
  },
  adultDevelopmentalAbnormalities?: string,
  wnl?: boolean;
  noReportHistoryOfAbuse?: boolean;
  historyofAbuse?: HistoryofAbuseModel,
  pSAServiceHistory?: PSAServiceHistoryModel[],
  noPSASserviceHistoryReported?: boolean,
  historyofPsychiatricDiagnoses?: HistoryofPsychiatricDiagnosesModel[],
  noHistrotypsyDiagnosesReported?: boolean,
  historyofSymptoms?: HistoryofSymptomsModel,
  currentModications?: CurrentMedicationsModel[],
  medicalHistory?: MedicalHistoryModel,
  healthNotes?: HealthNotesModel,
  legalHistory?: LegalHistoryModel,
  substanceAbuse?: SubstanceInfoModel,
  asamDimensions?: AsamDimensionsModel,
  schoolName?: string,
  highestEducation?: string,
  employmentVocational?: EmploymentVocationalModel,
  rank?: string,
  yearsServed?: string,
  reasonforDischarge?: string,
  serviceConnectedDisability?: {
    yesOrNo?: boolean;
    explaination?: string;
  },
  independentLiving?: IndependentLivingModel;
  mentalStatusExam?: MentalStatusExamModel;
  suicideRiskPotential?: SuicideRiskPotentialModel;
  summaryofNeeds?: SummaryofNeedSchemaModel;
  recoveryHistoryandEnvironment?: RecoveryHistoryandEnvironmentModel;
  mortivationEngageInServices?: mortivationEngageInServices;
  currentImpairments?: CurrentImpairmentssModel;
  psychiatricSymptom?: psychiatricSymptomModel;
  summeryOfFindings?: SummeryOfFindingModel;
  eligibilityRecommendations?: string;
  treatmentRecommendations?: string;
  recommendationNotes?: string;
  saftyPlanNotNeeded?: boolean;
  clientSaftyPlanCompleted?: boolean;
  clientSaftyPlan?: ClientSaftyPlanModel;
  signatureDetails?:SignatureDetailsModel;
  gad7anxiety?: GAD7AnxietyModel;
  aceScore?:AceScoreModel;
  patientHealthQuestionaire?: patientHealthQuestionaireModel
  findingAceScore?:Number;
  recoveryAssesmentScale?:RecoveryAssesmentScaleModel;
  isSignature?:boolean;
  therapistSignature?:Buffer;
  clientSignature?:Buffer;
  reasonForEdit?: string;
  versionCreatedBy: Types.ObjectId;
  versionCreatedAt: Date;
}
export interface DClinicalAssesmentVersion extends Common { }

export interface IClinicalAssesmentVersion extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
