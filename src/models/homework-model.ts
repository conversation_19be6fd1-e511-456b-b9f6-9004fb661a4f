import * as mongoose from "mongoose";

export enum HStatus {
    NEW = "NEW",
    PENDING = "PENDING",
    FINISHED = "FINISHED",
    REJECT = "REJECT",
    APPROVED = "APPROVED",
    SUBMITE = "SUBMITE",

}

interface Common {
    title: string;
    description: string;
    dueDate: string;
    isComplete: boolean;
    uploads?: any;
    createdBy: mongoose.Types.ObjectId;
    assignedFor: mongoose.Types.ObjectId;
    status: HStatus;
}

export interface DHomework extends Common {}

export interface IHomework extends Common, mongoose.Document {
    _id: mongoose.Types.ObjectId;
}