import * as mongoose from "mongoose";
import { Types } from "mongoose";

interface Common {
  referrerUserId?: Types.ObjectId;
  referredUserId?: Types.ObjectId;
  firstSessionCompletionBonusForReferrer?: Types.ObjectId;
  twentySessionsCompletionBonusForReferrer?: Types.ObjectId;
}
export interface DTherapistReferral extends Common { }

export interface ITherapistReferral extends Common, mongoose.Document {
  _id: Types.ObjectId;
  createdAt?: Date;
  updatedAt?: Date;
}