import * as mongoose from "mongoose";
import { AmeriGeneralInformationModel } from "./sub-models/AmeriHealthAuthFormSubModels/ameri-general-information-model";
import { AmeriServiceInformationModel } from "./sub-models/AmeriHealthAuthFormSubModels/ameri-service-information-model";
import { AmeriProviderModel } from "./sub-models/AmeriHealthAuthFormSubModels/ameri-provider-model";
import { AmeriPrescribingProviderModel } from "./sub-models/AmeriHealthAuthFormSubModels/ameri-prescribing-provider-model";
import { DAuthorizationForm, IAuthorizationForm } from "./authorization-form-model";

export interface Common {
  generalInformation: AmeriGeneralInformationModel,
  serviceInformation: AmeriServiceInformationModel,
  providerInformation: AmeriProviderModel;
  prescibingProviderInformation: AmeriPrescribingProviderModel;
}

export interface DAmeriHealthAuthForm extends Common, DAuthorizationForm { }

export interface IAmeriHealthAuthForm extends Common, IAuthorizationForm, mongoose.Document {
}