import * as mongoose from "mongoose";
import { Types } from "mongoose";

export enum AudienceType {
    PUBLIC = "public",
    CLIENTS = "clients",
    THERAPISTS = "therapists",
}

interface Common {
    documentTitle: string;
    documentDescription: string;
    uploads?: Types.ObjectId[];
    videoUrl?: string;
    vimoIds?: string[];
    createdBy: Types.ObjectId;
    editedBy?: Types.ObjectId[];
    createdAt?: Date;
    audience: AudienceType;
}

export interface DDocument extends Common {}

export interface IDocument extends Common, mongoose.Document {
    _id: Types.ObjectId;
}