import * as mongoose from "mongoose";
import { Types } from "mongoose";

interface Common {
  referrerUserId?: Types.ObjectId;
  referredUserId?: Types.ObjectId;
  firstSessionCompletionBonusForReferrer?: Types.ObjectId;
}
export interface DClientReferral extends Common { }

export interface IClientReferral extends Common, mongoose.Document {
  _id: Types.ObjectId;
  createdAt?: Date;
  updatedAt?: Date;
}