import * as mongoose from "mongoose";
import { Types } from "mongoose";

export enum ChatGroupMemberType {
  SUPER_ADMIN = "SUPER_ADMIN",
  ADMIN = "ADMIN",
  MEMBER = "MEMBER",
}

interface Common {
  groupId: Types.ObjectId;
  userId: Types.ObjectId;
  role: ChatGroupMemberType;
  lastActive: Date;
  is_main?: boolean;
}

export interface DChatGroupMember extends Common {}

export interface IChatGroupMember extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}
