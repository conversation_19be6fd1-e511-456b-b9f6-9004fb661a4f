import * as mongoose from "mongoose";

interface Common {
  clientId: mongoose.Types.ObjectId;
  therapistId: mongoose.Types.ObjectId;
  sessionDate: Date;
  startTime: string;
  endTime: string;
  fee: number;
  intrductoryComment: string;
  closingComment: string;
  sessionNote: string;
}

export interface DNote extends Common {}

export interface INote extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}
