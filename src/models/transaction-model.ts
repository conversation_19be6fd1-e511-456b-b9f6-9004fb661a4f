import * as mongoose from "mongoose";
import { Types } from "mongoose";

export enum TransactionType {
  EARNING = "EARNING",
  WITHDRAWAL = "WITHDRAWAL",
  TRANSFER = "TRANSFER",
  REFERRAL = "REFERRAL"
}

interface Common {
  therapistId: string;
  meetingId?: string;
  type: string;
  transactionAmount: number;
  accumulatedBalance: number;
  accumulatedTotalEarnings: number;
  accumulatedWithdrawals: number;
  paidStatus?: string;
  insuranceCompany?: string;
  eligibleForPayment?: boolean;
  paymentReleaseDate?: Date;
}

export interface DTransaction extends Common {}

export interface ITransaction extends Common, mongoose.Document {
  _id: Types.ObjectId;
  type: TransactionType;
  createdAt?: Date;
  updatedAt?: Date;
}
