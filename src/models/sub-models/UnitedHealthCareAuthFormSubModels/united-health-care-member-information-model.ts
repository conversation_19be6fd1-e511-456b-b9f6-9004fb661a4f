export interface UnitedHealthCareMemberInformationModel {
    memberName?: string;
    medicaidID?: string;
    dateOfBirth?: Date;
    streetAddress?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    phone?: string;
    allergies?: string;
    isRequestedMedication?: {
        new?: boolean;
        continuationDate?: Date;
    };
    isHospitalized?: {
        yes?: boolean;
        dischargeDate?: Date;
    };
    isPregnant?: {
        yes?: boolean;
        dueDate?: Date;
    };
}