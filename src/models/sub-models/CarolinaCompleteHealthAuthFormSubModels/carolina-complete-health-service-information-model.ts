export interface CarolinaCompleteHealthServiceInformationModel {
    serviceInformationArray?: CarolinaCompleteHealthServiceInformationArray[];
    necessityExplanation?: string;
}
export interface CarolinaCompleteHealthServiceInformationArray {
    referenceNumber?: string;
    procedureCode?: string;
    startDate?: Date;
    endDate?: Date;
    serviceDescription?: string;
    itemQuantity?: string;
    approved?:string;
    denied?:string;
    allowedAmount?:string;
}