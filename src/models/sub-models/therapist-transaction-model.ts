import { Types } from "mongoose";
import * as mongoose from "mongoose";
import { DTransaction, ITransaction } from "../transaction-model";

export interface TransActionModel {
    therapistId: string;
    meetingId?: string;
    type: string;
    transactionAmount: number;
    accumulatedBalance: number;
    accumulatedTotalEarnings: number;
    accumulatedWithdrawals: number;
    insuranceCompany?: string;
    isFlag?: boolean;
}

export interface TherapistTransactionModel {
    _id: string;
    role: string;
    primaryPhone: string;
    email: string;
    firstname: string;
    lastname: string;
    stripeConnectedAccountId: string;
    recentTransaction?: TransActionModel;
}
export interface TherapistTransactionArrModel {
    _id: string;
    role: string;
    primaryPhone: string;
    email: string;
    firstname: string;
    lastname: string;
    stripeConnectedAccountId: string;
    recentTransaction?: TransActionModel[] | any;
    paidStatus?: string;
}