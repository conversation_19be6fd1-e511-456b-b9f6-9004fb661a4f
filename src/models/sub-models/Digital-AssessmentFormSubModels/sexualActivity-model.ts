
export interface SexualActivity {
  sexuallyActive?: {
    sexuallyActiveness?: boolean | null;
    activeWith?: string;
  };
  protectionAgainstHepatitisHiv?: {
    protection?: boolean | null;
    how?: string;
  };

  protectionAgainstPregnancy?: {
    protection?: boolean | null;
    how?: string;
  };

  atRiskBehavior?: {
    risk?: boolean | null;
    describe?: string;
  };
  otherSymtoms?: string;
  comments?: string;
}