
export interface EvaluationOfRisk {
  selfHarming?: {
    lowToNoRisk?: boolean | null;
    moderateRisk?: boolean | null;
    highRisk?: boolean | null;
    imminentRisk?: boolean | null;
  };
  assaultive?: {
    lowToNoRisk?: boolean | null;
    moderateRisk?: boolean | null;
    highRisk?: boolean | null;
    imminentRisk?: boolean | null;
  };
  actionEvaluation?: string;
  beliefSystem?: string;
  roleOfBeliefinlife?: string;
  roleOfBeliefRecovery?: string;
}