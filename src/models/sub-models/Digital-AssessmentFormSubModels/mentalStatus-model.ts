import { OrientationType } from "./orientationType-model";

export interface MentalStatus {
  physicalStature?: {
    small?: boolean | null;
    average?: boolean | null;
    tall?: boolean | null;
  };
  weight?: {
    thin?: boolean | null;
    average?: boolean | null;
    overweight?: boolean | null;
    obese?: boolean | null;
  };
  grooming?: {
    wellGroomed?: boolean | null;
    normal?: boolean | null;
    neglected?: boolean | null;
    bizarre?: boolean | null;
  };
  clothing?: {
    neatClean?: boolean | null;
    inappropriate?: boolean | null;
    dirty?: boolean | null;
    seductive?: boolean | null;
    disheveled?: boolean | null;
    bizarre?: boolean | null;
  };
  posture?: {
    normal?: boolean | null;
    tense?: boolean | null;
    stooped?: boolean | null;
    rigid?: boolean | null;
    slumped?: boolean | null;
    bizarre?: boolean | null;
  };
  attitude?: {
    cooperative?: boolean | null;
    passive?: boolean | null;
    guarded?: boolean | null;
    irritable?: boolean | null;
    manipulative?: boolean | null;
    seductive?: boolean | null;
    suspicious?: boolean | null;
    defensive?: boolean | null;
    dramatic?: boolean | null;
    silly?: boolean | null;
    hostile?: boolean | null;
    critical?: boolean | null;
    resistant?: boolean | null;
    sarcastic?: boolean | null;
    uninterested?: boolean | null;
    argumentative?: boolean | null;
  };
  motor?: {
    nonremarkable?: boolean | null;
    tremor?: boolean | null;
    slowed?: boolean | null;
    tics?: boolean | null;
    restless?: boolean | null;
    agitated?: boolean | null;
  };
  speech?: {
    normal?: boolean | null;
    rapid?: boolean | null;
    slurred?: boolean | null;
    loud?: boolean | null;
    paucity?: boolean | null;
    pressured?: boolean | null;
    mute?: boolean | null;
  };
  affect?: {
    appropriate?: boolean | null;
    inappropriate?: boolean | null;
    flat?: boolean | null;
    restricted?: boolean | null;
    blunted?: boolean | null;
    labile?: boolean | null;
  };
  mood?: {
    euthymic?: boolean | null;
    confused?: boolean | null;
    pessimistic?: boolean | null;
    depressed?: boolean | null;
    anxious?: boolean | null;
    euphoric?: boolean | null;
    apathetic?: boolean | null;
    angry?: boolean | null;
  };
  thoughtForm?: {
    goaldirected?: boolean | null;
    appropriate?: boolean | null;
    logical?: boolean | null;
    tangentialthinking?: boolean | null;
    circumstantial?: boolean | null;
    looseassociations?: boolean | null;
    confused?: boolean | null;
    incoherent?: boolean | null;
    perseverations?: boolean | null;
    flightofidea?: boolean | null;
    slownessofthought?: boolean | null;
  };
  thoughtContent?: {
    appropriate?: boolean | null;
    paranoid?: boolean | null;
    suspicions?: boolean | null;
    persecutions?: boolean | null;
    paucity?: boolean | null;
    delusions?: boolean | null;
    bizarre?: boolean | null;
    hypochondriac?: boolean | null;
    ideasofreference?: boolean | null;
  };
  preoccupations?: {
    phobias?: boolean | null;
    guilt?: boolean | null;
    other?: boolean | null;
    somatic?: boolean | null;
    religion?: boolean | null;
    suicide?: boolean | null;
    homicidal?: boolean | null;
  };
  hallucinations?: {
    auditory?: boolean | null;
    other?: boolean | null;
    visual?: boolean | null;
    sensory?: boolean | null;
  };
  orientation?: OrientationType;
  levelOfIntellectualFunctioning?: {
    belowAverage?: boolean | null;
    average?: boolean | null;
    aboveAverage?: boolean | null;
  };
  fundofKnowledge?: {
    belowAverage?: boolean | null;
    average?: boolean | null;
    aboveAverage?: boolean | null;
  };
  judgment?: {
    belowAverage?: boolean | null;
    average?: boolean | null;
    aboveAverage?: boolean | null;
  };
  insightIntoProblems?: {
    belowAverage?: boolean | null;
    average?: boolean | null;
    aboveAverage?: boolean | null;
  };
  clinicalImpressionSummary?: string;
}
