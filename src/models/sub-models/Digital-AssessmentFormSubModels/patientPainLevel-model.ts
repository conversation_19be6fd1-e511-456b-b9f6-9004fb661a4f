import { TreatementDetails } from './treatementDetails-model';
import { DisabilityStatus } from './disabilityStatus-model';

export interface PatientPainLevel {

  currentLevelOfPhysicalPain: number;
  isCurrentReceivingTreatementPain: string;
  isReceivingTreatement: string;
  treatementDetails: TreatementDetails[];
  disablityStatus: DisabilityStatus;
  adjustDisability: string;
  requireEquipmentOrServices?: string;
}