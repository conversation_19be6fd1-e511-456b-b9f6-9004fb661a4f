
import { DepressiveSymptoms } from './depressiveSymptomsType-model';
import { ManicSymptomsType } from './manicSymptoms-model';
import { ConductLegalProblem } from './conductLegalProblem-model';
import { PsychosisType } from './psychosisType-model';
import { AnxietySymptoms } from './anxietySymptoms-model';
import { AttentionSymptomsType } from './attentionSymptoms-model';
export interface SymptomChicklist {
  depressiveSymptoms?: {
    symtoms?: DepressiveSymptoms;
    comment?: string;
  };
  manicSymptoms?: {
    symtoms?: ManicSymptomsType;
    comment?: string;
  };
  conductLegalProblem?: {
    symtoms?: ConductLegalProblem;
    comment?: string;
  };
  psychosis?: {
    symtoms?: PsychosisType;
    comment?: string;
  };
  anxietySymptoms?: {
    symtoms?: AnxietySymptoms;
    comment?: string;
  };
  attentionSymptoms?: {
    symtoms?: AttentionSymptomsType;
    comment?: string;
  };
}