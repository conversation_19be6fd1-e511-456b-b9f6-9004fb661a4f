

export interface LivingArrangementType {
  livingArrangement?: {
    privateResidence?: boolean | null;
    institution ?: boolean | null;
    ownResidence ?: boolean | null;
    other ?: boolean | null;
    roomHouseDorm ?: boolean | null;
    adultCareHome ?: boolean | null;
    alternativeFamilyLiving ?: boolean | null;
    nursingHome ?: boolean | null;
    communityICFMR?: boolean | null;
    correctionalFacility?: boolean | null;
    homeless  ?: boolean | null;
    residentialFacility?: boolean | null;
  };
  other?: string;
}