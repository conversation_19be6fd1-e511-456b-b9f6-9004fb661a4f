
import { ExperienceSymptoms } from './experienceSymptoms-model';
export interface TobaccoUse {
  tobaccoUse: boolean | null;
  tobaccoType?: string;
  howOften?: string;
  howMuch?: string;
  howLong?: string;
  interestEndUse?:boolean | null;
  interestReducingUse?: boolean | null;
  endingUse?: string;
  longestAbstinence?: string;
  whenWas?: string;
  relapse?: string;
  comment?: string;
  experiencedSymptoms: ExperienceSymptoms;
}