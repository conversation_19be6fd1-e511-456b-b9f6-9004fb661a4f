import { ConsumerPhoneType } from "./consumerPhone-model";
import { LegalGuardianshipType } from "./legalGuarianship-model";
import { LivingArrangementType } from "./livingArrangement-model";
import { RaceType } from "./raceType-modal";

export interface GeneralInformationModel {
  name?: string;
  dob?: Date;
  knownAs?: string;
  age?: string;
  primaryLanguage?: {
    primaryLanguageReadablityStatus?: boolean | null;
    english?: boolean | null;
    other?: boolean | null;
    spanish?: boolean | null;
    otherLanguage?: string;
  };
  consumerPhone?: ConsumerPhoneType;
  legalGuarianship?: LegalGuardianshipType;
  guardianPhone?: {
    sameasAbove?: boolean | null;
    home?: boolean | null;
    work?: boolean | null;
    cell?: boolean | null;
    guardianPhoneNumber?: string;
  };
  primaryInformant?: {
    primaryInformant: {
      self?: boolean | null;
      other?: boolean | null;
    };
    other?: string;
  };
  informantPhone?: {
    sameasAbove?: boolean | null;
    home?: boolean | null;
    work?: boolean | null;
    cell?: boolean | null;
    informantPhoneNumber?: string;
  };
  genderAtBirth?: {
    male?: boolean | null;
    female?: boolean | null;
    femalePregnant?: boolean | null;
  };
  genderIdentity?: {
    male?: boolean | null;
    female?: boolean | null;
    nonBinary?: boolean | null;
  };
  sexualOrientation?: {
    sexualOrientations: string;
    isInterestingLGBTService: boolean | null;
  };
  ethnicity?: {
    notHispanicOrigin?: boolean | null;
    hispanicCuban?: boolean | null;
    hispanicMexicanAmerican?: boolean | null;
    hispanicPuertoRican?: boolean | null;
    hispanicOther?: boolean | null;
  };
  raceType?: RaceType;
  livingArrangement?: LivingArrangementType;
  maritalStatus?: {
    married?: boolean | null;
    widowed?: boolean | null;
    divorced?: boolean | null;
    singleNeverMarried?: boolean | null;
    separated?: boolean | null;
    domesticPartnership?: boolean | null;
  };
  familySize?: string;
  currentEmploymentStatus?: {
    unemployed?: boolean | null;
    notavailableforwork?: boolean | null;
    employedFullTime?: boolean | null;
    homemaker?: boolean | null;
    armedForcesNationalGuard?: boolean | null;
    employedPartTime?: boolean | null;
    retired?: boolean | null;
    student?: boolean | null;
    disabilityIncome?: boolean | null;
  };
  employmentHistory?: string;
  education?: {
    highestGradeCompleted?: boolean | null;
    highSchoolgraduate?: boolean | null;
    ged?: boolean | null;
    someCollege?: boolean | null;
    associateDegree?: boolean | null;
    bachelordegree?: boolean | null;
    graduateSchool?: boolean | null;
    specialEdClasses?: boolean | null;
    technicalTradeSchool?: boolean | null;
  };
}
