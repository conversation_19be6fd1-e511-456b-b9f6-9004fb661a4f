
export interface ConductLegalProblem {
  fireSetting?: string;
  lying?: string;
  stealing?: string;
  fighting?: string;
  substanceAbuse?: string;
  none?: string;
  oppositionalDefiant?: string;
  gangInvolvement?: string;
  arrestsConviction?: string;
  impulsivity?: string;
  familyDesertion?: string;
  exhibitionism?: string;
  sexualActingOut?: string;
  consistentIrresponsibility?: string;
  propertyDestruction?: string;
  other?: string;
}