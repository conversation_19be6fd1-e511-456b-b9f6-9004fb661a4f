export interface TraumaHistory {
  isBadAccident?: boolean | null;
  badAccidentDescribe?: string;
  isNaturalDisaster?: boolean | null;
  naturalDisasterdDescribe?: string;
  isMilitaryCombat: boolean | null;
  militoryCombatDescribe?: string;
  isSeriousAttack: boolean | null;
  seriousAttackDescribe?: string;
  isSexualCoercion?: boolean | null;
  ageOfSexualCoercion?: number;
  describeSexualCoercion?: string;
  isTraumaticExperience: boolean | null;
  describeTraumaticExperience?: string;
  intrusiveThoughts: boolean | null;
  avoidanceBehavior: boolean | null;
  hypervigilance: boolean | null;
  emotionalDetachment: boolean | null;
  selfBlame: boolean | null;
}