import { Types } from "mongoose";
import { RepeatType } from "../appointment-model";

export interface RepeatInfo {
  _id?: Types.ObjectId;
  repeatType: RepeatType;
  interval?: string;
  repeatDays?: {
      sunday: boolean,
      monday: boolean,
      tuesday: boolean,
      wednesday: boolean,
      thursday: boolean,
      friday: boolean,
      saturday: boolean,
  };
  endingDate: string;
  endingAfter?: number;
  endingType?: "";
}