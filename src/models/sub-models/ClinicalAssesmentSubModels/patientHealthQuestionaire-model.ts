export interface score {
    notAtAll: boolean;
    severalDays: boolean;
    moreThanHalfTheDays: boolean;
    NearlyEveryDay: boolean;
}

export interface Diflevel {
    notDifficult:boolean;
    someWhatDifficult:boolean;
    veryDifficult:boolean;
    extremlyDifficult:boolean;
}

export interface ColumnScore {
    column1?: number;
    column2?: number;
    column3?: number;
    column4?: number;
    total?:number
}

export interface patientHealthQuestionaireModel {
    littleInteres?: score;
    feelingDown?: score;
    troubleFalling?: score;
    feelingTired?: score;
    poorAppetite?: score;
    feelingBad?: score;
    troubleConcentrating?: score;
    speakingSlowly?: score;
    thoughtsDead?: score;
    columnCal?: ColumnScore;
    difficultLevel?: Diflevel;
}