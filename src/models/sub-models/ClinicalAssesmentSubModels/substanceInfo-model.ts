export interface SubstanceModel {
    ageAtFirstUse?:string;
    ageAtRegularUse?:string;
    currentFrequencyAverageUse?:string;
    methodOfAdministration?:string;
    lastDateUsed?:string;
    primarySecondaryOrTertiary?:string;
}

export interface SARelatedWithdrawalSymptomsModel {
    past?: string;
    present?: string;
}

export interface SubstanceInfoModel {
    substanceAbuseHistoryReported: boolean;
    nicotine?: SubstanceModel;
    alcohol?: SubstanceModel;
    marijuana?: SubstanceModel;
    cocaineCrack?: SubstanceModel;
    amphetamines?: SubstanceModel;
    hallucinogens?: SubstanceModel;
    ecstasyOther?: SubstanceModel;
    inhalants?: SubstanceModel;
    heroin?: SubstanceModel;
    barbiturates?: SubstanceModel;
    other?: SubstanceModel;
    substanceMeans?: string;
    mortivationForUse?: string;
    useReductionInterest?: string;
    pastAbstinence?: string;
    problemUsageCaused?: string;
    additionalHistory?: addtionalHistory;
    chargesHistory?: string;
    sARelatedWithdrawalSymptoms?: SARelatedWithdrawalSymptomsModel;
    signsOfTolerance:string;
    isRecommended?: boolean;
    recommend:string;
}

export enum addtionalHistory {
    DWI = "DWI",
    BLACKOUTS = "BLACKOUTS",
    ABSENTEEISM = "ABSENTEEISM",
    SEIZURES = "SEIZURES",
    JOBLOSS = "JOBLOSS",
    IVDRUGUSE = "IVDRUGUSE",
}
