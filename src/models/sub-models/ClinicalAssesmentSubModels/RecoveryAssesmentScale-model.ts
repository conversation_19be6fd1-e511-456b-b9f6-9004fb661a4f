export interface RecoveryAssesmentScaleModel {
    desireToSucceed: string;
    ownPlanForWellness: string;
    lifeGoals: string;
    beliefInMeetingGoals: string;
    senseOfPurpose: string;
    othersCaringmySelf: string;
    fearlessLiving: string;
    handleLifeSituations: string;
    selfLove: string;
    peopleLikeMe: string;
    ideaWhoWantToBe: string;
    somethingGoodHappen: string;
    hopefulness: string;
    continueInterests: string;
    mentalIllnessCoping: string;
    symptomInterference: string;
    symptomDuration: string;
    helpSeekingAwareness: string;
    willingnessToAskForHelp: string;
    askingHelpWhenNeed: string;
    stressHandling: string;
    peaopleCanCounton: string;
    otherPeopleBelive: string;
    importanceOfFriendship: string;
}

export enum likertScale {

    STRONGLYDISAGREE = "STRONGLYDISAGREE",
    DISAGREE = "DISAGREE",
    AGREE = "AGREE",
    NOTSURE = "NOTSURE",
    STRONGLYAGREE = "STRONGLYAGREE",
}