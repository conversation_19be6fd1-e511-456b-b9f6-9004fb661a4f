export interface LegalHistoryModel {
    ifNoCurrent?: boolean;
    currentlegalCharges?: {
        explanation?: string;
    };
    probationOfficer?: {
        name?: string;
    };
    currentlyonProbation?: {
        name?: string;
    };
    historyofIncarcerations?: {
        name?: string;
        additionalInformation?: boolean;
    };
    historyoflegalInvolvement?: {
        name?: string;
    };
    familylegalInvolvement?: {
        name?: string;
    };
    specificTermsofProbation?: {
        name?: string;
    };
    legalImpairment?: {
        name?: LegalImpairment;
    };
}

export enum LegalImpairment{
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
}
