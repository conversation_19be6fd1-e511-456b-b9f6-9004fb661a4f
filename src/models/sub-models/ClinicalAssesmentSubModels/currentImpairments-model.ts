

export interface CurrentImpairmentssModel {
    moodDisturbance: Impair<PERSON><PERSON>evel;
    anxiety: Impairment<PERSON>evel;
    psychosis: Impairment<PERSON>evel;
    thinkingCognitionMemory: ImpairmentLevel;
    impulsiveRecklessAggressive: ImpairmentLevel;
    activitiesOfDailyLiving: Impairment<PERSON>evel;
    weightLossAssocWithEatingDO: ImpairmentLevel;
    medicalPhysicalConditions: ImpairmentLevel;
    substanceAbuseDependence: ImpairmentLevel;
    schoolJobPerformance: ImpairmentLevel;
    socialMaritalFamilyProblems: ImpairmentLevel;
    legal: ImpairmentLevel;
}

export enum ImpairmentLevel {
    NONE = "NONE",
    MILD = "MILD",
    MODERATE = "MODERATE",
    SEVERE = "SEVERE",
    NA = "NA"
}
