

export interface AceScoreModel {
    humiliateOrPhysicallyHurt: <PERSON>olean;
    pushGrabSlap: Boolean;
    intercourse: Boolean;
    familySupport: Boolean;
    noOneToProtectYou: Boolean;
    parentSeperateDivorce: Boolean;
    hitWithSomthingHard: Boolean;
    threatenedWithGunOrKnife: <PERSON>olean;
    liveWithDrugUser: Boolean;
    isHouseHoldMemberattemptSuicide: Boolean;
    isHouseHOldMemberGoToPrison: Boolean;
    aceScore: Number;
}