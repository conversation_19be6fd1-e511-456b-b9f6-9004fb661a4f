
export interface score {
    notAtAll: boolean;
    severalDays: boolean;
    moreThanHalfTheDays: boolean;
    NearlyEveryDay: boolean;
}

export interface Diflevel {
    notDifficult:boolean;
    someWhatDifficult:boolean;
    veryDifficult:boolean;
    extremlyDifficult:boolean;
}

export interface ColumnScore {
    column1?: number;
    column2?: number;
    column3?: number;
    column4?: number;
    total?:number
}

export interface GAD7AnxietyModel {

    feelingNervous: score;
    cantControllWorrying: score;
    worryingMuch: score;
    troubleRelaxing: score;
    beingRestless: score;
    easilyAnnoyed: score;
    feelingAfraid: score;
    difficultLevel: Diflevel;
    columnCal?:ColumnScore;
}



