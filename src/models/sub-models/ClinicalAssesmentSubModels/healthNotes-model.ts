export interface HealthNotesModel {
    notes?: string;
    allergies?: {
        isReported: boolean;
        explanation: string;
    };
    enuresisOrEncopresis?: {
        isReported?: boolean;
        explanation?: string;
    };
    visionComplications?: {
        isReported?: boolean;
        explanation?: string;
    };
    hearingComplications?: {
        isReported?: boolean;
        explanation?: string;
    };
    dentalComplications?: {
        isReported?: boolean;
        explanation?: string;
    };
    dietRestrictions?: {
        isReported?: boolean;
        explanation?: string;
    };
    seizures?: {
        isReported?: boolean;
        explanation?: string;
    };
    somaticComplaints?: {
        isReported?: boolean;
        explanation?: string;
    };
    heartBloodPressure?: {
        isReported?: boolean;
        explanation?: string;
    };
    diabetes?: {
        isReported?: boolean;
        explanation?: string;
    };
    hxofEatingDisorder?: {
        isReported?: boolean;
        explanation?: string;
    };
    problemsSleeping?: {
        isReported?: boolean;
        explanation?: string;
        goingtoSleep?: boolean;
        stayingAsleep?: boolean;
        Nightmares?: boolean;
    };
    historyofHeadInjury?: {
        isReported?: boolean;
        explanation?: string;
    };
    dateofLastPhysical?: string;
}
