
export enum SeverityScore {
    NR = "NR",
    NotPresent = "NotPresent",
    VeryMild = "VeryMild",
    Mild = "Mild",
    Moderate = "Moderate",
    ModeratelySevere = "ModeratelySevere",
    Severe = "Severe",
    ExtremelySevere = "ExtremelySevere"
}

export interface psychiatricSymptomModel {
    anxiety: SeverityScore;
    tension: SeverityScore;
    depressiveMood: SeverityScore;
    helplessnessHopelessness: SeverityScore;
    guiltFeelings: SeverityScore;
    somaticConcern: SeverityScore;
    hostility: SeverityScore;
    suspiciousness: SeverityScore;
    uncooperativeness: SeverityScore;
    distractibility: SeverityScore;
    elatedMood: SeverityScore;
    motorHyperactivity: SeverityScore;
    disorientation: SeverityScore;
    disorganizedSpeech: SeverityScore;
    grandioseStatements: SeverityScore;
    unusualIdeas: SeverityScore;
    hallucinatoryStatements: SeverityScore;
    hallucinatoryBehavior: SeverityScore;
    socialWithdrawal: SeverityScore;
    bluntedAffect: SeverityScore;
    motorRetardation: SeverityScore;
    mannerismsPosturing: SeverityScore;
    lossOfFunction: SeverityScore;
}

