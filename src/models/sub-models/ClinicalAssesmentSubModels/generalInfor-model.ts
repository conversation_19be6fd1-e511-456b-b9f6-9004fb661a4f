

export interface GeneralInforModel {
    pcpReferal: string;
    npi: string;
    gender: string;
    age: number;
    consumerDob: string;
    consumerphone: string;
    ethnicity: string;
    other: string;
    emergancyContactName: string;
    phone: string;
    individualParticipants: string;
    insuranceBesideBCBS: {
        noOtherInusrnace: boolean;
        nameOfInsurance: string;
    };
    otherTherapyService: {
        noService: boolean;
        nameOfService: string;
    };
}

export enum genralInfoGender {
    male = "male",
    female = "female",
}

export enum generalInforEthnicity {
    Hispanic = "Hispanic",
    Caucasian = "Caucasian",
    AfricanAmerican = "AfricanAmerican",
    AsianAmerican = "AsianAmerican",
    Other='Other'
}