

export interface MentalStatusExamModel {
    appearanceDress?: {
        neat?: boolean;
        relaxed?: boolean;
        disheveled?: boolean;
        eccentric?: boolean;
        ageAppropriate?: boolean;
        other?: string;
        hygiene?: {
            normal?: boolean;
            bodyOdor?: boolean;
            badBreath?: boolean;
        };
    };
    behaviour?: {
        passive?: boolean;
        defensive?: boolean;
        guarded?: boolean;
        hostile?: boolean;
        attentive?: boolean;
        cooperative?: boolean;
        demanding?: boolean;
        oppositional?: boolean;
        selfDestructive?: boolean;
        preoccupied?: boolean;
        eyeContact?: {
            intense?: boolean;
            appropriate?: boolean;
            sporadic?: boolean;
            avoidant?: boolean;
        };
    };
    motor?: {
        relaxed?: boolean;
        catatonic?: boolean;
        posturing?: boolean;
        restless?: boolean;
        tremors?: boolean;
        other?: string;
        pacing?: boolean;
        tics?: boolean;
        threatening?: boolean;
        mannerisms?: boolean;
        appearsSedated?: boolean;
        psychomotorRet?: boolean;
    };
    language?: {
        rate?: {
            normal?: boolean;
            pressured?: boolean;
            slow?: boolean;
        };
        quality?: {
            normal?: boolean;
            apraxic?: boolean;
            dysarthric?: boolean;
            paraphasia?: boolean;
            clanging?: boolean;
            echolalia?: boolean;
            incoherent?: boolean;
            neologisms?: boolean;
        };
        volume?: {
            normal?: boolean;
            loud?: boolean;
            soft?: boolean;
        };
    };
    mood?: {
        normal?: boolean;
        euphoric?: boolean;
        elevated?: boolean;
        angry?: boolean;
        depress?: boolean;
        anxious?: boolean;
        irritable?: boolean;
        other?: boolean;
    };
    affect?: {
        broad?: boolean;
        blunted?: boolean;
        reactive?: boolean;
        flat?: boolean;
        congruent?: boolean;
        labile?: boolean;
        restricted?: boolean;
        other?: string;
        inappropriate?: boolean;
    };
    thoughtFormation?: {
        logical?: boolean;
        concrete?: boolean;
        obsessive?: boolean;
        unexplainedDizziness?: boolean;
        illogical?: boolean;
        relevant?: boolean;
        blocking?: boolean;
        sequential?: boolean;
        irrelevant?: boolean;
        circumstantial?: boolean;
        indecisive?: boolean;
        distractible?: boolean;
        tangential?: boolean;
        goalDirected?: boolean;
        other?: string;
        flightofIdeas?: boolean;
        looseAssociations?: boolean;
    };
    thoughtContent?: {
        delusions?: boolean;
        somatic?: boolean;
        suicidal?: boolean;
        separationLossDwelling?: boolean;
        guilt?: boolean;
        homicidal?: boolean;
        phobias?: boolean;
        obsessions?: boolean;
        grandiose?: boolean;
        suspicious?: boolean;
        hopelessness?: boolean;
        futureOriented?: boolean;
        hallucinations?: boolean;
    };
    orientation?: {
        person?: boolean;
        place?: boolean;
        time?: boolean;
        situation?: boolean;
    };
    estimateIntellect?: {
        average?: boolean;
        upAverage?: boolean;
        downAverage?: boolean;
        borderline?: boolean;
        mentalRetardation?: boolean;
        knownDisability?: string;
    };
    memory?: {
        adequate?: boolean;
        impaired?: boolean;
        recent?: boolean;
        remote?: boolean;
        selective?: boolean;
    };
    perception?: {
        auditoryHallucinations?: boolean;
        visualHallucinations?: boolean;
        depersonalization?: boolean;
        traumaticFlashbacks?: boolean;
        ideasofReference?: boolean;
    };
    judgment?: {
        good?: boolean;
        fair?: boolean;
        impaired?: boolean;
        poor?: boolean;
    };
    impulseControl?: {
        fair?: boolean;
        poor?: boolean;
        severe?: boolean;
        adequate?: boolean;
        wNL?: boolean;
        mildlydistractible?: boolean;
        moderately?: boolean;
    };
}


