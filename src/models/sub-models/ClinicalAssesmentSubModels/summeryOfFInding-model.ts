

export interface findingModel {
    code: string;
    description: string;
}

export interface nonReportedOrDescribe {
    nonReported:boolean;
    describePlan:string;
}

export interface SummeryOfFindingModel {
    summeryOfFindingNarrative: string;
    I1: findingModel;
    // I2: findingModel;
    II1: findingModel;
    // II2: findingModel;
    III: findingModel;
    IV: findingModel;
    V: findingModel;
    advanceDirectives:nonReportedOrDescribe;

}

