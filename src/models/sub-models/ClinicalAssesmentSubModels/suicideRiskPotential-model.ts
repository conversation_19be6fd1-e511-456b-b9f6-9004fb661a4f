
export interface SuicideRiskPotentialModel {

    noRiskfactorsIdentified: boolean,
    suicideAttempts: {
        actionMethod: string,
        Ideation: string,
        plan: string,
    },
    homicideAttempts: {
        actionMethod: string,
        Ideation: string,
        plan: string,
    },
    historyOfImpulsiveBehaviors: boolean,
    safeEnviornment: boolean,
    crueltytoAnimalsHistory: boolean,
    fireSetting: boolean,
    otherRiskFactors: string,
    lethalityAssessment: string,
    addtionalComments: string
}

export enum lethalityAssessment {
    NONE = "NONE",
    LOW = "LOW",
    MODERATE = "MODERATE",
    HIGH = "HIGH",
    IMMINENT = "IMMINENT",
}

