

export interface CurrentServicesModel {
    mentalHealth: {
        nonReported: boolean;
        explanation: string;
    },
    substanceAbuse: {
        nonReported: boolean
        explanation: string;
    },
    medical: {
        nonReported: boolean
        explanation: string;
    },
    vocational: {
        nonReported: boolean
        explanation: string;
    },
    developmental: {
        nonReported: boolean
        explanation: string;
    },
    other: {
        nonReported: boolean
        explanation: string;
    }
    treatmentAttitudeOverTime: string;
    previousRecoveryFactors: string;
    notes: string;
}