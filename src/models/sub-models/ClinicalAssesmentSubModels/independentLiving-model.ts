export interface IndependentLivingModel {
    assistanceWithSkills: {
        feedingSelf: boolean;
        financialAssistance: boolean;
        applyingForBenefits: boolean;
        mealPreparation: boolean;
        legalAssistance: boolean;
        academicEnrollment: boolean;
        groceryShopping: boolean;
        attendingCourtMandatedEvents: boolean;
        vocationalAssistance: boolean;
        nutrition: boolean;
        accessingSpecializedServices: boolean;
        basicHygiene: boolean;
        accessingSupportSystems: boolean;
        toiletTraining: boolean;
        transportation: boolean;
        otherSkills: String;
    }
}