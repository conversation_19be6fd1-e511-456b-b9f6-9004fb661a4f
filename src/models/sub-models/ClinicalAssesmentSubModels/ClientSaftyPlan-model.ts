
export interface PeaopleModel {
    name1?: {
        name: string;
        phone: string;
    },
    name2?: {
        name?: string;
        phone?: string;
    },
    name3?: {
        name?: string;
        phone?: string;
    },
}

export interface ClientSaftyPlanModel {

    people?: PeaopleModel;
    professionals?: {
        clinicianName?: string;
        clinicianPhone?: string;
        docotorName?: string;
        docotorPhone?: string;
        careServiceAddress?: string;
        enviornmentSafe?: {
            1?: string;
            2?: string;
        },
        agreement?: {
            agree?: string;
            clientSignature?: string;
            clientDate?: string;
            therapistSignature?: string;
            therapistDate?: string;
        }
    }
}



