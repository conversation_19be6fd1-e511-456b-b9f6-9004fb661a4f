import { Types } from "mongoose";
import { RepeatType } from "../appointment-model";

export interface claimEligibilityDetails {
  subscriber?: {
    memberId: string,
    firstName: string,
    lastName: string,
    gender: string,
    entityIdentifier: string,
    entityType: string,
    dateOfBirth: string,
    address: {
      address1: string,
      city: string,
      state: string,
      postalCode: string,
    }
  };
  memberId?: string;
}

export interface claimEligibilityDetailsMD {
  ins_city: string,
  plan_begin_date: string,
  ins_name_f: string,
  eligid: string,
  ins_sex: string,
  ins_addr_1: string,
  ins_name_l: string,
  ins_number: string,
  group_number: string,
  elig_result_date: string,
  plan_number: string,
  ins_dob: string,
  elig_result_time: string,
  ins_state: string,
}