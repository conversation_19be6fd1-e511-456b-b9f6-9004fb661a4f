export interface WellCareServiceTypeModel {
    behaviourService?: {
        intensiveOutpatient?: boolean;
        csu?: boolean;
        inpatient?: boolean;
        subAcute?: boolean;
        detox?: boolean;
        routineOutpatient?: boolean;
        caseManagement?: boolean;
        residential?: boolean;
        rehabilitation?: boolean;
        ect?: boolean;
        other?: boolean;
        detailsForOther?: string;
    }
    medicalService?: {
        dmePurchase?: boolean;
        dmeRental?: boolean;
        homeHealth?: boolean;
        inpatientAdmission?: boolean;
        inpatientRehab?: boolean;
        ltach?: boolean;
        skilledTherapy?: boolean;
        snf?: boolean;
        surgeryOutpatient?: boolean;
        surgeryInpatient?: boolean;
        other?: boolean;
        detailsForOther?: string;
    }
    transpotation?: {
        air?: boolean;
        land?: boolean;
        mileage?: string;
        trip?: string;
        isO2Needed?: boolean;
        pickUpAddress?: string;
        dropOffAddress?: string;
    }
}