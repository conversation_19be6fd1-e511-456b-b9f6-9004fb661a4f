import * as mongoose from "mongoose";
import { Document, Schema } from "mongoose";

export interface AdminSubmitClaimByJotFormDocument extends Document {
  clientId: mongoose.Types.ObjectId | null;
  therapistId: mongoose.Types.ObjectId | null;
  memberId: string;
  submission_status: string;
  response: string;
  payload: any;
  claimData: any;
  submittedBy: mongoose.Types.ObjectId | null;
  submittedAt: Date;
}

const AdminSubmitClaimByJotFormSchema = new Schema(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      ref: "users",
      required: false
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      ref: "users",
      required: false
    },
    memberId: {
      type: String,
      required: false
    },
    submission_status: {
      type: String,
      enum: ["Success", "Failure", "Pending"],
      default: "Pending"
    },
    response: {
      type: String,
      required: false
    },
    payload: {
      type: Schema.Types.Mixed,
      required: false
    },
    claimData: {
      type: Schema.Types.Mixed,
      required: false
    },
    submittedBy: {
      type: Schema.Types.ObjectId,
      ref: "users",
      required: false
    },
    submittedAt: {
      type: Date,
      default: Date.now
    }
  },
  {
    timestamps: true
  }
);

export const AdminSubmitClaimByJotFormModel = mongoose.model<AdminSubmitClaimByJotFormDocument>(
  "adminsubmitclaimbyjotforms",
  AdminSubmitClaimByJotFormSchema
); 