import * as mongoose from "mongoose";
import { Types } from "mongoose";

interface Common {
    therapistId?: Types.ObjectId;
    clientId?: Types.ObjectId;
    note: string;
    createdAt?: Date;
    meetingStartedTime?: Date;
    isMeetingTranscribe: boolean;
    meetingId?: Types.ObjectId;
    mergedMeetings?: [Types.ObjectId];
    diagnosisNoteId?: Types.ObjectId | diagnosisNoteId;
    claimStatus?: string;
    transcribeId?: Types.ObjectId;
    errorMsg?: string;
    therapistDetails?: therapistDetails,
    clientDetails?: clientDetails,
    clientInsuranceDetails?: clientInsuranceDetails,
    diagnosisNoteDetails?: diagnosisNoteId,
    deleteTreatmentHistory?: boolean;
    cronjobCount?: number;
    flag?: boolean;
    claimSubmittedTime?: Date;
    subTherapistId?: Types.ObjectId;
    claimSubmittedUser?: string;
    pcn?: string;
    paidAmount?: string;
}
interface therapistDetails {
    _id: Types.ObjectId,
    email: string,
    firstname: string,
    lastname: string
}
interface clientInsuranceDetails {
    _id: Types.ObjectId,
    insuranceCompanyId: Types.ObjectId,
}
interface receiver {
    organizationName: string
}
interface clientDetails {
    _id: Types.ObjectId,
    email: string,
    firstname: string,
    lastname: string,
    insuranceId: Types.ObjectId,
    lavniTestAccount: boolean
}
interface diagnosisNoteId {
    _id: Types.ObjectId,
    encounterID: string,
    patientID: string,
    updatedByTherapist?: boolean,
    isVonageTranscribe?: boolean,
}


export interface DTreatmentHistory extends Common { }

export interface ITreatmentHistory extends Common, mongoose.Document {
    cronjobCount?: number;
    _id: Types.ObjectId
}
