import * as mongoose from "mongoose";

export enum NotificationEvent {
  REQUEST_SENT = "REQUEST_SENT",
  REQUEST_APPROVED = "REQUEST_APPROVED",
  APPOINMENT_CREATED = "APPOINMENT_CREATED",
  APPOINMENT_UPDATED = "APPOINMENT_UPDATED",
  HOMEWORK_ASSIGNED = "HOMEWORK_ASSIGNED",
  HOMEWORK_UPDATED = "HOMEWORK_UPDATED",
  HOMEWORK_APPROVED = "HOMEWORK_APPROVED",
  HOMEWORK_REJECTED = "HOMEWORK_REJECTED",
  GOAL_ASSIGNED = "GOAL_ASSIGNED",
  GOAL_UPDATED = "GOAL_UPDATED",
  GOAL_COMPLETED = "GOAL_COMPLETED",
  MISSED_CALL = "MISSED_CALL",
  BECAME_PREMIUM = "BECAME_PREMIUM",
  REVOKE_PREMIUM = "REVOKE_PREMIUM",
  MONTH<PERSON>Y_WITHDRAWAL = "MONTHLY_WITHDRAWAL",
  TRANSFER = "TRANSFER",
  APPOINTMENT_REMINDER_NEXT_DAY = "APPOINTMENT_REMINDER_NEXT_DAY",
  APPOINTMENT_REMINDER_IN_15_MINUTES = "APPOINTMENT_REMINDER_IN_15_MINUTES",
  APPOINTMENT_REMINDER_IN_30_MINUTES = "APPOINTMENT_REMINDER_IN_30_MINUTES",
  APPOINTMENT_REMINDER_IN_1_HOUR = "APPOINTMENT_REMINDER_IN_1_HOUR",
  PREMIUM_MONTHLY_PAYMENT = "PREMIUM_MONTHLY_PAYMENT",
  REQUEST_REMOVED = "REQUEST_REMOVED",
  UPLOAD_DOCUMENT = "UPLOAD_DOCUMENT",
  GRP_CHAT_MEMBER_ADDED = "GRP_CHAT_MEMBER_ADDED",
  GRP_CHAT_MEMBER_REMOVED = "GRP_CHAT_MEMBER_REMOVED",
  GRP_CHAT_JOIN_REQUEST_SEND = "GRP_CHAT_JOIN_REQUEST_SEND",
  GRP_CHAT_JOIN_REQUEST_CANCELED = "GRP_CHAT_JOIN_REQUEST_CANCELED",
  GRP_CHAT_JOIN_REQUEST_ACCEPTED = "GRP_CHAT_JOIN_REQUEST_ACCEPTED",
  GRP_CHAT_JOIN_REQUEST_REJECTED = "GRP_CHAT_JOIN_REQUEST_REJECTED",
  NEW_TWILIO_MESSAGE_FROM_CLIENT = "NEW_TWILIO_MESSAGE_FROM_CLIENT",
}

interface Common {
  senderId: mongoose.Types.ObjectId;
  receiverId: mongoose.Types.ObjectId;
  event: string;
  link: string;
  content: string;
  variant?: string;
  readStatus: boolean;
  twilioMessageReadStatus?: boolean;
}

export interface DNotification extends Common {}

export interface INotification extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}

export enum NotificationVarient {
  INFO = "info",
  SUCCESS = "success",
  ERROR = "error",
  WARNING = "warning",
}
