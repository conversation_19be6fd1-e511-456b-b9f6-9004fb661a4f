import * as mongoose from "mongoose";

interface Common {
  therapistId: mongoose.Types.ObjectId;
  responseTime?: number;
  availability?: number;
  followUpAppointments?: number;
  totalSessions?: number;
  missedAppointments?: number;
  loyaltyYears?: number;
  scheduledAppointments?: number;
  noOfMatches?: number;
  score?: number;
}

export interface DTransaction extends Common {}

export interface ITherapistScore extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
  createdAt?: Date;
  updatedAt?: Date;
}
