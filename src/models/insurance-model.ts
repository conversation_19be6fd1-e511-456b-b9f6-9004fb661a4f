import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { DependentModel } from "./sub-models/dependent-model";
import { SubscriberModel } from "./sub-models/subscriber-model";

export enum InsuranceState {
  'Alabama' = 'AL',
  'Alaska' = 'AK',
  'Arizona' = 'AZ',
  'Arkansas' = 'AR',
  'California' = 'CA',
  'Colorado' = 'CO',
  'Connecticut' = 'CT',
  'Delaware' = 'DE',
  'Florida' = 'FL',
  'Georgia' = 'GA',
  'Hawaii' = 'HI',
  'Idaho' = 'ID',
  'Illinois' = 'IL',
  'Indiana' = 'IN',
  'Iowa' = 'IA',
  'Kansas' = 'KS',
  'Kentucky' = 'KY',
  'Louisiana' = 'LA',
  'Maine' = 'ME',
  'Maryland' = 'MD',
  'Massachusetts' = 'MA',
  'Michigan' = 'MI',
  'Minnesota' = 'MN',
  'Mississippi' = 'MS',
  'Missouri' = 'MO',
  'Montana' = 'MT',
  'Nebraska' = 'NE',
  'Nevada' = 'NV',
  'New Hampshire' = 'NH',
  'New Jersey' = 'NJ',
  'New Mexico' = 'NM',
  'New York' = 'NY',
  'North Carolina' = 'NC',
  'North Dakota' = 'ND',
  'Ohio' = 'OH',
  'Oklahoma' = 'OK',
  'Oregon' = 'OR',
  'Pennsylvania' = 'PA',
  'Puerto Rico' = 'PR',
  'Rhode Island' = 'RI',
  'South Carolina' = 'SC',
  'South Dakota' = 'SD',
  'Tennessee' = 'TN',
  'Texas' = 'TX',
  'Utah' = 'UT',
  'Vermont' = 'VT',
  'Virginia' = 'VA',
  'Washington' = 'WA',
  'WashingtonDC' = 'DC',
  'West Virginia' = 'WV',
  'Wisconsin' = 'WI',
  'Wyoming' = 'WY'
}

export enum partner {
  'test' = 'Test',
  'My Cigna' = 'CIGNA',
  'Cigna Behavioral' = 'CIGNA',
  'Cigna' = 'CIGNA',
  'Healthy Blue Medicaid' = 'HBLUNC',
  'Molina Healthcare South Carolina' = 'SC Medicaid',
  'Healthy Blue by Blue Choice of SC' = 'SC Medicaid',
  'BCBS of NC' = 'NCBCBS',
  'Blue Cross Blue Shield South Carolina' = 'SCBCBS',
  'Blue Cross Blue Shield Federal' = 'NCBCBS',
  'United Healthcare' = 'UHC',
  'United Health Care Community Plan' = 'UHC',
  'United Health Care Community Plan ' = 'UHC',
  'Wellcare Medicaid' = 'WELLCR',
  'Carolina complete health' = 'CNTENE',
  'Blue Cross Blue Sheild' = 'NC BCBS',
  'Absolute Total Care' = 'SC Medicaid',
  'Anthem' = 'ANTHME',
  'AETNA' = 'AETNA',
  'AmeriHealth' = 'AMNCN',
  'Alliance Health' = 'NC Medicaid',
  'Trillium' = 'NC Medicaid',
  'United UMR' = 'UHC',
  'Partners' = 'Centene',
  'NC Medicaid' = 'NC Medicaid',
  'CareSource' = 'CareSource of Georgia',
  'Amerigroup' = 'Amerigroup DC,GA & NM',
  'First Choice by Select Health' = 'SC Medicaid',
  'First Health Network' = 'First Health Network fka CCN',
  'NC Medicare Part B' = 'NC Medicare Part B',
  'Tricare' = 'Tricare East Region',
  'Healthy Blue South Carolina' = 'Healthy Blue South Carolina',
  'Select Health of South Carolina' = 'Select Health of South Carolina',
  'South Carolina Blue Cross Blue Shield' = 'South Carolina Blue Cross Blue Shield',
};

export enum Tradingpartner_ServiceId {
  'test' = '9496',
  'My Cigna' = '62308',
  'Cigna Behavioral' = '62308',
  'Cigna' = '62308',
  'Healthy Blue Medicaid' = '00602',
  'Molina Healthcare South Carolina' = '46299',
  'Healthy Blue by Blue Choice of SC' = 'SCMCD',
  'BCBS of NC' = 'SB810',
  'Blue Cross Blue Shield South Carolina' = '00401',
  'Blue Cross Blue Shield South Carolina ' = '00401',
  'Blue Cross Blue Shield Federal' = 'SB810',
  'United Healthcare' = '87726',
  'United Health Care Community Plan' = '87726',
  'United Health Care Community Plan ' = '87726',
  'Wellcare Medicaid' = '14163',
  'Carolina complete health' = '68069',
  'Blue Cross Blue Sheild' = 'SB810',
  'Absolute Total Care' = '68055',
  'Anthem' = 'SB810',
  'AETNA' = '60054',
  'AmeriHealth' = '81671',
  'Alliance Health' = 'NCMCD',
  'Trillium' = 'NCMCD',
  'United UMR' = '87726',
  'Partners' = '68069',
  'NC Medicaid' = 'NCMCD',
  'CareSource' = 'GACS1',
  'Amerigroup' = '26375',
  'First Choice by Select Health' = 'SCMCD',
  'First Health Network' = '73159',
  'NC Medicare Part B' = '11502',
  'Tricare' = 'TREST',
  'Healthy Blue South Carolina' = '00403',
  'Select Health of South Carolina' = '23285',
  'South Carolina Blue Cross Blue Shield' = '00401',
};

export enum Insurance_company {
  'test' = 'Test',
  'My Cigna' = 'CIGNA',
  'Cigna Behavioral' = 'CIGNA Behavioral Health',
  'Cigna' = 'CIGNA',
  'Healthy Blue Medicaid' = 'Healthy Blue North Carolina',
  'Molina Healthcare South Carolina' = 'SC Medicaid',
  'Healthy Blue by Blue Choice of SC' = 'SC Medicaid',
  'BCBS of NC' = 'NC BCBS',
  'Blue Cross Blue Shield South Carolina' = 'SC BCBS',
  'Blue Cross Blue Shield Federal' = 'NC BCBS',
  'United Healthcare' = 'United Health Care',
  'United Health Care Community Plan' = 'United Health Care',
  'United Health Care Community Plan ' = 'United Health Care',
  'Wellcare Medicaid' = 'Wellcare',
  'Carolina complete health' = 'Centene',
  'Blue Cross Blue Sheild' = 'NC BCBS',
  'Absolute Total Care' = 'SC Medicaid',
  'Anthem' = 'Anthem',
  'AETNA' = 'AETNA',
  'AmeriHealth' = 'Amerihealth Caritas North Carolina',
  'Alliance Health' = 'NC Medicaid',
  'Trillium' = 'NC Medicaid',
  'United UMR' = 'United Health Care',
  'Partners' = 'Centene',
  'NC Medicaid' = 'NC Medicaid',
  'CareSource' = 'CareSource of Georgia',
  'Amerigroup' = 'Amerigroup DC,GA & NM',
  'First Choice by Select Health' = 'SC Medicaid',
  'First Health Network' = 'First Health Network fka CCN',
  'NC Medicare Part B' = 'NC Medicare Part B',
  'Tricare' = 'Tricare East Region',
  'Healthy Blue South Carolina' = 'Healthy Blue South Carolina',
  'Select Health of South Carolina' = 'Select Health of South Carolina',
  'South Carolina Blue Cross Blue Shield' = 'South Carolina Blue Cross Blue Shield',
}; 

interface Common {
  subscriber: SubscriberModel;
  dependent: DependentModel;
  insuranceCardId?: Types.ObjectId;
  insuranceCardBackId?: Types.ObjectId;
  clientId: Types.ObjectId;
  insuranceAccessToken?: string;
  controlNumber?: string;
  organizationDetails?: any;
  insuranceCompanyId?: Types.ObjectId | any;
}

export interface DInsurance extends Common { }

export interface IInsurance extends Common, mongoose.Document {
  _id: Types.ObjectId;
}

export interface IInsuranceAndClient{
    insurance: IInsurance[];
    client: any;
}

export interface InsuranceToken {
  client_id: string;
  client_secret: string;
  grant_type: string;
}

export interface AccessTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface Eligibility {
  controlNumber: string;
  reassociationKey: string;
  tradingPartnerServiceId: string;
  provider: EligibilityProvider;
  subscriber: EligibilitySubscriber;
  benefitInformation: BenefitInformation[];
}

export interface EligibilityProvider {
  entityIdentifier: string;
  entityType: string;
  serviceProviderNumber: string;
}

export interface EligibilitySubscriber {
  memberId: string;
  firstName: string;
  lastName: string;
  gender: string;
  entityIdentifier: string;
  entityType: string;
  dateOfBirth: string;
  groupNumber: string;
  relationToSubscriber: string;
  insuredIndicator?: string;
  maintenanceTypeCode?: string;
  maintenanceReasonCode?: string;
}

export interface BenefitInformation {
  code: string;
  name: string;
  coverageLevel?: string;
  benefitAmount?: string;
  serviceTypes?: string[];
}