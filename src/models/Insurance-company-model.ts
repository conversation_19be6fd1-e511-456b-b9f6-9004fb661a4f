import * as mongoose from "mongoose";

interface Common {
  insuranceCompany: string;
  contractPrice?: number;
  coPayment?: number;
  states?: string[];
  fax?: string;
  tradingPartnerServiceId?:string;
  organizationName?: string;
  payerName?: string;
  link?: string;
  isCommercialInsurance?: boolean;
  isMedicaid?: boolean;
}

export interface DInsuranceCompany extends Common {}

export interface IInsuranceCompany extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}