import * as mongoose from "mongoose";

interface Common {
    name: string;
    email: string;
    reviewMessage: string;
    ratingValue: string;
    status: LavniReviewStatus;
    clientId?:mongoose.Types.ObjectId;
    therapistId?:mongoose.Types.ObjectId;
    adminId?:mongoose.Types.ObjectId;

}

export interface DLavniReview extends Common { }

export interface ILavniReview extends Common, mongoose.Document { }

export enum LavniReviewStatus {
    PENDING = "PENDING",
    REJECTED = "REJECTED",
    APPROVED ="APPROVED"
}