import * as mongoose from "mongoose";

export enum PaymentStatus {
  PENDING = "pending",
  PAID = "paid"
}

interface Common {
  clientId: mongoose.Types.ObjectId;
  insuranceId:mongoose.Types.ObjectId;
  paidAmountByInsurance: string;
  dueAmount: number;
  paymentStatus: string;  
  paymentMonth:string;
}

export interface DInvoice extends Common {}

export interface IInvoice extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}
