import * as mongoose from "mongoose";
import { Types } from "mongoose";

interface Common {
  clientId: Types.ObjectId;
  therapistId: Types.ObjectId;
  conversationId: string;
  chatId: Types.ObjectId;
  twilioParticipantId: string;
  phoneNumber:string;
}

export interface DSmsChatConversation extends Common {}

export interface ISmsChatConversation extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}
