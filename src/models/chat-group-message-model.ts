import * as mongoose from "mongoose";
import { Types } from "mongoose";

export enum ChatGroupMessageStatus {
  DELETED = "DELETED",
}

interface Common {
  groupId: Types.ObjectId;
  messageText: string;
  createdBy: Types.ObjectId;
  messageStatus?: ChatGroupMessageStatus;
  preMessageId?: Types.ObjectId;
  mediaFileId?: Types.ObjectId;
}

export interface DChatGroupMessage extends Common {}

export interface IChatGroupMessage extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
  createdAt: Date;
}
