import * as mongoose from "mongoose";

interface Common {
  meetingId: string;
  clientId: mongoose.Types.ObjectId;
  therapistId: mongoose.Types.ObjectId;
  transcriptText: string[];
  videoUrl: string;
  transCribeInProcess: boolean;
  meetingStartedTime: Date;
  speakersDetected?: boolean;
  speakersArray?: mongoose.Types.ObjectId[];
  aiResponse?: string;
}

export interface DTranscribe extends Common {}

export interface ITranscribe extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
  createdAt?: Date;
}
