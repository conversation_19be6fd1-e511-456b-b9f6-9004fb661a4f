import * as mongoose from "mongoose";
import { Types } from "mongoose";

interface Common {
  originalFileName: string;
  fileNameInAwsBucket: string;
  type: string;
  createdBy: Types.ObjectId;
  title: string;
  description: string;
  parentFolderId?: Types.ObjectId | null;
}

export interface DTrainingDocumentFolderFile extends Common {}

export interface ITrainingDocumentFolderFile extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
