import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { Preference } from "./sub-models/preference-model";
import { DUser, IUser } from "./user-model";
import { claimEligibilityDetails, claimEligibilityDetailsMD } from "./sub-models/eligibility-model";

export enum SubscriptionStatus {
  ACTIVE = "active",
  PAST_DUE = "past_due",
  UNPAID = "unpaid",
  CANCELED = "canceled",
  INCOMPLETE = "incomplete",
  INCOMPLETE_EXPIRED = "incomplete_expired",
  TRIALING = "trialing",
  ALL = "all",
  ENDED = "ended",
}

export enum PremiumStatus {
  ACTIVE = "active",
  REVOKED = "revoked",
  NOT_SET = "not_set",
}

export enum testSubscriptionStatus {
  ACTIVE = "active",
  REVOKED = "revoked",
}

interface Common {
  incognito?: boolean;
  incognitoPopupShow?: boolean;
  dislikedTherapists?: mongoose.Types.ObjectId[];
  preference?: Preference;
  maritalStatus?: string;
  unit?: string;
  homePhone?: string;
  workPhone?: string;
  voiceMail?: string;
  stripeCustomerId?: string;
  subscriptionId?: string;
  subscriptionStatus?: string;
  testSubscriptionStatus?: string;
  insuranceId?: mongoose.Types.ObjectId;
  secondaryInsuranceId?: mongoose.Types.ObjectId;
  premiumStatus?: string;
  premiumMembershipStartedDate?: Date;
  premiumMembershipRevokedDate?: Date;
  chatWordCount?: Number;
  freeUser?: boolean;
  clientActiveStatus?: boolean;
  skip?: boolean;
  PersonalizeMatchData?: {};
  copaymentAmount?: Number;
  //isPremiumUserNotificationRead?:boolean;
  //invoices?: mongoose.Types.ObjectId[];
  isSignature?: boolean;
  assesmentSignature?: Buffer;
  claimEligibilityDetails?: claimEligibilityDetails;
  claimEligibilityMdDetails?: claimEligibilityDetailsMD;
  primaryTherapist?: Types.ObjectId;
  claimEligibilityMdErrorDetails?: string;
  claimEliMdInactiveDetails?:claimEligibilityDetailsMD;
  last_pay_copayment_time?: Date;
  last_pay_copayment_amount?: Number;
}

export interface DClient extends Common, DUser { }

export interface IClient extends Common, IUser, mongoose.Document { }
