import * as mongoose from "mongoose";

interface Common {
    meetingId: string,
    rate: number,
    feedback?: string,
    therapistComfortable?: boolean,
    satisfied?: boolean,
    nextSessionScheduled?: boolean,
    needCall?: boolean,
    is_read?: boolean,
    createdBy: mongoose.Types.ObjectId
}

export interface DSessionFeedBack extends Common {}

export interface ISessionFeedBack extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}
