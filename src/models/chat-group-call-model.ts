import * as mongoose from "mongoose";
import { Types } from "mongoose";

export enum ChatGroupCallCallingStatus {
  PENDING = "PENDING",
  ONGOING = "ONGOING",
  ENDED = "ENDED",
}

interface Common {
  groupId: Types.ObjectId;
  createdBy: Types.ObjectId;
  start: Date;
  callingStatus: ChatGroupCallCallingStatus;
}

export interface DChatGroupCall extends Common {}

export interface IChatGroupCall extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}
