import * as mongoose from "mongoose";
import { AmbetterBasicInformationModel } from "./sub-models/AmbetterAuthFormSubModels/ambetter-basic-information-model";
import { AmbetterMemberInformationModel } from "./sub-models/AmbetterAuthFormSubModels/ambetter-member-information-model";
import { AmbetterProviderModel } from "./sub-models/AmbetterAuthFormSubModels/ambetter-provider-model";
import { AmbetterFacilityInformationModel } from "./sub-models/AmbetterAuthFormSubModels/ambetter-facility-information-model";
import { AmbetterAuthorizationRequestModel } from "./sub-models/AmbetterAuthFormSubModels/ambetter-authorization-request-model";
import { DAuthorizationForm, IAuthorizationForm } from "./authorization-form-model";


export interface Common {
    basicInformation: AmbetterBasicInformationModel,
    memberInformation: AmbetterMemberInformationModel,
    providerInformation: AmbetterProviderModel,
    facilityInformation: AmbetterFacilityInformationModel,
    authorizationRequest: AmbetterAuthorizationRequestModel;
}

export interface DAmbetterAuthForm extends Common, DAuthorizationForm { }

export interface IAmbetterAuthForm extends Common, IAuthorizationForm, mongoose.Document {
}
