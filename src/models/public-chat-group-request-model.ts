import * as mongoose from "mongoose";
import { Types } from "mongoose";
export enum PublicChatGroupRequestStatusType {
  PENDING = "PENDING",
  ACCEPTED = "ACCEPTED",
  REJECTED = "REJECTED",
  CANCELLED = "CANCELLED",
}
interface Common {
  groupId: Types.ObjectId;
  userId: Types.ObjectId;
  status: PublicChatGroupRequestStatusType;
}
export interface DPublicChatGroupRequest extends Common {}
export interface IPublicChatGroupRequest extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}