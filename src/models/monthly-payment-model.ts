import * as mongoose from "mongoose";
import { TherapistTransactionArrModel } from "./sub-models/therapist-transaction-model";

interface Common {
    therapistTransaction:TherapistTransactionArrModel[];
    verifiedStatus:string;
    createdAt:Date;
    crownJobType?:string;
    timePeriodStartAt?:Date;
    timePeriodEndAt?:Date;
}

export interface DMonthlyPayment extends Common { }

export interface IMonthlyPayment extends Common, mongoose.Document {} 