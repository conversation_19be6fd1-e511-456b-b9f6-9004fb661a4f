import * as mongoose from "mongoose";
import { HealthyBlueGeneralInformationModel } from "./sub-models/HealthyBlueAuthFormSubModels/healthy-blue-general-information-model";
import { HealthyBlueServiceInformationModel } from "./sub-models/HealthyBlueAuthFormSubModels/healthy-blue-service-information-model";
import { HealthyBlueProviderModel } from "./sub-models/HealthyBlueAuthFormSubModels/healthy-blue-provider-model";
import { HealthyBluePractitionerModel } from "./sub-models/HealthyBlueAuthFormSubModels/healthy-blue-practitioner-model";
import { HealthyBlueAuthorizationModel } from "./sub-models/HealthyBlueAuthFormSubModels/healthy-blue-authorization-model";
import { DAuthorizationForm, IAuthorizationForm } from "./authorization-form-model";

export interface Common {
    generalInformation: HealthyBlueGeneralInformationModel,
    serviceInformation: HealthyBlueServiceInformationModel,
    providerInformation: HealthyBlueProviderModel;
    practitionerInformation: HealthyBluePractitionerModel;
    authorization: HealthyBlueAuthorizationModel;
    referenceNumber?: string; 
    signature?: string;
}

export interface DHealthyBlueAuthForm extends Common, DAuthorizationForm { }

export interface IHealthyBlueAuthForm extends Common, IAuthorizationForm, mongoose.Document {
}