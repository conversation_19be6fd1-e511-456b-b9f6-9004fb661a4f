import { FormHeader } from "./sub-models/Digital-AssessmentFormSubModels/formHeader-model";
import { GeneralInformationModel } from "./sub-models/Digital-AssessmentFormSubModels/generalInformation-model";
import { BiologicalFunctions } from "./sub-models/Digital-AssessmentFormSubModels/biologicalFunctions-model";
import { NameOfSubstance } from "./sub-models/Digital-AssessmentFormSubModels/nameOfSubstance-model";
import { TobaccoUse } from "./sub-models/Digital-AssessmentFormSubModels/tobaccoUse-model";
import { OutPatientTreatementHistory } from "./sub-models/Digital-AssessmentFormSubModels/outPatientTreatementHistory-model";
import { InpatientTreatementHistory } from "./sub-models/Digital-AssessmentFormSubModels/inpatientTreatementHistory-model";
import { BioPsychosocialDevelopemntHistory } from "./sub-models/Digital-AssessmentFormSubModels/bioPsychosocialDevelopemntHistory-model";
import { TraumaHistory } from "./sub-models/Digital-AssessmentFormSubModels/traumaHistory-model";
import { RiskToSelfAndOthers } from "./sub-models/Digital-AssessmentFormSubModels/riskToSelfAndOthers-model";
import { MentalStatus } from "./sub-models/Digital-AssessmentFormSubModels/mentalStatus-model";
import { ReleventDDSInformation } from "./sub-models/Digital-AssessmentFormSubModels/releventDDSInformation-model";
import { RelevantLegalInformation } from "./sub-models/Digital-AssessmentFormSubModels/relevantLegalInformation-model";
import { DiagnoseRecommendationDetails } from "./sub-models/Digital-AssessmentFormSubModels/diagnoseRecommendationDetails-model";
import { SymptomChicklist } from "./sub-models/Digital-AssessmentFormSubModels/symptomChicklist-model";
import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { ReleventMedicalInformation } from "./sub-models/Digital-AssessmentFormSubModels/releventMedicalInformation-model";
import { BioPsychosocialEducation } from "./sub-models/Digital-AssessmentFormSubModels/bioPsychosocialEducation-model";

export interface DigitalForm {
  therapistId?: ObjectId;
  clientId?: ObjectId;
  formHeader: FormHeader;
  generalInformation: GeneralInformationModel;
  presentingProblem: {
    description?: string;
    historyOfProblem?: string;
  };
  historyOfProblem: string;
  symptomChicklist: SymptomChicklist;
  biologicalFunction: BiologicalFunctions;
  alcoholAndDrugUseHistory: {
    historyStatus: string;
  };
  nameOfSubstance: NameOfSubstance[];
  tobaccoUseTypeDetails: TobaccoUse;
  releventMedicalInformation: ReleventMedicalInformation;
  outPatientTreatementHistory: OutPatientTreatementHistory;
  inpatientTreatementHistory: InpatientTreatementHistory;
  bioPsychosocialDevelopemntHistory: BioPsychosocialDevelopemntHistory;
  bioPsychosocialEducation: BioPsychosocialEducation;
  traumaHistory: TraumaHistory;
  riskToSelfAndOthers: RiskToSelfAndOthers;
  mentalStatus: MentalStatus;
  releventDDSInformation: ReleventDDSInformation;
  relevantLegalInformation: RelevantLegalInformation;
  diagnoseRecommendationDetails: DiagnoseRecommendationDetails;
}

export interface DDigitalAssessment extends DigitalForm {}

export interface IDigitalAssessment extends DigitalForm, mongoose.Document {
  _id: Types.ObjectId;
}
