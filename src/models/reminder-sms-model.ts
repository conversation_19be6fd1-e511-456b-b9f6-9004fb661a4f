import * as mongoose from "mongoose";

export enum REMINDER_SMS_TYPE {
    MATCH_APPOINTMENT_NO_INSUARANCE = "MATCH_APPOINTMENT_NO_INSUARANCE",
    NO_MATCH_INSUARANCE = "NO_MATCH_INSUARANCE",
    MATCH_APPOINTMENT_INSUARANCE = "MATCH_APPOINTMENT_INSUARANCE",
    NO_MATCH_NO_INSUARANCE = "NO_MATCH_NO_INSUARANCE",
    NO_SHOW_FIRST_APPOINTMENT = "NO_SHOW_FIRST_APPOINTMENT"
}

interface Common {
    clientId: mongoose.Types.ObjectId;
    sentSMS: string;
    reply?: number;
    smsType?: string;
    replySMS?: string[];
}

export interface DReminderSMS extends Common { }

export interface IReminderSMS extends Common, mongoose.Document {
    _id: mongoose.Types.ObjectId;
}