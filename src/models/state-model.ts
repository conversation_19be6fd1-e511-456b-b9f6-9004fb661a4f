import * as mongoose from "mongoose";
import { Types } from "mongoose";

interface Common {
    stateName: string;
}

export interface DState extends Common {}

export interface IState extends Common, mongoose.Document {
  _id: Types.ObjectId;
}

export interface AddStateResponse {
    success: boolean;
    state?: IState;
    error?: string;
}

export interface AddStateResponseTwo {
    success: boolean;
    states?: IState[];
    error?: string;
}
