import * as mongoose from "mongoose";
import { WellCareMemberInformationModel } from "./sub-models/WellcareAuthFormSubModels/wellcare-member-information-model";
import { WellCareFacilityInformationModel } from "./sub-models/WellcareAuthFormSubModels/wellcare-facility-information-model";
import { WellCareServiceTypeModel } from "./sub-models/WellcareAuthFormSubModels/wellcare-service-type-model";
import { WellCareAuthorizationModel } from "./sub-models/WellcareAuthFormSubModels/wellcare-authorization-model";
import { WellCarePlaceTypeModel } from "./sub-models/WellcareAuthFormSubModels/wellcare-place-type-model";
import { WellCareClinicalInformationModel } from "./sub-models/WellcareAuthFormSubModels/wellcare-clinical-information-model ";
import { WellCareProviderModel } from "./sub-models/WellcareAuthFormSubModels/wellcare-provider-model";
import { DAuthorizationForm, IAuthorizationForm } from "./authorization-form-model";

export interface Common {
    authorizationInformation:WellCareAuthorizationModel,
    memberInformation: WellCareMemberInformationModel,
    providerInformation:WellCareProviderModel;
    facilityInformation: WellCareFacilityInformationModel;
    serviceTypeInformation: WellCareServiceTypeModel,
    placeTypeInformation:WellCarePlaceTypeModel;
    clinicalInformation:WellCareClinicalInformationModel;
}

export interface DWellCareAuthForm extends Common, DAuthorizationForm { }

export interface IWellCareAuthForm extends Common, IAuthorizationForm, mongoose.Document {
}