import * as mongoose from "mongoose";
import { CarolinaCompleteGeneralInformationModel } from "./sub-models/CarolinaCompleteHealthAuthFormSubModels/carolina-complete-health-general-information-model";
import { CarolinaCompleteHealthServiceInformationModel } from "./sub-models/CarolinaCompleteHealthAuthFormSubModels/carolina-complete-health-service-information-model";
import { CarolinaCompleteHealthPrescribingProviderModel } from "./sub-models/CarolinaCompleteHealthAuthFormSubModels/carolina-complete-health-prescribing-provider-model";
import { CarolinaCompleteHealthProviderModel } from "./sub-models/CarolinaCompleteHealthAuthFormSubModels/carolina-complete-health-provider-model";
import { DAuthorizationForm, IAuthorizationForm } from "./authorization-form-model";

export interface Common {
    generalInformation: CarolinaCompleteGeneralInformationModel,
    serviceInformation: CarolinaCompleteHealthServiceInformationModel,
    providerInformation: CarolinaCompleteHealthProviderModel;
    prescibingProviderInformation: CarolinaCompleteHealthPrescribingProviderModel;
    denialReason?: string;
    date?: Date;
    signature?: any;
}

export interface DCarolinaCompleteHealthAuthForm extends Common, DAuthorizationForm { }

export interface ICarolinaCompleteHealthAuthForm extends Common, IAuthorizationForm, mongoose.Document {
}
