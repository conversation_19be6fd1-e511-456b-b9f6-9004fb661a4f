import { UserRole } from "./user-model";

export interface Caption {
    text: string;
    timestamp: string;
}

export interface TranscriptPayload {
    vonageSessionName: string;
    transcripts: Caption[];
    loggedUserRole: UserRole.THERAPIST | UserRole.CLIENT;  // Identify the sender
}

export const captionsDBModel = new Map<string, {
    therapistCaptions?: Caption[];
    clientCaptions?: Caption[];
    timestamp: number;
}>();