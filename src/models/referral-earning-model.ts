import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { TransactionType } from "./transaction-model";

export enum RewardType {
  OWNFIRST = "OWNFIRST",
  OWNSECOND = "OWNSECOND",
  REFERFIRST = "REFERFIRST",
  REFERSECOND = "REFERSECOND"
}

interface Common {
  therapistId: string;
  type: string;
  transactionAmount: number;
  accumulatedBalance: number;
  accumulatedTotalEarnings: number;
  accumulatedWithdrawals: number;
  paidStatus?: string;
  rewardType?: string;
  rewardReferralId?: Types.ObjectId | any;
  verifiedStatus?: string;
}

export interface DReferralEarning extends Common {}

export interface IReferralEarning extends Common, mongoose.Document {
  _id: Types.ObjectId;
  type: TransactionType;
  createdAt?: Date;
  updatedAt?: Date;
}
