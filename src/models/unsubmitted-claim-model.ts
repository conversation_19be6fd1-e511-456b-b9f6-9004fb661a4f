import * as mongoose from "mongoose";
import { Types } from "mongoose";

interface Common {
    claim_form?: number;
    payer_name?: string;
    accept_assign?: string;
    employment_related?: string;
    ins_name_f?: string;
    ins_name_l?: string;
    ins_addr_1?: string;
    ins_city?: string;
    ins_dob?: string;
    ins_sex?: string;
    ins_state?: string;
    ins_zip?: string;
    ins_number?: string;
    bill_taxonomy?: string;
    place_of_service_1?: string;
    prov_name_l?: string;
    prov_name_f?: string;
    prov_npi?: string;
    prov_taxonomy?: string;
    pcn?: string;
    charge_1?: number;
    pat_rel?: string;
    payerid?: string;
    total_charge?: number;
    claimNumber?: string;
    proc_code_1?: string;
    mod1_1?: string;
    diag_1?: string;
    diag_2?: string;
    from_date_1?: string;
    bill_name?: string;
    bill_addr_1?: string;
    bill_addr_2?: string;
    bill_city?: string;
    bill_state?: string;
    bill_zip?: string;
    bill_npi?: string;
    bill_id?: string;
    bill_phone?: string;
    bill_taxid?: string;
    bill_taxid_type?: string;
    diag_ref_1?: string;
    units_1?: number;
    pat_name_l?: string;
    pat_name_f?: string;
    pat_addr_1?: string;
    pat_city?: string;
    pat_state?: string;
    pat_zip?: string;
    pat_dob?: string;
    pat_sex?: string;
    status?: string;
    treatmentHistoryId?:Types.ObjectId;
}

export interface DUnsubmittedClaimMd extends Common {}

export interface IUnsubmittedClaimMd extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
