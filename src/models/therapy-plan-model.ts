import { Types } from "mongoose";
import * as mongoose from "mongoose";
import { DiagnosisModel } from "./sub-models/TherapyPlanSubModels/diagnosis-model";
import { GoalInformationModel } from "./sub-models/TherapyPlanSubModels/goal-information-model";
import { TreatmentSessionModel } from "./sub-models/TherapyPlanSubModels/treatment-session-model";
import { SignatureDetailsModel } from "./sub-models/TherapyPlanSubModels/signature-details-model";

export interface Common {
  therapistId?:ObjectId,
  clientId?:ObjectId,
  planCreationDate?: Date;
  creationDate?: Date;
  diagnosis?: DiagnosisModel;
  goalDate?: Date;
  goalInformation?: GoalInformationModel;
  treatmentSession?: TreatmentSessionModel;
  clientSignatureDetails?: SignatureDetailsModel;
  clientSignature?: Buffer;
  lrpSignatureDetails?: SignatureDetailsModel;
  lrpSignature?: Buffer;
  clinicianSignatureDetails?: SignatureDetailsModel;
  clinicianTwoSignatureDetails?: SignatureDetailsModel;
  clinicianSignature?: Buffer;
  clinicianTwoSignature?: Buffer;
  goalDate2?: Date;
  goalInformation2?: GoalInformationModel;
  treatmentSession2?: TreatmentSessionModel;
  goalDate3?: Date;
  goalInformation3?: GoalInformationModel;
  treatmentSession3?: TreatmentSessionModel;
  isSignature?:boolean;

}
export interface DTherapyPlan extends Common { }

export interface ITherapyPlan extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
