import * as mongoose from "mongoose";
import { StringOrObjectId } from "../common/util";
import { IFriendRequest } from "./friend-request-model";
import { Payment } from "./sub-models/payment-model";
import { ReminderType } from "./sub-models/reminderType-model";
import { ReminderTime } from "./sub-models/reminderTime-model";
import { PayRateType } from "./sub-models/pay-rate-type-model";
import { AdminPermission } from "./sub-models/admin-permission-model";

export enum UserRole {
  SUPER_ADMIN = "SUPER_ADMIN",
  SUB_ADMIN = "SUB_ADMIN",
  ADMIN = "ADMIN",
  THERAPIST = "THERAPIST",
  CLIENT = "CLIENT",
}

export enum UserStatus {
  PENDING = "PENDING",
  VERIFIED = "VERIFIED",
  TEMPORARY = "TEMPORARY ",
}

export enum Permission {
  statistics = "statistics",
  adminDashboard = "adminDashboard",
  viewEthnicity = "viewEthnicity",
  viewExperienceTags = "viewExperienceTags",
  viewInsuranceCompanies = "viewInsuranceCompanies",
  createAppointmentAdmin = "createAppointmentAdmin",
  viewProfessions = "viewProfessions",
  documents = "documents",
  accessManagement = "accessManagement",
  viewHashTags = "viewHashTags",
  viewThemeImage = "viewThemeImage",
  reportReviews = "reportReviews",
  reviews = "reviews",
  contactUs = "contactUs",
  articles = "articles",
  feedback = "feedback",
  newsLetterEmails = "newsLetterEmails",
  marketingEmails = "marketingEmails",
  viewMeetingsAndRecordings = "viewMeetingsAndRecordings",
  viewAllClients = "viewAllClients",
  manageClients = "manageClients",
  premiumClients = "premiumClients",
  reminderSms = "reminderSms",
  viewAllTherapists = "viewAllTherapists",
  manageTherapists = "manageTherapists",
  viewTherapistReviews = "viewTherapistReviews",
  viewTherapistsSoapReviews = "viewTherapistsSoapReviews",
  educationalDetails = "educationalDetails",
  licenseDetails = "licenseDetails",
  therapistRequests = "therapistRequests",
  availableBalances = "availableBalances",
  adminApprovePayment = "adminApprovePayment",
  referralEarnings = "referralEarnings",
  clientRewards = "clientRewards",
  notifications = "notifications",
  sessionFeedback = "sessionFeedback",
  approvalQueue = "approvalQueue",
  techTickets = "techTickets",
  profile = "profile",
}

export enum Medium {
  FACEBOOK = "FACEBOOK",
  GOOGLE = "GOOGLE",
  EMAIL = "EMAIL",
}

export enum PayRateTypes {
  PERCENTAGE_USER = "PERCENTAGE_USER",
  FLAT_USER = "FLAT_USER",
}

export enum AppointmentSMSStatus {
  BEFORE24SMS = "BEFORE24SMS",
  CONFIRMED = "CONFIRMED",
  RESCHEDULED = "RESCHEDULED",
}

interface CommonAttributes {
  firstname?: string;
  lastname?: string;
  middleInitials?: string;
  email?: string;
  secondaryEmail?: string;
  password?: string;
  role?: string;
  lastLogin?: Date;
  ethnicityId?: mongoose.Types.ObjectId;
  medium?: string;
  googleId?: string;
  facebookId?: string;
  photoId?: mongoose.Types.ObjectId;
  coverPhotoId?: mongoose.Types.ObjectId;
  videoId?: mongoose.Types.ObjectId;
  verifiedStatus?: string;
  loginVerification?: string;
  description?: string;
  blockedUser?: mongoose.Types.ObjectId[];
  verificationCode?: string;
  paymentDetails?: Payment[];
  socketId?: string;
  adminApproved?: boolean;
  friendRequests?: IFriendRequest[];
  username?: string;
  guideComplete?: boolean;
  avatarId?: mongoose.Types.ObjectId;
  avatarBackgroundId?: string;
  defaultAvatarId?: string;
  useDefaultAvatar?: boolean;
  incognito?: boolean;
  incognitoPopupShow?: boolean;
  gender?: string;
  dateOfBirth?: Date;
  blockedByAdmin?: boolean;
  streetAddress?: string;
  city?: string;
  zipCode?: string;
  state?: string;
  primaryPhone?: string;
  lavniTestAccount?: boolean;
  callRecordingAllowed?: boolean;
  hideCallTimer?: boolean;
  googleCalendarRefreshToken?: string;
  googleCalendarAccess?: boolean;
  reminderType?: ReminderType;
  reminderTime?: ReminderTime;
  smsStop?: boolean;
  avatarImage?: string;
  message?: string;
  pinNumber?: string;
  reasonToBlock?: string;
  payRateType?: PayRateType;
  priorityNumber?: number;
  adminCreated?: boolean;
  blackTherapyPriorityNumber?: number;
  relationshipTherapyPriorityNumber?: number;
  mensMentalTherapyPriorityNumber?: number;
  reminderSMS?: string;
  missed1stAppointment?: boolean;
  conversationId?: string;
  adminPermission?: AdminPermission;
  isLoginFirstTime?: boolean;
  FCMToken?: string;
  appointmentAuthToken?: string;
  appointmentAuthTokenExpiredAt?: Date;
}

export interface DUser extends CommonAttributes {
  _id?: StringOrObjectId;
}

export interface IUser extends CommonAttributes, mongoose.Document {
  [x: string]: any;

  readonly role: UserRole;

  lastLogin: Date;

  createAccessToken(): string;

  comparePassword(password: string): Promise<boolean>;

  compareVerificationCode(verificationCode: string): Promise<boolean>;
}
