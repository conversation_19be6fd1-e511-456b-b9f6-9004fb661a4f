import * as mongoose from "mongoose";
import { DueDate } from "./sub-models/due-date-model";

export enum GroupType {
    GROUP_TYPE_PUBLIC = "GROUP_TYPE_PUBLIC",
    GROUP_TYPE_PASSWORD = "GROUP_TYPE_PASSWORD",
    GROUP_TYPE_PRIVATE = "GROUP_TYPE_PRIVATE",
}

interface Common {
  groupTitle: string;  
  groupIconId : mongoose.Types.ObjectId;
  //groupType : string;
}

export interface DGroupChat extends Common {}

export interface IGroupChat extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}
