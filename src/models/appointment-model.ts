import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { RepeatInfo } from "./sub-models/repeat-info-model";

export enum AppointmentStatus {
  WAITING_FOR_APPROVAL = "WAITING_FOR_APPROVAL",
  PENDING = "PENDING",
  COMPLETED = "COMPLETED",
  REJECTED = "REJECTED",
  OVERDUE = "OVERDUE",
}

export enum MeetingStatus {
  PENDING = "PENDING",
  ONGOING = "ONGOING",
  STARTED = "STARTED",
  COMPLETED = "COMPLETED",
  WAITING_FOR_APPROVAL = "WAITING_FOR_APPROVAL"
}

export enum RepeatType {
  DOES_NOT_REPEAT = "DOES NOT REPEAT",
  WEEKLY = "WEEKLY",
  BI_WEEKLY = "BI_WEEKLY",
  MONTHLY = "MONTHLY"
}

export enum ApprovalStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

interface Common {
  therapistId: Types.ObjectId;
  clientId?: Types.ObjectId; //TODO - MAKE THIS REQUIRED ONCE CHANGES ARE MADE IN VIDEO CHAT
  start: Date;
  end: Date;
  typeOfMeeting: string;
  reminders: number[];
  title: string;
  repeatInfo: RepeatInfo;
  color: string;
  createdBy: Types.ObjectId;
  status?: AppointmentStatus;
  approvedStatus?: ApprovalStatus;
  groupId?: string;
  noOfCallingTriesByTherapist?: number;
  noOfCallingTriesByClient?: number;
  // new fields
  meetingStatus?: MeetingStatus;
  meetingId?: string;
  meetingStartedBy?: Types.ObjectId;
  GoogleCalendarEventId?: string;
  smsStatus?: string;
  therapistChangeApprovedStatusAt?: Date;
}

export interface DAppointment extends Common {}

export interface IAppointment extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}
