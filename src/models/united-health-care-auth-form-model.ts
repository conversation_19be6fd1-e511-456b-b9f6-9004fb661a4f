import * as mongoose from "mongoose";
import { UnitedHealthCareMemberInformationModel } from "./sub-models/UnitedHealthCareAuthFormSubModels/united-health-care-member-information-model";
import { UnitedHealthCarePrescriberInformationModel } from "./sub-models/UnitedHealthCareAuthFormSubModels/united-health-care-prescriber-information-model";
import { UnitedHealthCareClinicalInformationModel } from "./sub-models/UnitedHealthCareAuthFormSubModels/united-health-care-clinical-information-model";
import { UnitedHealthCareMedicationInformationModel } from "./sub-models/UnitedHealthCareAuthFormSubModels/united-health-care-medication-information-model";
import { DAuthorizationForm, IAuthorizationForm } from "./authorization-form-model";


export interface Common {
    memberInformation: UnitedHealthCareMemberInformationModel,
    prescriberInformation: UnitedHealthCarePrescriberInformationModel,
    medicationInformation: UnitedHealthCareMedicationInformationModel;
    clinicalInformation: UnitedHealthCareClinicalInformationModel;
    additionalInformation?: string;
    date?: Date;
    signature?: string;  
}

export interface DUnitedHealthCareAuthForm extends Common, DAuthorizationForm { }

export interface IUnitedHealthCareAuthForm extends Common, IAuthorizationForm, mongoose.Document {
}