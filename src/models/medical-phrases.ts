import * as mongoose from "mongoose";
import { StringOrObjectId } from "../common/util";

export enum TabTypes {
  RELATIONAL = "relational/01",
  TRAUMA = "trauma/02",
  IDENTITY = "identity/03",
  PROCESS = "process/04",
  MOURNING_LOSS = "mourning_loss/05",
  ADJUSTMENT = "adjustment/06",
  DAILY_LIFE = "daily_life/07",
  SPECIFIC_DIFFICULTIES = "specific_difficulties/08",
  AFFECTIVE_STATE = "affective_state/09",
  MENTAL_STATE = "mental_state/10",
  GLOBAL_ASSESSMENT = "global_assessment/11",
  LEVEL_OF_FUNCTIONING = "level_of_functioning/12",
  SIGNIFICANT_DEVELOPMENTS = "significant_developments/13",
  OUTSTANDING_ISSUES = "outstanding_issues/14",
  SUPPORTIVE = "supportive/15",
  COGNITIVE_BEHAVIORAL = "cognitive_behavioral/16",
  PSYCHOANALYTIC = "psychoanalytic/17",
  OTHER_INTERVENTIONS = "other_interventions/18",
  ONGOING_PLAN = "ongoing_plan/19",
}

export enum Stages {
  SUBJECTIVE = "subjective",
  OBJECTIVE = "objective",
  ASSESSMENT = "assessment",
  PLAN = "plan",
}

interface Common {
  value: string;
  priority: string;
  tabType: string;
  stage:string;
  systemGenerated:boolean;
  createdBy?:mongoose.Types.ObjectId;
}

export interface DMedicalPhrase extends Common {}

export interface IMedicalPhrase extends Common, mongoose.Document {
  _id: StringOrObjectId;
}
