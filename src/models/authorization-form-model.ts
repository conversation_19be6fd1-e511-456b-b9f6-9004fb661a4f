import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { StringOrObjectId } from "../common/util";

export enum AuthFormType {
  NoAuth = "NoAuth",
  AmeriHealthAuthForm = "AmeriHealthAuthForm",
  WellcareAuthForm = "WellcareAuthForm",
  UnitedHealthCareAuthForm = "UnitedHealthCareAuthForm",
  HealthyBlueAuthForm = "HealthyBlueAuthForm",
  CarolinaCompleteHealthAuthForm = "CarolinaCompleteHealthAuthForm",
  AmbetterAuthForm = "AmbetterAuthForm"
}

interface CommonAttributes {
  therapistId: Types.ObjectId,
  clientId: Types.ObjectId,
  insuranceCompanyId: Types.ObjectId;
  authFormType?: string
}

export interface DAuthorizationForm extends CommonAttributes {
  _id?: StringOrObjectId;
}

export interface IAuthorizationForm extends CommonAttributes, mongoose.Document {
  [x: string]: any;

  readonly authFormType: AuthFormType;
}
