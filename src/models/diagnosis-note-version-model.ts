import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { ProcedureCodeModel } from "./sub-models/procedure-code-model";

interface Common {
  diagnosisNoteId: Types.ObjectId,
  clientId: Types.ObjectId;
  therapistId: Types.ObjectId;
  meetingId: Types.ObjectId;
  updated?: Boolean;
  updatedByTherapist?: Boolean;
  clinicalReminderEmail?: Boolean;
  clinicalReminderSMS?: Boolean;
  isVonageTranscribe?: Boolean;
  patientID?: string;
  patientAcountNo?: string;
  encounterID?: string;
  encounterDate?: Date;
  encounterType?: string;
  chiefComplaint?: string;
  historyOfPresentIllness?: string;
  historyOfPresentIllnessAttachments?: any[];
  diagnosisICDcodes?: any[];
  secondaryDiagnosisICDcodes?: any[];
  mentalBehavioralStatus?: string;
  mentalBehavioralStatusAttachments?: any[];
  asssessments?: string;
  cptCode?: string;
  assessmentAttachments?: any[];
  procedureNotes?: string;
  signature?: Buffer;
  carePlan?: string;
  carePlanAttachments?: any[];
  selectedGoals?: string[];
  intervention?: string;
  procedureCodes?: ProcedureCodeModel[];
  noteType?: string;
  reasonForEdit?: string;
  versionCreatedAt: Date;
}

export interface DDiagnosisNoteVersion extends Common {}

export interface IDiagnosisNoteVersion extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
