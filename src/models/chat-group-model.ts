import * as mongoose from "mongoose";
import { Types } from "mongoose";

export enum ChatGroupType {
  PUBLIC = "PUBLIC",
  PRIVATE = "PRIVATE",
}

interface Common {
  title: string;
  description: string;
  type: ChatGroupType;
  createdBy: Types.ObjectId;
  groupIcon?: mongoose.Types.ObjectId;
}

export interface DChatGroup extends Common {}

export interface IChatGroup extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}
