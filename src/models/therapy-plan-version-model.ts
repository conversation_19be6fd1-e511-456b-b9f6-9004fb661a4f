import { Types } from "mongoose";
import * as mongoose from "mongoose";
import { DiagnosisModel } from "./sub-models/TherapyPlanSubModels/diagnosis-model";
import { GoalInformationModel } from "./sub-models/TherapyPlanSubModels/goal-information-model";
import { TreatmentSessionModel } from "./sub-models/TherapyPlanSubModels/treatment-session-model";
import { SignatureDetailsModel } from "./sub-models/TherapyPlanSubModels/signature-details-model";

export interface Common {
  therapyPlanId: Types.ObjectId,
  therapistId: Types.ObjectId,
  clientId: Types.ObjectId,
  planCreationDate?: Date;
  creationDate?: Date;
  diagnosis?: DiagnosisModel;
  goalDate?: Date;
  goalInformation?: GoalInformationModel;
  treatmentSession?: TreatmentSessionModel;
  clientSignatureDetails?: SignatureDetailsModel;
  clientSignature?: Buffer;
  lrpSignatureDetails?: SignatureDetailsModel;
  lrpSignature?: Buffer;
  clinicianTwoSignatureDetails?: SignatureDetailsModel;
  clinicianSignatureDetails?: SignatureDetailsModel;
  clinicianTwoSignature?: Buffer;
  clinicianSignature?: Buffer;
  goalDate2?: Date;
  goalInformation2?: GoalInformationModel;
  treatmentSession2?: TreatmentSessionModel;
  goalDate3?: Date;
  goalInformation3?: GoalInformationModel;
  treatmentSession3?: TreatmentSessionModel;
  isSignature?: boolean;
  reasonForEdit?: string;
  versionCreatedBy: Types.ObjectId;
  versionCreatedAt: Date;
}
export interface DTherapyPlanVersion extends Common { }

export interface ITherapyPlanVersion extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
