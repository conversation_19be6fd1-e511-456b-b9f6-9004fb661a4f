import * as mongoose from "mongoose";
import { Copayment } from "./sub-models/copayment-model";
import { InsuranceClaim } from "./sub-models/insuranceClaim-model";
import { Types } from "mongoose";

export enum MeetingType {
  THIRTYMINUTUES = "30",
  SIXTYMINUTUES = "60",
}

export enum CallingStatus {
  INITIALIZED = "INITIALIZED",
  STARTED = "STARTED",
  ONGOING = "ONGOING",
  CANCELLED = "CANCELLED",
  COMPLETED = "COMPLETED",
}

interface Common {
  meetingId?: string;
  meetingDuration: number;
  spentDuration?: number;
  clientId?: mongoose.Types.ObjectId;
  therapistId: mongoose.Types.ObjectId;
  accepted: boolean;
  noOfVideose: number;
  videoUrls?: string[];
  speakerHistory?: string[];
  transcribeAllowed: boolean;
  transcribingInProcess: boolean;
  transcribeCreated: boolean;
  isAppointmentBased: boolean;
  transcribeId?: mongoose.Types.ObjectId;
  appointmentId?: mongoose.Types.ObjectId;
  audioFiles?: mongoose.Types.ObjectId[];
  recordingAllowed: boolean;
  // new fields
  createdBy: mongoose.Types.ObjectId;
  callingStatus: string;
  recordingSharedWithClient: boolean;
  // new fields
  transcriptTextInProcess?: boolean;
  transcriptTextCreated?: boolean;
  isInvalidMeetingTime?: boolean;
  invalidDuration?: number;
  clientIdentifier: string;
  therapistIdentifier: string;
  password: string;
  bothJoinedAt?: Date;
  regularMeetingStartTime?: string;
  regularMeetingDate?: Date;
  isAudioCall?: boolean;
  sessionAmount?: number;
  copayment?: Copayment;
  insuranceClaim?: InsuranceClaim;
  hasTranscribes?: boolean;
  therapistAllowedTranscribe?: boolean;
  clientAllowedTranscribe?: boolean;
  clientDetails?: clientDetails;
  firstSpeaker?: mongoose.Types.ObjectId;
  vonageSessionName?: string;
  vonageArchiveId?: string;
  isGroupCall?: boolean;
  chatGroupCallId?: mongoose.Types.ObjectId;
  participantCount?: number;
  s3AudioPath?: string;
  isVonageNativeSDKCall?: boolean;
}

interface clientDetails {
  _id: Types.ObjectId,
  email: string,
  firstname: string,
  lastname: string,
  primaryPhone: string,
  lavniTestAccount: boolean
}

export interface DMeeting extends Common { }

export interface IMeeting extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
  meetingType: MeetingType;
  createdAt?: Date;
}