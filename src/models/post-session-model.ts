import * as mongoose from "mongoose";
import { Types } from "mongoose";

interface Common {
  serviceProvided?: string;
  suicidalIdeation?: string;
  homicidalIdeation?: string;
  perceptualDisturbance?: string;
  nextSessionScheduled?: string;
  clinicalDirectorReviewRequired?: string;
  comments?: string;
  diagnosisCodes?: string[];
  meetingId: string;
  therapistId: Types.ObjectId;
  clientId?: Types.ObjectId;
}

export interface DPostSession extends Common { }

export interface IPostSession extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
