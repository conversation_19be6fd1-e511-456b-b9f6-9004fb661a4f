import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { ERACHargeList } from "./sub-models/claim-era-model";


interface Common {
  pcn: string;
  pat_name_f: string;
  pat_name_l: string;
  from_dos: string;
  claim_received_date: string;
  total_paid: string;
  paid_amount: string;
  status_code: string;
  ins_number: string;
  total_charge: string;
  charge?: ERACHargeList[];
  status?: string;
  eraClaimId?: mongoose.Types.ObjectId;
}

export interface DERAClaimMdListSchema extends Common { }

export interface IERAClaimMdListSchema extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
