import * as mongoose from "mongoose";

interface Common {
  yearlyRecurringRevenueCount: string;
  monthlyRecurringRevenueCount: string;
  lifeTimeSales: string;
  averageCustomerLifetimeValue: string;
}

export interface DAdminStatistics extends Common {}

export interface IAdminStatistics extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
}

export interface IAdminStatisticsHourly extends Common , mongoose.Document {
    _id: mongoose.Types.ObjectId;
    pastMissedAppointmentsCount: number;
    scheduledAppointmentsCount: number;
    completedSessions: number;
}