import * as mongoose from "mongoose";
import { Types } from "mongoose";


interface Common {
  prov_taxid?: string;
  payer_name?: string;
  received_time?: string;
  employment_related?: string;
  check_number?: string;
  eraid?: number;
  paid_date?: string;
  paid_amount?: string;
  payerid?: string;
  prov_name?: string;
  claimmd_prov_name?: string;
  prov_npi?: string;
  status?: string;
}

export interface DERAClaimMdSchema extends Common { }

export interface IERAClaimMdSchema extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
