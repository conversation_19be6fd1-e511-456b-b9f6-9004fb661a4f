import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { Payment } from "./sub-models/payment-model";
import { Review } from "./sub-models/review-model";
import { WorkingHour } from "./sub-models/working-hours-model";
import { DUser, IUser } from "./user-model";
import { BlockDate } from "./sub-models/block-date-model";

export enum RegistrationApprovalStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

export enum AINotesType {
  SOAP = "SOAP",
  PIE = "PIE",
}

interface Common {
  licenseId?: Types.ObjectId[];
  qualifications?: Types.ObjectId[];
  profession?: Types.ObjectId;
  professionLicense?: Types.ObjectId;
  dislikedClients?: Types.ObjectId[];
  experiencedIn?: Types.ObjectId[];
  loginVerification?: string;
  workingHours?: WorkingHour[];
  insuranceCompanies?: Types.ObjectId[];
  isAvailable?: boolean;
  yearsOfExperience?: number;
  reviews?: Review[];
  payment?: Payment[];
  vimeoId?: string;
  socialSecurity?: string;
  cAQH?: string;
  nPI1?: string;
  taxIdentification?: string;
  license?: string;
  issueDate?: string;
  expirationDate?: string;
  schoolName?: string;
  dateOfGraduation?: string;
  schoolStreetAddress?: string;
  schoolCity?: string;
  schoolState?: string;
  schoolZipCode?: string;
  taxonomyCode?: string;
  malpracticePolicy?: string;
  malpracticeExpirationDate?: string;
  disclosureStatementId?: Types.ObjectId;
  stripeConnectedAccountId?: string;
  stripeConnectedLoginLink?: string;
  stripeChargesEnabled?: boolean;
  stripeDetailsSubmitted?: boolean;
  payRate?: number;
  timeZone?: string;
  aiGenerateCount?: number;
  aiReviewSubmitted?: boolean;
  caqhUserName?: string;
  caqhPassword?: string;
  recentTransaction?: any;
  medicaidUsername?: string;
  MedicaidPassword?: string;
  psychologyTodayUsername?: string;
  psychologyTodayPassword?: string;
  therapyState?: string[];
  blockedDates?: BlockDate[];
  claimOpen?: boolean;
  signature?: Buffer;
  firstSessionBonus?: Types.ObjectId;
  twentySessionsBonus?: Types.ObjectId;
  grantedAccessFileFolderPermission?: boolean;
  registrationApprovalStatus?: RegistrationApprovalStatus;
  aiNotesType?: AINotesType;
  medicaidId?: string;
  isNewDashboard?: boolean;
  therapistCategorizationByType?: TherapistCategorizationByType;
}

export interface DTherapist extends Common, DUser { }

export interface ITherapist extends Common, IUser, mongoose.Document {
}

export enum Timezones {
  HAWAII_STANDARD_TIME = "Hawaii Standard Time",
  HAWAII_ALEUTIAN_DAYLIGHT_TIME = "Hawaii-Aleutian Daylight Time",
  ALASKA_DAYLIGHT_TIME = "Alaska Daylight Time",
  PACIFIC_DAYLIGHT_TIME = "Pacific Daylight Time",
  MOUNTAIN_STANDARD_TIME = "Mountain Standard Time",
  MOUNTAIN_DAYLIGHT_TIME = "Mountain Daylight Time",
  CENTRAL_DAYLIGHT_TIME = "Central Daylight Time",
  EASTERN_DAYLIGHT_TIME = "Eastern Daylight Time",
}

export enum TherapistCategorizationByType {
  NONE="None",
  ASSOCIATE = "Associate",
  LICENSED = "Licensed",
}