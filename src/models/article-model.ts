import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { Comment } from "./sub-models/comment-model";
//import { DComment } from "./sub-models/comment-model";

interface Common {
  articleTitle: string;
  articleBody: string;
  articleTags: Types.ObjectId[];
  createdBy?: Types.ObjectId;
  editedBy?: any[];
  uploadId?: Types.ObjectId;
  videoThumbnail?: Types.ObjectId;
  likedBy?: string[];
  comments?: Comment[];
  hashTags: string[];
  videoUrl?: string;
  vimeoId?: string;
}

export interface DArticle extends Common {}

export interface IArticle extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
