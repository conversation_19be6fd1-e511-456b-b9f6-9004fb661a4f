import * as mongoose from "mongoose";
import { Types } from "mongoose";

export enum SubmissionApprovalStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

interface Common {
  clientId: string;
  therapistId: string;
  insuranceCompanyId?: Types.ObjectId | any;
  therapistApprovalStatus: SubmissionApprovalStatus;
  adminApprovalStatus?: SubmissionApprovalStatus;
  clinicalAssessmentUploadId?: Types.ObjectId | any;
  therapyPlanUploadId?: Types.ObjectId | any;
  authorizationFormUploadId?: Types.ObjectId | any;
  messageId?: string;
  messageStatus?: string;
  recipientId?: string;
  toPhoneNumber?: string;
  toMessageStatus?: string;
  fromPhoneNumber?: string;
  readStatus?: string;
  faxPageCount?: number;
  creationTime?: Date;
  lastModifiedTime?: Date;
}

export interface DInsuranceDocApproval extends Common {}

export interface IInsuranceDocApproval extends Common, mongoose.Document {
  _id: Types.ObjectId;
}
