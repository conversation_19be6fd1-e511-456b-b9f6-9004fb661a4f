import * as mongoose from "mongoose";

interface Common {
  responseTimeWeight?: number;
  availabilityWeight?: number;
  followUpAppointmentsWeight?: number;
  totalSessionsWeight?: number;
  missedAppointmentsWeight?: number;
  loyalityYearsWeight?: number;
  scheduledAppointmentsWeight?: number;
  noOfMatchesWeight?: number;
}

export interface DTherapistScoreConstants extends Common {}

export interface ITherapistScoreConstants extends Common, mongoose.Document {
  _id: mongoose.Types.ObjectId;
  createdAt?: Date;
  updatedAt?: Date;
}
