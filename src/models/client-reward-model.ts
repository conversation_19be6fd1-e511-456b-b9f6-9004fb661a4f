import * as mongoose from "mongoose";
import { Types } from "mongoose";
import { TransactionType } from "./transaction-model";

interface Common {
  clientId: string;
  type: string;
  transactionAmount: number;
  accumulatedBalance: number;
  accumulatedTotalEarnings: number;
  accumulatedWithdrawals: number;
  paidStatus?: string;
  rewardType?: string;
  rewardReferralId?: Types.ObjectId | any;
}

export interface DClientReward extends Common {}

export interface IClientReward extends Common, mongoose.Document {
  _id: Types.ObjectId;
  type: TransactionType;
  createdAt?: Date;
  updatedAt?: Date;
}
