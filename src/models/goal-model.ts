import * as mongoose from "mongoose";
import { DueDate } from "./sub-models/due-date-model";

interface Common {
    title: string;
    description: string;
    prograss: number;
    dueDate: string;
    completedDates?: DueDate[];
    isComplete: boolean;
    createdBy: mongoose.Types.ObjectId;
    assignedFor?: mongoose.Types.ObjectId;
}

export interface DGoal extends Common {}

export interface IGoal extends Common, mongoose.Document {
    _id: mongoose.Types.ObjectId;
  }