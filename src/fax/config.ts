import { App<PERSON>ogger } from "../common/logging";
import { UploadCategory } from "../end-point/user-ep";

const RC = require('@ringcentral/sdk').SDK;
const FormData = require('form-data');
const fs = require('fs');
require("dotenv").config();

export namespace FaxService {
  const rcsdk = new RC({
    server: process.env.RC_SERVER_URL,
    clientId: process.env.RC_APP_CLIENT_ID,
    clientSecret: process.env.RC_APP_CLIENT_SECRET
  });

  const platform = rcsdk.platform();

  async function login_jwt(): Promise<boolean> {
    try {
      await platform.login({ jwt: process.env.RC_USER_JWT });
      // AppLogger.info('Fax Service | Successfully logged in.');
      return true;
    } catch (e) {
      AppLogger.error(`Fax Service | Unable to login using JWT. E-details: ${e.message}`);
      return false;
    }
  }

  async function tokensValid(): Promise<boolean> {
    const isLoggedIn = await platform.loggedIn();
    if (!isLoggedIn) {
      // AppLogger.warn('Fax Service | Tokens expired. Re-authenticating...');
      return await login_jwt();
    }
    return true;
  }

  // async function platform_logout(): Promise<void> {
  //   try {
  //     if (platform.loggedIn()) {
  //       await platform.logout();
  //       AppLogger.info('Fax Service | Successfully logged out from platform.');
  //     }
  //   } catch (e) {
  //     AppLogger.error(`Fax Service | Error during logout. E-details: ${e.message}`);
  //   }
  // }

  export async function sendFax(clinicalAssessmentFileName: string, therapyPlanFileName: string, authorizationFormFileName: string, faxNumber: string) {
    try {
      if (!(await tokensValid())) {
        throw new Error('Unable to authenticate to RingCentral platform.');
      }

      const filePaths = {
        clinicalAssessment: `${process.env.UPLOAD_PATH}/${UploadCategory.CLINICAL_ASSESSMENT}/${clinicalAssessmentFileName}`,
        therapyPlan: `${process.env.UPLOAD_PATH}/${UploadCategory.THERAPY_PLAN}/${therapyPlanFileName}`,
        authorizationForm: `${process.env.UPLOAD_PATH}/${UploadCategory.AUTHORIZATION_FORM}/${authorizationFormFileName}`
      };

      const formData = new FormData();
      const bodyParams = {
        to: [{ phoneNumber: faxNumber }],
        faxResolution: 'Low',
        coverIndex: '7',
        coverPageText: ''
      };

      formData.append('json', Buffer.from(JSON.stringify(bodyParams)), {
        filename: 'request.json',
        contentType: 'application/json'
      });

      for (const [key, filePath] of Object.entries(filePaths)) {
        if (fs.existsSync(filePath)) {
          formData.append(key, fs.createReadStream(filePath));
        } else {
          AppLogger.error(`Fax Service | File not found: ${filePath}`);
          return false;
        }
      }

      const endpoint = '/restapi/v1.0/account/~/extension/~/fax';
      const resp = await platform.post(endpoint, formData);
      const jsonObj = await resp.json();
      AppLogger.info(`Fax Service | FAX sent. Message id: ${jsonObj.id}`);
      return jsonObj;
    } catch (e) {
      AppLogger.error(`Fax Service | Error sending fax. E-details: ${e.message}`);
      return null;
    }
  }

  export async function getUpdatedFaxStatus(messageId: string) {
    try {
      if (!(await tokensValid())) {
        throw new Error('Unable to authenticate to RingCentral platform.');
      }

      const endpoint = `/restapi/v1.0/account/~/extension/~/message-store/${messageId}`;
      const resp = await platform.get(endpoint);
      const jsonObj = await resp.json();
      return jsonObj;
    } catch (e) {
      AppLogger.error(`Fax Service | Error checking fax status. Message id: ${messageId}. E-details: ${e.message}`);
      return null;
    }
  }

  export async function resendFax(messageId: string) {
    try {
      if (!(await tokensValid())) {
        throw new Error('Unable to authenticate to RingCentral platform.');
      }
  
      const requestBody = {
        originalMessageId: messageId
      };
  
      const endpoint = `/restapi/v1.0/account/~/extension/~/fax`;
      const resp = await platform.post(endpoint, requestBody);
      const jsonObj = await resp.json();
      return jsonObj;
    } catch (e) {
      return null;
    }
  }
}