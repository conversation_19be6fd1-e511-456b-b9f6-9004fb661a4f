import { Express } from "express";
import { VonageCallGroupEp } from "../end-point/vonage-call-group-ep";
import { Authentication } from "../middleware/authentication";
const cron = require('node-cron');

export function initVonageCallGroupRoutes(app: Express) {

    app.post(
        "/api/auth/initialize/vonage/group/call",
        Authentication.therapistAndClientVerification,
        VonageCallGroupEp.initializeVonageVideoCallGroupValidation(),
        VonageCallGroupEp.initializeVonageVideoCallGroup
    );

    app.post(
        "/api/auth/start/vonage/group/call",
        Authentication.therapistAndClientVerification,
        VonageCallGroupEp.startCallGroupValidation(),
        VonageCallGroupEp.startVonageVideoCallGroup
    );

    app.get(
        "/api/auth/getMeetingDetailsByGroupCallId/:groupCallId",
        Authentication.therapistAndClientVerification,
        VonageCallGroupEp.getMeetingDetailsByGroupCallId
    );
    
    app.post(
        "/api/auth/join/vonage/group/call",
        Authentication.therapistAndClientVerification,
        VonageCallGroupEp.joinVonageMeetingGroup
    );

    app.post(
        "/api/auth/accept/vonage/group/call",
        Authentication.therapistAndClientVerification,
        VonageCallGroupEp.acceptVonageGroupCallValidation(),
        VonageCallGroupEp.acceptVonageGroupCall
    );

    app.post(
        "/api/auth/cancel/vonage/group/call",
        Authentication.therapistAndClientVerification,
        VonageCallGroupEp.cancelVonageCallGroup
    );

    app.get(
        "/api/auth/checkOngoingGroupCall",
        Authentication.clientVerification,
        VonageCallGroupEp.checkOngoingGroupCallForClient
    );
}