import { Express } from "express";
import { FriendRequestEp } from "../end-point/friend-request-ep";
import { Authentication } from "../middleware/authentication";
export function initRequestRoutes(app: Express) {
    app.post("/api/auth/createRequestByClient/:therapistId", Authentication.clientVerification, FriendRequestEp.createRequestByClient);
    
    app.post("/api/public/createRequestByClient/:therapistId/:clientId", FriendRequestEp.createRequestByClientPublic);

    app.post("/api/auth/updateRequestByTherapist", Authentication.therapistVerification, FriendRequestEp.updateRequestByTherapist);

    app.post("/api/auth/viewAllRequestsByTherapist/:limit/:offset", Authentication.therapistVerification, FriendRequestEp.viewAllRequestsByTherapist);

    app.get("/api/auth/viewAllRequestsByClient/:limit/:offset", Authentication.clientVerification, FriendRequestEp.viewAllRequestsByClient);

    app.get("/api/auth/checkIfUserIsFriend/:userId", Authentication.therapistAndClientVerification, FriendRequestEp.checkIfUserIsFriend);

    app.get("/api/public/checkIfUserIsFriend/:userId/:clientId",  FriendRequestEp.checkIfUserIsFriendPublic);

    app.get("/api/auth/viewAllSentRequestsByClient", Authentication.clientVerification, FriendRequestEp.viewAllSentRequestsByClient);

    app.post("/api/auth/unfriendUser/:requestId", Authentication.therapistAndClientVerification, FriendRequestEp.unfriendUser);

    app.get("/api/auth/viewAllRequestsForShare/:limit/:offset", Authentication.therapistAndAdminVerification, FriendRequestEp.viewAllRequestsForShare);

    app.post("/api/auth/unmatchMatchedClientOrTherapist/:therapistId/:clientId", Authentication.superAdminVerification, FriendRequestEp.unmatchMatchedClientOrTherapistByAdmin);

}
