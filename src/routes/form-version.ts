import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { FormVersionEp } from "../end-point/form-version-ep";
import { VideoChatEP } from "../end-point/video-chat-ep";

export function initFormVersionRoutes(app: Express) {
  app.get(
    "/api/auth/getCurrentTherapyPlanById/:id",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.getCurrentTherapyPlanById
  );
  app.post(
    "/api/auth/createTherapyPlanVersion",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.createTherapyPlanVersion
  );
  app.get(
    "/api/auth/getTherapyPlansWithAllVersions/:clientId",
    Authentication.therapistVerification,
    FormVersionEp.getTherapyPlansWithAllVersions
  );
  app.get(
    "/api/auth/getTherapyPlanVersion/:id",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.getTherapyPlanVersion
  );
  app.get(
    "/api/auth/getDiagnosisNoteVersionById/:noteId",
    Authentication.therapistAndAdminVerification,
    VideoChatEP.getDiagnosisNoteByIdParamValidation(),
    FormVersionEp.getDiagnosisNoteVersionById
  );
  app.get(
    "/api/auth/getCurrentClinicalAssessmentById/:id",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.getCurrentClinicalAssessmentById
  );
  app.post(
    "/api/auth/createClinicalAssessmentVersion",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.createClinicalAssessmentVersion
  );
  app.get(
    "/api/auth/getClinicalAssessmentsWithAllVersions/:clientId",
    Authentication.therapistVerification,
    FormVersionEp.getClinicalAssessmentsWithAllVersions
  );
  app.get(
    "/api/auth/getClinicalAssessmentVersion/:id",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.getClinicalAssessmentVersion
  );
  app.get(
    "/api/auth/getCurrentNewTypeOfClinicalAssessment/:id",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.getCurrentNewTypeOfClinicalAssessment
  );
  app.post(
    "/api/auth/createNewTypeOfClinicalAssessmentVersion",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.createNewTypeOfClinicalAssessmentVersion
  );
  app.post(
    "/api/auth/getNewTypeOfClinicalAssessmentDetailsForDownload",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.getNewTypeOfClinicalAssessmentDetailsForDownload
  );
  app.get(
    "/api/auth/getNewTypeOfClinicalAssessmentVersionForDownload/:id",
    Authentication.therapistAndAdminVerification,
    FormVersionEp.getNewTypeOfClinicalAssessmentVersionForDownload
  );
}