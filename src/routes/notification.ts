import { Express } from "express";
import { NotificationEp } from "../end-point/notification-ep";
import { Authentication } from "../middleware/authentication";

export function initNotificationRoutes(app: Express) {
  app.get(
    "/api/auth/getNotifications/:limit?/:offset?",
    Authentication.allUserVerification,
    NotificationEp.getNotifications
  );

  app.post(
    "/api/auth/markAllNotificationsRead",
    Authentication.allUserVerification,
    NotificationEp.markAllNotificationsAsRead
  );

  app.post(
    "/api/auth/createNotification",
    Authentication.allUserVerification,
    NotificationEp.notificationValidationRules(),
    NotificationEp.createNotification
  );

  app.delete(
    "/api/auth/deleteNotification/:notificationId",
    Authentication.allUserVerification,
    NotificationEp.deleteNotification
  );

  app.post(
    "/api/auth/markClientAllTwilioMessagesAsRead",
    Authentication.superAdminVerification,
    NotificationEp.markClientAllTwilioSmsMessageNotificationsAsRead
  );
}
