import { Express } from "express";
import { ZoomVideoCallEP } from "../end-point/zoom-video-call-ep";
import { Authentication } from "../middleware/authentication";

export function initZoomVideoCallRoutes(app: Express) {
  // app.post("/api/public/get/sdk/token", ZoomVideoCallEP.startZoomVideoCallTest);
  // app.get("/api/public/get/api/token", ZoomVideoCallEP.getApiJWT);
  // ////
  app.post(
    "/api/auth/initialize/zoom/call",
    Authentication.therapistAndClientVerification,
    ZoomVideoCallEP.initializeZoomVideoCallValidation(),
    ZoomVideoCallEP.initializeZoomVideoCall
  );
  app.post(
    "/api/auth/start/zoom/call",
    Authentication.therapistAndClientVerification,
    ZoomVideoCallEP.startCallValidation(),
    ZoomVideoCallEP.startZoomVideoCall
  );
  app.post(
    "/api/auth/join/zoom/call",
    Authentication.therapistAndClientVerification,
    ZoomVideoCallEP.joinZoomMeetingValidation(),
    ZoomVideoCallEP.joinZoomMeeting
  );
  app.post(
    "/api/auth/cancel/zoom/call",
    Authentication.therapistAndClientVerification,
    ZoomVideoCallEP.cancelZoomCallValidation(),
    ZoomVideoCallEP.cancelZoomCall
  );
  app.post(
    "/api/auth/accept/zoom/call",
    Authentication.therapistAndClientVerification,
    ZoomVideoCallEP.acceptZoomCallValidation(),
    ZoomVideoCallEP.acceptZoomCall
  );

  app.post(
    "/api/auth/zoom/checkRemainingTime",
    Authentication.therapistAndClientVerification,
    ZoomVideoCallEP.checkRemainingMeetingTimeValidation(),
    ZoomVideoCallEP.checkRemainingMeetingTime
  );

  app.post(
    "/api/auth/zoom/updateMeetingTimeWhenExtend",
    Authentication.therapistVerification,
    ZoomVideoCallEP.updateMeetingTimeWhenExtendValidation(),
    ZoomVideoCallEP.updateMeetingTimeWhenExtend
  );

  app.post(
    "/api/auth/finalise/zoom/meeting",
    Authentication.therapistVerification,
    ZoomVideoCallEP.finaliseZoomMeetingParamValidation(),
    ZoomVideoCallEP.finaliseZoomMeetingNew
  );

  app.post(
    "/api/auth/finalise/zoom/meetingWithNoRecordings",
    Authentication.therapistVerification,
    ZoomVideoCallEP.finaliseZoomMeetingParamValidation(),
    ZoomVideoCallEP.finaliseZoomMeetingWithoutSavingRecordings
  );

  // app.post(
  //   "/api/auth/createTranscribeTextManuallyWithZoom",
  //   Authentication.therapistAndClientVerification,
  //   ZoomVideoCallEP.createTranscribeTextManuallyWithZoomParamValidation(),
  //   ZoomVideoCallEP.createTranscribeTextManuallyWithZoom
  // );

  app.post(
    "/api/auth/bothJoinedTime",
    Authentication.therapistAndClientVerification,
    ZoomVideoCallEP.meetingBothJoinedTime
  );

  app.post(
    "/api/auth/createTranscribeNew",
    Authentication.therapistAndClientVerification,
    ZoomVideoCallEP.createTranscribeTextManuallyWithZoomParamValidation(),
    ZoomVideoCallEP.createTranscribeNew
  );
}
