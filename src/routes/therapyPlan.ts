import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { TherapyPlanEp } from "../end-point/therapy-plan-ep";


export function initTherapyPlanRoutes(app: Express) {
  app.post(
    "/api/auth/addTherapyPlanDetails",
    Authentication.therapistVerification,
    TherapyPlanEp.addTherapyPlanDetails
  );

  app.post(
    "/api/auth/updateTherapyPlanDetails",
    Authentication.therapistAndAdminVerification,
    TherapyPlanEp.updateTherapyPlanDetails
  );

  app.post(
    "/api/auth/getTherapyPlanDetails",
    Authentication.therapistAndAdminVerification,
    TherapyPlanEp.getTherapyPlanDetails
  );

  app.post(
    "/api/public/update-therapy-plan-signature",
    TherapyPlanEp.updateClientTherapyPlanSignature
  );

  app.post(
    "/api/auth/getClientNameAndTherapistName",
    Authentication.therapistVerification,
    TherapyPlanEp.getClientNameAndTherapistName
  );

    app.post(
      "/api/auth/getTherapistCategorizationByType",
      Authentication.therapistAndAdminVerification,
      TherapyPlanEp.getTherapistCategorizationByType
    )
  app.post(
      "/api/auth/updateTherapistCategorizationByType",
      Authentication.therapistAndAdminVerification,
      TherapyPlanEp.updateTherapistCategorizationByType
  );
}