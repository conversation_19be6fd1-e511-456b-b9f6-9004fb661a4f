import { Express } from "express";
import { ArticleEp } from "../end-point/article-ep";
import { Authentication } from "../middleware/authentication";

export function initArticleRoutes(app: Express) {
  app.post(
    "/api/auth/addArticle",
    Authentication.therapistVerification,
    ArticleEp.addArticle
  );

  app.post(
    "/api/auth/updateArticle/:id",
    Authentication.therapistVerification,
    ArticleEp.updateArticle
  );

  app.post(
    "/api/auth/updateArticleWithoutFile/:id",
    Authentication.therapistVerification,
    ArticleEp.updateArticleWithoutFile
  );

  app.delete(
    "/api/auth/deleteArticle/:articleId",
    Authentication.therapistVerification,
    ArticleEp.deleteArticle
  );

  app.get(
    "/api/auth/getAllArticles/:limit/:offset",
    Authentication.allUserVerification,
    ArticleEp.getAllArticles
  );

  // getting public articles

  app.get(
    "/api/public/getAllPublicArticles/:limit/:offset",
    ArticleEp.getAllPublicArticles
  );

  app.get("/api/auth/getArticleById/:id", ArticleEp.getArticleById);

  app.get("/api/public/getArticleById/:id", ArticleEp.getArticleByIdPublic);

  app.get(
    "/api/public/getAllArticlesPublic/:limit/:offset",
    ArticleEp.getAllArticlePublic
  );

  app.post(
    "/api/auth/likePost/:id",
    Authentication.therapistAndClientVerification,
    ArticleEp.toggleLike
  );

  app.post(
    "/api/auth/addComment/:id",
    Authentication.therapistAndClientVerification,
    ArticleEp.addComment
  );

  app.post(
    "/api/auth/searchArticlesByTags/:limit/:offset",
    Authentication.therapistVerification,
    ArticleEp.searchArticlesByTagsValidationRules(),
    ArticleEp.searchArticlesByTags
  );

  app.post(
    "/api/auth/searchArticlesByTagsAndHashTags/:limit/:offset",
    Authentication.clientVerification,
    ArticleEp.searchArticlesByTagsAndHashTagsValidationRules(),
    ArticleEp.searchArticlesByTagsAndHashTags
  );
  app.post(
    "/api/public/searchPublicArticlesByTagsAndHashTags/:limit/:offset",
    ArticleEp.searchArticlesByTagsAndHashTagsValidationRules(),
    ArticleEp.searchPublicArticlesByTagsAndHashTags
  );

  app.post(
    "/api/auth/addReply",
    Authentication.therapistAndClientVerification,
    ArticleEp.addReplyValidationRules(),
    ArticleEp.addReply
  );

  app.get(
    "/api/auth/getPopularPosts",
    Authentication.therapistAndClientVerification,
    ArticleEp.getPopularPosts
  );

  app.get(
    "/api/public/getPublicPopularPosts",
    ArticleEp.getPopularPublicPosts
  );

  app.post(
    "/api/auth/updateCommentReply/:id",
    Authentication.therapistAndClientVerification,
    ArticleEp.updateCommentReply
  );
}
