import { Express } from "express";
import { VideoChatEP } from "../end-point/video-chat-ep";
import { Authentication } from "../middleware/authentication";

export function initVideoChatRoutes(app: Express) {
  // app.get(
  //   "/api/auth/get/video/sdk/token",
  //   Authentication.therapistAndClientVerification,
  //   VideoChatEP.getVideoSDKToken
  // );

  app.post(
    "/api/auth/save/avatar/by/url",
    Authentication.therapistAndClientVerification,
    VideoChatEP.saveAvatarUsingUrlParamValidation(),
    VideoChatEP.saveAvatarUsingUrl
  );

  app.post(
    "/api/auth/save/default/avatar",
    Authentication.therapistAndClientVerification,
    VideoChatEP.saveDefaultAvatarParamValidation(),
    VideoChatEP.saveDefaultAvatar
  );

  app.post(
    "/api/auth/change/avatar/background",
    Authentication.therapistAndClientVerification,
    VideoChatEP.changeAvatarBackgroundParamValidation(),
    VideoChatEP.changeAvatarBackground
  );

  app.get(
    "/api/auth/get/avatar/details",
    Authentication.therapistAndClientVerification,
    VideoChatEP.getAvatarDetails
  );

  app.post(
    "/api/auth/update/speaker/history",
    Authentication.therapistVerification,
    VideoChatEP.updateSpeakerHistoryParamValidation(),
    VideoChatEP.updateSpeakerHistory
  );

  app.get(
    "/api/auth/get/transcribes/by/client/id",
    Authentication.therapistVerification,
    VideoChatEP.getTranscribeForSpecificClientByTherapistParamValidation(),
    VideoChatEP.getTranscribeForSpecificClientByTherapist
  );

  app.get(
    "/api/auth/get/transcribe/by/id/:transcribeId",
    Authentication.therapistAndClientVerification,
    VideoChatEP.getTranscribeByIdParamValidation(),
    VideoChatEP.getTranscribeById
  );

  app.get(
    "/api/auth/getMonthlyMeetings",
    Authentication.therapistVerification,
    VideoChatEP.getAllMeetingsForSpecificTherapistForCurrentMonth
  );

  app.get(
    "/api/auth/getAllMeetings",
    Authentication.therapistVerification,
    VideoChatEP.getAllMeetingsForSpecificTherapist
  );

  app.get(
    "/api/auth/getAllMeetingsForClientAndTherapistCurrentMonth",
    Authentication.therapistVerification,
    VideoChatEP.getAllMeetingsForThrepistAndClientCurrentMonthParamValidation(),
    VideoChatEP.getAllMeetingsForThrepistAndClientCurrentMonth
  );

  app.get(
    "/api/auth/getAllMeetingsForTherapistForTranscribe/:limit?/:offset?",
    Authentication.therapistVerification,
    VideoChatEP.getAllMeetingsForSpecificTherapistForTranscribe
  );

  app.get(
    "/api/auth/getDiagnosisNoteById/:noteId",
    Authentication.therapistAndAdminVerification,
    VideoChatEP.getDiagnosisNoteByIdParamValidation(),
    VideoChatEP.getDiagnosisNoteById
  );

  app.post(
    "/api/auth/downloadDiagnosisNoteById/:noteId",
    Authentication.therapistAndAdminVerification,
    VideoChatEP.downloadDiagnosisNoteById
  );

  app.post(
    "/api/auth/updateDiagnosisNote",
    Authentication.therapistAndAdminVerification,
    VideoChatEP.updateDiagnosisNoteWithNoteType
  );

  app.post(
    "/api/auth/deleteTreatmentHistory",
    Authentication.therapistAndAdminVerification,
    VideoChatEP.deleteTreatmentHistoryByTherapist
  );

  app.get(
    "/api/auth/getDiagnosisNoteByClientId/:clientId",
    Authentication.superAdminVerification,
    VideoChatEP.getDiagnosisNotesForClientParamValidation(),
    VideoChatEP.getDiagnosisNotesForClient
  );

  app.get(
    "/api/auth/checkForNearestUpcomingAppointment",
    Authentication.therapistAndClientVerification,
    VideoChatEP.checkForNearestUpcomingAppointment
  );

  // new routes for video calling
  // app.post(
  //   "/api/auth/startCall",
  //   Authentication.therapistAndClientVerification,
  //   VideoChatEP.startCallValidation(),
  //   VideoChatEP.startCall
  // );

  // app.post(
  //   "/api/auth/checkRemainingTime",
  //   Authentication.therapistAndClientVerification,
  //   VideoChatEP.checkRemainingMeetingTimeValidation(),
  //   VideoChatEP.checkRemainingMeetingTime
  // );

  app.post(
    "/api/auth/getRecieverSocketId",
    Authentication.therapistAndClientVerification,
    VideoChatEP.getRecieverSocketIdValidation(),
    VideoChatEP.getRecieverSocketId
  );

  // app.post(
  //   "/api/auth/acceptCall",
  //   Authentication.therapistAndClientVerification,
  //   VideoChatEP.acceptCallValidation(),
  //   VideoChatEP.acceptCall
  // );

  app.post(
    "/api/auth/deleteMeeting",
    Authentication.therapistAndClientVerification,
    VideoChatEP.deleteMeetingValidation(),
    VideoChatEP.deleteMeeting
  );

  app.post(
    "/api/auth/createVideoCallNotification",
    Authentication.therapistAndClientVerification,
    VideoChatEP.createVideoCallNotificationValidation(),
    VideoChatEP.createVideoCallNotification
  );

  app.get(
    "/api/auth/getAvatarDetailsOfOwnUser",
    Authentication.therapistAndClientVerification,
    VideoChatEP.getAvatarDetailsOfOwnUser
  );

  app.get(
    "/api/auth/getNotesForTherapists/:therapistId",
    VideoChatEP.getDiagnosisNotesForTherapists
  );

  app.put(
    "/api/auth/diagnosisNote/:diagnosisNoteId",
    Authentication.superAdminVerification,
    VideoChatEP.updateDiagnosisNoteValidation(),
    VideoChatEP.updateDiagnosisNote
  );

  // app.post(
  //   "/api/auth/updateMeetingTimeWhenExtend",
  //   Authentication.therapistVerification,
  //   VideoChatEP.updateMeetingTimeWhenExtendValidation(),
  //   VideoChatEP.updateMeetingTimeWhenExtend
  // );

  // app.post(
  //   "/api/auth/deleteRecordingsInVideosdkTest",
  //   Authentication.superAdminVerification,
  //   VideoChatEP.deleteRecordingsInVideosdkTest
  // );

  // with meeting link
  // app.post(
  //   "/api/auth/startCallWithMeetingLink",
  //   Authentication.therapistAndClientVerification,
  //   VideoChatEP.startCallValidation(),
  //   VideoChatEP.startCallWithMeetingLink
  // );

  app.get(
    "/api/auth/checkForNearestUpcomingAppointmentAndIncompletedCalls",
    Authentication.therapistAndClientVerification,
    VideoChatEP.checkForNearestUpcomingAppointmentAndIncompletedCalls
  );

  // app.post(
  //   "/api/auth/joinMeeting",
  //   Authentication.therapistAndClientVerification,
  //   VideoChatEP.joinMeetingValidation(),
  //   VideoChatEP.joinMeeting
  // );

  // app.post(
  //   "/api/auth/cancelMeeting",
  //   Authentication.therapistAndClientVerification,
  //   VideoChatEP.cancelCallValidation(),
  //   VideoChatEP.cancelCall
  // );

  // app.post(
  //   "/api/auth/acceptCallWithMeetingLink",
  //   Authentication.therapistAndClientVerification,
  //   VideoChatEP.acceptCallWithMeetingLinkValidation(),
  //   VideoChatEP.acceptCallWithMeetingLink
  // );
  app.post(
    "/api/auth/validateMeetingForAccept",
    Authentication.therapistAndClientVerification,
    VideoChatEP.validateMeetingForAcceptValidation(),
    VideoChatEP.validateMeetingForAccept
  );
  // app.post(
  //   "/api/auth/finalise/meetingWithLink",
  //   Authentication.therapistVerification,
  //   VideoChatEP.finaliseMeetingParamValidation(),
  //   VideoChatEP.finaliseMeetingWithLinkWithAWSS3WithAudioFiles
  // );

  // app.post(
  //   "/api/auth/finalise/meetingWithLinkWithNoRecordings",
  //   Authentication.therapistVerification,
  //   VideoChatEP.finaliseMeetingParamValidation(),
  //   VideoChatEP.finaliseMeetingWithoutSavingRecordings
  // );

  app.post(
    "/api/auth/recordingShare/meetingWithLink",
    Authentication.therapistVerification,
    VideoChatEP.recordingShareMeetingWithLink
  );

  // app.post(
  //   "/api/auth/checkAvailableMeetingTimeOfCurrentmeeting",
  //   Authentication.therapistAndClientVerification,
  //   VideoChatEP.checkAvailableMeetingTimeOfCurrentmeetingValidation(),
  //   VideoChatEP.checkAvailableMeetingTimeOfCurrentmeeting
  // );

  // app.post(
  //   "/api/auth/createTranscribeTextManuallyWithAws",
  //   Authentication.therapistVerification,
  //   VideoChatEP.createTranscribeTextManuallyWithAwsParamValidation(),
  //   VideoChatEP.createTranscribeTextManuallyWithAws
  // );

  app.post(
    "/api/auth/updateDiagnosisNoteWithOpenAiResponse",
    Authentication.therapistAndAdminVerification,
    VideoChatEP.updateDiagnosisNoteByOpenAiResponse
  );

}
