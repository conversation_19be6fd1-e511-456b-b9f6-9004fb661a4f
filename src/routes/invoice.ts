import { Express } from "express";
import { InvoiceEp } from "../end-point/invoice-ep";
import { Authentication } from "../middleware/authentication";

export function initInvoiceRoutes(app: Express) {
  app.get(
    "/api/auth/getPaymentHistoryByClient/:clientId/:limit/:offset",
    Authentication.superAdminVerification,
    InvoiceEp.getPaymentHistoryByClient
  );

  app.get(
    "/api/auth/getCurrentUserPaymentHistory/:limit/:offset",
    Authentication.clientVerification,
    InvoiceEp.getCurrentUserPaymentHistory
  );
}
