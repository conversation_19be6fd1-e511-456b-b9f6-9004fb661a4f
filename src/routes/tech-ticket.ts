import { Express } from "express";
import { TechTicketEp } from "../end-point/tech-ticket-ep";
import { Authentication } from "../middleware/authentication";

export function initTechTicketRoutes(app: Express) {
  app.post(
    "/api/auth/createTechTicket",
    Authentication.therapistAndClientVerification,
    TechTicketEp.createTechTicket
  );

  app.get(
    "/api/auth/getAllTechTickets/:limit?/:offset?",
    Authentication.superAdminVerification,
    TechTicketEp.getAllTechTickets
  );

  app.post("/api/auth/updateTechTicketStatus/:techTicketId",
    Authentication.superAdminVerification,
    TechTicketEp.updateTechTicketStatus
  );
}
