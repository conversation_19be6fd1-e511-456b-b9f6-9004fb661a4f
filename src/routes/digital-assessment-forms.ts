import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { DigitalAssessmentDetailsEp } from "../end-point/digital-assessment-forms-ep";

export function initDigitalAssessmentRoutes(app: Express) {
  app.post(
    "/api/auth/addDigitalAssesmentFormsDetails",
    Authentication.therapistAndAdminVerification,
    DigitalAssessmentDetailsEp.addDigitalAssessmentDetails
  );

  app.post(
    "/api/auth/updateDigitalAssesmentFormsDetails",
    Authentication.therapistAndAdminVerification,
    DigitalAssessmentDetailsEp.updateDigitalAssessmentForms
  );

  app.get(
    "/api/auth/getDigitalAssesmentFormsDetails/:clientId/:therapistId",
    Authentication.therapistVerification,
    DigitalAssessmentDetailsEp.getDigitalAssessmentDetailsByClientAndTherapistID
  );

  app.post(
    "/api/auth/getDigitalAssessmentDataByTherapist",
    Authentication.therapistVerification,
    DigitalAssessmentDetailsEp.getDigitalAssessmentDetailsByTherapist
  );

  app.post(
    "/api/auth/getDigitalAssessmentDataByAdmin",
    Authentication.superAdminVerification,
    DigitalAssessmentDetailsEp.getDigitalAssessmentDetailsByAdmin
  );

  app.post(
    "/api/auth/getClientUsingDigitalAssessmentIdByAdmin",
    Authentication.superAdminVerification,
    DigitalAssessmentDetailsEp.getClientUsingDigitalAssessmentIdByAdmin
  );

  app.post(
    "/api/auth/getDigitalAssestmentSessionDataByAdmin",
    Authentication.superAdminVerification,
    DigitalAssessmentDetailsEp.getDigitalAssestmentSessionDataByAdmin
  );

  app.post(
    "/api/auth/getDigitalAssestmentSessionData",
    Authentication.therapistVerification,
    DigitalAssessmentDetailsEp.getDigitalAssestmentSessionData
  );

  app.post(
     "/api/public/updateClientDigitalAssessmentSignature",
    // Authentication.therapistVerification,
    DigitalAssessmentDetailsEp.updateDigitalAssessmentFormsClientSignature
  );

  app.post(
    "/api/auth/getPreviousAIGeneratedAssessment",
    Authentication.therapistVerification,
    DigitalAssessmentDetailsEp.getPreviousAIGeneratedAssessment
  );

  app.post(
    "/api/auth/generateOpenAIAssessment",
    Authentication.therapistVerification,
    DigitalAssessmentDetailsEp.generateOpenAIAssessment
  );


}
