import { Express } from "express";
import { ReferralEarningEp } from "../end-point/referral-earning-ep";
import { Authentication } from "../middleware/authentication";

export function initReferralEarningsRoutes(app: Express) {

  app.get(
    "/api/auth/getReferralEarnings/:limit/:offset",
    Authentication.superAdminVerification,
    ReferralEarningEp.getAllReferralEarnings
  );

  app.post(
    "/api/auth/approveOrDeclineReferralEarningRecord",
    Authentication.superAdminVerification,
    ReferralEarningEp.approveOrDeclineReferralEarningRecord
  );

  app.post(
    "/api/auth/deleteReferralEarningRecord/:referralEarningId",
    Authentication.superAdminVerification,
    ReferralEarningEp.deleteReferralEarningRecord
  );

  app.post(
    "/api/auth/transferReferralEarningPayment",
    Authentication.superAdminVerification,
    ReferralEarningEp.transferReferralEarningPaymentToTherapist
  );

  app.post(
    "/api/auth/payReferralBonusOfCurrentMonthAsBulk",
    Authentication.superAdminVerification,
    ReferralEarningEp.transferReferralBonusOfCurrentMonthAsBulk
  );

  app.get(
    "/api/auth/singleTherapistRewardDetails",
    ReferralEarningEp.getSingleTherapistRewardDetails
  );

}
