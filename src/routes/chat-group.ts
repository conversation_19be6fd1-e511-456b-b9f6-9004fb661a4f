import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { ChatGroupEp } from "../end-point/chat-group-ep";

export function initChatGroupRoutes(app: Express) {
  app.post(
    "/api/auth/chatGroup/createChatGroup",
    Authentication.therapistVerification,
    ChatGroupEp.createChatGroup
  );
  app.post(
    "/api/auth/chatGroup/updateChatGroup",
    Authentication.therapistVerification,
    ChatGroupEp.updateChatGroup
  );
  app.post(
    "/api/auth/chatGroup/addMember",
    Authentication.therapistVerification,
    ChatGroupEp.addMemberToChatGroupValidationRules(),
    ChatGroupEp.addMemberToChatGroup
  );
  app.post(
    "/api/auth/chatGroup/removeMember",
    Authentication.therapistVerification,
    ChatGroupEp.removeMemberFromChatGroupValidationRules(),
    ChatGroupEp.removeMemberFromChatGroup
  );
  app.post(
    "/api/auth/chatGroup/getAllChatGroupsForUser",
    Authentication.therapistAndClientVerification,
    ChatGroupEp.getAllChatGroupsForUser
  );
  app.post(
    "/api/auth/chatGroup/getAllMembersInChatGroup",
    Authentication.therapistAndClientVerification,
    ChatGroupEp.getAllUsersInChatGroupValidationRules(),
    ChatGroupEp.getAllUsersInChatGroup
  );

  app.post(
    "/api/auth/chatGroup/getAllChatGroupSessions",
    Authentication.therapistAndClientVerification,
    ChatGroupEp.getAllSessionsInChatGroupValidationRules(),
    ChatGroupEp.getAllSessionsInChatGroup
  );

  app.post(
    "/api/auth/chatGroup/getAllMatchedClientsForChatGroup",
    Authentication.therapistVerification,
    ChatGroupEp.getAllMatchedClientsForChatGroupValidationRules(),
    ChatGroupEp.getAllMatchedClientsForChatGroup
  );

  app.post(
    "/api/auth/chatGroup/getAllPublicChatGroupsForClient",
    Authentication.clientVerification,
    ChatGroupEp.getAllPublicChatGroupsForClientValidationRules(),
    ChatGroupEp.getAllPublicChatGroupsForClient
  );

  app.post(
    "/api/auth/chatGroup/sendJoinRequestForPublicGroup",
    Authentication.clientVerification,
    ChatGroupEp.sendJoinRequestForPublicGroup
  );

  app.post(
    "/api/auth/chatGroup/cancellJoinRequestForPublicGroup",
    Authentication.clientVerification,
    ChatGroupEp.cancellJoinRequestForPublicGroup
  );

  app.post(
    "/api/auth/chatGroup/getAllJoinRequestForPublicChatGroup",
    Authentication.therapistVerification,
    ChatGroupEp.getAllJoinRequestForPublicChatGroup
  );

  app.post(
    "/api/auth/chatGroup/acceptJoinRequestForPublicGroup",
    Authentication.therapistVerification,
    ChatGroupEp.acceptJoinRequestForPublicGroup
  );

  app.post(
    "/api/auth/chatGroup/rejectJoinRequestForPublicGroup",
    Authentication.therapistVerification,
    ChatGroupEp.rejectJoinRequestForPublicGroup
  );

  // chat api's

  app.post(
    "/api/auth/chatGroup/sendMessage",
    Authentication.therapistAndClientVerification,
    ChatGroupEp.sendMessage
  );

  app.post(
    "/api/auth/chatGroup/deleteMessage",
    Authentication.therapistAndClientVerification,
    ChatGroupEp.deleteMessageValidationRules(),
    ChatGroupEp.deleteMessage
  );

  app.post(
    "/api/auth/chatGroup/getAllMessages",
    Authentication.therapistAndClientVerification,
    ChatGroupEp.getAllMessagesInChatGroupValidationRules(),
    ChatGroupEp.getAllMessagesInChatGroup
  );

  app.post(
    "/api/auth/chatGroup/updateLastActiveOfMember",
    Authentication.therapistAndClientVerification,
    ChatGroupEp.updateLastActiveOfMemberValidationRules(),
    ChatGroupEp.updateLastActiveOfMember
  );

  app.post(
    "/api/auth/chatGroup/updateMainMember",
    Authentication.therapistAndClientVerification,
    ChatGroupEp.updateMainMemberInChatGroup
  );

  app.post(
    "/api/auth/chatGroup/getAllChatGroupCallsByTherapistId",
    Authentication.therapistAndClientVerification,
    ChatGroupEp.getAllChatGroupCallsByTherapistIdValidationRules(),
    ChatGroupEp.getAllChatGroupCallsByTherapistId
  );
}
