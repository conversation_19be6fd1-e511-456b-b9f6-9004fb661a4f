import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { SaasTherapistEp } from "../end-point/saas-therapist-ep";

export function initSaasTherapistRoutes(app: Express) {
  app.get(
    "/api/auth/saasTherapist/getAllClients",
    Authentication.therapistVerification,
    SaasTherapistEp.getAllClients
  );
  app.get(
    "/api/auth/saasTherapist/getPersonalDetails/:clientId",
    Authentication.therapistVerification,
    SaasTherapistEp.getPersonalDetails
  );
  app.put(
    "/api/auth/saasTherapist/updatePersonalDetails/:clientId",
    Authentication.therapistVerification,
    SaasTherapistEp.updatePersonalDetailsValidationRules(),
    SaasTherapistEp.updatePersonalDetails
  );
  app.get(
    "/api/auth/saasTherapist/getTodoDetails",
    Authentication.therapistVerification,
    SaasTherapistEp.getTodoDetails
  );
  app.get(
    "/api/auth/saasTherapist/getTaskDetails",
    Authentication.therapistVerification,
    SaasTherapistEp.getTaskDetails
  );
  app.post(
    "/api/auth/saasTherapist/createTask",
    Authentication.therapistVerification,
    SaasTherapistEp.createTask
  );
  app.post(
    "/api/auth/saasTherapist/updateTask",
    Authentication.therapistVerification,
    SaasTherapistEp.updateTaskById
  );
  app.delete(
    "/api/auth/saasTherapist/deleteTask/:taskId",
    Authentication.therapistVerification,
    SaasTherapistEp.deleteTaskById
  );
  app.get(
    "/api/auth/saasTherapist/getInsuranceDetailsViaClientId/:clientId",
    Authentication.therapistVerification,
    SaasTherapistEp.getInsuranceDetailsViaClientId
  );
  app.get(
    "/api/auth/saasTherapist/viewProfileByUserId/:userId",
    Authentication.therapistVerification,
    SaasTherapistEp.viewProfileByUserId
  );
  app.post(
    "/api/auth/saasTherapist/searchClientsByTherapist/:limit/:offset",
    Authentication.therapistVerification,
    SaasTherapistEp.searchClientsByTherapist
  );
  app.post(
    "/api/auth/switchDashboardVersion",
    Authentication.therapistVerification,
    SaasTherapistEp.switchDashboardVersion
  );
}

