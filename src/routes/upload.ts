import { Express } from "express";
import { UploadEp } from "../end-point/upload-ep";
import multer = require("multer");
import { Authentication } from "../middleware/authentication";
import { UploadDocumentEp } from "../end-point/uplpad-documents-ep";
import {uploadCallRecordFile } from "../middleware/upload-call-record-files";

export function initUploadRoutes(app: Express) {
  app.get('/api/public/recorded_files/:fileId/:token', UploadEp.getCallRecordFiles);
  app.get('/api/public/file/:imageId/:name?/:token', UploadEp.getImageFromId);
  app.get("/downloadDiagonosisNoteDetail/:pdfName",UploadEp.downloadDiagnosisNote);
  app.get('/api/public/file/:imageId/:name?/:token', UploadEp.getImageFromToken);

  app.get('/api/public/files/:imageId/:name?', UploadEp.getImageFromId);

  app.post(
    "/api/auth/upload_documents",
    Authentication.therapistVerification,
    UploadDocumentEp.uploadDocuments
  );

  app.get(
    "/api/auth/upload_documents_by_therapists/:userId/:limit?/:offset?",
    Authentication.therapistVerification,
    UploadDocumentEp.getAllUploadDocumentByTherapistId
  );

  app.get(
    "/api/auth/upload_documents_by_clients/:userId/:limit?/:offset?",
    Authentication.clientVerification,
    UploadDocumentEp.getAllUploadDocumentByClientId
  );

  app.post(
    "/api/auth/upload_new_session_record",
    Authentication.therapistVerification,
    uploadCallRecordFile ,
    UploadEp.uploadNewSessionRecordFile
  );

  app.get('/api/public/disclosure-statement-of-therapist/:therapistId', UploadEp.getDisclosureStatementOfTherapist);
}
