import { Express } from "express";
import { InstagramFeedEp } from "../end-point/instagram-feed-ep";
var cron = require('node-cron');
const express = require('express')
const INSTAGRAMFEED_DIR = 'uploads/INSTAGRAM_FEED';

export function initInstagramFeedRoutes(app: Express) {
  app.use('/instagramFeed', express.static(INSTAGRAMFEED_DIR))
  app.get(
    "/api/instagram-feed",
    InstagramFeedEp.getInstagramFeed
  );

  app.get("/api/instagram-feed-run",InstagramFeedEp.runInstagramFeedCronJob);
}
