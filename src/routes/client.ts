import { Express } from "express";
import { ClientEp } from "../end-point/client-ep";
import { Authentication } from "../middleware/authentication";

export function initClientRoutes(app: Express) {
  app.get("/api/auth/dislikeTherapist/:therapistId", Authentication.clientVerification, ClientEp.dislikeTherapist);

  app.post("/api/auth/addPreferences", Authentication.clientVerification, ClientEp.addPreferences);

  app.post("/api/auth/subscribe", Authentication.clientVerification, ClientEp.clientSubscribe);

  app.post("/api/auth/createClient", Authentication.clientVerification, ClientEp.clientCreate);

  app.post("/api/auth/attachPaymentMethod", Authentication.clientVerification, ClientEp.attachPaymentMethod);

  app.get("/api/auth/currentSubscription", Authentication.clientVerification, ClientEp.getActiveSubscription);

  app.get("/api/auth/paymentMethods", Authentication.clientVerification, ClientEp.getPaymentMethods);

  app.get("/api/auth/billingHistory/:limit?/:offset?", Authentication.clientVerification, ClientEp.getBillingHistory);

  app.get("/api/auth/cancelSubscription", Authentication.clientVerification, ClientEp.cancelSubscription);

  app.get("/api/auth/resumeSubscription", Authentication.clientVerification, ClientEp.resumeSubscription);

  app.get("/api/auth/getClienByClientId/:clientId", Authentication.allUserVerification, ClientEp.getClientByClientId);

  app.get("/api/auth/getSingleClienByClientId/:clientId", Authentication.therapistAndClientVerification, ClientEp.getSingleClientByClientId);

  app.post("/api/auth/changeDefaultPaymentMethod", Authentication.clientVerification, ClientEp.changeDefaultPaymentMethod);

  app.post("/api/auth/updateWordCount", Authentication.clientVerification, ClientEp.updateWordCount);

  app.post("/api/auth/makeMonthlyInvoicePayment/:invoiceId", Authentication.clientVerification, ClientEp.makeMonthlyInvoicePayment);

  app.post(
    "/api/auth/sendReferrelMail",
    Authentication.clientVerification,
    ClientEp.sendReferralMail
  );

  app.post(
    "/api/auth/sendReferralSms",
    Authentication.clientVerification,
    ClientEp.sendReferralSMS
  );
  
  app.post(
    "/api/auth/changePrimaryTherapist",
    Authentication.clientVerification,
    ClientEp.changePrimaryTherapist
  );
  
  app.get(
    "/api/auth/getAllMatchedTherapists",
    Authentication.clientVerification,
    ClientEp.getAllMatchedTherapists
  );

  app.get("/api/auth/client-dashboard-stats", Authentication.clientVerification, ClientEp.getClientDashboardStats);

  app.get("/api/auth/client-appointment", Authentication.clientVerification, ClientEp.getClientAppointment);

  app.post(
    "/api/auth/send-referral-linkViaSMS",
    Authentication.clientVerification,
    ClientEp.sendReferralLinkSMS
  );
  
  app.post(
    "/api/auth/send-referral-linkViaMail",
    Authentication.clientVerification,
    ClientEp.sendReferralLinkMail
  );

  app.get(
    "/api/auth/client-copayment-amount",
    Authentication.verifyToken,
    ClientEp.getClientCopaymentAmount
  );

  app.get(
    "/api/auth/client-last-copayment-info",
    Authentication.clientVerification,
    ClientEp.getClientLastCopaymentInfo
  );
}
