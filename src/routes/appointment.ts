import { Express } from "express";
import { AppointmentEp } from "../end-point/appointment-ep";
import { Authentication } from "../middleware/authentication";
import { verifyActiveSubscription } from "../middleware/check-subscription";

export function initAppointmentRoutes(app: Express) {
  app.post(
    "/api/auth/createAppointmentByClient",
    Authentication.clientVerification,
    AppointmentEp.createAppointmentValidationRules(),
    verifyActiveSubscription(),
    AppointmentEp.createAppointmentByClient
  );
  
  app.post(
    "/api/auth/createAppointmentByClientWithMatch",
    Authentication.clientVerification,
    AppointmentEp.createAppointmentValidationRules(),
    verifyActiveSubscription(),
    AppointmentEp.createAppointmentByClientWithMatch
  );
  
  app.post(
    "/api/public/createAppointmentByClient",
    AppointmentEp.createAppointmentValidationRules(),
    AppointmentEp.createAppointmentByClientPublic
  );

  app.post(
    "/api/auth/createAppointmentByTherapist",
    Authentication.therapistVerification,
    AppointmentEp.createAppointmentValidationRules(),
    verifyActiveSubscription(),
    AppointmentEp.createAppointmentByTherapist
  );

  app.post(
    "/api/auth/updateAppointment",
    AppointmentEp.updateAppointmentValidationRules(),
    verifyActiveSubscription(),
    AppointmentEp.updateAppointment
  );

  app.post(
    "/api/auth/updateAppointmentStatus/:id/:status",
    Authentication.therapistAndClientVerification,
    AppointmentEp.updateAppointmentValidationRules(),
    verifyActiveSubscription(),
    AppointmentEp.updateAppointmentStatus
  );

  app.get(
    "/api/auth/viewSingleAppointment/:id",
    Authentication.therapistAndClientVerification,
    verifyActiveSubscription(),
    AppointmentEp.viewSingleAppointment
  );

  app.get(
    "/api/public/viewSingleAppointment/:id",
    AppointmentEp.viewSingleAppointmentPublic
  );

  app.post(
    "/api/auth/searchAppointmentsByDate",
    Authentication.therapistAndClientVerification,
    AppointmentEp.searchAppointmentValidationRules(),
    verifyActiveSubscription(),
    AppointmentEp.searchAppointmentsByDate
  );

  app.get(
    "/api/auth/viewAllAppointmentsByUser/:customActiveTab/:limit/:offset",
    Authentication.therapistAndClientVerification,
    AppointmentEp.viewAllAppointmentsByUser
  );

  app.get(
    "/api/auth/getAllUpcomingAppointmentsById/:limit?",
    Authentication.therapistAndClientVerification,
    AppointmentEp.getAllUpcomingAppointmentById
  );

  app.get(
    "/api/auth/viewAllUpcomingAppointments/:limit?",
    Authentication.therapistAndClientVerification,
    AppointmentEp.viewAllUpcomingAppointments
  );

  app.post(
    "/api/auth/deleteAppointment",
    verifyActiveSubscription(),
    AppointmentEp.deleteAppointment
  );

  app.post(
    "/api/public/deleteAppointment",
    AppointmentEp.deleteAppointmentPublic
  );

  app.get("/api/auth/viewAppointmentsByTherapist/:limit?", Authentication.therapistVerification, AppointmentEp.viewAppointmentsByTherapist);

  app.get(
    "/api/auth/viewAppointmentsByUserId/:limit/:offset",
    Authentication.therapistAndClientVerification,
    AppointmentEp.viewAppointmentsByUserIdParamValidation(),
    AppointmentEp.viewAppointmentsByUserId
  );

  app.post(
    "/api/auth/getAllAppointmentsAndDetailsByUserId",
    Authentication.therapistAndClientVerification,
    AppointmentEp.getAllAppointmentsAndDetailsByUserId
  );

  app.post(
    "/api/public/getAllAppointmentsAndDetailsByUserId",
    AppointmentEp.getAllAppointmentsAndDetailsByUserId
  );

  app.get(
    "/api/auth/viewAllAppointmentsByTherapistId/:id",
    Authentication.therapistAndClientVerification,
    AppointmentEp.viewAllAppointmentsByTherapistId
  );

  app.get(
    "/api/public/viewAllAppointmentsByTherapistId/:id",
    AppointmentEp.viewAllAppointmentsByTherapistId
  );

  app.get(
    "/api/auth/getAllPendingAndCompletedAppointmentsByUser/:id",
    Authentication.therapistAndClientVerification,
    AppointmentEp.getAllStatisticsOfAppointments
  );

  app.post(
    "/api/auth/updateApprovalStatus",
    Authentication.therapistAndClientVerification,
    AppointmentEp.approvalStatusValidationRules(),
    AppointmentEp.updateAppointmentApprovalStatus
  );

  app.post(
    "/api/auth/updateApprovalStatusByClientForClienAppointment",
    Authentication.therapistAndClientVerification,
    AppointmentEp.approvalStatusValidationRules(),
    AppointmentEp.updateAppointmentApprovalStatusByClientForClientCreatedAppointment
  );

  app.get(
    "/api/public/notify30min",
    AppointmentEp.notify30min
  );

  app.post(
    "/api/public/viewAllAppointmentsByTherapistIdForMonth",
    AppointmentEp.viewAllAppointmentsByTherapistIdForMonth
  );

  app.get(
    "/api/public/tomorrowSchedule/:id",
    AppointmentEp.viewTomorrowAppointmentScheduleForTherapist
  );

  app.post(
    "/api/public/create-appointment",
    Authentication.validateAppointmentsAuthToken,
    AppointmentEp.createPublicAppointmentValidationRules(),
    AppointmentEp.createPublicAppointment
  );

}
