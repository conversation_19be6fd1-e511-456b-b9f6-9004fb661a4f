import { Express } from "express";
import { ClientRewardEp } from "../end-point/client-reward-ep";
import { Authentication } from "../middleware/authentication";

export function initClientRewardRoutes(app: Express) {

  app.get(
    "/api/auth/getClientRewards/:limit/:offset",
    Authentication.superAdminVerification,
    ClientRewardEp.getClientRewards
  );

  app.post(
    "/api/auth/markedAsPaidClientRewardRecord",
    Authentication.superAdminVerification,
    ClientRewardEp.markedAsPaidClientRewardRecord
  );

  app.post(
    "/api/auth/deleteClientRewardRecord/:rewardId",
    Authentication.superAdminVerification,
    ClientRewardEp.deleteClientRewardRecord
  );

  app.get(
    "/api/auth/singleClientRewards",
    ClientRewardEp.getClientRewardsByClientId
  );

  // app.get(
  //   "/api/public/rewardAPIClient",
  //   ClientRewardEp.rewardAPIClient
  // );

}
