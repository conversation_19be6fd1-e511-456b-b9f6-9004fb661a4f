import { Express } from "express";
import { Vonage<PERSON>allEp } from "../end-point/vonage-call-ep";
import { Authentication } from "../middleware/authentication";
const cron = require('node-cron');

export function initVonageCallRoutes(app: Express) {
    app.get(
        "/api/auth/vonage_session/:name",
        Authentication.therapistAndClientVerification,
        VonageCallEp.createVonageSession
    )

    app.get(
        "/api/auth/vonage_token/:token",
        Authentication.therapistAndClientVerification,
        VonageCallEp.generateVonageToken
    )

    app.get(
        "/api/auth/meeting/:id",
        Authentication.therapistAndClientVerification,
        VonageCallEp.getMeetingByMeetingIdForVonage
    )

    app.get(
        "/api/auth/startArchive/:sessionId",
        Authentication.therapistAndClientVerification,
        VonageCallEp.startArchive
    )

    app.get(
        "/api/auth/stopArchive/:roomName",
        Authentication.therapistAndClientVerification,
        VonageCallEp.stopArchive
    )

    app.post(
        "/api/auth/meeting/updateFirstSpeaker",
        Authentication.therapistAndClientVerification,
        VonageCallEp.updateFirstSpeakerInMeeting
    )

    // app.get(
    //     "/api/public/getArchive/:sessionId",
    //     VonageCallEp.getArchiveByDeepgram
    // );

    app.post(
        "/api/auth/cancel/vonage/call",
        Authentication.therapistAndClientVerification,
        VonageCallEp.cancelVonageCall
    )

    app.post(
        "/api/auth/join/vonage/call",
        Authentication.therapistAndClientVerification,
        VonageCallEp.joinVonageMeeting
    );

    app.post(
        "/api/auth/initialize/vonage/call",
        Authentication.therapistAndClientVerification,
        VonageCallEp.initializeVonageVideoCallValidation(),
        VonageCallEp.initializeVonageVideoCall
    );

    app.post(
        "/api/auth/start/vonage/call",
        Authentication.therapistAndClientVerification,
        VonageCallEp.startCallValidation(),
        VonageCallEp.startVonageVideoCall
    );

    app.put(
        "/api/auth/vonage/updateSessionId/:roomName",
        Authentication.therapistAndClientVerification,
        VonageCallEp.updateSessionIdByRoomNameValidation(),
        VonageCallEp.updateSessionIdByRoomName
    );

    app.get(
        "/api/auth/vonage/getMeetingData/:roomName",
        Authentication.therapistAndClientVerification,
        VonageCallEp.getMeetingDetailsBySessionName
    );

    app.get(
        "/api/auth/vonage/getMeetingByMeetingId/:meetingId",
        Authentication.therapistAndClientVerification,
        VonageCallEp.getMeetingByMeetingIdField
    );

    app.get(
        "/api/auth/generateSessionForNetworkTest",
        Authentication.therapistAndClientVerification,
        VonageCallEp.createSessionForNetworkTest
    )

    app.get(
        "/api/public/updateParticipantCountInAllMeetings",
        VonageCallEp.updateParticipantCountInAllMeetings
    )

    app.get(
        "/api/public/updatecallingStatusInAllMeetings",
        VonageCallEp.updatecallingStatusInAllMeetings
    )

    app.post(
        "/api/auth/busy/vonage/call",
        Authentication.therapistAndClientVerification,
        VonageCallEp.busyVonageCall
    )

    app.post(
        "/api/auth/vonage/save/session-feedback",
        Authentication.therapistAndClientVerification,
        VonageCallEp.saveVonageSessionFeedback
    );

    app.post(
        "/api/auth/checkClientHasCompletedSessionsOnThisWeek",
        Authentication.clientVerification,
        VonageCallEp.checkClientHasCompletedSessionsOnThisWeek
    )
}