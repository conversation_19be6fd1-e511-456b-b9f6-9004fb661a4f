import { Express } from "express";
import { GoalEp } from "../end-point/goal-ep";
import { HomeworkEp } from "../end-point/homework-ep";
import { Authentication } from "../middleware/authentication";
import { verifyActiveSubscription } from "../middleware/check-subscription";

export function initHomeworkAndGoalRoutes(app: Express) {
    app.post("/api/auth/createHomework", Authentication.therapistVerification, HomeworkEp.createHomework);

    app.post("/api/auth/updateHomework", Authentication.therapistAndClientVerification, verifyActiveSubscription(), HomeworkEp.updateHomework);

    app.get("/api/auth/allHomeworksByTherapist/:userId/:limit?/:offset?", Authentication.therapistVerification, HomeworkEp.getAllHomeworkByTherapistId);

    app.get("/api/auth/allHomeworksByClient/:userId/:limit?/:offset?", Authentication.clientVerification, verifyActiveSubscription(), HomeworkEp.getAllHomeworkByClientId);

    app.get("/api/auth/homeworkBy/:hId", verifyActiveSubscription(), HomeworkEp.getHomeworkById);

    app.delete("/api/auth/deleteHomeworkBy/:hId", Authentication.therapistVerification, verifyActiveSubscription(), HomeworkEp.deleteHomeworkById);

    app.post("/api/auth/createGoal", Authentication.therapistAndClientVerification, verifyActiveSubscription(), GoalEp.createGoal);

    app.post("/api/auth/updateGoal/:goalId", Authentication.therapistAndClientVerification, verifyActiveSubscription(), GoalEp.updateGoal);

    app.get("/api/auth/getAllGoals/:createdId/:assignedId/:limit?/:offset?", verifyActiveSubscription(), GoalEp.getAllGoalsByUserIds);

    app.get("/api/auth/getGoal/:goalId", verifyActiveSubscription(), GoalEp.getGoalByGoalId);

    app.delete("/api/auth/deleteGoal/:goalId", Authentication.therapistAndClientVerification, verifyActiveSubscription(), GoalEp.deleteGoalById);
}