import { Express } from "express";
import { ClientInsuranceEp } from "../end-point/client-insurance-ep";
import { Authentication } from "../middleware/authentication";

export function initAdminRoutesExtra(app: Express) {
  app.post(
    "/api/auth/get-insurance-company",
    Authentication.verifyToken,
    ClientInsuranceEp.getInsuranceCompanyByClientId
  );

  // Add new public API that doesn't require authentication
  app.post(
    "/api/public/get-insurance-company",
    ClientInsuranceEp.getInsuranceCompanyByClientId
  );
} 