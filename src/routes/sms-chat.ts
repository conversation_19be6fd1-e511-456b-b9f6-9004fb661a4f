import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { SmsChatEp } from "../end-point/sms-chat-ep";
import multer = require("multer");

const uploadPost = multer({ dest: "uploads/ATTACHMENTS" }).single("attachFile");

export function initSmsChatRoutes(app: Express) {
  app.post(
    "/api/auth/chat/createMessage",
    Authentication.therapistAndClientVerification,
    SmsChatEp.createMessageNewWithDirectMessage
  );

  app.post(
    "/api/auth/chat/createMessageWithAttachment",
    Authentication.therapistAndClientVerification,
    uploadPost,
    SmsChatEp.uploadAttachmentFileAndCreateMessageNewWithDirectMessage
  );

  app.post("/api/conversations/webhook", SmsChatEp.conversationWebhookFunction);
}
