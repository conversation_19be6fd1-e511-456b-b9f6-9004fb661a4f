import { Express } from "express";
import { TherapistEp } from "../end-point/therapist-ep";
import { Authentication } from "../middleware/authentication";
import { AppointmentEp } from "../end-point/appointment-ep";
import { TransactionsEp } from "../end-point/transactions-ep";
import { uploadCallRecordFile } from "../middleware/upload-call-record-files";
import { uploadAndTransferToS3 } from './../middleware/upload-call-records-files-s3';

export function initTherapistRoutes(app: Express) {
  app.post(
    "/api/auth/searchTherapists/:limit/:offset",
    Authentication.clientVerification,
    TherapistEp.searchTherapists
  );

  app.post(
    "/api/public/searchTherapists/:limit/:offset",
    TherapistEp.searchTherapistsPublic
  );

  app.post("/api/public/allTherapists", TherapistEp.allTherapistsPublic);

  app.post(
    "/api/auth/updateProfileVideo",
    Authentication.therapistVerification,
    TherapistEp.updateProfileVideo
  );

  app.post(
    "/api/auth/addReview",
    Authentication.clientVerification,
    TherapistEp.addReviewValidationRules(),
    TherapistEp.addReview
  );

  app.get(
    "/api/auth/viewReviewsByTherapistId/:id",
    Authentication.therapistAndClientVerification,
    TherapistEp.viewReviewsByTherapistId
  );

  app.post(
    "/api/auth/searchClientsByParams/:limit/:offset",
    Authentication.therapistVerification,
    TherapistEp.searchClientByParamsValidationRules(),
    TherapistEp.searchClientByParams
  );

  app.post(
    "/api/auth/treatmentHistory",
    Authentication.therapistVerification,
    TherapistEp.treatmentHistory
  );

  app.get(
    "/api/auth/treatmentHistory/:clientId/:limit/:offset",
    Authentication.therapistVerification,
    TherapistEp.viewTreatmentHistoryByClientId
  );

  app.get(
    "/api/auth/treatmentHistoryBy/:therapistId/:limit/:offset",
    Authentication.clientVerification,
    TherapistEp.viewTreatmentHistoryByTherapistId
  );

  app.post(
    "/api/auth/treatmentHistoryBy/:therapistId/:limit/:offset",
    Authentication.therapistVerification,
    TherapistEp.viewTreatmentHistoryByTherapistIdViewMore
  );

  app.delete(
    "/api/auth/deleteTreatmentHistory/:_id",
    Authentication.therapistVerification,
    TherapistEp.deleteTreatmentHistory
  );

  app.post(
    "/api/auth/updateTreatmentHistory",
    Authentication.therapistVerification,
    TherapistEp.updateTreatmentHistory
  );

  app.post(
    "/api/auth/mergeTreatmentHistory",
    Authentication.therapistVerification,
    TherapistEp.mergeTreatmentHistory
  );

  app.post(
    "/api/auth/sendReminderSms",
    Authentication.therapistVerification,
    TherapistEp.sendReminderSms
  );

  app.post(
    "/api/auth/create-pin",
    Authentication.therapistVerification,
    TherapistEp.createPin
  );
  app.post(
    "/api/auth/update-pin",
    Authentication.therapistVerification,
    TherapistEp.updatePin
  );

  app.post(
    "/api/auth/generateSoapWithOpenAI",
    Authentication.therapistVerification,
    TherapistEp.generateSoapWithOpenAI
  );

  app.post(
    "/api/auth/update-aiGenerateCount",
    Authentication.therapistVerification,
    TherapistEp.updateAiGeneratedCount
  );
  app.get(
    "/api/auth/get-aiGenerateCount",
    Authentication.therapistVerification,
    TherapistEp.getAiGeneratedCountFromRequest
  );

  app.post(
    "/api/auth/therapist-review",
    Authentication.therapistVerification,
    TherapistEp.reviewValidationRules(),
    TherapistEp.TherapistReview
  );

  app.get(
    "/api/auth/therapist-dashboard-stats",
    Authentication.therapistVerification,
    TherapistEp.getTherapistDashboardStats
  );

  app.post(
    "/api/auth/therapist-sessions",
    Authentication.therapistVerification,
    AppointmentEp.getAllStatisticsOfAppointmentStatSessions
  );

  app.post(
    "/api/auth/therapist-earnings",
    Authentication.therapistVerification,
    TransactionsEp.getAllEarningsTherapist
  );

  app.post(
    "/api/auth/send-referral-linkByMail",
    Authentication.therapistVerification,
    TherapistEp.sendReferralMail
  );

  app.post(
    "/api/auth/send-referral-linkBySMS",
    Authentication.therapistVerification,
    TherapistEp.sendReferralSMS
  );

  app.post(
    "/api/auth/make-client-inactive",
    Authentication.therapistVerification,
    TherapistEp.makeClientInactive
  );

  app.post(
    "/api/auth/validate-appoiments-available",
    Authentication.therapistVerification,
    TherapistEp.validateAppoimentsAvailableInBlockDaterange
  );

  app.post(
    "/api/auth/update-blocked-dates",
    Authentication.therapistVerification,
    TherapistEp.updateBlockedDates
  );

  app.post(
    "/api/auth/add-blocked-dates",
    Authentication.therapistVerification,
    TherapistEp.addBlockedDates
  );

  app.post(
    "/api/auth/updateTherapistAvailableTimes",
    Authentication.therapistVerification,
    TherapistEp.updateTherapistAvailableTimes
  );

  app.post(
    "/api/auth/updateTherapistSignature",
    Authentication.therapistVerification,
    TherapistEp.updateTherapistSignature
  );

  app.get(
    "/api/auth/getRecentMonthlyAmounts",
    Authentication.therapistVerification,
    TransactionsEp.getRecentMonthlyAllAccumulatedBalance
  );

  // app.get(
  //   "/api/public/getScoreOfTheTherapist",
  //   TherapistEp.getScoreOfTheTherapist
  // );

  app.post(
    "/api/public/createTherapistScoreConstants",
    TherapistEp.createTherapistScoreConstants
  );

  app.get(
    "/api/auth/getTherapistScoreDetails/:limit/:offset",
    Authentication.superAdminVerification,
    TherapistEp.getTherapistScoreDetails
  );

  app.get(
    "/api/public/getTherapistScoreConstants",
    TherapistEp.getTherapistScoreConstants
  );

  app.get(
    "/api/public/forTesting",
    TherapistEp.forTesting
  );

  app.post(
    "/api/auth/createRegularMeeting",
    Authentication.therapistVerification,
    uploadAndTransferToS3,
    // uploadCallRecordFile,
    TherapistEp.createRegularMeeting
  );

  app.post(
    "/api/auth/getTherapists/:limit/:offset",
    Authentication.therapistVerification,
    TherapistEp.getTherapistsList
  );

  app.post(
    "/api/auth/addSubTherapistTreatmentHistory",
    Authentication.therapistAndAdminVerification,
    TherapistEp.addSubTherapistToTreatmentHistory
  );

  app.post(
    "/api/auth/createRegularMeetingForTWilio",
    Authentication.therapistVerification,
    TherapistEp.createRegularMeeting
  );

  app.post(
    "/api/auth/updateAINoteType",
    Authentication.therapistVerification,
    TherapistEp.updateAINoteType
  );
  
  app.post(
    "/api/auth/searchClientInsuranceDocumentsByTherapist/:limit?/:offset?",
    Authentication.therapistVerification,
    TherapistEp.searchClientInsuranceDocumentsByTherapist
  );

  app.post(
    "/api/auth/submitDocsForAdminApproval",
    Authentication.therapistVerification,
    TherapistEp.submitDocsForAdminApproval
  );

  app.post(
    "/api/auth/getPresignUrlForAudioMeeting",
    Authentication.allUserVerification,
    TherapistEp.getPresignUrlForTreatementHistoryAudioByMeetingId
  );

  //start
  app.post(
    "/api/public/updateSpecificRegularMeetingData",
    TherapistEp.regularCallDataUpdate
  );
  //end

  app.get(
    "/api/auth/getTherapistScoreByTherapistId",
    Authentication.therapistVerification,
    TherapistEp.getTherapistScoreAllDetailsByTherapistId
  );
  
}
