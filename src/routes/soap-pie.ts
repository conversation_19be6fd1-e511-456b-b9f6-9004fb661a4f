import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { SoapPIEEp } from '../end-point/sope-pie-ep'
import { VideoChatEP } from '../end-point/video-chat-ep'

export function initSopePieRoutes(app: Express) {
    app.post(
        "/api/auth/generateSopePieAINotes",
        Authentication.therapistVerification,
        SoapPIEEp.generateSopeOrPIEWithOpenAI
    )

    // New endpoint for admin to generate SOAP/PIE notes
    app.post(
        "/api/auth/admin/generateSopePieAINotes",
        Authentication.superAdminVerification,
        SoapPIEEp.generateSopeOrPIEWithOpenAIForAdmin
    )

    app.post(
        "/api/auth/downloadDiagnosisPIENoteById/:noteId",
        Authentication.therapistAndAdminVerification,
        SoapPIEEp.downloadPIEDiagnosisNoteById
    );
}