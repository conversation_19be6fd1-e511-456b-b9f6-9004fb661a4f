import { Express } from "express";
import { UserEp } from "../end-point/user-ep";
import { Authentication } from "../middleware/authentication";
import { InsuranceEp } from "../end-point/insurance-ep";
import * as UserSearchEp from "../end-point/user-search-ep";

export function initUserRoutes(app: Express) {
  // app.post("/api/public/signUpWithFacebook", UserEp.signUpWithFacebook);

  app.post("/api/public/signUpWithFacebookReact", UserEp.signUpWithFacebookReact);

  app.post("/api/public/loginWithFacebook", UserEp.loginWithFacebook);

  app.post("/api/public/signUpWithGoogle", UserEp.signUpWithGoogle);

  app.post("/api/public/signUpWithGoogleReact", UserEp.signUpWithGoogleReact);

  app.post("/api/public/loginWithGoogle", UserEp.loginWithGoogle);

  app.post("/api/public/signUp", UserEp.signUpValidationRules(), UserEp.signUp);

  app.post(
    "/api/public/login",
    UserEp.loginWithEmailValidationRules(),
    UserEp.loginWithEmail
  );

  app.post(
    "/api/public/registerWithEmail",
    UserEp.signUpValidationRulesTemp(),
    UserEp.signUpPublic
  );

  app.post(
    "/api/public/resendVerificationCode",
    UserEp.signUpValidationRulesResendVerificationTemp(),
    UserEp.resendSignUpVerification
  );

  app.post("/api/public/verifyByCode", UserEp.verifyUserByCode);

  app.post("/api/public/verifyByLink", UserEp.verifyUserByLink);

  app.post(
    "/api/public/forgotPassword",
    UserEp.forgotPasswordValidationRules(),
    UserEp.sendForgotPasswordMail
  );

  app.post("/api/public/resetPassword", UserEp.resetPassword);

  app.get("/api/auth/me", Authentication.verifyToken, UserEp.getMe);

  app.get("/api/user/profile", Authentication.verifyToken, UserEp.getMe);

  app.post(
    "/api/public/contact-us",
    UserEp.contactValidationRules(),
    UserEp.contactUs
  );

  app.post(
    "/api/public/submit-rating",
    UserEp.ratingValidationRules(),
    UserEp.submitRating
  );

  app.post(
    "/api/auth/updateContactRequestStatus/:contactRequestId",
    Authentication.superAdminVerification,
    UserEp.updateContactUsStatus
  );

  app.post(
    "/api/auth/education",
    Authentication.therapistVerification,
    UserEp.addEducationalInfo
  );

  app.post(
    "/api/auth/lisence",
    Authentication.therapistVerification,
    UserEp.addLicenseInfo
  );

  app.post(
    "/api/auth/changePassword",
    UserEp.changePasswordValidationRules(),
    UserEp.changePassword
  );

  app.get(
    "/api/auth/getEducationalDetailsByUserId/:id",
    UserEp.getEducationalDetailsByUserId
  );

  app.get(
    "/api/auth/getLisenceDetailsByUserId/:id",
    UserEp.getLisenceDetailsByUserId
  );

  app.post(
    "/api/auth/editLicenseDetails",
    Authentication.therapistVerification,
    UserEp.editLicenseInfo
  );

  app.post(
    "/api/auth/editEducationalDetails",
    Authentication.therapistVerification,
    UserEp.editEducationalInfo
  );

  app.post("/api/public/verifyTherapistLogin", UserEp.therapistLoginWithEmail);

  // API for therapist login without verification
  app.post(
    "/api/public/TherapistLogin",
    UserEp.therapistLoginWithoutVerification
  );

  // not using
  app.post("/api/auth/therapistLogout", UserEp.logoutTherapist);

  app.get(
    "/api/auth/getUserByUserId/:id",
    Authentication.therapistAndClientVerification,
    UserEp.viewUserProfileById
  );

  app.get("/api/public/getUserByUserId/:id", UserEp.viewUserProfileByIdPublic);

  app.get(
    "/api/public/getUserByFirstNameLastName/:name",
    UserSearchEp.getUserByFirstNameLastName
  );

  app.get(
    "/api/auth/getUserByUserId/with/avatar/:id",
    Authentication.therapistAndClientVerification,
    UserEp.getUserDetailsWithAvatar
  );

  app.delete(
    "/api/auth/deleteEducationDetails/:id",
    Authentication.therapistVerification,
    UserEp.deleteEducationInfo
  );

  app.delete(
    "/api/auth/deleteLisenceDetails/:id",
    Authentication.therapistVerification,
    UserEp.deleteLicenseInfo
  );

  app.post("/api/auth/updateSocketId", UserEp.updateSocketId);

  app.post(
    "/api/auth/addPaymentMethod",
    UserEp.addPaymentMethodValidationRules(),
    Authentication.therapistAndClientVerification,
    UserEp.addPaymentMethod
  );

  app.post(
    "/api/auth/updateProfileImage",
    Authentication.therapistAndClientVerification,
    UserEp.updateProfileImage
  );

  app.post(
    "/api/auth/updateUserNotification",
    Authentication.therapistAndClientVerification,
    UserEp.updateUserNotification
  );

  app.post(
    "/api/auth/updateProfileCoverImage",
    Authentication.therapistAndClientVerification,
    UserEp.updateProfileCoverImage
  );

  app.post(
    "/api/auth/updateTherapistProfile",
    Authentication.therapistVerification,
    UserEp.updateTherapistProfileValidationRules(),
    UserEp.updateTherapistProfile
  );

  app.post(
    "/api/auth/updateClientProfile",
    // Authentication.clientVerification,
    UserEp.updateClientValidationRules(),
    UserEp.updateClientProfile
  );

  app.post(
    "/api/auth/updateClientPackage",
    Authentication.clientVerification,
    UserEp.updateClientPackage
  );

  app.post(
    "/api/auth/updateIncognitoMode",
    Authentication.clientVerification,
    UserEp.updateClientIncognitoModeParamValidation(),
    UserEp.updateClientIncognitoMode
  );

  app.get(
    "/api/auth/getUserIncognitoMode/:id",
    UserEp.getUserIncognitoModeById
  );

  app.post(
    "/api/auth/updateClientIncognitoPopupShow",
    Authentication.clientVerification,
    UserEp.updateClientIncognitoPopupShow
  );
  app.get("/api/auth/getAllThemeImages", UserEp.getAllThemeImages);

  app.post(
    "/api/auth/addCompleteGuide",
    Authentication.therapistAndClientVerification,
    UserEp.addCompleteGuide
  );

  app.post(
    "/api/auth/updateCallRecordingAllowed",
    Authentication.therapistAndClientVerification,
    UserEp.updateClientIncognitoModeParamValidation(),
    UserEp.updateCallRecordingAllowed
  );

  app.post(
    "/api/auth/updateHideCallTimer",
    Authentication.therapistAndClientVerification,
    UserEp.updateClientIncognitoModeParamValidation(),
    UserEp.updateHideCallTimer
  );

  // testing
  app.post("/api/public/update/profileImage", UserEp.updateProfileImageTest);
  //google calendar sync part

  app.post(
    "/api/auth/getGoogleCalendarRefreshToken",
    Authentication.therapistAndClientVerification,
    UserEp.getGoogleCalendarRefreshToken
  );
  app.post(
    "/api/auth/createGoogleCalendarEvent",
    Authentication.therapistAndClientVerification,
    UserEp.createGoogleCalendarEvent
  );
  app.post(
    "/api/auth/getGoogleCalendarActiveStatus",
    Authentication.therapistAndClientVerification,
    UserEp.getGoogleCalendarActiveStatus
  );
  app.get(
    "/api/auth/googleCalendarLogout",
    Authentication.therapistAndClientVerification,
    UserEp.googleCalendarLogout
  );
  app.post(
    "/api/auth/updateGoogleCalendarEvent",
    Authentication.therapistAndClientVerification,
    UserEp.updateGoogleCalendarEvent
  );
  app.post(
    "/api/auth/deleteGoogleCalendarEvent",
    Authentication.therapistAndClientVerification,
    UserEp.deleteGoogleCalendarEvent
  );

  app.post(
    "/api/auth/client-review",
    Authentication.clientVerification,
    UserEp.reviewValidationRules(),
    UserEp.customerReview
  );

  app.post(
    "/api/auth/get-all-approved-reviews-by-therapist/:limit/:offset",
    Authentication.clientVerification,
    UserEp.getAllTherapistApprovedReviews
  );

  app.post(
    "/api/auth/get-all-approved-reviews-by-therapistId/:limit/:offset",
    // Authentication.clientVerification,
    UserEp.getAllTherapistApprovedReviews
  );

  app.get(
    "/api/public/get-all-approved-lavni-reviews/:limit/:offset",
    UserEp.getAllApprovedLavniReviews
  );

  app.post("/api/sms", UserEp.sendReplySMS);

  app.get(
    "/api/auth/setGoogleCalendarAccessToFalse",
    Authentication.therapistAndClientVerification,
    UserEp.setGoogleCalendarAccessToFalse
  );

  app.get(
    "/api/public/getTherapistByUserIdLimited/:id",
    UserEp.viewTherapistProfileByIdPublicLimited
  );

  app.post(
    "/api/auth/updateUserInsurance",
    Authentication.clientAndAdminVerification,
    InsuranceEp.updateClientInsurance
  );

  app.post(
    "/api/auth/addSecondaryUserInsurance",
    Authentication.clientAndAdminVerification,
    InsuranceEp.addClientSecondaryInsurance
  );

  app.post(
    "/api/auth/userDetailsById",
    Authentication.clientVerification,
    InsuranceEp.getUserDetailsByUser
  );
  
  app.get("/api/public/test012", UserEp.test12345);

  app.get("/api/public/getAllExperienceTagSymptomsPublic", UserEp.getAllExperienceTagSymptomsPublic)

  app.get("/api/public/getAllStatesPublic", UserEp.getAllStatesPublic);

}
