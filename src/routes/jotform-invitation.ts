import { Express } from "express";
import { JotFormInvitationEp } from "../end-point/jotform-invitation-ep";
import { Authentication } from "../middleware/authentication";

export function initJotFormInvitationRoutes(app: Express) {
    // Send form invitation to client - therapist only route
    app.post(
        "/api/auth/jotform/invitation",
        Authentication.verifyToken,
        JotFormInvitationEp.sendFormInvitation
    );

    // Send form invitation to client - admin only route
    app.post(
        "/api/auth/admin/jotform/invitation",
        Authentication.verifyToken,
        JotFormInvitationEp.sendFormInvitationFromAdmin
    );

    // Get form invitations by client ID - protected route
    app.get(
        "/api/auth/jotform/invitations/client/:clientId",
        Authentication.verifyToken,
        JotFormInvitationEp.getFormInvitationsByClientId
    );

    // Get form invitations by therapist ID - protected route
    app.get(
        "/api/auth/jotform/invitations/therapist/:therapistId",
        Authentication.verifyToken,
        JotFormInvitationEp.getFormInvitationsByTherapistId
    );
}
