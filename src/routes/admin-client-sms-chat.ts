import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { AdminClientSmsChatEp } from "../end-point/admin-client-sms-chat-ep";

export function initAdminClientSmsChatRoutes(app: Express) {
  app.post(
    "/api/auth/clientchat/sendTwilioMessage",
    Authentication.superAdminVerification,
    AdminClientSmsChatEp.sendAdminClientChatMessage
  );

  app.post(
    "/api/auth/clientchat/twilioMessageHistory",
    Authentication.superAdminVerification,
    AdminClientSmsChatEp.getTwilioMessageHistoryWithClient
  );
}
