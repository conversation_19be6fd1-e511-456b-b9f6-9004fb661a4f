import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { ChatGroupCallEp } from "../end-point/chat-group-call-ep";

export function initChatGroupCallRoutes(app: Express) {
  app.post(
    "/api/auth/chatGroup/createChatGroupCall",
    Authentication.therapistVerification,
    ChatGroupCallEp.createChatGroupCallValidationRules(),
    ChatGroupCallEp.createChatGroupCall
  );

  app.post(
    "/api/auth/chatGroup/updateChatGroupCall",
    Authentication.therapistVerification,
    ChatGroupCallEp.updateChatGroupCallValidationRules(),
    ChatGroupCallEp.updateChatGroupCall
  );

  app.post(
    "/api/auth/chatGroup/deleteChatGroupCall",
    Authentication.therapistVerification,
    ChatGroupCallEp.deleteChatGroupCallValidationRules(),
    ChatGroupCallEp.deleteChatGroupCall
  );
}
