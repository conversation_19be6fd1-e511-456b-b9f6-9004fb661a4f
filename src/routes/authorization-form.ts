import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { AuthorizationFormEp } from "../end-point/authorization-form-ep";

export function initAuthorizationFormRoutes(app: Express) {
  app.post(
    "/api/auth/createAmeriHealthAuthForm",
    Authentication.therapistVerification,
    AuthorizationFormEp.createAmeriHealthAuthForm
  );
  app.post(
    "/api/auth/createCarolinaCompleteHealthAuthForm",
    Authentication.therapistVerification,
    AuthorizationFormEp.createCarolinaCompleteHealthAuthForm
  );
  app.post(
    "/api/auth/createUnitedHealthCareAuthForm",
    Authentication.therapistVerification,
    AuthorizationFormEp.createUnitedHealthCareAuthForm
  );
  app.post(
    "/api/auth/createHealthyBlueAuthForm",
    Authentication.therapistVerification,
    AuthorizationFormEp.createHealthyBlueAuthForm
  );
  app.post(
    "/api/auth/createWellCareAuthForm",
    Authentication.therapistVerification,
    AuthorizationFormEp.createWellCareAuthForm
  );
  app.post(
    "/api/auth/createAmbetterAuthForm",
    Authentication.therapistVerification,
    AuthorizationFormEp.createAmbetterAuthForm
  );
  app.post(
    "/api/auth/getAuthorizationFormDetailsByTherapist",
    Authentication.therapistVerification,
    AuthorizationFormEp.getAuthorizationFormDetailsByTherapist
  );
  app.post(
    "/api/auth/updateAuthorizationForm/:authFormType",
    Authentication.therapistAndAdminVerification,
    AuthorizationFormEp.updateAuthorizationForm
  );
  app.post(
    "/api/auth/getAuthorizationFormDetailsByAdmin",
    Authentication.superAdminVerification,
    AuthorizationFormEp.getAuthorizationFormDetailsByAdmin
  );
  app.get(
    "/api/auth/getAllInsuranceCompanyList",
    Authentication.therapistAndAdminVerification,
    AuthorizationFormEp.getAllInsuranceCompanyList
  );
  app.get(
    "/api/auth/getIndividualFaxInfo/:insuranceDocApprovalId",
    Authentication.therapistAndAdminVerification,
    AuthorizationFormEp.getIndividualFaxInformation
  );
  app.post(
    "/api/auth/authorizationFormSubmissionConfirmation",
    Authentication.therapistVerification,
    AuthorizationFormEp.authorizationFormSubmissionConfirmation
  );
}
