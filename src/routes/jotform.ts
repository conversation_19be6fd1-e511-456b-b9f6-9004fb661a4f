import { Express } from "express";
import { Jot<PERSON><PERSON>E<PERSON> } from "../end-point/jotform-ep";
import { Authentication } from "../middleware/authentication";

export function initJotFormRoutes(app: Express) {
    // Get enabled forms - protected route
    app.get(
        "/api/auth/jotform/enabled-forms",
        Authentication.verifyToken,
        JotFormEp.getEnabledForms
    );

    // Get submissions by client ID - protected route
    app.get(
        "/api/auth/jotform/submissions/client/:clientId",
        Authentication.verifyToken,
        JotFormEp.getJotFormByClientId
    );

    // Get submissions by client and therapist - protected route
    app.get(
        "/api/auth/getJotFormByClientIdViaTherapist/:therapistId/:clientId",
        Authentication.verifyToken,
        JotFormEp.getJotFormByClientIdViaTherapist
    );

    // Get submissions by client ID only without therapist filter - protected route
    app.get(
        "/api/auth/getJotFormByClientIdOnly/:clientId",
        Authentication.verifyToken,
        JotFormEp.getJotFormByClientIdOnly
    );

    // Update shared therapists for a submission - protected route
    app.put(
        "/api/auth/jotform/submissions/:submissionId/share",
        Authentication.verifyToken,
        JotFormEp.updateSharedTherapists
    );

    // Public route for generating PDF
    app.get(
        "/api/jotform/generatePDF/:formId/:submissionId",
        JotFormEp.generatePDF
    );
    
    // Admin-only route for getting all JotForm submissions
    app.get(
        "/api/auth/admin/getAllJotForms",
        Authentication.verifyToken,
        JotFormEp.getAllJotFormsAdmin
    );
}
