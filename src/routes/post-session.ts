import { Express, Router } from "express";
import { PostSessionEndpoint } from "../end-point/post-session-ep";
import { Authentication } from "../middleware/authentication";

const router = Router();

// Endpoint to submit post-session information
router.post("/submit-post-session", Authentication.therapistVerification, PostSessionEndpoint.submitPostSession);

// Thêm các endpoint cụ thể trước các endpoint động
router.get(
  "/getAllSessionTherapistFeedbackNeedCall/:limit/:offset",
  Authentication.superAdminVerification,
  PostSessionEndpoint.getAllTherapistPostSessionsNeedCall
);

router.get(
  "/getAllSessionTherapistFeedbackNeedCallCount",
  Authentication.superAdminVerification,
  PostSessionEndpoint.getTherapistPostSessionsNeedCallCount
);

router.put(
  "/updateSessionTherapistFeedbackIsRead/:id",
  Authentication.superAdminVerification,
  PostSessionEndpoint.updateTherapistPostSessionIsRead
);

// Endpoint to get post-session information by meetingId
router.get("/meeting/:meetingId", Authentication.therapistVerification, PostSessionEndpoint.getPostSessionByMeetingId);

// Endpoint to get all post-session information by therapistId
router.get("/therapist/:therapistId", Authentication.therapistVerification, PostSessionEndpoint.getPostSessionsByTherapistId);

// Endpoint to get post-session information by ID - placed at the end to avoid route conflicts
router.get("/:id", Authentication.therapistVerification, PostSessionEndpoint.getPostSessionById);

export function initPostSessionRoutes(app: Express) {
  app.use("/api/auth", router);
}

export default router;
