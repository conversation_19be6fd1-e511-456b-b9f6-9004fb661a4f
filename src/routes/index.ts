import { Express, Request, Response } from "express";
import { initAdminRoutes } from "./admin";
import { initAppointmentRoutes } from "./appointment";
import { initArticleRoutes } from "./article";
import { initGroupTherapySessionRoutes } from "./group-therapy";
import { initClientRoutes } from "./client";
import { initReportRoutes } from "./report";
import { initTherapistRoutes } from "./therapist";
import { initUploadRoutes } from "./upload";
import { initUserRoutes } from "./user";
import { initVideoChatRoutes } from "./video-chat";
import { initHomeworkAndGoalRoutes } from "./homework-and-goal";
import { initRequestRoutes } from "./friend-request";
import { initNotificationRoutes } from "./notification";
import { initTransactionsRoutes } from "./transactions";
import { initReferralEarningsRoutes } from "./referral-earning";
import { initClientRewardRoutes } from "./client-reward";
import { initFeedbackRoutes } from "./feedback";
import { initInsuranceRoutes } from "./insurance";
import { initGroupChatRoutes } from "./group-chat";
import { initInvoiceRoutes } from "./invoice";
import { initDocumentRoutes } from "./document";
import { initMedicalPhraseRoutes } from "./medical-phrase";
import { initChatRoutes } from "./chat";
import { initZoomVideoCallRoutes } from "./zoom-video-call";
import { initInstagramFeedRoutes } from "./instagram-feed";
import { initVonageCallRoutes } from "./vonage-call";
import { initChatGroupRoutes } from "./chat-group";
import { initChatGroupCallRoutes } from "./chat-group-call";
import { initVonageCallGroupRoutes } from "./vonage-call-group";
import { initClinicalAssestmentRoutes } from "./clinicalAssestment";
import { initTherapyPlanRoutes } from "./therapyPlan";
import { initTwilioCallRoutes } from "./twilio-call";
import { initSmsChatRoutes } from "./sms-chat";
import { initAdminClientSmsChatRoutes } from "./admin-client-sms-chat";
import { initTechTicketRoutes } from "./tech-ticket";
import { initSopePieRoutes } from "./soap-pie";
import { initAuthorizationFormRoutes } from "./authorization-form";
import { initFormVersionRoutes } from "./form-version";
import { initVonageNativeSdkCallRoutes } from "./vonage-native-call";
import { initDigitalAssessmentRoutes } from "./digital-assessment-forms";
import { initSaasTherapistRoutes } from "./saas-therapist";
import { initJotFormRoutes } from "./jotform";
import { initPaymentRoutes } from "./payment";
import { initAdminRoutesExtra } from "./admin-routes";
import { initMetricsRoutes } from "./metrics";
import { initPostSessionRoutes } from "./post-session";
import { initJotFormInvitationRoutes } from "./jotform-invitation";

export function initRoutes(app: Express) {
  /* TOP LEVEL */
  app.get("/api", (req: Request, res: Response) =>
    res.sendSuccess("Lavni™ Api", "Success")
  );

  initAdminRoutes(app);
  initUploadRoutes(app);
  initClientRoutes(app);
  initTherapistRoutes(app);
  initUserRoutes(app);
  initArticleRoutes(app);
  initGroupTherapySessionRoutes(app);
  initReportRoutes(app);
  initAppointmentRoutes(app);
  initVideoChatRoutes(app);
  initHomeworkAndGoalRoutes(app);
  initRequestRoutes(app);
  initNotificationRoutes(app);
  initTransactionsRoutes(app);
  initReferralEarningsRoutes(app);
  initClientRewardRoutes(app);
  initFeedbackRoutes(app);
  initInsuranceRoutes(app);
  initGroupChatRoutes(app);
  initInvoiceRoutes(app);
  initDocumentRoutes(app);
  initMedicalPhraseRoutes(app);
  initChatRoutes(app);
  initZoomVideoCallRoutes(app);
  initInstagramFeedRoutes(app);
  initVonageCallRoutes(app);
  initChatGroupRoutes(app);
  initChatGroupCallRoutes(app);
  initVonageCallGroupRoutes(app);
  initClinicalAssestmentRoutes(app);
  initTherapyPlanRoutes(app);
  initTwilioCallRoutes(app);
  initSmsChatRoutes(app);
  initAdminClientSmsChatRoutes(app);
  initTechTicketRoutes(app);
  initSopePieRoutes(app);
  initAuthorizationFormRoutes(app);
  initFormVersionRoutes(app);
  initVonageNativeSdkCallRoutes(app);
  initPaymentRoutes(app);
  initDigitalAssessmentRoutes(app);
  initSaasTherapistRoutes(app);
  initJotFormRoutes(app);
  initJotFormInvitationRoutes(app);
  initAdminRoutesExtra(app);
  initMetricsRoutes(app);
  initPostSessionRoutes(app);

  /* ALL INVALID REQUESTS */
  app.get("/", (req: Request, res: Response) => res.redirect(301, "/api"));
  app.all("*", (req: Request, res: Response) =>
    res.sendError("Route Not Found")
  );
}
