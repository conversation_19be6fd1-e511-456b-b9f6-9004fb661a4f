import { Express } from "express";
import { TrainingDocumentEp } from "../end-point/training-document-ep";
import { Authentication } from "../middleware/authentication";

export function initDocumentRoutes(app: Express) {
  app.post(
    "/api/auth/add-training-doc",
    Authentication.therapistVerification,
    TrainingDocumentEp.createTrainingDocumentsAndVideo
  );
  app.post(
    "/api/auth/update-training-doc",
    TrainingDocumentEp.updateTrainingDocumentsAndVideo
  );
  app.get(
    "/api/auth/get-all-training-doc/:limit/:offset",
    Authentication.therapistAndClientVerification,
    TrainingDocumentEp.getAllTrainingDocuments
  );
  app.post(
    "/api/auth/search-documents/:limit?/:offset?",
    TrainingDocumentEp.searchDocuments
  ); // this is common route for client and therapist...
  app.delete(
    "/api/auth/delete-training-document-by-id/:tId",
    TrainingDocumentEp.deleteTrainingDocument
  );
  app.get(
    "/api/auth/get-training-document-by-id/:tId",
    TrainingDocumentEp.getTrainingDocument
  );
  app.get(
    "/api/auth/get-all-training-doc-admin",
    Authentication.superAdminVerification,
    TrainingDocumentEp.getAllTrainingDocuments
  );
  app.post(
    "/api/auth/search-documents-admin/:limit?/:offset?",
    Authentication.superAdminVerification,
    TrainingDocumentEp.searchDocumentsAdmin
  );
  app.post(
    "/api/public/createTrainingDocumentFolder",
    TrainingDocumentEp.createTrainingDocumentFolderValidation(),
    TrainingDocumentEp.createTrainingDocumentFolder
  );
  app.post(
    "/api/public/createTrainingDocumentFolderFile",
    TrainingDocumentEp.createTrainingDocumentFolderFileValidation(),
    TrainingDocumentEp.createTrainingDocumentFolderFile
  );
  app.get(
    "/api/public/getTrainingDocumentFoldersAndFiles/:parentFolderId?",
    TrainingDocumentEp.getTrainingDocumentFoldersAndFiles
  );
  app.post(
    "/api/public/generatePresignedURLForAccessTrainingFiles",
    TrainingDocumentEp.generatePresignedURLForStoreFilesInAWSBucket
  );
  app.post(
    "/api/public/generatePresignedURLForGetFiles",
    TrainingDocumentEp.generatePresignedURLForGetFiles
  );

  app.post(
    "/api/auth/deleteFile",
    Authentication.therapistAndAdminVerification,
    TrainingDocumentEp.deleteFileFolderTrainingDocument
  );

  app.post(
    "/api/auth/deleteFolder",
    Authentication.therapistAndAdminVerification,
    TrainingDocumentEp.deleteFoldersTrainingDocument
  );

  app.post(
    "/api/auth/renameFile",
    Authentication.therapistAndAdminVerification,
    TrainingDocumentEp.renameFile
  );

  app.post(
    "/api/auth/renameFolder",
    Authentication.therapistAndAdminVerification,
    TrainingDocumentEp.renameFolder
  );

  app.post(
    "/api/auth/searchFilesFolders",
    Authentication.therapistAndAdminVerification,
    TrainingDocumentEp.searchFileFolders
  );

  app.post(
    "/api/auth/filterTherapistsDocumentAccessByAdmin/:limit?/:offset?",
    Authentication.superAdminVerification,
    TrainingDocumentEp.filterTherapistsDocAccessByAdmin
  );

  app.post(
    "/api/auth/editUserFolderDocumentPermission",
    Authentication.superAdminVerification,
    TrainingDocumentEp.allowAccessToTherapistToCreateFolders
  );
}
