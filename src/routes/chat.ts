import { Express } from "express";
import multer = require("multer");
import { ChatEp } from "../end-point/chat-ep";
import { Authentication } from "../middleware/authentication";

const uploadPost = multer({ dest: "uploads/ATTACHMENTS" }).single("attachFile");

export function initChatRoutes(app: Express) {
  app.get(
    "/api/auth/chat/getUsers",
    Authentication.therapistAndClientVerification,
    ChatEp.getAllUsers
  );

  app.post(
    "/api/auth/chat/allUserChats",
    Authentication.therapistAndClientVerification,
    ChatEp.getAllUserChatsValidationRules(),
    ChatEp.getAllUserChats
  );

  app.get(
    "/api/auth/chat/getMessages/:chatId/:limit/:offset",
    Authentication.therapistAndClientVerification,
    ChatEp.getAllMeeagesByChatId
  );

  app.get(
    "/api/auth/chat/markAsReadChatMessages/:chatId",
    Authentication.therapistAndClientVerification,
    ChatEp.MarkAsReadMessagesByChatId
  );

  // app.post(
  //   "/api/auth/chat/createChat",
  //   Authentication.therapistAndClientVerification,
  //   ChatEp.createChat
  // );

  // app.post(
  //   "/api/auth/chat/createMessage",
  //   Authentication.therapistAndClientVerification,
  //   ChatEp.createMessage
  // );

  // app.post(
  //   "/api/auth/chat/createMessageWithAttachment",
  //   Authentication.therapistAndClientVerification,
  //   uploadPost,
  //   ChatEp.uploadAttachmentFileAndCreateMessage
  // );

  app.get(
    "/api/auth/chat/deleteMessage/:messageId",
    Authentication.therapistAndClientVerification,
    ChatEp.deleteMessageByMessageId
  );

  app.post(
    "/api/auth/search/chat",
    Authentication.therapistAndClientVerification,
    ChatEp.searchAndBrowseChatsValidationRules(),
    ChatEp.searchAndBrowseChats
  );

  app.get(
    "/api/auth/chat/getUserUnreadChatCount/:userId",
    Authentication.allUserVerification,
    ChatEp.getUserUnreadChatCount
  );

  app.get(
    "/api/auth/chat/getAllUnreadMessagesFromUserIdForDashboard/:userId",
    Authentication.therapistAndClientVerification,
    ChatEp.getAllUnreadMessagesFromUserId
  );

  app.get(
    "/api/auth/chat/getAllUnreadChatsUserIdForDashboard/:userId",
    Authentication.therapistAndClientVerification,
    ChatEp.getAllUnreadChatsFromUserId
  );

  app.get(
    "/api/auth/chat/getChatByChatIdForDashboard/:userId/:chatId",
    Authentication.therapistAndClientVerification,
    ChatEp.getChatByChatIdForDashboard
  );

  app.get(
    "/api/auth/chat/getUnreadMessageCountByChatId/:userId/:chatId",
    Authentication.therapistAndClientVerification,
    ChatEp.getUnreadMessageCountByChatId
  );

  app.post(
    "/api/auth/chat/updateMeetingTherapistTranscribeAllowed",
    Authentication.therapistVerification,
    ChatEp.updateMeetingTherapistTranscribeAllowed
  );

  //admin routes
  /*   app.get(
    "/api/auth/chat/admin/allUserChats",
    Authentication.therapistAndClientVerification,
    ChatEp.adminGetAllUserChats
  );

  app.post(
    "/api/auth/admin/search/chat",
    Authentication.therapistAndClientVerification,
    Authentication.adminUserORSuperAdminUserVerification,
    ChatEp.searchAndBrowseChatsValidationRules(),
    ChatEp.searchAndBrowseChatsForAdmin
  ); */
}
