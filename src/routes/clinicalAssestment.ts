import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { ClinicalAssestmentEp } from "../end-point/cllinical-assestment-ep";


export function initClinicalAssestmentRoutes(app: Express) {
  app.post(
    "/api/auth/addClinicalAssestmentDetails",
    Authentication.therapistVerification,
    ClinicalAssestmentEp.addClinicalAssestmentDetails
  );

  app.post(
    "/api/auth/updateClinicalAssestmentDetails",
    Authentication.therapistAndAdminVerification,
    ClinicalAssestmentEp.updateClinicalAssestmentDetails
  );

  app.post(
    "/api/auth/getClinicalAssestmentDetails",
    Authentication.therapistAndAdminVerification,
    ClinicalAssestmentEp.getClinicalAssesment
  );

  app.post(
    "/api/public/update-assesment-signature",
    ClinicalAssestmentEp.updateClientAssesmentSignature
  );

  app.post(
    "/api/auth/getClinicalAssestmentDetailsWithSessionData",
    Authentication.therapistVerification,
    ClinicalAssestmentEp.getClinicalAssestmentDetailsWithSessionData
  );

  app.post(
    "/api/auth/getClinicalAssestmentDetailsForDownload",
    Authentication.therapistAndAdminVerification,
    ClinicalAssestmentEp.getClinicalAssestmentDetailsForDownload
  );
}