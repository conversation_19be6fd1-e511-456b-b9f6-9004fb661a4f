import { Express } from "express";
import { FeedbackEp } from "../end-point/Feedback-ep";
import { Authentication } from "../middleware/authentication";

export function initFeedbackRoutes(app: Express) {
  app.post("/api/auth/addFeedback", Authentication.therapistAndClientVerification, FeedbackEp.adddFeedback);

  app.post(
    "/api/auth/getAllFeedbacks/:limit/:offset",
    Authentication.superAdminVerification,
    FeedbackEp.getAllFeedbacks
  );

  app.post("/api/auth/updateFeedbackStatus/:feedbackId", Authentication.superAdminVerification, FeedbackEp.updateFeedbackStatus);

  app.post(
    "/api/auth/addSessionFeedback",
    Authentication.therapistAndClientVerification,
    FeedbackEp.saveSessionFeedback
  );

  app.get(
    "/api/auth/getAllSessionFeedback/:limit/:offset",
    Authentication.superAdminVerification,
    FeedbackEp.getAllSessionFeedbacks
  );

  app.get(
    "/api/auth/getAllSessionFeedbackNeedCall/:limit/:offset",
    Authentication.superAdminVerification,
    FeedbackEp.getAllSessionFeedbacksNeedCall
  );

  app.put(
    "/api/auth/updateSessionFeedbackIsRead/:id",
    Authentication.superAdminVerification,
    FeedbackEp.updateSessionFeedbackIsRead
  );

  app.get(
    "/api/auth/getAllSessionFeedbackNeedCallCount",
    Authentication.superAdminVerification,
    FeedbackEp.getSessionFeedbacksNeedCallCount
  );
}
