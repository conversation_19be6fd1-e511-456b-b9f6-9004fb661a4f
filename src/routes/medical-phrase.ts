import { Express } from "express";
import { MedicalPhraseEp } from "../end-point/medical-phrase-ep";
import { Authentication } from "../middleware/authentication";

export function initMedicalPhraseRoutes(app: Express) {
  app.post(
    "/api/auth/createPhrase",
    Authentication.therapistVerification,
    MedicalPhraseEp.createMedicalPhraseValidationRules(),
    MedicalPhraseEp.createMedicalPhrase
  );

  app.post(
    "/api/auth/editPhrase",
    Authentication.therapistVerification,
    MedicalPhraseEp.editMedicalPhraseValidationRules(),
    MedicalPhraseEp.editMedicalPhrase
  );

  app.get(
    "/api/auth/getAllPhrases/:stage",
    Authentication.therapistAndAdminVerification,
    MedicalPhraseEp.getAllMedicalPhrasesByStage
  );
}
