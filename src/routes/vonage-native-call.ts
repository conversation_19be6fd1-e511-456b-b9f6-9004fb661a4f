import { Express } from "express";
import { VonageCallEp } from "../end-point/vonage-call-ep";
import { Authentication } from "../middleware/authentication";
import { VonageNativeCallEp } from "../end-point/vonage-native-call-ep";

export function initVonageNativeSdkCallRoutes(app: Express) {
    app.get(
        "/api/public/generate-vonage-session/:name",
        VonageCallEp.createVonageSession
    )

    app.get(
        "/api/public/meeting/:id",
        VonageCallEp.getMeetingByMeetingIdForVonage
    )

    app.get(
        "/api/auth/startArchiveVonageNative/:sessionId",
        Authentication.therapistVerification,
        VonageNativeCallEp.startArchiveVonageNativeSdkCall
    )

    app.get(
        "/api/auth/stopArchiveVonageNative/:sessionName",
        Authentication.therapistVerification,
        VonageNativeCallEp.stopArchiveVonageNativeSdkCall
    )

    app.post(
        "/api/auth/initiate-vonage-native-call",
        VonageNativeCallEp.initializeVonageVideoCallValidation(),
        VonageNativeCallEp.initializeVonageNativeVideoCall
    )

    app.post(
        "/api/public/join-vonage-native-call",
        VonageNativeCallEp.joinVonageNativeVideoCallValidation(),
        VonageNativeCallEp.joinVonageNativeVideoCall
    )

    app.put(
        "/api/public/update-session-id/:roomName",
        VonageCallEp.updateSessionIdByRoomName
    );

    app.post(
        "/api/public/cancel-vonage-native-call",
        // VonageNativeCallEp.joinVonageNativeVideoCallValidation(),
        VonageNativeCallEp.cancelVonageNativeCall
    );

    app.post(
        "/api/auth/update-user-FCM-token",
        Authentication.therapistAndClientVerification,
        VonageNativeCallEp.updateUserFCMToken
    );

    app.get(
        "/api/auth/get-all-ongoing-meetings-by-user-id",
        Authentication.therapistAndClientVerification,
        VonageNativeCallEp.getAllOngoingMeetingsByUserId
    );

    app.post(
        "/api/auth/captions/start",
        Authentication.therapistVerification,
        VonageNativeCallEp.startCaptionsVonageNativeSDK
    );

    app.post(
        "/api/auth/captions/:captionsId/stop",
        Authentication.therapistVerification,
        VonageNativeCallEp.stopCaptionsVonageNativeSDK
    );

    app.post(
        "/api/public/update-transcribes",
        VonageNativeCallEp.updateTranscribesWhenCallEnds
    );
}