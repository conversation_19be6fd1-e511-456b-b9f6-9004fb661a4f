import {Express} from "express";
import {AdminEp} from "../end-point/admin-ep";
import {AdminStatisticsEp} from "../end-point/admin-statistics-ep";
import {AppointmentEp} from "../end-point/appointment-ep";
import {FriendRequestEp} from "../end-point/friend-request-ep";
import {TherapistEp} from "../end-point/therapist-ep";
import {TransactionsEp} from "../end-point/transactions-ep";
import {UserEp} from "../end-point/user-ep";
import {Authentication} from "../middleware/authentication";
import {verifyActiveSubscriptionAdmin2, verifyActiveSubscriptionAdminDelete,} from "../middleware/check-subscription";
import {InsuranceEp} from "../end-point/insurance-ep";
import {ClinicalAssestmentEp} from "../end-point/cllinical-assestment-ep";
import {TherapyPlanEp} from "../end-point/therapy-plan-ep";

export function initAdminRoutes(app: Express) {
    app.post(
        "/api/auth/create/ethnicity",
        Authentication.superAdminVerification,
        AdminEp.addEthnicityValidationRules(),
        AdminEp.addEthnicity
    );

    app.post(
        "/api/auth/updateEthnicity",
        Authentication.superAdminVerification,
        AdminEp.updateEthnicityValidationRules(),
        AdminEp.updateEthnicity
    );

    app.get("/api/public/getAllEthnicityTypes", AdminEp.getAllEthnicityTypes_);
    app.get(
        "/api/public/getEthnicityTypes/:limit?/:offset?",
        AdminEp.getAllEthnicityTypes
    );

    app.post("/api/auth/create/experienceTag", AdminEp.addExperienceTags);

    app.get(
        "/api/auth/getExperienceTags/:limit?/:offset?",
        AdminEp.getAllExperienceTags
    );
    app.get(
        "/api/public/getPublicExperienceTags/:limit?/:offset?",
        AdminEp.getAllPublicExperienceTags
    );

    app.post(
        "/api/auth/create/profession",
        Authentication.superAdminVerification,
        AdminEp.addProfession
    );

    app.post(
        "/api/auth/update/profession/:professionId",
        Authentication.superAdminVerification,
        AdminEp.updateProfession
    );

    app.get(
        "/api/auth/getProfessions/:limit?/:offset?",
        AdminEp.getAllProfessions
    );

    app.get("/api/public/getProfessions", AdminEp.getAllProfessionsPublic);

    app.get("/api/auth/get_profession/:professionId", AdminEp.getProfessionById);

    app.post(
        "/api/auth/create/professionLicense",
        Authentication.superAdminVerification,
        AdminEp.addProfessionLicense
    );

    app.get("/api/auth/getProfessionLicense", AdminEp.getAllProfessionLicenses);

    app.get(
        "/api/auth/get_profession_license/:professionLicenseId",
        AdminEp.getProfessionLicenseById
    );

    app.post("/api/auth/create/hashTag", AdminEp.addHashTags);

    app.get("/api/auth/getAllHashTags/:limit?/:offset?", AdminEp.getAllHashTags);

    app.get(
        "/api/public/getAllPublicHashTags/:limit?/:offset?",
        AdminEp.getAllPublicHashTags
    );

    app.get(
        "/api/auth/getAllPendingClients/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllPendingClients
    );

    app.get(
        "/api/auth/getAllApprovedClients/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllApprovedClients
    );

    app.post(
        "/api/auth/approveRejectClient",
        Authentication.superAdminVerification,
        AdminEp.approveRejectClient
    );

    app.get(
        "/api/auth/getAllPendingTherapists/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllPendingTherapists
    );

    app.get(
        "/api/auth/getAllApprovedTherapists/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllApprovedTherapists
    );

    app.post(
        "/api/auth/approveRejectTherapist",
        Authentication.superAdminVerification,
        AdminEp.approveRejectTherapist
    );

    app.post("/api/auth/updateThemeImage", AdminEp.updateThemeImage);

    app.get(
        "/api/auth/getAllClients/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllClients
    );

    app.get(
        "/api/auth/get-all-client-state",
        Authentication.superAdminVerification,
        UserEp.getAllClientStates
    );

    app.get(
        "/api/auth/get-all-client-insurance-company",
        Authentication.superAdminVerification,
        AdminEp.getAllClientInsuranceCompanies
    );

    app.post(
        "/api/auth/searchClientsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchClientsByAdmin
    );

    // New simple API for searching clients by name
    app.post(
        "/api/auth/searchClientsByName/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchClientsByName
    );

    app.post(
        "/api/auth/getRecentSessionsForClientByAdmin/:clientId/:therapistId",
        Authentication.superAdminVerification,
        AdminEp.getRecentSessionsForClientByAdmin
    );

    app.get(
        "/api/auth/getAllTherapists/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllTherapists
    );

    app.get(
        "/api/auth/getAllTherapistsSimple",
        Authentication.superAdminVerification,
        AdminEp.getAllTherapistsSimple
    );

    app.get(
        "/api/public/getAllTherapists/:limit?/:offset?",
        AdminEp.getAllTherapistsPublic
    );

    app.post(
        "/api/auth/searchTherapistsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchTherapistsByAdmin
    );

    app.post(
        "/api/auth/searchAllTherapistsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchAllTherapistsByAdmin
    );

    app.post(
        "/api/auth/searchAllReferralEarningsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchAllReferralEarningsByAdmin
    );

    app.post(
        "/api/auth/searchAllClientRewardsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchAllClientRewardsByAdmin
    );

    app.get(
        "/api/auth/viewProfileByUserId/:userId",
        Authentication.superAdminVerification,
        AdminEp.viewUserProfileById
    );

    app.get(
        "/api/auth/getAllReportReviews/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllReportReviews
    );

    app.get(
        "/api/auth/getAllContactUsRequests/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllContactUsRequests
    );

    app.get(
        "/api/auth/getTherapistByProfession/:pId/:limit/:offset",
        Authentication.superAdminVerification,
        TherapistEp.getTherapistByProfessionId
    );

    app.get(
        "/api/auth/getAllPendingEducationalDetails/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllPendingEducationalDetails
    );

    app.get(
        "/api/auth/getAllApprovedEducationalDetails/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllApprovedEducationalDetails
    );

    app.get(
        "/api/auth/getAllPendingLicenseDetails/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllPendingLicenseDetails
    );

    app.get(
        "/api/auth/getAllApprovedLicenseDetails/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllApprovedLicenseDetails
    );

    app.get(
        "/api/auth/getAllPremiumClients/:limit/:offset",
        Authentication.superAdminVerification,
        AdminEp.getAllPremiumClients
    );

    app.get(
        "/api/auth/getAllPremiumMembershipRevokedClients/:limit/:offset",
        Authentication.superAdminVerification,
        AdminEp.getAllPremiumMembershipRevokedClients
    );

    app.get(
        "/api/auth/getAllPremiumAndRevokedClients",
        Authentication.superAdminVerification,
        AdminEp.getAllPremiumAndRevokedClients
    );

    app.get(
        "/api/auth/getNoteListByClientId/:clientId/:limit/:offset",
        Authentication.superAdminVerification,
        AdminEp.getDiagnosisNotesByClientId
    );

    app.post(
        "/api/auth/blockUserByAdmin",
        Authentication.superAdminVerification,
        AdminEp.blockUserByAdmin
    );

    app.post(
        "/api/auth/unblockUserByAdmin",
        Authentication.superAdminVerification,
        AdminEp.unblockUserByAdmin
    );

    app.post(
        "/api/auth/toggleEducationalDetailsReviewStatus/:educationDetailsId",
        Authentication.superAdminVerification,
        AdminEp.toggleEducationalDetailsReviewStatus
    );

    app.post(
        "/api/auth/toggleLicenseDetailsReviewStatus/:licenseId",
        Authentication.superAdminVerification,
        AdminEp.toggleLicenseDetailsReviewStatus
    );

    app.post(
        "/api/auth/updateExperienceTags",
        AdminEp.updateExpTagValidationRules(),
        AdminEp.updateExperienceTags
    );

    app.post(
        "/api/auth/updateHashTags",
        AdminEp.updateHashTagValidationRules(),
        AdminEp.updateHashTags
    );

    app.post(
        "/api/auth/togglePremiumMembership/:clientId",
        Authentication.superAdminVerification,
        AdminEp.togglePremiumMembership
    );

    app.post(
        "/api/auth/chargePaymentAndSendEmail",
        AdminEp.chargePaymentAndSendEmailValidationRules(),
        AdminEp.chargePaymentAndSendEmail
    );

    app.get(
        "/api/auth/getAllArticledetails/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllArticles
    );

    app.delete(
        "/api/auth/deleteArticleById/:articleId",
        Authentication.superAdminVerification,
        AdminEp.deleteArticleById
    );

    app.delete(
        "/api/auth/deleteEthnicity/:deleteEthnicityId",
        Authentication.superAdminVerification,
        AdminEp.deleteEthnicity
    );

    app.delete(
        "/api/auth/deleteExperienceTag/:deleteExperienceTagId",
        Authentication.superAdminVerification,
        AdminEp.deleteExperienceTag
    );

    app.delete(
        "/api/auth/deleteHashTag/:deleteHashTagId",
        Authentication.superAdminVerification,
        AdminEp.deleteHashTag
    );

    app.delete(
        "/api/auth/deleteProfessionLicense/:deleteProfessionLicenseId",
        Authentication.superAdminVerification,
        AdminEp.deleteProfessionLicense
    );

    app.delete(
        "/api/auth/deleteProfession/:deleteProfessionId",
        Authentication.superAdminVerification,
        AdminEp.deleteProfession
    );

    app.delete(
        "/api/auth/deleteThemeImage/:imageId",
        Authentication.superAdminVerification,
        AdminEp.deleteThemeImage
    );

    app.delete(
        "/api/auth/deleteUser/:userId",
        Authentication.superAdminVerification,
        AdminEp.deleteUser
    );

    app.get(
        "/api/auth/getAllMeetingsForTherapistForTranscribeByAdmin/:therapistId/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllMeetingsForSpecificTherapistForTranscribeByTherapistId
    );

    app.post(
        "/api/auth/getAllMeetingsByTherapist/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllMeetingsByTherapist
    );

    app.post(
        "/api/auth/sendMarketingEmail1",
        Authentication.superAdminVerification,
        AdminEp.sendMarketingEmail1
    );

    app.delete(
        "/api/auth/deleteMarketingEmails/:marketingEmailId",
        Authentication.superAdminVerification,
        AdminEp.deleteMarketingEmailLists
    );

    app.post(
        "/api/auth/saveMarketingEmails",
        Authentication.superAdminVerification,
        AdminEp.sendMarketingEmailList
    );

    app.get(
        "/api/auth/getAllMarketingEmails/:limit?/:offset?",
        AdminEp.getAllMarketingEmailLists
    );

    app.post(
        "/api/auth/create/insuranceCompany",
        // AdminEp.addInsuranceCompanyValidationRules(),
        AdminEp.addInsuranceCompany
    );

    app.post(
        "/api/auth/updateInsuranceCompany",
        Authentication.superAdminVerification,
        AdminEp.updateInsuranceCompanyValidationRules(),
        AdminEp.updateInsuranceCompany
    );

    app.delete(
        "/api/auth/deleteInsuranceCompany/:deleteInsuranceCompanyId",
        Authentication.superAdminVerification,
        AdminEp.deleteInsuranceCompany
    );

    app.get(
        "/api/auth/getInsuranceCompanies/:limit?/:offset?",
        AdminEp.getAllInsuranceCompanies
    );

    app.get(
        "/api/public/getInsuranceCompanies",
        AdminEp.getAllInsuranceCompaniesPublic
    );

    app.get(
        "/api/auth/getInsuranceById/:insuranceCompanyId",
        AdminEp.getInsuranceById
    );

    app.post(
        "/api/auth/createAppointmentByAdmin",
        Authentication.superAdminVerification,
        AppointmentEp.createAppointmentValidationRules(),
        verifyActiveSubscriptionAdmin2(),
        AppointmentEp.createAppointmentByAdmin
    );

    app.get(
        "/api/auth/viewSingleAppointmentAdmin/:id",
        Authentication.superAdminVerification,
        AppointmentEp.viewSingleAppointmentAdmin
    );

    app.delete(
        "/api/auth/deleteAppointmentAdmin/:id",
        Authentication.superAdminVerification,
        verifyActiveSubscriptionAdminDelete(),
        AppointmentEp.deleteAppointmentAdmin
    );

    app.get(
        "/api/auth/viewAppointmentsByTherapistAdmin/:therapistId/:limit?",
        Authentication.superAdminVerification,
        AppointmentEp.viewAppointmentsByTherapistAdmin
    );

    app.post(
        "/api/auth/getAllAppointmentsAndDetailsByUserIdAdmin",
        Authentication.superAdminVerification,
        AppointmentEp.getAllAppointmentsAndDetailsByUserIdAdmin
    );

    app.get(
        "/api/auth/viewAllAppointmentsByTherapistIdAdmin/:id",
        Authentication.superAdminVerification,
        AppointmentEp.viewAllAppointmentsByTherapistIdAdmin
    );

    app.get(
        "/api/auth/getUserByUserIdAdmin/:therapistId/:clientId",
        Authentication.superAdminVerification,
        UserEp.viewUserProfileByIdAdmin
    );

    app.get(
        "/api/auth/checkIfUserIsFriendAdmin/:clientId/:therapistId",
        Authentication.superAdminVerification,
        FriendRequestEp.checkIfUserIsFriendAdmin
    );

    app.get(
        "/api/auth/viewAllFriendsByTherapistAdmin/:therapistId",
        Authentication.superAdminVerification,
        FriendRequestEp.viewAllRequestsByTherapistAdmin
    );

    app.delete(
        "/api/auth/deleteMeetingsAndRecordings/:meetingId",
        Authentication.superAdminVerification,
        AdminEp.deleteMeetingsAndRecordings
    );

    app.post(
        "/api/auth/searchMeetingsAndRecordingsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchMeetingsAndRecordingsByAdmin
    );

    app.get(
        "/api/auth/getAllMeetingsAndRecordings/:limit/:offset",
        Authentication.superAdminVerification,
        AdminEp.getAllMeetingsAndRecords
    );

    app.get(
        "/api/auth/get-user-counts",
        Authentication.superAdminVerification,
        AdminStatisticsEp.getTotalUsers
    );

    app.get(
        "/api/auth/get-all-appointment-statistics",
        Authentication.superAdminVerification,
        AdminStatisticsEp.getAllAppointmentStatisctics
    );

    app.post(
        "/api/auth/get-all-admin-statistics",
        Authentication.superAdminVerification,
        AdminStatisticsEp.getAllAdminStatistics
    );

    app.get(
        "/api/auth/get-all-subscriptions",
        Authentication.superAdminVerification,
        AdminStatisticsEp.getAllSubscriptionCounts
    );

    app.get(
        "/api/auth/get-all-lavni-pending-reviews/:limit/:offset",
        Authentication.superAdminVerification,
        AdminEp.getAllLavniPendingReviews
    );

    app.get(
        "/api/auth/get-all-lavni-approved-reviews/:limit/:offset",
        Authentication.superAdminVerification,
        AdminEp.getAllLavniApprovedReviews
    );

    app.post(
        "/api/auth/update-lavni-review",
        Authentication.superAdminVerification,
        AdminEp.updateLavniReview
    );

    app.get(
        "/api/auth/get-all-therapist-pending-reviews/:limit/:offset",
        Authentication.superAdminVerification,
        AdminEp.getAllTherapistPendingReviews
    );

    app.get(
        "/api/auth/get-all-therapists-soap-reviews/:limit/:offset",
        Authentication.superAdminVerification,
        AdminEp.getAllTherapistsSOAPReviews
    );

    app.get(
        "/api/auth/get-all-therapist-approved-reviews/:limit/:offset",
        Authentication.superAdminVerification,
        AdminEp.getAllTherapistApprovedReviews
    );

    app.post(
        "/api/auth/update-customer-review",
        Authentication.superAdminVerification,
        AdminEp.updateCustomerReview
    );

    app.post(
        "/api/auth/create-user",
        Authentication.superAdminVerification,
        AdminStatisticsEp.adminCreateUser
    );

    app.post(
        "/api/auth/update-first-time-login",
        Authentication.superAdminVerification,
        AdminEp.updateFirstTimeLogin
    );

    app.post(
        "/api/auth/update-user",
        Authentication.superAdminVerification,
        AdminEp.updateUserByAdmin
    );

    app.get(
        "/api/auth/getAllAdminUsers/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllAdminUsers
    );

    app.post(
        "/api/auth/geta-all-missed-appointments/:userId",
        Authentication.superAdminVerification,
        AdminStatisticsEp.getAllMissedAppointmentsCounts
    );

    app.post(
        "/api/auth/changeUserPasswordAdmin/:userId",
        UserEp.changePasswordValidationRulesAdmim(),
        Authentication.superAdminVerification,
        AdminStatisticsEp.changePasswordByAdmin
    );

    app.post(
        "/api/auth/searchTherapistsByAdminStatistic/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchTherapistsByAdminStatistic
    );

    app.post(
        "/api/auth/searchClientsByAdminStatistic/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchClientsByAdminStatistic
    );

    app.post(
        "/api/auth/getAllClaims/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllClaimsByAdmin
    );

    app.post(
        "/api/auth/getAllClinicalNotes/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllClinicalNotesByAdmin
    );
    app.post(
        "/api/auth/getAllPassDueNotes/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllPassDueNotesByTherapist
    );

    // app.get(
    //   "/api/auth/get-all-therapist-pending-reviews",
    //   Authentication.superAdminVerification,
    //   AdminEp.getAllTherapistPendingReviews
    // );

    // app.get(
    //   "/api/auth/get-all-therapist-approved-reviews/:limit?/:offset?",
    //   Authentication.superAdminVerification,
    //   AdminEp.getAllTherapistApprovedReviews
    // );

    app.post(
        "/api/auth/sendReminderClinicalNotes",
        Authentication.superAdminVerification,
        AdminEp.sendReminderClinicalNotes
    );

    app.post(
        "/api/auth/sendReminderClient",
        Authentication.superAdminVerification,
        AdminEp.sendReminderClient
    );

    app.post(
        "/api/auth/updateClientProfileByAdmin",
        Authentication.superAdminVerification,
        UserEp.updateClientValidationRules(),
        AdminEp.updateClientProfileByAdmin
    );

    app.post(
        "/api/auth/updateTherapistProfileByAdmin",
        // Authentication.therapistVerification,
        UserEp.updateTherapistProfileValidationRules(),
        AdminEp.updateTherapistProfileByAdmin
    );

    app.post(
        "/api/auth/updateProfileCoverImageByAdmin/:userId?",
        // Authentication.therapistAndClientVerification,
        AdminEp.updateProfileCoverImageByAdmin
    );

    app.post(
        "/api/auth/updateProfileImageByadmin/:userId?",
        // Authentication.therapistAndClientVerification,
        AdminEp.updateProfileImageByAdmin
    );

    app.post(
        "/api/auth/searchMatchTherapistsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchMatchTherapistsByAdmin
    );

    app.post(
        "/api/auth/searchMatchedTherapistsByClientId/:limit?/:offset?",
        Authentication.allUserVerification,
        AdminEp.searchMatchedTherapistsByClientId
    );

    app.post(
        "/api/auth/searchMatchedTherapistsWithoutOwnTherapistByClientId/:limit?/:offset?",
        Authentication.allUserVerification,
        AdminEp.searchMatchedTherapistsWithoutOwnTherapistByClientId
    );

    // app.post(
    //   "/api/auth/testOne",
    //   Authentication.superAdminVerification,
    //   TransactionsEp.withdrawBalances
    // );
    app.post(
        "/api/auth/sendMailToMultipleUsers",
        Authentication.superAdminVerification,
        AdminEp.sendMailToUsers
    );
    app.post(
        "/api/auth/sendSMSToMultipleUsers",
        Authentication.superAdminVerification,
        AdminEp.sendSMSToUsers
    );
    app.post(
        "/api/auth/getMergeMeetingByNoteId",
        Authentication.superAdminVerification,
        AdminEp.getMergeMeetings
    );

    app.post(
        "/api/auth/runCrownJob1stOfMonth",
        Authentication.superAdminVerification,
        TransactionsEp.setMonthlyAllAccumulatedBalanceCrownjobIn1stOfMonthByAdmin
    );

    app.post(
        "/api/auth/runCrownJob15thOfMonth",
        Authentication.superAdminVerification,
        TransactionsEp.setMonthlyAllAccumulatedBalanceCrownjobIn15thOfMonthByAdmin
    );
    app.post(
        "/api/auth/updateClientDobByAdmin",
        Authentication.superAdminVerification,
        AdminEp.updateClientDobByAdmin
    );

    app.post(
        "/api/auth/runClaimByAdmin",
        Authentication.superAdminVerification,
        AdminEp.runClaimByAdmin
    );

    app.post(
        "/api/auth/updateTherpistPayRateType",
        Authentication.superAdminVerification,
        AdminEp.updateTherapistPayRateByAdmin
    );

    app.post(
        "/api/auth/reviewSubmissionByAdminOnBehalfOfClient",
        Authentication.superAdminVerification,
        AdminEp.reviewSubmissionByAdminOnBehalfOfClient
    );

    app.post(
        "/api/auth/reviewSubmissionByAdminOnBehalfOfTherapist",
        Authentication.superAdminVerification,
        AdminEp.reviewSubmissionByAdminOnBehalfOfTherapist
    );

    app.post(
        "/api/auth/getExistingReviewByClientId/:clientId",
        Authentication.superAdminVerification,
        AdminEp.getExistingReviewByClientId
    );

    app.post(
        "/api/auth/getExistingReviewByTherapistId/:therapistId",
        Authentication.superAdminVerification,
        AdminEp.getExistingReviewByTherapistId
    );

    app.post(
        "/api/auth/updateClientCopaymentAmount",
        Authentication.superAdminVerification,
        AdminEp.updateClientCopaymentAmountByAdmin
    );
    // for update all therapist payrateType and flat value
    app.post(
        "/api/auth/updateCreatedTherpistDefaultPayRateType",
        Authentication.superAdminVerification,
        AdminEp.updateOldTherapistPayRateType
    );
    app.post(
        "/api/auth/updateTherapistPnumberByAdmin",
        Authentication.superAdminVerification,
        AdminEp.updateTherapistPnumberByAdmin
    );

    app.post(
        "/api/auth/updateBlackTherapyPnumberByAdmin",
        Authentication.superAdminVerification,
        AdminEp.updateTherapistBlackTherapyPnumberByAdmin
    );

    app.post(
        "/api/auth/updateRelationshipTherapyPnumberByAdmin",
        Authentication.superAdminVerification,
        AdminEp.updateTherapistRelationshipTherapyPnumberByAdmin
    );

    app.post(
        "/api/auth/updateMensMentalTherapyPnumberByAdmin",
        Authentication.superAdminVerification,
        AdminEp.updateTherapistMensMentalTherapyPnumberByAdmin
    );

    app.get(
        "/api/auth/getAllTherapistCountByAdmin",
        Authentication.superAdminVerification,
        AdminEp.getAllTherapistCount
    );
    // app.post(
    //   "/api/auth/getTherapistPriorityNumberByAdmin",
    //   Authentication.superAdminVerification,
    //   AdminEp.getTherapistPriorityNumber
    // );
    // testing cronjop api
    app.post(
        "/api/public/setDailySuccessClaimsStatusUpdate",
        InsuranceEp.setDailySuccessClaimsStatusUpdate
    );
    // FOR TESTING PURPOSES
    // app.post(
    //   "/api/public/runCrownJob1",
    //   AdminEp.setDailySuccessClaimsStatusUpdate
    // );

    //for development purposes(test crownjob)
    // app.post(
    //   "/api/auth/crownJobtest",
    //   Authentication.superAdminVerification,
    //   AdminEp.setDailySuccessClaimsStatusUpdate
    // );
    app.post(
        "/api/auth/updateMonthlyPaymentAndTransaction",
        Authentication.superAdminVerification,
        AdminEp.updateMonthlyPaymentsAndTransaction
    );

    app.post(
        "/api/auth/deleteTreatmentHistoryByAdmin",
        Authentication.superAdminVerification,
        AdminEp.deleteTreatmentHistoryByAdmin
    );

    app.post(
        "/api/auth/updateClaimStatus",
        Authentication.superAdminVerification,
        AdminEp.updateTreatmentHistoryStatusByAdmin
    );

    // for development
    app.post(
        "/api/public/getTreatmentHistories",
        AdminEp.updateFlagTreatmentHistories
    );

    app.get(
        "/api/public/notUpdatedTreatmentHistories",
        AdminEp.notUpdatedTreatmentHistories
    );

    app.post(
        "/api/auth/removeTreatmentHistoryFlag",
        Authentication.superAdminVerification,
        AdminEp.removeTreatmentHistoryFlag
    );


    //for development purposes(REMOVE ALL TRANSACTION HISTROY FLAG ATRIBUTE)
    app.post(
        "/api/auth/deleteTreatmentHistoryFlag",
        Authentication.superAdminVerification,
        AdminEp.deleteTreatmentHistoryFlag
    );

    app.post(
        "/api/auth/updateClaimOpen",
        Authentication.superAdminVerification,
        AdminEp.updateOpenClaim
    );

    app.get(
        "/api/auth/get-detail-claims-status-of-therapist/:therapistId",
        Authentication.superAdminVerification,
        AdminEp.getDetailClaimsStatusOfTherapist
    );

    app.get(
        "/api/auth/getAllTherapistsList",
        Authentication.superAdminVerification,
        AdminEp.getAllTherapistsList
    );

    app.get(
        "/api/auth/getAllReminderSms/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllReminderSms
    );


    app.post(
        "/api/auth/sendCustomSms",
        Authentication.superAdminVerification,
        AdminEp.sendCustomSms
    );

    app.post(
        "/api/public/getInsuaranceCompanyByState",
        AdminEp.getInsuaranceCompanyByState
    );

    app.get(
        "/api/auth/getReminderSmsByClientId/:clientId?/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getReminderSmsByClientId
    );


    app.post(
        "/api/auth/getERALists/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getERALists
    );


    app.post(
        "/api/auth/filterTherapistByTimeRange/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.filterTherapistByTimeRange
    );

    app.get(
        "/api/auth/getAllPendingTherapistRequests/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllPendingTherapistRequests
    );

    app.get(
        "/api/auth/getAllApprovedTherapistRequests/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllApprovedTherapistRequests
    );

    app.get(
        "/api/auth/getAllRejectedTherapistRequests/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.getAllRejectedTherapistRequests
    );

    app.post(
        "/api/auth/approveTherapistRequest",
        Authentication.superAdminVerification,
        AdminEp.approveTherapistRequest
    );

    app.post(
        "/api/auth/rejectTherapistRequest",
        Authentication.superAdminVerification,
        AdminEp.rejectTherapistRequest
    );

    app.post(
        "/api/auth/searchDocumentDownloadsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchDocumentDownloads
    );

    app.post(
        "/api/auth/getClinicalAssestmentDetailsByAdmin",
        Authentication.superAdminVerification,
        ClinicalAssestmentEp.getClinicalAssesmentByAdmin
    );

    app.post(
        "/api/auth/getTherapyPlanDetailsByAdmin",
        Authentication.superAdminVerification,
        TherapyPlanEp.getTherapyPlanDetailsByAdmin
    );

    app.post(
        "/api/auth/searchInsuranceDocumentsByCondition/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchInsuranceDocumentsByCondition
    );

    app.post(
        "/api/auth/uploadInsurancePdf/:uploadCategory/:insuranceDocApprovalId",
        Authentication.superAdminVerification,
        AdminEp.uploadInsurancePdfByAdmin
    );

    app.get(
        "/api/auth/getUploadedInsuranceDocStatus/:insuranceDocApprovalId",
        Authentication.superAdminVerification,
        AdminEp.getUploadedInsuranceDocStatus
    );

    app.post(
        "/api/auth/adminApprovalForInsuranceDocumentsFaxing",
        Authentication.superAdminVerification,
        AdminEp.adminApprovalForInsuranceDocumentsFaxing
    );

    app.post(
        "/api/auth/sendReminderEmailToTherapistRegardingIncompletedForm",
        Authentication.superAdminVerification,
        AdminEp.sendReminderEmailToTherapistRegardingIncompletedForm
    );

    app.post(
        "/api/auth/getClientUsingClinicalAssessmentId",
        Authentication.superAdminVerification,
        ClinicalAssestmentEp.getClientUsingClinicalAssessmentId
    );

    app.post(
        "/api/auth/getClientUsingTherapyPlanId",
        // Authentication.superAdminVerification, # fix bug can not send mail when amend a therapist plan
        TherapyPlanEp.getClientUsingTherapyPlanId
    );

    app.post(
        "/api/auth/getClinicalAssestmentDetailsWithSessionDataByAdmin",
        Authentication.superAdminVerification,
        ClinicalAssestmentEp.getClinicalAssestmentDetailsWithSessionDataByAdmin
    );

    app.post(
        "/api/auth/getClinicalAssestmentDetailsForDownloadByAdmin",
        Authentication.superAdminVerification,
        ClinicalAssestmentEp.getClinicalAssestmentDetailsForDownloadByAdmin
    );

    app.post(
        "/api/auth/getEraListForClaim",
        Authentication.superAdminVerification,
        AdminEp.getEraListForClaim
    );

    app.post(
        "/api/auth/searchMatchClientsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchMatchClientsByAdmin
    );

    app.post(
        "/api/auth/get-all-admin-statistics-on-demand",
        Authentication.superAdminVerification,
        AdminStatisticsEp.updateAdminStatisticsOnDemand
    );

    app.post(
        "/api/auth/searchMatchedTherapistsByAdmin/:limit?/:offset?",
        Authentication.superAdminVerification,
        AdminEp.searchMatchedTherapistsByAdmin
    );

    app.post(
        "/api/auth/generateAppointmentAuthTokenByAdmin",
        Authentication.superAdminVerification,
        AdminEp.generateAppointmentAuthTokenByAdmin
    );

    app.post(
        "/api/auth/getAppointmentAuthTokenByAdmin",
        Authentication.superAdminVerification,
        AdminEp.getAppointmentAuthTokenByAdmin
    );

    app.post(
        "/api/auth/releaseOrHoldPaymentForSpecificTherapist",
        Authentication.superAdminVerification,
        AdminEp.releaseOrHoldPaymentForSpecificTherapist
    );

    app.post(
        "/api/auth/addStateByAdmin",
        Authentication.superAdminVerification,
        AdminEp.addStateByAdmin
    );

    app.post(
        "/api/auth/deleteStateByAdmin",
        Authentication.superAdminVerification,
        AdminEp.deleteStateByAdmin
    );

    app.get(
        "/api/auth/getAllStatesByAdmin",
        Authentication.superAdminVerification,
        AdminEp.getAllStatesByAdmin
    );

    app.post(
        "/api/auth/submit-claim-md",
        Authentication.superAdminVerification,
        AdminEp.submitClaimMd
    );

    app.post(
        "/api/auth/admin/submit-claim",
        Authentication.superAdminVerification,
        AdminEp.submitAdminClaim
    );
}
