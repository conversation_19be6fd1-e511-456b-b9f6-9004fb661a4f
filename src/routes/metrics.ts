import { Express } from "express";
import { MetricsEp } from "../end-point/metrics-ep";
import { Authentication } from "../middleware/authentication";

export function initMetricsRoutes(app: Express) {
    // Aggregated metrics API
    app.get(
        "/api/auth/metrics/aggregated",
        Authentication.verifyToken,
        Authentication.superAdminVerification,
        MetricsEp.getAggregatedMetrics
    );

    // Clients data API
    app.get(
        "/api/auth/metrics/clients",
        Authentication.verifyToken,
        Authentication.superAdminVerification,
        MetricsEp.getClientsData
    );

    // Therapists API
    app.get(
        "/api/auth/metrics/therapists",
        Authentication.verifyToken,
        Authentication.superAdminVerification,
        MetricsEp.getTherapists
    );

    // DB info debugging API
    app.get(
        "/api/auth/debug/dbinfo",
        Authentication.verifyToken,
        Authentication.superAdminVerification,
        MetricsEp.getDbInfo
    );
} 