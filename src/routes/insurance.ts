import { Express } from "express";
import { InsuranceEp } from "../end-point/insurance-ep";
import { Authentication } from "../middleware/authentication";
import { CopaymentEp } from "../end-point/copayment-ep";
import { EligibilityEp } from "../end-point/eligibility-ep";

export function initInsuranceRoutes(app: Express) {
  app.post(
    "/api/auth/create/insurance",
    Authentication.clientVerification,
    InsuranceEp.addInsuranceInfo
  );

  app.post(
    "/api/auth/update/insurance",
    Authentication.clientVerification,
    InsuranceEp.updateInsuranceInfo
  );

  app.post(
    "/api/auth/update/insuranceByAdmin/:userId",
    Authentication.superAdminVerification,
    InsuranceEp.updateInsuranceInfoBYAdmin
  );
  app.post(
    "/api/auth/update/insurance",
    Authentication.clientVerification,
    InsuranceEp.updateInsuranceInfo
  );

  app.get(
    "/api/auth/viewSingleInsurance/:id",
    Authentication.clientAndAdminVerification,
    InsuranceEp.viewSingleInsurance
  );

  // app.post(
  //   "/api/auth/eligibilityCheck/:insuranceId/:serviceId",
  //   Authentication.superAdminVerification,
  //   InsuranceEp.checkInsuranceEligibility
  // );

  app.post(
    "/api/auth/submitClaim/:insuranceId",
    Authentication.superAdminVerification,
    InsuranceEp.submitProfessionalClaims
  );


  app.post(
    "/api/public/checkout-link",
    InsuranceEp.checkoutPaymentLink
  );

  app.post(
    "/api/public/update-copayment",
    InsuranceEp.UpdatePaymentWithStripeLink
  );

  app.post(
    "/api/public/check-copayment-status",
    InsuranceEp.checkCoPaymentStatus
  );

  app.post(
    "/api/auth/checkEligibilityByAdmin/:insuranceId",
    Authentication.superAdminVerification,
    InsuranceEp.checkEligibilityAdmin
  );


  app.post(
    "/api/auth/checkEligibility/:userId",
    Authentication.superAdminVerification,
    InsuranceEp.checkEligibility
  );

  app.post(
    "/api/auth/checkEligibilityMD/:userId",
    Authentication.superAdminVerification,
    // InsuranceEp.checkEligibilityMD
    EligibilityEp.checkEligibilityMD
  );

  app.get(
    "/api/auth/checkCoPayment/:limit?/:offset?",
    Authentication.superAdminVerification,
    InsuranceEp.checkCoPayment
  );

  app.get(
    "/api/auth/getClaimStatusByInsuranceId/:insuranceId/:serviceId/:noteId",
    Authentication.superAdminVerification,
    InsuranceEp.viewClaimStauts
  );

  app.get(
    "/api/auth/getClaimDetailsByInsuranceId/:noteId",
    Authentication.superAdminVerification,
    InsuranceEp.getClaimDetails
  );

  app.post(
    "/api/auth/create/insuranceByAdmin/:userId",
    Authentication.superAdminVerification,
    InsuranceEp.addInsuranceInfoByAdmin
  );

  app.get(
    "/api/auth/getPendingCopaymentsByClientId/:userId",
    Authentication.therapistAndClientVerification,
    InsuranceEp.getAllPendingCopaymentAmounts
  );

  app.get(
    "/api/auth/getPaidCopaymentsByClientId/:userId",
    Authentication.therapistAndClientVerification,
    InsuranceEp.getAlPaidPendingCopaymentAmounts
  );

  app.post(
    "/api/auth/payPendingCopaymentsByClientId",
    Authentication.clientVerification,
    InsuranceEp.payAllPendingCopaymentAmounts
  );

  app.post(
    "/api/auth/createClientStripeAccountAndPayCopaymnt",
    Authentication.clientVerification,
    InsuranceEp.createStripeAccountAndPay
  );

  app.post(
    "/api/auth/sendSMSAndEmailForClient/:userId/:copaymentAmount",
    Authentication.therapistVerification,
    InsuranceEp.sendSMSAndEmailForClient
  );

  app.post("/api/auth/createClientStripeAccountAndPayCopaymnt", Authentication.clientVerification, InsuranceEp.createStripeAccountAndPay);
  // app.get("/api/public/testApi/:id", InsuranceEp.test);

  app.get("/api/public/getInsuaranceCoPaymentAndContractPriceById/:insuranceId", InsuranceEp.getInsuaranceCoPaymentAndContractPriceById)

  // app.post(
  //   "/api/auth/create/insuranceByClient",
  //   Authentication.clientVerification,
  //   InsuranceEp.addInsuranceInfoByClient
  // );

  app.get(
    "/api/auth/get/insurancesByClient",
    Authentication.clientVerification,
    InsuranceEp.getInsuranceInfoByClient
  );
  
  app.post(
    "/api/auth/create/insuranceByUser/:userId",
    Authentication.clientAndAdminVerification,
    InsuranceEp.addInsuranceInfoByAdmin
  );

  app.post(
    "/api/auth/create/secondaryInsuranceByUser/:userId",
    Authentication.clientAndAdminVerification,
    InsuranceEp.addSecondaryInsuranceInfo
  );

  app.post(
    "/api/auth/update/insuranceByUser/:userId",
    Authentication.clientAndAdminVerification,
    InsuranceEp.updateInsuranceInfoBYAdmin
  );

  app.post(
    "/api/auth/get/insurancesByAdmin",
    Authentication.superAdminVerification,
    InsuranceEp.getInsuranceInfoByAdmin
  );

  app.get(
    "/api/auth/getInsuranceByClientIdViaTherapist/:clientId",
    Authentication.therapistVerification,
    InsuranceEp.getInsuranceByClientIdViaTherapist
  );

  app.get(
    "/api/auth/get-copayment",
    Authentication.allUserVerification,
    CopaymentEp.getCopaymentByClientId
  );

  // Public endpoint that doesn't require authentication
  app.get(
    "/get-copayment",
    CopaymentEp.getCopaymentByClientId
  );
}
