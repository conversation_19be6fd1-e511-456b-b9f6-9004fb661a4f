import { Express } from "express";
import { TransactionsEp } from "../end-point/transactions-ep";
import { Authentication } from "../middleware/authentication";

export function initTransactionsRoutes(app: Express) {
  app.get("/api/auth/recentBalance", 
    TransactionsEp.getRecentBalance);

  app.get("/api/auth/allTransactions", 
    TransactionsEp.getAllTransactions);

  app.get(
    "/api/auth/allTransactions/:tId/:limit?/:offset?",
    TransactionsEp.getAllTransactionsByTherapistId
  );

  app.post(
    "/api/auth/createStripeConnectedAccount",
    TransactionsEp.createStripeConnectedAccount
  );

  app.post("/api/auth/verifyStripeAccount", TransactionsEp.verifyStripeAccount);

  app.post(
    "/api/auth/createStripeLoginLink",
    TransactionsEp.createStripeLoginLink
  );

  // admin
  app.post(
    "/api/auth/transferAmont",
    Authentication.superAdminVerification,
    TransactionsEp.transeferAmmount
  );

  app.post(
    "/api/auth/transferAmontSession",
    Authentication.superAdminVerification,
    TransactionsEp.transeferAmmountBySession
  );

  app.post(
    "/api/auth/markAsPaidAmmount",
    Authentication.superAdminVerification,
    TransactionsEp.markAsPaidTranseferAmmountBySession
  );

  app.get(
    "/api/auth/get/accumulatedBalances",
    Authentication.superAdminVerification,
    TransactionsEp.getAllAccumulatedBalance
  );

  app.get(
    "/api/auth/get/totalRevenue",
    Authentication.superAdminVerification,
    TransactionsEp.getAllTotalRevenue
  );

  app.post(
    "/api/auth/declaredMonthlyAmount",
    Authentication.superAdminVerification,
    TransactionsEp.declareMonthlyAllAccumulatedBalance
  );

  app.post(
    "/api/auth/deleteMonthlyAmountRecord/:amountRecordId",
    Authentication.superAdminVerification,
    TransactionsEp.deleteMonthlyAllAccumulatedRecord
  );

  app.get(
    "/api/auth/getMonthlyAmounts/:limit/:offset",
    Authentication.superAdminVerification,
    TransactionsEp.getMonthlyAllAccumulatedBalance
  );

  app.post(
    "/api/auth/payMonthlyAmounts",
    Authentication.superAdminVerification,
    TransactionsEp.payMonthlyAllAccumulatedBalance
  );

  app.post(
    "/api/auth/transferAmontForSelcetedTherapist",
    Authentication.superAdminVerification,
    TransactionsEp.transeferAmmountForSelectedTherapist
  );
}
