import { Express } from "express";
import { Authentication } from "../middleware/authentication";
import { PaymentEp } from "../end-point/payment-ep";

/**
 * Initialize payment routes
 * @param app Express application
 */
export function initPaymentRoutes(app: Express) {
  /**
   * @route POST /api/auth/pay-copayment
   * @desc Pay client copayment using their default payment method
   * @access Private (Client only)
   */
  app.post("/api/auth/pay-copayment", Authentication.verifyToken, Authentication.clientVerification, PaymentEp.payClientCopayment);

  /**
   * @route POST /api/auth/get-copayment-status-of-a-meeting
   * @desc Get copayment status of a meeting by vonageSessionId
   * @access Private
   */
  app.post("/api/auth/get-copayment-status-of-a-meeting", Authentication.verifyToken, PaymentEp.getCopaymentStatusByMeeting);
  
  /**
   * @route POST /api/auth/get-recent-meeting-copayment
   * @desc Get copayment status of any meeting in the last 24 hours
   * @access Private
   */
  app.post("/api/auth/get-recent-meeting-copayment", Authentication.verifyToken, PaymentEp.getRecentMeetingCopayment);
} 