import * as passport from "passport";
import { NextFunction, Request, Response } from "express";
import { AppLogger } from "../common/logging";
import { UserRole } from "../models/user-model";
import { UserDao } from "../dao/user-dao";
let jwt = require("jsonwebtoken");

export class Authentication {
  public static verifyToken(req: Request, res: Response, next: NextFunction) {
    return passport.authenticate("jwt", { session: false }, (err: any, user: any, info: any) => {
      if (err || !user) {
        AppLogger.error(`Login Failed. reason: ${info}`);
        return res.sendError(info);
      }
      req.user = user;
      req.body.user = user._id;
      return next();
    })(req, res, next);
  }

  public static clientVerification(req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.CLIENT) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        message: "No authorization to access this route.",
      });
    }
  }

  public static therapistVerification(req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.ADMIN || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        message: "No authorization to access this route.",
      });
    }
  }

  public static superAdminVerification(req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        message: "No authorization to access this route.",
      });
    }
  }

  public static therapistAndClientVerification(req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.CLIENT || req.user.role == UserRole.THERAPIST) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        message: "No authorization to access this route.",
      });
    }
  }

  public static therapistAndAdminVerification(req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUB_ADMIN) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        message: "No authorization to access this route.",
      });
    }
  }

  public static clientAndAdminVerification(req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.CLIENT || req.user.role == UserRole.SUB_ADMIN) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        message: "No authorization to access this route.",
      });
    }
  }

  public static allUserVerification(req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.CLIENT || req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUB_ADMIN) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        message: "No authorization to access this route.",
      });
    }
  }

  public static async validateAppointmentsAuthToken(req: Request, res: Response, next: NextFunction) {
    try {
      const authHeader = req.headers["authorization"];

      if (!authHeader) {
        return res.status(403).json({
          success: false,
          message: "Authorization header is missing.",
        });
      }
  
      if (!authHeader.startsWith("Bearer ")) {
        return res.status(403).json({
          success: false,
          message: "Authorization header format is invalid.",
        });
      }
  
      const token = authHeader.split(" ")[1];
  
      if (!token) {
        return res.status(403).json({
          success: false,
          message: "Token is missing from Authorization header..",
        });
      }
  
      const secretKey = process.env.JWT_SECRET;
      const decoded = jwt.verify(token, secretKey);

      const user = await UserDao.getUserByUserId(decoded.userId);

      if (!user || user.appointmentAuthToken !== token) {
        return res.status(403).json({
          success: false,
          message: "Invalid or expired token.",
        });
      }

      next();
    } catch (error) {
      return res.status(403).json({
        success: false,
        message: `An error occured. Error: ${error}`,
      });
    }
  }
  
  
}