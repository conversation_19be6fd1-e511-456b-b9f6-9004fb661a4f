import multer = require("multer");
import { Request, Response, NextFunction } from 'express';
const path = require('path');
const fs = require("fs");

const storage = multer.diskStorage({
    destination: function(req, file, cb){
      const destinationDirectory = path.join(__dirname, '..', '..', 'uploads', 'CALL_RECORDS',req.user?._id.toString());
      if (!fs.existsSync(destinationDirectory)) {
        fs.mkdirSync(destinationDirectory, { recursive: true });
      }
      return cb(null, destinationDirectory)
    },
    filename: function(req, file, cb){
      const uniqueFileName = `${req.user?._id}_${Date.now()}_${file.originalname}`;
      const filePath = `uploads/CALL_RECORDS/${req.user?._id}/`+uniqueFileName
      req.body.uploadedFilePath = filePath
      req.body.originalName = uniqueFileName
      req.body.audioAvailable =true
      return cb(null, uniqueFileName)
    }
  })

export const singleFileUploadMiddleware = multer({ storage }).single('file');

export const uploadCallRecordFile = (req: Request, res: Response, next: NextFunction) => {
  if(req.file==undefined){
    console.log("undefinedddddddddddddddddddddddddddddddd");
    
  }
    singleFileUploadMiddleware(req, res, (error) => {
      if (error) {
        return res.status(500).json({ error: 'File upload failed.' });
      }
      next();
    });
  };
