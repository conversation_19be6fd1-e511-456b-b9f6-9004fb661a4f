
import AWS = require('aws-sdk')
import multer = require("multer");
const path = require('path');
const fs = require("fs");
import { Request, Response, NextFunction } from 'express';

// Configure AWS SDK
AWS.config.update({
  accessKeyId: process.env.AWS_S3_ACCESSKEY_ID, // Store securely
  secretAccessKey: process.env.AWS_S3_SECRET_ACCESSKEY, // Store securely
  region: process.env.AWS_S3_REGION, // e.g., 'us-west-2'
});

const s3 = new AWS.S3();

// Configure multer to store files locally
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const destinationDirectory = path.join(__dirname, '..', '..', 'uploads', 'TEMP',req.user?._id.toString());
   // fs.ensureDirSync(destinationDirectory);

    if (!fs.existsSync(destinationDirectory)) {
        fs.mkdirSync(destinationDirectory, { recursive: true });
    }
     return cb(null, destinationDirectory);
  },
  filename: (req, file, cb) => {
    const uniqueFileName = `${req.user?._id}_${Date.now()}_${file.originalname}`;
    // req.body.uploadedFilePath = filePath
    // req.body.originalName = uniqueFileName
       req.body.audioAvailable =true;
      //  req.body.meetingType == "Regular"
    return cb(null, uniqueFileName);
  },
});

const upload = multer({ storage }).single('file');

// Middleware to handle file uploads and S3 upload
export const uploadAndTransferToS3 = async (req: Request, res: Response, next: NextFunction) => {
  upload(req, res, async (error) => {
    if (error) {
      return res.sendError(
        "File upload failed."
      );
    }

    try {
      if (!req.file) {
        return next();
      }

      // Read the file from the local storage
      const fileContent = await fs.readFileSync(req.file.path);
      const fileName = req.file.filename;
      const s3FilePath = `CALL_RECORDS/${req.user?._id}/${fileName}`;

      // Set up S3 upload parameters
      const params = {
        Bucket: process.env.AWS_S3_BUCKET_REGULER_AUDIO_CALLS, // Replace with your bucket name
        Key: s3FilePath,
        Body: fileContent,
        ContentType: req.file.mimetype, // Set appropriate MIME type
      };

      // Upload the file to S3
      const uploadResult = await s3.upload(params).promise();

      // Update request body with S3 URL and other details
      req.body.uploadedFilePath = `${s3FilePath}`;
      req.body.s3Url = uploadResult.Location;
      req.body.originalName = fileName;
      req.body.audioAvailable = true;

      // Remove the local file after upload
      await fs.promises.unlink(req.file.path);

      next();
    } catch (s3Error) {
      return res.sendError(
        "S3 upload failed."
      );
    }
  });
};
