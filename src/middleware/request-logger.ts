import {Request, Response} from 'express';
import {RequestLogger} from '../common/logging';

export function RequestLoggerHandler(req: Request, res: Response, next: any) {
    // Bỏ qua request OPTIONS
    if (req.method !== "OPTIONS") {
        // Log request đơn giản
        RequestLogger.info(`${req.method} ${req.url}`);
        
        // Ghi log khi response được gửi
        res.on('finish', () => {
            RequestLogger.info(`${req.method} ${req.url} completed with status ${res.statusCode}`);
        });
    }
    next();
}