import { NextFunction, Request, Response } from "express";
import User = Express.User;
import { UserRole } from "../models/user-model";
import { ClientDao } from "../dao/client-dao";
import { Types } from "mongoose";
import { SubscriptionStatus } from "../models/client-model";
import { AppointmentDao } from "../dao/appointment-dao";
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

export function verifyActiveSubscription() {
  return async function (req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.THERAPIST || req.user.role == UserRole.SUPER_ADMIN || req.user.role == UserRole.SUB_ADMIN) {
      next();
    } else {
      const status = await checkValidSubscription(req.user);

      if (status) {
        next();
      } else {
        return res.sendError("No active subscription found!");
      }
    }
  };
}

export function verifyActiveSubscriptionAdmin() {
  return async function (req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.THERAPIST) {
      next();
    } else {
      const status = await checkValidSubscriptionAdmin(req.body.clientId);

      if (status) {
        next();
      } else {
        return res.sendError("No active subscription found!");
      }
    }
  };
}

export function verifyActiveSubscriptionAdminDelete() {
  return async function (req: Request, res: Response, next: NextFunction) {
    const appointmentId = Types.ObjectId(req.params.id);
    if (req.user.role == UserRole.THERAPIST) {
      next();
    } else {
      let appointment = await AppointmentDao.getAppointmentById(appointmentId);

      const status = await checkValidSubscriptionAdmin(appointment.clientId.toString());

      if (status) {
        next();
      } else {
        return res.sendError("No active subscription found!");
      }
    }
  };
}

export function verifyActiveSubscriptionAdmin2() {
  return async function (req: Request, res: Response, next: NextFunction) {
    if (req.user.role == UserRole.THERAPIST) {
      next();
    } else {
      const appointment = req.body.appointment;
      const clientId = appointment.clientId;
      let status = false;

      try {
        status = await checkValidSubscriptionAdmin(clientId);
      } catch (error) {
        return res.sendError(error);
      }

      if (status) {
        next();
      } else {
        return res.sendError("No active subscription found!");
      }
    }
  };
}

export async function checkValidSubscription(user: User): Promise<boolean> {
  const currentUser = await ClientDao.getUserById(user._id);

  if (currentUser.premiumStatus) {
    return true;
  }

  if (!currentUser.subscriptionId) {
    return false;
  }

  const subscription = await stripe.subscriptions.retrieve(currentUser.subscriptionId);

  if (subscription.status != currentUser.subscriptionStatus) {
    await ClientDao.updateClient(currentUser._id, { subscriptionStatus: subscription.status });
  }

  if (subscription.status == SubscriptionStatus.ACTIVE || currentUser.premiumStatus) {
    return true;
  } else {
    return false;
  }
}

export async function checkValidSubscriptionAdmin(user: string): Promise<boolean> {
  const currentUser = await ClientDao.getUserById(user);

  if (currentUser.premiumStatus) {
    return true;
  }

  if (!currentUser.subscriptionId) {
    return false;
  }

  const subscription = await stripe.subscriptions.retrieve(currentUser.subscriptionId);

  if (subscription.status != currentUser.subscriptionStatus) {
    await ClientDao.updateClient(currentUser._id, { subscriptionStatus: subscription.status });
  }

  if (subscription.status == SubscriptionStatus.ACTIVE || currentUser.premiumStatus) {
    return true;
  } else {
    return false;
  }
}
